package com.logistics.tms.api.impl.vehicleoilcard;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.vehicleoilcard.VehicleOilCardServiceApi;
import com.logistics.tms.api.feign.vehicleoilcard.model.*;
import com.logistics.tms.biz.vehicleoilcard.VehicleOilCardBiz;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/4 15:21
 */
@RestController
public class VehicleOilCardServiceApiImpl implements VehicleOilCardServiceApi {

    @Autowired
    private VehicleOilCardBiz vehicleOilCardBiz;

    /**
     * 车辆油卡列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchVehicleOilCardListResponseModel>> searchVehicleOilCardList(@RequestBody SearchVehicleOilCardListRequestModel requestModel) {
        return Result.success(vehicleOilCardBiz.searchVehicleOilCardList(requestModel));
    }

    /**
     * 新增车辆油卡
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> addVehicleOilCard(@RequestBody AddVehicleOilCardRequestModel requestModel) {
        vehicleOilCardBiz.addVehicleOilCard(requestModel);
        return Result.success(true);
    }

    /**
     * 车辆油卡详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<VehicleOilCardDetailResponseModel> vehicleOilCardDetail(@RequestBody VehicleOilCardIdRequestModel requestModel) {
        return Result.success(vehicleOilCardBiz.vehicleOilCardDetail(requestModel));
    }

    /**
     * 解绑车辆油卡
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> unBindVehicleOilCard(@RequestBody VehicleOilCardIdRequestModel requestModel) {
        vehicleOilCardBiz.unBindVehicleOilCard(requestModel);
        return Result.success(true);
    }

    /**
     * 绑定车辆油卡
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> bindVehicleOilCard(@RequestBody BindVehicleOilCardRequestModel requestModel) {
        vehicleOilCardBiz.bindVehicleOilCard(requestModel);
        return Result.success(true);
    }

    /**
     * 操作记录
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<GetVehicleOilCardRecordResponseModel>> getVehicleOilCardRecord(@RequestBody VehicleOilCardIdRequestModel requestModel) {
        return Result.success(vehicleOilCardBiz.getVehicleOilCardRecord(requestModel));
    }
}
