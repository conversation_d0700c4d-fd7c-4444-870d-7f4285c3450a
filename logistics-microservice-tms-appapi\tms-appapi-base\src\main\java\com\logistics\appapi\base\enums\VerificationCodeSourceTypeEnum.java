package com.logistics.appapi.base.enums;

/**
 * @author: wjf
 * @date: 2022/8/19 9:35
 */
public enum VerificationCodeSourceTypeEnum {
    CARRIER_WEB(4,"承运商网站"),
    DRIVER_APP(5,"司机APP"),
    QIYA_WEIXIN(6,"奇亚微信"),
    TMS_DRIVER_APPLET(7,"tms司机小程序"),
    TMS_CUSTOMER_WEB(8, "tms客户前台"),
    ;
    private Integer key;
    private String value;

    VerificationCodeSourceTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
