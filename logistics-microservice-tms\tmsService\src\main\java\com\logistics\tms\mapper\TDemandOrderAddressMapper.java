package com.logistics.tms.mapper;

import com.logistics.tms.entity.TDemandOrderAddress;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface TDemandOrderAddressMapper extends BaseMapper<TDemandOrderAddress> {

    int batchInsertSelective(@Param("list") List<TDemandOrderAddress> list);

    TDemandOrderAddress getByDemandOrderId(@Param("demandOrderId") Long demandOrderId);

    TDemandOrderAddress getInvalidByDemandOrderId(@Param("demandOrderId") Long demandOrderId);

    List<TDemandOrderAddress> getByDemandOrderIds(@Param("demandOrderIds") String demandOrderIds);

    int batchUpdateSelective(@Param("list") List<TDemandOrderAddress> upDemandOrderAddressList);

    List<Long> dispatchAlarmStatisticsAddress(@Param("loadCityIdList")List<Long> loadCityIdList);

    List<TDemandOrderAddress> getByIds(@Param("ids") String ids);
}