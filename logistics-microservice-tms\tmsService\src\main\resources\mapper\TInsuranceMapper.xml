<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TInsuranceMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TInsurance">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="insurance_type" jdbcType="INTEGER" property="insuranceType" />
    <result column="status_type" jdbcType="INTEGER" property="statusType" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_property" jdbcType="INTEGER" property="vehicleProperty" />
    <result column="driver_id" jdbcType="BIGINT" property="driverId" />
    <result column="insurance_company_id" jdbcType="BIGINT" property="insuranceCompanyId" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="settlement_status" jdbcType="INTEGER" property="settlementStatus" />
    <result column="premium" jdbcType="DECIMAL" property="premium" />
    <result column="unpaid_premium" jdbcType="DECIMAL" property="unpaidPremium" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="payment_of_vehicle_and_vessel_tax" jdbcType="DECIMAL" property="paymentOfVehicleAndVesselTax" />
    <result column="personal_accident_insurance_id" jdbcType="BIGINT" property="personalAccidentInsuranceId" />
    <result column="related_personal_accident_insurance_id" jdbcType="BIGINT" property="relatedPersonalAccidentInsuranceId" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
    <result column="refund_premium" jdbcType="DECIMAL" property="refundPremium" />
  </resultMap>
  <sql id="Base_Column_List">
    id, insurance_type, status_type, cancel_reason, vehicle_id, vehicle_property, driver_id, 
    insurance_company_id, policy_no, settlement_status, premium, unpaid_premium, start_time, 
    end_time, remark, payment_of_vehicle_and_vessel_tax, personal_accident_insurance_id, 
    related_personal_accident_insurance_id, source, created_by, created_time, last_modified_by, 
    last_modified_time, valid, refund_premium
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_insurance
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_insurance
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TInsurance">
    insert into t_insurance (id, insurance_type, status_type, 
      cancel_reason, vehicle_id, vehicle_property, 
      driver_id, insurance_company_id, policy_no, 
      settlement_status, premium, unpaid_premium, 
      start_time, end_time, remark, 
      payment_of_vehicle_and_vessel_tax, personal_accident_insurance_id, 
      related_personal_accident_insurance_id, source, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid, refund_premium
      )
    values (#{id,jdbcType=BIGINT}, #{insuranceType,jdbcType=INTEGER}, #{statusType,jdbcType=INTEGER}, 
      #{cancelReason,jdbcType=VARCHAR}, #{vehicleId,jdbcType=BIGINT}, #{vehicleProperty,jdbcType=INTEGER}, 
      #{driverId,jdbcType=BIGINT}, #{insuranceCompanyId,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, 
      #{settlementStatus,jdbcType=INTEGER}, #{premium,jdbcType=DECIMAL}, #{unpaidPremium,jdbcType=DECIMAL}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{paymentOfVehicleAndVesselTax,jdbcType=DECIMAL}, #{personalAccidentInsuranceId,jdbcType=BIGINT}, 
      #{relatedPersonalAccidentInsuranceId,jdbcType=BIGINT}, #{source,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}, #{refundPremium,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TInsurance" keyProperty="id" useGeneratedKeys="true">
    insert into t_insurance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="insuranceType != null">
        insurance_type,
      </if>
      <if test="statusType != null">
        status_type,
      </if>
      <if test="cancelReason != null">
        cancel_reason,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleProperty != null">
        vehicle_property,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="insuranceCompanyId != null">
        insurance_company_id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="settlementStatus != null">
        settlement_status,
      </if>
      <if test="premium != null">
        premium,
      </if>
      <if test="unpaidPremium != null">
        unpaid_premium,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="paymentOfVehicleAndVesselTax != null">
        payment_of_vehicle_and_vessel_tax,
      </if>
      <if test="personalAccidentInsuranceId != null">
        personal_accident_insurance_id,
      </if>
      <if test="relatedPersonalAccidentInsuranceId != null">
        related_personal_accident_insurance_id,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="refundPremium != null">
        refund_premium,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="insuranceType != null">
        #{insuranceType,jdbcType=INTEGER},
      </if>
      <if test="statusType != null">
        #{statusType,jdbcType=INTEGER},
      </if>
      <if test="cancelReason != null">
        #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleProperty != null">
        #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=BIGINT},
      </if>
      <if test="insuranceCompanyId != null">
        #{insuranceCompanyId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="settlementStatus != null">
        #{settlementStatus,jdbcType=INTEGER},
      </if>
      <if test="premium != null">
        #{premium,jdbcType=DECIMAL},
      </if>
      <if test="unpaidPremium != null">
        #{unpaidPremium,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="paymentOfVehicleAndVesselTax != null">
        #{paymentOfVehicleAndVesselTax,jdbcType=DECIMAL},
      </if>
      <if test="personalAccidentInsuranceId != null">
        #{personalAccidentInsuranceId,jdbcType=BIGINT},
      </if>
      <if test="relatedPersonalAccidentInsuranceId != null">
        #{relatedPersonalAccidentInsuranceId,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
      <if test="refundPremium != null">
        #{refundPremium,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TInsurance">
    update t_insurance
    <set>
      <if test="insuranceType != null">
        insurance_type = #{insuranceType,jdbcType=INTEGER},
      </if>
      <if test="statusType != null">
        status_type = #{statusType,jdbcType=INTEGER},
      </if>
      <if test="cancelReason != null">
        cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleProperty != null">
        vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=BIGINT},
      </if>
      <if test="insuranceCompanyId != null">
        insurance_company_id = #{insuranceCompanyId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="settlementStatus != null">
        settlement_status = #{settlementStatus,jdbcType=INTEGER},
      </if>
      <if test="premium != null">
        premium = #{premium,jdbcType=DECIMAL},
      </if>
      <if test="unpaidPremium != null">
        unpaid_premium = #{unpaidPremium,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="paymentOfVehicleAndVesselTax != null">
        payment_of_vehicle_and_vessel_tax = #{paymentOfVehicleAndVesselTax,jdbcType=DECIMAL},
      </if>
      <if test="personalAccidentInsuranceId != null">
        personal_accident_insurance_id = #{personalAccidentInsuranceId,jdbcType=BIGINT},
      </if>
      <if test="relatedPersonalAccidentInsuranceId != null">
        related_personal_accident_insurance_id = #{relatedPersonalAccidentInsuranceId,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
      <if test="refundPremium != null">
        refund_premium = #{refundPremium,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TInsurance">
    update t_insurance
    set insurance_type = #{insuranceType,jdbcType=INTEGER},
      status_type = #{statusType,jdbcType=INTEGER},
      cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_property = #{vehicleProperty,jdbcType=INTEGER},
      driver_id = #{driverId,jdbcType=BIGINT},
      insurance_company_id = #{insuranceCompanyId,jdbcType=BIGINT},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      settlement_status = #{settlementStatus,jdbcType=INTEGER},
      premium = #{premium,jdbcType=DECIMAL},
      unpaid_premium = #{unpaidPremium,jdbcType=DECIMAL},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      payment_of_vehicle_and_vessel_tax = #{paymentOfVehicleAndVesselTax,jdbcType=DECIMAL},
      personal_accident_insurance_id = #{personalAccidentInsuranceId,jdbcType=BIGINT},
      related_personal_accident_insurance_id = #{relatedPersonalAccidentInsuranceId,jdbcType=BIGINT},
      source = #{source,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER},
      refund_premium = #{refundPremium,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>