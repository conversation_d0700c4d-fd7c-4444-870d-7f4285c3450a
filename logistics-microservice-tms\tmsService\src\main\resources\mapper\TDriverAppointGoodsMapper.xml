<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDriverAppointGoodsMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDriverAppointGoods">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="driver_appoint_id" jdbcType="BIGINT" property="driverAppointId" />
    <result column="renewable_sku_code" jdbcType="VARCHAR" property="renewableSkuCode" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="goods_amount" jdbcType="DECIMAL" property="goodsAmount" />
    <result column="goods_unit" jdbcType="INTEGER" property="goodsUnit" />
    <result column="goods_price" jdbcType="DECIMAL" property="goodsPrice" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, driver_appoint_id, renewable_sku_code, goods_name, goods_amount, goods_unit, 
    goods_price, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_driver_appoint_goods
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_driver_appoint_goods
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDriverAppointGoods">
    insert into t_driver_appoint_goods (id, driver_appoint_id, renewable_sku_code, 
      goods_name, goods_amount, goods_unit, 
      goods_price, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{driverAppointId,jdbcType=BIGINT}, #{renewableSkuCode,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{goodsAmount,jdbcType=DECIMAL}, #{goodsUnit,jdbcType=INTEGER}, 
      #{goodsPrice,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDriverAppointGoods">
    insert into t_driver_appoint_goods
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="driverAppointId != null">
        driver_appoint_id,
      </if>
      <if test="renewableSkuCode != null">
        renewable_sku_code,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="goodsAmount != null">
        goods_amount,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="goodsPrice != null">
        goods_price,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="driverAppointId != null">
        #{driverAppointId,jdbcType=BIGINT},
      </if>
      <if test="renewableSkuCode != null">
        #{renewableSkuCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsAmount != null">
        #{goodsAmount,jdbcType=DECIMAL},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=INTEGER},
      </if>
      <if test="goodsPrice != null">
        #{goodsPrice,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDriverAppointGoods">
    update t_driver_appoint_goods
    <set>
      <if test="driverAppointId != null">
        driver_appoint_id = #{driverAppointId,jdbcType=BIGINT},
      </if>
      <if test="renewableSkuCode != null">
        renewable_sku_code = #{renewableSkuCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsAmount != null">
        goods_amount = #{goodsAmount,jdbcType=DECIMAL},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=INTEGER},
      </if>
      <if test="goodsPrice != null">
        goods_price = #{goodsPrice,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDriverAppointGoods">
    update t_driver_appoint_goods
    set driver_appoint_id = #{driverAppointId,jdbcType=BIGINT},
      renewable_sku_code = #{renewableSkuCode,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      goods_amount = #{goodsAmount,jdbcType=DECIMAL},
      goods_unit = #{goodsUnit,jdbcType=INTEGER},
      goods_price = #{goodsPrice,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>