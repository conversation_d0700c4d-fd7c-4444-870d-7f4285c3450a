/**
 * Created by yun.zhou on 2017/12/12.
 */
package com.logistics.management.webapi.base.enums;

public enum SettlementTonnageEnum {

    LOAD(1, "实际提货数量"),
    UNLOAD(2, "实际卸货数量"),
    SIGN(3,"实际签收数量"),
    EXPECT(4,"委托数量"),
    ;

    private Integer key;
    private String value;

    SettlementTonnageEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static SettlementTonnageEnum getEnum(Integer key) {
        for (SettlementTonnageEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
