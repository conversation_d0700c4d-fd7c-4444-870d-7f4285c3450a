package com.logistics.management.webapi.api.impl.demandorderobjectionsinopec.mapping;

import com.logistics.management.webapi.api.feign.demandorderobjectionsinopec.dto.GetSinopecObjectionDetailResponseDto;
import com.logistics.management.webapi.base.enums.AuditStatusEnum;
import com.logistics.management.webapi.base.enums.DemandOrderSinopecObjectionTypeEnum;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.GetSinopecObjectionDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @author: wjf
 * @date: 2022/5/30 17:39
 */
public class GetSinopecObjectionDetailMapping extends MapperMapping<GetSinopecObjectionDetailResponseModel, GetSinopecObjectionDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    public GetSinopecObjectionDetailMapping(String imagePrefix , Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        GetSinopecObjectionDetailResponseModel source = getSource();
        GetSinopecObjectionDetailResponseDto destination = getDestination();

        //客户单号订单号（委托单号）
        destination.setCustomerOrderCode(source.getSinopecOrderNo() + "（" + source.getCustomerOrderCode() + "）");
        //调度人
        if (StringUtils.isNotBlank(source.getDispatcherName())) {
            destination.setDispatcher(Optional.ofNullable(source.getDispatcherName()).orElse("") + " " + Optional.ofNullable(source.getDispatcherPhone()).orElse(""));
        }
        //异常类型
        destination.setObjectionTypeLabel(DemandOrderSinopecObjectionTypeEnum.getEnum(source.getObjectionType()).getValue());

        //审核结果
        if (AuditStatusEnum.AUDITED.getKey().equals(source.getAuditStatus())){
            destination.setAuditStatusLabel("通过");
        }else if (AuditStatusEnum.REJECTED.getKey().equals(source.getAuditStatus())){
            destination.setAuditStatusLabel("驳回");
        }
        //审核依据
        if (ListUtils.isNotEmpty(source.getAuditTicketList())){
            List<String> auditTicketList = new ArrayList<>();
            for (String path : source.getAuditTicketList()) {
                auditTicketList.add(imagePrefix+imageMap.get(path));
            }
            destination.setAuditTicketList(auditTicketList);
        }
        //审核异常类型
        destination.setAuditObjectionTypeLabel(DemandOrderSinopecObjectionTypeEnum.getEnum(source.getAuditObjectionType()).getValue());
    }
}
