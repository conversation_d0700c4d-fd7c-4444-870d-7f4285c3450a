package com.logistics.tms.client.feign.warehouse.stock.reponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2023/12/4 16:01
 */
@Data
public class CheckChangeUnloadWarehouseResponseModel {
    @ApiModelProperty("运单")
    private String carrierOrderCode;

    @ApiModelProperty("是否可修改卸货地：1可以修改 2类型不匹配 3入库单已入库")
    private String ifChangeUploadWarehouse;
}
