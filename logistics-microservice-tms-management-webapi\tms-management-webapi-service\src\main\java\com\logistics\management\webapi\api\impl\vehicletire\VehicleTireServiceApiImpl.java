package com.logistics.management.webapi.api.impl.vehicletire;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.controller.uploadfile.response.SrcUrlDto;
import com.logistics.management.webapi.api.feign.vehicletire.VehicleTireApi;
import com.logistics.management.webapi.api.feign.vehicletire.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.vehicletire.mapper.VehicleTireDetailMapping;
import com.logistics.management.webapi.api.impl.vehicletire.mapper.VehicleTireListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ImportExcelHeaders;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.vehicletire.VehicleTireServiceApi;
import com.logistics.tms.api.feign.vehicletire.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisLockUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * <AUTHOR>
 * @date 2019/6/24 9:30
 */

@RestController
@Slf4j
public class VehicleTireServiceApiImpl implements VehicleTireApi {
    @Autowired
    private VehicleTireServiceApi vehicleTireServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private RedisLockUtils redisLockUtils;
    private static final String IMPORT_TIRE_PIC = "IMPORT_TIRE_PIC";

    private static final String TIRE_BRAND_REG ="[\u4e00-\u9fa5A-Za-z0-9.]{1,50}";
    private static final String TIRE_AMOUNT_REG = "[0-9]{1,2}";

    /**
     * 获取轮胎管理列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<VehicleTireListResponseDto>> searchVehicleTireList(@RequestBody VehicleTireListRequestDto requestDto) {
        Result<PageInfo<VehicleTireListResponseModel>> result = vehicleTireServiceApi.searchVehicleTireList(MapperUtils.mapper(requestDto, VehicleTireListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(), VehicleTireListResponseDto.class, new VehicleTireListMapping()));
        return Result.success(pageInfo);
    }

    /**
     * 查看详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<VehicleTireDetailResponseDto> getVehicleTireDetail(@RequestBody @Valid VehicleTireIdRequestDto requestDto) {
        Result<VehicleTireDetailResponseModel> result = vehicleTireServiceApi.getVehicleTireDetail(MapperUtils.mapper(requestDto, VehicleTireIdRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (CertificationPicturesResponseModel picturesResponseModel : result.getData().getFileList()) {
            sourceSrcList.add(picturesResponseModel.getRelativeFilepath());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(), VehicleTireDetailResponseDto.class, new VehicleTireDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 新增/修改
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addOrModifyVehicleTire(@RequestBody @Valid AddOrModifyVehicleTireRequestDto requestDto) {
        if (ListUtils.isEmpty(requestDto.getVehicleTireNoList()) || requestDto.getVehicleTireNoList().size() > CommonConstant.INTEGER_FIVE){
            throw new BizException(ManagementWebApiExceptionEnum.TIRE_NO_COUNT_ERROR);
        }
        List<String> tireNoList = requestDto.getVehicleTireNoList().stream().map(AddVehicleTireNoRequestDto::getTireBrand).distinct().collect(Collectors.toList());
        if (tireNoList.size() != requestDto.getVehicleTireNoList().size()){
            throw new BizException(ManagementWebApiExceptionEnum.TIRE_BRAND_REPEAT);
        }
        if (ListUtils.isEmpty(requestDto.getFileList()) || requestDto.getFileList().size() > CommonConstant.INTEGER_FOUR){
            throw new BizException(ManagementWebApiExceptionEnum.INSURANCE_TICKETS_ERROR);
        }
        Result<Boolean> result = vehicleTireServiceApi.addOrModifyVehicleTire(MapperUtils.mapperNoDefault(requestDto, AddOrModifyVehicleTireRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 删除
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> deleteVehicleTire(@RequestBody @Valid VehicleTireIdRequestDto requestDto) {
        Result<Boolean> result = vehicleTireServiceApi.deleteVehicleTire(MapperUtils.mapper(requestDto, VehicleTireIdRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @Override
    public void export(VehicleTireListRequestDto requestDto, HttpServletResponse response) {
        Result<List<VehicleTireListResponseModel>> result = vehicleTireServiceApi.export(MapperUtils.mapper(requestDto, VehicleTireListRequestModel.class));
        result.throwException();
        String fileName = "轮胎管理" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        List<VehicleTireListResponseDto> resultList = MapperUtils.mapper(result.getData(), VehicleTireListResponseDto.class, new VehicleTireListMapping());
        //取轮胎牌号最多的数
        Optional<VehicleTireListResponseDto> maxDto = resultList.stream().max((o1,o2) -> o1.getTireNoCount().compareTo(o2.getTireNoCount()));
        Integer tireNoCountMax = 0;
        if(maxDto.isPresent()){
            tireNoCountMax = maxDto.get().getTireNoCount();
        }

        Map<String, String> excelHeaders = new LinkedHashMap<>();
        excelHeaders.put("车牌号", "vehicleNo");
        excelHeaders.put("车辆机构", "vehiclePropertyLabel");
        excelHeaders.put("司机姓名", "driverName");
        excelHeaders.put("司机联系方式", "driveMobile");
        excelHeaders.put("更换日期", "replaceDate");
        excelHeaders.put("轮胎企业", "tireCompany");
        excelHeaders.put("备注", "remark");
        for (int i=1; i<=tireNoCountMax; i++){
            excelHeaders.put("轮胎号"+i, "tireBrand"+i);
            excelHeaders.put("数量"+i, "amount"+i);
            excelHeaders.put("单价"+i, "unitPrice"+i);
        }
        List<Map> list = new ArrayList<>();
        Map row;
        for (VehicleTireListResponseDto tire:resultList) {
            row = new LinkedHashMap();
            row.put("vehicleNo",tire.getVehicleNo());
            row.put("vehiclePropertyLabel",tire.getVehiclePropertyLabel());
            row.put("driverName",tire.getDriverName());
            row.put("driveMobile",tire.getDriveMobile());
            row.put("replaceDate",tire.getReplaceDate());
            row.put("tireCompany",tire.getTireCompany());
            row.put("remark",tire.getRemark());
            for (int i=1; i<=tire.getVehicleTireNoList().size(); i++){
                row.put("tireBrand"+i,tire.getVehicleTireNoList().get(i-1).getTireBrand());
                row.put("amount"+i,tire.getVehicleTireNoList().get(i-1).getAmount());
                row.put("unitPrice"+i,tire.getVehicleTireNoList().get(i-1).getUnitPrice());
            }
            list.add(row);
        }
        ExcelUtils.exportExcelForServlet(fileName, fileName, excelHeaders, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * Excel导入轮胎管理信息
     * @param file
     * @param request
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ImportVehicleTireInfoResponseDto> importExcelInfoVehicleTireInfo(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_VEHICLE_TIRE_FILE_IS_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            log.error("导入轮胎管理信息失败，", e);
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_VEHICLE_TIRE_FILE_IS_EMPTY);
        }
        List<List<Object>> excelList = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportVehicleTireType());
        if (ListUtils.isNotEmpty(excelList)) {
            ImportVehicleTireInfoRequestDto requestDto = new ImportVehicleTireInfoRequestDto();
            requestDto.setImportList(commonBiz.transferExcelToObject(ImportVehicleTireInfoListRequestDto.class, excelList));
            ImportVehicleTireInfoRequestModel model = initImportTire(requestDto);
            Result<ImportVehicleTireInfoResponseModel> result = vehicleTireServiceApi.importExcelInfoVehicleTireInfo(model);
            result.throwException();
            return Result.success(MapperUtils.mapper(result.getData(), ImportVehicleTireInfoResponseDto.class));
        }
        return Result.success(new ImportVehicleTireInfoResponseDto());
    }
    //入参校验及转换
    public ImportVehicleTireInfoRequestModel initImportTire(ImportVehicleTireInfoRequestDto requestDto){
        ImportVehicleTireInfoRequestModel requestModel = new ImportVehicleTireInfoRequestModel();
        ImportVehicleTireInfoRequestDto source = requestDto;
        Integer failureCount = CommonConstant.INTEGER_ZERO;
        if (ListUtils.isNotEmpty(source.getImportList())){
            ImportVehicleTireInfoListRequestModel tireRequestModel;
            List<ImportVehicleTireInfoListRequestModel> tireRequestModelList = new ArrayList<>();
            ImportVehicleTireListRequestModel vehicleTireNo;
            List<ImportVehicleTireListRequestModel> vehicleTireNoList;
            for (ImportVehicleTireInfoListRequestDto tire:source.getImportList()) {
                tireRequestModel = new ImportVehicleTireInfoListRequestModel();
                if (!FrequentMethodUtils.validateVehicleFormat(tire.getVehicleNo())){
                    failureCount++;
                    continue;
                }
                if (StringUtils.isBlank(tire.getDriveName()) || !FrequentMethodUtils.checkReg(tire.getDriveName(),"[\u4e00-\u9fa5]{1,50}")){
                    failureCount++;
                    continue;
                }
                if (StringUtils.isBlank(tire.getDriveMobile()) || !FrequentMethodUtils.validateTelFormat(tire.getDriveMobile())){
                    failureCount++;
                    continue;
                }
                if (StringUtils.isBlank(tire.getTireCompany()) || !FrequentMethodUtils.checkReg(tire.getTireCompany(),"[\u4e00-\u9fa5]{1,50}")){
                    failureCount++;
                    continue;
                }
                if (StringUtils.isNotBlank(tire.getRemark()) && tire.getRemark().length() > CommonConstant.INTEGER_THREE_HUNDRED){
                    failureCount++;
                    continue;
                }
                Date replaceDate = MapperUtils.mapperNoDefault(tire.getReplaceDate(),Date.class);
                if(replaceDate == null){
                    failureCount++;
                    continue;
                }
                vehicleTireNoList = new ArrayList<>();
                if (StringUtils.isNotBlank(tire.getTireBrand1()) && FrequentMethodUtils.checkReg(tire.getTireBrand1(),TIRE_BRAND_REG)
                        && StringUtils.isNotBlank(tire.getAmount1()) && FrequentMethodUtils.checkReg(tire.getAmount1(),TIRE_AMOUNT_REG)
                        && ConverterUtils.toBigDecimal(tire.getAmount1()).compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO && ConverterUtils.toBigDecimal(tire.getAmount1()).compareTo(CommonConstant.BIG_DECIMAL_ELEVEN) < CommonConstant.INTEGER_ZERO
                        && StringUtils.isNotBlank(tire.getUnitPrice1()) && FrequentMethodUtils.isNumberOrFloatNumberTwo(tire.getUnitPrice1())
                        && ConverterUtils.toBigDecimal(tire.getUnitPrice1()).compareTo(BigDecimal.ZERO) >= CommonConstant.INTEGER_ZERO && ConverterUtils.toBigDecimal(tire.getUnitPrice1()).compareTo(CommonConstant.BIG_DECIMAL_FIVE_THOUSAND) <= CommonConstant.INTEGER_ZERO){
                    vehicleTireNo = new ImportVehicleTireListRequestModel();
                    vehicleTireNo.setTireBrand(tire.getTireBrand1());
                    vehicleTireNo.setAmount(ConverterUtils.toBigDecimal(tire.getAmount1()).intValue());
                    vehicleTireNo.setUnitPrice(ConverterUtils.toBigDecimal(tire.getUnitPrice1()));
                    vehicleTireNoList.add(vehicleTireNo);
                }
                if (StringUtils.isNotBlank(tire.getTireBrand2()) && FrequentMethodUtils.checkReg(tire.getTireBrand2(),TIRE_BRAND_REG)
                        && StringUtils.isNotBlank(tire.getAmount2()) && FrequentMethodUtils.checkReg(tire.getAmount2(),TIRE_AMOUNT_REG)
                        && ConverterUtils.toBigDecimal(tire.getAmount2()).compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO && ConverterUtils.toBigDecimal(tire.getAmount2()).compareTo(CommonConstant.BIG_DECIMAL_ELEVEN) < CommonConstant.INTEGER_ZERO
                        && StringUtils.isNotBlank(tire.getUnitPrice2()) && FrequentMethodUtils.isNumberOrFloatNumberTwo(tire.getUnitPrice2())
                        && ConverterUtils.toBigDecimal(tire.getUnitPrice2()).compareTo(BigDecimal.ZERO) >= CommonConstant.INTEGER_ZERO && ConverterUtils.toBigDecimal(tire.getUnitPrice2()).compareTo(CommonConstant.BIG_DECIMAL_FIVE_THOUSAND) <= CommonConstant.INTEGER_ZERO){
                    vehicleTireNo = new ImportVehicleTireListRequestModel();
                    vehicleTireNo.setTireBrand(tire.getTireBrand2());
                    vehicleTireNo.setAmount(ConverterUtils.toBigDecimal(tire.getAmount2()).intValue());
                    vehicleTireNo.setUnitPrice(ConverterUtils.toBigDecimal(tire.getUnitPrice2()));
                    vehicleTireNoList.add(vehicleTireNo);
                }
                if (StringUtils.isNotBlank(tire.getTireBrand3()) && FrequentMethodUtils.checkReg(tire.getTireBrand3(),TIRE_BRAND_REG)
                        && StringUtils.isNotBlank(tire.getAmount3()) && FrequentMethodUtils.checkReg(tire.getAmount3(),TIRE_AMOUNT_REG)
                        && ConverterUtils.toBigDecimal(tire.getAmount3()).compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO && ConverterUtils.toBigDecimal(tire.getAmount3()).compareTo(CommonConstant.BIG_DECIMAL_ELEVEN) < CommonConstant.INTEGER_ZERO
                        && StringUtils.isNotBlank(tire.getUnitPrice3()) && FrequentMethodUtils.isNumberOrFloatNumberTwo(tire.getUnitPrice3())
                        && ConverterUtils.toBigDecimal(tire.getUnitPrice3()).compareTo(BigDecimal.ZERO) >= CommonConstant.INTEGER_ZERO && ConverterUtils.toBigDecimal(tire.getUnitPrice3()).compareTo(CommonConstant.BIG_DECIMAL_FIVE_THOUSAND) <= CommonConstant.INTEGER_ZERO){
                    vehicleTireNo = new ImportVehicleTireListRequestModel();
                    vehicleTireNo.setTireBrand(tire.getTireBrand3());
                    vehicleTireNo.setAmount(ConverterUtils.toBigDecimal(tire.getAmount3()).intValue());
                    vehicleTireNo.setUnitPrice(ConverterUtils.toBigDecimal(tire.getUnitPrice3()));
                    vehicleTireNoList.add(vehicleTireNo);
                }
                if (StringUtils.isNotBlank(tire.getTireBrand4()) && FrequentMethodUtils.checkReg(tire.getTireBrand4(),TIRE_BRAND_REG)
                        && StringUtils.isNotBlank(tire.getAmount4()) && FrequentMethodUtils.checkReg(tire.getAmount4(),TIRE_AMOUNT_REG)
                        && ConverterUtils.toBigDecimal(tire.getAmount4()).compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO && ConverterUtils.toBigDecimal(tire.getAmount4()).compareTo(CommonConstant.BIG_DECIMAL_ELEVEN) < CommonConstant.INTEGER_ZERO
                        && StringUtils.isNotBlank(tire.getUnitPrice4()) && FrequentMethodUtils.isNumberOrFloatNumberTwo(tire.getUnitPrice4())
                        && ConverterUtils.toBigDecimal(tire.getUnitPrice4()).compareTo(BigDecimal.ZERO) >= CommonConstant.INTEGER_ZERO && ConverterUtils.toBigDecimal(tire.getUnitPrice4()).compareTo(CommonConstant.BIG_DECIMAL_FIVE_THOUSAND) <= CommonConstant.INTEGER_ZERO){
                    vehicleTireNo = new ImportVehicleTireListRequestModel();
                    vehicleTireNo.setTireBrand(tire.getTireBrand4());
                    vehicleTireNo.setAmount(ConverterUtils.toBigDecimal(tire.getAmount4()).intValue());
                    vehicleTireNo.setUnitPrice(ConverterUtils.toBigDecimal(tire.getUnitPrice4()));
                    vehicleTireNoList.add(vehicleTireNo);
                }
                if (StringUtils.isNotBlank(tire.getTireBrand5()) && FrequentMethodUtils.checkReg(tire.getTireBrand5(),TIRE_BRAND_REG)
                        && StringUtils.isNotBlank(tire.getAmount5()) && FrequentMethodUtils.checkReg(tire.getAmount5(),TIRE_AMOUNT_REG)
                        && ConverterUtils.toBigDecimal(tire.getAmount5()).compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO && ConverterUtils.toBigDecimal(tire.getAmount5()).compareTo(CommonConstant.BIG_DECIMAL_ELEVEN) < CommonConstant.INTEGER_ZERO
                        && StringUtils.isNotBlank(tire.getUnitPrice5()) && FrequentMethodUtils.isNumberOrFloatNumberTwo(tire.getUnitPrice5())
                        && ConverterUtils.toBigDecimal(tire.getUnitPrice5()).compareTo(BigDecimal.ZERO) >= CommonConstant.INTEGER_ZERO && ConverterUtils.toBigDecimal(tire.getUnitPrice5()).compareTo(CommonConstant.BIG_DECIMAL_FIVE_THOUSAND) <= CommonConstant.INTEGER_ZERO){
                    vehicleTireNo = new ImportVehicleTireListRequestModel();
                    vehicleTireNo.setTireBrand(tire.getTireBrand5());
                    vehicleTireNo.setAmount(ConverterUtils.toBigDecimal(tire.getAmount5()).intValue());
                    vehicleTireNo.setUnitPrice(ConverterUtils.toBigDecimal(tire.getUnitPrice5()));
                    vehicleTireNoList.add(vehicleTireNo);
                }
                if (ListUtils.isEmpty(vehicleTireNoList)){
                    failureCount++;
                    continue;
                }else{//根据轮胎牌号去重
                    vehicleTireNoList = vehicleTireNoList.stream().collect(collectingAndThen(toCollection(() -> new TreeSet<>(comparing(ImportVehicleTireListRequestModel::getTireBrand))),ArrayList::new));
                }
                tireRequestModel.setVehicleNo(tire.getVehicleNo());
                tireRequestModel.setDriveName(tire.getDriveName());
                tireRequestModel.setDriveMobile(tire.getDriveMobile());
                tireRequestModel.setReplaceDate(replaceDate);
                tireRequestModel.setTireCompany(tire.getTireCompany());
                tireRequestModel.setRemark(StringUtils.isBlank(tire.getRemark())?null:tire.getRemark());
                tireRequestModel.setVehicleTireList(vehicleTireNoList);
                tireRequestModelList.add(tireRequestModel);
            }
            requestModel.setImportList(tireRequestModelList);
            requestModel.setNumberFailures(failureCount);
        }
        return requestModel;
    }

    /**
     * 导入轮胎管理凭证
     * @param file
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> importVehicleTireCertificateInfo(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        SrcUrlDto srcUrlDto = commonBiz.uploadToTmpCatalog(file);
        ImportTireCertificateRequestModel model = initImportTireCertificate(srcUrlDto);
        if (model == null){
            return Result.fail("");
        }
        String uuid = UUIDGenerateUtil.generateUUID();
        Long startTime = System.currentTimeMillis();
        while (!redisLockUtils.tryLock(IMPORT_TIRE_PIC+model.getVehicleNo(),uuid,10)){
            try {
                Thread.sleep(200);
                Long endTime = System.currentTimeMillis();
                if (endTime-startTime > 10000){
                    throw new BizException(ManagementWebApiExceptionEnum.COMMON_IO_EXCEPTION);
                }
            }catch (Exception e){
                log.info(e.getMessage());
            }
        }
        Result<Boolean> result =vehicleTireServiceApi.importVehicleTireCertificateInfo(model);
        redisLockUtils.releaseLock(IMPORT_TIRE_PIC+model.getVehicleNo(),uuid);
        result.throwException();
        return Result.success(true);
    }
    //入参校验及转换
    public ImportTireCertificateRequestModel initImportTireCertificate(SrcUrlDto srcUrlDto){
        if(StringUtils.isBlank(srcUrlDto.getFileName()) || StringUtils.isBlank(srcUrlDto.getRelativePath())){
            return null;
        }
        String fileName = srcUrlDto.getFileName();
        String tireRegex = "([冀豫云辽黑湘皖鲁苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼渝京津沪新军空海北沈兰济南广成使领]{1}[A-Z]{1}[A-Z0-9]{4,5}[a-zA-Z0-9挂学警港澳]{1})+((-轮胎凭证1)|(-轮胎凭证2)|(-轮胎凭证3)|(-轮胎凭证4))+([(][\\d]{4}-[\\d]{2}-[\\d]{2}[)])";
        if (FrequentMethodUtils.match(tireRegex,fileName)){
            return null;
        }
        String vehicleNoRegex = "[冀豫云辽黑湘皖鲁苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼渝京津沪新军空海北沈兰济南广成使领]{1}[A-Z]{1}[A-Z0-9]{4,5}[a-zA-Z0-9挂学警港澳]{1}";
        Pattern pattern = Pattern.compile(vehicleNoRegex);
        Matcher matcher = pattern.matcher(fileName);
        if (!matcher.find()){
            return null;
        }
        String vehicleNo = matcher.group();
        String dateRegex = "[\\d]{4}-[\\d]{2}-[\\d]{2}";
        pattern = Pattern.compile(dateRegex);
        matcher = pattern.matcher(fileName);
        if (!matcher.find()){
            return null;
        }
        Date replaceDate = Optional.ofNullable(DateUtils.stringToDate(matcher.group(),DateUtils.DATE_TO_STRING_SHORT_PATTERN)).orElse(null);
        if (replaceDate == null){
            return null;
        }
        ImportTireCertificateRequestModel model = new ImportTireCertificateRequestModel();
        model.setVehicleNo(vehicleNo);
        model.setReplaceDate(replaceDate);
        model.setFilePath(srcUrlDto.getRelativePath());
        return model;
    }
}
