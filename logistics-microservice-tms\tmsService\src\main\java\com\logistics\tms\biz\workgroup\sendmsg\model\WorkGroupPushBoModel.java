package com.logistics.tms.biz.workgroup.sendmsg.model;

import com.logistics.tms.base.enums.WorkGroupEntrustTypeGroupEnum;
import com.logistics.tms.base.enums.WorkGroupOrderNodeEnum;
import com.logistics.tms.base.enums.WorkGroupOrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WorkGroupPushBoModel {

    @ApiModelProperty("需求单/运单id")
    private Long orderId;

    @ApiModelProperty("需求单来源")
    private Integer orderSource;

    @ApiModelProperty("项目标签")
    private String projectLabel;

    @ApiModelProperty("委托类型")
    private WorkGroupEntrustTypeGroupEnum entrustTypeGroup;

    @ApiModelProperty("单据类型")
    private WorkGroupOrderTypeEnum orderType;

    @ApiModelProperty("单据节点")
    private WorkGroupOrderNodeEnum orderNode;

    public WorkGroupPushBoModel setEntrustTypeGroup(Integer orderEntrustType) {
        this.entrustTypeGroup = WorkGroupEntrustTypeGroupEnum.getEnumByOrderEntrustType(orderEntrustType);
        return this;
    }

    public WorkGroupPushBoModel setEntrustTypeGroup(WorkGroupEntrustTypeGroupEnum orderEntrustType) {
        this.entrustTypeGroup = orderEntrustType;
        return this;
    }
}
