package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/09/19
*/
@Data
public class TShippingFreight extends BaseEntity {
    /**
    * 运价规则name
    */
    @ApiModelProperty("运价规则name")
    private String carrierPriceRuleName;

    /**
    * 业务类型,多个,分割
    */
    @ApiModelProperty("委托类型：-1其他 1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售  多个,分割")
    private String entrustType;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}