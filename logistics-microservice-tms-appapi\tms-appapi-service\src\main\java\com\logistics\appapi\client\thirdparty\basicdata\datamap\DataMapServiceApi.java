package com.logistics.appapi.client.thirdparty.basicdata.datamap;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.thirdparty.basicdata.datamap.hystrix.DataMapServiceApiHystrix;
import com.logistics.appapi.client.thirdparty.basicdata.datamap.request.GetMapListRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.datamap.response.GetMapListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/7 13:17
 */
@FeignClient(name = FeignClientName.BASIC_DATA_SERVICES,
        path = "/service/datamap",
        fallback = DataMapServiceApiHystrix.class)
public interface DataMapServiceApi {

    @ApiOperation("省市区-列表-查询(三级联动)")
    @PostMapping({"/getMapList"})
    Result<List<GetMapListResponseModel>> getMapList(@RequestBody GetMapListRequestModel requestModel);

}
