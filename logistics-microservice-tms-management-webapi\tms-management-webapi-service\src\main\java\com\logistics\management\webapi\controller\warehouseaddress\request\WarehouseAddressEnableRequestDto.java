package com.logistics.management.webapi.controller.warehouseaddress.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author:lei.zhu
 * @date:2021/1/21 11:21
 */
@Data
public class WarehouseAddressEnableRequestDto {
    @ApiModelProperty("仓库ID,逗号分隔")
    @NotBlank(message = "仓库id不能为空")
    private String warehouseAddressIds = "";
    @ApiModelProperty("操作类型 0禁用 1 启用")
    @NotBlank(message = "操作类型不能为空")
    private String operateType;
}
