package com.logistics.tms.api.feign.gpsfee.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/10/8 10:29
 */
@Data
public class SearchGpsFeeListResponseModel {
    private Long gpsFeeId;
    @ApiModelProperty("结算状态：0 待结算，1 部分结算，2 结算完成")
    private Integer status;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("司机")
    private String driverName;
    private String driverPhone;
    @ApiModelProperty("服务商")
    private String gpsServiceProvider;
    @ApiModelProperty("终端型号")
    private String terminalType;
    @ApiModelProperty("服务费")
    private BigDecimal serviceFee;
    @ApiModelProperty("合作周期（月）")
    private Integer cooperationPeriod;
    @ApiModelProperty("SIM卡号")
    private String simNumber;
    @ApiModelProperty("起始日期")
    private Date startDate;
    @ApiModelProperty("截止时间")
    private Date endDate;
    @ApiModelProperty("终止时间")
    private Date finishDate;
    @ApiModelProperty("合作状态：1 已预付，2 进行中，3 已终止")
    private Integer cooperationStatus;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("最后修改人")
    private String lastModifiedBy;
    @ApiModelProperty("最后修改时间")
    private Date lastModifiedTime;
}
