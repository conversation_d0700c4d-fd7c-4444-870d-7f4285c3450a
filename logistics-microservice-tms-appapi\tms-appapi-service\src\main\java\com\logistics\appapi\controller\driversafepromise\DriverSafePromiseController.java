package com.logistics.appapi.controller.driversafepromise;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.client.driversafepromise.DriverSafePromiseClient;
import com.logistics.appapi.client.driversafepromise.request.SafePromiseAppletDetailRequestModel;
import com.logistics.appapi.client.driversafepromise.request.SearchSafePromiseAppletListRequestModel;
import com.logistics.appapi.client.driversafepromise.request.UploadSafePromiseRequestModel;
import com.logistics.appapi.client.driversafepromise.response.SafePromiseAppletDetailResponseModel;
import com.logistics.appapi.client.driversafepromise.response.SearchSafePromiseAppletListResponseModel;
import com.logistics.appapi.client.driversafepromise.response.SummarySignSafePromiseAppletResponseModel;
import com.logistics.appapi.controller.driversafepromise.mapping.SafePromiseDetailMapping;
import com.logistics.appapi.controller.driversafepromise.mapping.SafePromiseListMapping;
import com.logistics.appapi.controller.driversafepromise.request.SafePromiseDetailRequestDto;
import com.logistics.appapi.controller.driversafepromise.request.SearchSafePromiseListRequestDto;
import com.logistics.appapi.controller.driversafepromise.request.UploadSafePromiseRequestDto;
import com.logistics.appapi.controller.driversafepromise.response.SafePromiseDetailResponseDto;
import com.logistics.appapi.controller.driversafepromise.response.SearchSafePromiseListResponseDto;
import com.logistics.appapi.controller.driversafepromise.response.SummarySearchSafePromiseResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2024/3/14 13:43
 */
@Api(value = "司机安全承诺书", tags = "司机安全承诺书")
@RestController
@RequestMapping(value = "/api/applet/safePromise")
public class DriverSafePromiseController {

    @Resource
    private DriverSafePromiseClient driverSafePromiseClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 列表
     * @param requestDto
     * @return
     */
    @ApiOperation("列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchSafePromiseListResponseDto>> searchList(@RequestBody SearchSafePromiseListRequestDto requestDto) {
        Result<PageInfo<SearchSafePromiseAppletListResponseModel>> pageInfoResult = driverSafePromiseClient.searchAppletList(MapperUtils.mapper(requestDto, SearchSafePromiseAppletListRequestModel.class));
        pageInfoResult.throwException();
        PageInfo pageInfoResultData = pageInfoResult.getData();
        if (pageInfoResultData != null) {
            List<SearchSafePromiseAppletListResponseModel> list = pageInfoResultData.getList();
            if (ListUtils.isNotEmpty(list)) {
                List<SearchSafePromiseListResponseDto> dtos = MapperUtils.mapper(list, SearchSafePromiseListResponseDto.class, new SafePromiseListMapping());
                pageInfoResultData.setList(dtos);
            }
        }
        return Result.success(pageInfoResultData);
    }

    /**
     * 详情
     * @param requestDto
     * @return
     */
    @ApiOperation("详情")
    @PostMapping(value = "/getDetail")
    public Result<SafePromiseDetailResponseDto> getDetail(@RequestBody @Valid SafePromiseDetailRequestDto requestDto) {
        Result<SafePromiseAppletDetailResponseModel> detailResult = driverSafePromiseClient.getAppletDetail(MapperUtils.mapper(requestDto, SafePromiseAppletDetailRequestModel.class));
        detailResult.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        sourceSrcList.add(detailResult.getData().getHandPromiseUrl());
        sourceSrcList.add(detailResult.getData().getSignResponsibilityUrl());
        sourceSrcList.add(detailResult.getData().getAttachmentUrl());
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(detailResult.getData(), SafePromiseDetailResponseDto.class, new SafePromiseDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 签订承诺书-上传附件
     * @param requestDto
     * @return
     */
    @ApiOperation("签订承诺书-上传附件")
    @PostMapping(value = "/uploadSafePromise")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> uploadSafePromise(@RequestBody @Valid UploadSafePromiseRequestDto requestDto) {
        UploadSafePromiseRequestModel requestModel = MapperUtils.mapper(requestDto, UploadSafePromiseRequestModel.class);
        requestModel.setSignSafePromiseId(ConverterUtils.toLong(requestDto.getRelationId()));
        return driverSafePromiseClient.uploadSafePromise(requestModel);
    }

    /**
     * 列表头部汇总
     * @param requestDto
     * @return
     */
    @ApiOperation("列表头部汇总")
    @PostMapping(value = "/getSummary")
    public Result<SummarySearchSafePromiseResponseDto> getSummary(@RequestBody SearchSafePromiseListRequestDto requestDto) {
        Result<SummarySignSafePromiseAppletResponseModel>  result = driverSafePromiseClient.searchAppletSummary(MapperUtils.mapper(requestDto,SearchSafePromiseAppletListRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SummarySearchSafePromiseResponseDto.class));
    }
}
