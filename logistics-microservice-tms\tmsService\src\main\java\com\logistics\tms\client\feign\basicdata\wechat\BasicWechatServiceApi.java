package com.logistics.tms.client.feign.basicdata.wechat;

import com.logistics.tms.client.feign.FeignClientName;
import com.logistics.tms.client.feign.basicdata.wechat.hystrix.BasicWechatServiceApiHystrix;
import com.logistics.tms.client.feign.basicdata.wechat.request.CreateGroupChatRequestModel;
import com.logistics.tms.client.feign.basicdata.wechat.request.PushMessageRequestModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;

/**
 * 企业微信接口
 */

@FeignClient(name = FeignClientName.BASIC_DATA_SERVICES,
        path = "/service/wechatPush",
        fallback = BasicWechatServiceApiHystrix.class)
public interface BasicWechatServiceApi {

    @PostMapping(value = "/pushMessage")
    Result<Boolean> pushMessage(@RequestBody Collection<PushMessageRequestModel> requestModel);

    @ApiOperation(value = "创建群聊")
    @PostMapping(value = "/createGroupChat")
    Result<Boolean> createGroupChat(@RequestBody CreateGroupChatRequestModel createGroupChatRequestModel);
}
