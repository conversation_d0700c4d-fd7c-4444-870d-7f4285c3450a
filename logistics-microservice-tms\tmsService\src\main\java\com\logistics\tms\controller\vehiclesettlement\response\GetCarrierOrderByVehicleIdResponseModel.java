package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/10 15:45
 */
@Data
public class GetCarrierOrderByVehicleIdResponseModel {
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("运单状态")
    private Integer status;
    @ApiModelProperty("卸货数量")
    private BigDecimal unloadAmount;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("司机运费价格类型 1 单价 2 一口价")
    private Integer dispatchFreightFeeType;
    @ApiModelProperty("司机运费")
    private BigDecimal dispatchFreightFee;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("多装多卸加价费用")
    private BigDecimal markupFee;
    @ApiModelProperty("司机临时费用")
    private BigDecimal driverOtherFee;
    @ApiModelProperty("车牌号码")
    private String vehicleNo;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机手机号")
    private String driverMobile;
    @ApiModelProperty("调度时间")
    private Date dispatchTime;
    @ApiModelProperty("提货地址")
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    @ApiModelProperty("卸货地址")
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("运单回单图片路径 列表")
    private List<String> ticketList;

    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private Integer demandOrderSource;
}
