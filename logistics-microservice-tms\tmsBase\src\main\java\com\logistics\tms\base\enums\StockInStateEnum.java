package com.logistics.tms.base.enums;

/**
 * 入库状态
 * @author:lei.zhu
 * @date:2021/9/18 15:05:14
 */
public enum StockInStateEnum {
    CANCEL(-1, ""),
    DEFAULT(0, ""),
    WAIT_STOCK_IN(1,"待入库"),
    PART_STOCK_IN(2, "部分入库"),
    STOCK_IN(3,"已入库"),
    CANCEL_WAIT_AUDIT(4, "取消待审核"),
    ABNORMAL_ROLLBACK_WAIT_AUDIT(5, "异常回退待审核"),
    ABNORMAL_ROLLBACK(6, "异常回退"),
    ;

    private Integer key;
    private String value;

    StockInStateEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
