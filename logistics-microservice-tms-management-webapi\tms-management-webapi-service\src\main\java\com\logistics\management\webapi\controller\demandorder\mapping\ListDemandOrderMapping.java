package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderGoodsResponseModel;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.DemandOrderResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
public class ListDemandOrderMapping extends MapperMapping<DemandOrderResponseModel, DemandOrderResponseDto> {

    @Override
    public void configure() {
        DemandOrderResponseModel source = getSource();
        DemandOrderResponseDto destination = getDestination();

        //拼接货物名和规格
        List<DemandOrderGoodsResponseModel> goodsResponseModels = source.getGoodsResponseModels();
        if (ListUtils.isNotEmpty(goodsResponseModels)) {
            BigDecimal amount = BigDecimal.ZERO;
            StringBuilder goodsName = new StringBuilder();
            StringBuilder goodsSize = new StringBuilder();
            for (int index = 0; index < goodsResponseModels.size(); index++) {
                DemandOrderGoodsResponseModel tmpGoodsModel = goodsResponseModels.get(index);
                if (source.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
                    tmpGoodsModel.setGoodsSize(tmpGoodsModel.getGoodsSize() + " " + tmpGoodsModel.getLength() + "*" + tmpGoodsModel.getWidth() + "*" + tmpGoodsModel.getHeight() + "mm");
                }
                if (index != 0) {
                    goodsName.append("/");
                }
                if (StringUtils.isNotBlank(goodsSize.toString()) && StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())) {
                    goodsSize.append("/");
                }
                goodsName.append(tmpGoodsModel.getGoodsName());
                if (StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())) {
                    goodsSize.append(tmpGoodsModel.getGoodsSize());
                }
                amount = amount.add(tmpGoodsModel.getGoodsAmountNumber() == null ? BigDecimal.ZERO : tmpGoodsModel.getGoodsAmountNumber());
            }

            destination.setGoodsName(goodsName.toString());
            destination.setGoodsSize(goodsSize.toString());
        }

        //预计货主费用,无费用类型和预约类型TMS操作，预计货主费用为空
        BigDecimal price = BigDecimal.ZERO;
        if (PriceTypeEnum.DEFAULT.getKey().equals(source.getExceptContractPriceType())
                || (EntrustTypeEnum.BOOKING.getKey().equals(source.getEntrustType()) )){
            destination.setExpectEntrustFee("");
        }else{
            if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getExceptContractPriceType())) {
                price = source.getExceptContractPrice().multiply(source.getGoodsAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getExceptContractPriceType())) {
                price = source.getExceptContractPrice();
            }
            destination.setExpectEntrustFee(ConverterUtils.toString(price));
        }

        //如果实际货主费用类型为空，且没有生成结算数据信息,则列表展示为空
        if (PriceTypeEnum.DEFAULT.getKey().equals(source.getContractPriceType())
                && (source.getPriceType() == null || PriceTypeEnum.DEFAULT.getKey().equals(source.getPriceType()))) {
            destination.setActualEntrustFee("");
        }else if(source.getActualEntrustFee() != null && source.getActualEntrustFee().compareTo(BigDecimal.ZERO) > 0){
            destination.setActualEntrustFee(ConverterUtils.toString(source.getActualEntrustFee()));
        }
        // 发货省市区
        StringBuilder loadAddress = new StringBuilder();
        if (StringUtils.isNotBlank(source.getLoadWarehouse())) {
            destination.setLoadWarehouse("【" + source.getLoadWarehouse() + "】");
        }
        loadAddress.append(Optional.ofNullable(source.getLoadProvinceName()).orElse("")).
                append(Optional.ofNullable(source.getLoadCityName()).orElse("")).
                append(Optional.ofNullable(source.getLoadAreaName()).orElse(""));
        destination.setLoadAddress(loadAddress.toString());
        destination.setLoadDetailAddress(source.getLoadDetailAddress());

        // 收货省市区
        StringBuilder unLoadAddress = new StringBuilder();
        if (StringUtils.isNotBlank(source.getUnloadWarehouse())) {
            destination.setUnloadWarehouse("【"+source.getUnloadWarehouse()+"】");
        }
        unLoadAddress.append(Optional.ofNullable(source.getUnloadProvinceName()).orElse("")).
                append(Optional.ofNullable(source.getUnloadCityName()).orElse("")).
                append(Optional.ofNullable(source.getUnloadAreaName()).orElse(""));
        destination.setUnloadAddress(unLoadAddress.toString());
        destination.setUnloadDetailAddress(source.getUnloadDetailAddress());

        destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
        //数量
        destination.setNotArrangedAmount(Optional.ofNullable(source.getNotArrangedAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
        destination.setGoodsAmount(Optional.ofNullable(source.getGoodsAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
        destination.setArrangedAmount(Optional.ofNullable(source.getArrangedAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
        destination.setBackAmount(Optional.ofNullable(source.getBackAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());

        //转换需求单状态
        if (CommonConstant.INTEGER_ONE.equals(source.getIfCancel())) {
            destination.setStatus(DemandOrderStatusEnum.CANCEL_DISPATCH.getKey().toString());
            destination.setStatusDesc(DemandOrderStatusEnum.CANCEL_DISPATCH.getValue());
        }else if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) {
            destination.setStatus(DemandOrderStatusEnum.ORDER_EMPTY.getKey().toString());
            destination.setStatusDesc(DemandOrderStatusEnum.ORDER_EMPTY.getValue());
        }else{
            destination.setStatusDesc(DemandOrderStatusEnum.getEnum(source.getStatus()).getValue());
        }
        if (CommonConstant.INTEGER_ONE.equals(source.getIfObjection())||CommonConstant.INTEGER_ONE.equals(source.getIfObjectionSinopec())){
            destination.setExportStatusDesc("(" + IfObjectionEnum.YES.getValue() + ")" + destination.getStatusDesc());
        }else{
            destination.setExportStatusDesc(destination.getStatusDesc());
        }

        //时间转换
        if (source.getExpectedUnloadTime() != null) {
            destination.setExpectedUnloadTime(DateUtils.dateToString(source.getExpectedUnloadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (source.getExpectedLoadTime() != null) {
            destination.setExpectedLoadTime(DateUtils.dateToString(source.getExpectedLoadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (source.getTicketTime() != null) {
            destination.setTicketDate(DateUtils.dateToString(source.getTicketTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }

        //默认货主为企业类型
        destination.setCompanyEntrustType(CompanyTypeEnum.COMPANY.getKey().toString());

        //个人车主展示姓名+手机号
        if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())){
            destination.setCompanyCarrierName(source.getCarrierContactName() + " " + source.getCarrierContactMobile());
        }

        //转换下单类型
        destination.setOrderTypeDesc(DemandOrderOrderTypeEnum.getEnum(source.getOrderType()).getValue());

        //中石化推送的单子客户单号展示为：订单号（委托单号）
        if (DemandOrderSourceEnum.SINOPEC.getKey().equals(source.getSource()) && DemandOrderOrderTypeEnum.PUSH.getKey().equals(source.getOrderType())){
            destination.setCustomerOrderCode(source.getSinopecOrderNo() + "（" + source.getCustomerOrderCode() + "）");
        }
        if (StringUtils.isNotBlank(source.getDispatcherName())) {
            destination.setDispatcher(Optional.ofNullable(source.getDispatcherName()).orElse("") + " " + Optional.ofNullable(source.getDispatcherPhone()).orElse(""));
        }

        //是否网货
        destination.setSinopecOnlineGoodsFlagLabel(IfInstallGpsEnum.getEnum(source.getSinopecOnlineGoodsFlag()).getValue());
    }
}
