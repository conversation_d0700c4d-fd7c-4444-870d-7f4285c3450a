<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TEntrustAddressMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TEntrustAddress">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_entrust_id" jdbcType="BIGINT" property="companyEntrustId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="address_type" jdbcType="INTEGER" property="addressType" />
    <result column="province_id" jdbcType="BIGINT" property="provinceId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_id" jdbcType="BIGINT" property="cityId" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="area_id" jdbcType="BIGINT" property="areaId" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="detail_address" jdbcType="VARCHAR" property="detailAddress" />
    <result column="warehouse" jdbcType="VARCHAR" property="warehouse" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_mobile" jdbcType="VARCHAR" property="contactMobile" />
    <result column="entrust_contact_id" jdbcType="BIGINT" property="entrustContactId" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_entrust_id, company_name, address_type, province_id, province_name, city_id, 
    city_name, area_id, area_name, detail_address, warehouse, contact_name, contact_mobile, 
    entrust_contact_id, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_entrust_address
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_entrust_address
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TEntrustAddress">
    insert into t_entrust_address (id, company_entrust_id, company_name, 
      address_type, province_id, province_name, 
      city_id, city_name, area_id, 
      area_name, detail_address, warehouse, 
      contact_name, contact_mobile, entrust_contact_id, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{companyEntrustId,jdbcType=BIGINT}, #{companyName,jdbcType=VARCHAR}, 
      #{addressType,jdbcType=INTEGER}, #{provinceId,jdbcType=BIGINT}, #{provinceName,jdbcType=VARCHAR}, 
      #{cityId,jdbcType=BIGINT}, #{cityName,jdbcType=VARCHAR}, #{areaId,jdbcType=BIGINT}, 
      #{areaName,jdbcType=VARCHAR}, #{detailAddress,jdbcType=VARCHAR}, #{warehouse,jdbcType=VARCHAR}, 
      #{contactName,jdbcType=VARCHAR}, #{contactMobile,jdbcType=VARCHAR}, #{entrustContactId,jdbcType=BIGINT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TEntrustAddress" keyProperty="id" useGeneratedKeys="true">
    insert into t_entrust_address
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyEntrustId != null">
        company_entrust_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="addressType != null">
        address_type,
      </if>
      <if test="provinceId != null">
        province_id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="detailAddress != null">
        detail_address,
      </if>
      <if test="warehouse != null">
        warehouse,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactMobile != null">
        contact_mobile,
      </if>
      <if test="entrustContactId != null">
        entrust_contact_id,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="companyEntrustId != null">
        #{companyEntrustId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="addressType != null">
        #{addressType,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        #{provinceId,jdbcType=BIGINT},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=BIGINT},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="detailAddress != null">
        #{detailAddress,jdbcType=VARCHAR},
      </if>
      <if test="warehouse != null">
        #{warehouse,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactMobile != null">
        #{contactMobile,jdbcType=VARCHAR},
      </if>
      <if test="entrustContactId != null">
        #{entrustContactId,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TEntrustAddress">
    update t_entrust_address
    <set>
      <if test="companyEntrustId != null">
        company_entrust_id = #{companyEntrustId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="addressType != null">
        address_type = #{addressType,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        province_id = #{provinceId,jdbcType=BIGINT},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=BIGINT},
      </if>
      <if test="areaName != null">
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="detailAddress != null">
        detail_address = #{detailAddress,jdbcType=VARCHAR},
      </if>
      <if test="warehouse != null">
        warehouse = #{warehouse,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactMobile != null">
        contact_mobile = #{contactMobile,jdbcType=VARCHAR},
      </if>
      <if test="entrustContactId != null">
        entrust_contact_id = #{entrustContactId,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TEntrustAddress">
    update t_entrust_address
    set company_entrust_id = #{companyEntrustId,jdbcType=BIGINT},
      company_name = #{companyName,jdbcType=VARCHAR},
      address_type = #{addressType,jdbcType=INTEGER},
      province_id = #{provinceId,jdbcType=BIGINT},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=BIGINT},
      city_name = #{cityName,jdbcType=VARCHAR},
      area_id = #{areaId,jdbcType=BIGINT},
      area_name = #{areaName,jdbcType=VARCHAR},
      detail_address = #{detailAddress,jdbcType=VARCHAR},
      warehouse = #{warehouse,jdbcType=VARCHAR},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_mobile = #{contactMobile,jdbcType=VARCHAR},
      entrust_contact_id = #{entrustContactId,jdbcType=BIGINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>