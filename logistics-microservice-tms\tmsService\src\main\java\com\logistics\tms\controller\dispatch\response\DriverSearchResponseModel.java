package com.logistics.tms.controller.dispatch.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DriverSearchResponseModel {
    @ApiModelProperty("司机id")
    private Long driverId;
    @ApiModelProperty("司机名字")
    private String driverName;
    @ApiModelProperty("司机手机号")
    private String driverPhone;
    @ApiModelProperty("司机身份证")
    private String driverIdentityNumber;
}
