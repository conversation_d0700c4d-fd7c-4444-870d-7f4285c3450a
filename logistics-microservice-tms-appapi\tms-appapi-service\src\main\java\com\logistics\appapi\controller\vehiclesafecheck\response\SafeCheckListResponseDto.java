package com.logistics.appapi.controller.vehiclesafecheck.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Author: sj
 * @Date: 2019/11/18 13:22
 */
@Data
public class SafeCheckListResponseDto {
    @ApiModelProperty("安全检查id")
    private String safeCheckVehicleId = "";
    @ApiModelProperty("车辆检查名称-年月+车辆安全检查")
    private String period = "";
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("状态: 0未检查、10待确认、20待整改、30已整改、40检查完成")
    private String status = "";
    @ApiModelProperty("状态文本")
    private String statusLabel = "";
    @ApiModelProperty("检查时间")
    private String checkTime = "";
    @ApiModelProperty("检查项")
    private String checkItemCount = "";
    @ApiModelProperty("合格项")
    private String qualifiedItemCount = "";
    @ApiModelProperty("整改项")
    private String reformItemCount = "";
}
