package com.logistics.appapi.controller.leave.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 请假记录详情查询响应dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/29
 */
@Data
public class LeaveApplyDetailResponseDto {

	@ApiModelProperty("请假申请ID")
	private String leaveApplyId = "";

	@ApiModelProperty("请假申请审核状态,审核状态: 0 待审核，1 已审核，2 已驳回，3 已撤销")
	private String leaveAuditStatus = "";

	@ApiModelProperty("请假申请审核状态文本")
	private String leaveAuditStatusLabel = "";

	@ApiModelProperty("申请人")
	private String leaveApplyStaff;

	@ApiModelProperty("请假类型")
	private String leaveType  = "";

	@ApiModelProperty("请假类型文本")
	private String leaveTypeLabel  = "";

	@ApiModelProperty("请假申请开始时间 (年-月-日 上午/下午)")
	private String leaveStartTime  = "";

	@ApiModelProperty("请假申请结束时间 (年-月-日 上午/下午)")
	private String leaveEndTime  = "";

	@ApiModelProperty("请假时长")
	private String leaveDuration  = "";

	@ApiModelProperty("请假原由")
	private String leaveReason  = "";

	@ApiModelProperty("审核时间")
	private String auditTime  = "";

	@ApiModelProperty("审核备注/撤销备注")
	private String remark  = "";
}
