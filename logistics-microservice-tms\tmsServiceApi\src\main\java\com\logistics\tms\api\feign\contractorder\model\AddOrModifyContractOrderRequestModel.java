package com.logistics.tms.api.feign.contractorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 新增/修改合同信息
 * @Author: sj
 * @Date: 2019/4/4 17:37
 */
@Data
public class AddOrModifyContractOrderRequestModel {
    @ApiModelProperty("合同表ID")
    private Long contractId;
    @ApiModelProperty("外部部合同编号")
    private String contractNoExternal;
    @ApiModelProperty("合同性质：1 货源合同，2 车源合同 , 3 租赁合同")
    private Integer contractNature;
    @ApiModelProperty("合同类型：合同类型：1 框架合同，2 单次合同")
    private Integer contractType;
    @ApiModelProperty("客户ID(签订对象)")
    private Long contractObjectId;
    @ApiModelProperty("客户公司名称，租赁合同使用")
    private String customerCompanyName;
    @ApiModelProperty("公司抬头")
    private String contractHeader;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("合同有效期起始时间")
    private Date contractStartTime;
    @ApiModelProperty("合同有效期结束时间")
    private Date contractEndTime;
    @ApiModelProperty("合同附件列表")
    private List<String> contractFiles;
}
