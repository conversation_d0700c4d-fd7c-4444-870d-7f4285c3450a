package com.logistics.appapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销
 */
@Getter
@AllArgsConstructor
public enum WorkOrderStatusEnum {

    DEFAULT(-1, ""),
    WAITING_TASK(0, "待处理"),
    BEING_PROCESSED(10, "处理中"),
    PROCESSED(20, "已处理"),
    CLOSED(30, "已关闭"),
    RESCINDED(40, "已撤销");

    private final Integer key;
    private final String value;

    public static WorkOrderStatusEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
