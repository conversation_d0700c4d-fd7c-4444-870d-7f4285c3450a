package com.logistics.tms.api.feign.parkingfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/10/9 15:38
 */
@Data
public class ParkingFeeDeductingHistoryResponseModel {
    @ApiModelProperty("扣减历史Id")
    private Long parkingFeeDeductingId;
    @ApiModelProperty("停车费用ID")
    private Long parkingFeeId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("扣减月份")
    private String deductingMonth;
    @ApiModelProperty("总金额")
    private BigDecimal totalFee;
    @ApiModelProperty("扣减费用")
    private BigDecimal deductingFee;
    @ApiModelProperty("剩余未扣减费用")
    private BigDecimal remainingDeductingFee;
    @ApiModelProperty("最后修改人")
    private String lastModifiedBy;
    @ApiModelProperty("最后修改时间")
    private Date lastModifiedTime;
}
