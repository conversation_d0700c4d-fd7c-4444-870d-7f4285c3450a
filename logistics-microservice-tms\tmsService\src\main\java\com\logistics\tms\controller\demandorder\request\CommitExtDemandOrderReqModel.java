package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
public class CommitExtDemandOrderReqModel {



    @ApiModelProperty("当前的运单id")
    private Long carrierOrderId;

    @ApiModelProperty("微信定位的详细地址")
    private String loadAddress;


    /**
     * 定位-经度
     */
    private String longitude;
    /**
     * 定位-纬度
     */
    private String latitude;

    @ApiModelProperty("发货联系人")
    private String consignorName;

    @ApiModelProperty("发货联系方式")
    private String consignorMobile;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("采购单号")
    private String purchaseOrderCode;

    @ApiModelProperty("上游联系人姓名")
    private String upstreamContactPerson;

    @ApiModelProperty("上游联系方式")
    private String upstreamContactPhone;

    @ApiModelProperty("上游企业名称")
    private String upstreamCompanyName;

    @ApiModelProperty("提货数量")
    private BigDecimal loadAmount;

}
