package com.logistics.tms.controller.reservationorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class SearchCarrierReservationOrderReqModel extends AbstractPageForm<SearchCarrierReservationOrderReqModel> {


    /**
     * 勾选运单的参数是运单集合
     */
    @ApiModelProperty(value = "运单id集合 可以为空")
    private List<String> carrierOrderIds;


    /**
     * 不勾选运单情况下参数查车
     */
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;



    /**
     * 不勾选运单情况下参数查提货卸货   提货对应待到达提货地】或【待提货】  卸货对应待到达【待到达卸货地】、【待卸货】
     */
    @ApiModelProperty(value = "提货卸货 1:提货 2.卸货")
    private Integer operateNode;

}
