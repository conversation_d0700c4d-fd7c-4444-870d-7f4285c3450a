package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 中石化确定发布需求单,需求单详情
 *
 * @author: wei.wang
 * @date: 2021/12/3
 */
@Data
public class BatchPublishSinopecDemandDetailDto {

	@ApiModelProperty("需求单id")
	@NotBlank(message = "需求单id不能为空")
	private String demandOrderId;

	@ApiModelProperty("发货省份id")
	@NotBlank(message = "发货省份id不能为空")
	private String loadProvinceId;
	@ApiModelProperty("发货省份名字")
	@NotBlank(message = "发货省份名字不能为空")
	private String loadProvinceName;
	@ApiModelProperty("发货城市id")
	@NotBlank(message = "发货城市id不能为空")
	private String loadCityId;
	@ApiModelProperty("发货城市名字")
	@NotBlank(message = "发货城市名字不能为空")
	private String loadCityName;
	@ApiModelProperty("发货县区id")
	@NotBlank(message = "发货县区id不能为空")
	private String loadAreaId;
	@ApiModelProperty("发货县区名字")
	@NotBlank(message = "发货县区名字不能为空")
	private String loadAreaName;
	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress;

	@ApiModelProperty("收货省份id")
	@NotBlank(message = "收货省份id不能为空")
	private String unloadProvinceId;
	@ApiModelProperty("收货省份名字")
	@NotBlank(message = "收货省份名字不能为空")
	private String unloadProvinceName;
	@ApiModelProperty("收货城市id")
	@NotBlank(message = "收货城市id不能为空")
	private String unloadCityId;
	@ApiModelProperty("收货城市名字")
	@NotBlank(message = "收货城市名字不能为空")
	private String unloadCityName;
	@ApiModelProperty("收货县区id")
	@NotBlank(message = "收货县区id不能为空")
	private String unloadAreaId;
	@ApiModelProperty("收货县区名字")
	@NotBlank(message = "收货县区名字不能为空")
	private String unloadAreaName;
	@ApiModelProperty("收货详细地址")
	@NotBlank(message = "收货详细地址不能为空")
	private String unloadDetailAddress;
}
