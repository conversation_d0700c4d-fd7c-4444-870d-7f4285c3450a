package com.logistics.tms.biz.staff

import com.logistics.tms.controller.staff.request.ImportStaffListRequestModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.biz.customeraccount.CustomerAccountBiz
import com.logistics.tms.mapper.TStaffBasicMapper
import com.logistics.tms.mapper.TStaffDriverContinueLearningRecordMapper
import com.logistics.tms.mapper.TStaffDriverCredentialMapper
import com.logistics.tms.mapper.TStaffDriverIntegrityExaminationRecordMapper
import com.logistics.tms.mapper.TStaffDriverOccupationalRecordMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class StaffTransBizTest extends Specification {
    @Mock
    TStaffBasicMapper tqStaffBasicMapper
    @Mock
    TStaffDriverOccupationalRecordMapper tqStaffDriverOccupationalRecordMapper
    @Mock
    TStaffDriverContinueLearningRecordMapper tqStaffDriverContinueLearningRecordMapper
    @Mock
    TStaffDriverIntegrityExaminationRecordMapper tqStaffDriverIntegrityExaminationRecordMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TStaffDriverCredentialMapper staffDriverCredentialMapper
    @Mock
    CustomerAccountBiz customerAccountBiz
    @InjectMocks
    StaffTransBiz staffTransBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "import Add Staff where requestModel=#requestModel"() {
        expect:
        staffTransBiz.importAddStaff(requestModel)
        assert expectedResult == false

        where:
        requestModel                      || expectedResult
        new ImportStaffListRequestModel() || true
    }

    @Unroll
    def "import Modify Staff where requestModel=#requestModel and staffId=#staffId"() {
        given:
        when(tqStaffDriverOccupationalRecordMapper.selectRecordByStaffIdAndValidDate(anyLong(), any(), any())).thenReturn(0)
        when(tqStaffDriverContinueLearningRecordMapper.selectRecordByStaffIdAndValidDate(anyLong(), any())).thenReturn(0)
        when(tqStaffDriverIntegrityExaminationRecordMapper.selectRecordByStaffIdAndValidDate(anyLong(), any())).thenReturn(0)
        when(staffDriverCredentialMapper.updateByStaffIdSelective(any())).thenReturn(0)

        expect:
        staffTransBiz.importModifyStaff(staffId, requestModel)
        assert expectedResult == false

        where:
        requestModel                                                              | staffId || expectedResult
        new ImportStaffListRequestModel() | 1l || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme