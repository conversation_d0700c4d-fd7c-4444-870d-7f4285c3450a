package com.logistics.management.webapi.api.feign.driveraccount.dto.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchDriverAccountRequestDto extends AbstractPageForm<SearchDriverAccountRequestDto> {

    @ApiModelProperty(value = "司机, 根据司机姓名以及手机号模糊搜索")
    private String driverName;

    @ApiModelProperty(value = "机构, 1 自主 2 外包 3 自营")
    private String driverProperty;

    @ApiModelProperty(value = "银行账号")
    private String bankAccount;

    @ApiModelProperty(value = "新增开始时间: yyyy-MM-dd")
    private String createdStartTime;

    @ApiModelProperty(value = "新增结束时间: yyyy-MM-dd")
    private String createdEndTime;

    @ApiModelProperty(value = "操作开始时间: yyyy-MM-dd")
    private String operateStartTime;

    @ApiModelProperty(value = "操作结束时间: yyyy-MM-dd")
    private String operateEndTime;

}
