<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TWorkGroupMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TWorkGroup">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="group_desc" jdbcType="VARCHAR" property="groupDesc" />
    <result column="group_owner_id" jdbcType="BIGINT" property="groupOwnerId" />
    <result column="group_owner_mobile" jdbcType="VARCHAR" property="groupOwnerMobile" />
    <result column="group_owner_username" jdbcType="VARCHAR" property="groupOwnerUsername" />
    <result column="participant_id" jdbcType="BIGINT" property="participantId" />
    <result column="participant_mobile" jdbcType="VARCHAR" property="participantMobile" />
    <result column="participant_username" jdbcType="VARCHAR" property="participantUsername" />
    <result column="work_group_source" jdbcType="INTEGER" property="workGroupSource" />
    <result column="work_group_code" jdbcType="VARCHAR" property="workGroupCode" />
    <result column="matching_location" jdbcType="VARCHAR" property="matchingLocation" />
    <result column="matching_field" jdbcType="INTEGER" property="matchingField" />
    <result column="config_warehouse" jdbcType="VARCHAR" property="configWarehouse" />
    <result column="entrust_type_group" jdbcType="VARCHAR" property="entrustTypeGroup" />
    <result column="project_label" jdbcType="VARCHAR" property="projectLabel" />
    <result column="enabled" jdbcType="INTEGER" property="enabled" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, group_name, group_desc, group_owner_id, group_owner_mobile, group_owner_username, 
    participant_id, participant_mobile, participant_username, work_group_source, work_group_code, 
    matching_location, matching_field, config_warehouse, entrust_type_group, project_label, 
    enabled, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_work_group
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_work_group
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TWorkGroup">
    insert into t_work_group (id, group_name, group_desc, 
      group_owner_id, group_owner_mobile, group_owner_username, 
      participant_id, participant_mobile, participant_username, 
      work_group_source, work_group_code, matching_location, 
      matching_field, config_warehouse, entrust_type_group, 
      project_label, enabled, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{groupName,jdbcType=VARCHAR}, #{groupDesc,jdbcType=VARCHAR}, 
      #{groupOwnerId,jdbcType=BIGINT}, #{groupOwnerMobile,jdbcType=VARCHAR}, #{groupOwnerUsername,jdbcType=VARCHAR}, 
      #{participantId,jdbcType=BIGINT}, #{participantMobile,jdbcType=VARCHAR}, #{participantUsername,jdbcType=VARCHAR}, 
      #{workGroupSource,jdbcType=INTEGER}, #{workGroupCode,jdbcType=VARCHAR}, #{matchingLocation,jdbcType=VARCHAR}, 
      #{matchingField,jdbcType=INTEGER}, #{configWarehouse,jdbcType=VARCHAR}, #{entrustTypeGroup,jdbcType=VARCHAR}, 
      #{projectLabel,jdbcType=VARCHAR}, #{enabled,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TWorkGroup">
    insert into t_work_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="groupDesc != null">
        group_desc,
      </if>
      <if test="groupOwnerId != null">
        group_owner_id,
      </if>
      <if test="groupOwnerMobile != null">
        group_owner_mobile,
      </if>
      <if test="groupOwnerUsername != null">
        group_owner_username,
      </if>
      <if test="participantId != null">
        participant_id,
      </if>
      <if test="participantMobile != null">
        participant_mobile,
      </if>
      <if test="participantUsername != null">
        participant_username,
      </if>
      <if test="workGroupSource != null">
        work_group_source,
      </if>
      <if test="workGroupCode != null">
        work_group_code,
      </if>
      <if test="matchingLocation != null">
        matching_location,
      </if>
      <if test="matchingField != null">
        matching_field,
      </if>
      <if test="configWarehouse != null">
        config_warehouse,
      </if>
      <if test="entrustTypeGroup != null">
        entrust_type_group,
      </if>
      <if test="projectLabel != null">
        project_label,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="groupDesc != null">
        #{groupDesc,jdbcType=VARCHAR},
      </if>
      <if test="groupOwnerId != null">
        #{groupOwnerId,jdbcType=BIGINT},
      </if>
      <if test="groupOwnerMobile != null">
        #{groupOwnerMobile,jdbcType=VARCHAR},
      </if>
      <if test="groupOwnerUsername != null">
        #{groupOwnerUsername,jdbcType=VARCHAR},
      </if>
      <if test="participantId != null">
        #{participantId,jdbcType=BIGINT},
      </if>
      <if test="participantMobile != null">
        #{participantMobile,jdbcType=VARCHAR},
      </if>
      <if test="participantUsername != null">
        #{participantUsername,jdbcType=VARCHAR},
      </if>
      <if test="workGroupSource != null">
        #{workGroupSource,jdbcType=INTEGER},
      </if>
      <if test="workGroupCode != null">
        #{workGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="matchingLocation != null">
        #{matchingLocation,jdbcType=VARCHAR},
      </if>
      <if test="matchingField != null">
        #{matchingField,jdbcType=INTEGER},
      </if>
      <if test="configWarehouse != null">
        #{configWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="entrustTypeGroup != null">
        #{entrustTypeGroup,jdbcType=VARCHAR},
      </if>
      <if test="projectLabel != null">
        #{projectLabel,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TWorkGroup">
    update t_work_group
    <set>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="groupDesc != null">
        group_desc = #{groupDesc,jdbcType=VARCHAR},
      </if>
      <if test="groupOwnerId != null">
        group_owner_id = #{groupOwnerId,jdbcType=BIGINT},
      </if>
      <if test="groupOwnerMobile != null">
        group_owner_mobile = #{groupOwnerMobile,jdbcType=VARCHAR},
      </if>
      <if test="groupOwnerUsername != null">
        group_owner_username = #{groupOwnerUsername,jdbcType=VARCHAR},
      </if>
      <if test="participantId != null">
        participant_id = #{participantId,jdbcType=BIGINT},
      </if>
      <if test="participantMobile != null">
        participant_mobile = #{participantMobile,jdbcType=VARCHAR},
      </if>
      <if test="participantUsername != null">
        participant_username = #{participantUsername,jdbcType=VARCHAR},
      </if>
      <if test="workGroupSource != null">
        work_group_source = #{workGroupSource,jdbcType=INTEGER},
      </if>
      <if test="workGroupCode != null">
        work_group_code = #{workGroupCode,jdbcType=VARCHAR},
      </if>
      <if test="matchingLocation != null">
        matching_location = #{matchingLocation,jdbcType=VARCHAR},
      </if>
      <if test="matchingField != null">
        matching_field = #{matchingField,jdbcType=INTEGER},
      </if>
      <if test="configWarehouse != null">
        config_warehouse = #{configWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="entrustTypeGroup != null">
        entrust_type_group = #{entrustTypeGroup,jdbcType=VARCHAR},
      </if>
      <if test="projectLabel != null">
        project_label = #{projectLabel,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TWorkGroup">
    update t_work_group
    set group_name = #{groupName,jdbcType=VARCHAR},
      group_desc = #{groupDesc,jdbcType=VARCHAR},
      group_owner_id = #{groupOwnerId,jdbcType=BIGINT},
      group_owner_mobile = #{groupOwnerMobile,jdbcType=VARCHAR},
      group_owner_username = #{groupOwnerUsername,jdbcType=VARCHAR},
      participant_id = #{participantId,jdbcType=BIGINT},
      participant_mobile = #{participantMobile,jdbcType=VARCHAR},
      participant_username = #{participantUsername,jdbcType=VARCHAR},
      work_group_source = #{workGroupSource,jdbcType=INTEGER},
      work_group_code = #{workGroupCode,jdbcType=VARCHAR},
      matching_location = #{matchingLocation,jdbcType=VARCHAR},
      matching_field = #{matchingField,jdbcType=INTEGER},
      config_warehouse = #{configWarehouse,jdbcType=VARCHAR},
      entrust_type_group = #{entrustTypeGroup,jdbcType=VARCHAR},
      project_label = #{projectLabel,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>