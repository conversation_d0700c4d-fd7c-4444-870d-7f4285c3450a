package com.logistics.tms.biz.workgroup.sendmsg.model.mapping;

import com.logistics.tms.controller.demandorder.response.DemandOrderGoodsResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupDemandOrderForLeYiBoModel;
import com.logistics.tms.controller.demandorder.response.DemandOrderForLeYiResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public class WorkGroupDemandOrderForLeYiMapping extends MapperMapping<DemandOrderForLeYiResponseModel, WorkGroupDemandOrderForLeYiBoModel> {

    @Override
    public void configure() {
        DemandOrderForLeYiResponseModel source = getSource();
        WorkGroupDemandOrderForLeYiBoModel destination = getDestination();

        //拼接货物名和规格
        List<DemandOrderGoodsResponseModel> goodsResponseModels = source.getGoodsResponseModels();
        if (ListUtils.isNotEmpty(goodsResponseModels)) {
            BigDecimal amount = BigDecimal.ZERO;
            StringBuilder goodsName = new StringBuilder();
            StringBuilder goodsSize = new StringBuilder();
            for (int index = 0; index < goodsResponseModels.size(); index++) {
                DemandOrderGoodsResponseModel tmpGoodsModel = goodsResponseModels.get(index);
                if (source.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
                    tmpGoodsModel.setGoodsSize(tmpGoodsModel.getGoodsSize() + " " + tmpGoodsModel.getLength() + "*" + tmpGoodsModel.getWidth() + "*" + tmpGoodsModel.getHeight() + "mm");
                }
                if (index != 0) {
                    goodsName.append("/");
                }
                if (StringUtils.isNotBlank(goodsSize.toString()) && StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())) {
                    goodsSize.append("/");
                }
                goodsName.append(tmpGoodsModel.getGoodsName());
                if (StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())) {
                    goodsSize.append(tmpGoodsModel.getGoodsSize());
                }
                amount = amount.add(tmpGoodsModel.getGoodsAmountNumber() == null ? BigDecimal.ZERO : tmpGoodsModel.getGoodsAmountNumber());
            }

            destination.setGoodsName(goodsName.toString());
            destination.setGoodsSize(goodsSize.toString());
        }

        // 发货省市区
        StringBuilder loadAddress = new StringBuilder();
        if (StringUtils.isNotBlank(source.getLoadWarehouse())) {
            destination.setLoadWarehouse("【" + source.getLoadWarehouse() + "】");
        }
        loadAddress.append(Optional.ofNullable(source.getLoadProvinceName()).orElse("")).
                append(Optional.ofNullable(source.getLoadCityName()).orElse("")).
                append(Optional.ofNullable(source.getLoadAreaName()).orElse(""));
        destination.setLoadAddress(loadAddress.toString());
        destination.setLoadDetailAddress(source.getLoadDetailAddress());
        if (StringUtils.isNotBlank(source.getLoadRegionContactName())){
            destination.setLoadRegionContactName(source.getLoadRegionContactName() + " " + source.getLoadRegionContactPhone());
        }
        destination.setConsignor((StringUtils.isNotBlank(source.getConsignorName())?source.getConsignorName():"") + " " + (StringUtils.isNotBlank(source.getConsignorMobile())?source.getConsignorMobile():""));

        // 收货省市区
        StringBuilder unLoadAddress = new StringBuilder();
        if (StringUtils.isNotBlank(source.getUnloadWarehouse())) {
            destination.setUnloadWarehouse("【"+source.getUnloadWarehouse()+"】");
        }
        unLoadAddress.append(Optional.ofNullable(source.getUnloadProvinceName()).orElse("")).
                append(Optional.ofNullable(source.getUnloadCityName()).orElse("")).
                append(Optional.ofNullable(source.getUnloadAreaName()).orElse(""));
        destination.setUnloadAddress(unLoadAddress.toString());
        destination.setUnloadDetailAddress(source.getUnloadDetailAddress());
        destination.setReceiver((StringUtils.isNotBlank(source.getReceiverName())?source.getReceiverName():"") + " " + (StringUtils.isNotBlank(source.getReceiverMobile())?source.getReceiverMobile():""));

        String goodsUnit = GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit();
        //数量
        destination.setNotArrangedAmount(Optional.ofNullable(source.getNotArrangedAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString()+goodsUnit);
        destination.setGoodsAmount(Optional.ofNullable(source.getGoodsAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString()+goodsUnit);
        destination.setArrangedAmount(Optional.ofNullable(source.getArrangedAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString()+goodsUnit);
        destination.setBackAmount(Optional.ofNullable(source.getBackAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString()+goodsUnit);
        destination.setAbnormalAmount(Optional.ofNullable(source.getAbnormalAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString()+goodsUnit);

        //转换需求单状态
        if (CommonConstant.INTEGER_ONE.equals(source.getIfCancel())) {
            destination.setStatusDesc(DemandOrderStatusEnum.CANCEL_DISPATCH.getValue());
        }else if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())) {
            destination.setStatusDesc(DemandOrderStatusEnum.ORDER_EMPTY.getValue());
        }else if (CommonConstant.INTEGER_ONE.equals(source.getIfRollback())) {//已回退
            destination.setStatusDesc(DemandOrderStatusEnum.ROLLBACK.getValue());
        }else{
            destination.setStatusDesc(DemandOrderStatusEnum.getEnum(source.getStatus()).getValue());
        }

        //时间转换
        if (source.getExpectedUnloadTime() != null) {
            destination.setExpectedUnloadTime(DateUtils.dateToString(source.getExpectedUnloadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (source.getExpectedLoadTime() != null) {
            destination.setExpectedLoadTime(DateUtils.dateToString(source.getExpectedLoadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }

        //转换货主名称
        String companyEntrustName = source.getCompanyEntrustName();
        if (EntrustTypeEnum.BOOKING.getKey().equals(source.getEntrustType())){
            companyEntrustName = CommonConstant.LEYI_POINTS_FOR_LOGISTICS;
        }
        destination.setCompanyEntrustName("【公司】"+companyEntrustName);

        //个人车主展示姓名+手机号
        if (CompanyTypeEnum.PERSON.getKey().equals(source.getCompanyCarrierType())){
            destination.setCompanyCarrierName(source.getCarrierContactName()+" "+source.getCarrierContactPhone());
        }

        //转换差异数
        destination.setDifferenceAmount("");
        if(DemandOrderStatusEnum.SIGN_DISPATCH.getKey().equals(source.getStatus())){
            if(source.getDifferenceAmount()!=null) {
                destination.setDifferenceAmount(source.getDifferenceAmount().stripTrailingZeros().toPlainString()+goodsUnit);
            }else{
                destination.setDifferenceAmount("");
            }
        }

        //转换委托类型
        destination.setEntrustType(EntrustTypeEnum.getEnum(source.getEntrustType()).getValue());

        //转换调度时效
        if (source.getStatus() >= DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey()) {
            if (CommonConstant.INTEGER_ONE.equals(source.getIfOverdue())) {
                destination.setDispatchValidity(source.getDispatchValidity() + "(逾期)");
            }else{
                destination.setDispatchValidity(source.getDispatchValidity().toString());
            }
        }else{
            destination.setDispatchValidity("");
        }

        //装卸方式
        destination.setLoadingUnloadingPartLabel(LoadingUnloadingPartEnum.getEnum(source.getLoadingUnloadingPart()).getValue());

        //其他要求
        destination.setOtherRequirements(AvailableOnWeekendsEnum.getEnum(source.getAvailableOnWeekends()).getValue());

        //时效要求
        destination.setRecycleTaskTypeLabel(RecycleTaskTypeEnum.getEnum(source.getRecycleTaskType()).getValue());

        //回退原因
        if (CommonConstant.INTEGER_ONE.equals(source.getIfRollback())) {
            DemandOrderCancelTypeEnum.DemandOrderCancelTypeTwoEnum typeTwoEnum = DemandOrderCancelTypeEnum.DemandOrderCancelTypeTwoEnum.getEnum(source.getRollbackCauseTypeTwo());
            DemandOrderCancelTypeEnum typeEnum = typeTwoEnum.getTypeEnum();
            String rollbackRemark = typeEnum.getValue() +
                    "-" +
                    typeTwoEnum.getValue() +
                    "，" +
                    source.getRollbackRemark();
            destination.setRollbackRemark(rollbackRemark);
        }
    }
}