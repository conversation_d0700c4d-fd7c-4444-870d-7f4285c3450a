package com.logistics.tms.controller.driversafemeeting.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/1 19:45
 */
@Data
public class DriverSafeMeetingKanBanResponseModel {
    @ApiModelProperty("学习例会id")
    private Long safeMeetingId;
    @ApiModelProperty("1:安全例会 2：紧急培训")
    private Integer type ;
    @ApiModelProperty("学习周期")
    private String period;
    @ApiModelProperty("例会标题")
    private String title;

    @ApiModelProperty("学习司机信息")
    private List<DriverSafeMeetingKanBanItemResponseModel> itemList;
}
