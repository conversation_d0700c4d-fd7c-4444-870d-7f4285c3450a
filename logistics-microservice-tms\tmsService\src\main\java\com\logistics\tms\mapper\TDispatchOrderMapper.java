package com.logistics.tms.mapper;

import com.logistics.tms.biz.dispatchorder.model.DispatchOrderItemSqlConditionModel;
import com.logistics.tms.controller.dispatchorder.request.DispatchOrderSearchRequestModel;
import com.logistics.tms.controller.dispatchorder.response.DispatchOrderDetailResponseModel;
import com.logistics.tms.controller.dispatchorder.response.DispatchOrderSearchResponseModel;
import com.logistics.tms.entity.TDispatchOrder;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TDispatchOrderMapper extends BaseMapper<TDispatchOrder>{

    TDispatchOrder selectByPrimaryKeyDecrypt(Long id);

    int insertSelectiveEncrypt(TDispatchOrder tDispatchOrder);

    int updateByPrimaryKeySelectiveEncrypt(TDispatchOrder tDispatchOrder);

    List<Long> searchIdList(@Param("params") DispatchOrderSearchRequestModel requestModel, @Param("companyCarrierId") Long companyCarrierId);

    List<DispatchOrderSearchResponseModel> searchList(@Param("ids") String ids);

    DispatchOrderDetailResponseModel getDetail(@Param("dispatchOrderId") Long dispatchOrderId);

    List<TDispatchOrder> selectByCondition(@Param("param1") DispatchOrderItemSqlConditionModel dispatchOrderItemSqlConditionModel);

    /**
     * 根据调度单编码查询调度单信息
     */
    TDispatchOrder selectByDispatchOrderCode(@Param("dispatchOrderCode") String dispatchOrderCode);
}