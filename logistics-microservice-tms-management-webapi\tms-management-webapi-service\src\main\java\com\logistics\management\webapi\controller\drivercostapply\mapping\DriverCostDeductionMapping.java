package com.logistics.management.webapi.controller.drivercostapply.mapping;

import com.logistics.management.webapi.base.enums.ReserveApplyTypeEnum;
import com.logistics.management.webapi.client.drivercostapply.response.DriverCostDeductionResponseModel;
import com.logistics.management.webapi.controller.drivercostapply.response.ReserveItemDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;
import java.util.Objects;

public class DriverCostDeductionMapping extends MapperMapping<DriverCostDeductionResponseModel, ReserveItemDto> {

    @Override
    public void configure() {
        DriverCostDeductionResponseModel source = getSource();
        ReserveItemDto destination = getDestination();

        // 金额转换
        destination.setBalance(amountConversion(source.getBalance()));
        destination.setWriteOffAmount(amountConversion(source.getWriteOffAmount().negate()));
        // 类型
        destination.setReserveTypeLabel(ReserveApplyTypeEnum.getEnumByKey(source.getReserveType()).getValue());
    }

    private String amountConversion(BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return "";
        }
        return amount.stripTrailingZeros().toPlainString();
    }
}
