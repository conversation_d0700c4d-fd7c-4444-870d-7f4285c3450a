package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TLoanSettlementRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TLoanSettlementRecordMapper extends BaseMapper<TLoanSettlementRecord> {
    List<TLoanSettlementRecord> getSettlementRecordList(@Param("loanRecordId") Long loanRecordId);

    TLoanSettlementRecord getByLoanRecordsIdMonth(@Param("loanRecordId") Long loanRecordId, @Param("settlementMonth") String settlementMonth);
}