package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/21
 */

@Data
public class MQBaseMessageModel {

	@ApiModelProperty("业务组定义的消息类型Key")
	private String key;

	@ApiModelProperty("各自系统的业务单据号")
	private String businessCode;

	@ApiModelProperty("业务类型 1.发货")
	private Integer businessType;

	@ApiModelProperty("业务实体参数")
	private String businessMessage;

	@ApiModelProperty("其他信息")
	private String extParams;
}
