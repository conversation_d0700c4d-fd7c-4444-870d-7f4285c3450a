package com.logistics.appapi.controller.carrierorderotherfee.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/9/2 13:08
 */
@Data
public class CommitCarrierOrderOtherFeeRequestDto {
    @ApiModelProperty(value = "临时费用id",required = true)
    @NotBlank(message = "id不能为空")
    private String carrierOrderOtherFeeId;

    @ApiModelProperty(value = "临时费用",required = true)
    @NotEmpty(message = "请维护临时费用")
    @Valid
    private List<CommitCarrierOrderOtherFeeItemRequestDto> otherFeeList;
}
