package com.logistics.tms.biz.demandorder;

import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.model.CompanyCarrierByIdModel;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.entity.TCompanyCarrier;
import com.logistics.tms.entity.TDemandOrderAddress;
import com.logistics.tms.entity.TDemandOrderEvents;
import com.logistics.tms.entity.TDemandOrderOperateLogs;
import com.logistics.tms.mapper.TCompanyCarrierMapper;
import com.logistics.tms.mapper.TDemandOrderAddressMapper;
import com.logistics.tms.mapper.TDemandOrderCustomerRelMapper;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.SyncSignDemandOrderListToGroundPushModel;
import com.logistics.tms.rabbitmq.publisher.model.SyncSignDemandOrderToGroundPushModel;
import com.yelo.basicdata.api.feign.datamap.model.GetLonLatByMapIdRequestModel;
import com.yelo.basicdata.api.feign.datamap.model.GetLonLatByMapIdResponseModel;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/26 13:55
 */
@Service
public class DemandOrderCommonBiz {

    @Autowired
    private TDemandOrderAddressMapper tDemandOrderAddressMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private BasicDataClient basicDataClient;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TDemandOrderCustomerRelMapper tDemandOrderCustomerRelMapper;
    @Autowired
    private RabbitMqPublishBiz rabbitMqPublishBiz;

    /**
     * 更新需求单地址经纬度
     *
     * @param dbAddressList 需求单地址
     */
    public void updateAddressLonAndLat(List<TDemandOrderAddress> dbAddressList) {
        List<TDemandOrderAddress> upDemandOrderAddressList = new ArrayList<>();
        TDemandOrderAddress upDemandOrderAddress;

        GetLonLatByMapIdRequestModel model;
        StringBuilder loadAddress;//提货省市区
        GetLonLatByMapIdResponseModel loadLonModel;
        StringBuilder unloadAddress;//卸货省市区
        GetLonLatByMapIdResponseModel unloadLonModel;
        for (TDemandOrderAddress address : dbAddressList) {
            if (StringUtils.isBlank(address.getLoadProvinceName()) && StringUtils.isBlank(address.getUnloadProvinceName())) {
                continue;
            }

            loadLonModel = null;
            if (StringUtils.isNotBlank(address.getLoadProvinceName())) {
                loadAddress = new StringBuilder();
                if (StringUtils.isNotBlank(address.getLoadDetailAddress())) {
                    loadAddress.append(address.getLoadProvinceName()).append(address.getLoadCityName()).append(address.getLoadAreaName()).append(address.getLoadDetailAddress());
                }
                model = new GetLonLatByMapIdRequestModel();
                model.setAddress(loadAddress.toString());
                model.setMapId(address.getLoadAreaId());
                loadLonModel = basicDataClient.getLonLatByMapId(model);
            }

            unloadLonModel = null;
            if (StringUtils.isNotBlank(address.getUnloadProvinceName())) {
                unloadAddress = new StringBuilder();
                if (StringUtils.isNotBlank(address.getUnloadDetailAddress())) {
                    unloadAddress.append(address.getUnloadProvinceName()).append(address.getUnloadCityName()).append(address.getUnloadAreaName()).append(address.getUnloadDetailAddress());
                }
                model = new GetLonLatByMapIdRequestModel();
                model.setAddress(unloadAddress.toString());
                model.setMapId(address.getUnloadAreaId());
                unloadLonModel = basicDataClient.getLonLatByMapId(model);
            }
            if ((loadLonModel == null || StringUtils.isBlank(loadLonModel.getLongitude())) && (unloadLonModel == null || StringUtils.isBlank(unloadLonModel.getLongitude()))){
                continue;
            }

            upDemandOrderAddress = new TDemandOrderAddress();
            upDemandOrderAddress.setId(address.getId());
            if (loadLonModel != null) {
                upDemandOrderAddress.setLoadLongitude(loadLonModel.getLongitude());
                upDemandOrderAddress.setLoadLatitude(loadLonModel.getLatitude());
            }
            if (unloadLonModel != null) {
                upDemandOrderAddress.setUnloadLongitude(unloadLonModel.getLongitude());
                upDemandOrderAddress.setUnloadLatitude(unloadLonModel.getLatitude());
            }
            upDemandOrderAddressList.add(upDemandOrderAddress);
        }
        if (ListUtils.isNotEmpty(upDemandOrderAddressList)) {
            tDemandOrderAddressMapper.batchUpdateSelective(upDemandOrderAddressList);
        }
    }

    /**
     * 根据是否我司获取我司或者其他车主 信息
     *
     * @param isOurCompany     是否我司 1:我司 2:其他车主
     * @param companyCarrierId 其他车主ID
     * @return 车主信息
     */
    public CompanyCarrierByIdModel companyCarrierInfoByIsOurCompany(Integer isOurCompany, Long companyCarrierId) {
        CompanyCarrierByIdModel companyCarrierByIdMode = new CompanyCarrierByIdModel();
        if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(isOurCompany)) {//我司
            //查询我司公司信息
            String companyCarrierName = commonBiz.getQiyaCompanyName();
            if (StringUtils.isBlank(companyCarrierName)) {
                throw new BizException(EntrustDataExceptionEnum.QIYA_COMPANY_EMPTY);
            }
            TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.getByName(companyCarrierName);
            if (tCompanyCarrier == null) {
                throw new BizException(EntrustDataExceptionEnum.QIYA_COMPANY_EMPTY);
            }
            companyCarrierByIdMode.setCompanyCarrierName(companyCarrierName);
            companyCarrierByIdMode.setCompanyCarrierId(tCompanyCarrier.getId());
            companyCarrierByIdMode.setCompanyCarrierType(CompanyTypeEnum.COMPANY.getKey());
            companyCarrierByIdMode.setIfAddBlacklist(tCompanyCarrier.getIfAddBlacklist());
        } else {//其他车主
            //查询非我司的车主
            companyCarrierByIdMode = tCompanyCarrierMapper.selectNoOurCompanyCarrierById(companyCarrierId);
            if (companyCarrierByIdMode == null) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
        }
        companyCarrierByIdMode.setCompanyCarrierLevel(isOurCompany);
        return companyCarrierByIdMode;
    }

    public TDemandOrderEvents generateEvent(Long demandOrderId, Long companyCarrierId, DemandOrderEventsTypeEnum typeEnum, String userName) {
        TDemandOrderEvents tDemandOrderEvents = new TDemandOrderEvents();
        tDemandOrderEvents.setDemandOrderId(demandOrderId);
        tDemandOrderEvents.setCompanyCarrierId(companyCarrierId);
        tDemandOrderEvents.setEvent(typeEnum.getKey());
        tDemandOrderEvents.setEventDesc(typeEnum.getValue());
        tDemandOrderEvents.setEventTime(new Date());
        tDemandOrderEvents.setOperatorName(userName);
        tDemandOrderEvents.setOperateTime(new Date());
        commonBiz.setBaseEntityAdd(tDemandOrderEvents, userName);
        return tDemandOrderEvents;
    }

    //创建需求单日志
    public TDemandOrderOperateLogs getDemandOrderOperateLogs(Long demandOrderId, DemandOrderOperateLogsEnum logsEnum, String userName, String remark) {
        TDemandOrderOperateLogs demandOrderOperateLogs = new TDemandOrderOperateLogs();
        demandOrderOperateLogs.setDemandOrderId(demandOrderId);
        demandOrderOperateLogs.setOperationType(logsEnum.getKey());
        demandOrderOperateLogs.setOperationContent(logsEnum.getValue());
        demandOrderOperateLogs.setRemark(remark);
        demandOrderOperateLogs.setOperatorName(userName);
        demandOrderOperateLogs.setOperateTime(new Date());
        commonBiz.setBaseEntityAdd(demandOrderOperateLogs, userName);
        return demandOrderOperateLogs;
    }

    /**
     * 需求单（HR单）签收同步给地推系统
     * @param demandOrderIdList 签收的需求单id
     * @param signUser 签收人
     */
    public void demandOrderSignSyncGroundPush(List<Long> demandOrderIdList, String signUser){
        if (ListUtils.isEmpty(demandOrderIdList)){
            return;
        }
        List<SyncSignDemandOrderListToGroundPushModel> signDemandOrderList = tDemandOrderCustomerRelMapper.getByDemandOrderIds(StringUtils.listToString(demandOrderIdList,','));
        if (ListUtils.isEmpty(signDemandOrderList)){
            return;
        }

        SyncSignDemandOrderToGroundPushModel model = new SyncSignDemandOrderToGroundPushModel();
        model.setSignDemandOrderList(signDemandOrderList);
        model.setSignTime(new Date());
        model.setSignUser(signUser);
        model.setOperator(signUser);
        rabbitMqPublishBiz.syncSignDemandOrderToGroundPush(model);
    }
}
