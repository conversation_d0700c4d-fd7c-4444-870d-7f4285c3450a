package com.logistics.tms.api.feign.vehiclepayeerel.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.vehiclepayeerel.VehiclePayeeRelServiceApi;
import com.logistics.tms.api.feign.vehiclepayeerel.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/7/11 14:06
 */
@Component("tmsVehiclePayeeRelServiceApiHystrix")
public class VehiclePayeeRelServiceApiHystrix implements VehiclePayeeRelServiceApi {
    @Override
    public Result<PageInfo<SearchVehiclePayeeRelListResponseModel>> searchVehiclePayeeRelList(SearchVehiclePayeeRelListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<VehiclePayeeRelDetailResponseModel> getVehiclePayeeRelDetail(VehiclePayeeRelIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addOrModifyVehiclePayeeRel(AddOrModifyVehiclePayeeRelRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delVehiclePayeeRel(VehiclePayeeRelIdsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchVehiclePayeeRelListResponseModel>> exportVehiclePayeeRel(SearchVehiclePayeeRelListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ImportVehiclePayeeRelResponseModel> importVehiclePayeeRel(ImportVehiclePayeeRelRequestModel requestModel) {
        return Result.timeout();
    }
}
