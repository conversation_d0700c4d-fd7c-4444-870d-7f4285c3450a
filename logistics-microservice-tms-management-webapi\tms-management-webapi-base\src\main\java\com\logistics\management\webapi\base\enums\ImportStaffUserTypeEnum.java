package com.logistics.management.webapi.base.enums;


public enum ImportStaffUserTypeEnum {
    DRIVER(1,"驾驶员"),
    SUPER_CARGO(2,"押运员"),
    DRIVER_CARGO(3,"驾驶员及押运员"),
    ;
    private Integer key;
    private String value;

    ImportStaffUserTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static ImportStaffUserTypeEnum getEnum(String value) {
        for (ImportStaffUserTypeEnum t : values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return null;
    }

    public static ImportStaffUserTypeEnum getEnumByKey(Integer key) {
        for (ImportStaffUserTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
