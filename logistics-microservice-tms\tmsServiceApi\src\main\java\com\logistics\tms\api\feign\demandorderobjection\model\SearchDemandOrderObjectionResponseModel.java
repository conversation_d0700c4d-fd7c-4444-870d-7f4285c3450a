package com.logistics.tms.api.feign.demandorderobjection.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SearchDemandOrderObjectionResponseModel {
    @ApiModelProperty("需求单id")
    private Long demandId;
    @ApiModelProperty("需求单状态：1000待调度 2000部分调度 3000调度完成")
    private Integer status;
    private Integer ifCancel;//是否取消：0 否，1 是
    private Integer ifEmpty;//是否放空：0 否，1 是
    private Integer ifRollback;//是否回退
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("上报时间")
    private Date reportTime;
    @ApiModelProperty("上报人")
    private String reportContactName;
    @ApiModelProperty("客户名称")
    private String customerName;
    @ApiModelProperty("问题类型")
    private Integer objectionType;
    @ApiModelProperty("问题描述")
    private String objectionReason;
    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;
    @ApiModelProperty("地区（提货大区）")
    private String loadRegionName;

    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private Integer demandOrderSource;
}
