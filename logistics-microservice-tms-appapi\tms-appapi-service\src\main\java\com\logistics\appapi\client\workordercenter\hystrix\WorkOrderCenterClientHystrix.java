package com.logistics.appapi.client.workordercenter.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.workordercenter.WorkOrderCenterClient;
import com.logistics.appapi.client.workordercenter.request.*;
import com.logistics.appapi.client.workordercenter.response.WorkOrderDetailAppletResponseModel;
import com.logistics.appapi.client.workordercenter.response.WorkOrderListAppletResponseModel;
import com.logistics.appapi.client.workordercenter.response.WorkOrderProcessAppletResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/7 14:08
 */
@Component
public class WorkOrderCenterClientHystrix implements WorkOrderCenterClient {
    @Override
    public Result<PageInfo<WorkOrderListAppletResponseModel>> workOrderListForApplet(WorkOrderListAppletRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<WorkOrderDetailAppletResponseModel> workOrderDetailForApplet(WorkOrderDetailAppletRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<WorkOrderProcessAppletResponseModel>> workOrderProcessForApplet(WorkOrderDetailAppletRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancelWorkOrder(CancelWorkOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> reportWorkException(ReportWorkExceptionRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> reReportWorkException(ReReportWorkExceptionRequestModel requestModel) {
        return Result.timeout();
    }
}
