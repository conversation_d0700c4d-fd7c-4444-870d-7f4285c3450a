package com.logistics.tms.biz.demandorderobjection;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionRequestModel;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionResponseModel;
import com.logistics.tms.mapper.TDemandOrderObjectionMapper;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author:lei.zhu
 * @date:2021/11/3 10:52:46
 */
@Service
public class DemandOrderObjectionBiz {

    @Autowired
    private TDemandOrderObjectionMapper tDemandOrderObjectionMapper;

    /**
     * 需求单异常表
     *
     * @param  requestModel
     */
    public PageInfo<SearchDemandOrderObjectionResponseModel> searchDemandOrderObjection(SearchDemandOrderObjectionRequestModel requestModel) {
        requestModel.enablePaging();
        List<Long> ids=tDemandOrderObjectionMapper.searchDemandOrderObjectionIds(requestModel);
        if(ListUtils.isEmpty(ids)){
            return new PageInfo<>();
        }
        PageInfo page=new PageInfo(ids);
        List<SearchDemandOrderObjectionResponseModel> models=tDemandOrderObjectionMapper.searchDemandOrderObjection(StringUtils.listToString(ids,','));
        page.setList(models);
        return page;
    }
}
