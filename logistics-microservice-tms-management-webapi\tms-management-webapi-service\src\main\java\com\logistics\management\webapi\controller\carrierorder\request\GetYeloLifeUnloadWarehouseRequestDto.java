package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/24
 */
@Data
public class GetYeloLifeUnloadWarehouseRequestDto {

	@ApiModelProperty(value = "卸货仓库查询关键字", required = true)
	@NotBlank(message = "请输入卸货地址关键字查询")
	private String unloadSearchName;
}
