package com.logistics.management.webapi.client.oilfilled;


import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.oilfilled.hystrix.OilFilledServiceHystrix;
import com.logistics.management.webapi.client.oilfilled.request.*;
import com.logistics.management.webapi.client.oilfilled.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = OilFilledServiceHystrix.class)
public interface OilFilledServiceClient {

    @PostMapping(value = "/service/oilFilled/searchList")
    @ApiOperation(value = "充油列表")
    Result<PageInfo<OilFilledListResponseModel>> searchList(@RequestBody OilFilledListRequestModel requestModel);

    @PostMapping(value = "/service/oilFilled/addOrModify")
    @ApiOperation(value = "新增/修改充油")
    Result<Boolean> addOrModify(@RequestBody AddOrModifyOilFilledRequestModel requestModel);

    @PostMapping(value = "/service/oilFilled/getSummary")
    @ApiOperation(value = "充油列表汇总")
    Result<OilFilledGetSummaryResponseModel> getSummary(@RequestBody OilFilledListRequestModel requestModel);

    @PostMapping(value = "/service/oilFilled/getDetail")
    @ApiOperation(value = "充油详情接口")
    Result<OilFilledDetailResponseModel> getDetail(@RequestBody OilFilledDetailRequestModel requestModel);

    @PostMapping(value = "/service/oilFilled/getOperationRecord")
    @ApiOperation(value = "充油操作记录")
    Result<List<OilFilledOperationRecordResponseModel>> getOperationRecord(@RequestBody OilFilledOperationRecordRequestModel requestModel);

    @PostMapping(value = "/service/oilFilled/addOrModifyRefund")
    @ApiOperation(value = "新增/修改油费退款")
    Result<Boolean> addOrModifyRefund(@RequestBody AddOrModifyOilRefundRequestModel requestModel);

    @PostMapping(value = "/service/oilFilled/getOilRefundDetail")
    @ApiOperation(value = "油费退款详情")
    Result<OilRefundDetailResponseModel> getOilRefundDetail(@RequestBody OilFilledDetailRequestModel requestModel);

    @PostMapping(value = "/service/oilFilled/importRefuelCard")
    @ApiOperation(value = "导入充油卡")
    Result<ImportOilFilledResponseModel> importRefuelCard(@RequestBody ImportOilFilledCardInfoRequestModel requestModel);

    @PostMapping(value = "/service/oilFilled/importRefuelCar")
    @ApiOperation(value = "导入加油车")
    Result<ImportOilFilledResponseModel> importRefuelCar(@RequestBody ImportOilFilledCarInfoRequestModel requestModel);
}
