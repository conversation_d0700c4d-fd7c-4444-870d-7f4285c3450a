package com.logistics.management.webapi.api.impl.attendance.mapping;

import com.logistics.management.webapi.api.feign.attendance.dto.SearchAttendanceListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.StaffPropertyEnum;
import com.logistics.tms.api.feign.attendance.model.SearchAttendanceListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

import java.util.Date;

public class SearchAttendanceListMapping extends MapperMapping<SearchAttendanceListResponseModel, SearchAttendanceListResponseDto> {

    @Override
    public void configure() {

        SearchAttendanceListResponseModel source = getSource();
        SearchAttendanceListResponseDto destination = getDestination();

        // 司机名称转换
        String staffName = source.getStaffName();
        String staffMobile = source.getStaffMobile();
        destination.setAttendanceUser(String.join( "_", staffName, staffMobile));

        // 机构名称转换
        destination.setStaffPropertyLabel(StaffPropertyEnum.getEnum(source.getStaffProperty()).getValue());

        // 考勤日期转换
        Date attendanceDate = source.getAttendanceDate();
        destination.setAttendanceMonth(DateUtils.dateToString(attendanceDate, CommonConstant.DATE_TO_STRING_YM_PATTERN_TEXT));
        destination.setAttendanceDate(DateUtils.dateToString(attendanceDate, CommonConstant.DATE_TO_STRING_YMD_PATTERN_TEXT));

        // 工时转换
        destination.setManHour(source.getManHour().stripTrailingZeros().toPlainString());

    }
}
