package com.logistics.management.webapi.base.enums;

/**
 * Created by yuhong.lin on 2019/1/18
 */
public enum LoadingUnloadingPartEnum {
    DEFAULT(0 ,""),
    MARK_UP(1, "我司装卸"),
    MARK_DOWN(2, "客户装卸")
    ;

    private Integer key;
    private String value;

    LoadingUnloadingPartEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static LoadingUnloadingPartEnum getEnum(Integer key) {
        for (LoadingUnloadingPartEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }
}
