/**
 * Created by yun<PERSON><PERSON><PERSON> on 2017/10/23.
 */
package com.logistics.management.webapi.base.enums;

public enum CarrierOrderTicketsTypeEnum {
    NULL(-99,""),
    CARRIER_ORDER_LOAD_TICKETS(1, "提货单"),
    CARRIER_ORDER_UNLOAD_TICKETS(2, "出库单"),
    CARRIER_ORDER_SIGN_TICKETS(3, "签收单"),
    CARRIER_ORDER_OTHER_TICKETS(4, "其他单据"),
    CARRIER_ORDER_ARRIVE_PICK_UP(5, "到达提货地凭证"),
    CARRIER_ORDER_ARRIVE_UNLOADING(6, "到达卸货地凭证"),
    CARRIER_ORDER_STOCK_IN_TICKETS(7, "入库单"),
    CARRIER_ORDER_LOAD_SCENE_PIC(8, "提货现场图片"),
    CARRIER_ORDER_UNLOAD_SCENE_PIC(9, "卸货现场图片"),
    ;
    private Integer key;
    private String value;

    CarrierOrderTicketsTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;

    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CarrierOrderTicketsTypeEnum getEnum(Integer key) {
        for (CarrierOrderTicketsTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return NULL;
    }

}
