package com.logistics.tms.rabbitmq.consumer.tms;

import com.yelo.tools.rabbitmq.annocation.EnableMqErrorMessageCollect;
import com.logistics.tms.biz.carrierorder.CarrierOrderForLeYiBiz;
import com.logistics.tms.rabbitmq.consumer.model.SyncAbnormalAmountMessage;
import com.rabbitmq.client.Channel;
import com.yelo.tools.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;


/**
 * 接受云仓发起的异常数并同步给云盘
 */
@Component
@Slf4j
public class SyncAbnormalAmountConsumer {

	private final ObjectMapper objectMapper = JacksonUtils.getInstance();

	@Autowired
	private CarrierOrderForLeYiBiz carrierOrderForLeYiBiz;

	@EnableMqErrorMessageCollect
	@RabbitListener(bindings = {@QueueBinding(
			exchange = @Exchange(name = "logistics.topic", type = "topic"),
			value = @Queue(value = "logistics.tmsSyncAbnormalAmount", durable = "true"),
			key = "tmsSyncAbnormalAmount")}, concurrency = "3")
	public void process(@Payload String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {
		SyncAbnormalAmountMessage abnormalAmountMessage = objectMapper.readValue(message, SyncAbnormalAmountMessage.class);
		log.info("接收云仓发起异常：" + abnormalAmountMessage.toString());
		carrierOrderForLeYiBiz.syncAbnormalAmount(abnormalAmountMessage);
		channel.basicAck(deliveryTag, false);
	}

}
