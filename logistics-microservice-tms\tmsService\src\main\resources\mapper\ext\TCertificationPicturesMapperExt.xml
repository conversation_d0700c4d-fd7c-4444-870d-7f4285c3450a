<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCertificationPicturesMapper" >
  <select id="getByObjectIdType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_certification_pictures
    where valid = 1
    and object_type = #{objectType,jdbcType=INTEGER}
    and object_id = #{objectId,jdbcType=BIGINT}
    and file_type = #{fileType,jdbcType=INTEGER}
    order by upload_time desc, id desc
  </select>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TCertificationPictures" >
    <foreach collection="list" item="item" separator=";">
      insert into t_certification_pictures
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.objectType != null" >
          object_type,
        </if>
        <if test="item.objectId != null" >
          object_id,
        </if>
        <if test="item.fileType != null" >
          file_type,
        </if>
        <if test="item.fileTypeName != null" >
          file_type_name,
        </if>
        <if test="item.fileName != null" >
          file_name,
        </if>
        <if test="item.filePath != null" >
          file_path,
        </if>
        <if test="item.uploadUserName != null" >
          upload_user_name,
        </if>
        <if test="item.uploadTime != null" >
          upload_time,
        </if>
        <if test="item.suffix != null" >
          suffix,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.objectType != null" >
          #{item.objectType,jdbcType=INTEGER},
        </if>
        <if test="item.objectId != null" >
          #{item.objectId,jdbcType=BIGINT},
        </if>
        <if test="item.fileType != null" >
          #{item.fileType,jdbcType=INTEGER},
        </if>
        <if test="item.fileTypeName != null" >
          #{item.fileTypeName,jdbcType=VARCHAR},
        </if>
        <if test="item.fileName != null" >
          #{item.fileName,jdbcType=VARCHAR},
        </if>
        <if test="item.filePath != null" >
          #{item.filePath,jdbcType=VARCHAR},
        </if>
        <if test="item.uploadUserName != null" >
          #{item.uploadUserName,jdbcType=VARCHAR},
        </if>
        <if test="item.uploadTime != null" >
          #{item.uploadTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.suffix != null" >
          #{item.suffix,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="com.logistics.tms.entity.TCertificationPictures" >
    <foreach collection="list" item="item" separator=";">
      update t_certification_pictures
      <set >
        <if test="item.objectType != null" >
          object_type = #{item.objectType,jdbcType=INTEGER},
        </if>
        <if test="item.objectId != null" >
          object_id = #{item.objectId,jdbcType=BIGINT},
        </if>
        <if test="item.fileType != null" >
          file_type = #{item.fileType,jdbcType=INTEGER},
        </if>
        <if test="item.fileTypeName != null" >
          file_type_name = #{item.fileTypeName,jdbcType=VARCHAR},
        </if>
        <if test="item.fileName != null" >
          file_name = #{item.fileName,jdbcType=VARCHAR},
        </if>
        <if test="item.filePath != null" >
          file_path = #{item.filePath,jdbcType=VARCHAR},
        </if>
        <if test="item.uploadUserName != null" >
          upload_user_name = #{item.uploadUserName,jdbcType=VARCHAR},
        </if>
        <if test="item.uploadTime != null" >
          upload_time = #{item.uploadTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.suffix != null" >
          suffix = #{item.suffix,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="updateFilePath">
    update t_certification_pictures tqcp set tqcp.valid = 0
    where tqcp.valid = 1
    and tqcp.object_id = #{params.objectId,jdbcType = BIGINT}
    and tqcp.object_type = #{params.objectType,jdbcType = INTEGER}
    and tqcp.file_type = #{params.fileType,jdbcType = INTEGER }
    and tqcp.file_path not in (${params.filePaths})
  </update>

  <select id="getPicsByIdsAndType" resultType="com.logistics.tms.api.feign.common.model.CertificatePictureModel">
    select
    object_id as objectId,
    file_type_name as fileTypeName,
    file_path as filePath,
    upload_user_name as uploadUserName,
    upload_time as uploadTime
    from t_certification_pictures
    where valid = 1
    and object_id in (${objectIds})
    and object_type = #{objectType,jdbcType=BIGINT}
    and file_type = #{fileType,jdbcType=BIGINT}
    order by upload_time desc
  </select>

  <select id="getTPicsByIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    from t_certification_pictures
    where valid  = 1
    and object_id in (${objectIds})
    and object_type in (${objectTypes})
  </select>

  <select id="getUnionFiles" resultMap="BaseResultMap">
     select <include refid="Base_Column_List"/>
     from t_certification_pictures
     where valid = 1
     <if test="list != null and list.size > 0">
       <trim prefix="and(" suffix=")" prefixOverrides="or">
         <foreach collection="list" index="index" item="item" separator=" ">
           or ( object_type = #{item.objectType,jdbcType = INTEGER}
           and object_id = #{item.objectId,jdbcType = BIGINT}
           and file_type = #{item.fileType,jdbcType = INTEGER} )
         </foreach>
       </trim>
    </if>
  </select>
  <select id="getByObjectIdAndType" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from t_certification_pictures
      where valid = 1
      and object_type = #{objectType,jdbcType=INTEGER}
      and object_id = #{objectId,jdbcType=BIGINT}
      order by upload_time desc,id desc
  </select>

  <select id="getImageByIdsAndType" resultMap="BaseResultMap">
      select
        <include refid="Base_Column_List"/>
      from t_certification_pictures
      where valid = 1
        and object_id in (${objectIds})
        and object_type = #{objectType,jdbcType=BIGINT}
      order by upload_time desc
  </select>

  <update id="delByObjectTypeId">
    update t_certification_pictures
    set valid=0,last_modified_by=#{operatorName,jdbcType=VARCHAR},last_modified_time=now()
    where object_type=#{objectType,jdbcType=INTEGER}
    and object_id =#{objectId,jdbcType=BIGINT}
  </update>

  <update id="delByObjectTypeFileTypeId">
    update t_certification_pictures
    set valid=0,last_modified_by=#{operatorName,jdbcType=VARCHAR},last_modified_time=now()
    where object_type=#{objectType,jdbcType=INTEGER}
    and file_type=#{fileType,jdbcType=INTEGER}
    and object_id =#{objectId,jdbcType=BIGINT}
  </update>

  <update id="delByObjectTypeObjectIds">
    update t_certification_pictures
    set valid=0,last_modified_by=#{operatorName,jdbcType=VARCHAR},last_modified_time=now()
    where object_type=#{objectType,jdbcType=INTEGER}
    and object_id in (${objectIds})
  </update>
</mapper>