package com.logistics.management.webapi.controller.carrierorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SearchCarrierOrderListRequestDto extends AbstractPageForm<SearchCarrierOrderListRequestDto> {
    @ApiModelProperty("运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消 1 待审核")
    private String  status;
    @ApiModelProperty("运单号")
    private String  carrierOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("需求单号")
    private String  demandOrderCode;
    @ApiModelProperty("车牌号")
    private String  vehicleNo;
    @ApiModelProperty("司机")
    private String  driver;
    @ApiModelProperty("发货地址")
    private String  loadAddress;
    @ApiModelProperty("收货地址")
    private String  unloadAddress;
    @ApiModelProperty("调度人")
    private String  dispatchUserName;
    @ApiModelProperty("预计到货时间")
    private String  expectArrivalTimeFrom;
    @ApiModelProperty("预计到货时间")
    private String  expectArrivalTimeTo;
    @ApiModelProperty("运单生成时间")
    private String dispatchTimeFrom;
    @ApiModelProperty("运单生成时间")
    private String dispatchTimeTo;
    @ApiModelProperty("下单时间from")
    private String demandCreatedTimeFrom;
    @ApiModelProperty("下单时间to")
    private String demandCreatedTimeTo;
    @ApiModelProperty("车辆需要审核 0 无需审核 1 需要审核")
    private String ifWaitAudit;
    @ApiModelProperty("承运商")
    private String carrierCompany;
    @ApiModelProperty("委托方,逗号分隔")
    private String companyEntrustIds;

    @ApiModelProperty("车辆停运检测,跳转列表")
    private String carrierOrderIds;

    @ApiModelProperty("是否异常：0 否，1 是")
    private String ifObjection;

    @ApiModelProperty("批量查询-需求单号")
    private List<String> demandOrderCodeList;

    @ApiModelProperty("批量查询-客户单号")
    private List<String> customerOrderCodeList;
}
