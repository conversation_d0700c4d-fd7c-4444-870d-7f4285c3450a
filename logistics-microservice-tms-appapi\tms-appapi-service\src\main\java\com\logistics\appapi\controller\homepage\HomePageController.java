package com.logistics.appapi.controller.homepage;

import com.logistics.appapi.client.homepage.HomePageClient;
import com.logistics.appapi.client.homepage.response.HomeOrderCollectResponseModel;
import com.logistics.appapi.client.homepage.response.VerifyWarehousePermissionResponseModel;
import com.logistics.appapi.controller.homepage.response.HomeOrderCollectResponseDto;
import com.logistics.appapi.controller.homepage.response.VerifyWarehousePermissionResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: wjf
 * @date: 2024/3/7 10:59
 */
@Api(value = "主页", tags = "主页")
@RestController
@RequestMapping(value = "/api/applet/homePage")
public class HomePageController {

    @Resource
    private HomePageClient homeOrderCollect;

    /**
     * 首页单据汇总
     *
     * @return 运单汇总数量
     */
    @ApiOperation(value = "首页单据汇总", tags = "1.1.2")
    @PostMapping(value = "/homeOrderCollect")
    public Result<HomeOrderCollectResponseDto> homeOrderCollect() {
        Result<HomeOrderCollectResponseModel> modelResult = homeOrderCollect.homeOrderCollect();
        modelResult.throwException();
        return Result.success(MapperUtils.mapper(modelResult.getData(), HomeOrderCollectResponseDto.class));
    }

    /**
     * 验证是否可以跳转云仓小程序
     *
     * @return 云仓小程序token
     */
    @ApiOperation(value = "验证是否可以跳转云仓小程序", tags = "1.1.0")
    @PostMapping(value = "/verifyWarehousePermission")
    public Result<VerifyWarehousePermissionResponseDto> verifyWarehousePermission() {
        Result<VerifyWarehousePermissionResponseModel> result = homeOrderCollect.verifyWarehousePermission();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), VerifyWarehousePermissionResponseDto.class));
    }
}
