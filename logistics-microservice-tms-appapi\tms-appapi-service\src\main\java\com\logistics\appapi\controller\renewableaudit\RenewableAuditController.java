package com.logistics.appapi.controller.renewableaudit;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.client.renewableaudit.RenewableAuditClient;
import com.logistics.appapi.client.renewableaudit.request.*;
import com.logistics.appapi.client.renewableaudit.response.*;
import com.logistics.appapi.controller.renewableaudit.mapping.RenewableOrderDetailMapping;
import com.logistics.appapi.controller.renewableaudit.mapping.RenewableOrderListMapping;
import com.logistics.appapi.controller.renewableaudit.mapping.SearchLifeSkuMapping;
import com.logistics.appapi.controller.renewableaudit.request.*;
import com.logistics.appapi.controller.renewableaudit.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/3/14 14:31
 */
@Api(value = "乐橘新生订单")
@RestController
public class RenewableAuditController {

    @Resource
    private RenewableAuditClient renewableAuditClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 订单tab汇总
     *
     * @return
     */
    @ApiOperation("订单tab汇总 v1.0.3")
    @PostMapping(value = "/api/applet/yeloLife/renewableOrderListStatistic")
    public Result<RenewableOrderListStatisticResponseDto> renewableOrderListStatistic() {
        Result<RenewableOrderListStatisticResponseModel> modelResult = renewableAuditClient.renewableOrderListStatistic();
        return Result.success(MapperUtils.mapper(modelResult.getData(), RenewableOrderListStatisticResponseDto.class));
    }

    /**
     * 新生订单确认列表
     * @param requestDto
     * @return
     */
    @ApiOperation("新生订单确认列表 v1.0.3")
    @PostMapping(value = "/api/applet/yeloLife/renewableOrderList")
    public Result<PageInfo<RenewableOrderListResponseDto>> renewableOrderList(@RequestBody RenewableOrderListRequestDto requestDto) {
        Result<PageInfo<RenewableOrderListResponseModel>> modelResult = renewableAuditClient.renewableOrderList(MapperUtils.mapper(requestDto, RenewableOrderListRequestModel.class));
        modelResult.throwException();
        PageInfo pageInfo = modelResult.getData();
        List<RenewableOrderListResponseDto> list = MapperUtils.mapper(pageInfo.getList(), RenewableOrderListResponseDto.class, new RenewableOrderListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 新生订单详情
     *
     * @param requestDto
     * @return
     */
    @ApiOperation("新生订单详情 v1.0.3")
    @PostMapping(value = "/api/applet/yeloLife/renewableOrderDetail")
    public Result<RenewableOrderDetailResponseDto> renewableOrderDetail(@RequestBody @Valid RenewableOrderDetailRequestDto requestDto) {
        Result<RenewableOrderDetailResponseModel> modelResult = renewableAuditClient.renewableOrderDetail(MapperUtils.mapper(requestDto, RenewableOrderDetailRequestModel.class));
        modelResult.throwException();
        Map<String, String> imageMap = new HashMap<>();
        if (modelResult.getData() != null) {
            List<RenewableOrderTicketModel> renewableOrderTickets = modelResult.getData().getRenewableOrderTickets();
            if (ListUtils.isNotEmpty(renewableOrderTickets)) {
                List<String> ticketPathList = renewableOrderTickets.stream().map(RenewableOrderTicketModel::getRelativePath).collect(Collectors.toList());
                imageMap = commonBiz.batchGetOSSFileUrl(ticketPathList);
            }
        }
        return Result.success(MapperUtils.mapper(modelResult.getData(), RenewableOrderDetailResponseDto.class, new RenewableOrderDetailMapping(configKeyConstant.fileAccessAddress, imageMap)));
    }

    /**
     * 新生订单详情-确认提交货物
     *
     * @param requestDto
     * @return
     */
    @ApiOperation("新生订单详情-确认提交货物 v1.0.3")
    @PostMapping(value = "/api/applet/yeloLife/submitGoods")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> submitGoods(@RequestBody @Valid RenewableOrderSubmitGoodsRequestDto requestDto) {
        // skuCode唯一性
        List<RenewableOrderGoodRequestDto> goodsList = requestDto.getRenewableOrderGoods();
        List<String> skuCodeList = goodsList.stream().map(RenewableOrderGoodRequestDto::getSkuCode)
                .distinct().collect(Collectors.toList());
        if (skuCodeList.size() != goodsList.size()) {
            throw new BizException(AppApiExceptionEnum.SKU_CODE_NOT_UNIQUE);
        }
        return renewableAuditClient.submitGoods(MapperUtils.mapper(requestDto, RenewableOrderSubmitGoodsRequestModel.class));
    }

    /**
     * 新生订单详情-确认提交单据
     * @param requestDto
     * @return
     */
    @ApiOperation("新生订单详情-确认提交单据 v1.0.3")
    @PostMapping(value = "/api/applet/yeloLife/submitTicket")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> submitTicket(@RequestBody @Valid RenewableOrderSubmitTicketRequestDto requestDto) {
        return renewableAuditClient.submitTicket(MapperUtils.mapper(requestDto, RenewableOrderSubmitTicketRequestModel.class));
    }

    /**
     * 新生订单详情-确认提交单据
     *
     * @param requestDto
     * @return
     */
    @ApiOperation("新生订单详情-确认提交信息 v1.0.3")
    @PostMapping(value = "/api/applet/yeloLife/submitRenewableOrder")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> submitRenewableOrder(@RequestBody @Valid SubmitRenewableOrderRequestDto requestDto) {
        return renewableAuditClient.submitRenewableOrder(MapperUtils.mapper(requestDto, SubmitRenewableOrderRequestModel.class));
    }

    /**
     * 新生订单详情-查询收货仓库(先查询大数据再查询云仓仓库地址)
     * @param requestDto
     * @return
     */
    @ApiOperation("新生订单详情-查询收货仓库(先查询大数据再查询云仓仓库地址) v1.0.3")
    @PostMapping(value = "/api/applet/yeloLife/searchWarehouse")
    public Result<List<SearchWarehouseResponseDto>> searchWarehouse(@RequestBody @Valid SearchWarehouseRequestDto requestDto) {
        if (!CommonConstant.ONE.equals(requestDto.getSearchType()) && !CommonConstant.TWO.equals(requestDto.getSearchType())){
            throw new BizException(AppApiExceptionEnum.PARAMS_ERROR);
        }
        Result<List<SearchWarehouseResponseModel>> result = renewableAuditClient.searchWarehouse(MapperUtils.mapper(requestDto, SearchWarehouseRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SearchWarehouseResponseDto.class));
    }

    /**
     * 驾驶员下单-查询发货人(先查询大数据再查询新生客户地址)
     * @param requestDto
     * @return
     */
    @ApiOperation("驾驶员下单-查询发货人(先查询大数据再查询新生客户地址) v1.0.3")
    @PostMapping(value = "/api/applet/yeloLife/searchConsignor")
    public Result<List<SearchConsignorResponseDto>> searchConsignor(@RequestBody @Valid SearchConsignorRequestDto requestDto) {
        if (!CommonConstant.ONE.equals(requestDto.getSearchType()) && !CommonConstant.TWO.equals(requestDto.getSearchType())){
            throw new BizException(AppApiExceptionEnum.PARAMS_ERROR);
        }
        Result<List<SearchConsignorResponseModel>> result = renewableAuditClient.searchConsignor(MapperUtils.mapper(requestDto, SearchConsignorRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SearchConsignorResponseDto.class));
    }

    /**
     * 驾驶员下单
     *
     * @param requestDto
     * @return
     */
    @ApiOperation("驾驶员下单 v1.0.3")
    @PostMapping(value = "/api/applet/yeloLife/publishRenewableOrder")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<PublishRenewableOrderResponseDto> publishRenewableOrder(@RequestBody @Valid PublishRenewableOrderRequestDto requestDto) {
        // skuCode唯一性
        List<RenewableOrderGoodRequestDto> goodsList = requestDto.getRenewableOrderGoods();
        List<String> skuCodeList = goodsList.stream().map(RenewableOrderGoodRequestDto::getSkuCode)
                .distinct().collect(Collectors.toList());
        if (skuCodeList.size() != goodsList.size()) {
            throw new BizException(AppApiExceptionEnum.SKU_CODE_NOT_UNIQUE);
        }
        Result<PublishRenewableOrderResponseModel> result = renewableAuditClient.publishRenewableOrder(MapperUtils.mapper(requestDto, PublishRenewableOrderRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), PublishRenewableOrderResponseDto.class));
    }

    /**
     * 查询sku下拉列表
     *
     * @param requestDto
     * @return
     */
    @ApiOperation("查询sku下拉列表 v1.0.3")
    @PostMapping(value = "/api/applet/yeloLife/searchLifeSku")
    public Result<List<SearchLifeSkuResponseDto>> searchLifeSku(@RequestBody SearchLifeSkuRequestDto requestDto) {
        Result<List<SearchLifeSkuResponseModel>> result = renewableAuditClient.searchLifeSku(MapperUtils.mapper(requestDto, SearchLifeSkuRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SearchLifeSkuResponseDto.class,new SearchLifeSkuMapping()));
    }

    /**
     * 查看sku示例内容
     *
     * @param requestDto
     * @return
     */
    @ApiOperation("查看sku示例内容 v1.0.3")
    @PostMapping(value = "/api/applet/yeloLife/searchSkuDetail")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<SearchSkuDetailResponseDto> searchSkuDetail(@RequestBody @Valid SearchSkuDetailRequestDto requestDto) {
        Result<SearchSkuDetailResponseModel> modelResult = renewableAuditClient.searchSkuDetail(MapperUtils.mapper(requestDto, SearchSkuDetailRequestModel.class));
        modelResult.throwException();
        return Result.success(MapperUtils.mapper(modelResult.getData(), SearchSkuDetailResponseDto.class));
    }
}
