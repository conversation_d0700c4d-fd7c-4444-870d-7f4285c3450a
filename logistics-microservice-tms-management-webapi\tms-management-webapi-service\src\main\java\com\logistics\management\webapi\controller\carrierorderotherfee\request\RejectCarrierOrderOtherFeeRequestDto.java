package com.logistics.management.webapi.controller.carrierorderotherfee.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class RejectCarrierOrderOtherFeeRequestDto {

    @ApiModelProperty(value = "临时费用id",required = true)
    @NotBlank(message = "id不能为空")
    private String carrierOrderOtherFeeId;

    @ApiModelProperty(value = "备注",required = true)
    @Size(min = 1, max = 300, message = "备注不能为空")
    private String remark;
}
