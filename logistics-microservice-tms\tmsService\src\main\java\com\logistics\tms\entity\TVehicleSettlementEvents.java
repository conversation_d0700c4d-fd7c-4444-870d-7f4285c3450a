package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleSettlementEvents extends BaseEntity {
    /**
    * 车辆结算id
    */
    @ApiModelProperty("车辆结算id")
    private Long vehicleSettlementId;

    /**
    * 事件类型：1 对账，2 确认，3 结清
    */
    @ApiModelProperty("事件类型：1 对账，2 确认，3 结清")
    private Integer event;

    /**
    * 事件描述
    */
    @ApiModelProperty("事件描述")
    private String eventDesc;

    /**
    * 事件内容
    */
    @ApiModelProperty("事件内容")
    private String remark;

    /**
    * 事件时间
    */
    @ApiModelProperty("事件时间")
    private Date eventTime;

    /**
    * 操作人名称
    */
    @ApiModelProperty("操作人名称")
    private String operatorName;

    /**
    * 操作时间
    */
    @ApiModelProperty("操作时间")
    private Date operateTime;
}