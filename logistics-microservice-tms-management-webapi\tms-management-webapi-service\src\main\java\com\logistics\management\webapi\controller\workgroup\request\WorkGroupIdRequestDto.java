package com.logistics.management.webapi.controller.workgroup.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2023/12/21 14:58
 */
@Data
public class WorkGroupIdRequestDto {
    @ApiModelProperty(value = "智能推送配置表id", required = true)
    @NotBlank(message = "请选择配置")
    private String workGroupId;
}
