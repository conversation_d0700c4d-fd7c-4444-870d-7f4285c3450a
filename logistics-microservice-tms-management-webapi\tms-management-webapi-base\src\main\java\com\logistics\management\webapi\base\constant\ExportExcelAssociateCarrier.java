package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/5/31 13:55
 */
public class ExportExcelAssociateCarrier {
    private ExportExcelAssociateCarrier() {
    }

    private static final Map<String, String> ASSOCIATE_CARRIER_MAP;

    static {
        ASSOCIATE_CARRIER_MAP = new LinkedHashMap<>();
        ASSOCIATE_CARRIER_MAP.put("承运商名称", "companyCarrierName");
        ASSOCIATE_CARRIER_MAP.put("零担运价新增人", "shippingFreightAddUser");
        ASSOCIATE_CARRIER_MAP.put("零担运价新增时间", "shippingFreightAddTime");
    }

    public static Map<String, String> getDateAssociateCarrierMap() {
        return ASSOCIATE_CARRIER_MAP;
    }
}

