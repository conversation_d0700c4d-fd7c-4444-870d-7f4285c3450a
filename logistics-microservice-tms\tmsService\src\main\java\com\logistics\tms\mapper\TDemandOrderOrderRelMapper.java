package com.logistics.tms.mapper;

import com.logistics.tms.controller.demandorder.response.DemandOrderOrderRelResponseModel;
import com.logistics.tms.entity.TDemandOrderOrderRel;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TDemandOrderOrderRelMapper extends BaseMapper<TDemandOrderOrderRel> {

    List<TDemandOrderOrderRel> getByIds(@Param("ids")String ids);

    int batchUpdateByPrimaryKeySelective(@Param("list")List<TDemandOrderOrderRel> list);


    int batchInsertDemandOrderOrderRelSelective(@Param("list") List<TDemandOrderOrderRel> list);

    List<TDemandOrderOrderRel> getDemandOrderOrderRelByDemandIds(@Param("demandOrderIds")String demandOrderIds);

    List<TDemandOrderOrderRel> getInvalidByDemandOrderId(@Param("demandOrderId")Long demandOrderId);

    List<DemandOrderOrderRelResponseModel> getDemandOrderOrders(@Param("demandId") Long demandId);
}