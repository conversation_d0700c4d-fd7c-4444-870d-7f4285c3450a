package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/10/8 14:39
 */
public class ExportExcelDeductingHistory {
    private ExportExcelDeductingHistory() {
    }

    private static final Map<String, String> EXPORT_DEDUCTING_HISTORY;

    static {
        EXPORT_DEDUCTING_HISTORY = new LinkedHashMap<>();
        EXPORT_DEDUCTING_HISTORY.put("扣减月份", "deductingMonth");
        EXPORT_DEDUCTING_HISTORY.put("总金额", "totalFee");
        EXPORT_DEDUCTING_HISTORY.put("未扣减费用合计", "remainingDeductingFeeTotal");
        EXPORT_DEDUCTING_HISTORY.put("扣减费用", "deductingFee");
        EXPORT_DEDUCTING_HISTORY.put("剩余未扣减", "remainingDeductingFee");
        EXPORT_DEDUCTING_HISTORY.put("操作人", "lastModifiedBy");
        EXPORT_DEDUCTING_HISTORY.put("操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getExportDeductingHistory() {
        return EXPORT_DEDUCTING_HISTORY;
    }
}
