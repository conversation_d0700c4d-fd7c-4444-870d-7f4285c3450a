package com.logistics.management.webapi.client.carrierorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/17 14:08
 */
@Data
public class SearchCarrierOrderListForLeYiRequestModel extends AbstractPageForm<SearchCarrierOrderListForLeYiRequestModel> {

    @ApiModelProperty("运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消 1 待审核 2已放空")
    private List<Integer> statusList;

    @ApiModelProperty("运单号")
    private String  carrierOrderCode;

    @ApiModelProperty("客户单号")
    private String customerOrderCode;

    @ApiModelProperty("需求单号")
    private String  demandOrderCode;

    @ApiModelProperty("车牌号")
    private String  vehicleNo;

    @ApiModelProperty("司机")
    private String  driver;

    @ApiModelProperty("发货仓库")
    private String loadWarehouse;

    @ApiModelProperty("发货地址")
    private String  loadAddress;

    @ApiModelProperty("收货仓库")
    private String unloadWarehouse;

    @ApiModelProperty("收货地址")
    private String  unloadAddress;

    @ApiModelProperty("调度人")
    private String  dispatchUserName;

    @ApiModelProperty("预计到货时间")
    private String expectArrivalTimeFrom;

    @ApiModelProperty("预计到货时间")
    private String  expectArrivalTimeTo;

    @ApiModelProperty("预计提货时间")
    private String  expectLoadTimeFrom;

    @ApiModelProperty("预计提货时间")
    private String  expectLoadTimeTo;

    @ApiModelProperty("运单生成时间")
    private String dispatchTimeFrom;

    @ApiModelProperty("运单生成时间")
    private String dispatchTimeTo;

    @ApiModelProperty("下单时间from")
    private String demandCreatedTimeFrom;

    @ApiModelProperty("下单时间to")
    private String demandCreatedTimeTo;

    @ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;

    @ApiModelProperty("车辆需要审核 0 无需审核 1 需要审核")
    private Integer ifWaitAudit;

    @ApiModelProperty("承运商")
    private String carrierCompany;

    @ApiModelProperty("是否加急：0 否，1 是")
    private Integer ifUrgent;

    @ApiModelProperty("纠错状态：0 待纠错，1 已纠错，2 无需纠错")
    private Integer correctStatus;

    @ApiModelProperty("区域（提货大区）")
    private String loadRegionName;

    @ApiModelProperty("批量查询-需求单号")
    private List<String> demandOrderCodeList;

    @ApiModelProperty("批量查询-运单号")
    private List<String> carrierOrderCodeList;

    @ApiModelProperty("调度单号")
    private String dispatchOrderCode;

    @ApiModelProperty("运单ids-勾选导出用")
    private String carrierOrderIds;

    @ApiModelProperty("出库状态：0 待出库，1 部分出库，2 已出库")
    private Integer outStatus;

    @ApiModelProperty("货物名")
    private String goods;

    @ApiModelProperty("发货人")
    private String consignor;

    @ApiModelProperty("收货人")
    private String receiver;

    @ApiModelProperty("实际提货时间开始")
    private String loadTimeStart;
    @ApiModelProperty("实际提货时间结束")
    private String loadTimeEnd;

    @ApiModelProperty("签收时间开始 yyyy-MM-dd")
    private String signTimeStart;
    @ApiModelProperty("签收时间结束 yyyy-MM-dd")
    private String signTimeEnd;

    @ApiModelProperty(value = "部门Code")
    private String orgCode;

    @ApiModelProperty("回收任务类型：1 日常回收，2 加急或节假日回收")
    private Integer recycleTaskType;

    @ApiModelProperty("项目标签：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private Integer projectLabel;

    @ApiModelProperty("是否是额外补的运单  0：否 1：是  V2.6.8")
    private String ifExtCarrierOrder = "";

}
