<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRouteEnquiryAddressQuoteMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TRouteEnquiryAddressQuote" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="route_enquiry_address_id" property="routeEnquiryAddressId" jdbcType="BIGINT" />
    <result column="route_enquiry_company_id" property="routeEnquiryCompanyId" jdbcType="BIGINT" />
    <result column="distance" property="distance" jdbcType="DECIMAL" />
    <result column="quote_price_type" property="quotePriceType" jdbcType="INTEGER" />
    <result column="quote_price" property="quotePrice" jdbcType="DECIMAL" />
    <result column="quote_remark" property="quoteRemark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, route_enquiry_address_id, route_enquiry_company_id, distance,
    quote_price_type, quote_price, quote_remark, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_route_enquiry_address_quote
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_route_enquiry_address_quote
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TRouteEnquiryAddressQuote" >
    insert into t_route_enquiry_address_quote (id, route_enquiry_address_id,
      route_enquiry_company_id, distance, quote_price_type, 
      quote_price, quote_remark, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{routeEnquiryAddressId,jdbcType=BIGINT},
      #{routeEnquiryCompanyId,jdbcType=BIGINT}, #{distance,jdbcType=DECIMAL}, #{quotePriceType,jdbcType=INTEGER}, 
      #{quotePrice,jdbcType=DECIMAL}, #{quoteRemark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TRouteEnquiryAddressQuote" >
    insert into t_route_enquiry_address_quote
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="routeEnquiryAddressId != null" >
        route_enquiry_address_id,
      </if>
      <if test="routeEnquiryCompanyId != null" >
        route_enquiry_company_id,
      </if>
      <if test="distance != null" >
        distance,
      </if>
      <if test="quotePriceType != null" >
        quote_price_type,
      </if>
      <if test="quotePrice != null" >
        quote_price,
      </if>
      <if test="quoteRemark != null" >
        quote_remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="routeEnquiryAddressId != null" >
        #{routeEnquiryAddressId,jdbcType=BIGINT},
      </if>
      <if test="routeEnquiryCompanyId != null" >
        #{routeEnquiryCompanyId,jdbcType=BIGINT},
      </if>
      <if test="distance != null" >
        #{distance,jdbcType=DECIMAL},
      </if>
      <if test="quotePriceType != null" >
        #{quotePriceType,jdbcType=INTEGER},
      </if>
      <if test="quotePrice != null" >
        #{quotePrice,jdbcType=DECIMAL},
      </if>
      <if test="quoteRemark != null" >
        #{quoteRemark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TRouteEnquiryAddressQuote" >
    update t_route_enquiry_address_quote
    <set >
      <if test="routeEnquiryAddressId != null" >
        route_enquiry_address_id = #{routeEnquiryAddressId,jdbcType=BIGINT},
      </if>
      <if test="routeEnquiryCompanyId != null" >
        route_enquiry_company_id = #{routeEnquiryCompanyId,jdbcType=BIGINT},
      </if>
      <if test="distance != null" >
        distance = #{distance,jdbcType=DECIMAL},
      </if>
      <if test="quotePriceType != null" >
        quote_price_type = #{quotePriceType,jdbcType=INTEGER},
      </if>
      <if test="quotePrice != null" >
        quote_price = #{quotePrice,jdbcType=DECIMAL},
      </if>
      <if test="quoteRemark != null" >
        quote_remark = #{quoteRemark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TRouteEnquiryAddressQuote" >
    update t_route_enquiry_address_quote
    set route_enquiry_address_id = #{routeEnquiryAddressId,jdbcType=BIGINT},
      route_enquiry_company_id = #{routeEnquiryCompanyId,jdbcType=BIGINT},
      distance = #{distance,jdbcType=DECIMAL},
      quote_price_type = #{quotePriceType,jdbcType=INTEGER},
      quote_price = #{quotePrice,jdbcType=DECIMAL},
      quote_remark = #{quoteRemark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>