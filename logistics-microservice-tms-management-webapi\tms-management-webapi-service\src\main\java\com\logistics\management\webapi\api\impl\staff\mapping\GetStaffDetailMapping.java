package com.logistics.management.webapi.api.impl.staff.mapping;

import com.logistics.management.webapi.api.feign.staff.dto.GetStaffDetailResponseDto;
import com.logistics.management.webapi.api.feign.staff.dto.GetStaffTicketsResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.GenderEnum;
import com.logistics.management.webapi.base.enums.StaffDriverCertificateEnum;
import com.logistics.management.webapi.base.enums.StaffTypeEnum;
import com.logistics.tms.api.feign.staff.model.GetStaffDetailResponseModel;
import com.logistics.tms.api.feign.staff.model.GetStaffTicketsResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/6/10 9:50
 */
public class GetStaffDetailMapping extends MapperMapping<GetStaffDetailResponseModel,GetStaffDetailResponseDto> {

    private String imagePrefix;
    private Map<String,String> imageMap;
    public GetStaffDetailMapping(String imagePrefix , Map<String,String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        GetStaffDetailResponseModel source = getSource();
        GetStaffDetailResponseDto destination = getDestination();
        if (source != null){
            destination.setTypeDesc(StaffTypeEnum.getEnum(source.getType()).getValue());
            destination.setGenderDesc(GenderEnum.getEnum(source.getGender()).getValue());
            if(CommonConstant.ZERO.equals(destination.getType())){
                destination.setType(CommonConstant.BLANK_TEXT);
            }
            if(CommonConstant.ZERO.equals(destination.getGender())){
                destination.setGender(CommonConstant.BLANK_TEXT);
            }

            if (ListUtils.isNotEmpty(source.getTicketsList())){
                for (GetStaffTicketsResponseModel model:source.getTicketsList()) {
                    for (GetStaffTicketsResponseDto dto : destination.getTicketsList()) {
                        if (model.getTicketId().toString().equals(dto.getTicketId())){
                            dto.setFilePathSrc(imagePrefix+imageMap.get(model.getFilePath()));
                            if (model.getUploadTime() != null){
                                dto.setUploadTime(DateUtils.dateToString(model.getUploadTime(),CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
                            }
                            if (StaffDriverCertificateEnum.STAFF_IDENTITY_FRONT.getObjectType().equals(model.getObjectType())){
                                if (StaffDriverCertificateEnum.STAFF_IDENTITY_FRONT.getFileType().equals(model.getFileType())){
                                    dto.setFileType(StaffDriverCertificateEnum.STAFF_IDENTITY_FRONT.getKeyStr());
                                }else if (StaffDriverCertificateEnum.STAFF_IDENTITY_BACK.getFileType().equals(model.getFileType())){
                                    dto.setFileType(StaffDriverCertificateEnum.STAFF_IDENTITY_BACK.getKeyStr());
                                }
                            }else if (StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_FRONT.getObjectType().equals(model.getObjectType())){
                                if (StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_FRONT.getFileType().equals(model.getFileType())){
                                    dto.setFileType(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_FRONT.getKeyStr());
                                }else if (StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_BACK.getFileType().equals(model.getFileType())){
                                    dto.setFileType(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_BACK.getKeyStr());
                                }else if (StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_FRONT.getFileType().equals(model.getFileType())){
                                    dto.setFileType(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_FRONT.getKeyStr());
                                }else if (StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_BACK.getFileType().equals(model.getFileType())){
                                    dto.setFileType(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_BACK.getKeyStr());
                                }else if (StaffDriverCertificateEnum.DRIVER_LICENSE_FRONT.getFileType().equals(model.getFileType())){
                                    dto.setFileType(StaffDriverCertificateEnum.DRIVER_LICENSE_FRONT.getKeyStr());
                                }
                            }
                        }
                    }
                }
            }
            if(source.getDriversLicenseDateFrom()!=null){
                destination.setDriversLicenseDateFrom(DateUtils.dateToString(source.getDriversLicenseDateFrom(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getDriversLicenseDateTo()!=null){
                destination.setDriversLicenseDateTo(DateUtils.dateToString(source.getDriversLicenseDateTo(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getInitialIssuanceDate()!=null){
                destination.setInitialIssuanceDate(DateUtils.dateToString(source.getInitialIssuanceDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getLaborContractValidDate()!=null){
                destination.setLaborContractValidDate(DateUtils.dateToString(source.getLaborContractValidDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getIdentityValidity()!=null){
                destination.setIdentityValidity(DateUtils.dateToString(source.getIdentityValidity(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(CommonConstant.INTEGER_ONE.equals(source.getIdentityIsForever())){
                destination.setIdentityValidity("");
            }
            if(source.getAge()==null||source.getAge()==0){
                destination.setAge("");
            }
            destination.setCompanyCarrierName(CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyCarrierType()) ?
                    source.getCompanyCarrierName() :
                    source.getCarrierContactName() + " " + source.getCarrierContactPhone());

        }
    }
}
