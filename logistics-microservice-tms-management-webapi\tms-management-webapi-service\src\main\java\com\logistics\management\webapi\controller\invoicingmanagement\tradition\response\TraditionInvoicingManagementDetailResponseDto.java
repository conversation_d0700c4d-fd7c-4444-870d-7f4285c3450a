package com.logistics.management.webapi.controller.invoicingmanagement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/19 17:06
 */
@Data
public class TraditionInvoicingManagementDetailResponseDto {
    /**
     * 发票管理id
     */
    @ApiModelProperty("发票管理id")
    private String invoicingId="";

    /**
     * 业务名称
     */
    @ApiModelProperty("业务名称")
    private String businessName="";

    /**
     * 开票月份
     */
    @ApiModelProperty("开票月份")
    private String invoicingMonth="";

    /**
     * 承运商id
     */
    @ApiModelProperty("承运商id")
    private String companyCarrierId="";
    /**
     * 承运商名称
     */
    @ApiModelProperty("承运商名称")
    private String companyCarrierName="";

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark="";
}
