package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/26 10:05
 */
@Data
public class CarrierOrderCancelToYeloLifeModel {

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("客户单号")
    private String customerOrderCode;

    @ApiModelProperty(value = "操作人")
    private String userName;

    @ApiModelProperty(value = "需求单是否回到待调度状态：0 否，1 是")
    private Integer demandOrderStatusBack = 0;

}
