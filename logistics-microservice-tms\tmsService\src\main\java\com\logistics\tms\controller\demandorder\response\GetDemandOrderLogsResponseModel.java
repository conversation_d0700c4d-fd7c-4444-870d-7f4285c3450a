package com.logistics.tms.controller.demandorder.response;

import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2018/11/6 10:02
 */
@Data
public class GetDemandOrderLogsResponseModel {

    private Long id;

    private Long demandOrderId;

    private Integer operationType;

    private String operationContent;

    private String remark;

    private String operatorName;

    private Date operateTime;

    private String createdBy;

    private Date createdTime;

    private String lastModifiedBy;

    private Date lastModifiedTime;

    private Integer valid;
}
