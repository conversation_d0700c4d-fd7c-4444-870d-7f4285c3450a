package com.logistics.tms.controller.freightconfig.request;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class ConfigVehicleReqModel {


    /**
     * 主键
     */
    private Long shippingFreightRuleId;


    /**
     * 车长（米） -1就是零担
     */
    private BigDecimal vehicleLength;


    /**
     * 阶梯详细信息
     */
    private List<ConfigVehicleItemModel> itemReqDtos = new ArrayList<>();


}
