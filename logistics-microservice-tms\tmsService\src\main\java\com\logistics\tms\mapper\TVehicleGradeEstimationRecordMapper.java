package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.vehicleassetmanagement.response.VehicleGradeEstimationListResponseModel;
import com.logistics.tms.entity.TVehicleGradeEstimationRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface TVehicleGradeEstimationRecordMapper extends BaseMapper<TVehicleGradeEstimationRecord>{

    Map<String,String> getDueGradeEstimationCount(@Param("companyCarrierId") Long companyCarrierId,@Param("remindDays") Integer remindDays);

    List<VehicleGradeEstimationListResponseModel> getVehicleGradeEstimationRecordByVehicleIds(@Param("vehicleIds") String vehicleIds);

    int batchUpdate(@Param("list") List<TVehicleGradeEstimationRecord> tqVehicleGradeEstimationRecords);

    int countGradeEstimationByDate(@Param("vehicleId") Long vehicleId, @Param("estmationTime") Date estmationTime);
}