package com.logistics.tms.biz.sinopec.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/12/5 10:24
 */
@Data
public class SinopecRefuseConsignOrderRequestModel {

    @ApiModelProperty("租户ID")
    private Long customerId;
    @ApiModelProperty("租户名称")
    private String customerName;
    @ApiModelProperty("订单号")
    private String orderNo;
    @ApiModelProperty("委托单号")
    private String sn;
    @ApiModelProperty("撤销原因")
    private String reasons;
    @ApiModelProperty("联系人")
    private String contact;
    @ApiModelProperty("联系电话")
    private String phoneNo;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("原因类型, 1-承运商原因、2-托运人原因、3-装货人原因、4-政府政策原因、5-不可抗力原因")
    private Integer refuseReasonType;
}
