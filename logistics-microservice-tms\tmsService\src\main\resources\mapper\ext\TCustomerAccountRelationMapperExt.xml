<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TCustomerAccountRelationMapper">
  <select id="getRelationByUserRoleAndUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_customer_account_relation
    where valid = 1
    and user_role = #{userRole}
    and user_id = #{userId}
  </select>

  <select id="getTCustomerAccountRelationByIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    from t_customer_account_relation
    where valid = 1
    and user_id in (${userIds})
    and user_role = #{userRole,jdbcType=INTEGER}
  </select>

  <update id="batchUpdate">
    <foreach collection="list" item="item" index="index" separator=";">
      update t_customer_account_relation
      <set>
        <if test="item.userRole != null">
          user_role = #{item.userRole,jdbcType=INTEGER},
        </if>
        <if test="item.accountId != null">
          account_id = #{item.accountId,jdbcType=BIGINT},
        </if>
        <if test="item.userId != null">
          user_id = #{item.userId,jdbcType=BIGINT},
        </if>
        <if test="item.ifClose != null">
          if_close = #{item.ifClose,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          created_by= #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time= #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by= #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time= #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid= #{item.valid,jdbcType=INTEGER},
        </if>
        <if test="item.enabled != null">
          enabled= #{item.enabled,jdbcType=BIGINT},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>