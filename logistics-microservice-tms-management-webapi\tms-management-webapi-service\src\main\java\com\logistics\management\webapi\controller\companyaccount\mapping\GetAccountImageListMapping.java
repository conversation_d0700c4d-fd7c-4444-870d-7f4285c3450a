package com.logistics.management.webapi.controller.companyaccount.mapping;

import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.client.companyaccount.response.CompanyAccountImageResponseModel;
import com.logistics.management.webapi.controller.companyaccount.response.CompanyAccountImageResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/6
 */
public class GetAccountImageListMapping extends MapperMapping<CompanyAccountImageResponseModel, CompanyAccountImageResponseDto> {
	private final ConfigKeyConstant configKeyConstant;

	private final Map<String, String> imageMap;

	public GetAccountImageListMapping(ConfigKeyConstant configKeyConstant, Map<String, String> imageMap) {
		this.configKeyConstant = configKeyConstant;
		this.imageMap = imageMap;
	}

	@Override
	public void configure() {
		CompanyAccountImageResponseModel source = getSource();
		CompanyAccountImageResponseDto destination = getDestination();

		if (ListUtils.isNotEmpty(source.getImagePaths())) {
			List<String> imageList = new ArrayList<>();
			for (String imagePath : source.getImagePaths()) {
				imageList.add(configKeyConstant.fileAccessAddress + imageMap.get(imagePath));
			}
			destination.setImagePaths(imageList);
		}
	}
}
