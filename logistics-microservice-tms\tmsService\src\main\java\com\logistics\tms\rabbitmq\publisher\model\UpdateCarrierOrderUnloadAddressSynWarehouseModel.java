package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/4 15:49
 */
@Data
public class UpdateCarrierOrderUnloadAddressSynWarehouseModel {
    @ApiModelProperty("运单号s")
    private List<String> carrierOrderCodes;

    @ApiModelProperty("云盘仓库外部id")
    private Long warehouseId;

    @ApiModelProperty("操作人")
    private String userName;
}
