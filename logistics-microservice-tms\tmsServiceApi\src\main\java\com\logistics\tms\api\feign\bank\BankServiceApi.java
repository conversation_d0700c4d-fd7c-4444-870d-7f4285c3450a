package com.logistics.tms.api.feign.bank;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.bank.hystirx.BankServiceApiHystrix;
import com.logistics.tms.api.feign.bank.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/7/10 13:53
 */
@Api(value = "API-BankServiceApi-配置中心-保险公司管理")
@FeignClient(name = "logistics-tms-services", fallback = BankServiceApiHystrix.class)
public interface BankServiceApi {

    @ApiOperation(value = "银行列表")
    @PostMapping(value = "/service/bank/bankList")
    Result<PageInfo<SearchBankResponseModel>> searchBankList(@RequestBody SearchBankRequestModel requestModel);

    @ApiOperation(value = "银行新增修改")
    @PostMapping(value = "/service/bank/saveBank")
    Result<Boolean> saveOrModifyBank(@RequestBody SaveOrModifyBankRequestModel requestModel);

    @ApiOperation(value = "查看详情")
    @PostMapping(value = "/service/bank/getDetail")
    Result<BankDetailResponseModel> getDetail(@RequestBody BankDetailRequestModel requestModel);

    @ApiOperation(value = "启用/禁用银行信息")
    @PostMapping(value = "/service/bank/enable")
    Result<Boolean> enableOrDisable(@RequestBody EnableBankRequestModel requestModel);

    @ApiOperation(value = "导出")
    @PostMapping(value = "/service/bank/export")
    Result<List<SearchBankResponseModel>> export(@RequestBody SearchBankRequestModel requestModel);

    @ApiOperation(value = "导入")
    @PostMapping(value = "/service/bank/import")
    Result<ImportBankResponseModel> importBank(@RequestBody ImportBankRequestModel requestModel);

    @ApiOperation(value = "根据名称模糊匹配银行信息")
    @PostMapping(value = "/service/bank/fuzzyQuery")
    Result<List<FuzzyQueryBankListResponseModel>> fuzzyQueryBank(@RequestBody FuzzyQueryBankRequestModel requestModel);

}
