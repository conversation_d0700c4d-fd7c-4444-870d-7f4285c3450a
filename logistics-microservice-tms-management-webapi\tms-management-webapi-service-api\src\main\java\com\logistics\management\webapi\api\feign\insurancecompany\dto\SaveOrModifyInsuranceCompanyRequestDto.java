package com.logistics.management.webapi.api.feign.insurancecompany.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @Author: sj
 * @Date: 2019/5/29 14:22
 */
@Data
public class SaveOrModifyInsuranceCompanyRequestDto implements Serializable{
    @ApiModelProperty("保险公司ID")
    private String insuranceCompanyId;
    @ApiModelProperty("保险公司名称")
    @NotBlank(message = "请填写保险公司名称")
    @Pattern(regexp = "[\\u4e00-\\u9fa5]{2,50}",message = "2-50字符，必须中文")
    private String companyName;
    @ApiModelProperty("备注")
    @Size(max = 300,message = "不超过300字")
    private String remark;
}
