package com.logistics.management.webapi.controller.companycarrier.mapping;

import com.logistics.management.webapi.client.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.management.webapi.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseDto;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * 模糊查询公司
 *
 * <AUTHOR>
 * @date ：Created in 2022/7/12
 */
public class FuzzyQueryMapping extends MapperMapping<FuzzySearchCompanyCarrierResponseModel, FuzzySearchCompanyCarrierResponseDto> {
	@Override
	public void configure() {
		FuzzySearchCompanyCarrierResponseModel source = getSource();
		FuzzySearchCompanyCarrierResponseDto destination = getDestination();

		//个人类型 把联系人+手机号当作公司名
		if (source != null && CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyType())) {
			destination.setCompanyName(source.getContactName() + " " + source.getContactPhone());
		}
	}
}
