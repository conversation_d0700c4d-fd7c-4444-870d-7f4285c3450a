package com.logistics.tms.controller.workordercenter.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Objects;
@Data
@Accessors(chain = true)
public class ReportWorkExceptionRequestModel {

    @ApiModelProperty(value = "请求来源: 1 后台 2 前台 3 小程序")
    private Integer reportSource;

    @ApiModelProperty(value = "工单类型：10 需求单，20 运单")
    private Integer workOrderType;

    @ApiModelProperty(value = "订单Id: (根据工单类型区分为 需求单Id、运单Id)")
    private Long orderId;

    @ApiModelProperty(value = "异常类型（一级）：10 联系不上客户，20 不想还盘，30 不可抗力，40 重复下单")
    private Integer anomalyTypeOne;

    @ApiModelProperty(value = "异常类型（二级）：" +
            "101 电话空号 102 无人接听 103联系方式错误;" +
            "201 客户占用 202 客户不协助装车 203 贸易商流向错误 204 装车费用确认;" +
            "301 暴风暴雨 302 当地修路;" +
            "401 重复下单;")
    private Integer anomalyTypeTwo;

    @ApiModelProperty(value = "核验联系方式: 1 无误，2 有误")
    private Integer checkContact;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "联系人联系方式")
    private String contactTelephone;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "司机是否到达现场：0 否，1 是")
    private Integer isArriveScene;

    @ApiModelProperty(value = "到达现场图片")
    private String arriveScenePicture;

    @ApiModelProperty(value = "工单优先级：1,2,3,4,5（数字越小，优先级越高）")
    private Integer workOrderPriority;

    @ApiModelProperty(value = "定位抬头")
    private String addressHead;

    @ApiModelProperty(value = "定位详细地址")
    private String addressDetail;

    public Integer getWorkOrderPriority() {
        return Objects.equals(isArriveScene, 1) ? 1 : 2;
    }

    public Integer getIsArriveScene() {
        return Objects.isNull(isArriveScene) ? 0 : isArriveScene;
    }
}
