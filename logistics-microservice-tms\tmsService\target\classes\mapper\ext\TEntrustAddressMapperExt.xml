<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TEntrustAddressMapper" >
  <select id="getByCompanyAndAddress" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_entrust_address
    where valid = 1
    and company_entrust_id = #{params.companyEntrustId,jdbcType=BIGINT}
    <if test="params.addressType == 2 and params.companyName != null and params.companyName != ''">
      and company_name = #{params.companyName,jdbcType=VARCHAR}
    </if>
    and address_type = #{params.addressType,jdbcType=INTEGER}
    and province_id = #{params.provinceId,jdbcType=BIGINT}
    and city_id = #{params.cityId,jdbcType=BIGINT}
    and area_id = #{params.areaId,jdbcType=BIGINT}
    and detail_address = #{params.detailAddress,jdbcType=VARCHAR}
    and warehouse = #{params.warehouse,jdbcType=VARCHAR}
    limit 1
  </select>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TEntrustAddress">
    <foreach collection="list" item="item" separator=";">
      insert into t_entrust_address
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          id,
        </if>
        <if test="item.companyEntrustId != null">
          company_entrust_id,
        </if>
        <if test="item.companyName != null">
          company_name,
        </if>
        <if test="item.addressType != null">
          address_type,
        </if>
        <if test="item.provinceId != null">
          province_id,
        </if>
        <if test="item.provinceName != null">
          province_name,
        </if>
        <if test="item.cityId != null">
          city_id,
        </if>
        <if test="item.cityName != null">
          city_name,
        </if>
        <if test="item.areaId != null">
          area_id,
        </if>
        <if test="item.areaName != null">
          area_name,
        </if>
        <if test="item.detailAddress != null">
          detail_address,
        </if>
        <if test="item.warehouse != null">
          warehouse,
        </if>
        <if test="item.contactName != null">
          contact_name,
        </if>
        <if test="item.contactMobile != null">
          contact_mobile,
        </if>
        <if test="item.entrustContactId != null">
          entrust_contact_id,
        </if>
        <if test="item.createdBy != null">
          created_by,
        </if>
        <if test="item.createdTime != null">
          created_time,
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time,
        </if>
        <if test="item.valid != null">
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.companyEntrustId != null">
          #{item.companyEntrustId,jdbcType=BIGINT},
        </if>
        <if test="item.companyName != null">
          #{item.companyName,jdbcType=VARCHAR},
        </if>
        <if test="item.addressType != null">
          #{item.addressType,jdbcType=INTEGER},
        </if>
        <if test="item.provinceId != null">
          #{item.provinceId,jdbcType=BIGINT},
        </if>
        <if test="item.provinceName != null">
          #{item.provinceName,jdbcType=VARCHAR},
        </if>
        <if test="item.cityId != null">
          #{item.cityId,jdbcType=BIGINT},
        </if>
        <if test="item.cityName != null">
          #{item.cityName,jdbcType=VARCHAR},
        </if>
        <if test="item.areaId != null">
          #{item.areaId,jdbcType=BIGINT},
        </if>
        <if test="item.areaName != null">
          #{item.areaName,jdbcType=VARCHAR},
        </if>
        <if test="item.detailAddress != null">
          #{item.detailAddress,jdbcType=VARCHAR},
        </if>
        <if test="item.warehouse != null">
          #{item.warehouse,jdbcType=VARCHAR},
        </if>
        <if test="item.contactName != null">
          #{item.contactName,jdbcType=VARCHAR},
        </if>
        <if test="item.contactMobile != null">
          #{item.contactMobile,jdbcType=VARCHAR},
        </if>
        <if test="item.entrustContactId != null">
          #{item.entrustContactId,jdbcType=BIGINT},
        </if>
        <if test="item.createdBy != null">
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="com.logistics.tms.entity.TEntrustAddress">
    <foreach collection="list" item="item" separator=";">
      update t_entrust_address
      <set>
        <if test="item.companyEntrustId != null">
          company_entrust_id = #{item.companyEntrustId,jdbcType=BIGINT},
        </if>
        <if test="item.companyName != null">
          company_name = #{item.companyName,jdbcType=VARCHAR},
        </if>
        <if test="item.addressType != null">
          address_type = #{item.addressType,jdbcType=INTEGER},
        </if>
        <if test="item.provinceId != null">
          province_id = #{item.provinceId,jdbcType=BIGINT},
        </if>
        <if test="item.provinceName != null">
          province_name = #{item.provinceName,jdbcType=VARCHAR},
        </if>
        <if test="item.cityId != null">
          city_id = #{item.cityId,jdbcType=BIGINT},
        </if>
        <if test="item.cityName != null">
          city_name = #{item.cityName,jdbcType=VARCHAR},
        </if>
        <if test="item.areaId != null">
          area_id = #{item.areaId,jdbcType=BIGINT},
        </if>
        <if test="item.areaName != null">
          area_name = #{item.areaName,jdbcType=VARCHAR},
        </if>
        <if test="item.detailAddress != null">
          detail_address = #{item.detailAddress,jdbcType=VARCHAR},
        </if>
        <if test="item.warehouse != null">
          warehouse = #{item.warehouse,jdbcType=VARCHAR},
        </if>
        <if test="item.contactName != null">
          contact_name = #{item.contactName,jdbcType=VARCHAR},
        </if>
        <if test="item.contactMobile != null">
          contact_mobile = #{item.contactMobile,jdbcType=VARCHAR},
        </if>
        <if test="item.entrustContactId != null">
          entrust_contact_id = #{item.entrustContactId,jdbcType=BIGINT},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="getAddressByCompanyNameOrWarehouse" resultType="com.logistics.tms.api.feign.entrustaddress.model.GetAddressByCompanyNameResponseModel">
    SELECT
    tea.id             as entrustAddressId,
    tea.company_name   as companyName,
    tea.address_type   as addressType,
    tea.province_id    as provinceId,
    tea.province_name  as provinceName,
    tea.city_id        as cityId,
    tea.city_name      as cityName,
    tea.area_id        as areaId,
    tea.area_name      as areaName,
    tea.detail_address as detailAddress,
    tea.warehouse      as warehouse,
    tea.contact_name   as contactName,
    tea.contact_mobile as contactMobile
    FROM t_entrust_address tea
    WHERE tea.valid = 1
    and tea.address_type = #{params.addressType,jdbcType=INTEGER}
    and tea.company_entrust_id = #{params.companyEntrustId,jdbcType=BIGINT}
    <if test="params.warehouse != null and params.warehouse != ''">
      and (instr(tea.warehouse, #{params.warehouse,jdbcType=VARCHAR}) or
           instr(tea.province_name, #{params.warehouse,jdbcType=VARCHAR}) or
           instr(tea.city_name, #{params.warehouse,jdbcType=VARCHAR}) or
           instr(tea.area_name, #{params.warehouse,jdbcType=VARCHAR}) or
           instr(tea.detail_address, #{params.warehouse,jdbcType=VARCHAR}) or
           instr(tea.contact_name, #{params.warehouse,jdbcType=VARCHAR}) or
           instr(tea.contact_mobile, #{params.warehouse,jdbcType=VARCHAR}) or
           instr(concat(tea.province_name, tea.city_name, tea.area_name, tea.warehouse, tea.detail_address), #{params.warehouse,jdbcType=VARCHAR}))
    </if>
    order by tea.created_time desc, id desc
  </select>
  <select id="searchList" resultType="com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressResponseModel">
    select
    tea.id as entrustAddressId,
    tea.company_name as companyName,
    tea.warehouse as warehouse,
    tea.province_name as provinceName,
    tea.city_name as cityName,
    tea.area_name as areaName,
    tea.detail_address as detailAddress,
    tea.contact_name as contactName,
    tea.contact_mobile as contactMobile,
    tea.last_modified_by as lastModifiedBy,
    tea.last_modified_time as lastModifiedTime,
    tce.company_id as companyId,
    tce.type as type,
    tc.company_name as companyEntrustName,
    tea.created_by as createdBy
    from t_entrust_address tea
    left join t_company_entrust tce on tce.id = tea.company_entrust_id and tce.valid = 1
    left join t_company tc on tc.id = tce.company_id and tc.valid=1
    where tea.valid = 1
    and tea.address_type = #{addressType,jdbcType=INTEGER}
    order by tea.id desc
  </select>
</mapper>