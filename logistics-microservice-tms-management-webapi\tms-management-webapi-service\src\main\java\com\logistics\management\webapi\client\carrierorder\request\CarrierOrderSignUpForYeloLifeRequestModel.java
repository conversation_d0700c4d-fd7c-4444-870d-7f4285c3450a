package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/24
 */
@Data
public class CarrierOrderSignUpForYeloLifeRequestModel {

	@ApiModelProperty(value = "运单ID")
	private Long carrierOrderId;

	@ApiModelProperty(value = "货物列表")
	private List<SignGoodsForYeloLifeRequestModel> goodsList;

	@ApiModelProperty(value = "货主实际运费 0<一口价<=50000")
	private BigDecimal entrustFreightFee;

	@ApiModelProperty(value = "司机实际运费 0<一口价<=50000")
	private BigDecimal dispatchFreightFee;

	/**
	 * (3.23.0)车主运费
	 */
	@ApiModelProperty(value = "车主实际运费 0<一口价<=50000", required = true)
	private BigDecimal carrierFreight;

}
