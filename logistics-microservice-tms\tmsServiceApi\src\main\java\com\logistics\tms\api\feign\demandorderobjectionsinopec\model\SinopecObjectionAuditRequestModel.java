package com.logistics.tms.api.feign.demandorderobjectionsinopec.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/5/30 13:55
 */
@Data
public class SinopecObjectionAuditRequestModel {
    @ApiModelProperty("需求单异常id")
    private Long demandOrderObjectionId;

    @ApiModelProperty("审核结果：1 通过，２ 驳回")
    private Integer auditStatus;
    @ApiModelProperty("异常类型：1 已报价，2 已取消")
    private Integer auditObjectionType;
    @ApiModelProperty("审核依据")
    private List<String> auditTicketList;
    @ApiModelProperty("备注")
    private String auditRemark;
}
