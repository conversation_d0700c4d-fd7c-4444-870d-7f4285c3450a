package com.logistics.appapi.controller.carrierorderotherfee.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/9/2 10:27
 */
@Data
public class AddCarrierOrderOtherFeeRequestDto {
    @ApiModelProperty(value = "运单id",required = true)
    @NotBlank(message = "运单ID不能为空")
    private String carrierOrderId;

    @ApiModelProperty(value = "临时费用",required = true)
    @NotEmpty(message = "请维护临时费用")
    @Valid
    private List<AddCarrierOrderOtherFeeItemRequestDto> otherFeeList;

}
