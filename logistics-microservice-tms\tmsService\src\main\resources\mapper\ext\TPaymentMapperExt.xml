<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TPaymentMapper" >
    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TPayment" >
        <foreach collection="list" item="item" separator=";">
            insert into t_payment
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.carrierOrderId != null" >
                    carrier_order_id,
                </if>
                <if test="item.priceType != null">
                    price_type,
                </if>
                <if test="item.settlementAmount != null" >
                    settlement_amount,
                </if>
                <if test="item.settlementCostTotal != null">
                    settlement_cost_total,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderId != null" >
                    #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.priceType != null">
                    #{item.priceType,jdbcType=INTEGER},
                </if>
                <if test="item.settlementAmount != null" >
                    #{item.settlementAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.settlementCostTotal != null">
                    #{item.settlementCostTotal,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TPayment">
        <foreach collection="list" item="item" separator=";">
            update t_payment
            <set>
                <if test="item.carrierOrderId != null">
                    carrier_order_id = #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.priceType != null">
                    price_type = #{item.priceType,jdbcType=INTEGER},
                </if>
                <if test="item.settlementAmount != null">
                    settlement_amount = #{item.settlementAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.settlementCostTotal != null">
                    settlement_cost_total = #{item.settlementCostTotal,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getSettlementByDemandOrderId" resultType="com.logistics.tms.biz.carrierorder.model.GetSettlementByDemandOrderIdModel">
        select
        tco.id as carrierOrderId,
        tp.id as settlementId,
        tp.price_type as priceType,
        tp.settlement_amount as settlementAmount,
        tp.settlement_cost_total as settlementCostTotal
        from t_payment tp
        left join t_carrier_order tco on tco.id = tp.carrier_order_id and tco.valid = 1
        where tp.valid = 1
        and tco.demand_order_id = #{demandOrderId,jdbcType=BIGINT}
    </select>

    <select id="getByCarrierOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_payment
        where carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
        and valid = 1
    </select>

    <select id="getByCarrierOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_payment
        where carrier_order_id in (${carrierOrderIds})
        and valid = 1
    </select>

    <update id="batchUpdateByCarrierOrderIds">
        <foreach collection="list" item="item" separator=";">
            update t_payment
            <set>
                <if test="item.priceType != null">
                    price_type = #{item.priceType,jdbcType=INTEGER},
                </if>
                <if test="item.settlementAmount != null">
                    settlement_amount = #{item.settlementAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.settlementCostTotal != null">
                    settlement_cost_total = #{item.settlementCostTotal,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where carrier_order_id = #{item.carrierOrderId,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>