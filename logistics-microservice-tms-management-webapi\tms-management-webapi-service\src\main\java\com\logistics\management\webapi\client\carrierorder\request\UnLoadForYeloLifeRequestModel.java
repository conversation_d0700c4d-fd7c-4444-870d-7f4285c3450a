package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/22
 */
@Data
public class UnLoadForYeloLifeRequestModel {

	@ApiModelProperty(value = "运单ID")
	private Long carrierOrderId;

	@ApiModelProperty(value = "货物列表")
	private List<UnloadGoodsForYeloLifeRequestModel> goodsList;
	
	@ApiModelProperty(value = "现场图片列表")
	private List<String> siteImgList;
	
	@ApiModelProperty(value = "回单列表")
	private List<String> outImgList;
}
