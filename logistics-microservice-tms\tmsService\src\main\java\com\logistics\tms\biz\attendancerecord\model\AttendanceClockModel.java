package com.logistics.tms.biz.attendancerecord.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class AttendanceClockModel {

    @ApiModelProperty("考勤记录id")
    private Long recodeId;

    @ApiModelProperty("员工id")
    private Long staffId;

    @ApiModelProperty("请求打卡状态")
    private Integer punchType;

    @ApiModelProperty("当日打卡状态")
    private Integer toDayPunchType;

    @ApiModelProperty("打卡图片地址")
    private String dutyPunchPic;

    @ApiModelProperty("打卡地点")
    private String dutyPunchLocation;

    @ApiModelProperty("上班打卡时间")
    private Date onDutyPunchTime;

    @ApiModelProperty("下班打卡时间")
    private Date offDutyPunchTime;
}
