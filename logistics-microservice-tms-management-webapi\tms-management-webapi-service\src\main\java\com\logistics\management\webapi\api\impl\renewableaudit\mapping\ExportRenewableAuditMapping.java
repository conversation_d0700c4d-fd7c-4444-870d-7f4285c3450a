package com.logistics.management.webapi.api.impl.renewableaudit.mapping;

import com.logistics.management.webapi.api.feign.renewableaudit.dto.RenewableAuditResponseDto;
import com.logistics.management.webapi.base.enums.RenewableAuditStatusEnum;
import com.logistics.management.webapi.base.enums.RenewableBusinessType;
import com.logistics.management.webapi.base.enums.RenewableGoodsUnitEnum;
import com.logistics.tms.api.feign.renewableaudit.model.RenewableAuditResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Optional;

public class ExportRenewableAuditMapping extends MapperMapping<RenewableAuditResponseModel, RenewableAuditResponseDto> {
    @Override
    public void configure() {
        RenewableAuditResponseModel source = getSource();
        RenewableAuditResponseDto destination = getDestination();

        //审核状态转换
        destination.setStatusDesc(RenewableAuditStatusEnum.getEnum(source.getStatus()).getValue());
        //数量去零
        destination.setGoodsAmountTotal(source.getGoodsAmountTotal().stripTrailingZeros().toPlainString());
        destination.setVerifiedGoodsAmountTotal(source.getVerifiedGoodsAmountTotal().stripTrailingZeros().toPlainString()+ RenewableGoodsUnitEnum.KILOGRAM.getValue());

        //业务类型转换
        if (source.getBusinessType().equals(RenewableBusinessType.COMPANY.getCode())) {
            destination.setBusinessTypeDesc(RenewableBusinessType.COMPANY.getName());
        } else if (source.getBusinessType().equals(RenewableBusinessType.PERSON.getCode())) {
            destination.setBusinessTypeDesc(RenewableBusinessType.PERSON.getName());
            destination.setCustomerName(source.getCustomerUserName() + " " + source.getCustomerUserMobile());
        }
        //发货地址转换
        destination.setLoadDetailAddress(Optional.ofNullable(source.getLoadProvinceName()).orElse("") + Optional.ofNullable(source.getLoadCityName()).orElse("") + Optional.ofNullable(source.getLoadAreaName()).orElse(""));
        //收货地址转换
        destination.setUnloadDetailAddress(Optional.ofNullable(source.getUnloadProvinceName()).orElse("") + Optional.ofNullable(source.getUnloadCityName()).orElse("") + Optional.ofNullable(source.getUnloadAreaName()).orElse(""));
        //发货人转换
        destination.setConsignor(Optional.ofNullable(source.getConsignorName()).orElse("") + " " + Optional.ofNullable(source.getConsignorMobile()).orElse(""));
        //司机转换
        destination.setDriver(Optional.ofNullable(source.getStaffName()).orElse("") + " " + Optional.ofNullable(source.getStaffMobile()).orElse(""));
        //收货人转换
        destination.setReceiver(Optional.ofNullable(source.getReceiverName()).orElse("") + " " + Optional.ofNullable(source.getReceiverMobile()).orElse(""));
    }
}
