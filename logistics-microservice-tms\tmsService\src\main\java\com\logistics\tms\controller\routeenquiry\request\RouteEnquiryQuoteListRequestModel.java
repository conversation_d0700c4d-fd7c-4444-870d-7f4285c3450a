package com.logistics.tms.controller.routeenquiry.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/7/9 13:44
 */
@Data
public class RouteEnquiryQuoteListRequestModel {

    /**
     * 路线询价单地址表id
     */
    private Long routeEnquiryAddressId;

    /**
     * 运距
     */
    private BigDecimal distance;

    /**
     * 报价类型：1 单价，2 一口价
     */
    private Integer quotePriceType;

    /**
     * 报价金额
     */
    private BigDecimal quotePrice;

    /**
     * 备注
     */
    private String quoteRemark;

}
