package com.logistics.tms.biz.reservationorder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.carrierorder.CarrierOrderBiz;
import com.logistics.tms.biz.carrierorder.model.CarrierOrderSynchronizeModel;
import com.logistics.tms.biz.carrierorder.model.OrderAmountModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.staff.model.CarrierDriverRelationModel;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupPushBoModel;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.client.feign.warehouse.stock.WarehouseStockServiceApi;
import com.logistics.tms.client.feign.warehouse.stock.reponse.GetWareHouseByIdsAndKeyWordAndTypeResponseModel;
import com.logistics.tms.client.feign.warehouse.stock.request.GetWareHouseByIdsAndKeyWordAndTypeRequestModel;
import com.logistics.tms.controller.carrierorder.request.ReachLoadAddressRequestModel;
import com.logistics.tms.controller.carrierorder.request.ReachUnloadAddressRequestModel;
import com.logistics.tms.controller.carrierorderapplet.request.CarrierDriverRelationForAppletModel;
import com.logistics.tms.controller.reservationorder.request.*;
import com.logistics.tms.controller.reservationorder.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.SyncReservationCarrierOrderCreateToWarehouseModel;
import com.logistics.tms.rabbitmq.publisher.model.SyncReservationOrderCreateToWarehouseModel;
import com.logistics.tms.rabbitmq.publisher.model.SyncReservationOrderSignInToWarehouseModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/8/19 9:38
 */
@Service
public class ReservationOrderBiz {

    @Resource
    private TReservationOrderMapper tReservationOrderMapper;
    @Resource
    private TReservationOrderItemMapper tReservationOrderItemMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Resource
    private RabbitMqPublishBiz rabbitMqPublishBiz;
    @Resource
    private CarrierOrderBiz carrierOrderBiz;
    @Autowired
    private BasicDataClient basicDataClient;
    @Resource
    private TCarrierOrderAddressMapper tCarrierOrderAddressMapper;
    @Resource
    private WarehouseStockServiceApi warehouseStockServiceApi;
    @Resource
    private SysConfigBiz sysConfigBiz;
    @Resource
    private TReservationVisitorRecordMapper tReservationVisitorRecordMapper;
    @Resource
    private TCarrierOrderVehicleHistoryMapper tCarrierOrderVehicleHistoryMapper;

    /**
     * 预约汇总
     * @return
     */
    public ReservationOrderSummaryResponseModel summary(){
        ReservationOrderSummaryResponseModel responseModel = new ReservationOrderSummaryResponseModel();
        //查询登录司机与车主关系信息
        List<CarrierDriverRelationModel> driverModel = commonBiz.getLoginUserDriver();
        if (ListUtils.isEmpty(driverModel)) {
            return responseModel;
        }
        List<CarrierDriverRelationForAppletModel> carrierDriverModel = MapperUtils.mapper(driverModel, CarrierDriverRelationForAppletModel.class);

        //待预约-提货运单id集合
        List<Long> loadWaitReservationOrderId = tCarrierOrderMapper.waitReservationOrderId(carrierDriverModel, ReservationTypeEnum.LOAD.getKey());
        //待预约-卸货运单id集合
        List<Long> unloadWaitReservationOrderId = tCarrierOrderMapper.waitReservationOrderId(carrierDriverModel, ReservationTypeEnum.UNLOAD.getKey());

        //查询已预约列表
        List<ReservationOrderSummaryListResponseModel> waitSignInSummaryList = tReservationOrderMapper.waitSignInSummaryList(carrierDriverModel);

        responseModel.setLoadOrderCount(loadWaitReservationOrderId.size());
        responseModel.setUnloadOrderCount(unloadWaitReservationOrderId.size());
        responseModel.setSummaryList(waitSignInSummaryList);
        return responseModel;
    }

    /**
     * 待预约列表
     * @return
     */
    public WaitReservationResponseModel waitReservationList(WaitReservationRequestModel requestModel){
        WaitReservationResponseModel responseModel = new WaitReservationResponseModel();
        responseModel.setReservationType(requestModel.getReservationType());

        //查询登录司机与车主关系信息
        List<CarrierDriverRelationModel> driverModel = commonBiz.getLoginUserDriver();
        if (ListUtils.isEmpty(driverModel)) {
            return responseModel;
        }
        List<CarrierDriverRelationForAppletModel> carrierDriverModel = MapperUtils.mapper(driverModel, CarrierDriverRelationForAppletModel.class);

        //查询待预约运单列表
        List<WaitReservationCarrierOrderListResponseModel> orderList;
        if (ReservationTypeEnum.LOAD.getKey().equals(requestModel.getReservationType())){
            orderList = tCarrierOrderMapper.loadWaitReservationCarrierOrder(carrierDriverModel, null, List.of(1,4));
        }else if (ReservationTypeEnum.UNLOAD.getKey().equals(requestModel.getReservationType())){
            orderList = tCarrierOrderMapper.unloadWaitReservationCarrierOrder(carrierDriverModel, null, List.of(2, 4, 12));
        }else{
            return responseModel;
        }
        responseModel.setOrderCount(orderList.size());
        responseModel.setOrderList(orderList);
        return responseModel;
    }

    /**
     * 待预约详情
     * @return
     */
    public WaitReservationDetailResponseModel waitReservationDetail(WaitReservationDetailRequestModel requestModel){
        WaitReservationDetailResponseModel responseModel = new WaitReservationDetailResponseModel();
        responseModel.setReservationType(requestModel.getReservationType());

        List<Integer> loadDemandOrderEntrustTypeList = null;
        List<Integer> unLoadDemandOrderEntrustTypeList = null;

        List<CarrierDriverRelationForAppletModel> carrierDriverModel = new ArrayList<>();
        //查询登录司机与车主关系信息
        if (requestModel.getSource() != null) {
            if (ReservationSourceEnum.DRIVER_APP.getKey().equals(requestModel.getSource())) {
                List<CarrierDriverRelationModel> driverModel = commonBiz.getLoginUserDriver();
                if (ListUtils.isEmpty(driverModel)) {
                    return responseModel;
                }
                carrierDriverModel = MapperUtils.mapper(driverModel, CarrierDriverRelationForAppletModel.class);
                loadDemandOrderEntrustTypeList = List.of(1, 4);
                unLoadDemandOrderEntrustTypeList = List.of(2, 4, 12);
            } else if (ReservationSourceEnum.H5.getKey().equals(requestModel.getSource())) {
            }
        }


        //查询待预约运单列表
        List<WaitReservationCarrierOrderListResponseModel> orderList;
        if (ReservationTypeEnum.LOAD.getKey().equals(requestModel.getReservationType())){
            orderList = tCarrierOrderMapper.loadWaitReservationCarrierOrder(carrierDriverModel, requestModel.getCarrierOrderIdList(), loadDemandOrderEntrustTypeList);
        }else if (ReservationTypeEnum.UNLOAD.getKey().equals(requestModel.getReservationType())){
            orderList = tCarrierOrderMapper.unloadWaitReservationCarrierOrder(carrierDriverModel, requestModel.getCarrierOrderIdList(), unLoadDemandOrderEntrustTypeList);
        }else{
            return responseModel;
        }


        if (ListUtils.isEmpty(orderList)){
            return responseModel;
        }

        //判断单子地址是否一致
        List<String> addressList = new ArrayList<>();
        for (WaitReservationCarrierOrderListResponseModel model : orderList) {
            StringBuilder addressBuilder = new StringBuilder();
            if (StringUtils.isNotBlank(model.getWarehouse())){
                addressBuilder.append("【");
                addressBuilder.append(model.getWarehouse());
                addressBuilder.append("】");
            }
            addressBuilder.append(model.getProvinceName());
            addressBuilder.append(model.getCityName());
            addressBuilder.append(model.getAreaName());
            addressBuilder.append(model.getDetailAddress());

            if (!addressList.contains(addressBuilder.toString())){
                addressList.add(addressBuilder.toString());
            }
        }
        if (addressList.size() > CommonConstant.INTEGER_ONE){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_ADDRESS_DIFFERENT);
        }

        responseModel.setAddress(addressList.get(CommonConstant.INTEGER_ZERO));
        responseModel.setWarehouse(orderList.get(CommonConstant.INTEGER_ZERO).getWarehouse());
        responseModel.setOrderList(orderList);

        //获取经纬度存在的单子
        WaitReservationCarrierOrderListResponseModel carrierOrderModel = orderList
                .stream()
                .filter(f -> StringUtils.isNotBlank(f.getLongitude()) && StringUtils.isNotBlank(f.getLatitude()))
                .findFirst()
                .orElse(null);
        //查询司机到提卸货地的距离
        if (carrierOrderModel != null) {
            // 判断直线距离
            double distance = commonBiz.calculateDistance(
                    Double.parseDouble(carrierOrderModel.getLatitude()),
                    Double.parseDouble(carrierOrderModel.getLongitude()),
                    Double.parseDouble(requestModel.getLatitude()),
                    Double.parseDouble(requestModel.getLongitude()));
            BigDecimal expectMileage = BigDecimal.valueOf(distance).divide(new BigDecimal("1000.00")).setScale(2, BigDecimal.ROUND_HALF_UP);
            responseModel.setExpectMileage(expectMileage);
        }

        return responseModel;
    }

    /**
     * 确认预约
     * @return
     */
    @Transactional
    public void confirmReservation(ConfirmReservationRequestModel requestModel){

        // 判断时间是否正确
        this.checkReservationTime(requestModel);

        //查询待预约的运单models
        List<WaitReservationCarrierOrderListResponseModel> orderList = this.getWaitReservationCarrierOrderModels(requestModel);
        if (ListUtils.isEmpty(orderList)){
            throw new BizException(CarrierDataExceptionEnum.SYSTEM_ONT_SUPPORT);
        }

        //判断单子地址是否一致
        List<String> addressList = new ArrayList<>();
        for (WaitReservationCarrierOrderListResponseModel model : orderList) {
            StringBuilder addressBuilder = new StringBuilder();
            if (StringUtils.isNotBlank(model.getWarehouse())){
                addressBuilder.append("【");
                addressBuilder.append(model.getWarehouse());
                addressBuilder.append("】");
            }
            addressBuilder.append(model.getProvinceName());
            addressBuilder.append(model.getCityName());
            addressBuilder.append(model.getAreaName());
            addressBuilder.append(model.getDetailAddress());

            if (!addressList.contains(addressBuilder.toString())){
                addressList.add(addressBuilder.toString());
            }
        }
        if (addressList.size() > CommonConstant.INTEGER_ONE){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_ADDRESS_DIFFERENT);
        }

        //获取经纬度存在的单子
        WaitReservationCarrierOrderListResponseModel waitReservationCarrierOrder = orderList
                .stream()
                .filter(f -> StringUtils.isNotBlank(f.getLongitude()) && StringUtils.isNotBlank(f.getLatitude()))
                .findFirst()
                .orElse(null);
        if (waitReservationCarrierOrder == null){
            waitReservationCarrierOrder = orderList.get(CommonConstant.INTEGER_ZERO);
        }
        String userName = BaseContextHandler.getUserName();
        Date now = new Date();

        //解析预约时间
        Date reservationDate;
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getReservationTimeType())){
            reservationDate = now;
        }else{
            reservationDate = DateUtils.add(now, Calendar.DAY_OF_WEEK, 1);
        }
        String reservationDateStr = DateUtils.dateToString(reservationDate, DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        String[] reservationTimes = requestModel.getReservationTime().split(CommonConstant.HYPHEN);
        Date reservationStartTime = DateUtils.stringToDate(reservationDateStr+" "+reservationTimes[0]+":00", DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
        Date reservationEndTime = DateUtils.stringToDate(reservationDateStr+" "+reservationTimes[1]+":00", DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);

        //生成预约单
        TReservationOrder tReservationOrder = MapperUtils.mapper(waitReservationCarrierOrder, TReservationOrder.class);
        tReservationOrder.setReservationOrderCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.RESERVATION_ORDER_CODE, "", userName));
        tReservationOrder.setStatus(ReservationOrderStatusEnum.WAIT_SIGN_IN.getKey());
        tReservationOrder.setReservationType(requestModel.getReservationType());
        tReservationOrder.setReservationStartTime(reservationStartTime);
        tReservationOrder.setReservationEndTime(reservationEndTime);
        tReservationOrder.setReservationRole(requestModel.getSource());
        tReservationOrder.setReservationSource(requestModel.getSource());
        tReservationOrder.setReservationPerson(requestModel.getMobilePhone());
        commonBiz.setBaseEntityAdd(tReservationOrder, userName, now);
        tReservationOrderMapper.insertSelective(tReservationOrder);

        //同步云仓model
        SyncReservationCarrierOrderCreateToWarehouseModel syncReservationCarrierOrderModel;
        List<SyncReservationCarrierOrderCreateToWarehouseModel> syncReservationCarrierOrderModelList = new ArrayList<>();
        //生成预约单明细
        TReservationOrderItem tReservationOrderItem;
        List<TReservationOrderItem> tReservationOrderItemList = new ArrayList<>();
        for (WaitReservationCarrierOrderListResponseModel model : orderList) {
            tReservationOrderItem = new TReservationOrderItem();
            tReservationOrderItem.setReservationOrderId(tReservationOrder.getId());
            tReservationOrderItem.setCarrierOrderId(model.getCarrierOrderId());
            tReservationOrderItem.setCarrierOrderCode(model.getCarrierOrderCode());
            if (requestModel.getExpectedStockIn()!= null){
                tReservationOrderItem.setExpectAmount(requestModel.getExpectedStockIn());
            }else {
                tReservationOrderItem.setExpectAmount(model.getExpectAmount());
            }
            tReservationOrderItem.setDemandOrderEntrustType(model.getEntrustType());
            tReservationOrderItem.setExpectedTime(model.getExpectedTime());
            commonBiz.setBaseEntityAdd(tReservationOrderItem, userName, now);
            tReservationOrderItemList.add(tReservationOrderItem);

            syncReservationCarrierOrderModel = new SyncReservationCarrierOrderCreateToWarehouseModel();
            syncReservationCarrierOrderModel.setCarrierOrderCode(tReservationOrderItem.getCarrierOrderCode());
            syncReservationCarrierOrderModel.setPlanCount(tReservationOrderItem.getExpectAmount());
            syncReservationCarrierOrderModelList.add(syncReservationCarrierOrderModel);
        }
        if (ListUtils.isNotEmpty(tReservationOrderItemList)){
            tReservationOrderItemMapper.batchInsert(tReservationOrderItemList);
        }

        //访客保存记录
        if (ReservationSourceEnum.H5.getKey().equals(requestModel.getSource())) {
            TReservationVisitorRecord tReservationVisitorRecord = new TReservationVisitorRecord();
            tReservationVisitorRecord.setIp(BaseContextHandler.getUserName());
            tReservationVisitorRecord.setReservationOrderId(tReservationOrder.getId());
            tReservationVisitorRecord.setOperateType(ReservationVisitorRecordOperateTypeEnum.RESERVE.getKey());
            commonBiz.setBaseEntityAdd(tReservationVisitorRecord,BaseContextHandler.getUserName());
            tReservationVisitorRecordMapper.insertSelective(tReservationVisitorRecord);
        }
        //创建预约单同步云仓
        SyncReservationOrderCreateToWarehouseModel warehouseModel = new SyncReservationOrderCreateToWarehouseModel();
        warehouseModel.setReservationCode(tReservationOrder.getReservationOrderCode());
        warehouseModel.setWarehouseName(tReservationOrder.getWarehouse());
        warehouseModel.setWarehouseProvince(tReservationOrder.getProvinceName());
        warehouseModel.setWarehouseCity(tReservationOrder.getCityName());
        warehouseModel.setWarehouseArea(tReservationOrder.getAreaName());
        warehouseModel.setWarehouseDetail(tReservationOrder.getDetailAddress());
        warehouseModel.setDriverName(tReservationOrder.getDriverName());
        warehouseModel.setDriverMobilePhone(tReservationOrder.getDriverMobile());
        warehouseModel.setIdentityCardNumber(tReservationOrder.getDriverIdentity());
        warehouseModel.setVehicleNumber(tReservationOrder.getVehicleNo());
        warehouseModel.setOperationNode(tReservationOrder.getReservationType());
        warehouseModel.setReservationTimeStart(tReservationOrder.getReservationStartTime());
        warehouseModel.setReservationTimeEnd(tReservationOrder.getReservationEndTime());
        warehouseModel.setItemModels(syncReservationCarrierOrderModelList);
        warehouseModel.setReservationRole(requestModel.getSource());
        warehouseModel.setReservationSource(requestModel.getSource());
        warehouseModel.setUserName(userName);
        rabbitMqPublishBiz.syncReservationOrderCreateToWarehouse(warehouseModel);
    }

    /**
     * 查询待预约运单列表
     */
    private List<WaitReservationCarrierOrderListResponseModel> getWaitReservationCarrierOrderModels(ConfirmReservationRequestModel requestModel) {

        //查询待预约运单列表
        List<WaitReservationCarrierOrderListResponseModel> orderList = new ArrayList<>();


        //小程序 有具体限制单子类型进行预约，H5没有
        if (ReservationSourceEnum.H5.getKey().equals(requestModel.getSource())) {
            if (ReservationTypeEnum.LOAD.getKey().equals(requestModel.getReservationType())) {
                orderList = tCarrierOrderMapper.loadWaitReservationCarrierOrder(null, requestModel.getCarrierOrderIdList(), null);
            } else if (ReservationTypeEnum.UNLOAD.getKey().equals(requestModel.getReservationType())) {
                orderList = tCarrierOrderMapper.unloadWaitReservationCarrierOrder(null, requestModel.getCarrierOrderIdList(), null);
            }
        } else if (ReservationSourceEnum.DRIVER_APP.getKey().equals(requestModel.getSource())) {
            List<CarrierDriverRelationModel> driverModel = commonBiz.getLoginUserDriver();
            if (ListUtils.isEmpty(driverModel)) {
                throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
            }
            List<CarrierDriverRelationForAppletModel> carrierDriverModel = MapperUtils.mapper(driverModel, CarrierDriverRelationForAppletModel.class);
            List<Integer> loadDemandOrderEntrustTypeList = List.of(1, 4);
            List<Integer> unLoadDemandOrderEntrustTypeList = List.of(2, 4, 12);

            if (ReservationTypeEnum.LOAD.getKey().equals(requestModel.getReservationType())) {
                orderList = tCarrierOrderMapper.loadWaitReservationCarrierOrder(carrierDriverModel, requestModel.getCarrierOrderIdList(), loadDemandOrderEntrustTypeList);
            } else if (ReservationTypeEnum.UNLOAD.getKey().equals(requestModel.getReservationType())) {
                orderList = tCarrierOrderMapper.unloadWaitReservationCarrierOrder(carrierDriverModel, requestModel.getCarrierOrderIdList(), unLoadDemandOrderEntrustTypeList);
            }
        }
        //如果是车主前台 查询车主id
        else if (ReservationSourceEnum.CARRIER_WEB.getKey().equals(requestModel.getSource())) {
            //查询车主
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
                throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_EMPTY);
            }

            if (ReservationTypeEnum.LOAD.getKey().equals(requestModel.getReservationType())) {
                orderList = tCarrierOrderMapper.loadWaitReservationCarrierOrder(null, requestModel.getCarrierOrderIdList(), null);
            } else if (ReservationTypeEnum.UNLOAD.getKey().equals(requestModel.getReservationType())) {
                orderList = tCarrierOrderMapper.unloadWaitReservationCarrierOrder(null, requestModel.getCarrierOrderIdList(), null);
            }
            if (ListUtils.isNotEmpty(orderList)){
                //判断运单是不是同一车辆同一司机
                boolean ifSameDriverAndCar = true;
                Long driverId = orderList.get(0).getDriverId();
                String vehicleNo = orderList.get(0).getVehicleNo();
                for (WaitReservationCarrierOrderListResponseModel order:  orderList){
                    if (order.getDriverId().equals(driverId) && vehicleNo.equals(order.getVehicleNo())){
                    }else {
                        ifSameDriverAndCar = false;
                        break;
                    }
                }
                if (!ifSameDriverAndCar){
                    throw new BizException(CarrierDataExceptionEnum.SYSTEM_ONT_SUPPORT_ACTION);
                }

                if (orderList.size()!= requestModel.getCarrierOrderIdList().size()){
                    throw new BizException(CarrierDataExceptionEnum.SYSTEM_ONT_SUPPORT_ACTION);
                }
            }
        }



        return orderList;
    }

    private void checkReservationTime(ConfirmReservationRequestModel requestModel) {
        if (requestModel.getReservationTimeType() == 1 && StringUtils.isNotEmpty(requestModel.getReservationTime())
                && requestModel.getReservationTime().split("-").length == 2) {
            LocalTime endTime = LocalTime.of(Integer.parseInt(requestModel.getReservationTime().split("-")[1].split(":")[0]),
                    Integer.parseInt(requestModel.getReservationTime().split("-")[1].split(":")[1])); // 下午
            LocalTime currentTime = LocalTime.now(); // 获取当前时间

            if (currentTime.isBefore(endTime)) {
            } else {
                if (endTime.equals( LocalTime.of(0,0))){
                    // 不判断0.00
                }else {
                    throw new BizException(CarrierDataExceptionEnum.RESERVATION_TIME_ERROR);
                }
            }
        }
    }

    /**
     * 预约单详情
     * @return
     */
    public ReservationOrderSignDetailResponseModel reservationOrderDetail(ReservationOrderSignDetailRequestModel requestModel){

        List<CarrierDriverRelationModel> driverModel = new ArrayList<>();
        //查询登录司机与车主关系信息
        if (requestModel.getSource() != null) {
            if (ReservationSourceEnum.DRIVER_APP.getKey().equals(requestModel.getSource())) {
                  driverModel = commonBiz.getLoginUserDriver();
                if (ListUtils.isEmpty(driverModel)) {
                    throw new BizException(CarrierDataExceptionEnum.RESERVATION_ORDER_NOT_EXIST);
                }
            } else if (ReservationSourceEnum.H5.getKey().equals(requestModel.getSource())) {
            }
        }

        //查询预约单
        TReservationOrder dbReservationOrder = null;
        if (requestModel.getReservationOrderId() != null) {
            dbReservationOrder = tReservationOrderMapper.selectByPrimaryKey(requestModel.getReservationOrderId());
        }else if (StringUtils.isNotBlank(requestModel.getCarrierOrderCode())) {
            List<TCarrierOrder> tCarrierOrders = tCarrierOrderMapper.getCarrierOrderByCodes(Arrays.asList(requestModel.getCarrierOrderCode()));
            if (CollectionUtil.isEmpty(tCarrierOrders)) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
            }
            TCarrierOrder tCarrierOrder = tCarrierOrders.get(0);
            Integer reservationType = EntrustTypeEnum.RECYCLE_IN.getKey().equals(tCarrierOrder.getDemandOrderEntrustType()) ?
                    ReservationTypeEnum.UNLOAD.getKey() : ReservationTypeEnum.LOAD.getKey();

            //查询预约单明细
            List<TReservationOrderItem> tReservationOrderItems = tReservationOrderItemMapper.listByCarrierOrderIds(Arrays.asList(tCarrierOrder.getId()));
            if (CollectionUtil.isEmpty(tReservationOrderItems)) {
                throw new BizException(CarrierDataExceptionEnum.RESERVATION_ORDER_ITEM_NOT_EXIST);
            }
            List<Long> tReservationOrderIds = tReservationOrderItems.stream().map(TReservationOrderItem::getReservationOrderId).collect(Collectors.toList());

            //查询预约单
            List<TReservationOrder> tReservationOrders = tReservationOrderMapper.getOrderByIds(tReservationOrderIds);
            dbReservationOrder = tReservationOrders.stream().filter(tReservationOrder -> reservationType.equals(tReservationOrder.getReservationType())).findFirst().orElse(null);

        }
        if (dbReservationOrder == null || dbReservationOrder.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.RESERVATION_ORDER_NOT_EXIST);
        }
        if (dbReservationOrder.getStatus().equals(ReservationOrderStatusEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.RESERVATION_ORDER_STATUS_ERROR);
        }
        //校验单子是否是该司机的
        TReservationOrder finalDbReservationOrder = dbReservationOrder;
        // h5不用校验
        if (ReservationSourceEnum.DRIVER_APP.getKey().equals(requestModel.getSource())){
            CarrierDriverRelationModel carrierDriverRelationModel = driverModel.stream().filter(item -> item.getDriverId().equals(finalDbReservationOrder.getDriverId())).findFirst().orElse(null);
            if (carrierDriverRelationModel == null || !ReservationOrderStatusEnum.WAIT_SIGN_IN.getKey().equals(dbReservationOrder.getStatus())){
                throw new BizException(CarrierDataExceptionEnum.RESERVATION_ORDER_NOT_EXIST);
            }
        }

        //查询预约单下运单信息
        List<ReservationCarrierOrderListResponseModel> orderList = tReservationOrderItemMapper.getOrderByReservationOrderId(dbReservationOrder.getId());

        //查询司机到提卸货地的距离
        BigDecimal expectMileage = null;
        if (StringUtils.isNotBlank(dbReservationOrder.getLongitude())
                && StringUtils.isNotBlank(dbReservationOrder.getLatitude())) {
            double distance = commonBiz.calculateDistance(
                    Double.parseDouble(dbReservationOrder.getLatitude()),
                    Double.parseDouble(dbReservationOrder.getLongitude()),
                    Double.parseDouble(requestModel.getLatitude()),
                    Double.parseDouble(requestModel.getLongitude()));
              expectMileage = BigDecimal.valueOf(distance).divide(new BigDecimal("1000.00")).setScale(2, BigDecimal.ROUND_HALF_UP);

        }

        //组装信息
        ReservationOrderSignDetailResponseModel responseModel = MapperUtils.mapper(dbReservationOrder, ReservationOrderSignDetailResponseModel.class);
        responseModel.setReservationOrderId(dbReservationOrder.getId());
        responseModel.setExpectMileage(expectMileage);
        responseModel.setOrderList(orderList);
        responseModel.setDemandOrderEntrustType(orderList.get(0).getEntrustType());
        responseModel.setDemandOrderEntrustTypeLabel(EntrustTypeEnum.getEnum(orderList.get(0).getEntrustType()).getValue());
        return responseModel;
    }

    /**
     * 确认签到
     * @return
     */
    @Transactional
    public void confirmSign(ReservationOrderConfirmSignRequestModel requestModel){
        //查询预约单
        TReservationOrder dbReservationOrder = tReservationOrderMapper.selectByPrimaryKey(requestModel.getReservationOrderId());
        if (dbReservationOrder == null || dbReservationOrder.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.RESERVATION_ORDER_NOT_EXIST);
        }

        //如果是访客登录
        if (ReservationSourceEnum.H5.getKey().equals(requestModel.getSource())) {
        }
        //如果是非访客登录 需要校验登录人
        else {
            List<CarrierDriverRelationModel> driverModel = commonBiz.getLoginUserDriver();
            if (ListUtils.isEmpty(driverModel)) {
                throw new BizException(CarrierDataExceptionEnum.RESERVATION_ORDER_NOT_EXIST);
            }
            //校验单子是否是该司机的
            CarrierDriverRelationModel carrierDriverRelationModel = driverModel.stream().filter(item -> item.getDriverId().equals(dbReservationOrder.getDriverId())).findFirst().orElse(null);
            if (carrierDriverRelationModel == null) {
                throw new BizException(CarrierDataExceptionEnum.RESERVATION_ORDER_NOT_EXIST);
            }
        }

        //只有【待签到】状态才能操作
        if (!ReservationOrderStatusEnum.WAIT_SIGN_IN.getKey().equals(dbReservationOrder.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.RESERVATION_ORDER_STATUS_ERROR);
        }

        //校验距离是否满足要求（大于5公里不能签到）
        if (StringUtils.isNotBlank(dbReservationOrder.getLongitude())
                && StringUtils.isNotBlank(dbReservationOrder.getLatitude())) {
            // 判断直线距离
            double distance = commonBiz.calculateDistance(
                    Double.parseDouble(dbReservationOrder.getLatitude()),
                    Double.parseDouble(dbReservationOrder.getLongitude()),
                    Double.parseDouble(requestModel.getLatitude()),
                    Double.parseDouble(requestModel.getLongitude()));
            BigDecimal expectMileage = BigDecimal.valueOf(distance).divide(new BigDecimal("1000.00")).setScale(2, BigDecimal.ROUND_HALF_UP);
            if (expectMileage.compareTo(CommonConstant.BIG_DECIMAL_FIFTY) > CommonConstant.INTEGER_ZERO){
               throw new BizException(CarrierDataExceptionEnum.RESERVATION_ORDER_SIGN_IN_ERROR);
            }
        }

        String userName = BaseContextHandler.getUserName();
        Date now = new Date();

        // 把单子变到达提货地 或到达卸货地
        Integer reservationType = dbReservationOrder.getReservationType();
        List<ReservationCarrierOrderListResponseModel> orderByReservationOrderId = tReservationOrderItemMapper.getOrderByReservationOrderId(dbReservationOrder.getId());
        if (ListUtils.isNotEmpty(orderByReservationOrderId)){
            List<Long> carrierOrderIds = orderByReservationOrderId.stream().map(ReservationCarrierOrderListResponseModel::getCarrierOrderId).collect(Collectors.toList());
            List<TCarrierOrder> tCarrierOrders = tCarrierOrderMapper.selectCarrierOrdersByIds(StringUtils.listToString(carrierOrderIds, ','));
            if (ReservationTypeEnum.LOAD.getKey().equals(reservationType)){
                List<TCarrierOrder> orders = tCarrierOrders.stream().filter(e -> e.getStatus().equals(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey())).collect(Collectors.toList());
                if (ListUtils.isNotEmpty(orders)){
                    ReachLoadAddressRequestModel reachLoadAddressRequestModel = new ReachLoadAddressRequestModel();
                    reachLoadAddressRequestModel.setCarrierOrderId(orders.stream().map(TCarrierOrder::getId).collect(Collectors.toList()));
                    reachLoadAddressRequestModel.setSource(1);
                    carrierOrderBiz.reachLoadAddress(reachLoadAddressRequestModel);
                }
            }else if (ReservationTypeEnum.UNLOAD.getKey().equals(reservationType)){
                List<TCarrierOrder> orders = tCarrierOrders.stream().filter(e -> e.getStatus().equals(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey())).collect(Collectors.toList());
                if (ListUtils.isNotEmpty(orders)) {
                    ReachUnloadAddressRequestModel reachUnloadAddressRequestModel = new ReachUnloadAddressRequestModel();
                    reachUnloadAddressRequestModel.setCarrierOrderId(orders.stream().map(TCarrierOrder::getId).collect(Collectors.toList()));
                    reachUnloadAddressRequestModel.setSource(1);
                    carrierOrderBiz.reachUnloadAddress(reachUnloadAddressRequestModel);
                }
            }

        }


        //更新预约单状态
        TReservationOrder tReservationOrder = new TReservationOrder();
        tReservationOrder.setId(dbReservationOrder.getId());
        tReservationOrder.setStatus(ReservationOrderStatusEnum.SIGN_IN.getKey());
        tReservationOrder.setReservationPerson(requestModel.getMobilePhone());
        tReservationOrder.setSignDate(now);
        commonBiz.setBaseEntityModify(tReservationOrder, userName, now);
        tReservationOrderMapper.updateByPrimaryKeySelective(tReservationOrder);

        //预约单签到同步云仓
        SyncReservationOrderSignInToWarehouseModel warehouseModel = new SyncReservationOrderSignInToWarehouseModel();
        warehouseModel.setReservationCode(dbReservationOrder.getReservationOrderCode());
        warehouseModel.setSignTime(now);
        warehouseModel.setUserName(userName);
        rabbitMqPublishBiz.syncReservationOrderSignInToWarehouse(warehouseModel);
    }


    public GetReservationInfo4H5RespModel getReservationInfo4H5(GetReservationInfo4H5ReqModel requestModel){
        GetReservationInfo4H5RespModel respModel = tCarrierOrderMapper.selectCarrierOrderReservationInfoByCode(requestModel.getCarrierOrderCode());
        Map<String, String> configMap = sysConfigBiz.getSysConfig(ConfigKeyEnum.ConfigGroupCodeEnum
                .RESERVATION_VERIFY.getCode());
        if (MapUtils.isEmpty(configMap)){
            respModel.setIfNeedVerifyDriver(0);
        }
        respModel.setIfNeedVerifyDriver(Integer.parseInt(configMap.get(ConfigKeyEnum.RESERVATION_VERIFY.getValue())));
        return respModel;


    }



    /**
     * 预约按钮 进入预约页面 v2.45
     */
    public void enterReservation(EnterReservationRequestModel requestModel) {

        //验证手机号、短信验证码
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getIfNeedVerifyDriver())) {
            basicDataClient.checkVerificationCode(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET.getKey(),VerificationCodeTypeEnum.H5_VISITOR_RESERVATION.getKey(),requestModel.getUserAccount(),requestModel.getVerificationCode());
        }

        //查询运单
        List<TCarrierOrder> carrierOrderByCodes = tCarrierOrderMapper.getCarrierOrderByCodes(Collections.singletonList(requestModel.getCarrierOrderCode()));
        if (CollectionUtil.isEmpty(carrierOrderByCodes)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        TCarrierOrder tCarrierOrder = carrierOrderByCodes.get(0);

        //查询运单地址
        TCarrierOrderAddress tCarrierOrderAddress = tCarrierOrderAddressMapper.getByCarrierOrderId(tCarrierOrder.getId());
        if (tCarrierOrderAddress == null) {
            throw new BizException(CarrierDataExceptionEnum.ENTRUST_ADDRESS_EMPTY);
        }

        final boolean ifCarrierOrderError = tCarrierOrder.getIfEmpty() == 1 ||tCarrierOrder.getIfCancel() == 1;
        if ( ifCarrierOrderError || (ReservationTypeEnum.LOAD.getKey().equals(requestModel.getReservationType()) && !(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey().equals(tCarrierOrder.getStatus()) ||
                CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(tCarrierOrder.getStatus())) )){
            throw new BizException(CarrierDataExceptionEnum.LOAD_RESERVATION_ORDER_CARRIER_STATUS_ERROR);
        }

        if (ifCarrierOrderError || (ReservationTypeEnum.UNLOAD.getKey().equals(requestModel.getReservationType()) && !(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey().equals(carrierOrderByCodes.get(0).getStatus()) ||
                CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(carrierOrderByCodes.get(0).getStatus())))){
            throw new BizException(CarrierDataExceptionEnum.UNLOAD_RESERVATION_ORDER_CARRIER_STATUS_ERROR);
        }


        //校验运单预约地址是我司
        this.checkCarrierOrderReservationAddressIsMine(requestModel.getReservationType(), tCarrierOrderAddress);

        //校验是否被重复预约   是否被重复预约：根据运单号+预约事件，判断是否被重复预约，重复预约提示“系统不支持操作”
        List<TReservationOrderItem> tReservationOrderItems = tReservationOrderItemMapper.listByCarrierOrderIds(Arrays.asList(tCarrierOrder.getId()));
        if (CollectionUtil.isNotEmpty(tReservationOrderItems)) {
            List<Long> reservationOrderIds = tReservationOrderItems.stream().map(TReservationOrderItem::getCarrierOrderId).distinct().collect(Collectors.toList());
            //再查询对应的预约单的预约事件
            List<TReservationOrder> tReservationOrders = tReservationOrderMapper.getOrderByIds(reservationOrderIds);
            boolean anyMatch = tReservationOrders.stream().anyMatch(tReservationOrder -> requestModel.getReservationType().equals(tReservationOrder.getReservationType()));
            if (anyMatch) {
                throw new BizException(CarrierDataExceptionEnum.ENTRUST_ADDRESS_EMPTY);
            }
        }


    }



    /**
     * 校验运单预约地址是我司
     */
    private void checkCarrierOrderReservationAddressIsMine(Integer reservationType, TCarrierOrderAddress tCarrierOrderAddress) {

        //提货取提货仓库 卸货取卸货仓库
        String warehouseName = reservationType.equals(ReservationTypeEnum.LOAD.getKey()) ? tCarrierOrderAddress.getLoadWarehouse() : tCarrierOrderAddress.getUnloadWarehouse();
        if (StringUtils.isEmpty(warehouseName)){
            throw new BizException(CarrierDataExceptionEnum.SYSTEM_ONT_SUPPORT_ACTION);
        }
        GetWareHouseByIdsAndKeyWordAndTypeRequestModel wareHouseByIdsAndKeyWordAndTypeRequestModel = new GetWareHouseByIdsAndKeyWordAndTypeRequestModel();
        wareHouseByIdsAndKeyWordAndTypeRequestModel.setWarehouseNameInAllMatch(warehouseName);
        Result<List<GetWareHouseByIdsAndKeyWordAndTypeResponseModel>> warehouseResult = warehouseStockServiceApi.getWareHouseByIdsAndKeyWordAndType(wareHouseByIdsAndKeyWordAndTypeRequestModel);
        warehouseResult.throwException();
        List<GetWareHouseByIdsAndKeyWordAndTypeResponseModel> warehouseModels = warehouseResult.getData();
        if (CollectionUtil.isEmpty(warehouseModels)) {
            throw new BizException(CarrierDataExceptionEnum.SYSTEM_ONT_SUPPORT_ACTION);
        }
    }

    /**
     * 签到按钮 进入签到页面 v2.45
     */
    public void enterSignUp(EnterSignUpRequestModel requestModel) {

        //验证手机号、短信验证码
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getIfNeedVerifyDriver())) {
            basicDataClient.checkVerificationCode(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET.getKey(),VerificationCodeTypeEnum.H5_VISITOR_SIGN_IN.getKey(),requestModel.getUserAccount(),requestModel.getVerificationCode());
        }

        //查询运单
        List<TCarrierOrder> carrierOrderByCodes = tCarrierOrderMapper.getCarrierOrderByCodes(Collections.singletonList(requestModel.getCarrierOrderCode()));
        if (CollectionUtil.isEmpty(carrierOrderByCodes)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //校验是否被重复预约   是否被重复预约：根据运单号+预约事件，判断是否被重复预约，重复预约提示“系统不支持操作”
        TReservationOrder reservationOrder = tReservationOrderMapper.selectByPrimaryKey(requestModel.getReservationOrderId());

//        List<TReservationOrderItem> tReservationOrderItems = tReservationOrderItemMapper.listByCarrierOrderIds(Arrays.asList(tCarrierOrder.getId()));
//        if (CollectionUtil.isNotEmpty(tReservationOrderItems)) {
//            List<Long> reservationOrderIds = tReservationOrderItems.stream().map(TReservationOrderItem::getCarrierOrderId).distinct().collect(Collectors.toList());
//            //再查询对应的预约单的预约事件
//            List<TReservationOrder> tReservationOrders = tReservationOrderMapper.getOrderByIds(reservationOrderIds);
//            reservationOrder = tReservationOrders.stream().filter(tReservationOrder -> requestModel.getReservationType().equals(tReservationOrder.getReservationType())).findFirst().orElse(null);
//        }
        if (reservationOrder == null || !ReservationOrderStatusEnum.WAIT_SIGN_IN.getKey().equals(reservationOrder.getStatus())) {
            throw new BizException(CarrierDataExceptionEnum.RESERVATION_ORDER_NOT_EXIST);
        }

    }




    /**
     * 运单列表-预约按钮弹窗回显
     */
    public CarrierOrderListStartReservationDetailResponseModel searchCarrierReservationOrder(SearchCarrierReservationOrderReqModel requestModel) {
        //勾选运单 (永远只查询运单) 取第一条运单的信息回显
        if (CollectionUtil.isNotEmpty(requestModel.getCarrierOrderIds())) {
            return this.chooseCarrierOrderStartReservationDetail(requestModel);
        }
        //未勾选运单 (根据条件过滤运单) 不更新入参查询条件
        else {
            return this.notChooseCarrierOrderStartReservationDetail(requestModel);
        }
    }

    /**
     * 勾选运单 (永远只查询运单) 取第一条运单的信息回显
     */
    public CarrierOrderListStartReservationDetailResponseModel chooseCarrierOrderStartReservationDetail(SearchCarrierReservationOrderReqModel requestModel){
        //运单
        String carrierOrderIdsJoin = requestModel.getCarrierOrderIds().stream().collect(Collectors.joining(","));
        requestModel.enablePaging();
        List<TCarrierOrder> tCarrierOrders = tCarrierOrderMapper.selectYunPanCarrierOrdersByIds(carrierOrderIdsJoin);
        if (CollectionUtil.isEmpty(tCarrierOrders)) {
            return new CarrierOrderListStartReservationDetailResponseModel();
        }
        PageInfo pageInfo = new PageInfo<>(tCarrierOrders);

        //运单车辆
        List<TCarrierOrderVehicleHistory> tCarrierOrderVehicleHistories = tCarrierOrderVehicleHistoryMapper.getValidByCarrierOrderIds(carrierOrderIdsJoin);
        Map<Long, List<TCarrierOrderVehicleHistory>> tCarrierOrderVehicleHistoriesMap = tCarrierOrderVehicleHistories.stream().collect(Collectors.groupingBy(TCarrierOrderVehicleHistory::getCarrierOrderId));



        List<TCarrierOrderAddress> tCarrierOrderAddresses = tCarrierOrderAddressMapper.getByCarrierOrderIds(carrierOrderIdsJoin);
        Map<Long, TCarrierOrderAddress> tCarrierOrderAddressesMap = tCarrierOrderAddresses.stream().collect(Collectors.toMap(TCarrierOrderAddress::getCarrierOrderId, Function.identity()));


        List<SearchCarrierReservationOrderRespModel> respModels = new ArrayList<>();
        for (TCarrierOrder tCarrierOrder : tCarrierOrders) {

            TCarrierOrderAddress tCarrierOrderAddress = tCarrierOrderAddressesMap.get(tCarrierOrder.getId());

            List<TCarrierOrderVehicleHistory> carrierOrderVehicleHistories = tCarrierOrderVehicleHistoriesMap.getOrDefault(tCarrierOrder.getId(), new ArrayList<>());
            TCarrierOrderVehicleHistory lastCarrierOrderVehicleHistory = carrierOrderVehicleHistories.stream().max(Comparator.comparing(TCarrierOrderVehicleHistory::getId)).orElse(null);



            //拿到当前运单的预约类型
            Integer reservationType = this.getReservationType(tCarrierOrder);


            SearchCarrierReservationOrderRespModel searchCarrierReservationOrderRespModel = new SearchCarrierReservationOrderRespModel();
            searchCarrierReservationOrderRespModel.setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
            searchCarrierReservationOrderRespModel.setCarrierOrderId(tCarrierOrder.getId());
            if (lastCarrierOrderVehicleHistory != null) {
                searchCarrierReservationOrderRespModel.setDriverName(lastCarrierOrderVehicleHistory.getDriverName());
                searchCarrierReservationOrderRespModel.setReservationVehicleNo(lastCarrierOrderVehicleHistory.getVehicleNo());
            }
            if (tCarrierOrderAddress != null) {
                searchCarrierReservationOrderRespModel.setLoadAddress(tCarrierOrderAddress.getLoadProvinceName() + tCarrierOrderAddress.getLoadCityName() + tCarrierOrderAddress.getLoadAreaName());
                searchCarrierReservationOrderRespModel.setUnLoadAddress(tCarrierOrderAddress.getUnloadProvinceName() + tCarrierOrderAddress.getUnloadCityName() + tCarrierOrderAddress.getUnloadAreaName());
                searchCarrierReservationOrderRespModel.setWarehouseName(ReservationTypeEnum.LOAD.getKey().equals(reservationType) ? tCarrierOrderAddress.getLoadWarehouse() : tCarrierOrderAddress.getUnloadWarehouse());
            }
            searchCarrierReservationOrderRespModel.setLoadAmountExpect(tCarrierOrder.getExpectAmount().stripTrailingZeros());
            searchCarrierReservationOrderRespModel.setReservationType(reservationType);
            searchCarrierReservationOrderRespModel.setReservationTypeLabel(ReservationTypeEnum.getEnum(reservationType).getValue());
            respModels.add(searchCarrierReservationOrderRespModel);
        }
        //把预约类型为空的排最后
        respModels = respModels.stream().sorted(Comparator.nullsLast(Comparator.comparing(SearchCarrierReservationOrderRespModel::getReservationType))).collect(Collectors.toList());
        pageInfo.setList(respModels);

        CarrierOrderListStartReservationDetailResponseModel responseModel = new CarrierOrderListStartReservationDetailResponseModel();
        responseModel.setPageInfo(pageInfo);
        responseModel.setReservationType(respModels.get(0).getReservationType());
        responseModel.setReservationVehicleNo(respModels.get(0).getReservationVehicleNo());

        return responseModel;
    }


    /**
     * 提货：运单需求类型为【调拨】、【发货】时，运单的状态【待到达提货地】或【待提货】；
     * 卸货：运单需求类型为【调拨】、【回收入库】时，运单的状态【待到达卸货地】、【待卸货】；
     */
    private Integer getReservationType(TCarrierOrder tCarrierOrder) {
        Integer reservationType = ReservationTypeEnum.DEFAULT.getKey();
//        //判断运单是提货还是卸货
//        if (Arrays.asList(EntrustTypeEnum.TRANSFERS.getKey(), EntrustTypeEnum.DELIVER.getKey()).contains(tCarrierOrder.getDemandOrderEntrustType())) {
//            if (Arrays.asList(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey(), CarrierOrderStatusEnum.WAIT_LOAD.getKey()).contains(tCarrierOrder.getStatus())) {
//                reservationType = ReservationTypeEnum.LOAD.getKey();
//            }
//        }else if (Arrays.asList(EntrustTypeEnum.TRANSFERS.getKey(), EntrustTypeEnum.RECYCLE_IN.getKey()).contains(tCarrierOrder.getDemandOrderEntrustType())){
//            if (Arrays.asList(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey(), CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()).contains(tCarrierOrder.getStatus())) {
//                reservationType = ReservationTypeEnum.UNLOAD.getKey();
//            }
//        }
        //判断运单是提货还是卸货
        if (Arrays.asList(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey(), CarrierOrderStatusEnum.WAIT_LOAD.getKey()).contains(tCarrierOrder.getStatus())) {
            reservationType = ReservationTypeEnum.LOAD.getKey();
        }else if (Arrays.asList(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey(), CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()).contains(tCarrierOrder.getStatus())) {
            reservationType = ReservationTypeEnum.UNLOAD.getKey();
        }
        return reservationType;
    }

    /**
     * 未勾选运单 (根据条件过滤运单) 不更新入参查询条件
     */
    public CarrierOrderListStartReservationDetailResponseModel notChooseCarrierOrderStartReservationDetail(SearchCarrierReservationOrderReqModel requestModel) {
        //获取当前登录司机
        Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }

        Map<Long, TCarrierOrderVehicleHistory> tCarrierOrderVehicleHistoriesMap = new HashMap<>();
        if (StringUtils.isNotBlank(requestModel.getVehicleNo())) {
            List<TCarrierOrderVehicleHistory> tCarrierOrderVehicleHistories = tCarrierOrderVehicleHistoryMapper.listValidByVehicleNo(requestModel.getVehicleNo());
            if (CollectionUtil.isEmpty(tCarrierOrderVehicleHistories)) {
                return new CarrierOrderListStartReservationDetailResponseModel();
            }
            tCarrierOrderVehicleHistoriesMap = tCarrierOrderVehicleHistories.stream().collect(Collectors.toMap(TCarrierOrderVehicleHistory::getCarrierOrderId, Function.identity()));
        }

        //运单
        requestModel.enablePaging();
        List<TCarrierOrder> tCarrierOrders = tCarrierOrderMapper.searchCarrierReservationOrder(loginUserCompanyCarrierId, requestModel, tCarrierOrderVehicleHistoriesMap.keySet());
        if (CollectionUtil.isEmpty(tCarrierOrders)) {
            return new CarrierOrderListStartReservationDetailResponseModel();
        }
        List<Long> tCarrierOrderIds = tCarrierOrders.stream().map(TCarrierOrder::getId).collect(Collectors.toList());
        String tCarrierOrderIdsJoin = tCarrierOrderIds.stream().map(Objects::toString).collect(Collectors.joining(","));
        PageInfo pageInfo = new PageInfo<>(tCarrierOrders);

        //运单车辆
        if (MapUtil.isEmpty(tCarrierOrderVehicleHistoriesMap)) {
            List<TCarrierOrderVehicleHistory> tCarrierOrderVehicleHistories = tCarrierOrderVehicleHistoryMapper.getValidByCarrierOrderIds(tCarrierOrderIdsJoin);
            tCarrierOrderVehicleHistoriesMap = tCarrierOrderVehicleHistories.stream().collect(Collectors.toMap(TCarrierOrderVehicleHistory::getCarrierOrderId, Function.identity()));
        }

        //运单地址
        List<TCarrierOrderAddress> tCarrierOrderAddresses = tCarrierOrderAddressMapper.getByCarrierOrderIds(tCarrierOrderIdsJoin);
        Map<Long, TCarrierOrderAddress> tCarrierOrderAddressesMap = tCarrierOrderAddresses.stream().collect(Collectors.toMap(TCarrierOrderAddress::getCarrierOrderId, Function.identity()));

        //组装返回参数
        List<SearchCarrierReservationOrderRespModel> respModels = new ArrayList<>();
        for (TCarrierOrder tCarrierOrder : tCarrierOrders) {

            TCarrierOrderAddress tCarrierOrderAddress = tCarrierOrderAddressesMap.get(tCarrierOrder.getId());

            TCarrierOrderVehicleHistory carrierOrderVehicleHistory = tCarrierOrderVehicleHistoriesMap.get(tCarrierOrder.getId());


            /**
             * 提货：运单需求类型为【调拨】、【发货】时，运单的状态【待到达提货地】或【待提货】；
             * 卸货：运单需求类型为【调拨】、【回收入库】时，运单的状态【待到达卸货地】、【待卸货】；
             */
            Integer reservationType = ReservationTypeEnum.DEFAULT.getKey();
            //判断运单是提货还是卸货
            if (Arrays.asList(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey(), CarrierOrderStatusEnum.WAIT_LOAD.getKey()).contains(tCarrierOrder.getStatus())) {
                reservationType = ReservationTypeEnum.LOAD.getKey();
            } else if (Arrays.asList(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey(), CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()).contains(tCarrierOrder.getStatus())) {
                reservationType = ReservationTypeEnum.UNLOAD.getKey();
            }
//            if (Arrays.asList(EntrustTypeEnum.TRANSFERS.getKey(), EntrustTypeEnum.DELIVER.getKey()).contains(tCarrierOrder.getDemandOrderEntrustType())) {
//
//            } else if (Arrays.asList(EntrustTypeEnum.TRANSFERS.getKey(), EntrustTypeEnum.RECYCLE_IN.getKey(), EntrustTypeEnum.RETURN_GOODS_TRANSFERS.getKey()).contains(tCarrierOrder.getDemandOrderEntrustType())) {
//
//            }


            SearchCarrierReservationOrderRespModel searchCarrierReservationOrderRespModel = new SearchCarrierReservationOrderRespModel();
            searchCarrierReservationOrderRespModel.setCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
            searchCarrierReservationOrderRespModel.setCarrierOrderId(tCarrierOrder.getId());
            if (carrierOrderVehicleHistory != null) {
                searchCarrierReservationOrderRespModel.setDriverName(carrierOrderVehicleHistory.getDriverName());
                searchCarrierReservationOrderRespModel.setReservationVehicleNo(carrierOrderVehicleHistory.getVehicleNo());
            }
            if (tCarrierOrderAddress != null) {
                searchCarrierReservationOrderRespModel.setLoadAddress(tCarrierOrderAddress.getLoadProvinceName() + tCarrierOrderAddress.getLoadCityName() + tCarrierOrderAddress.getLoadAreaName());
                searchCarrierReservationOrderRespModel.setUnLoadAddress(tCarrierOrderAddress.getUnloadProvinceName() + tCarrierOrderAddress.getUnloadCityName() + tCarrierOrderAddress.getUnloadAreaName());
                searchCarrierReservationOrderRespModel.setWarehouseName(ReservationTypeEnum.LOAD.getKey().equals(reservationType) ? tCarrierOrderAddress.getLoadWarehouse() : tCarrierOrderAddress.getUnloadWarehouse());
            }
            searchCarrierReservationOrderRespModel.setLoadAmountExpect(tCarrierOrder.getExpectAmount().stripTrailingZeros());
            searchCarrierReservationOrderRespModel.setReservationType(reservationType);
            searchCarrierReservationOrderRespModel.setReservationTypeLabel(ReservationTypeEnum.getEnum(reservationType).getValue());
            respModels.add(searchCarrierReservationOrderRespModel);
        }
        //把预约类型为空的排最后
        respModels = respModels.stream().sorted(Comparator.nullsLast(Comparator.comparing(SearchCarrierReservationOrderRespModel::getReservationType))).collect(Collectors.toList());
        pageInfo.setList(respModels);

        CarrierOrderListStartReservationDetailResponseModel responseModel = new CarrierOrderListStartReservationDetailResponseModel();
        responseModel.setPageInfo(pageInfo);
        responseModel.setReservationType(respModels.get(0).getReservationType());
        responseModel.setReservationVehicleNo(respModels.get(0).getReservationVehicleNo());

        return responseModel;
    }



    /**
     * 司机前台预约单列表
     */
    public PageInfo<ReservationOrderSearchListResponseModel> searchListForDriverWeb(ReservationOrderSearchListRequestModel requestModel) {
        //获取当前车主
        Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        requestModel.setCarrierCompanyId(loginUserCompanyCarrierId);
        //分页
        requestModel.enablePaging();
        //查询匹配的预约单
        List<ReservationOrderSearchListResponseModel> responseModels = tReservationOrderMapper.searchListForDriverWeb(requestModel);
        if (CollectionUtil.isEmpty(responseModels)) {
            return new PageInfo<>(new ArrayList<>());
        }
        PageInfo pageInfo = new PageInfo<>(responseModels);
        return pageInfo;

    }

    /**
     * 后台web预约单列表
     */
    public PageInfo<ReservationOrderSearchListForManagementWebResModel> searchListForManagementWeb(ReservationOrderSearchListForManagementWebReqModel requestModel) {

        //分页
        requestModel.enablePaging();
        //查询匹配的预约单
        List<ReservationOrderSearchListForManagementWebResModel> responseModels = tReservationOrderMapper.searchListForManagementWeb(requestModel);
        if (CollectionUtil.isEmpty(responseModels)) {
            return new PageInfo<>(new ArrayList<>());
        }
        PageInfo pageInfo = new PageInfo<>(responseModels);
        return pageInfo;

    }

    /**
     * 校验预约信息返回仓库name
     * 选择运单后，请点击第二步获取预约时间，进行以下校验，校验成功刷新预约仓库以及预约时间，不成功提示“系统不支持操作”：
     * 勾选的运单是否是同一车辆且是同一司机；
     * 勾选的运单是否是同一仓库并且属于云仓仓库；
     */
    public CheckReservationAndGetWarehouseNameResponseModel checkReservationAndGetWarehouseName(CheckReservationAndGetWarehouseNameRequestModel requestModel) {

        //查询运单集合
        String carrierOrderIdsJoin = requestModel.getCarrierOrderIds().stream().map(Objects::toString).collect(Collectors.joining(","));
        List<TCarrierOrder> tCarrierOrders = tCarrierOrderMapper.selectCarrierOrdersByIds(carrierOrderIdsJoin);
        if (CollectionUtil.isEmpty(tCarrierOrders)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        TCarrierOrder firstCarrierOrder = tCarrierOrders.get(0);
        Map<Long, TCarrierOrder> tCarrierOrdersMap = tCarrierOrders.stream().collect(Collectors.toMap(TCarrierOrder::getId, Function.identity()));

        //查询运单车辆信息
        List<TCarrierOrderVehicleHistory> tCarrierOrderVehicleHistories = tCarrierOrderVehicleHistoryMapper.getValidByCarrierOrderIds(carrierOrderIdsJoin);
        if (CollectionUtil.isEmpty(tCarrierOrderVehicleHistories)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_VEHICLE_RELATION_EMPTY);
        }

        //查询运单地址
        List<TCarrierOrderAddress> tCarrierOrderAddresses = tCarrierOrderAddressMapper.getByCarrierOrderIds(carrierOrderIdsJoin);
        if (CollectionUtil.isEmpty(tCarrierOrderAddresses)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_ADDRESS_NOT_EXIST);
        }
        Map<Long, TCarrierOrderAddress> tCarrierOrderAddressesMap = tCarrierOrderAddresses.stream().collect(Collectors.toMap(TCarrierOrderAddress::getCarrierOrderId, Function.identity()));


        //校验车辆司机信息
        String firstVehicleNo = tCarrierOrderVehicleHistories.get(0).getVehicleNo();
        String firstDriverName = tCarrierOrderVehicleHistories.get(0).getDriverName();
        boolean allMatchVehicleDriverSame = tCarrierOrderVehicleHistories.stream().allMatch(tCarrierOrderVehicleHistory -> firstVehicleNo.equals(tCarrierOrderVehicleHistory.getVehicleNo()) && firstDriverName.equals(tCarrierOrderVehicleHistory.getDriverName()));
        if (!allMatchVehicleDriverSame) {
            throw new BizException(CarrierDataExceptionEnum.SYSTEM_ONT_SUPPORT_ACTION);
        }

        //校验预约类型
        Integer firstReservationType = this.getReservationType(firstCarrierOrder);
        boolean allMatchReservationTypeSame = tCarrierOrders.stream().allMatch(tCarrierOrder -> firstReservationType.equals(this.getReservationType(tCarrierOrder)));
        if (!allMatchReservationTypeSame) {
            throw new BizException(CarrierDataExceptionEnum.SYSTEM_ONT_SUPPORT_ACTION);
        }

        //校验仓库一致
        TCarrierOrderAddress firstCarrierOrderAddress = tCarrierOrderAddressesMap.get(firstCarrierOrder.getId());
        String firstWarehouseName = ReservationTypeEnum.LOAD.getKey().equals(firstReservationType) ? firstCarrierOrderAddress.getLoadWarehouse() : firstCarrierOrderAddress.getUnloadWarehouse();
        boolean allMatchWarehouseSame = tCarrierOrderAddresses.stream().allMatch(tCarrierOrderAddress -> {
            TCarrierOrder tCarrierOrder = tCarrierOrdersMap.get(tCarrierOrderAddress.getCarrierOrderId());
            if (tCarrierOrder == null) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
            }
            Integer reservationType = this.getReservationType(tCarrierOrder);
            String warehouseName = ReservationTypeEnum.LOAD.getKey().equals(reservationType) ? tCarrierOrderAddress.getLoadWarehouse() : tCarrierOrderAddress.getUnloadWarehouse();
            return firstWarehouseName.equals(warehouseName);
        });
        if (!allMatchWarehouseSame) {
            throw new BizException(CarrierDataExceptionEnum.SYSTEM_ONT_SUPPORT_ACTION);
        }

        //组装参数返回
        CheckReservationAndGetWarehouseNameResponseModel responseModel = new CheckReservationAndGetWarehouseNameResponseModel();
        responseModel.setWarehouseName(firstWarehouseName);
        return responseModel;
    }


}
