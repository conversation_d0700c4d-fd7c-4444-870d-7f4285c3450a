package com.logistics.management.webapi.controller.warehouseaddress;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportWarehouseAddressInfo;
import com.logistics.management.webapi.base.utils.ConvertPageInfoUtil;
import com.logistics.management.webapi.client.warehouseaddress.WarehouseAddressClient;
import com.logistics.management.webapi.client.warehouseaddress.request.*;
import com.logistics.management.webapi.client.warehouseaddress.response.SearchWarehouseAddressResponseModel;
import com.logistics.management.webapi.client.warehouseaddress.response.WarehouseAddressDetailResponseModel;
import com.logistics.management.webapi.client.warehouseaddress.response.WarehouseAddressListResponseModel;
import com.logistics.management.webapi.controller.warehouseaddress.mapping.WarehouseAddressListExportMapping;
import com.logistics.management.webapi.controller.warehouseaddress.mapping.WarehouseAddressListMapping;
import com.logistics.management.webapi.controller.warehouseaddress.request.*;
import com.logistics.management.webapi.controller.warehouseaddress.response.SearchWarehouseAddressResponseDto;
import com.logistics.management.webapi.controller.warehouseaddress.response.WarehouseAddressDetailListResponseDto;
import com.logistics.management.webapi.controller.warehouseaddress.response.WarehouseAddressDetailResponseDto;
import com.logistics.management.webapi.controller.warehouseaddress.response.WarehouseAddressListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 仓库地址管理
 * @author: wjf
 * @date: 2024/7/11 13:43
 */
@Api(value = "仓库地址管理",tags = "仓库地址管理")
@RestController
@RequestMapping(value = "/api/warehouseaddress")
public class WarehouseAddressController {

    @Resource
    private WarehouseAddressClient warehouseAddressClient;

    /**
     * 列表
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/list")
    public Result<PageInfo<WarehouseAddressListResponseDto>> warehouseAddressList(@RequestBody WarehouseAddressListRequestDto requestDto) {
        Result<PageInfo<WarehouseAddressListResponseModel>> result = warehouseAddressClient.warehouseAddressList(MapperUtils.mapper(requestDto, WarehouseAddressListRequestModel.class));
        result.throwException();
        return Result.success(ConvertPageInfoUtil.convertPageInfo(result.getData(), WarehouseAddressListResponseDto.class,new WarehouseAddressListMapping()));
    }

    /**
     * 新增/编辑/删除
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/addOrModifyOrDel")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> warehouseAddressAddOrModifyOrDel(@RequestBody @Valid AddWarehouseAddressRequestDto requestDto) {
        return warehouseAddressClient.warehouseAddressAddOrModifyOrDel(MapperUtils.mapper(requestDto, AddWarehouseAddressRequestModel.class));
    }

    /**
     * 详情
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/detail")
    public Result<WarehouseAddressDetailResponseDto> warehouseAddressDetail(@RequestBody @Valid WarehouseAddressDetailRequestDto requestDto) {
        Result<WarehouseAddressDetailResponseModel> warehouseAddressDetailResponseModelResult = warehouseAddressClient.warehouseAddressDetail(MapperUtils.mapper(requestDto, WarehouseAddressDetailRequestModel.class));
        warehouseAddressDetailResponseModelResult.throwException();
        return Result.success(MapperUtils.mapper(warehouseAddressDetailResponseModelResult.getData(),WarehouseAddressDetailResponseDto.class));
    }

    /**
     * 详情列表
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/detailList")
    public Result<List<WarehouseAddressDetailListResponseDto>> warehouseAddressDetailList(@RequestBody WarehouseAddressDetailListRequestDto requestDto) {
        WarehouseAddressListRequestModel requestModel = MapperUtils.mapper(requestDto, WarehouseAddressListRequestModel.class);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<WarehouseAddressListResponseModel>> result = warehouseAddressClient.warehouseAddressList(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData().getList(), WarehouseAddressDetailListResponseDto.class));
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @GetMapping(value = "/export")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void export(WarehouseAddressListRequestDto requestDto, HttpServletResponse response) {
        Result<List<WarehouseAddressListResponseModel>> result = warehouseAddressClient.export(MapperUtils.mapper(requestDto, WarehouseAddressListRequestModel.class));
        result.throwException();
        String fileName = "仓库配置" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        List<WarehouseAddressListResponseDto> resultList = MapperUtils.mapper(result.getData(),WarehouseAddressListResponseDto.class,  new WarehouseAddressListExportMapping());
        Map<String, String> exportTypeMap = ExportWarehouseAddressInfo.getWarehouseAddressInfo();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return resultList;
            }
        });
    }

    /**
     * 启用/禁用
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/enable")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> enable(@RequestBody @Valid WarehouseAddressEnableRequestDto requestDto) {
        WarehouseAddressEnableRequestModel requestModel = MapperUtils.mapper(requestDto, WarehouseAddressEnableRequestModel.class);
        return warehouseAddressClient.enable(requestModel);
    }

    /**
     * 根据仓库名模糊搜索地址信息 3.25.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/searchWarehouseAddress")
    public Result<PageInfo<SearchWarehouseAddressResponseDto>> searchWarehouseAddress(@RequestBody SearchWarehouseAddressRequestDto requestDto){
        Result<PageInfo<SearchWarehouseAddressResponseModel>> result = warehouseAddressClient.searchWarehouseAddress(MapperUtils.mapper(requestDto, SearchWarehouseAddressRequestModel.class));
        return Result.success(ConvertPageInfoUtil.convertPageInfo(result.getData(), SearchWarehouseAddressResponseDto.class));
    }
}
