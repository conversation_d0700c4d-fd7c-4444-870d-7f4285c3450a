package com.logistics.tms.controller.workordercenter.response;

import com.yelo.tools.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/17
 */
@Data
public class WorkOrderDetailResponseModel {

	@ApiModelProperty("工单id")
	private Long workOrderId;

	@ApiModelProperty("状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销")
	private Integer status;

	@ApiModelProperty("需求单ID")
	private Long demandOrderId;

	@ApiModelProperty("运单ID")
	private Long carrierOrderId ;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty(value = "异常类型（一级）：10 联系不上客户，20 不想还盘，30 不可抗力，40 重复下单")
	private Integer anomalyTypeOne;

	@ApiModelProperty(value = "异常类型（二级）：")
	private Integer anomalyTypeTwo;

	@ApiModelProperty("提报人")
	private String reportUserName;

	@ApiModelProperty("提报时间")
	private Date reportTime;

	@ApiModelProperty("司机id")
	private Long driverId;

	@ApiModelProperty("司机名")
	private String driverName;

	@ApiModelProperty("司机手机号")
	private String driverPhone;

	@ApiModelProperty("司机是否到达现场：0 否，1 是")
	private Integer isArriveScene;

	@ApiModelProperty("地址详情")
	private String addressDetail;

	@ApiModelProperty("核验联系方式: 1 无误，2 有误")
	private Integer checkContact;

	@ApiModelProperty(value = "联系人姓名")
	private String contactName;

	@ApiModelProperty(value = "联系人联系方式")
	private String contactTelephone;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "现场证明-相对路径")
	private String arriveScenePicture;

	@ApiModelProperty(value = "地址抬头")
	private String addressHead;

	@ApiModelProperty("提报来源：1 后台，2 前台，3 小程序")
	private Integer reportSource;

	@ApiModelProperty("发货人手机号")
	private String consignorMobile;

	@ApiModelProperty("发货人姓名")
	private String consignorName;

	public String getConsignorMobile() {
		return StringUtils.isNotBlank(consignorMobile) ? consignorMobile : contactTelephone;
	}

	public String getConsignorName() {
		return StringUtils.isNotBlank(consignorName) ? consignorName : contactName;
	}
}
