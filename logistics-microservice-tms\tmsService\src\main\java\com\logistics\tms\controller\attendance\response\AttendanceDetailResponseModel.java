package com.logistics.tms.controller.attendance.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class AttendanceDetailResponseModel {

    @ApiModelProperty("考勤用户姓名")
    private String staffName;

    @ApiModelProperty("考勤用户手机号")
    private String staffMobile;

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("考勤日期")
    private Date attendanceDate;

    @ApiModelProperty("上班打卡时间")
    private Date onDutyPunchTime;

    @ApiModelProperty("下班打卡时间")
    private Date offDutyPunchTime;

    @ApiModelProperty("上班打卡图片")
    private String onDutyPunchPic;

    @ApiModelProperty("下班打卡图片")
    private String offDutyPunchPic;
}
