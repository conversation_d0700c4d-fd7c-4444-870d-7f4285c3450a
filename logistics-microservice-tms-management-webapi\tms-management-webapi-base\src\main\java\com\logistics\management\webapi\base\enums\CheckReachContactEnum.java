package com.logistics.management.webapi.base.enums;

public enum CheckReachContactEnum {

    NULL(-1,""),
    ERRORLESS(0, "无误"),
    WRONG(1, "有误"),

    ;

    private Integer key;
    private String value;

    CheckReachContactEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CheckReachContactEnum getEnum(Integer key) {
        for (CheckReachContactEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return NULL;
    }

}
