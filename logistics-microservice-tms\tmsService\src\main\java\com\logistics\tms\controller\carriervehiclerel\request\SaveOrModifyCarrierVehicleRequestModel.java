package com.logistics.tms.controller.carriervehiclerel.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新增/修改车主车辆 model
 *
 * <AUTHOR>
 * @date ：Created in 2022/7/14
 */

@Data
public class SaveOrModifyCarrierVehicleRequestModel {

	@ApiModelProperty(value = "车牌号")
	private String vehicleNo;

	@ApiModelProperty(value = "车辆类型")
	private Long vehicleType;

	@ApiModelProperty("装载量（可装载托盘数）")
	private Integer loadingCapacity;
}
