<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>
    <classPathEntry
            location="C:\.m2\mysql\mysql-connector-java\5.1.35\mysql-connector-java-5.1.35.jar"/>
    <!--            location="D:\soft\repos\mysql\mysql-connector-java\5.1.35\mysql-connector-java-5.1.35.jar"/>-->

    <context id="context1">

        <!-- 自定义插件-增加实体类注释、mapper继承 -->
        <plugin type="com.yelo.tray.core.plugin.LombokPlugin">
            <!-- 是否生成批量插入的方法和sql true:是；false:否（为false时可以去掉此引用） -->
            <property name="addBatchInsertMethodFlag" value="false"/>

            <!-- 是否生成批量修改的方法和sql true:是；false:否（为false时可以去掉此引用） -->
            <property name="addBatchUpdateMethodFlag" value="false"/>
        </plugin>

        <commentGenerator>
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*******************************************************"
                        userId="dev"
                        password="123456">
        </jdbcConnection>

        <!-- 默认false，把JDBC DECIMAL 和 NUMERIC 类型解析为 Integer true，把JDBC DECIMAL 和
            NUMERIC 类型解析为java.math.BigDecimal -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>


        <!-- 表結構對應實體存放目录 -->
        <javaModelGenerator targetPackage="com.logistics.tms.entity"
                            targetProject="src/main/java"/>

        <!-- mapper xml文件存放目录 -->
        <sqlMapGenerator targetPackage="mapper"
                         targetProject="src/main/resources"/>

        <!--&lt;!&ndash; mapper java 接口存放目录 &ndash;&gt;-->
       <javaClientGenerator targetPackage="com.logistics.tms.mapper"
                             targetProject="src/main/java" type="XMLMAPPER"/>

        <table tableName="t_ext_demand_order_relation" domainObjectName="TExtDemandOrderRelation"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>
<!--        <table tableName="t_reservation_order_item" domainObjectName="TReservationOrderItem"-->
<!--               enableCountByExample="false" enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false" enableSelectByExample="false"-->
<!--               selectByExampleQueryId="false">-->
<!--        </table>-->
    </context>
</generatorConfiguration>
