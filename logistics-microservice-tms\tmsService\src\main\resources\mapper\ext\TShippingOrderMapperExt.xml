<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TShippingOrderMapper" >
  <select id="selectBatchIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_shipping_order
    where valid = 1
    and id in
    <foreach collection="params" item="item" open="(" close=")" separator=",">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>

<select id="searchList" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from t_shipping_order
    where valid = 1
    <if test="param1.shippingOrderCode != null and param1.shippingOrderCode != ''">
        and instr(shipping_order_code,#{param1.shippingOrderCode,jdbcType=VARCHAR})
    </if>
    <if test="param1.status != null">
        and status = #{param1.status,jdbcType=INTEGER}
    </if>
    <if test="param1.createdTimeStart != null and param1.createdTimeStart != ''">
      and created_time &gt;= DATE_FORMAT(#{param1.createdTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
    </if>
    <if test="param1.createdTimeEnd != null and param1.createdTimeEnd != ''">
      and created_time &lt;= DATE_FORMAT(#{param1.createdTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>

    <if test="dispatchOrderIds != null and dispatchOrderIds.size() != 0">
        and dispatch_order_id in
        <foreach collection="dispatchOrderIds" open="(" close=")" separator="," item="item">
            #{item,jdbcType=BIGINT}
        </foreach>
    </if>

    <if test="conditionShippingOrderIds != null and conditionShippingOrderIds.size() != 0">
        and id in
        <foreach collection="conditionShippingOrderIds" open="(" close=")" separator="," item="item">
            #{item,jdbcType=BIGINT}
        </foreach>
    </if>
    ORDER BY last_modified_time desc, id desc

</select>


<select id="selectWaitAuditCount" resultType="java.lang.Integer">
    select
        count(*)
    from t_shipping_order
    where valid = 1 and status = 0
</select>

    <select id="searchCanJoinShippingOrder" resultType="com.logistics.tms.controller.dispatch.response.SearchCanJoinShippingOrderRespModel">
        select
        tso.id shippingOrderId,
        tso.shipping_order_code shippingOrderCode,
        tdo.vehicle_no vehicleNo,
        tso.cross_point_fee crossPointFee,
        tso.carrier_freight carrierFreight,
        tsoi.demand_order_id demandOrderId,
        tsoi.unload_province_name unloadProvinceName,
        tsoi.unload_city_name unloadCityName,
        tsoi.unload_area_name unloadAreaName
        from t_shipping_order tso
        join t_dispatch_order tdo on tdo.id = tso.dispatch_order_id and tdo.valid = 1
        join t_shipping_order_item tsoi on tso.id = tsoi.shipping_order_id and tsoi.valid = 1
        where tso.valid = 1
        and tso.status in (0,2)
        <if test="param1.vehicleNo != null and param1.vehicleNo != ''">
              and instr(tdo.vehicle_no,#{param1.vehicleNo,jdbcType = VARCHAR}) > 0
        </if>
        <if test="param1.shippingOrderCode != null and param1.shippingOrderCode != ''">
              and instr(tso.shipping_order_code,#{param1.shippingOrderCode,jdbcType = VARCHAR}) > 0
        </if>
        group by tso.id
        order by tso.id desc
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_shipping_order
    where valid = 1
    <if test="param1.shippingOrderCodes != null and param1.shippingOrderCodes.size() != 0">
        and shipping_order_code in
        <foreach collection="param1.shippingOrderCodes" open="(" close=")" item="item" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </if>
</select>

</mapper>