package com.logistics.tms.base.enums;

/**
 * 新生订单状态枚举
 */
public enum RenewableAuditStatusEnum {

    WAIT_ASSIGN(0,"待指派"),
    WAIT_CONFIRM(1,"待确认"),
    WAIT_AUDIT(2,"待审核"),
    AUDIT_THROUGH(3,"已审核"),
    HAS_CANCELED(4,"已取消");

    private Integer key;
    private String value;

    RenewableAuditStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static RenewableAuditStatusEnum getEnum(Integer key) {
        for (RenewableAuditStatusEnum t : values()) {
            if (t.getKey() .equals(key)) {
                return t;
            }
        }
        return WAIT_ASSIGN;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
