package com.logistics.management.webapi.api.feign.loanrecord;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.loanrecord.dto.*;
import com.logistics.management.webapi.api.feign.loanrecord.hystrix.LoanRecordApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;


/**
 * @Author: sj
 * @Date: 2019/9/30 9:34
 */
@Api(value = "API-LoanRecordApi-贷款记录")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = LoanRecordApiHystrix.class)
public interface LoanRecordApi {

    @ApiOperation("查询贷款记录列表")
    @PostMapping(value = "/api/loanRecord/searchList")
    Result<PageInfo<LoanRecordListResponseDto>> searchList(@RequestBody LoanRecordListRequestDto requestDto);

    @ApiOperation("获取列表汇总数据")
    @PostMapping(value = "/api/loanRecord/getSummary")
    Result<SummaryLoanRecordResponseDto> getSummary(@RequestBody LoanRecordListRequestDto requestDto);

    @ApiOperation("新增/修改")
    @PostMapping(value = "/api/loanRecord/saveOrUpdate")
    Result<Boolean> saveOrUpdate(@RequestBody @Valid SaveOrUpdateLoanRecordRequestDto recordRequestDto);

    @ApiOperation("详情")
    @PostMapping(value = "/api/loanRecord/getDetail")
    Result<LoanRecordDetailResponseDto> getDetail(@RequestBody @Valid LoanRecordDetailRequestDto requestDto);

    @ApiOperation("查询操作记录")
    @PostMapping(value = "/api/loanRecord/getOperationRecords")
    Result<List<LoanOperationRecordResponseDto>> getOperationRecords(@RequestBody LoanOperationRecordRequestDto requestDto);

    @ApiOperation("查询结算记录")
    @PostMapping(value = "/api/loanRecord/getSettlementRecords")
    Result<List<LoanSettlementRecordResponseDto>> getSettlementRecords(@RequestBody LoanSettlementRecordRequestDto requestDto);

    @ApiOperation(value = "导出贷款记录")
    @GetMapping(value = "/api/loanRecord/exportLoanRecords")
    void exportLoanRecords(LoanRecordListRequestDto requestDto, HttpServletResponse response);

    @ApiOperation(value = "导出结算记录")
    @GetMapping(value = "/api/loanRecord/exportSettlementRecords")
    void exportSettlementRecords(LoanSettlementRecordRequestDto requestDto, HttpServletResponse response);

    @ApiOperation(value = "导出操作记录")
    @GetMapping(value = "/api/loanRecord/exportOperationRecords")
    void exportOperationRecords(LoanOperationRecordRequestDto requestDto, HttpServletResponse response);

}
