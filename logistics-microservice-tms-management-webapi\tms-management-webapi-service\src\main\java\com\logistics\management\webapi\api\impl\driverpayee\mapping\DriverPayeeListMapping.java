package com.logistics.management.webapi.api.impl.driverpayee.mapping;

import com.logistics.management.webapi.api.feign.driverpayee.dto.DriverPayeeListResponseDto;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.tms.api.feign.driverpayee.model.DriverPayeeListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;


public class DriverPayeeListMapping extends MapperMapping<DriverPayeeListResponseModel, DriverPayeeListResponseDto> {

    private ConfigKeyConstant configKeyConstant;
    private Map<String,String> imageMap;

    public DriverPayeeListMapping(ConfigKeyConstant configKeyConstant,Map<String,String> imageMap) {
        this.configKeyConstant = configKeyConstant;
        this.imageMap = imageMap;
    }

    @Override
    public void configure() {
        DriverPayeeListResponseModel source = getSource();
        DriverPayeeListResponseDto destination = getDestination();
        if (source != null && ListUtils.isNotEmpty(destination.getImageList())) {
            destination.getImageList().stream().forEach(dto -> {
                        if (StringUtils.isNotBlank(dto.getFilePath()))
                            dto.setFilePathSrc(configKeyConstant.fileAccessAddress + imageMap.get(dto.getFilePath()));
                    }
            );
        }
    }
}
