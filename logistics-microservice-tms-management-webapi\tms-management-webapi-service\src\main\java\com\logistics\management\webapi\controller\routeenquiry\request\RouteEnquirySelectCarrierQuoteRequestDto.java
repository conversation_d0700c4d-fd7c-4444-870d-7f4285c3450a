package com.logistics.management.webapi.controller.routeenquiry.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @author: wjf
 * @date: 2024/7/9 10:18
 */
@Data
public class RouteEnquirySelectCarrierQuoteRequestDto {

    /**
     * 路线询价单车主表id
     */
    @NotBlank(message = "请选择报价的车主")
    private String routeEnquiryCompanyId;

    /**
     * 关联合同号
     */
    @NotBlank(message = "关联合同号不能为空")
    @Size(max = 50, message = "关联合同号最多50字")
    private String contractCode;

    /**
     * 备注
     */
    @Size(max = 300, message = "备注最多300字")
    private String remark;

}
