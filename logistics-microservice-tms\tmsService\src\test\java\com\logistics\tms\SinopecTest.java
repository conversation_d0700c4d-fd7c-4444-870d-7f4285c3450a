package com.logistics.tms;

import com.alibaba.fastjson.JSONObject;
import com.logistics.tms.biz.sinopec.SinopecCommon;
import com.pcitc.paas.signature.SignatureClient;
import com.yelo.tools.utils.HttpClientUtils;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/20
 */
public class SinopecTest {

	//拒绝委托
	private String url1 = "https://api.el.sinopec.com/portal/receive/tms/refusedEntrust";
	//接单集成
	private String url2 = "https://api.el.sinopec.com/portal/receive/tms/OrderReceiv";

	private String ak = "ab432bb437b942e2";
	private String sk = "890c6fc4443f4a3c9a36d0f8442e69fc";

	@Test
	public void 拒绝委托测试() throws Exception {
		Map<String, String> signatureMap = SignatureClient.hmacSignatureSha256(ak, sk, "");
		signatureMap.remove("Content-Type");
		String s = HttpClientUtils.requestWithJsonWithHeadersUsingPost(url1, (HashMap<String, String>) signatureMap, refuseConsignOrderV1());
		System.out.println(s);
	}

	@Test
	public void 报价测试() throws Exception {
		Map<String, String> signatureMap = SignatureClient.hmacSignatureSha256(ak, sk, "");
		signatureMap.remove("Content-Type");
		String s = HttpClientUtils.requestWithJsonWithHeadersUsingPost(url2, (HashMap<String, String>) signatureMap, orderQuotationV1());
		System.out.println(s);
	}

	/**
	 * 接单报价V1
	 *
	 * @return
	 */
	public String orderQuotationV1() {
		JSONObject params = new JSONObject();
		params.put(SinopecCommon.CUSTOMER_ID, 1034);
		params.put(SinopecCommon.CUSTOMER_NAME, "化销");
		params.put(SinopecCommon.BUSINESS_ORDER_NUMBER, "2611693898");
		params.put(SinopecCommon.ASSOCIATED_NUMBER, "YS2614840362AB");
		params.put(SinopecCommon.TRANS_UNIT_PRICE, "98");
		params.put(SinopecCommon.DISPATCHER, "乐橘测试");
		params.put(SinopecCommon.DISPATCHER_PHONE_NO, "1234556666");
		return params.toJSONString();
	}

	/**
	 * 拒绝委托V1
	 *
	 * @return
	 */
	public String refuseConsignOrderV1() {
		JSONObject params = new JSONObject();
		params.put(SinopecCommon.CUSTOMER_ID, "1034");
		params.put(SinopecCommon.CUSTOMER_NAME, "化销");
		params.put(SinopecCommon.ASSOCIATED_NUMBER, "YS2614840362AB");
		params.put(SinopecCommon.REFUSE_REASON, "托运人原因");
		params.put(SinopecCommon.REFUSE_REASON_TYPE, 2);
		params.put(SinopecCommon.CONTACT, "乐橘测试");
		params.put(SinopecCommon.PHONE_NO, "***********");
		return params.toJSONString();
	}
}
