package com.logistics.tms.client.feign.warehouse.lift;

import com.logistics.tms.client.feign.FeignClientName;
import com.logistics.tms.client.feign.warehouse.lift.hystrix.WarehouseLifeServiceApiHystrix;
import com.logistics.tms.client.feign.warehouse.lift.reponse.GetWarehouseDetailForLifeResponseModel;
import com.logistics.tms.client.feign.warehouse.lift.request.GetWarehouseDetailForLifeRequestModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/4 16:33
 */
@FeignClient(name = FeignClientName.WAREHOUSE_LIFE_SERVICES, fallback = WarehouseLifeServiceApiHystrix.class)
public interface WarehouseLifeServiceApi {

    @ApiOperation("提供新生直调方法(新生v1.0.1)")
    @PostMapping({"/api/lifeStock/getWarehouseDetailForLife"})
    Result<List<GetWarehouseDetailForLifeResponseModel>> getWarehouseDetailForLife(@RequestBody @Valid GetWarehouseDetailForLifeRequestModel requestModel);

}
