package com.logistics.appapi.client.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderTicketModel {

	@ApiModelProperty("单据ID")
	private Long ticketId;

	@ApiModelProperty("单据类型: 1:现场图片 2:确认单据")
	private Integer ticketType;

	@ApiModelProperty("图片绝对路径")
	private String src;

	@ApiModelProperty("图片相对路径")
	private String relativePath;
}
