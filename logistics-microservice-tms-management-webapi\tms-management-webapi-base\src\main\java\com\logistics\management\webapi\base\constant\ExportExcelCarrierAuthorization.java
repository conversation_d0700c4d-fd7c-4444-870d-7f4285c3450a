package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;


public class ExportExcelCarrierAuthorization {
    private ExportExcelCarrierAuthorization() {
    }

    private static final Map<String, String> EXPORT_EXCEL_CARRIER_AUTHORIZATION;

    static {
        EXPORT_EXCEL_CARRIER_AUTHORIZATION = new LinkedHashMap<>();
        EXPORT_EXCEL_CARRIER_AUTHORIZATION.put("状态", "authorizationStatusLabel");
        EXPORT_EXCEL_CARRIER_AUTHORIZATION.put("类型", "companyCarrierTypeLabel");
        EXPORT_EXCEL_CARRIER_AUTHORIZATION.put("车主", "companyCarrierName");
        EXPORT_EXCEL_CARRIER_AUTHORIZATION.put("归档", "isArchivedLabel");
        EXPORT_EXCEL_CARRIER_AUTHORIZATION.put("创建人", "createdBy");
        EXPORT_EXCEL_CARRIER_AUTHORIZATION.put("创建时间", "createdTime");
        EXPORT_EXCEL_CARRIER_AUTHORIZATION.put("归档人", "archivedUsername");
        EXPORT_EXCEL_CARRIER_AUTHORIZATION.put("归档时间", "archivedTime");
    }

    public static Map<String, String> getExportExcelCarrierAuthorization() {
        return EXPORT_EXCEL_CARRIER_AUTHORIZATION;
    }
}
