package com.logistics.appapi.configuration;

import com.yelo.tray.core.handler.GlobalExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CarrierConfiguration {
    /**
     * 全局异常处理
     *
     * @return
     */
    @Bean
    public GlobalExceptionHandler getGlobalExceptionHandler() {
        return new GlobalExceptionHandler();
    }


    /**
     * 全局异常处理2
     *
     * @return
     */
    @Bean
    public TmsGlobalExceptionHandler getGlobalExceptionHandler2() {
        return new TmsGlobalExceptionHandler();
    }

}
