package com.logistics.management.webapi.controller.oilfilled.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ImportOilFilledCardListRequestDto {

    @ApiModelProperty(value = "副卡卡号")
    private String subCardNumber;
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    @ApiModelProperty(value = "司机")
    private String driverName;
    @ApiModelProperty(value = "司机手机号")
    private String driverPhone;
    @ApiModelProperty(value = "充值金额")
    private String applyAmount;
    @ApiModelProperty(value = "充值时间")
    private String oilFilledDate;
    @ApiModelProperty(value = "备注")
    private String remark;

}
