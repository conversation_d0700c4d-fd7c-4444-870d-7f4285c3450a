package com.logistics.management.webapi.api.impl.traycost.mapping;

import com.logistics.management.webapi.api.feign.traycost.dto.SearchCostRecordsListResponseDto;
import com.logistics.management.webapi.base.enums.EntrustTypeEnum;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.tms.api.feign.traycost.model.SearchCostRecordsListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * <AUTHOR>
 * @date 2020/4/21 13:20
 */
public class SearchCostRecordsListMapping extends MapperMapping<SearchCostRecordsListResponseModel, SearchCostRecordsListResponseDto> {
    @Override
    public void configure() {
        SearchCostRecordsListResponseModel source = getSource();
        SearchCostRecordsListResponseDto destination = getDestination();

        destination.setEntrustType(EntrustTypeEnum.getEnum(source.getEntrustType()).getValue());
        destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
        if (source.getStartTime() != null){
            destination.setStartTime(DateUtils.dateToString(source.getStartTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
    }
}
