package com.logistics.tms.base.enums;

/**
 * @Author: sj
 * @Date: 2019/5/6 17:21
 */
public enum CompanyQualifiedTypeEnum {
    COMPANY(1, "企业"),
    PERSONAL(2, "个人"),
            ;
    private Integer key;
    private String value;

    CompanyQualifiedTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
