package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/1/4 14:25
 */
@Data
public class WaitCorrectStatisticsDto {
    @ApiModelProperty("发货省份")
    private String loadProvinceName="";
    @ApiModelProperty("发货市")
    private String loadCityName="";
    @ApiModelProperty("负责人")
    private String loadRegionContactName="";
    @ApiModelProperty("下单数（单数）")
    private String carrierOrderCount="";
    @ApiModelProperty("百分比")
    private String proportion = "";
}
