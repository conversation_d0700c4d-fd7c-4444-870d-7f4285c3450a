package com.logistics.management.webapi.controller.freightconfig.request.address;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigAddressListRequestDto extends AbstractPageForm<CarrierFreightConfigAddressListRequestDto> {

    @ApiModelProperty(value = "运价配置Id", required = true)
    @NotBlank(message = "运价配置Id不允许为空")
    private String freightConfigId;

    @ApiModelProperty(value = "运价状态; 0 禁用 1 启用")
    private String enabled;

    @ApiModelProperty(value = "发货地")
    private String loadAddress;

    @ApiModelProperty(value = "收货地")
    private String unloadAddress;

    @ApiModelProperty("选择导出ids")
    private String freightConfigAddressIds;
}
