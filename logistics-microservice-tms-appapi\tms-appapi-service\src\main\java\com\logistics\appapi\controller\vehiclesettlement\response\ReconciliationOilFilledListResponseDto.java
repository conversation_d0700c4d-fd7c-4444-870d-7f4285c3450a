package com.logistics.appapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/4/9 16:33
 */
@Data
public class ReconciliationOilFilledListResponseDto {
    @ApiModelProperty(value = "充值时间")
    private String oilFilledDate="";
    @ApiModelProperty(value = "充油方式")
    private String oilFilledType="";
    @ApiModelProperty(value = "副卡卡号")
    private String subCardNumber="";
    @ApiModelProperty(value = "充值积分")
    private String topUpIntegral="";
    @ApiModelProperty(value = "奖励积分")
    private String rewardIntegral="";
    @ApiModelProperty(value = "合作公司")
    private String cooperationCompany="";
    @ApiModelProperty(value = "升数")
    private String liter="";
    @ApiModelProperty(value = "充油金额")
    private String oilFilledFee="";
}
