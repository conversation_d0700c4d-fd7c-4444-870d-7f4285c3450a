package com.logistics.management.webapi.api.feign.gpsfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * @author: wjf
 * @date: 2019/10/9 9:05
 */
@Data
public class TerminationGpsFeeRequestDto {
    @NotBlank(message = "id不能为空")
    private String gpsFeeId;
    @ApiModelProperty("终止时间")
    @Pattern(regexp = "[\\d]{4}-[\\d]{1,2}-[\\d]{1,2}",message = "请维护正确的终止时间")
    private String finishDate;
    @ApiModelProperty("备注")
    @Size(min = 1,max = 300,message = "备注不能为空，300字以内")
    private String remark;
}
