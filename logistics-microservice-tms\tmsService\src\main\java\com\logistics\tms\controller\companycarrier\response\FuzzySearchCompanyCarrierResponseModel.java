package com.logistics.tms.controller.companycarrier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/9/27 17:55
 */
@Data
public class FuzzySearchCompanyCarrierResponseModel {

    @ApiModelProperty("车主公司ID")
    private Long companyId;

    @ApiModelProperty("车主类型 1公司 2 个人")
    private Integer companyType;

    @ApiModelProperty("车主公司名称")
    private String companyName;

    @ApiModelProperty("车主联系人id")
    private Long contactId;

    @ApiModelProperty("车主联系人姓名")
    private String contactName;

    @ApiModelProperty("车主联系人手机号")
    private String contactPhone;

    @ApiModelProperty("车主联系人身份证号")
    private String identityNumber;

    @ApiModelProperty("是否我司: 1:我司 2:其他车主")
    private Integer isOurCompany;

    @ApiModelProperty("授权状态(企业): 0 待授权 1 待审核 2已驳回 3 已授权")
    private Integer authorizationStatus;

    @ApiModelProperty("实名认证(个人): 0 待实名 1 实名中 2 已实名")
    private Integer realNameAuthenticationStatus;

    @ApiModelProperty("身份证正面")
    private String identityFaceFile;

    @ApiModelProperty("身份证反面")
    private String identityNationalFile;

    /**
     * 是否加入黑名单：0 否，1 是
     */
    @ApiModelProperty("是否加入黑名单：0 否，1 是")
    private Integer ifAddBlacklist;
}
