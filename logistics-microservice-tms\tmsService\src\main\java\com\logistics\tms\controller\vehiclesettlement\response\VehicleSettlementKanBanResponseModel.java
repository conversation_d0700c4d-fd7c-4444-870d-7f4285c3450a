package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/12 19:59
 */
@Data
public class VehicleSettlementKanBanResponseModel {
    @ApiModelProperty("年月")
    private String settlementMonth;
    @ApiModelProperty("月份")
    private String month;
    @ApiModelProperty("车辆数")
    private Integer vehicleCount;
    @ApiModelProperty("待对账数")
    private Integer waitSettlementCount;
    @ApiModelProperty("待发送数")
    private Integer waitSendCount;
    @ApiModelProperty("待确认数")
    private Integer waitCommitCount;
    @ApiModelProperty("待处理数")
    private Integer waitHandleCount;
    @ApiModelProperty("待结清数")
    private Integer waitPaySettlementCount;
    @ApiModelProperty("部分结清数")
    private Integer partPaySettlementCount;
    @ApiModelProperty("已结清数")
    private Integer payedSettlementCount;

    private List<VehicleSettlementKanBanModel> vehicleSettlementList;
}
