package com.logistics.tms.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RenewableTicketsUrlResponseModel {

    @ApiModelProperty(value = "票据表id")
    private Long certificationPicturesId;

    @ApiModelProperty(value = "文件类型名称")
    private Integer fileType;

    @ApiModelProperty(value = "文件类型名称")
    private String fileTypeName;

    @ApiModelProperty(value = "图片地址")
    private String filePath;

}
