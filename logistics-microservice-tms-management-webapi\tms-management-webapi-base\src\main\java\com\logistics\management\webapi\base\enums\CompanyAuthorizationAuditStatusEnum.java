package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CompanyAuthorizationAuditStatusEnum {

    DEFAULT(-1, ""),
    AWAIT_AUTHORIZATION(0, "待授权"),
    AWAIT_AUDIT(1, "待审核"),
    OVERRULE(2, "已驳回"),
    ALREADY_AUTHORIZATION(3, "已授权"),

    ;

    private Integer key;
    private String value;

    public static CompanyAuthorizationAuditStatusEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
