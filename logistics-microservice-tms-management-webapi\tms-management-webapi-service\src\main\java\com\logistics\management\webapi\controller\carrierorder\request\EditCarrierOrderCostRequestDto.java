package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/1
 */
@Data
public class EditCarrierOrderCostRequestDto {

	@ApiModelProperty(value = "运单ID", required = true)
	@NotBlank(message = "运单id不能为空")
	private String carrierOrderId;

	@ApiModelProperty(value = "费用类型 1:货主费用 2:车主费用", required = true)
	@NotBlank(message = "请选择要编辑的费用类型")
	@Range(min = 1, max = 2, message = "费用类型错误")
	private String costType;

	@ApiModelProperty(value = "价格类型 1 单价 2 一口价", required = true)
	@NotBlank(message = "请选择单价或一口价")
	@Range(min = 1, max = 2, message = "请选择单价或一口价")
	private String priceType;

	@ApiModelProperty(value = "价格", required = true)
	@NotBlank(message = "请输入价格")
	@DecimalMin(value = "0.00", inclusive = false, message = "价格仅数字，最低价格大于0且最多支持2位小数")
	private String price;
}
