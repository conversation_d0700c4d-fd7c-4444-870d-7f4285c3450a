package com.logistics.tms.biz.demandorderobjectionsinopec;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.context.BaseContextHandler;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.DemandOrderCommonBiz;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2022/5/30 15:29
 */
@Service
public class DemandOrderObjectionSinopecBiz {

    @Autowired
    private TDemandOrderObjectionSinopecMapper tDemandOrderObjectionSinopecMapper;
    @Autowired
    private TCertificationPicturesMapper tCertificationPicturesMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TDemandOrderMapper tDemandOrderMapper;
    @Autowired
    private TDemandOrderAddressMapper tDemandOrderAddressMapper;
    @Autowired
    private TDemandOrderOperateLogsMapper tDemandOrderOperateLogsMapper;
    @Autowired
    private TDemandOrderEventsMapper tDemandOrderEventsMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private DemandOrderCommonBiz demandOrderCommonBiz;

    /**
     * 中石化需求单异常列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchDemandOrderObjectionSinopecResponseModel> searchSinopecObjection(SearchDemandOrderObjectionSinopecRequestModel requestModel) {
        //开启分页
        requestModel.enablePaging();
        List<SearchDemandOrderObjectionSinopecResponseModel> list = tDemandOrderObjectionSinopecMapper.searchSinopecObjection(requestModel);
        if (ListUtils.isNotEmpty(list)){
            List<Long> objectionIdList = list.stream().map(SearchDemandOrderObjectionSinopecResponseModel::getDemandOrderObjectionId).collect(Collectors.toList());

            //查询审核依据图片路径
            List<TCertificationPictures> objectionTicketsList = tCertificationPicturesMapper.getTPicsByIds(StringUtils.listToString(objectionIdList,','), CertificationPicturesObjectTypeEnum.T_DEMAND_ORDER_OBJECTION_SINOPEC.getObjectType().toString());
            //以id进行聚合
            Map<Long, List<TCertificationPictures>> objectionTicketsMap = new HashMap<>();
            if (ListUtils.isNotEmpty(objectionTicketsList)){
                objectionTicketsMap = objectionTicketsList.stream().collect(Collectors.groupingBy(TCertificationPictures::getObjectId));
            }

            //拼接数据
            List<TCertificationPictures> picturesList;
            List<String> auditTicketList;
            for (SearchDemandOrderObjectionSinopecResponseModel model : list) {
                picturesList = objectionTicketsMap.get(model.getDemandOrderObjectionId());
                if (ListUtils.isNotEmpty(picturesList)){
                    auditTicketList = new ArrayList<>();
                    for (TCertificationPictures pictures : picturesList) {
                        auditTicketList.add(pictures.getFilePath());
                    }
                    model.setAuditTicketList(auditTicketList);
                }
            }
        }
        return new PageInfo<>(list);
    }

    /**
     * 中石化需求单异常详情
     * @param requestModel
     * @return
     */
    public GetSinopecObjectionDetailResponseModel getSinopecObjectionDetail(GetSinopecObjectionDetailRequestModel requestModel) {
        GetSinopecObjectionDetailResponseModel detail = tDemandOrderObjectionSinopecMapper.getSinopecObjectionDetail(requestModel.getDemandOrderObjectionId());
        if (detail == null){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_OBJECTION_INFO_EMPTY);
        }
        //查询审核依据图片路径
        List<TCertificationPictures> objectionTicketsList = tCertificationPicturesMapper.getTPicsByIds(detail.getDemandOrderObjectionId().toString(), CertificationPicturesObjectTypeEnum.T_DEMAND_ORDER_OBJECTION_SINOPEC.getObjectType().toString());
        if (ListUtils.isNotEmpty(objectionTicketsList)){
            List<String> auditTicketList = objectionTicketsList.stream().map(TCertificationPictures::getFilePath).collect(Collectors.toList());
            detail.setAuditTicketList(auditTicketList);
        }
        return detail;
    }

    /**
     * 中石化需求单异常审核
     * @param requestModel
     * @return
     */
    @Transactional
    public void sinopecObjectionAudit(SinopecObjectionAuditRequestModel requestModel) {
        //查询异常数据
        TDemandOrderObjectionSinopec dbDemandOrderObjectionSinopec = tDemandOrderObjectionSinopecMapper.selectByPrimaryKey(requestModel.getDemandOrderObjectionId());
        if (dbDemandOrderObjectionSinopec == null || dbDemandOrderObjectionSinopec.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_OBJECTION_INFO_EMPTY);
        }
        //判断是否是待审核状态
        if (!dbDemandOrderObjectionSinopec.getAuditStatus().equals(AuditStatusEnum.WAIT_AUDIT.getKey())){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_APPROVAL_STATUS_ERROR);
        }
        //查询需求单
        TDemandOrder dbDemandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(dbDemandOrderObjectionSinopec.getDemandOrderId());
        if (dbDemandOrder == null || dbDemandOrder.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        TDemandOrderAddress dbDemandOrderAddress = tDemandOrderAddressMapper.getByDemandOrderId(dbDemandOrder.getId());

        //云途公司id、公司名称
        Long companyCarrierId = null;
        String companyCarrierName = null;
        Integer companyCarrierLevel = null;

        //审核通过
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getAuditStatus())){
            //已报价
            if (CommonConstant.INTEGER_ONE.equals(requestModel.getAuditObjectionType())){
                //只能是待发布的
                if (!DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(dbDemandOrder.getEntrustStatus())) {
                    throw new BizException(EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR);
                }

                companyCarrierName = commonBiz.getQiyaCompanyName();
                TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.getByName(companyCarrierName);
                if (tCompanyCarrier != null) {
                    companyCarrierId = tCompanyCarrier.getId();
                    companyCarrierLevel = tCompanyCarrier.getLevel();
                }
            }else{//已取消
                //已经调度了
                if (dbDemandOrder.getEntrustStatus() > DemandOrderStatusEnum.WAIT_DISPATCH.getKey()) {
                    throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_NOT_CANCEL);
                }
            }
        }

        String userName = BaseContextHandler.getUserName();
        Date now = new Date();

        //更新审核数据
        TDemandOrderObjectionSinopec tDemandOrderObjectionSinopec = new TDemandOrderObjectionSinopec();
        tDemandOrderObjectionSinopec.setId(dbDemandOrderObjectionSinopec.getId());
        tDemandOrderObjectionSinopec.setAuditStatus(requestModel.getAuditStatus());
        tDemandOrderObjectionSinopec.setAuditObjectionType(requestModel.getAuditObjectionType());
        tDemandOrderObjectionSinopec.setAuditRemark(requestModel.getAuditRemark());
        tDemandOrderObjectionSinopec.setAuditorName(userName);
        tDemandOrderObjectionSinopec.setAuditTime(now);
        commonBiz.setBaseEntityModify(tDemandOrderObjectionSinopec, userName);
        tDemandOrderObjectionSinopecMapper.updateByPrimaryKeySelective(tDemandOrderObjectionSinopec);

        //新增审核依据
        CertificationPicturesFileTypeEnum fileTypeEnum = CertificationPicturesFileTypeEnum.T_DEMAND_ORDER_OBJECTION_SINOPEC_IMAGE_FILE;
        TCertificationPictures tCertificationPictures;
        List<TCertificationPictures> addPicturesList = new ArrayList<>();

        for (String path : requestModel.getAuditTicketList()) {
            tCertificationPictures = new TCertificationPictures();
            tCertificationPictures.setObjectType(fileTypeEnum.getObjectType().getObjectType());
            tCertificationPictures.setObjectId(dbDemandOrderObjectionSinopec.getId());
            tCertificationPictures.setFileType(fileTypeEnum.getFileType());
            tCertificationPictures.setFileTypeName(fileTypeEnum.getFileName());
            tCertificationPictures.setFileName(fileTypeEnum.getFileName());
            tCertificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DEMAND_ORDER_OBJECTION_SINOPEC.getKey(), dbDemandOrder.getDemandOrderCode(), path, null));
            tCertificationPictures.setUploadUserName(userName);
            tCertificationPictures.setUploadTime(now);
            tCertificationPictures.setSuffix(path.substring(path.lastIndexOf('.')));
            commonBiz.setBaseEntityAdd(tCertificationPictures, userName);
            addPicturesList.add(tCertificationPictures);
        }
        tCertificationPicturesMapper.batchInsert(addPicturesList);

        //审核通过
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getAuditStatus())){
            //更新需求单表信息
            TDemandOrder tDemandOrder = new TDemandOrder();
            tDemandOrder.setId(dbDemandOrder.getId());
            tDemandOrder.setCompanyCarrierId(companyCarrierId);
            tDemandOrder.setCompanyCarrierName(companyCarrierName);
            tDemandOrder.setCompanyCarrierLevel(companyCarrierLevel);
            tDemandOrder.setExpectContractPriceType(ContractPriceTypeEnum.UNIT_PRICE.getKey());
            tDemandOrder.setExpectContractPrice(dbDemandOrderObjectionSinopec.getContractPrice());
            tDemandOrder.setContractPriceType(ContractPriceTypeEnum.UNIT_PRICE.getKey());
            tDemandOrder.setContractPrice(dbDemandOrderObjectionSinopec.getContractPrice());
            tDemandOrder.setCarrierPriceType(ContractPriceTypeEnum.UNIT_PRICE.getKey());
            tDemandOrder.setCarrierPrice(dbDemandOrderObjectionSinopec.getContractPrice());
            tDemandOrder.setDispatcherName(dbDemandOrderObjectionSinopec.getDispatcherName());
            tDemandOrder.setDispatcherPhone(dbDemandOrderObjectionSinopec.getDispatcherPhone());
            tDemandOrder.setIfObjectionSinopec(CommonConstant.INTEGER_ZERO);
            commonBiz.setBaseEntityModify(tDemandOrder, userName);

            //更新需求单操作log
            TDemandOrderOperateLogs demandOrderOperateLogs = new TDemandOrderOperateLogs();
            demandOrderOperateLogs.setDemandOrderId(dbDemandOrder.getId());
            demandOrderOperateLogs.setOperatorName(userName);
            demandOrderOperateLogs.setOperateTime(now);
            commonBiz.setBaseEntityAdd(demandOrderOperateLogs, userName);

            //更新需求单事件
            TDemandOrderEvents demandOrderEvents = null;

            if (CommonConstant.INTEGER_ONE.equals(requestModel.getAuditObjectionType())) {
                tDemandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
                tDemandOrder.setStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());

                demandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.PUBLISH_DEMAND_ORDER.getKey());
                demandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.PUBLISH_DEMAND_ORDER.getValue());
                demandOrderOperateLogs.setRemark("【"+dbDemandOrderObjectionSinopec.getDispatcherName()+"&"+dbDemandOrderObjectionSinopec.getDispatcherPhone()+"】："+dbDemandOrderObjectionSinopec.getContractPrice());
            }else{
                tDemandOrder.setIfCancel(CommonConstant.INTEGER_ONE);
                tDemandOrder.setCancelReason(requestModel.getAuditRemark());
                tDemandOrder.setCancelTime(now);

                demandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.CANCEL_DEMAND_ORDER.getKey());
                demandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.CANCEL_DEMAND_ORDER.getValue());
                demandOrderOperateLogs.setRemark("【"+dbDemandOrderObjectionSinopec.getDispatcherName()+"&"+dbDemandOrderObjectionSinopec.getDispatcherPhone()+"】"+requestModel.getAuditRemark());

                //生成需求单事件
                demandOrderEvents = new TDemandOrderEvents();
                demandOrderEvents.setDemandOrderId(tDemandOrder.getId());
                demandOrderEvents.setCompanyCarrierId(tDemandOrder.getCompanyCarrierId());
                demandOrderEvents.setEvent(DemandOrderEventsTypeEnum.CANCEL_DEMANDORDER.getKey());
                demandOrderEvents.setEventDesc(DemandOrderEventsTypeEnum.CANCEL_DEMANDORDER.getValue());
                demandOrderEvents.setEventTime(now);
                demandOrderEvents.setOperatorName(userName);
                demandOrderEvents.setOperateTime(now);
                commonBiz.setBaseEntityAdd(demandOrderEvents, userName);
            }

            //更新需求单地址表信息
            TDemandOrderAddress tDemandOrderAddress = MapperUtils.mapper(dbDemandOrderObjectionSinopec, TDemandOrderAddress.class);
            tDemandOrderAddress.setId(dbDemandOrderAddress.getId());
            commonBiz.setBaseEntityModify(tDemandOrderAddress, userName);

            //更新数据库
            tDemandOrderMapper.updateByPrimaryKeySelectiveEncrypt(tDemandOrder);
            tDemandOrderAddressMapper.updateByPrimaryKeySelective(tDemandOrderAddress);
            tDemandOrderOperateLogsMapper.insertSelective(demandOrderOperateLogs);
            if (demandOrderEvents != null){
                tDemandOrderEventsMapper.insertSelective(demandOrderEvents);
            }

            //异步查询地址经纬度
            AsyncProcessQueue.execute(() -> demandOrderCommonBiz.updateAddressLonAndLat(Collections.singletonList(tDemandOrderAddress)));
        }else{//驳回
            //修改需求单异常标识
            TDemandOrder tDemandOrder = new TDemandOrder();
            tDemandOrder.setId(dbDemandOrder.getId());
            tDemandOrder.setIfObjectionSinopec(CommonConstant.INTEGER_ZERO);
            commonBiz.setBaseEntityModify(tDemandOrder, userName);
            tDemandOrderMapper.updateByPrimaryKeySelectiveEncrypt(tDemandOrder);
        }
    }
}
