package com.logistics.tms.biz.vehicletire

import com.logistics.tms.controller.staffvehiclerelation.response.SearchStaffVehicleListResponseModel
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel
import com.logistics.tms.api.feign.vehicletire.model.AddOrModifyVehicleTireRequestModel
import com.logistics.tms.api.feign.vehicletire.model.GetTireByTireIdsModel
import com.logistics.tms.api.feign.vehicletire.model.ImportVehicleTireInfoRequestModel
import com.logistics.tms.api.feign.vehicletire.model.ImportVehicleTireInfoResponseModel
import com.logistics.tms.api.feign.vehicletire.model.VehicleTireDetailResponseModel
import com.logistics.tms.api.feign.vehicletire.model.VehicleTireIdRequestModel
import com.logistics.tms.api.feign.vehicletire.model.VehicleTireListRequestModel
import com.logistics.tms.api.feign.vehicletire.model.VehicleTireListResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TCertificationPictures
import com.logistics.tms.entity.TStaffBasic
import com.logistics.tms.entity.TVehicleBasic
import com.logistics.tms.entity.TVehicleDrivingLicense
import com.logistics.tms.entity.TVehicleSettlementRelation
import com.logistics.tms.entity.TVehicleTireNo
import com.logistics.tms.mapper.TCertificationPicturesMapper
import com.logistics.tms.mapper.TStaffBasicMapper
import com.logistics.tms.mapper.TStaffVehicleRelationMapper
import com.logistics.tms.mapper.TVehicleBasicMapper
import com.logistics.tms.mapper.TVehicleDrivingLicenseMapper
import com.logistics.tms.mapper.TVehicleSettlementRelationMapper
import com.logistics.tms.mapper.TVehicleTireMapper
import com.logistics.tms.mapper.TVehicleTireNoMapper
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class VehicleTireBizTest extends Specification {
    @Mock
    TVehicleTireMapper tqVehicleTireMapper
    @Mock
    TCertificationPicturesMapper tqCertificationPicturesMapper
    @Mock
    TVehicleBasicMapper tqVehicleBasicMapper
    @Mock
    TVehicleTireNoMapper tqVehicleTireNoMapper
    @Mock
    TStaffBasicMapper tqStaffBasicMapper
    @Mock
    TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TStaffVehicleRelationMapper tStaffVehicleRelationMapper
    @Mock
    TVehicleDrivingLicenseMapper tVehicleDrivingLicenseMapper
    @Mock
    Logger log
    @InjectMocks
    VehicleTireBiz vehicleTireBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Vehicle Tire List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqVehicleTireMapper.searchVehicleTireIdsList(any())).thenReturn([1l])
        when(tqVehicleTireMapper.searchVehicleTireList(anyString())).thenReturn([new VehicleTireListResponseModel()])

        expect:
        vehicleTireBiz.searchVehicleTireList(requestModel) == expectedResult

        where:
        requestModel                      || expectedResult
        new VehicleTireListRequestModel() || null
    }

    @Unroll
    def "get Vehicle Tire Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqVehicleTireMapper.getVehicleTireDetailById(anyLong())).thenReturn(new VehicleTireDetailResponseModel())
        when(tVehicleSettlementRelationMapper.getByObjectIdAndType(anyInt(), anyLong())).thenReturn([new TVehicleSettlementRelation()])
        when(tVehicleDrivingLicenseMapper.getByVehicleId(anyLong())).thenReturn(new TVehicleDrivingLicense(vehicleNo: "vehicleNo"))

        expect:
        vehicleTireBiz.getVehicleTireDetail(requestModel) == expectedResult

        where:
        requestModel                    || expectedResult
        new VehicleTireIdRequestModel() || new VehicleTireDetailResponseModel()
    }

    @Unroll
    def "add Or Modify Vehicle Tire where requestModel=#requestModel"() {
        given:
        when(tqVehicleTireMapper.getByTireIdOrVehicleId(anyLong(), anyLong(), any())).thenReturn(new GetTireByTireIdsModel())
        when(tqCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 45).getTime(), suffix: "suffix")])
        when(tqCertificationPicturesMapper.batchInsert(any())).thenReturn(0)
        when(tqCertificationPicturesMapper.batchUpdate(any())).thenReturn(0)
        when(tqVehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())
        when(tqVehicleTireNoMapper.batchUpdate(any())).thenReturn(0)
        when(tqVehicleTireNoMapper.batchInsert(any())).thenReturn(0)
        when(tqVehicleTireNoMapper.getByTireId(anyLong())).thenReturn([new TVehicleTireNo(tireId: 1l, amount: 0, unitPrice: 0 as BigDecimal, tireBrand: "tireBrand")])
        when(tVehicleSettlementRelationMapper.getByObjectIdAndType(anyInt(), anyLong())).thenReturn([new TVehicleSettlementRelation()])
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(tStaffVehicleRelationMapper.searchStaffVehicleList(any())).thenReturn([new SearchStaffVehicleListResponseModel()])

        expect:
        vehicleTireBiz.addOrModifyVehicleTire(requestModel)
        assert expectedResult == false

        where:
        requestModel                             || expectedResult
        new AddOrModifyVehicleTireRequestModel() || true
    }

    @Unroll
    def "delete Vehicle Tire where requestModel=#requestModel"() {
        given:
        when(tqVehicleTireMapper.getByTireIdOrVehicleId(anyLong(), anyLong(), any())).thenReturn(new GetTireByTireIdsModel())
        when(tqVehicleTireNoMapper.batchUpdate(any())).thenReturn(0)
        when(tVehicleSettlementRelationMapper.getByObjectIdAndType(anyInt(), anyLong())).thenReturn([new TVehicleSettlementRelation()])

        expect:
        vehicleTireBiz.deleteVehicleTire(requestModel)
        assert expectedResult == false

        where:
        requestModel                    || expectedResult
        new VehicleTireIdRequestModel() || true
    }

    @Unroll
    def "import Excel Info Vehicle Tire Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tqVehicleTireMapper.getByTireIdOrVehicleId(anyLong(), anyLong(), any())).thenReturn(new GetTireByTireIdsModel())
        when(tqVehicleTireMapper.batchUpdate(any())).thenReturn(0)
        when(tqVehicleBasicMapper.getInfoByVehicleNo(anyString())).thenReturn(new TVehicleBasic(vehicleProperty: 0, operatingState: 0))
        when(tqVehicleTireNoMapper.batchUpdate(any())).thenReturn(0)
        when(tqVehicleTireNoMapper.batchInsert(any())).thenReturn(0)
        when(tqStaffBasicMapper.getByMobile(anyString())).thenReturn(new TStaffBasic(type: 0, staffProperty: 0))

        expect:
        vehicleTireBiz.importExcelInfoVehicleTireInfo(requestModel) == expectedResult

        where:
        requestModel                            || expectedResult
        new ImportVehicleTireInfoRequestModel() || new ImportVehicleTireInfoResponseModel()
    }

    @Unroll
    def "import Vehicle Tire Certificate Info where requestModel=#requestModel"() {
        given:
        when(tqVehicleTireMapper.getByTireIdOrVehicleId(anyLong(), anyLong(), any())).thenReturn(new GetTireByTireIdsModel())
        when(tqCertificationPicturesMapper.getByObjectIdType(anyLong(), anyInt(), anyInt())).thenReturn([new TCertificationPictures(objectType: 0, objectId: 1l, fileType: 0, fileTypeName: "fileTypeName", filePath: "filePath", uploadUserName: "uploadUserName", uploadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 16, 45).getTime(), suffix: "suffix")])
        when(tqVehicleBasicMapper.getInfoByVehicleNo(anyString())).thenReturn(new TVehicleBasic())
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        vehicleTireBiz.importVehicleTireCertificateInfo(requestModel)
        assert expectedResult == false

        where:
        requestModel                                                                          || expectedResult
        new com.logistics.tms.api.feign.vehicletire.model.ImportTireCertificateRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme