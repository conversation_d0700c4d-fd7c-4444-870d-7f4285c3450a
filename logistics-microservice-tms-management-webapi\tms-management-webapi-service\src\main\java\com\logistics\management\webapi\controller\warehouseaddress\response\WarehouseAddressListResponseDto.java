package com.logistics.management.webapi.controller.warehouseaddress.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WarehouseAddressListResponseDto{
    @ApiModelProperty("状态 0 禁用 1启用")
    private String enabled;
    @ApiModelProperty("状态文本")
    private String enabledLabel;
    @ApiModelProperty("Id")
    private String warehouseAddressId;
    @ApiModelProperty("仓库")
    private String warehouse;
    @ApiModelProperty("货主")
    private String companyEntrustName;
    @ApiModelProperty("省市区")
    private String address;
    @ApiModelProperty("操作人")
    private String operateUserName;
    @ApiModelProperty("操作时间")
    private String operateTime;

}
