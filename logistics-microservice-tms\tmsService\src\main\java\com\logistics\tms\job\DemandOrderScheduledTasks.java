package com.logistics.tms.job;

import com.logistics.tms.biz.email.EmailBiz;
import com.logistics.tms.biz.email.PullFromSinopecBiz;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2022/9/5 14:59
 */
@Slf4j
@Component
public class DemandOrderScheduledTasks {

    @Autowired
    private PullFromSinopecBiz pullFromSinopecBiz;
    @Autowired
    private EmailBiz emailBiz;

    /**
     * 从中石化网拉取订单（每5分钟执行一次）
     */
    @XxlJob("logisticsTmsPullDemandOrderByRequestSinopec")
    public void pullDemandOrderByRequestSinopec(){
        try {
            log.info("tms定时任务：从中石化网拉取订单-开始");
            //中石化旧系统已关闭，所以关闭定时任务，等后续直接对接中石化接口
            //pullFromSinopecBiz.pullDemandOrderByRequestSinopec();
            log.info("tms定时任务：从中石化网拉取订单-结束");
        }catch (Exception e){
            log.error("定时任务，从中石化网拉取订单错误: ", e);
        }
    }

    /**
     * 从扬巴邮件附件里解析pdf相关内容生成需求单（每10分钟执行一次）
     */
    @XxlJob("logisticsTmsSaveDemandOrderForParseEmail")
    public void processSaveDemandOrder(){
        try {
            log.info("tms定时任务：从扬巴邮件附件里解析pdf相关内容生成需求单-开始");
            emailBiz.processSaveDemandOrder();
            log.info("tms定时任务：从扬巴邮件附件里解析pdf相关内容生成需求单-结束");
        }catch (Exception e){
            log.error("定时任务，从扬巴邮件附件里解析pdf相关内容生成需求单错误: ", e);
        }
    }

}
