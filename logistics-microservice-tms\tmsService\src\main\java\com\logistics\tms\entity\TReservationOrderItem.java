package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2024/08/29
*/
@Data
public class TReservationOrderItem extends BaseEntity {
    /**
    * 预约单id
    */
    @ApiModelProperty("预约单id")
    private Long reservationOrderId;

    /**
    * 运单id
    */
    @ApiModelProperty("运单id")
    private Long carrierOrderId;

    /**
    * 运单号
    */
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    /**
    * 运单预计数量
    */
    @ApiModelProperty("运单预计数量")
    private BigDecimal expectAmount;

    /**
    * 委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售
    */
    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售")
    private Integer demandOrderEntrustType;

    /**
    * 提卸货-预计时间
    */
    @ApiModelProperty("提卸货-预计时间")
    private Date expectedTime;

    /**
    * 0 禁用，1 启用
    */
    @ApiModelProperty("0 禁用，1 启用")
    private Integer enabled;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}