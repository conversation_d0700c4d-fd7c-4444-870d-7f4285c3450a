<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TBiddingOrderQuoteMapper" >
  <sql id="Base_Column_List_Decrypt" >
    id, bidding_order_id, quote_status, company_carrier_type, company_carrier_id, company_carrier_name, 
    carrier_contact_id, carrier_contact_name,
    AES_DECRYPT(UNHEX(carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
    quote_price_type, quote_price, vehicle_length_id, vehicle_length,quote_operator ,quote_time, created_by, created_time,
    last_modified_by, last_modified_time, valid
  </sql>

  <select id="selectByPrimaryKeyDecrypt" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List_Decrypt" />
    from t_bidding_order_quote
    where id = #{id,jdbcType=BIGINT}
    and valid = 1
  </select>

  <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TBiddingOrderQuote" >
    insert into t_bidding_order_quote
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="biddingOrderId != null" >
        bidding_order_id,
      </if>
      <if test="quoteStatus != null" >
        quote_status,
      </if>
      <if test="companyCarrierType != null" >
        company_carrier_type,
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id,
      </if>
      <if test="companyCarrierName != null" >
        company_carrier_name,
      </if>
      <if test="carrierContactId != null" >
        carrier_contact_id,
      </if>
      <if test="carrierContactName != null" >
        carrier_contact_name,
      </if>
      <if test="carrierContactPhone != null" >
        carrier_contact_phone,
      </if>
      <if test="quotePriceType != null" >
        quote_price_type,
      </if>
      <if test="quotePrice != null" >
        quote_price,
      </if>
      <if test="vehicleLengthId != null" >
        vehicle_length_id,
      </if>
      <if test="vehicleLength != null" >
        vehicle_length,
      </if>
      <if test="quoteOperator != null">
        quote_operator,
      </if>
      <if test="quoteTime != null" >
        quote_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="biddingOrderId != null" >
        #{biddingOrderId,jdbcType=BIGINT},
      </if>
      <if test="quoteStatus != null" >
        #{quoteStatus,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierType != null" >
        #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null" >
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null" >
        #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null" >
        #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null" >
        #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null" >
        HEX(AES_ENCRYPT(#{carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="quotePriceType != null" >
        #{quotePriceType,jdbcType=INTEGER},
      </if>
      <if test="quotePrice != null" >
        #{quotePrice,jdbcType=DECIMAL},
      </if>
      <if test="vehicleLengthId != null" >
        #{vehicleLengthId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null" >
        #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="quoteOperator != null">
        #{quoteOperator,jdbcType=VARCHAR},
      </if>
      <if test="quoteTime != null" >
        #{quoteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TBiddingOrderQuote" >
    update t_bidding_order_quote
    <set >
      <if test="biddingOrderId != null" >
        bidding_order_id = #{biddingOrderId,jdbcType=BIGINT},
      </if>
      <if test="quoteStatus != null" >
        quote_status = #{quoteStatus,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierType != null" >
        company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null" >
        company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null" >
        carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null" >
        carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null" >
        carrier_contact_phone = HEX(AES_ENCRYPT(#{carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="quotePriceType != null" >
        quote_price_type = #{quotePriceType,jdbcType=INTEGER},
      </if>
      <if test="quotePrice != null" >
        quote_price = #{quotePrice,jdbcType=DECIMAL},
      </if>
      <if test="vehicleLengthId != null" >
        vehicle_length_id = #{vehicleLengthId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null" >
        vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="quoteOperator != null">
        quote_operator = #{quoteOperator,jdbcType=VARCHAR},
      </if>
      <if test="quoteTime != null" >
        quote_time = #{quoteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

    <select id="selectEndCompleteQuotePrice" resultType="com.logistics.tms.biz.biddingorder.bo.QuotePriceBo">
        select tbo.id                as biddingOrderId,
               tboq.quote_price      as quotePrice,
               tboq.quote_price_type as quotePriceType

        from t_bidding_order tbo
                     inner join t_bidding_order_quote tboq
                on tbo.id = tboq.bidding_order_id and tboq.valid = 1 and tboq.quote_status = 1
                where tbo.valid = 1
                  and tbo.bidding_status = 4
        <if test="biddingOrderIds != null and biddingOrderIds.size() != 0">
            and tbo.id in
            <foreach collection="biddingOrderIds" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="selectBiddingOrderQuoteByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_bidding_order_quote tboq
                where tboq.valid = 1
        <if test="companyCarrierId != null">
            and tboq.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        </if>
        <if test="biddingOrderId != null">
            and tboq.bidding_order_id = #{biddingOrderId,jdbcType=BIGINT}
        </if>
        <if test="biddingStatuses != null and biddingStatuses.size() != 0">
            and tboq.quote_status in
            <foreach collection="biddingStatuses" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="biddingOrderIds != null and biddingOrderIds.size() != 0">
            and tboq.bidding_order_id in
            <foreach collection="biddingOrderIds" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <update id="emptyOrderQuote">
        update t_bidding_order_quote set valid = 0
        where valid = 1 and bidding_order_id = #{biddingOrderId,jdbcType=BIGINT}
    </update>

    <select id="selectEndCompleteVehicleLength" resultType="com.logistics.tms.biz.biddingorder.bo.VehicleLengthBo">
        select tboq.vehicle_length_id as vehicleLengthId,
               tboq.vehicle_length    as vehicleLength,
               tbo.id                 as biddingOrderId
        from t_bidding_order tbo
                     inner join t_bidding_order_quote tboq
                on tbo.id = tboq.bidding_order_id and tboq.valid = 1 and tboq.quote_status = 1
                where tbo.valid = 1
                  and tbo.bidding_status = 4
        <if test="vehicleLengthId != null">
            and tboq.vehicle_length_id = #{vehicleLengthId,jdbcType=BIGINT}
        </if>
        <if test="biddingOrderIds != null and biddingOrderIds.size() != 0">
            and tbo.id in
            <foreach collection="biddingOrderIds" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>
</mapper>