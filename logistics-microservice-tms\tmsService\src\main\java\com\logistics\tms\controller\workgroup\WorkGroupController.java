package com.logistics.tms.controller.workgroup;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.workgroup.WorkGroupBiz;
import com.logistics.tms.controller.workgroup.request.*;
import com.logistics.tms.controller.workgroup.response.SearchWorkGroupListResponseModel;
import com.logistics.tms.controller.workgroup.response.WorkGroupDetailResponseModel;
import com.logistics.tms.controller.workgroup.response.WorkGroupNodeResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: wjf
 * @date: 2023/12/22 15:44
 */
@RestController
@Api(value = "智能推送配置")
@RequestMapping(value = "/service/workGroup")
public class WorkGroupController {

    @Resource
    private WorkGroupBiz workGroupBiz;

    @PostMapping(value = "/searchList")
    @ApiOperation(value = "列表")
    public Result<PageInfo<SearchWorkGroupListResponseModel>> searchList(@RequestBody SearchWorkGroupListRequestModel requestModel) {
        return Result.success(workGroupBiz.searchList(requestModel));
    }

    @PostMapping(value = "/workGroupEnable")
    @ApiOperation(value = "列表-禁用/启用")
    public Result<Boolean> workGroupEnable(@RequestBody WorkGroupEnableRequestModel requestModel) {
        workGroupBiz.workGroupEnable(requestModel);
        return Result.success(true);
    }

    @PostMapping(value = "/delWorkGroup")
    @ApiOperation(value = "列表-删除")
    public Result<Boolean> delWorkGroup(@RequestBody WorkGroupIdRequestModel requestModel) {
        workGroupBiz.delWorkGroup(requestModel);
        return Result.success(true);
    }

    @PostMapping(value = "/getDetail")
    @ApiOperation(value = "推送配置详情")
    public Result<WorkGroupDetailResponseModel> getDetail(@RequestBody WorkGroupIdRequestModel requestModel) {
        return Result.success(workGroupBiz.getDetail(requestModel));
    }

    @PostMapping(value = "/addEditWorkGroup")
    @ApiOperation(value = "新增/编辑推送配置")
    public Result<Boolean> addEditWorkGroup(@RequestBody AddEditWorkGroupRequestModel requestModel) {
        workGroupBiz.addEditWorkGroup(requestModel);
        return Result.success(true);
    }

    @PostMapping(value = "/addEditNode")
    @ApiOperation(value = "新增/编辑配置节点信息")
    public Result<Boolean> addEditNode(@RequestBody AddEditWorkGroupNodeRequestModel requestModel) {
        workGroupBiz.addEditNode(requestModel);
        return Result.success(true);
    }

    @PostMapping(value = "/getNodeInfo")
    @ApiOperation(value = "配置节点信息")
    public Result<WorkGroupNodeResponseModel> getNodeInfo(@RequestBody WorkGroupIdRequestModel requestModel) {
        return Result.success(workGroupBiz.getNodeInfo(requestModel));
    }
}
