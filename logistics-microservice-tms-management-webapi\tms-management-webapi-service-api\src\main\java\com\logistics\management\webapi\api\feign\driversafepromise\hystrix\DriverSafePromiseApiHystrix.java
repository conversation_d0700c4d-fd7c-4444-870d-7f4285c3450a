package com.logistics.management.webapi.api.feign.driversafepromise.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.driversafepromise.DriverSafePromiseApi;
import com.logistics.management.webapi.api.feign.driversafepromise.dto.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @Author: sj
 * @Date: 2019/11/4 10:06
 */
@Component
public class DriverSafePromiseApiHystrix implements DriverSafePromiseApi {

    @Override
    public Result<PageInfo<SearchSafePromiseListResponseDto>> searchList(SearchSafePromiseListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addSafePromise(AddSafePromiseRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<SafePromiseDetailResponseDto> getDetail(SafePromiseDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delSafePromise(DeleteSafePromiseRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> reissueSafePromise(ReissueSavePromiseRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchSignSafePromiseListResponseDto>> searchSignList(SearchSignSafePromiseListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<SignSafePromiseDetailResponseDto> getSignDetail(SignSafePromiseDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> uploadSafePromise(UploadSafePromiseRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<SummarySignSafePromiseResponseDto> getSignSummary(SearchSignSafePromiseListRequestDto responseDto) {
        return Result.timeout();
    }
}
