package com.logistics.tms.controller.biddingorder.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/04/26
 */
@Data
public class SearchBiddingOrderListResponseModel {
    /**
     * 竞价单id
     */
    private Long biddingOrderId;

    /**
     * 竞价单号
     */
    private String biddingOrderCode;

    /**
     * 竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消
     */
    private Integer biddingStatus;

    /**
     * 竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消
     */
    private String biddingStatusLabel;

    /**
     * 装卸方式 1 一装一卸、2 多装一卸
     */
    private Integer handlingMode;

    /**
     * 装卸方式 1 一装一卸、2 多装一卸
     */
    private String handlingModeLabel;

    /**
     * 关联单据
     */
    private String demandCount;

    /**
     * 报价承运商数量
     */
    private Integer carrierCount;

    /**
     * 发货地址
     */
    private String loadAddress;

    /**
     * 途径点
     */
    private Integer pathwayCount;

    /**
     * 收货地址
     */
    private String unloadAddress;

    /**
     * 总价
     */
    private BigDecimal quotePrice;

    /**
     * 车长
     */
    private BigDecimal vehicleLength;

    /**
     * 数量
     */
    private BigDecimal goodsCount;

    /**
     * 货物单位 件 /吨
     */
    private String goodsUnit;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 报价开始时间
     */
    private Date quoteStartTime;

    /**
     * 报价结束时间
     */
    private Date quoteEndTime;
}
