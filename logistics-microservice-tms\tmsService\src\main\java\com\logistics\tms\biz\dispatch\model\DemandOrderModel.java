package com.logistics.tms.biz.dispatch.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/7 17:29
 */
@Data
public class DemandOrderModel {
    /**
     * 需求单id
     */
    private Long demandOrderId;

    /**
     * 需求单号
     */
    private String demandOrderCode;

    /**
     * 委托单状态：500待发布 600竞价中 1000待调度 2000部分调度 3000调度完成
     */
    private Integer status;
    /**
     * 是否取消 1 是 0 否
     */
    private Integer ifCancel;
    /**
     * 是否放空：0 否，1 是
     */
    private Integer ifEmpty;
    /**
     * 是否回退：0 否，1 是
     */
    private Integer ifRollback;
    /**
     * 是否异常（中石化推送单子）：0 否，1 是
     */
    private Integer ifObjectionSinopec;

    /**
     * 客户单号
     */
    private String customerOrderCode;

    /**
     * 委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生
     */
    private Integer source;

    /**
     * 需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售
     */
    private Integer entrustType;

    /**
     * 委托数量
     */
    private BigDecimal goodsAmount;

    /**
     * 货物单位
     */
    private Integer goodsUnit;

    /**
     * 车主类型 1 公司 2 个人
     */
    private Integer companyCarrierType;
    /**
     * 车主id
     */
    private Long companyCarrierId;
    /**
     * 车主公司名
     */
    private String companyCarrierName;
    /**
     * 车主联系人id
     */
    private Long carrierContactId;
    /**
     * 车主联系人
     */
    private String carrierContactName;
    /**
     * 车主联系方式
     */
    private String carrierContactPhone;

    /**
     * 货主id
     */
    private Long companyEntrustId;


    /**
     * 发货地址
     */
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadWarehouse;
    private String loadDetailAddress;
    private String loadLongitude;
    private String loadLatitude;

    /**
     * 收货地址
     */
    private String unloadWarehouse;
    private String unloadProvinceName;
    private String unloadCityName;
    private Long unloadAreaId;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadLongitude;
    private String unloadLatitude;


    /**
     * 货物
     */
    private List<DemandOrderGoodsModel> goodsList;

}
