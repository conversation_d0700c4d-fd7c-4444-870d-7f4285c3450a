package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2023/04/04
*/
@Data
public class TCarrierOrderTicketsAudit extends BaseEntity {
    /**
    * 运单ID
    */
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    /**
    * 票据审核状态：0 待审核，1 已审核，2 已驳回
    */
    @ApiModelProperty("票据审核状态：0 待审核，1 已审核，2 已驳回")
    private Integer ticketsAuditStatus;

    /**
    * 票据审核人
    */
    @ApiModelProperty("票据审核人")
    private String ticketsAuditorName;

    /**
    * 票据审核时间
    */
    @ApiModelProperty("票据审核时间")
    private Date ticketsAuditTime;

    /**
    * 是否自动识别审核：0 否，1 是
    */
    @ApiModelProperty("是否自动识别审核：0 否，1 是")
    private Integer ifAutomaticAudit;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}