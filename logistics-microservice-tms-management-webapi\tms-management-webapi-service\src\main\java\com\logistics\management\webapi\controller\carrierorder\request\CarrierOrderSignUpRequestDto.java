package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CarrierOrderSignUpRequestDto {

    @ApiModelProperty("运单ID")
    @NotBlank(message = "id不能为空")
    private String carrierOrderId;
    @ApiModelProperty("签收运费")
    @NotBlank(message = "委托费用不能为空")
    private String signFreightFee;

}
