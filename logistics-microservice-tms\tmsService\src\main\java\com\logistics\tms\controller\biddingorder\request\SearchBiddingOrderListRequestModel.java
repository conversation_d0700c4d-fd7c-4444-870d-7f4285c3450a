package com.logistics.tms.controller.biddingorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/04/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SearchBiddingOrderListRequestModel extends AbstractPageForm<SearchBiddingOrderListRequestModel> {

    /**
     * 竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消
     */
    private Integer biddingStatus;

    /**
     * 装卸方式 1一装一卸、2多装一卸
     */
    private Integer handlingMode;

    /**
     * 发货地址
     */
    private String loadAddress;

    /**
     * 收货地址
     */
    private String unloadAddress;

    /**
     * 车长
     */
    private Long vehicleLength;

}
