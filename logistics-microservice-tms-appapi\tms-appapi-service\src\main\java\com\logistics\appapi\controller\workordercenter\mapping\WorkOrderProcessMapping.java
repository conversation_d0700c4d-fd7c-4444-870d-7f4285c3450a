package com.logistics.appapi.controller.workordercenter.mapping;

import com.logistics.appapi.base.enums.WorkOrderProcessSolveLabelEnum;
import com.logistics.appapi.base.enums.WorkOrderProcessStatusEnum;
import com.logistics.appapi.client.workordercenter.response.WorkOrderProcessAppletResponseModel;
import com.logistics.appapi.controller.workordercenter.response.WorkOrderProcessResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/17
 */
public class WorkOrderProcessMapping extends MapperMapping<WorkOrderProcessAppletResponseModel, WorkOrderProcessResponseDto> {

	@Override
	public void configure() {
		WorkOrderProcessAppletResponseModel source = getSource();
		WorkOrderProcessResponseDto destination = getDestination();

		//状态
		destination.setStatusLabel(WorkOrderProcessStatusEnum.getEnumByKey(source.getStatus()).getValue());
		destination.setSolveSourceLabel(WorkOrderProcessSolveLabelEnum.getEnumByKey(source.getSolveSource()).getValue());
		String remark = StringUtils.isNotBlank(source.getSolveRemark()) ? "。 " + source.getSolveRemark() : "";
		destination.setSolveRemark(source.getSolveDesc() + remark);
	}
}
