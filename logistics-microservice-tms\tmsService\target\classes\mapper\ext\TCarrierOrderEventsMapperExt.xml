<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderEventsMapper" >
    <insert id="batchInsertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderEvents" >
        <foreach collection="list" item="item" separator=";">
            insert into t_carrier_order_events
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.carrierOrderId != null" >
                    carrier_order_id,
                </if>
                <if test="item.event != null" >
                    event,
                </if>
                <if test="item.eventDesc != null" >
                    event_desc,
                </if>
                <if test="item.remark != null" >
                    remark,
                </if>
                <if test="item.eventTime != null" >
                    event_time,
                </if>
                <if test="item.ticketsCount != null" >
                    tickets_count,
                </if>
                <if test="item.operatorName != null" >
                    operator_name,
                </if>
                <if test="item.operateTime != null" >
                    operate_time,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderId != null" >
                    #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.event != null" >
                    #{item.event,jdbcType=INTEGER},
                </if>
                <if test="item.eventDesc != null" >
                    #{item.eventDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null" >
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.eventTime != null" >
                    #{item.eventTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.ticketsCount != null" >
                    #{item.ticketsCount,jdbcType=INTEGER},
                </if>
                <if test="item.operatorName != null" >
                    #{item.operatorName,jdbcType=VARCHAR},
                </if>
                <if test="item.operateTime != null" >
                    #{item.operateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="selectEventByEventTypeAndCarrieriOrderId" resultMap="BaseResultMap">
       SELECT <include refid="Base_Column_List"/>
        FROM t_carrier_order_events WHERE valid=1
        and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT} and event = #{eventType,jdbcType=INTEGER}
        order by operate_time desc limit 1
    </select>

    <select id="getEvent4App" resultType="com.logistics.tms.controller.carrierorderapplet.response.CarrierOrderLogisticsEventResponseModel">
        select
        tcoe.id as eventId,
        tcoe.event,
        tcoe.event_time as eventTime,
        tcoe.remark as eventDesc,
        tcoe.operator_name as operatorName,
        tcoe.tickets_count as ticketCount
        from t_carrier_order_events tcoe
        where tcoe.valid = 1
        and tcoe.carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
        and tcoe.event in (20,30,60,70,80,90,100,110)
        order by tcoe.event_time desc
    </select>


    <select id="selectByCarrierOrderId" resultMap="BaseResultMap">
       SELECT <include refid="Base_Column_List"/>
        FROM t_carrier_order_events WHERE valid=1
        and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
    </select>
</mapper>