package com.logistics.management.webapi.controller.freightconfig.response.shipping;

import lombok.Data;

@Data
public class AssociateCarrierListRespDto {


    /**
     * 主键id
     */
    private String shippingFreightId ;

    /**
     * 车主id
     */
    private String companyCarrierId;

    /**
     * 承运商名称
     */
    private String companyCarrierName = "";

    /**
     * 零担运价新增人
     */
    private String shippingFreightAddUser = "";

    /**
     * 零担运价新增时间
     */
    private String shippingFreightAddTime = "";

}
