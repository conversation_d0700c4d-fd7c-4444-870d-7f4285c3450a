package com.logistics.appapi.client.reservationorder.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/14 14:03
 */
@Data
public class ReservationOrderSummaryResponseModel {

    /**
     * 待预约-提货订单数量
     */
    private Integer loadOrderCount;

    /**
     * 待预约-卸货订单数量
     */
    private Integer unloadOrderCount;

    /**
     * 已预约-列表
     */
    private List<ReservationOrderSummaryListResponseModel> summaryList=new ArrayList<>();

}
