package com.logistics.management.webapi.controller.settlestatement.packaging.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierAdjustCostResponseDto {

    @ApiModelProperty("对账单id")
    private String settleStatementId = "";

    @ApiModelProperty("车主运费")
    private String carrierFreight = "";

    @ApiModelProperty("运费费点")
    private String freightTaxPoint = "";

    @ApiModelProperty("费额合计")
    private String carrierFreightTotal = "";

    @ApiModelProperty("临时费用")
    private String otherFee = "";

    @ApiModelProperty("临时费用费点")
    private String otherFeeTaxPoint = "";

    @ApiModelProperty("临时费用合计")
    private String otherFeeTotal = "";

    @ApiModelProperty("申请费用总额")
    private String applyFeeTotal = "";

    @ApiModelProperty("对账费用")
    private String reconciliationFee = "";

    @ApiModelProperty("调整费用")
    private String adjustCost = "";

    @ApiModelProperty("调整费用符号：1 ‘+’， 2 ‘-’")
    private String adjustCostSymbol = "";
}
