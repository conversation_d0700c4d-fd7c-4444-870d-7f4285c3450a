package com.logistics.tms.controller.attendance.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考勤打卡详情请求
 */
@Data
public class AttendanceClockRequestModel {

	@ApiModelProperty(value = "打卡类型, 1:上班打卡 2:下班打卡", required = true)
	private Integer punchType;

	@ApiModelProperty(value = "打卡地点", required = true)
	private String punchLocation;

	@ApiModelProperty(value = "打卡图片地址", required = true)
	private String punchPic;
}
