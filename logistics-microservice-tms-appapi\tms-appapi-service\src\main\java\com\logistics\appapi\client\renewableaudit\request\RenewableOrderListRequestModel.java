package com.logistics.appapi.client.renewableaudit.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderListRequestModel extends AbstractPageForm<RenewableOrderListRequestModel> {

    @ApiModelProperty("订单状态：1 待确认，2 待审核，3 已审核")
    private Integer status;

    private Long staffId;
}
