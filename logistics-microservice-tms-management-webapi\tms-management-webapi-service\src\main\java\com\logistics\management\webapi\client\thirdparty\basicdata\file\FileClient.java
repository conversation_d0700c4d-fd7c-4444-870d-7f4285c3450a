package com.logistics.management.webapi.client.thirdparty.basicdata.file;


import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.hystrix.FileClientHystrix;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.request.BatchGetOSSFileUrlRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.request.GetFileByFilePathRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.request.GetOSSUrlRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.response.FileUploadForAIResponseModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.response.GetFileByteOSSResponseModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.response.GetOSSUrlResponseModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.response.UploadFileOSSResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/27
 */
@FeignClient(name = FeignClientName.BASIC_DATA_SERVICES,fallback = FileClientHystrix.class)
public interface FileClient {

    @ApiOperation(value = "上传图片到oss并识别文字信息")
    @PostMapping(name = "OCR", value = "/file/api/uploadOSSFileForAI", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    Result<FileUploadForAIResponseModel> uploadOSSFileForAI(@RequestPart("file") MultipartFile file, @RequestParam("picType") String picType, @RequestParam(value = "idCardSide", required = false) String idCardSide);


    @ApiOperation(value = "上传文件到oss")
    @PostMapping(value = "/file/api/uploadMultiPartFileOSS", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    Result<UploadFileOSSResponseModel> uploadMultiPartFileOSS(@RequestPart("file") MultipartFile file);


    @ApiOperation(value = "批量获取oss图片预览url")
    @PostMapping(value = "/file/api/batchGetOSSFileUrl")
    Result<List<GetOSSUrlResponseModel>> batchGetOSSFileUrl(@RequestBody BatchGetOSSFileUrlRequestModel requestModel);

    @ApiOperation(value = "获取oss图片预览url")
    @PostMapping(value = "/file/api/getOSSFileUrl")
    Result<GetOSSUrlResponseModel> getOSSFileUrl(@RequestBody GetOSSUrlRequestModel requestModel);

    @ApiOperation(value = "获取oss File流")
    @PostMapping(value = "/file/api/getFileByteOSS")
    Result<GetFileByteOSSResponseModel> getFileByteOSS(@RequestBody GetFileByFilePathRequestModel requestModel);
}
