package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TStaffDriverIntegrityExaminationRecord extends BaseEntity {
    /**
    * 人员ID
    */
    @ApiModelProperty("人员ID")
    private Long staffId;

    /**
    * 有效期
    */
    @ApiModelProperty("有效期")
    private Date validDate;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}