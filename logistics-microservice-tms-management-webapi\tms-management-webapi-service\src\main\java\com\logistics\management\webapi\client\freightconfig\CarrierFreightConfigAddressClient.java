package com.logistics.management.webapi.client.freightconfig;

import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.freightconfig.hystrix.CarrierFreightConfigAddressHystrix;
import com.logistics.management.webapi.client.freightconfig.request.address.*;
import com.logistics.management.webapi.client.freightconfig.response.address.CarrierFreightConfigAddressDetailResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.address.CarrierFreightConfigAddressListResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.address.CarrierFreightConfigAddressLogsResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.address.SearchCarrierFreightConfigResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 路线计价配置管理
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = CarrierFreightConfigAddressHystrix.class)
public interface CarrierFreightConfigAddressClient {

    @PostMapping(value = "/service/freight/config/address/searchList")
    @ApiOperation(value = "路线计价配置列表")
    Result<CarrierFreightConfigAddressListResponseModel> searchList(@RequestBody CarrierFreightConfigAddressListRequestModel requestModel);

    @PostMapping(value = "/service/freight/config/address/detail")
    @ApiOperation(value = "路线计价配置查看")
    Result<CarrierFreightConfigAddressDetailResponseModel> detail(@RequestBody CarrierFreightAddressDetailRequestModel requestModel);

    @PostMapping(value = "/service/freight/config/address/add")
    @ApiOperation(value = "新增路线计价配置")
    Result<Boolean> add(@RequestBody CarrierFreightConfigAddressAddRequestModel requestModel);

    @PostMapping(value = "/service/freight/config/address/edit")
    @ApiOperation(value = "编辑路线计价配置")
    Result<Boolean> edit(@RequestBody CarrierFreightAddressEditEnableRequestModel requestModel);

    @PostMapping(value = "/service/freight/config/address/delete")
    @ApiOperation(value = "删除路线计价配置")
    Result<Boolean> delete(@RequestBody CarrierFreightAddressDeleteRequestModel requestModel);

    @PostMapping(value = "/service/freight/config/address/enable")
    @ApiOperation(value = "启用禁用路线计价配置")
    Result<Boolean> enable(@RequestBody CarrierFreightAddressEnableRequestModel requestModel);

    @PostMapping(value = "/service/freight/config/address/export")
    @ApiOperation(value = "路线计价配置列表导出")
    Result<CarrierFreightConfigAddressListResponseModel> export(@RequestBody CarrierFreightConfigAddressListRequestModel requestModel);

    @ApiOperation(value = "查询车主运价信息")
    @PostMapping(value = "/service/freight/config/address/getCarrierFreight")
    Result<SearchCarrierFreightConfigResponseModel> getCarrierFreight(@RequestBody SearchCarrierFreightConfigRequestModel requestModel);

    @ApiOperation(value = "车主运价细则日志", tags = "1.3.5")
    @PostMapping(value = "/service/freight/config/address/getAddressRuleLogs")
    Result<List<CarrierFreightConfigAddressLogsResponseModel>> getAddressRuleLogs(@RequestBody CarrierFreightAddressDetailRequestModel requestDto);
}
