package com.logistics.tms.controller.staffvehiclerelation.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/6/3 11:15
 */
@Data
public class GetFuzzyQueryDriverInfoResponseModel{
    @ApiModelProperty("人员机构  1 自主，2 外部，3 自营")
    private Integer staffProperty;
    @ApiModelProperty("司机ID")
    private Long driverId;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driverPhone;
    @ApiModelProperty("身份证号")
    private String identityNumber;
}
