package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/25 9:55
 */
@Data
public class AggregateDataStatisticsResponseModel {
    @ApiModelProperty("需求单集合")
    private List<DemandOrderDataStatisticsResponseModel> demandOrders;
    @ApiModelProperty("纠错运单")
    private CorrectCarrierOrderDataStatisticsResponseModel correctCarrierOrder;

}
