package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TBusinessCode;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TBusinessCodeMapper extends BaseMapper<TBusinessCode> {
    TBusinessCode selectBusinessCodeByCondition(@Param("businessType") Integer businessType,
                                                @Param("businessCodePrefix") String businessCodePrefix,
                                                @Param("businessCodeDateFormat") String businessCodeDateFormat);
}