<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TInsuranceCostsMapper" >
    <resultMap id="getByVehicleIdCurrentMonth_Map" type="com.logistics.tms.controller.vehiclesettlement.response.GetInsuranceCostsByVehicleIdResponseModel">
        <id column="id" property="insuranceCostsId" jdbcType="BIGINT"/>
        <result column="commercial_insurance_cost" property="commercialInsuranceCost" jdbcType="DECIMAL"/>
        <result column="compulsory_insurance_cost" property="compulsoryInsuranceCost" jdbcType="DECIMAL"/>
        <result column="cargo_insurance_cost" property="cargoInsuranceCost" jdbcType="DECIMAL"/>
        <result column="carrier_insurance_cost" property="carrierInsuranceCost" jdbcType="DECIMAL"/>
        <result column="pay_commercial_insurance_cost" property="payCommercialInsuranceCost" jdbcType="DECIMAL"/>
        <result column="pay_compulsory_insurance_cost" property="payCompulsoryInsuranceCost" jdbcType="DECIMAL"/>
        <result column="pay_cargo_insurance_cost" property="payCargoInsuranceCost" jdbcType="DECIMAL"/>
        <result column="pay_carrier_insurance_cost" property="payCarrierInsuranceCost" jdbcType="DECIMAL"/>
        <result column="insurance_claims_cost" property="insuranceClaimsCost" jdbcType="DECIMAL"/>
        <collection property="insuranceIdList"  ofType="java.lang.Long" javaType="list">
            <result column="insurance_id"/>
        </collection>
    </resultMap>
    <select id="getByIdForSettlement" resultMap="getByVehicleIdCurrentMonth_Map">
        select
        tic.id,
        tic.commercial_insurance_cost,
        tic.compulsory_insurance_cost,
        tic.cargo_insurance_cost,
        tic.carrier_insurance_cost,
        tic.pay_commercial_insurance_cost,
        tic.pay_compulsory_insurance_cost,
        tic.pay_cargo_insurance_cost,
        tic.pay_carrier_insurance_cost,
        tic.insurance_claims_cost,
        ticr.insurance_id
        from t_insurance_costs tic
        left join t_insurance_costs_relation ticr on ticr.insurance_costs_id = tic.id and ticr.valid = 1
        where tic.valid = 1
        and tic.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="getLastByVehicleId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from t_insurance_costs
        where valid = 1
        and vehicle_id = #{vehicleId,jdbcType=BIGINT}
        order by created_time desc
        limit 1
    </select>
    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update t_insurance_costs
            <set>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.vehicleId != null">
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleNo != null">
                    vehicle_no = #{item.vehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.settlementMonth != null">
                    settlement_month = #{item.settlementMonth,jdbcType=VARCHAR},
                </if>
                <if test="item.commercialInsuranceCost != null">
                    commercial_insurance_cost = #{item.commercialInsuranceCost,jdbcType=DECIMAL},
                </if>
                <if test="item.compulsoryInsuranceCost != null">
                    compulsory_insurance_cost = #{item.compulsoryInsuranceCost,jdbcType=DECIMAL},
                </if>
                <if test="item.cargoInsuranceCost != null">
                    cargo_insurance_cost = #{item.cargoInsuranceCost,jdbcType=DECIMAL},
                </if>
                <if test="item.carrierInsuranceCost != null">
                    carrier_insurance_cost = #{item.carrierInsuranceCost,jdbcType=DECIMAL},
                </if>
                <if test="item.insuranceClaimsCost != null">
                    insurance_claims_cost = #{item.insuranceClaimsCost,jdbcType=DECIMAL},
                </if>
                <if test="item.insuranceClaimsTime != null">
                    insurance_claims_time = #{item.insuranceClaimsTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.payCommercialInsuranceCost != null">
                    pay_commercial_insurance_cost = #{item.payCommercialInsuranceCost,jdbcType=DECIMAL},
                </if>
                <if test="item.payCompulsoryInsuranceCost != null">
                    pay_compulsory_insurance_cost = #{item.payCompulsoryInsuranceCost,jdbcType=DECIMAL},
                </if>
                <if test="item.payCargoInsuranceCost != null">
                    pay_cargo_insurance_cost = #{item.payCargoInsuranceCost,jdbcType=DECIMAL},
                </if>
                <if test="item.payCarrierInsuranceCost != null">
                    pay_carrier_insurance_cost = #{item.payCarrierInsuranceCost,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="deleteInsuranceCostAndRelation">
        update t_insurance_costs tic
        left join t_insurance_costs_relation ticr on ticr.insurance_costs_id=tic.id and ticr.valid=1
        left join t_vehicle_settlement_relation tvsr on tvsr.object_type=60 and tvsr.object_id=tic.id and tvsr.valid=1
        set
        tic.valid=0,tic.last_modified_by= #{name,jdbcType=VARCHAR}, tic.last_modified_time=now(),
        ticr.valid=0,ticr.last_modified_by= #{name,jdbcType=VARCHAR}, ticr.last_modified_time=now(),
        tvsr.valid=0,tvsr.last_modified_by= #{name,jdbcType=VARCHAR}, tvsr.last_modified_time=now()
        where tic.id = #{insuranceCostId,jdbcType=BIGINT}
        and tic.valid=1
    </update>
</mapper>