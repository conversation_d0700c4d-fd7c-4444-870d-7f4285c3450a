package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2024/08/06
*/
@Data
public class TShippingOrderItem extends BaseEntity {
    /**
    * 运输单id
    */
    @ApiModelProperty("运输单id")
    private Long shippingOrderId;

    /**
    * 运单ID
    */
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    /**
    * 运单号
    */
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    /**
    * 需求单ID
    */
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;

    /**
    * 需求单号
    */
    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    /**
    * 预计数量
    */
    @ApiModelProperty("预计数量")
    private BigDecimal expectAmount;

    /**
    * 排序
    */
    @ApiModelProperty("排序")
    private Integer orderNum;

    /**
    * 到下个点位距离 (最后一个是到卸货地的距离)
    */
    @ApiModelProperty("到下个点位距离 (最后一个是到卸货地的距离)")
    private BigDecimal nextPointDistance;

    /**
    * 发货省份ID
    */
    @ApiModelProperty("发货省份ID")
    private Long loadProvinceId;

    /**
    * 发货省份名字
    */
    @ApiModelProperty("发货省份名字")
    private String loadProvinceName;

    /**
    * 发货城市ID
    */
    @ApiModelProperty("发货城市ID")
    private Long loadCityId;

    /**
    * 发货城市名字
    */
    @ApiModelProperty("发货城市名字")
    private String loadCityName;

    /**
    * 发货县区id
    */
    @ApiModelProperty("发货县区id")
    private Long loadAreaId;

    /**
    * 发货县区名字
    */
    @ApiModelProperty("发货县区名字")
    private String loadAreaName;

    /**
    * 发货详细地址
    */
    @ApiModelProperty("发货详细地址")
    private String loadDetailAddress;

    /**
    * 发货仓库
    */
    @ApiModelProperty("发货仓库")
    private String loadWarehouse;

    /**
    * 发货经度
    */
    @ApiModelProperty("发货经度")
    private String loadLongitude;

    /**
    * 发货纬度
    */
    @ApiModelProperty("发货纬度")
    private String loadLatitude;

    /**
    * 发货人姓名
    */
    @ApiModelProperty("发货人姓名")
    private String consignorName;

    /**
    * 发货人手机号
    */
    @ApiModelProperty("发货人手机号")
    private String consignorMobile;

    /**
    * 收货省份ID
    */
    @ApiModelProperty("收货省份ID")
    private Long unloadProvinceId;

    /**
    * 收货省份名字
    */
    @ApiModelProperty("收货省份名字")
    private String unloadProvinceName;

    /**
    * 收货城市ID
    */
    @ApiModelProperty("收货城市ID")
    private Long unloadCityId;

    /**
    * 收货城市名字
    */
    @ApiModelProperty("收货城市名字")
    private String unloadCityName;

    /**
    * 收货县区id
    */
    @ApiModelProperty("收货县区id")
    private Long unloadAreaId;

    /**
    * 收货县区名字
    */
    @ApiModelProperty("收货县区名字")
    private String unloadAreaName;

    /**
    * 收货详细地址
    */
    @ApiModelProperty("收货详细地址")
    private String unloadDetailAddress;

    /**
    * 收货仓库
    */
    @ApiModelProperty("收货仓库")
    private String unloadWarehouse;

    /**
    * 收货经度
    */
    @ApiModelProperty("收货经度")
    private String unloadLongitude;

    /**
    * 收货纬度
    */
    @ApiModelProperty("收货纬度")
    private String unloadLatitude;

    /**
    * 收货人姓名
    */
    @ApiModelProperty("收货人姓名")
    private String receiverName;

    /**
    * 收货人手机号
    */
    @ApiModelProperty("收货人手机号")
    private String receiverMobile;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;

    /**
     * 来源
     */
    @ApiModelProperty("1初始新增 2后期加入")
    private Integer source;
}