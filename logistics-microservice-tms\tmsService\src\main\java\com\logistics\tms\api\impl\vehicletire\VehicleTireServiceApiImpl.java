package com.logistics.tms.api.impl.vehicletire;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.vehicletire.VehicleTireServiceApi;
import com.logistics.tms.api.feign.vehicletire.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.vehicletire.VehicleTireBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class VehicleTireServiceApiImpl implements VehicleTireServiceApi {

    @Autowired
    private VehicleTireBiz vehicleTireBiz;

    /**
     * 获取轮胎管理列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<VehicleTireListResponseModel>> searchVehicleTireList(@RequestBody VehicleTireListRequestModel requestModel) {
        return Result.success(vehicleTireBiz.searchVehicleTireList(requestModel.enablePaging()));
    }

    /**
     * 查看详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<VehicleTireDetailResponseModel> getVehicleTireDetail(@RequestBody VehicleTireIdRequestModel requestModel) {
        return Result.success(vehicleTireBiz.getVehicleTireDetail(requestModel));
    }

    /**
     * 新增/修改
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> addOrModifyVehicleTire(@RequestBody AddOrModifyVehicleTireRequestModel requestModel) {
        vehicleTireBiz.addOrModifyVehicleTire(requestModel);
        return Result.success(true);
    }

    /**
     * 删除
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> deleteVehicleTire(@RequestBody VehicleTireIdRequestModel requestModel) {
        vehicleTireBiz.deleteVehicleTire(requestModel);
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<VehicleTireListResponseModel>> export(@RequestBody VehicleTireListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        PageInfo<VehicleTireListResponseModel> pageInfo=vehicleTireBiz.searchVehicleTireList(requestModel);
        return Result.success(pageInfo.getList());
    }

    /**
     * Excel导入轮胎管理信息
     * @param requestModel
     * @return
     */
    @Override
    public Result<ImportVehicleTireInfoResponseModel> importExcelInfoVehicleTireInfo(@RequestBody ImportVehicleTireInfoRequestModel requestModel) {
        return Result.success(vehicleTireBiz.importExcelInfoVehicleTireInfo(requestModel));
    }

    /**
     * 导入轮胎管理凭证
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> importVehicleTireCertificateInfo(@RequestBody ImportTireCertificateRequestModel requestModel) {
        vehicleTireBiz.importVehicleTireCertificateInfo(requestModel);
        return Result.success(true);
    }
}
