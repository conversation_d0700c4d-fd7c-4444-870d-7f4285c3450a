<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleSettlementEventsMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleSettlementEvents" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="vehicle_settlement_id" property="vehicleSettlementId" jdbcType="BIGINT" />
    <result column="event" property="event" jdbcType="INTEGER" />
    <result column="event_desc" property="eventDesc" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="event_time" property="eventTime" jdbcType="TIMESTAMP" />
    <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
    <result column="operate_time" property="operateTime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, vehicle_settlement_id, event, event_desc, remark, event_time, operator_name, 
    operate_time, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_settlement_events
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_vehicle_settlement_events
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleSettlementEvents" >
    insert into t_vehicle_settlement_events (id, vehicle_settlement_id, event, 
      event_desc, remark, event_time, 
      operator_name, operate_time, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{vehicleSettlementId,jdbcType=BIGINT}, #{event,jdbcType=INTEGER}, 
      #{eventDesc,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{eventTime,jdbcType=TIMESTAMP}, 
      #{operatorName,jdbcType=VARCHAR}, #{operateTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleSettlementEvents" >
    insert into t_vehicle_settlement_events
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="vehicleSettlementId != null" >
        vehicle_settlement_id,
      </if>
      <if test="event != null" >
        event,
      </if>
      <if test="eventDesc != null" >
        event_desc,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="eventTime != null" >
        event_time,
      </if>
      <if test="operatorName != null" >
        operator_name,
      </if>
      <if test="operateTime != null" >
        operate_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vehicleSettlementId != null" >
        #{vehicleSettlementId,jdbcType=BIGINT},
      </if>
      <if test="event != null" >
        #{event,jdbcType=INTEGER},
      </if>
      <if test="eventDesc != null" >
        #{eventDesc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="eventTime != null" >
        #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorName != null" >
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null" >
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleSettlementEvents" >
    update t_vehicle_settlement_events
    <set >
      <if test="vehicleSettlementId != null" >
        vehicle_settlement_id = #{vehicleSettlementId,jdbcType=BIGINT},
      </if>
      <if test="event != null" >
        event = #{event,jdbcType=INTEGER},
      </if>
      <if test="eventDesc != null" >
        event_desc = #{eventDesc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="eventTime != null" >
        event_time = #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorName != null" >
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null" >
        operate_time = #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleSettlementEvents" >
    update t_vehicle_settlement_events
    set vehicle_settlement_id = #{vehicleSettlementId,jdbcType=BIGINT},
      event = #{event,jdbcType=INTEGER},
      event_desc = #{eventDesc,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      event_time = #{eventTime,jdbcType=TIMESTAMP},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      operate_time = #{operateTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>