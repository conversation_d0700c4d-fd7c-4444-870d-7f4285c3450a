package com.logistics.tms.biz.drivercostapply.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SelectByCodeAndNumModel {

    @ApiModelProperty("1 增值税，2 出租车，3 火车票，4 定额，5 卷票，6 机打，7 过路")
    private Integer type;

    @ApiModelProperty("发票代码")
    private String invoiceCode;

    @ApiModelProperty("发票号码")
    private String invoiceNum;
}
