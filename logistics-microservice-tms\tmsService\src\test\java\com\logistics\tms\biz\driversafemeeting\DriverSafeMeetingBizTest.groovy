package com.logistics.tms.biz.driversafemeeting

import com.logistics.tms.controller.driversafemeeting.request.AddDriverSafeMeetingRequestModel
import com.logistics.tms.controller.driversafemeeting.response.AppletSafeMeetingDetailResponseModel
import com.logistics.tms.controller.driversafemeeting.request.AppletSafeMeetingListRequestModel
import com.logistics.tms.controller.driversafemeeting.response.AppletSafeMeetingListResponseModel
import com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingContentDetailResponseModel
import com.logistics.tms.controller.driversafemeeting.request.DriverSafeMeetingDetailRequestModel
import com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingDetailResponseModel
import com.logistics.tms.controller.driversafemeeting.request.DriverSafeMeetingIdRequestModel
import com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingKanBanItemResponseModel
import com.logistics.tms.controller.driversafemeeting.request.DriverSafeMeetingKanBanRequestModel
import com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingKanBanResponseModel
import com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingListCountResponseModel
import com.logistics.tms.controller.driversafemeeting.request.DriverSafeMeetingRelationIdRequestModel
import com.logistics.tms.controller.driversafemeeting.request.ModifyDriverSafeMeetingRequestModel
import com.logistics.tms.controller.driversafemeeting.request.ReplacementDriverSafeMeetingRequestModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.controller.driversafemeeting.request.AppletConfirmLeaningRequestModel
import com.logistics.tms.entity.TDriverSafeMeetingRelation
import com.logistics.tms.entity.TStaffBasic
import com.logistics.tms.mapper.TDriverSafeMeetingMapper
import com.logistics.tms.mapper.TDriverSafeMeetingRelationMapper
import com.logistics.tms.mapper.TStaffBasicMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class DriverSafeMeetingBizTest extends Specification {
    @Mock
    TDriverSafeMeetingMapper driverSafeMeetingMapper
    @Mock
    TDriverSafeMeetingRelationMapper driverSafeMeetingRelationMapper
    @Mock
    TStaffBasicMapper staffBasicMapper
    @Mock
    CommonBiz commonBiz
    @InjectMocks
    DriverSafeMeetingBiz driverSafeMeetingBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "driver Safe Meeting Kan Ban where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(driverSafeMeetingMapper.driverSafeMeetingKanBan(any())).thenReturn([new DriverSafeMeetingKanBanResponseModel()])
        when(driverSafeMeetingRelationMapper.getDriverSafeMeetingKanBanItem(anyString())).thenReturn([new DriverSafeMeetingKanBanItemResponseModel()])

        expect:
        driverSafeMeetingBiz.driverSafeMeetingKanBan(requestModel) == expectedResult

        where:
        requestModel                              || expectedResult
        new DriverSafeMeetingKanBanRequestModel() || [new DriverSafeMeetingKanBanResponseModel()]
    }

    @Unroll
    def "add Driver Safe Meeting where requestModel=#requestModel"() {
        given:
        when(driverSafeMeetingRelationMapper.batchInsert(any())).thenReturn(0)
        when(staffBasicMapper.getInternalDriverCount()).thenReturn(0)
        when(staffBasicMapper.getInternalDriverByIds(anyString())).thenReturn([new TStaffBasic(staffProperty: 0, name: "name", mobile: "mobile")])
        when(commonBiz.processReplaceTempPicture(anyString(), any(), anyString())).thenReturn("processReplaceTempPictureResponse")

        expect:
        driverSafeMeetingBiz.addDriverSafeMeeting(requestModel)
        assert expectedResult == false

        where:
        requestModel                           || expectedResult
        new AddDriverSafeMeetingRequestModel() || true
    }

    @Unroll
    def "add Driver Safe Meeting Relation where safeMeetingId=#safeMeetingId and driverIdList=#driverIdList and driverList=#driverList"() {
        given:
        when(driverSafeMeetingRelationMapper.batchInsert(any())).thenReturn(0)

        expect:
        driverSafeMeetingBiz.addDriverSafeMeetingRelation(safeMeetingId, driverList, driverIdList)
        assert expectedResult == false

        where:
        safeMeetingId | driverIdList | driverList                                                          || expectedResult
        1l            | [1l]         | [new TStaffBasic(staffProperty: 0, name: "name", mobile: "mobile")] || true
    }

    @Unroll
    def "modify Driver Safe Meeting where requestModel=#requestModel"() {
        given:
        when(driverSafeMeetingRelationMapper.getBySafeMeetingId(anyLong())).thenReturn([new TDriverSafeMeetingRelation(status: 0, studyTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 23).getTime(), signTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 23).getTime(), signLocateAddress: "signLocateAddress", staffDriverImageUrl: "staffDriverImageUrl", signImageUrl: "signImageUrl")])
        when(driverSafeMeetingRelationMapper.batchUpdateForStudySignTimeNull(any())).thenReturn(0)
        when(commonBiz.processReplaceTempPicture(anyString(), any(), anyString())).thenReturn("processReplaceTempPictureResponse")

        expect:
        driverSafeMeetingBiz.modifyDriverSafeMeeting(requestModel)
        assert expectedResult == false

        where:
        requestModel                              || expectedResult
        new ModifyDriverSafeMeetingRequestModel() || true
    }

    @Unroll
    def "replacement Driver Safe Meeting where requestModel=#requestModel"() {
        given:
        when(driverSafeMeetingRelationMapper.batchInsert(any())).thenReturn(0)
        when(staffBasicMapper.getInternalDriverCount()).thenReturn(0)
        when(staffBasicMapper.getInternalDriverByIds(anyString())).thenReturn([new TStaffBasic(staffProperty: 0, name: "name", mobile: "mobile")])

        expect:
        driverSafeMeetingBiz.replacementDriverSafeMeeting(requestModel)
        assert expectedResult == false

        where:
        requestModel                                   || expectedResult
        new ReplacementDriverSafeMeetingRequestModel() || true
    }

    @Unroll
    def "driver Safe Meeting Content Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(driverSafeMeetingMapper.driverSafeMeetingContentDetail(anyLong())).thenReturn(new DriverSafeMeetingContentDetailResponseModel())
        when(commonBiz.addRealPath(anyString())).thenReturn("addRealPathResponse")

        expect:
        driverSafeMeetingBiz.driverSafeMeetingContentDetail(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new DriverSafeMeetingIdRequestModel() || new DriverSafeMeetingContentDetailResponseModel()
    }

    @Unroll
    def "driver Safe Meeting Detail List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(driverSafeMeetingRelationMapper.driverSafeMeetingDetailList(any())).thenReturn([new DriverSafeMeetingDetailResponseModel()])

        expect:
        driverSafeMeetingBiz.driverSafeMeetingDetailList(requestModel) == expectedResult

        where:
        requestModel                              || expectedResult
        new DriverSafeMeetingDetailRequestModel() || null
    }

    @Unroll
    def "driver Safe Meeting List Count where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(driverSafeMeetingRelationMapper.driverSafeMeetingListCount(any())).thenReturn(new DriverSafeMeetingListCountResponseModel())

        expect:
        driverSafeMeetingBiz.driverSafeMeetingListCount(requestModel) == expectedResult

        where:
        requestModel                              || expectedResult
        new DriverSafeMeetingDetailRequestModel() || new DriverSafeMeetingListCountResponseModel()
    }

    @Unroll
    def "del Driver Safe Meeting Relation where requestModel=#requestModel"() {
        expect:
        driverSafeMeetingBiz.delDriverSafeMeetingRelation(requestModel)
        assert expectedResult == false

        where:
        requestModel                                  || expectedResult
        new DriverSafeMeetingRelationIdRequestModel() || true
    }

    @Unroll
    def "applet Safe Meeting List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(driverSafeMeetingRelationMapper.appletSafeMeetingList(any(), anyLong())).thenReturn([new AppletSafeMeetingListResponseModel()])
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        driverSafeMeetingBiz.appletSafeMeetingList(requestModel) == expectedResult

        where:
        requestModel                            || expectedResult
        new AppletSafeMeetingListRequestModel() || null
    }

    @Unroll
    def "applet Safe Meeting List Count"() {
        given:
        when(driverSafeMeetingRelationMapper.appletSafeMeetingListCount(anyLong())).thenReturn(new DriverSafeMeetingListCountResponseModel())
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)

        expect:
        driverSafeMeetingBiz.appletSafeMeetingListCount() == expectedResult

        where:
        expectedResult << new DriverSafeMeetingListCountResponseModel()
    }

    @Unroll
    def "applet Safe Meeting Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(driverSafeMeetingRelationMapper.appletSafeMeetingDetail(anyLong(), anyLong())).thenReturn(new AppletSafeMeetingDetailResponseModel())
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(commonBiz.addRealPath(anyString())).thenReturn("addRealPathResponse")

        expect:
        driverSafeMeetingBiz.appletSafeMeetingDetail(requestModel) == expectedResult

        where:
        requestModel                                  || expectedResult
        new DriverSafeMeetingRelationIdRequestModel() || new AppletSafeMeetingDetailResponseModel()
    }

    @Unroll
    def "applet Confirm Leaning where requestModel=#requestModel"() {
        given:
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(commonBiz.getAddressByLonAndLat(anyString())).thenReturn("getAddressByLonAndLatResponse")

        expect:
        driverSafeMeetingBiz.appletConfirmLeaning(requestModel)
        assert expectedResult == false

        where:
        requestModel                                                                               || expectedResult
        new AppletConfirmLeaningRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme