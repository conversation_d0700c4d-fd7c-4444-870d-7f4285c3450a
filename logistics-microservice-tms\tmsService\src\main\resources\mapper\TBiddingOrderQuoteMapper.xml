<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TBiddingOrderQuoteMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TBiddingOrderQuote">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bidding_order_id" jdbcType="BIGINT" property="biddingOrderId" />
    <result column="quote_status" jdbcType="INTEGER" property="quoteStatus" />
    <result column="company_carrier_type" jdbcType="INTEGER" property="companyCarrierType" />
    <result column="company_carrier_id" jdbcType="BIGINT" property="companyCarrierId" />
    <result column="company_carrier_name" jdbcType="VARCHAR" property="companyCarrierName" />
    <result column="carrier_contact_id" jdbcType="BIGINT" property="carrierContactId" />
    <result column="carrier_contact_name" jdbcType="VARCHAR" property="carrierContactName" />
    <result column="carrier_contact_phone" jdbcType="VARCHAR" property="carrierContactPhone" />
    <result column="quote_price_type" jdbcType="INTEGER" property="quotePriceType" />
    <result column="quote_price" jdbcType="DECIMAL" property="quotePrice" />
    <result column="quote_operator" jdbcType="VARCHAR" property="quoteOperator" />
    <result column="quote_time" jdbcType="TIMESTAMP" property="quoteTime" />
    <result column="vehicle_length_id" jdbcType="BIGINT" property="vehicleLengthId" />
    <result column="vehicle_length" jdbcType="DECIMAL" property="vehicleLength" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, bidding_order_id, quote_status, company_carrier_type, company_carrier_id, company_carrier_name, 
    carrier_contact_id, carrier_contact_name, carrier_contact_phone, quote_price_type, 
    quote_price, quote_operator, quote_time, vehicle_length_id, vehicle_length, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_bidding_order_quote
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_bidding_order_quote
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TBiddingOrderQuote">
    insert into t_bidding_order_quote (id, bidding_order_id, quote_status, 
      company_carrier_type, company_carrier_id, company_carrier_name, 
      carrier_contact_id, carrier_contact_name, carrier_contact_phone, 
      quote_price_type, quote_price, quote_operator, 
      quote_time, vehicle_length_id, vehicle_length, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{biddingOrderId,jdbcType=BIGINT}, #{quoteStatus,jdbcType=INTEGER}, 
      #{companyCarrierType,jdbcType=INTEGER}, #{companyCarrierId,jdbcType=BIGINT}, #{companyCarrierName,jdbcType=VARCHAR}, 
      #{carrierContactId,jdbcType=BIGINT}, #{carrierContactName,jdbcType=VARCHAR}, #{carrierContactPhone,jdbcType=VARCHAR}, 
      #{quotePriceType,jdbcType=INTEGER}, #{quotePrice,jdbcType=DECIMAL}, #{quoteOperator,jdbcType=VARCHAR}, 
      #{quoteTime,jdbcType=TIMESTAMP}, #{vehicleLengthId,jdbcType=BIGINT}, #{vehicleLength,jdbcType=DECIMAL}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TBiddingOrderQuote">
    insert into t_bidding_order_quote
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="biddingOrderId != null">
        bidding_order_id,
      </if>
      <if test="quoteStatus != null">
        quote_status,
      </if>
      <if test="companyCarrierType != null">
        company_carrier_type,
      </if>
      <if test="companyCarrierId != null">
        company_carrier_id,
      </if>
      <if test="companyCarrierName != null">
        company_carrier_name,
      </if>
      <if test="carrierContactId != null">
        carrier_contact_id,
      </if>
      <if test="carrierContactName != null">
        carrier_contact_name,
      </if>
      <if test="carrierContactPhone != null">
        carrier_contact_phone,
      </if>
      <if test="quotePriceType != null">
        quote_price_type,
      </if>
      <if test="quotePrice != null">
        quote_price,
      </if>
      <if test="quoteOperator != null">
        quote_operator,
      </if>
      <if test="quoteTime != null">
        quote_time,
      </if>
      <if test="vehicleLengthId != null">
        vehicle_length_id,
      </if>
      <if test="vehicleLength != null">
        vehicle_length,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="biddingOrderId != null">
        #{biddingOrderId,jdbcType=BIGINT},
      </if>
      <if test="quoteStatus != null">
        #{quoteStatus,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierType != null">
        #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null">
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null">
        #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null">
        #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null">
        #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null">
        #{carrierContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="quotePriceType != null">
        #{quotePriceType,jdbcType=INTEGER},
      </if>
      <if test="quotePrice != null">
        #{quotePrice,jdbcType=DECIMAL},
      </if>
      <if test="quoteOperator != null">
        #{quoteOperator,jdbcType=VARCHAR},
      </if>
      <if test="quoteTime != null">
        #{quoteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleLengthId != null">
        #{vehicleLengthId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null">
        #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TBiddingOrderQuote">
    update t_bidding_order_quote
    <set>
      <if test="biddingOrderId != null">
        bidding_order_id = #{biddingOrderId,jdbcType=BIGINT},
      </if>
      <if test="quoteStatus != null">
        quote_status = #{quoteStatus,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierType != null">
        company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null">
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null">
        company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null">
        carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null">
        carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null">
        carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="quotePriceType != null">
        quote_price_type = #{quotePriceType,jdbcType=INTEGER},
      </if>
      <if test="quotePrice != null">
        quote_price = #{quotePrice,jdbcType=DECIMAL},
      </if>
      <if test="quoteOperator != null">
        quote_operator = #{quoteOperator,jdbcType=VARCHAR},
      </if>
      <if test="quoteTime != null">
        quote_time = #{quoteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleLengthId != null">
        vehicle_length_id = #{vehicleLengthId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null">
        vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TBiddingOrderQuote">
    update t_bidding_order_quote
    set bidding_order_id = #{biddingOrderId,jdbcType=BIGINT},
      quote_status = #{quoteStatus,jdbcType=INTEGER},
      company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
      quote_price_type = #{quotePriceType,jdbcType=INTEGER},
      quote_price = #{quotePrice,jdbcType=DECIMAL},
      quote_operator = #{quoteOperator,jdbcType=VARCHAR},
      quote_time = #{quoteTime,jdbcType=TIMESTAMP},
      vehicle_length_id = #{vehicleLengthId,jdbcType=BIGINT},
      vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>