package com.logistics.tms.biz.workgroup;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.client.feign.basicdata.BasicServiceClient;
import com.logistics.tms.client.feign.basicdata.user.response.GetUserByIdsResponseModel;
import com.logistics.tms.client.feign.basicdata.wechat.request.CreateGroupChatRequestModel;
import com.logistics.tms.client.feign.basicdata.wechat.request.PushMessageRequestModel;
import com.logistics.tms.controller.workgroup.request.*;
import com.logistics.tms.controller.workgroup.response.SearchWorkGroupListResponseModel;
import com.logistics.tms.controller.workgroup.response.WorkGroupDetailResponseModel;
import com.logistics.tms.controller.workgroup.response.WorkGroupDistrictResponseModel;
import com.logistics.tms.controller.workgroup.response.WorkGroupNodeResponseModel;
import com.logistics.tms.entity.TWorkGroup;
import com.logistics.tms.entity.TWorkGroupDistrict;
import com.logistics.tms.entity.TWorkGroupNode;
import com.logistics.tms.entity.TWorkGroupNodeField;
import com.logistics.tms.mapper.TWorkGroupDistrictMapper;
import com.logistics.tms.mapper.TWorkGroupMapper;
import com.logistics.tms.mapper.TWorkGroupNodeFieldMapper;
import com.logistics.tms.mapper.TWorkGroupNodeMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2023/12/22 15:58
 * 智能推送
 */
@Service
public class WorkGroupBiz {

    @Resource
    private TWorkGroupMapper tWorkGroupMapper;
    @Resource
    private TWorkGroupNodeMapper tWorkGroupNodeMapper;
    @Resource
    private TWorkGroupDistrictMapper tWorkGroupDistrictMapper;
    @Resource
    private TWorkGroupNodeFieldMapper tWorkGroupNodeFieldMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private BasicServiceClient basicServiceClient;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchWorkGroupListResponseModel> searchList(SearchWorkGroupListRequestModel requestModel) {
        requestModel.enablePaging();
        List<SearchWorkGroupListResponseModel> list = tWorkGroupMapper.searchList(requestModel);
        return new PageInfo<>(list);
    }

    /**
     * 列表-禁用/启用
     * @param requestModel
     */
    @Transactional
    public void workGroupEnable(WorkGroupEnableRequestModel requestModel) {
        //查询智能推送配置是否存在
        TWorkGroup dbWorkGroup = getWorkGroup(requestModel.getWorkGroupId());

        //启用
        if (EnabledEnum.ENABLED.getKey().equals(requestModel.getEnabled())){
            //仅禁用状态允许操作
            if (!EnabledEnum.DISABLED.getKey().equals(dbWorkGroup.getEnabled())){
                throw new BizException(CarrierDataExceptionEnum.WORK_GROUP_DO_NOT_OPERATE);
            }

            //校验节点信息是否存在
            List<TWorkGroupNode> workGroupNodeList = tWorkGroupNodeMapper.getByWorkGroupId(dbWorkGroup.getId());
            if (ListUtils.isEmpty(workGroupNodeList)){
                throw new BizException(CarrierDataExceptionEnum.WORK_GROUP_DO_NOT_OPERATE);
            }
        }
        //禁用
        else if (EnabledEnum.DISABLED.getKey().equals(requestModel.getEnabled())){
            //仅启用状态允许操作
            if (!EnabledEnum.ENABLED.getKey().equals(dbWorkGroup.getEnabled())){
                throw new BizException(CarrierDataExceptionEnum.WORK_GROUP_DO_NOT_OPERATE);
            }
        }
        //入参不正确
        else {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        //更新状态
        TWorkGroup upWorkGroup = new TWorkGroup();
        upWorkGroup.setId(dbWorkGroup.getId());
        upWorkGroup.setEnabled(requestModel.getEnabled());
        commonBiz.setBaseEntityModify(upWorkGroup, BaseContextHandler.getUserName());
        tWorkGroupMapper.updateByPrimaryKeySelectiveEncrypt(upWorkGroup);

    }

    /**
     * 列表-删除
     * @param requestModel
     */
    @Transactional
    public void delWorkGroup(WorkGroupIdRequestModel requestModel) {
        //查询智能推送配置是否存在
        TWorkGroup dbWorkGroup = getWorkGroup(requestModel.getWorkGroupId());

        //仅禁用状态允许操作
        if (!EnabledEnum.DISABLED.getKey().equals(dbWorkGroup.getEnabled())){
            throw new BizException(CarrierDataExceptionEnum.WORK_GROUP_DO_NOT_OPERATE);
        }

        //删除配置
        tWorkGroupMapper.delWorkGroup(dbWorkGroup.getId(), BaseContextHandler.getUserName(), new Date());
    }

    /**
     * 推送配置详情
     * @param requestModel
     * @return
     */
    public WorkGroupDetailResponseModel getDetail(WorkGroupIdRequestModel requestModel) {
        //查询智能推送配置
        TWorkGroup dbWorkGroup = getWorkGroup(requestModel.getWorkGroupId());

        //数据转换
        WorkGroupDetailResponseModel detail = MapperUtils.mapper(dbWorkGroup, WorkGroupDetailResponseModel.class);
        detail.setWorkGroupId(dbWorkGroup.getId());
        //查询区域
        if (MatchingFieldEnum.AREA.getKey().equals(dbWorkGroup.getMatchingField())){
            List<WorkGroupDistrictResponseModel> workGroupDistrictList = tWorkGroupDistrictMapper.getWorkGroupDistrictDetail(dbWorkGroup.getId());
            detail.setDistrictList(workGroupDistrictList);
        }
        return detail;
    }

    //查询智能推送配置
    private TWorkGroup getWorkGroup(Long workGroupId){
        TWorkGroup dbWorkGroup = tWorkGroupMapper.selectByPrimaryKeyDecrypt(workGroupId);
        if (dbWorkGroup == null){
            throw new BizException(CarrierDataExceptionEnum.WORK_GROUP_NOT_EXIST);
        }
        return dbWorkGroup;
    }

    /**
     * 新增/编辑推送配置
     *
     * @param requestModel
     */
    @Transactional
    public void addEditWorkGroup(AddEditWorkGroupRequestModel requestModel) {
        if (requestModel.getWorkGroupId() == null) {
            //新增
            addWorkGroup(requestModel);
        } else {
            //编辑
            editWorkGroup(requestModel);
        }
    }

    /**
     * 编辑推送配置
     *
     * @param requestModel
     */
    public void editWorkGroup(AddEditWorkGroupRequestModel requestModel) {
        //查询智能推送配置是否存在
        TWorkGroup dbWorkGroup = getWorkGroup(requestModel.getWorkGroupId());

        //仅禁用状态允许操作
        if (!EnabledEnum.DISABLED.getKey().equals(dbWorkGroup.getEnabled())){
            throw new BizException(CarrierDataExceptionEnum.WORK_GROUP_DO_NOT_OPERATE);
        }

        //推送设置不能更改
        if (!dbWorkGroup.getWorkGroupSource().equals(requestModel.getWorkGroupSource())){
            throw new BizException(CarrierDataExceptionEnum.WORK_GROUP_SOURCE_ERROR);
        }

        //修改信息
        TWorkGroup workGroupUpdate = new TWorkGroup();
        MapperUtils.mapperNoDefault(requestModel, workGroupUpdate);
        workGroupUpdate.setId(requestModel.getWorkGroupId());
        workGroupUpdate.setMatchingLocation(StringUtils.listToString(requestModel.getMatchingLocationList(),','));
        commonBiz.setBaseEntityModify(workGroupUpdate, BaseContextHandler.getUserName());
        if (!dbWorkGroup.getGroupOwnerId().equals(requestModel.getGroupOwnerId()) || !dbWorkGroup.getParticipantId().equals(requestModel.getParticipantId())) {
            //查询负责人和参与人
            List<Long> userIds = new ArrayList<>();
            setUerIds(userIds, requestModel.getGroupOwnerId());
            setUerIds(userIds, requestModel.getParticipantId());
            Map<Long, GetUserByIdsResponseModel> userMap = getBasicUserInfo(userIds);
            setUserInfo(requestModel, workGroupUpdate, userMap);
        }
        //如果是新建的群聊，企业微信群id不能更改
        if (WorkGroupSourceEnum.CREATE.getKey().equals(dbWorkGroup.getWorkGroupSource())){
            workGroupUpdate.setWorkGroupCode(null);
        }
        tWorkGroupMapper.updateByPrimaryKeySelectiveEncrypt(workGroupUpdate);

        //处理地区配置
        //数据库为【区域】
        if (MatchingFieldEnum.AREA.getKey().equals(dbWorkGroup.getMatchingField())) {

            //查询区域信息
            List<TWorkGroupDistrict> dbWorkGroupDistricts = tWorkGroupDistrictMapper.selectByWorkGroupId(dbWorkGroup.getId());

            //编辑后仍是【区域】，则修改
            if (MatchingFieldEnum.AREA.getKey().equals(requestModel.getMatchingField())) {
                Set<Long> districtSet = new HashSet<>();
                List<TWorkGroupDistrict> workGroupDistrictInsertList = new ArrayList<>();
                List<TWorkGroupDistrict> workGroupDistrictUpdateList = new ArrayList<>();
                for (AddEditWorkGroupDistrictRequestModel districtRequestModel : requestModel.getDistrictList().stream().distinct().collect(Collectors.toList())) {
                    districtSet.add(districtRequestModel.getWorkGroupDistrictId());
                    TWorkGroupDistrict tWorkGroupDistrict = MapperUtils.mapperNoDefault(districtRequestModel, TWorkGroupDistrict.class);
                    if (districtRequestModel.getWorkGroupDistrictId() == null) {
                        tWorkGroupDistrict.setWorkGroupId(requestModel.getWorkGroupId());
                        commonBiz.setBaseEntityAdd(tWorkGroupDistrict, BaseContextHandler.getUserName());
                        workGroupDistrictInsertList.add(tWorkGroupDistrict);
                    } else {
                        commonBiz.setBaseEntityModify(tWorkGroupDistrict, BaseContextHandler.getUserName());
                        workGroupDistrictUpdateList.add(tWorkGroupDistrict);
                    }
                }

                if (ListUtils.isNotEmpty(dbWorkGroupDistricts)) {
                    workGroupDistrictUpdateList.addAll(dbWorkGroupDistricts.stream().filter(model -> !districtSet.contains(model.getId()))
                            .map(tWorkGroupDistrict -> {
                                TWorkGroupDistrict model = new TWorkGroupDistrict();
                                model.setId(tWorkGroupDistrict.getId());
                                model.setValid(IfValidEnum.INVALID.getKey());
                                commonBiz.setBaseEntityModify(model, BaseContextHandler.getUserName());
                                return model;
                            }).collect(Collectors.toList()));
                }
                if (ListUtils.isNotEmpty(workGroupDistrictInsertList)) {
                    tWorkGroupDistrictMapper.batchInsert(workGroupDistrictInsertList);
                }
                if (ListUtils.isNotEmpty(workGroupDistrictUpdateList)) {
                    tWorkGroupDistrictMapper.batchUpdateSelective(workGroupDistrictUpdateList);
                }
            }
            //编辑后改为【仓库】，则删除
            else{
                if (ListUtils.isNotEmpty(dbWorkGroupDistricts)) {
                    List<TWorkGroupDistrict> workGroupDistrictDelList = dbWorkGroupDistricts.stream()
                            .map(tWorkGroupDistrict -> {
                                TWorkGroupDistrict model = new TWorkGroupDistrict();
                                model.setId(tWorkGroupDistrict.getId());
                                model.setValid(IfValidEnum.INVALID.getKey());
                                commonBiz.setBaseEntityModify(model, BaseContextHandler.getUserName());
                                return model;
                            }).collect(Collectors.toList());
                    if (ListUtils.isNotEmpty(workGroupDistrictDelList)) {
                        tWorkGroupDistrictMapper.batchUpdateSelective(workGroupDistrictDelList);
                    }
                }
            }
        }
        //数据库为【仓库】
        else{
            //编辑后改为【区域】，则新增
            if (MatchingFieldEnum.AREA.getKey().equals(requestModel.getMatchingField())) {
                TWorkGroupDistrict tWorkGroupDistrictInsert;
                List<TWorkGroupDistrict> tWorkGroupDistrictInsertList = new ArrayList<>();
                for (AddEditWorkGroupDistrictRequestModel districtRequestModel : requestModel.getDistrictList().stream().distinct().collect(Collectors.toList())) {
                    tWorkGroupDistrictInsert = new TWorkGroupDistrict();
                    tWorkGroupDistrictInsert.setWorkGroupId(dbWorkGroup.getId());
                    MapperUtils.mapperNoDefault(districtRequestModel, tWorkGroupDistrictInsert);
                    commonBiz.setBaseEntityAdd(tWorkGroupDistrictInsert, BaseContextHandler.getUserName());
                    tWorkGroupDistrictInsertList.add(tWorkGroupDistrictInsert);
                }
                if (ListUtils.isNotEmpty(tWorkGroupDistrictInsertList)) {
                    tWorkGroupDistrictMapper.batchInsert(tWorkGroupDistrictInsertList);
                }
            }
        }
    }

    /**
     * 新增推送配置
     *
     * @param requestModel
     */
    public void addWorkGroup(AddEditWorkGroupRequestModel requestModel) {
        //查询负责人当前已经有多少个群
        int groupCount = tWorkGroupMapper.selectCount();
        //最多50个群
        if (groupCount >= CommonConstant.INT_FIFTY) {
            throw new BizException(CarrierDataExceptionEnum.WORK_GROUP_COUNT_MAX);
        }

        //查询负责人和参与人
        List<Long> userIds = new ArrayList<>();
        setUerIds(userIds, requestModel.getGroupOwnerId());
        setUerIds(userIds, requestModel.getParticipantId());
        Map<Long, GetUserByIdsResponseModel> userMap = getBasicUserInfo(userIds);

        //新建：企业微信群id；已有：机器人url
        String workGroupCode;
        if (WorkGroupSourceEnum.CREATE.getKey().equals(requestModel.getWorkGroupSource())){
            workGroupCode = UUID.randomUUID().toString().replace("-", "");
        }else{
            workGroupCode = requestModel.getWorkGroupCode();
        }

        //插入推送配置信息
        TWorkGroup tWorkGroupInsert = new TWorkGroup();
        MapperUtils.mapperNoDefault(requestModel, tWorkGroupInsert);
        tWorkGroupInsert.setWorkGroupCode(workGroupCode);
        tWorkGroupInsert.setEnabled(EnabledEnum.DISABLED.getKey());
        tWorkGroupInsert.setMatchingLocation(StringUtils.listToString(requestModel.getMatchingLocationList(),','));
        setUserInfo(requestModel, tWorkGroupInsert, userMap);
        commonBiz.setBaseEntityAdd(tWorkGroupInsert, BaseContextHandler.getUserName());

        //新建群聊，需要调企微接口创建群
        if (WorkGroupSourceEnum.CREATE.getKey().equals(requestModel.getWorkGroupSource())) {
            //创建群聊
            List<Long> userList = new ArrayList<>();
            userList.add(requestModel.getGroupOwnerId());
            userList.add(requestModel.getParticipantId());
            CreateGroupChatRequestModel createGroupChatRequestModel = new CreateGroupChatRequestModel();
            createGroupChatRequestModel.setChatId(workGroupCode);
            createGroupChatRequestModel.setName(requestModel.getGroupName());
            createGroupChatRequestModel.setOwnerId(requestModel.getGroupOwnerId());
            createGroupChatRequestModel.setUserList(userList);
            basicServiceClient.createGroupChat(createGroupChatRequestModel);
        }

        //防止群聊未创建成功，插入的数据又要回滚
        tWorkGroupMapper.insertSelectiveEncrypt(tWorkGroupInsert);

        //选择区域
        if (MatchingFieldEnum.AREA.getKey().equals(requestModel.getMatchingField())) {
            //插入省市信息
            TWorkGroupDistrict tWorkGroupDistrictInsert;
            List<TWorkGroupDistrict> tWorkGroupDistrictInsertList = new ArrayList<>();
            for (AddEditWorkGroupDistrictRequestModel districtRequestModel : requestModel.getDistrictList().stream().distinct().collect(Collectors.toList())) {
                tWorkGroupDistrictInsert = new TWorkGroupDistrict();
                tWorkGroupDistrictInsert.setWorkGroupId(tWorkGroupInsert.getId());
                MapperUtils.mapperNoDefault(districtRequestModel, tWorkGroupDistrictInsert);
                commonBiz.setBaseEntityAdd(tWorkGroupDistrictInsert, BaseContextHandler.getUserName());
                tWorkGroupDistrictInsertList.add(tWorkGroupDistrictInsert);
            }
            if (ListUtils.isNotEmpty(tWorkGroupDistrictInsertList)) {
                tWorkGroupDistrictMapper.batchInsert(tWorkGroupDistrictInsertList);
            }
        }

        if (WorkGroupSourceEnum.CREATE.getKey().equals(requestModel.getWorkGroupSource())) {
            //群聊创建成功后发送一条消息
            AsyncProcessQueue.execute(() -> sendMsg(workGroupCode));
        }
    }
    private void sendMsg(String chatId){
        List<PushMessageRequestModel> wechatPushMessageRequests = Lists.newArrayList();
        PushMessageRequestModel pushMessageRequestModel = new PushMessageRequestModel();
        pushMessageRequestModel.setMsgType(PushMessageRequestModel.PushMessageMsgType.text.name());
        pushMessageRequestModel.setMessage("hello world");
        pushMessageRequestModel.setChatId(chatId);
        wechatPushMessageRequests.add(pushMessageRequestModel);
        basicServiceClient.workGroupPushMessage(wechatPushMessageRequests);
    }

    private void setUerIds(List<Long> userIds, Long userId) {
        if (userId != null) {
            userIds.add(userId);
        }
    }

    private static void setUserInfo(AddEditWorkGroupRequestModel requestModel, TWorkGroup workGroupUpdate, Map<Long, GetUserByIdsResponseModel> userMap) {
        workGroupUpdate.setGroupOwnerUsername(userMap.get(requestModel.getGroupOwnerId()).getUserName());
        workGroupUpdate.setGroupOwnerMobile(userMap.get(requestModel.getGroupOwnerId()).getMobilePhone());
        workGroupUpdate.setParticipantUsername(userMap.get(requestModel.getParticipantId()).getUserName());
        workGroupUpdate.setParticipantMobile(userMap.get(requestModel.getParticipantId()).getMobilePhone());
    }

    private Map<Long, GetUserByIdsResponseModel> getBasicUserInfo(List<Long> userIds) {
        List<GetUserByIdsResponseModel> userInfo = basicServiceClient.getUserByIds(userIds);
        if (ListUtils.isEmpty(userInfo)) {
            throw new BizException(CarrierDataExceptionEnum.WORK_GROUP_USER_NOT_EXIST);
        }

        Map<Long, GetUserByIdsResponseModel> resultMap = userInfo.stream().filter(model -> CommonConstant.INTEGER_ONE.equals(model.getEnabled())).collect(Collectors.toMap(GetUserByIdsResponseModel::getId, Function.identity()));
        if (resultMap.size() != userIds.size()) {
            throw new BizException(CarrierDataExceptionEnum.WORK_GROUP_USER_NOT_EXIST);
        }
        return resultMap;
    }

    /**
     * 新增/编辑配置节点信息
     *
     * @param requestModel
     */
    @Transactional
    public void addEditNode(AddEditWorkGroupNodeRequestModel requestModel) {
        //查询智能推送配置是否存在
        TWorkGroup dbWorkGroup = getWorkGroup(requestModel.getWorkGroupId());

        //仅禁用状态允许操作
        if (!EnabledEnum.DISABLED.getKey().equals(dbWorkGroup.getEnabled())){
            throw new BizException(CarrierDataExceptionEnum.WORK_GROUP_DO_NOT_OPERATE);
        }

        //查询所有节点
        List<TWorkGroupNode> dbWorkGroupNodeList = tWorkGroupNodeMapper.getByWorkGroupId(dbWorkGroup.getId());

        Set<Long> hasIdReqModelSet = new HashSet<>();
        Map<Integer, List<AddEditWorkGroupNodeListRequestModel>> nodeMap = requestModel.getNodeList()
                .stream()
                .peek(model -> {
                    if (model.getWorkGroupNodeId() != null) {
                        hasIdReqModelSet.add(model.getWorkGroupNodeId());
                    }
                })
                .collect(Collectors.groupingBy(model -> model.getWorkGroupNodeId() == null ? CommonConstant.INTEGER_ZERO : CommonConstant.INTEGER_ONE));

        List<AddEditWorkGroupNodeListRequestModel> addNodeList = nodeMap.get(CommonConstant.INTEGER_ZERO);
        List<AddEditWorkGroupNodeListRequestModel> editNodeList = nodeMap.get(CommonConstant.INTEGER_ONE);
        if (ListUtils.isNotEmpty(addNodeList)) {
            //新增节点
            addNode(addNodeList, dbWorkGroup.getId());
        }
        if (ListUtils.isNotEmpty(editNodeList)) {
            //编辑节点
            editNode(editNodeList);
        }

        //更新需求类型
        Collections.sort(requestModel.getEntrustTypeList());
        String reqEntrustTypeGroup = StringUtils.join(requestModel.getEntrustTypeList(), CommonConstant.COMMA);
        String dbEntrustTypeGroup = CommonConstant.BLANK_TEXT;
        if (StringUtils.isNotBlank(dbWorkGroup.getEntrustTypeGroup())) {
            String[] entrustTypeArray = dbWorkGroup.getEntrustTypeGroup().split(CommonConstant.COMMA);
            Arrays.sort(entrustTypeArray);
            dbEntrustTypeGroup = StringUtils.join(entrustTypeArray, CommonConstant.COMMA);
        }
        //更新项目标签
        Collections.sort(requestModel.getProjectLabelList());
        String reqProjectLabel = StringUtils.join(requestModel.getProjectLabelList(), CommonConstant.COMMA);
        String dbProjectLabel = CommonConstant.BLANK_TEXT;
        if (StringUtils.isNotBlank(dbWorkGroup.getProjectLabel())) {
            String[] projectLabelArray = dbWorkGroup.getProjectLabel().split(CommonConstant.COMMA);
            Arrays.sort(projectLabelArray);
            dbProjectLabel = StringUtils.join(projectLabelArray, CommonConstant.COMMA);
        }

        //更新【需求类型】/【项目标签】
        if (StringUtils.isBlank(dbEntrustTypeGroup) || !reqEntrustTypeGroup.equals(dbEntrustTypeGroup)
                || StringUtils.isBlank(dbProjectLabel) || !reqProjectLabel.equals(dbProjectLabel)) {

            TWorkGroup workGroupUpdate = new TWorkGroup();
            workGroupUpdate.setId(dbWorkGroup.getId());
            if (StringUtils.isBlank(dbEntrustTypeGroup) || !reqEntrustTypeGroup.equals(dbEntrustTypeGroup)) {
                workGroupUpdate.setEntrustTypeGroup(reqEntrustTypeGroup);
            }
            if (StringUtils.isBlank(dbProjectLabel) || !reqProjectLabel.equals(dbProjectLabel)) {
                workGroupUpdate.setProjectLabel(reqProjectLabel);
            }
            commonBiz.setBaseEntityModify(workGroupUpdate, BaseContextHandler.getUserName());
            tWorkGroupMapper.updateByPrimaryKeySelectiveEncrypt(workGroupUpdate);
        }

        //删除节点信息
        List<TWorkGroupNode> tWorkGroupNodesUpdateList = new ArrayList<>();
        if (ListUtils.isNotEmpty(dbWorkGroupNodeList)) {
            tWorkGroupNodesUpdateList = dbWorkGroupNodeList.stream()
                    .filter(model -> !hasIdReqModelSet.contains(model.getId()))
                    .map(tWorkGroupNode -> {
                        TWorkGroupNode model = new TWorkGroupNode();
                        model.setId(tWorkGroupNode.getId());
                        model.setValid(IfValidEnum.INVALID.getKey());
                        commonBiz.setBaseEntityModify(model, BaseContextHandler.getUserName());
                        return model;
                    }).collect(Collectors.toList());
        }

        if (ListUtils.isNotEmpty(tWorkGroupNodesUpdateList)) {
            tWorkGroupNodeMapper.batchUpdateSelective(tWorkGroupNodesUpdateList);
        }
    }

    /**
     * 编辑节点
     *
     * @param editNodeList
     */
    public void editNode(List<AddEditWorkGroupNodeListRequestModel> editNodeList) {
        //更新节点信息
        List<Long> nodeIdList = new ArrayList<>();
        List<TWorkGroupNode> workGroupNodeUpdateList = new ArrayList<>();
        for (AddEditWorkGroupNodeListRequestModel reqNodeModel : editNodeList) {

            nodeIdList.add(reqNodeModel.getWorkGroupNodeId());
            TWorkGroupNode nodeInsertModel = MapperUtils.mapperNoDefault(reqNodeModel, TWorkGroupNode.class);
            nodeInsertModel.setId(reqNodeModel.getWorkGroupNodeId());
            commonBiz.setBaseEntityModify(nodeInsertModel, BaseContextHandler.getUserName());
            workGroupNodeUpdateList.add(nodeInsertModel);

            reqNodeModel.setTWorkGroupNode(nodeInsertModel);
        }

        //删除节点下面的所有字段模板
        tWorkGroupNodeFieldMapper.delAllFieldByNodeId(nodeIdList, BaseContextHandler.getUserName());

        tWorkGroupNodeMapper.batchUpdateSelective(workGroupNodeUpdateList);
        //插入节点模板字段信息
        List<TWorkGroupNodeField> tWorkGroupNodeFieldInsetList = getAddNodeFieldList(editNodeList);
        tWorkGroupNodeFieldMapper.batchInsert(tWorkGroupNodeFieldInsetList);
    }

    /**
     * 新增节点
     *
     * @param addNodeList
     * @param workGroupId
     */
    public void addNode(List<AddEditWorkGroupNodeListRequestModel> addNodeList, Long workGroupId) {
        //插入节点信息
        List<TWorkGroupNode> workGroupNodeInsertList = new ArrayList<>();
        for (AddEditWorkGroupNodeListRequestModel reqNodeModel : addNodeList) {

            TWorkGroupNode nodeInsertModel = MapperUtils.mapperNoDefault(reqNodeModel, TWorkGroupNode.class);
            nodeInsertModel.setWorkGroupId(workGroupId);
            commonBiz.setBaseEntityAdd(nodeInsertModel, BaseContextHandler.getUserName());
            workGroupNodeInsertList.add(nodeInsertModel);

            reqNodeModel.setTWorkGroupNode(nodeInsertModel);
        }
        tWorkGroupNodeMapper.batchInsertAndReturnId(workGroupNodeInsertList);

        //插入节点模板字段信息
        List<TWorkGroupNodeField> tWorkGroupNodeFieldInsetList = getAddNodeFieldList(addNodeList);
        tWorkGroupNodeFieldMapper.batchInsert(tWorkGroupNodeFieldInsetList);
    }

    private List<TWorkGroupNodeField> getAddNodeFieldList(List<AddEditWorkGroupNodeListRequestModel> addNodeList) {
        List<TWorkGroupNodeField> tWorkGroupNodeFieldInsetList = new ArrayList<>();
        for (AddEditWorkGroupNodeListRequestModel reqNodeModel : addNodeList) {

            Long workGroupNodeId = reqNodeModel.getTWorkGroupNode().getId();
            List<String> fieldList = reqNodeModel.getFieldList();
            for (String field : fieldList) {
                TWorkGroupNodeField tWorkGroupNodeField = new TWorkGroupNodeField();
                tWorkGroupNodeField.setWorkGroupNodeId(workGroupNodeId);
                tWorkGroupNodeField.setField(field);
                commonBiz.setBaseEntityAdd(tWorkGroupNodeField, BaseContextHandler.getUserName());
                tWorkGroupNodeFieldInsetList.add(tWorkGroupNodeField);
            }
        }
        return tWorkGroupNodeFieldInsetList;
    }

    /**
     * 配置节点信息
     *
     * @param requestModel
     * @return
     */
    public WorkGroupNodeResponseModel getNodeInfo(WorkGroupIdRequestModel requestModel) {
        WorkGroupNodeResponseModel responseModel = new WorkGroupNodeResponseModel();
        //查询智能推送配置是否存在
        TWorkGroup dbWorkGroup = getWorkGroup(requestModel.getWorkGroupId());

        //转换需求类型
        List<Integer> entrustTypeList = new ArrayList<>();
        if (StringUtils.isNotBlank(dbWorkGroup.getEntrustTypeGroup())) {
            entrustTypeList = Arrays.stream(dbWorkGroup.getEntrustTypeGroup().split(CommonConstant.COMMA)).map(Integer::parseInt).collect(Collectors.toList());
        }

        //转换项目标签
        List<Integer> projectLabelList = new ArrayList<>();
        if (StringUtils.isNotBlank(dbWorkGroup.getProjectLabel())) {
            projectLabelList = Arrays.stream(dbWorkGroup.getProjectLabel().split(CommonConstant.COMMA)).map(Integer::parseInt).collect(Collectors.toList());
        }

        responseModel.setWorkGroupId(dbWorkGroup.getId());
        responseModel.setEntrustTypeList(entrustTypeList);
        responseModel.setProjectLabelList(projectLabelList);
        responseModel.setNodeList(tWorkGroupNodeMapper.selectNodeListByWorkGroupId(dbWorkGroup.getId()));
        return responseModel;
    }
}
