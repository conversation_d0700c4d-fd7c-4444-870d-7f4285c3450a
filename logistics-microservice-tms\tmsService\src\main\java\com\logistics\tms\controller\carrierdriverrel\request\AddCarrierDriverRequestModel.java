package com.logistics.tms.controller.carrierdriverrel.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/10/14 13:38
 */
@Data
public class AddCarrierDriverRequestModel {

    @ApiModelProperty(value = "二级承运商ID",required = true)
    @NotBlank(message = "承运商不存在")
    private String companyCarrierId;

    @ApiModelProperty(value = "司机id集合",required = true)
    @NotNull(message = "请选择司机")
    private List<String> staffDriverIdList;
}
