package com.logistics.appapi.client.attendance.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 考勤打卡历史item
 *
 */
@Data
public class AttendanceHistoryItemResponseModel {

	@ApiModelProperty("考勤打卡ID")
	private Long attendanceRecordId;

	@ApiModelProperty("考勤日期,yyyy-MM-dd")
	private Date attendanceDate;

	@ApiModelProperty("上班打卡时间")
	private Date onDutyPunchTime;

	@ApiModelProperty("上班打卡地点")
	private String onDutyPunchLocation;

	@ApiModelProperty("下班打卡时间")
	private Date offDutyPunchTime;

	@ApiModelProperty("下班打卡地点")
	private String offDutyPunchLocation;

	@ApiModelProperty("工时")
	private BigDecimal manHour;

	@ApiModelProperty("是否有修改申请 0:无申请 1:有申请 3:已过申请时间")
	private Integer onDutyPunchApply = 0;

	@ApiModelProperty("是否有修改申请 0:无申请 1:有申请 3:已过申请时间")
	private Integer offDutyPunchApply = 0;
}
