package com.logistics.tms.controller.driversafepromise.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 详情
 * @Author: sj
 * @Date: 2019/11/4 10:21
 */
@Data
public class SafePromiseDetailResponseModel {
    @ApiModelProperty("承诺书ID")
    private Long safePromiseId;
    @ApiModelProperty("周期（年份）")
    private String period;
    @ApiModelProperty("经办人")
    private String agent;
    @ApiModelProperty("上传时间")
    private Date uploadTime;
    @ApiModelProperty("已签订人数")
    private Integer hasSignCount;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("内容")
    private String content;
    @ApiModelProperty("附件相对路径")
    private String attachmentUrl;
    @ApiModelProperty("已签订司机ID列表")
    private List<Long> driverList;
    @ApiModelProperty("所有关联司机ID列表")
    private List<Long> allDriverList;
}
