package com.logistics.tms.biz.reservebalance.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class DriverBalanceDetailModel {

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 余额Id
     */
    private Long reserveBalanceId;

    /**
     * 余额
     */
    private BigDecimal balanceAmount;

    /**
     * 待核销金额
     */
    private BigDecimal awaitVerificationAmount;

    /**
     * 已冲销金额
     */
    private BigDecimal verificationAmount;
}
