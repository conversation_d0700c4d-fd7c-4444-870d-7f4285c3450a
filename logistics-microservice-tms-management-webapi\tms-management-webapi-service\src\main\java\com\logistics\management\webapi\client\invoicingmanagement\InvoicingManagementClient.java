package com.logistics.management.webapi.client.invoicingmanagement;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.invoicingmanagement.hystrix.InvoicingManagementClientHystrix;
import com.logistics.management.webapi.client.invoicingmanagement.request.*;
import com.logistics.management.webapi.client.invoicingmanagement.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/21 14:13
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/invoicingManagement",
        fallback = InvoicingManagementClientHystrix.class)
public interface InvoicingManagementClient {

    @ApiOperation(value = "发票管理-列表")
    @PostMapping(value = "/searchList")
    Result<PageInfo<SearchInvoicingManagementListResponseModel>> searchList(@RequestBody SearchInvoicingManagementListRequestModel requestModel);

    @ApiOperation(value = "发票管理-查看发票")
    @PostMapping(value = "/getInvoicePictures")
    Result<List<GetInvoicePicturesResponseModel>> getInvoicePictures(@RequestBody InvoicingManagementDetailRequestModel requestModel);

    @ApiOperation(value = "发票管理-修改业务名称")
    @PostMapping(value = "/updateBusinessName")
    Result<Boolean> updateBusinessName(@RequestBody UpdateBusinessNameRequestModel requestModel);

    @ApiOperation(value = "发票管理-详情-头部信息")
    @PostMapping(value = "/getDetail")
    Result<InvoicingManagementDetailResponseModel> getDetail(@RequestBody InvoicingManagementDetailRequestModel requestModel);

    @ApiOperation(value = "发票管理-详情-发票列表")
    @PostMapping(value = "/getInvoiceList")
    Result<List<GetInvoiceListResponseModel>> getInvoiceList(@RequestBody InvoicingManagementDetailRequestModel requestModel);

    @ApiOperation(value = "发票管理-详情-发票列表-查看详情")
    @PostMapping(value = "/getInvoiceDetail")
    Result<GetInvoiceDetailResponseModel> getInvoiceDetail(@RequestBody GetInvoiceDetailRequestModel requestModel);

    @ApiOperation(value = "发票管理-详情-发票列表-新增/编辑发票")
    @PostMapping(value = "/addOrModifyInvoice")
    Result<Boolean> addOrModifyInvoice(@RequestBody AddOrModifyInvoiceRequestModel requestModel);

    @ApiOperation(value = "发票管理-详情-发票列表-删除发票")
    @PostMapping(value = "/delInvoice")
    Result<Boolean> delInvoice(@RequestBody GetInvoiceDetailRequestModel requestModel);

    @ApiOperation(value = "发票管理-详情-对账单列表")
    @PostMapping(value = "/getSettleStatementList")
    Result<PageInfo<GetSettleStatementListResponseModel>> getSettleStatementList(@RequestBody GetSettleStatementListRequestModel requestModel);

    @ApiOperation(value = "发票管理-详情-对账单-添加对账单列表")
    @PostMapping(value = "/getAddSettleStatementList")
    Result<PageInfo<GetAddSettleStatementListResponseModel>> getAddSettleStatementList(@RequestBody GetAddSettleStatementListRequestModel requestModel);

    @ApiOperation(value = "发票管理-详情-对账单-添加对账单")
    @PostMapping(value = "/addSettleStatement")
    Result<Boolean> addSettleStatement(@RequestBody AddInvoicingSettleStatementRequestModel requestModel);

    @ApiOperation(value = "发票管理-详情-对账单-移除对账单")
    @PostMapping(value = "/delSettleStatement")
    Result<Boolean> delSettleStatement(@RequestBody DelSettleStatementRequestModel requestModel);

    @ApiOperation(value = "发票管理-详情-归档列表")
    @PostMapping(value = "/getInvoicingArchiveList")
    Result<List<GetInvoicingArchiveListResponseModel>> getInvoicingArchiveList(@RequestBody InvoicingManagementDetailRequestModel requestModel);

    @ApiOperation(value = "发票管理-详情-确认归档")
    @PostMapping(value = "/invoicingArchive")
    Result<Boolean> invoicingArchive(@RequestBody InvoicingArchiveRequestModel requestModel);

}
