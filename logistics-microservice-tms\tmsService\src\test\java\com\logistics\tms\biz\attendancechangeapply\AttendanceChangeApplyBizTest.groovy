package com.logistics.tms.biz.attendancechangeapply

import com.logistics.tms.controller.attendance.request.AttendanceChangeDetailRequestModel
import com.logistics.tms.controller.attendance.response.AttendanceChangeDetailResponseModel
import com.logistics.tms.controller.attendance.response.AttendanceDetailResponseModel
import com.logistics.tms.controller.attendance.request.AuditAttendanceChangeApplyRequestModel
import com.logistics.tms.controller.attendance.request.CancelUpdateAttendanceHistoryRequestModel
import com.logistics.tms.controller.attendance.request.SearchAttendanceChangeListRequestModel
import com.logistics.tms.controller.attendance.response.SearchAttendanceChangeListResponseModel
import com.logistics.tms.controller.attendance.request.UpdateAttendanceHistoryDetailRequestModel
import com.logistics.tms.controller.attendance.response.UpdateAttendanceHistoryDetailResponseModel
import com.logistics.tms.controller.attendance.request.UpdateAttendanceHistoryRequestModel
import com.logistics.tms.biz.attendancerecord.handle.AttendancePunchHandleFactory
import com.logistics.tms.biz.attendancerecord.handle.UpdateDutyPunchHandle
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.controller.attendance.request.CancelAttendanceChangeApplyRequestModel
import com.logistics.tms.entity.TAttendanceChangeApply
import com.logistics.tms.entity.TAttendanceRecord
import com.logistics.tms.mapper.TAttendanceChangeApplyMapper
import com.logistics.tms.mapper.TAttendanceRecordMapper
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class AttendanceChangeApplyBizTest extends Specification {
    @Mock
    CommonBiz commonBiz
    @Mock
    TAttendanceChangeApplyMapper attendanceChangeApplyMapper
    @Mock
    TAttendanceRecordMapper attendanceRecordMapper
    @Mock
    AttendancePunchHandleFactory attendancePunchHandleFactory
    @Mock
    Logger log
    @InjectMocks
    AttendanceChangeApplyBiz attendanceChangeApplyBiz

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "update Attendance History where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(attendanceChangeApplyMapper.selectByAttendanceRecordIdIn(any())).thenReturn([new TAttendanceChangeApply()])
        when(attendanceRecordMapper.selectOneByIdAndStaffId(anyLong(), anyLong())).thenReturn(new TAttendanceRecord())

        expect:
        attendanceChangeApplyBiz.updateAttendanceHistory(requestModel) == expectedResult

        where:
        requestModel                              || expectedResult
        new UpdateAttendanceHistoryRequestModel() || true
    }

    @Unroll
    def "cancel Update Attendance History where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(attendanceChangeApplyMapper.selectAllByIdIn(any())).thenReturn([new TAttendanceChangeApply()])
        when(attendanceChangeApplyMapper.updateByIdAndAuditStatus(any(), anyLong(), anyInt())).thenReturn(0)
        when(attendanceRecordMapper.countByIdAndStaffId(anyLong(), anyLong())).thenReturn(1l)

        expect:
        attendanceChangeApplyBiz.cancelUpdateAttendanceHistory(requestModel) == expectedResult

        where:
        requestModel                                    || expectedResult
        new CancelUpdateAttendanceHistoryRequestModel() || true
    }

    @Unroll
    def "update Attendance History Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(attendanceChangeApplyMapper.selectByAttendanceRecordIdIn(any())).thenReturn([new TAttendanceChangeApply()])
        when(attendanceRecordMapper.selectOneByIdAndStaffId(anyLong(), anyLong())).thenReturn(new TAttendanceRecord())

        expect:
        attendanceChangeApplyBiz.updateAttendanceHistoryDetail(requestModel) == expectedResult

        where:
        requestModel                                    || expectedResult
        new UpdateAttendanceHistoryDetailRequestModel() || new UpdateAttendanceHistoryDetailResponseModel()
    }

    @Unroll
    def "search Attendance Change List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(attendanceChangeApplyMapper.selectSearchAttendanceChangeList(any())).thenReturn([new SearchAttendanceChangeListResponseModel()])

        expect:
        attendanceChangeApplyBiz.searchAttendanceChangeList(requestModel) == expectedResult

        where:
        requestModel                                 || expectedResult
        new SearchAttendanceChangeListRequestModel() || null
    }

    @Unroll
    def "attendance Change Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(attendanceChangeApplyMapper.selectAllByIdIn(any())).thenReturn([new TAttendanceChangeApply()])
        when(attendanceRecordMapper.selectOneDetailById(anyLong())).thenReturn(new AttendanceDetailResponseModel())

        expect:
        attendanceChangeApplyBiz.attendanceChangeDetail(requestModel) == expectedResult

        where:
        requestModel                             || expectedResult
        new AttendanceChangeDetailRequestModel() || new AttendanceChangeDetailResponseModel()
    }

    @Unroll
    def "audit Attendance Change Apply where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(attendanceChangeApplyMapper.selectAllByIdIn(any())).thenReturn([new TAttendanceChangeApply()])
        when(attendanceChangeApplyMapper.updateByIdAndAuditStatus(any(), anyLong(), anyInt())).thenReturn(0)
        when(attendancePunchHandleFactory.getHandle(anyInt())).thenReturn(new UpdateDutyPunchHandle(new CommonBiz(), null))

        expect:
        attendanceChangeApplyBiz.auditAttendanceChangeApply(requestModel) == expectedResult

        where:
        requestModel                                 || expectedResult
        new AuditAttendanceChangeApplyRequestModel() || true
    }

    @Unroll
    def "cancel Attendance Change Apply where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(attendanceChangeApplyMapper.selectAllByIdIn(any())).thenReturn([new TAttendanceChangeApply()])
        when(attendanceChangeApplyMapper.updateByIdAndAuditStatus(any(), anyLong(), anyInt())).thenReturn(0)

        expect:
        attendanceChangeApplyBiz.cancelAttendanceChangeApply(requestModel) == expectedResult

        where:
        requestModel                                                                               || expectedResult
        new CancelAttendanceChangeApplyRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme