package com.logistics.appapi.client.vehiclesafecheck.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 详情
 * @Author: sj
 * @Date: 2019/11/6 13:07
 */
@Data
public class SafeCheckDetailResponseModel {
    @ApiModelProperty("安全检查ID")
    private Long safeCheckVehicleId;
    @ApiModelProperty("状态0未检查，10待确认，20待整改，30已整改，40检查完成")
    private Integer status;
    @ApiModelProperty("车辆ID")
    private Long vehicleId;
    @ApiModelProperty("车牌号(牵引车/一体车)")
    private String vehicleNo;
    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;
    @ApiModelProperty("司机姓名")
    private String staffName;
    @ApiModelProperty("司机电话")
    private String staffMobile;
    @ApiModelProperty("检查人ID")
    private Long checkUserId;
    @ApiModelProperty("检查人姓名")
    private String checkUserName;
    @ApiModelProperty("检查人电话号码")
    private String checkUserMobile;
    @ApiModelProperty("检查时间")
    private Date checkTime;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("文件列表")
    private List<SafeCheckFileResponseModel> fileList;
    @ApiModelProperty("检查项列表")
    private List<SafeCheckItemResponseModel> itemList;
    @ApiModelProperty("整改事项")
    private SafeCheckReformResponseModel checkReformInfo;

}
