package com.logistics.management.webapi.controller.freightconfig.response.address;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetAddressRuleLogsResponseDto {

    @ApiModelProperty("操作内容")
    private String operateContents = "";

    @ApiModelProperty("备注")
    private String remark = "";

    @ApiModelProperty("操作人")
    private String operateUserName = "";

    @ApiModelProperty("时间")
    private String operateTime = "";
}
