package com.logistics.tms.controller.demandorder;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.EntrustDataExceptionEnum;
import com.logistics.tms.biz.demandorder.SinopecDemandOrderBiz;
import com.logistics.tms.controller.demandorder.request.*;
import com.logistics.tms.controller.demandorder.request.sinopec.CancelSinopecDemandV1RequestModel;
import com.logistics.tms.controller.demandorder.request.sinopec.ReceiveSinopecDemandV1RequestModel;
import com.logistics.tms.controller.demandorder.response.BatchPublishSinopecResponseModel;
import com.logistics.tms.controller.demandorder.response.PublishSinopecResponseModel;
import com.logistics.tms.controller.demandorder.response.SinopecReportAbnormalDetailResponseModel;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/3/27 9:39
 */
@Api(value = "中石化需求单管理")
@Slf4j
@RestController
public class SinopecDemandOrderController {

    @Resource
    private SinopecDemandOrderBiz sinopecDemandOrderBiz;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private RedisUtils redisUtils;

    /**
     * 接收中石化委托单
     *
     * @param requestModel
     */
    @ApiModelProperty("中石化推送需求单")
    @PostMapping(value = "/service/sinopec/receiveDemand")
    public Result<Boolean> receiveDemand(@RequestBody ReceiveSinopecDemandRequestModel requestModel) {
        sinopecDemandOrderBiz.receiveDemand(requestModel);
        return Result.success(true);
    }

    /**
     * 中石化主动取消需求单
     *
     * @param requestModel
     */
    @ApiModelProperty("中石化取消需求单")
    @PostMapping(value = "/service/sinopec/cancelDemand")
    public Result<Boolean> cancelDemand(@RequestBody CancelSinopecDemandRequestModel requestModel) {
        sinopecDemandOrderBiz.cancelDemand(requestModel);
        return Result.success(true);
    }

    /**
     * 接收中石化委托单(新)
     *
     * @param requestJson
     * @param ip
     */
    @ApiModelProperty("中石化推送需求单(新)")
    @PostMapping(value = "/service/sinopec/receiveDemandV1")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> receiveDemandV1(@RequestBody String requestJson, @RequestHeader(value = "ip", required = false) String ip, @RequestHeader(value = "sign", required = false) String sign) {
        List<String> associatedNumberList = null;
        try {
            log.info("中石化同步需求单签名: " + sign);
            log.info("中石化同步需求单: " + requestJson);
            boolean signResult = sinopecDemandOrderBiz.checkSinopecSign(requestJson, sign);
            if (!signResult) {
                throw new BizException(EntrustDataExceptionEnum.SINOPEC_SIGN_ERROR);
            }
            //转换原始数据
            List<ReceiveSinopecDemandV1RequestModel> requestModel = objectMapper.readValue(requestJson, new TypeReference<>() {
            });
            associatedNumberList = requestModel.stream().map(ReceiveSinopecDemandV1RequestModel::getAssociatedNumber).collect(Collectors.toList());
            if (ListUtils.isEmpty(associatedNumberList)) {
                throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_NOT_EXIST);
            }
            sinopecDemandOrderBiz.receiveDemandV1(requestModel, associatedNumberList, ip);
        } catch (BizException e) {
            throw new BizException(CommonConstant.NEGATIVE_INTEGER_ONE, e.getMessage());
        } catch (Exception e) {
            log.error("接收中石化委托单V1错误: ", e);
            throw new BizException(CommonConstant.NEGATIVE_INTEGER_ONE, EntrustDataExceptionEnum.REQUEST_PARAM_ERROR.getMsg());
        }finally {
            if (ListUtils.isNotEmpty(associatedNumberList)){
                for (String associatedNumber : associatedNumberList) {
                    //清除当前委托单号在redis中的key
                    redisUtils.delete(CommonConstant.LOGISTICS_SINOPEC_ASSOCIATED + associatedNumber);
                }
            }
        }
        return Result.success(true);
    }

    /**
     * 中石化主动取消需求单(新)
     *
     * @param requestJson
     * @param ip
     */
    @ApiModelProperty("中石化取消需求单(新)")
    @PostMapping(value = "/service/sinopec/cancelDemandV1")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelDemandV1(@RequestBody String requestJson, @RequestHeader(value = "ip", required = false) String ip, @RequestHeader(value = "sign", required = false) String sign) {
        try {
            log.info("中石化取消需求单签名: " + sign);
            log.info("中石化取消需求单: " + requestJson);
            boolean signResult = sinopecDemandOrderBiz.checkSinopecSign(requestJson, sign);
            if (!signResult) {
                throw new BizException(EntrustDataExceptionEnum.SINOPEC_SIGN_ERROR);
            }
            //转换参数
            CancelSinopecDemandV1RequestModel requestModel = objectMapper.readValue(requestJson, CancelSinopecDemandV1RequestModel.class);
            //获取openapi设置的ip
            requestModel.setIp(ip);
            //判断操作码是不是201
            if (!CommonConstant.INTEGER_TWO_HUNDRED_ONE.equals(requestModel.getActionCode())) {
                throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_ACTION_CODE_ERROR);
            }
            //非空判断
            if (ListUtils.isEmpty(requestModel.getAssociatedNoList())) {
                throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_ASSOCIATED_NO_IS_NULL);
            }
            if (requestModel.getAssociatedNoList().size() > CommonConstant.INTEGER_ONE) {
                //不允许大于一条
                throw new BizException(EntrustDataExceptionEnum.SINOPEC_DEMANDORDER_MULTIPLE_CANCEL_ERROR);
            }
            sinopecDemandOrderBiz.cancelDemandV1(requestModel);
        } catch (BizException e) {
            throw new BizException(CommonConstant.NEGATIVE_INTEGER_ONE, e.getMessage());
        } catch (Exception e) {
            log.error("中石化主动取消需求单V1错误: ", e);
            throw new BizException(CommonConstant.NEGATIVE_INTEGER_ONE, EntrustDataExceptionEnum.REQUEST_PARAM_ERROR.getMsg());
        }
        return Result.success(true);
    }

    /**
     * 取消中石化需求单
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "取消中石化需求单")
    @PostMapping(value = "/service/demandOrder/cancelSinopecDemandOrder")
    public Result<Boolean> cancelSinopecDemandOrder(@RequestBody SinopecDemandOrderCancelRequestModel requestModel) {
        sinopecDemandOrderBiz.cancelSinopecDemandOrder(requestModel);
        return Result.success(true);
    }

    /**
     * 获取中石化需求发布详情
     *
     * @param requestModel 单个需求单id
     * @return 需求单发布详情
     */
    @ApiOperation(value = "获取中石化需求发布详情")
    @PostMapping(value = "/service/demandOrder/publishSinopecDetail")
    public Result<PublishSinopecResponseModel> publishSinopecDetail(@RequestBody @Valid PublishSinopecDetailRequestModel requestModel) {
        return Result.success(sinopecDemandOrderBiz.publishSinopecDetail(requestModel));
    }

    /**
     * 批量获取中石化需求单发布详情
     *
     * @param requestModel 多个需求单id
     * @return 需求单发布详情
     */
    @ApiOperation(value = "批量获取中石化需求单发布详情")
    @PostMapping(value = "/service/demandOrder/batchPublishSinopecDetail")
    public Result<BatchPublishSinopecResponseModel> batchPublishSinopecDetail(@RequestBody @Valid BatchPublishSinopecDetailRequestModel requestModel) {
        return Result.success(sinopecDemandOrderBiz.batchPublishSinopecDetail(requestModel));
    }

    /**
     * 批量发布中石化需求单
     *
     * @param requestModel 要发布的需求单信息
     * @return 发布结果
     */
    @ApiOperation(value = "批量发布中石化需求单")
    @PostMapping(value = "/service/demandOrder/batchPublishSinopecDemandOrder")
    public Result<Boolean> batchPublishSinopecDemandOrder(@RequestBody @Valid BatchPublishSinopecDemandRequestModel requestModel) {
        sinopecDemandOrderBiz.batchPublishSinopecDemandOrder(requestModel);
        return Result.success(true);
    }

    /**
     * 单个发布中石化需求单
     *
     * @param requestModel 要发布的需求单信息
     * @return 发布结果
     */
    @ApiOperation(value = "单个发布中石化需求单")
    @PostMapping(value = "/service/demandOrder/publishSinopecDemandOrder")
    public Result<Boolean> publishSinopecDemandOrder(@RequestBody @Valid PublishSinopecDemandRequestModel requestModel) {
        sinopecDemandOrderBiz.publishSinopecDemandOrder(requestModel);
        return Result.success(true);
    }


    /**
     * 中石化需求单上报异常详情查询
     *
     * @param requestModel 需求单id
     * @return 需求单详情列表
     */
    @ApiOperation(value = "中石化需求单上报异常详情查询")
    @PostMapping(value = "/service/demandOrder/sinopecReportAbnormalDetail")
    public Result<SinopecReportAbnormalDetailResponseModel> sinopecReportAbnormalDetail(@RequestBody @Valid SinopecReportAbnormalDetailRequestModel requestModel) {
        return Result.success(sinopecDemandOrderBiz.sinopecReportAbnormalDetail(requestModel));
    }



    /**
     * 中石化需求单异常上报
     *
     * @param requestModel 异常上报信息
     * @return 操作结果
     */
    @ApiOperation(value = "中石化需求单异常上报")
    @PostMapping(value = "/service/demandOrder/saveSinopecReportAbnormal")
    public Result<Boolean> saveSinopecReportAbnormal(@RequestBody @Valid SaveSinopecReportAbnormalRequestModel requestModel) {
        sinopecDemandOrderBiz.saveSinopecReportAbnormal(requestModel);
        return Result.success(true);
    }

}
