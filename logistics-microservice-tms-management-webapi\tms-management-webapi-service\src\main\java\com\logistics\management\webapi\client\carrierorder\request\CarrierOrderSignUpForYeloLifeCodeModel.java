package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CarrierOrderSignUpForYeloLifeCodeModel {
    @ApiModelProperty(value = "code", required = true)
    private String yeloCode;

    @ApiModelProperty(value = "重量 对应实提实卸实签", required = true)
    private BigDecimal weight;

    @ApiModelProperty(value = "单位 1.kg", required = true)
    private Integer unit;
}
