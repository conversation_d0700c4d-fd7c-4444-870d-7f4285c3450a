package com.logistics.tms.base.enums;

public enum RabbitMqExChangeTypeEnum {

    CARRIER_ORDER_EXCHANGE("logistics.carrierorder.exchange","运单信息同步交换机"),
    LOGISTICS_TOPIC("logistics.topic","物流消息交换机"),
//    LOGISTICS_CARRIER_TOPIC("logistics.carrier.topic","承运商交换机"),
    FINANCIAL_TOPIC("financial.topic","托盘财务交换机"),
    LOGISTICS_QIYA_TMS_TOP("logistics.qiyatms.topic","TMS交换机"),
    ORDER_TOPIC("order.topic","托盘订单交换机"),
    LIFE_ORDER_TOPIC("life.order.topic","新生订单交换机"),
    LIFE_FINANCIAL_TOPIC("life.financial.topic","新生财务交换机"),
    GROUND_PUSH_TOPIC("ground-push.topic","地推系统交换机"),
    WAREHOUSE_STOCK_TOPIC("warehouse-stock.topic","云仓仓库模块交换机"),
    BASIC_DATA_TOPIC("basicData.topic","基础数据服务消息交换机"),
    DELAY_QUEUE_TOPIC("queue.topic", "延时队列交换器"),
    ;

    private String key;
    private String value;

    RabbitMqExChangeTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }


}
