package com.logistics.management.webapi.controller.workgroup.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AddEditWorkGroupDistrictRequestDto {

    @ApiModelProperty("区域配置id,编辑时填写")
    private String workGroupDistrictId;

    @ApiModelProperty(value = "省份id", required = true)
    @NotBlank(message = "请选择省份")
    private String provinceId;

    @ApiModelProperty(value = "省份名", required = true)
    @NotBlank(message = "请选择省份")
    private String provinceName;

    @ApiModelProperty(value = "市id", required = true)
    @NotBlank(message = "请选择市")
    private String cityId;

    @ApiModelProperty(value = "市名", required = true)
    @NotBlank(message = "请选择市")
    private String cityName;
}
