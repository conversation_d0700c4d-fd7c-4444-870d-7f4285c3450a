package com.logistics.tms.mapper;

import com.logistics.tms.biz.drivercostapply.model.SelectByCodeAndNumModel;
import com.logistics.tms.controller.drivercostapply.response.DriverCostApplyInvoiceResponseModel;
import com.logistics.tms.entity.TDriverCostApplyInvoice;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2023/07/31
*/
@Mapper
public interface TDriverCostApplyInvoiceMapper extends BaseMapper<TDriverCostApplyInvoice> {

    void batchInsertAndReturnId(@Param("List") List<TDriverCostApplyInvoice> tDriverCostApplyInvoicesInsertList);

    List<TDriverCostApplyInvoice> selectByDriverCostApplyId(@Param("driverCostApplyId") Long driverCostApplyId);

    List<DriverCostApplyInvoiceResponseModel> selectInvoiceInfoByCostApplyId(@Param("driverCostApplyId") Long driverCostApplyId);

    List<TDriverCostApplyInvoice> selectByCodeAndNum(@Param("list") List<SelectByCodeAndNumModel> list);

}