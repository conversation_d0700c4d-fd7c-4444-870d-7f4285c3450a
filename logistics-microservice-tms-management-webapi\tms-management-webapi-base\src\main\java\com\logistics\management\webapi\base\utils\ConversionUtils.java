package com.logistics.management.webapi.base.utils;

import lombok.extern.slf4j.Slf4j;

/**
 *  短链接生成
 *  10进制,62进制互转
 **/

@Slf4j
public class ConversionUtils {
    /**
     * 初始化 62 进制数据，索引位置代表字符的数值，比如 A代表10,z代表61等
     */
    private static final String chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final int scale = 62;

    /**
     * 将数字转为62进制

     * 对scale (62)进行求余，然后将余数追加至 sb 中
     * 由于是从末位开始追加的，因此最后需要反转（reverse）字符串
     *
     * @param num    Long 型数字
     * @return 62进制字符串
     */
    public static String base62(long num) {
        log.info("encode param: {}", num);
        int remainder;
        StringBuilder sb = new StringBuilder();
        while (num > scale - 1) {
            remainder = (int)(num % scale);
            sb.append(chars.charAt(remainder));
            num = num / scale;
        }

        sb.append(chars.charAt((int) num));
        log.info("encode result: {}", sb.reverse().toString());
        return sb.reverse().toString().substring(0,6);
    }

}