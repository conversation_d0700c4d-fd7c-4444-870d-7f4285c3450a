package com.logistics.management.webapi.api.feign.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CertificatePictureDto {
    @ApiModelProperty("图片ID")
    private String objectId;
    @ApiModelProperty("图片类型名")
    private String fileTypeName;
    @ApiModelProperty("图片路径")
    private String filePath;
    @ApiModelProperty("图片路径")
    private String filePathSrc;
    @ApiModelProperty("上传人姓名")
    private String uploadUserName;
    @ApiModelProperty("上传时间")
    private String uploadTime;

}
