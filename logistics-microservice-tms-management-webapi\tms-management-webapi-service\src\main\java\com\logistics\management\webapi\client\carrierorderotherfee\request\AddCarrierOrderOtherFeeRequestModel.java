package com.logistics.management.webapi.client.carrierorderotherfee.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AddCarrierOrderOtherFeeRequestModel {

    @ApiModelProperty(value = "运单主表ID" ,required = true)
    private Long carrierOrderId;

    @ApiModelProperty(value = "临时费用明细" ,required = true)
    private List<AddCarrierOrderOtherFeeItemRequestModel> otherFeeList;

    @ApiModelProperty("请求来源：1 后台，2 前台，3 小程序")
    private String requestSource;
}
