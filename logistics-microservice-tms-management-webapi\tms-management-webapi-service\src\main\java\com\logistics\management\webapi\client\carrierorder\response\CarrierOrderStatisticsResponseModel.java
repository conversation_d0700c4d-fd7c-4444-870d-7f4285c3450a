package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/22
 */
@Data
public class CarrierOrderStatisticsResponseModel {

	@ApiModelProperty("全部数量")
	private Integer allCount = 0;

	@ApiModelProperty("待提货")
	private Integer waitPickCount = 0;

	@ApiModelProperty("待卸货")
	private Integer waitUnloadCount = 0;

	@ApiModelProperty("待签收")
	private Integer waitSignCount = 0;

	@ApiModelProperty("已签收")
	private Integer signCount = 0;

	@ApiModelProperty("已取消")
	private Integer cancelCount = 0;

	@ApiModelProperty("已放空")
	private Integer emptyCount = 0;
}
