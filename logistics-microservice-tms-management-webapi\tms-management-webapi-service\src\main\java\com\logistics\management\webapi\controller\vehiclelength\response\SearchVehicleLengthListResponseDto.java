package com.logistics.management.webapi.controller.vehiclelength.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchVehicleLengthListResponseDto {

    /**
     * 车长配置id
     */
    @ApiModelProperty("车长配置id")
    private String vehicleLengthId="";

    /**
     * 车长
     */
    @ApiModelProperty("车长")
    private String vehicleLength="";

    /**
     * 承运范围-低
     */
    @ApiModelProperty("承运范围-低")
    private String carriageScopeMin="";

    /**
     * 承运范围-高
     */
    @ApiModelProperty("承运范围-高")
    private String carriageScopeMax="";

    /**
     * 最后操作人
     */
    @ApiModelProperty("最后操作人")
    private String lastModifiedBy="";

    /**
     * 最后操作时间
     */
    @ApiModelProperty("最后操作时间")
    private String lastModifiedTime="";
}
