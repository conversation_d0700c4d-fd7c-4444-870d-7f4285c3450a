package com.logistics.management.webapi.client.sysconfig;

import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.sysconfig.hystrix.SysConfigClientHystrix;
import com.logistics.management.webapi.client.sysconfig.request.SysConfigEditRequestModel;
import com.logistics.management.webapi.client.sysconfig.request.SysConfigRequestModel;
import com.logistics.management.webapi.client.sysconfig.response.SysConfigResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.List;


/**
 * @author: wjf
 * @date: 2024/3/7 17:13
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/sysConfig",
        fallback = SysConfigClientHystrix.class)
public interface SysConfigClient {

    /**
     * 获取系统配置
     * @param requestModel 请求Model
     * @return 配置 Value
     */
    @ApiOperation(value = "获取系统配置", tags = "1.2.6")
    @PostMapping(value = "/get")
    Result<String> getSysConfig(@RequestBody SysConfigRequestModel requestModel);

    /**
     * 批量获取配置
     * @param requestModel 请求Model
     * @return 配置列表
     */
    @ApiOperation(value = "批量获取系统配置", tags = "1.2.6")
    @PostMapping(value = "/batchGet")
    Result<List<SysConfigResponseModel>> batchGetSysConfig(@RequestBody Collection<SysConfigRequestModel> requestModel);

    /**
     * 修改系统配置
     * @param requestModel 请求 Model
     * @return true 成功 false 失败
     */
    @ApiOperation(value = "系统配置编辑", tags = "1.2.6")
    @PostMapping(value = "/edit")
    Result<Boolean> editSysConfig(@RequestBody SysConfigEditRequestModel requestModel);
}
