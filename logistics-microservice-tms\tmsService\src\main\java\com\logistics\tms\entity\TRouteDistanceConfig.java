package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2023/06/30
*/
@Data
public class TRouteDistanceConfig extends BaseEntity {
    /**
    * 发货省份ID
    */
    @ApiModelProperty("发货省份ID")
    private Long fromProvinceId;

    /**
    * 发货省份名字
    */
    @ApiModelProperty("发货省份名字")
    private String fromProvinceName;

    /**
    * 发货城市ID
    */
    @ApiModelProperty("发货城市ID")
    private Long fromCityId;

    /**
    * 发货城市名字
    */
    @ApiModelProperty("发货城市名字")
    private String fromCityName;

    /**
    * 发货县区ID
    */
    @ApiModelProperty("发货县区ID")
    private Long fromAreaId;

    /**
    * 发货县区名字
    */
    @ApiModelProperty("发货县区名字")
    private String fromAreaName;

    /**
    * 卸货省份ID
    */
    @ApiModelProperty("卸货省份ID")
    private Long toProvinceId;

    /**
    * 卸货省份名字
    */
    @ApiModelProperty("卸货省份名字")
    private String toProvinceName;

    /**
    * 卸货城市ID
    */
    @ApiModelProperty("卸货城市ID")
    private Long toCityId;

    /**
    * 卸货城市名字
    */
    @ApiModelProperty("卸货城市名字")
    private String toCityName;

    /**
    * 卸货县区id
    */
    @ApiModelProperty("卸货县区id")
    private Long toAreaId;

    /**
    * 卸货县区名字
    */
    @ApiModelProperty("卸货县区名字")
    private String toAreaName;

    /**
    * 计费距离
    */
    @ApiModelProperty("计费距离")
    private BigDecimal billingDistance;
}