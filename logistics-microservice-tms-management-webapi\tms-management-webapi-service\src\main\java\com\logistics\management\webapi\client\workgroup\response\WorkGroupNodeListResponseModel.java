package com.logistics.management.webapi.client.workgroup.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/22 15:44
 */
@Data
public class WorkGroupNodeListResponseModel {

    @ApiModelProperty(value = "节点id")
    private Long workGroupNodeId;

    @ApiModelProperty(value = "单据类型, 10:需求单 20:运单")
    private Integer orderType;

    @ApiModelProperty(value = "单据节点, 101:下单 102:调度 103:取消；201:提货 202:纠错 203:取消 204:生成运单 205:修改车辆 206:卸货 207:到达提货地 208:到达卸货地")
    private Integer orderNode;

    @ApiModelProperty(value = "时间要求,仅需求单&下单节点展示此字段并必填")
    private Integer timeRequire;

    @ApiModelProperty(value = "取值要求, 101:委托数 102:已安排 103:未安排；201:预提数 202:实提数 203:差异数(预提-实提)")
    private Integer amountType;

    @ApiModelProperty(value = "取值要求符号, 1: >= 2: = 3: <=")
    private Integer amountSymbol;

    @ApiModelProperty(value = "取值要求,0~10000整数")
    private BigDecimal amountRequire;

    @ApiModelProperty(value = "模板字段")
    private List<String> fieldList;
}
