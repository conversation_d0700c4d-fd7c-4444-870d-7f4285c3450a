package com.logistics.appapi.client.website.entrust.hystrix;

import com.logistics.appapi.client.website.entrust.EntrustSourceClient;
import com.logistics.appapi.client.website.entrust.request.AddEntrustSourceRequestModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/15 11:00
 */
@Component
public class EntrustSourceClientHystrix implements EntrustSourceClient {
    @Override
    public Result addEntrustSource(AddEntrustSourceRequestModel requestModel) {
        return Result.timeout();
    }
}
