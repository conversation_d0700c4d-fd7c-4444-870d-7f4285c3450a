package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TVehicleRoadTransportCertificate;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

@Mapper
public interface TVehicleRoadTransportCertificateMapper extends BaseMapper<TVehicleRoadTransportCertificate> {

    TVehicleRoadTransportCertificate getByVehicleId(@Param("vehicleId") Long vehicleId);

    int deleteRoadTransportCertificateInfo(@Param("vehicleIds") String vehicleIds, @Param("modifiedBy") String modifiedBy, @Param("modifiedTime") Date modifiedTime);

    int updateByPrimaryKeySelectiveExt(@Param("params") TVehicleRoadTransportCertificate vehicleRoadTransportCertificate);

    int updateByPrimaryKeySelectiveForExcel(@Param("params") TVehicleRoadTransportCertificate vehicleRoadTransportCertificate);

    List<TVehicleRoadTransportCertificate> getByVehicleIds(@Param("vehicleIds") String vehicleIds);
}