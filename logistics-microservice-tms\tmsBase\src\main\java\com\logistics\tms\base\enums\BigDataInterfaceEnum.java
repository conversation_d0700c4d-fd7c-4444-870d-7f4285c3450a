package com.logistics.tms.base.enums;

/**
 * 大数据提供接口枚举
 * @author: wjf
 * @date: 2022/9/1 13:52
 */
public enum BigDataInterfaceEnum {

    USER_AUTH("/api/user/auth","智能物流鉴权"),
    SEARCH_CONSIGNOR_LIST("/yelolife/searchConsignorList","物流⼩程序下单-查询发货⼈地址（根据当前位置查询新⽣客⼾地址，按最近距离 排序）"),
    SEARCH_WAREHOUSE_LIST("/yelolife/searchWarehouseList","物流⼩程序下单-查询收货仓库（根据发货⼈地址最近距离排序，查询云仓仓库 （和新⽣⼯⼚）地址信息"),
    SEARCH_FIXED_INFO("/api/vehicle/searchParkingInfoByWhsCode","根据仓库code批量查询查派车装载量，车牌号，承运商"),
    RECYCLE_PUBLISH_UPDATE_DEMAND("/api/recyclePublishConfig/insetAndUpdateDemandInfo","更新智慧运营回收配置关联的需求单信息"),
    SEARCH_AUTO_PUBLISH_INFO("/api/recyclePublishConfig/getConfigInfoDetails","根据自动发布配置code获取配置信息"),
    PATH_PLAN("/math/path_plan","路径规划"),
    ;

    private String key;
    private String value;

    BigDataInterfaceEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
