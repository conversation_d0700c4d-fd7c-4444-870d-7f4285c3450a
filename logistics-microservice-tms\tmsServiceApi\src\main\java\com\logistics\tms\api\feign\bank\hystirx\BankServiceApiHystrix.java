package com.logistics.tms.api.feign.bank.hystirx;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.bank.BankServiceApi;
import com.logistics.tms.api.feign.bank.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/7/10 13:54
 */
@Component("tmsBankServiceApiHystrix")
public class BankServiceApiHystrix implements BankServiceApi {

    @Override
    public Result<PageInfo<SearchBankResponseModel>> searchBankList(SearchBankRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveOrModifyBank(SaveOrModifyBankRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<BankDetailResponseModel> getDetail(BankDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enableOrDisable(EnableBankRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchBankResponseModel>> export(SearchBankRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ImportBankResponseModel> importBank(ImportBankRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<FuzzyQueryBankListResponseModel>> fuzzyQueryBank(FuzzyQueryBankRequestModel requestModel) {
        return Result.timeout();
    }
}
