package com.logistics.management.webapi.client.companycarrierauthorization.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CarrierAuthorizationAddRequestModel {

    @ApiModelProperty(value = "车主id", required = true)
    private Long companyCarrierId;

    @ApiModelProperty(value = "授权书图片", required = true)
    private List<String> authorizationImageList;

    @ApiModelProperty(value = "是否归档, 0:否 1:是", required = true)
    private Integer isArchived;

    @ApiModelProperty(value = "备注")
    private String remark;
}
