<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderEventsMapper">
    <insert id="batchInsertSelective" parameterType="com.logistics.tms.entity.TDemandOrderEvents">
        <foreach collection="list" item="item" separator=";">
            insert into t_demand_order_events
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.demandOrderId != null">
                    demand_order_id,
                </if>
                <if test="item.companyCarrierId != null">
                    company_carrier_id,
                </if>
                <if test="item.event != null">
                    event,
                </if>
                <if test="item.eventDesc != null">
                    event_desc,
                </if>
                <if test="item.eventTime != null">
                    event_time,
                </if>
                <if test="item.operatorName != null">
                    operator_name,
                </if>
                <if test="item.operateTime != null">
                    operate_time,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderId != null">
                    #{item.demandOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.companyCarrierId != null">
                    #{item.companyCarrierId,jdbcType=BIGINT},
                </if>
                <if test="item.event != null">
                    #{item.event,jdbcType=INTEGER},
                </if>
                <if test="item.eventDesc != null">
                    #{item.eventDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.eventTime != null">
                    #{item.eventTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.operatorName != null">
                    #{item.operatorName,jdbcType=VARCHAR},
                </if>
                <if test="item.operateTime != null">
                    #{item.operateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="getDemandOrderEventsByDemandId" resultType="com.logistics.tms.controller.demandorder.response.DemandOrderEventResponseModel">
        SELECT
        event      as event,
        event_desc as eventDesc,
        event_time as eventTime
        from t_demand_order_events
        where valid = 1
        and demand_order_id = #{demandId,jdbcType=BIGINT}
        and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        order by event_time asc, id asc
    </select>
</mapper>