package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/9/26 current system date
 */
@Data
public class DriverAndVehicleResponseModel {

	@ApiModelProperty("运单Id")
	private Long carrierOrderId;

	@ApiModelProperty("有效车辆历史记录ID")
	private Long vehicleHistoryId;

	@ApiModelProperty("司机id")
	private Long driverId;

	@ApiModelProperty("司机名字")
	private String driverName;

	@ApiModelProperty("司机手机号")
	private String driverPhone;

	@ApiModelProperty("司机身份证")
	private String driverIdentityNumber;

	@ApiModelProperty("车辆id")
	private Long vehicleId;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("挂车车辆ID")
	private Long trailerVehicleId;

	@ApiModelProperty("挂车车牌号")
	private String trailerVehicleNo;

	@ApiModelProperty("车辆机构 1 自主，2 外部，3 自营")
	private Integer vehicleProperty;

	@ApiModelProperty("承运商id")
	private Long companyCarrierId;

	@ApiModelProperty("是否我司 1:我司,2:其他车主")
	private Integer isOurCompany;
}
