package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2018/11/6 11:12
 */
@Data
public class DeleteTicketsRequestDto {
    @ApiModelProperty("运单ID")
    @NotBlank(message = "运单ID不能为空")
    private String carrierOrderId;
    @ApiModelProperty("图片Id")
    @NotBlank(message = "图片id不能为空")
    private String imageId;
}
