package com.logistics.management.webapi.base.enums;

public enum  ContractTypeEnum {
    DEFAULT_VALUE(0,""),
    FRAME_CONTRACT(1, "框架合同"),
    SINGLE_CONTRACT(2, "单次合同"),
    ;
    private Integer key;
    private String value;

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    ContractTypeEnum(Integer key, String value) {

        this.key = key;
        this.value = value;
    }

    public static ContractTypeEnum getEnum(Integer key) {
        for (ContractTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
