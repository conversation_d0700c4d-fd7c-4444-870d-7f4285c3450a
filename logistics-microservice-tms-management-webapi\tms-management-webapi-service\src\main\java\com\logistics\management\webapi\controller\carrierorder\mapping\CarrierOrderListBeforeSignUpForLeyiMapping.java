package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.carrierorder.response.CarrierOrderListBeforeSignUpForLeyiResponseModel;
import com.logistics.management.webapi.controller.carrierorder.response.CarrierOrderListBeforeSignUpForLeyiResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/5
 */
public class CarrierOrderListBeforeSignUpForLeyiMapping extends MapperMapping<CarrierOrderListBeforeSignUpForLeyiResponseModel, CarrierOrderListBeforeSignUpForLeyiResponseDto> {
	@Override
	public void configure() {
		CarrierOrderListBeforeSignUpForLeyiResponseModel source = getSource();
		CarrierOrderListBeforeSignUpForLeyiResponseDto destination = getDestination();

		if (source != null) {
			destination.setEntrustType(EntrustTypeEnum.getEnum(source.getEntrustType()).getValue());
			if (CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyCarrierType())) {
				//企业类型
				destination.setCarrierCompany(source.getCompanyCarrierName());
			} else {
				//个人类型
				destination.setCarrierCompany(source.getCarrierContactName() + " " + source.getCarrierContactMobile());
			}

			//单位转换
			destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());

			//数量
			destination.setAmountTotal(source.getUnloadAmount().stripTrailingZeros().toPlainString());


			//货主实际结算数量
			BigDecimal entrustSettlementAmount = BigDecimal.ZERO;
			if (SettlementTonnageEnum.LOAD.getKey().equals(source.getSettlementTonnage())) {
				entrustSettlementAmount = source.getLoadAmount();
			} else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getSettlementTonnage())) {
				entrustSettlementAmount = source.getUnloadAmount();
			} else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getSettlementTonnage())) {
				entrustSettlementAmount = source.getUnloadAmount();
			} else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getSettlementTonnage())) {
				entrustSettlementAmount = source.getExpectAmount();
			}
			//货主费用
			BigDecimal actualEntrustFee = BigDecimal.ZERO;
			if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getEntrustFreightType())){
				actualEntrustFee = source.getEntrustFreight().multiply(entrustSettlementAmount).setScale(2,BigDecimal.ROUND_HALF_UP);
			}else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getEntrustFreightType())){
				actualEntrustFee = source.getEntrustFreight();
			}
			destination.setActualEntrustFee(ConverterUtils.toString(actualEntrustFee));

			//车主实际结算数量
			BigDecimal carrierSettlementAmount = BigDecimal.ZERO;
			if (SettlementTonnageEnum.LOAD.getKey().equals(source.getCarrierSettlement())) {
				carrierSettlementAmount = source.getLoadAmount();
			} else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getCarrierSettlement())) {
				carrierSettlementAmount = source.getUnloadAmount();
			} else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getCarrierSettlement())) {
				carrierSettlementAmount = source.getUnloadAmount();
			} else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getCarrierSettlement())) {
				carrierSettlementAmount = source.getExpectAmount();
			}
			//车主费用
			BigDecimal carrierFreight = null;
			if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getCarrierPriceType())){
				carrierFreight = source.getCarrierPrice().multiply(carrierSettlementAmount).setScale(2,BigDecimal.ROUND_HALF_UP);
			}else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getCarrierPriceType())){
				carrierFreight = source.getCarrierPrice();
			}
			if (carrierFreight != null){
				destination.setCarrierFreight(carrierFreight.compareTo(BigDecimal.ZERO) == CommonConstant.INTEGER_ZERO ? CommonConstant.BLANK_TEXT : ConverterUtils.toString(carrierFreight));
				destination.setSignCarrierFreight(ConverterUtils.toString(carrierFreight.add(source.getOtherFee()).setScale(2, RoundingMode.HALF_UP)));
			}else{
				destination.setSignCarrierFreight(CommonConstant.ZERO);
			}

			//我司
			if (CommonConstant.INTEGER_ONE.equals(source.getIsOurCompany())) {
				//司机费用 ,货主费用+临时费用
				destination.setDispatchFreightFeeTotal(actualEntrustFee.add(source.getOtherFee()).setScale(2, RoundingMode.HALF_UP).toString());
			} else {
				//其他车主
				destination.setDispatchFreightFeeTotal(CommonConstant.ZERO);
			}
		}
	}
}
