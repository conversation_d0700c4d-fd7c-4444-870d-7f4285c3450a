package com.logistics.tms.biz.attendanceaskleave;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.LeaveApplyAuditStatusEnum;
import com.logistics.tms.base.enums.LeaveApplyTimeTypeEnum;
import com.logistics.tms.base.enums.StaffPropertyEnum;
import com.logistics.tms.biz.attendanceaskleave.model.LeaveApplyCancelModel;
import com.logistics.tms.biz.attendanceaskleave.model.LeaveApplySubmitModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.leave.request.*;
import com.logistics.tms.controller.leave.response.*;
import com.logistics.tms.entity.TAttendanceAskLeave;
import com.logistics.tms.entity.TStaffBasic;
import com.logistics.tms.mapper.TAttendanceAskLeaveMapper;
import com.logistics.tms.mapper.TStaffBasicMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.ObjectUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

@Service
public class AttendanceAskLeaveBiz {

    @Resource
    private CommonBiz commonBiz;
    @Resource
    private TStaffBasicMapper staffBasicMapper;
    @Resource
    private TAttendanceAskLeaveMapper attendanceAskLeaveMapper;

    /**
     * 请假申请
     *
     * @param requestModel
     * @return boolean
     */
    @Transactional
    public boolean driverLeaveApply(DriverLeaveApplyRequestModel requestModel) {

        // 获取员工id
        Long loginDriverAppletUserId = getLoginDriverAppletUserId();
        // 获取员工信息
        TStaffBasic staff = staffBasicMapper.getStaffByIds(loginDriverAppletUserId.toString())
                .stream()
                // 过滤出自营自主员工
                .filter(f -> {
                    return StaffPropertyEnum.OWN_STAFF.getKey().equals(f.getStaffProperty()) ||
                            StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(f.getStaffProperty());
                })
                .findFirst()
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST));

        // 参数校验
        LeaveApplySubmitModel boModel = MapperUtils.mapper(requestModel, LeaveApplySubmitModel.class);
        boModel.setStaffId(loginDriverAppletUserId);
        checkLeaveApply(boModel);

        // 构建model
        TAttendanceAskLeave leave = new TAttendanceAskLeave();
        leave.setStaffId(loginDriverAppletUserId);
        leave.setStaffName(staff.getName());
        leave.setStaffMobile(staff.getMobile());
        leave.setStaffProperty(staff.getStaffProperty());

        leave.setLeaveType(requestModel.getLeaveType());
        leave.setLeaveStartTime(requestModel.getLeaveStartTime());
        leave.setLeaveStartTimeType(requestModel.getLeaveStartTimeType());
        leave.setLeaveEndTime(requestModel.getLeaveEndTime());
        leave.setLeaveEndTimeType(requestModel.getLeaveEndTimeType());
        leave.setLeaveDuration(boModel.getLeaveDuration());
        leave.setLeaveReason(requestModel.getLeaveReason());
        leave.setApplyTime(new Date());
        commonBiz.setBaseEntityAdd(leave, BaseContextHandler.getUserName());
        attendanceAskLeaveMapper.insertSelective(leave);
        return true;
    }

    // 提交请假申请校验
    private void checkLeaveApply(LeaveApplySubmitModel boModel) {

        LocalDate leaveApplyStartDate = LocalDate.ofInstant(boModel.getLeaveStartTime().toInstant(), ZoneId.systemDefault());
        LocalDate leaveApplyEndDate = LocalDate.ofInstant(boModel.getLeaveEndTime().toInstant(), ZoneId.systemDefault());

        // 结束时间需在开始时间之后
        if (leaveApplyEndDate.isBefore(leaveApplyStartDate) ||
                (leaveApplyStartDate.isEqual(leaveApplyEndDate) && boModel.getLeaveStartTimeType() > boModel.getLeaveEndTimeType())) {
            throw new BizException(CarrierDataExceptionEnum.LEAVE_APPLY_TIME_CROSS);
        }

        // 校验申请时间是否超过30天
        BigDecimal duration = BigDecimal.valueOf(leaveApplyStartDate.until(leaveApplyEndDate, ChronoUnit.DAYS));
        if (boModel.getLeaveStartTimeType().equals(boModel.getLeaveEndTimeType())) {
            duration = duration.add(CommonConstant.ZERO_POINT_FIVE);
        } else if (LeaveApplyTimeTypeEnum.MORNING.getKey().equals(boModel.getLeaveStartTimeType())) {
            duration = duration.add(CommonConstant.BIG_DECIMAL_ONE);
        }
        if (duration.compareTo(CommonConstant.BIG_DECIMAL_THIRTY) > CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.LEAVE_APPLY_TIME_TOO_LONG);
        }
        boModel.setLeaveDuration(duration);

        // 请假时间不能重复或包含(待审核 | 已审核)
        LocalDateTime leaveApplyStartLocalDateTime = LocalDateTime.of(leaveApplyStartDate,
                LeaveApplyTimeTypeEnum.getLocalTime(boModel.getLeaveStartTimeType()));
        LocalDateTime leaveApplyEndLocalDateTime = LocalDateTime.of(leaveApplyEndDate,
                LeaveApplyTimeTypeEnum.getLocalTime(boModel.getLeaveEndTimeType()));

        List<Integer> audiStatus = Lists.newArrayList(LeaveApplyAuditStatusEnum.WAIT_AUDIT.getKey(), LeaveApplyAuditStatusEnum.AUDIT_THROUGH.getKey());
        List<TAttendanceAskLeave> applyRecord = attendanceAskLeaveMapper.selectAllByAuditStatusIn(boModel.getStaffId(), audiStatus);
        if (ListUtils.isNotEmpty(applyRecord)) {
            applyRecord.forEach(f -> {
                LocalDateTime leaveStartLocalDateTime = LocalDateTime.of(LocalDate.ofInstant(f.getLeaveStartTime().toInstant(),
                                ZoneId.systemDefault()), LeaveApplyTimeTypeEnum.getLocalTime(f.getLeaveStartTimeType()));
                LocalDateTime leaveEndLocalDateTime = LocalDateTime.of(LocalDate.ofInstant(f.getLeaveEndTime().toInstant(),
                                ZoneId.systemDefault()), LeaveApplyTimeTypeEnum.getLocalTime(f.getLeaveEndTimeType()));
                if (!(leaveApplyEndLocalDateTime.isBefore(leaveStartLocalDateTime) || leaveApplyStartLocalDateTime.isAfter(leaveEndLocalDateTime))) {
                    throw new BizException(CarrierDataExceptionEnum.LEAVE_APPLY_TIME_CONFLICT);
                }
            });
        }
    }

    // 获取员工id
    private Long getLoginDriverAppletUserId() {
        // 获取员工id
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
        }
        return loginDriverAppletUserId;
    }

    /**
     * 查询请假申请列表
     *
     * @param requestModel
     * @return LeaveApplyListResponseModel
     */
    public DriverLeaveApplyListResponseModel driverLeaveApplyList(DriverLeaveApplyListRequestModel requestModel) {

        DriverLeaveApplyListResponseModel responseModel = new DriverLeaveApplyListResponseModel();

        // 获取员工id
        Long loginDriverAppletUserId = getLoginDriverAppletUserId();

        // 统计申请次数(待审核 | 已审核)
        Long leaveApplyNumber = attendanceAskLeaveMapper.selectLeaveApplyListStatistical(loginDriverAppletUserId, requestModel.getLeaveDate());
        responseModel.setLeaveApplyCount(leaveApplyNumber.intValue());

        // 分页查询列表
        requestModel.enablePaging();
        List<DriverLeaveApplyListItemModel> leaveApplyListItem = attendanceAskLeaveMapper.selectLeaveApplyList(loginDriverAppletUserId, requestModel.getLeaveDate());
        responseModel.setLeaveApplyListItem(new PageInfo<>(leaveApplyListItem));
        return responseModel;
    }

    /**
     * 请假申请详情
     *
     * @param requestModel
     * @return LeaveApplyDetailResponseModel
     */
    public DriverLeaveApplyDetailResponseModel driverLeaveApplyDetail(DriverLeaveApplyDetailRequestModel requestModel) {
        // 获取员工ID
        Long loginDriverAppletUserId = getLoginDriverAppletUserId();
        DriverLeaveApplyDetailResponseModel leaveApplyDetail = attendanceAskLeaveMapper.selectDriverLeaveApplyDetail(requestModel.getLeaveApplyId(),
                loginDriverAppletUserId);
        if (ObjectUtils.isEmpty(leaveApplyDetail)) {
            throw new BizException(CarrierDataExceptionEnum.LEAVE_APPLY_NOT_EXISTS);
        }
        return leaveApplyDetail;
    }

    /**
     * 重新提交请假申请
     *
     * @param requestModel
     * @return boolean
     */
    @Transactional
    public boolean driverResubmitLeaveApply(DriverLeaveApplyResubmitRequestModel requestModel) {

        // 获取员工id
        Long loginDriverAppletUserId = getLoginDriverAppletUserId();
        // 获取员工信息
        staffBasicMapper.getStaffByIds(loginDriverAppletUserId.toString())
                .stream()
                // 过滤出自营自主员工
                .filter(f -> {
                    return StaffPropertyEnum.OWN_STAFF.getKey().equals(f.getStaffProperty()) ||
                            StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(f.getStaffProperty());
                })
                .findFirst()
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST));

        // 参数校验
        LeaveApplySubmitModel boModel = MapperUtils.mapper(requestModel, LeaveApplySubmitModel.class);
        boModel.setStaffId(loginDriverAppletUserId);
        checkLeaveApply(boModel);

        // 查询当前申请审核状态
        Integer currentAuditStatus = getDriverLeaveApply.apply(requestModel.getLeaveApplyId())
                .map(TAttendanceAskLeave::getAuditStatus)
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.LEAVE_APPLY_NOT_EXISTS));

        // 校验是否是已驳回状态
        if (!LeaveApplyAuditStatusEnum.AUDIT_REJECT.getKey().equals(currentAuditStatus)) {
            throw new BizException(CarrierDataExceptionEnum.LEAVE_APPLY_NOT_UNABLE_SUBMIT);
        }

        // 构建model
        TAttendanceAskLeave updateLeave = new TAttendanceAskLeave();
        updateLeave.setLeaveType(requestModel.getLeaveType());
        updateLeave.setLeaveStartTime(requestModel.getLeaveStartTime());
        updateLeave.setLeaveEndTime(requestModel.getLeaveEndTime());
        updateLeave.setLeaveDuration(boModel.getLeaveDuration());
        updateLeave.setLeaveReason(requestModel.getLeaveReason());
        updateLeave.setAuditStatus(LeaveApplyAuditStatusEnum.WAIT_AUDIT.getKey());

        // 更新请假申请
        updateLeaveApplyByAudit(updateLeave, requestModel.getLeaveApplyId(), currentAuditStatus);
        return true;
    }

    // 根据id查询请假申请
    private Function<Long, Optional<TAttendanceAskLeave>> getLeaveApply = (leaveApplyId) -> {
        return Optional.ofNullable(attendanceAskLeaveMapper.selectOneById(leaveApplyId));
    };

    /**
     * 司机撤销请假申请
     *
     * @param requestModel
     * @return boolean
     */
    @Transactional
    public boolean driverCancelLeaveApply(DriverLeaveApplyCancelRequestModel requestModel) {
        // 撤销申请
        LeaveApplyCancelModel cancelModel = MapperUtils.mapper(requestModel, LeaveApplyCancelModel.class);
        cancelLeave(cancelModel, getDriverLeaveApply);
        return true;
    }

    // 查询司机请假申请
    private Function<Long, Optional<TAttendanceAskLeave>> getDriverLeaveApply = (leaveApplyId) -> {
        Long loginDriverAppletUserId = getLoginDriverAppletUserId();
        return Optional.ofNullable(attendanceAskLeaveMapper.selectOneByIdAndStaffId(leaveApplyId, loginDriverAppletUserId));
    };

    // 撤销请假
    private void cancelLeave(LeaveApplyCancelModel boModel, Function<Long, Optional<TAttendanceAskLeave>> leaveApply) {

        Integer currentAuditStatus = leaveApply.apply(boModel.getLeaveApplyId())
                .map(TAttendanceAskLeave::getAuditStatus)
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.LEAVE_APPLY_NOT_EXISTS));

        // 校验状态 仅已驳回/待审核状态
        if (!(LeaveApplyAuditStatusEnum.WAIT_AUDIT.getKey().equals(currentAuditStatus) ||
                LeaveApplyAuditStatusEnum.AUDIT_REJECT.getKey().equals(currentAuditStatus))) {
            throw new BizException(CarrierDataExceptionEnum.LEAVE_APPLY_NOT_UNDO);
        }

        TAttendanceAskLeave updateLeaveApply = new TAttendanceAskLeave();
        updateLeaveApply.setAuditStatus(LeaveApplyAuditStatusEnum.WAIT_SUBMIT.getKey());
        updateLeaveApply.setRemark(boModel.getRemark());
        updateLeaveApply.setAuditorName(BaseContextHandler.getUserName());
        updateLeaveApply.setAuditTime(new Date());
        updateLeaveApplyByAudit(updateLeaveApply, boModel.getLeaveApplyId(), currentAuditStatus);
    }

    /**
     * 请假申请列表
     *
     * @param requestModel
     * @return PageInfo<LeaveApplySearchListResponseModel>
     */
    public PageInfo<LeaveApplySearchListResponseModel> searchLeaveApplyList(LeaveApplySearchListRequestModel requestModel) {
        requestModel.enablePaging();
        List<LeaveApplySearchListResponseModel> leaveApplySearchList = attendanceAskLeaveMapper.searchLeaveApplyList(requestModel);
        if (ListUtils.isEmpty(leaveApplySearchList)) {
            return new PageInfo<>(Collections.emptyList());
        }
        return new PageInfo<>(leaveApplySearchList);
    }

    /**
     * 撤销请假申请
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public boolean cancelLeaveApply(LeaveApplyCancelRequestModel requestModel) {
        LeaveApplyCancelModel leaveApplyCancelModel = MapperUtils.mapper(requestModel, LeaveApplyCancelModel.class);
        cancelLeave(leaveApplyCancelModel, getLeaveApply);
        return true;
    }

    /**
     * 请假申请详情
     *
     * @param requestModel
     * @return LeaveApplyDetailResponseModel
     */
    public LeaveApplyDetailResponseModel leaveApplyDetail(LeaveApplyDetailRequestModel requestModel) {
        return attendanceAskLeaveMapper.selectLeaveApplyDetail(requestModel.getLeaveApplyId());
    }

    /**
     * 请假申请审核
     *
     * @param requestModel
     * @return boolean
     */
    public boolean auditLeaveApply(LeaveApplyAuditRequestModel requestModel) {

        TAttendanceAskLeave leaveApply = getLeaveApply.apply(requestModel.getLeaveApplyId())
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.LEAVE_APPLY_NOT_EXISTS));

        Integer currentAuditStatus = leaveApply.getAuditStatus();
        // 用户已撤销
        if (LeaveApplyAuditStatusEnum.WAIT_SUBMIT.getKey().equals(currentAuditStatus)) {
            throw new BizException(CarrierDataExceptionEnum.LEAVE_APPLY_HAD_WITHDRAWN);
        }
        // 已审核|驳回
        else if (!LeaveApplyAuditStatusEnum.WAIT_AUDIT.getKey().equals(currentAuditStatus)) {
            throw new BizException(CarrierDataExceptionEnum.LEAVE_APPLY_AUDIT_HAD_CHANGE);
        }

        TAttendanceAskLeave updateLeaveApply = new TAttendanceAskLeave();
        updateLeaveApply.setAuditStatus(requestModel.getAuditType());
        updateLeaveApply.setRemark(requestModel.getRemark());
        updateLeaveApply.setAuditorName(BaseContextHandler.getUserName());
        updateLeaveApply.setAuditTime(new Date());
        updateLeaveApplyByAudit(updateLeaveApply, requestModel.getLeaveApplyId(), currentAuditStatus);
        return true;
    }

    // 更新请假申请
    private void updateLeaveApplyByAudit(TAttendanceAskLeave entity, Long leaveApplyId, Integer currentAuditStatus) {
        commonBiz.setBaseEntityModify(entity, BaseContextHandler.getUserName());
        attendanceAskLeaveMapper.updateByIdAndAuditStatus(entity, leaveApplyId, currentAuditStatus);
    }
}
