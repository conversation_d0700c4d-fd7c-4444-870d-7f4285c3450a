package com.logistics.tms.api.feign.insuarance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/12/26 16:42
 */
@Data
public class GetInsuranceInfoByVehicleIdResponseModel {
    @ApiModelProperty(value = "商业险信息")
    private GetInsuranceInfoByVehicleIdModel commercialInsuranceInfo;
    @ApiModelProperty(value = "交强险信息")
    private GetInsuranceInfoByVehicleIdModel compulsoryInsuranceInfo;
    @ApiModelProperty(value = "货物险信息")
    private GetInsuranceInfoByVehicleIdModel cargoInsuranceInfo;
}
