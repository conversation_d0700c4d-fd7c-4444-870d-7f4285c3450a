package com.logistics.tms.api.feign.freight.hystrix;
import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.freight.FreightServiceApi;
import com.logistics.tms.api.feign.freight.model.*;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/12/24 12:00
 */
@Component
public class FreightServiceApiHystrix implements FreightServiceApi {
    @Override
    public Result<PageInfo<SearchFreightListResponseModel>> searchList(SearchFreightListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addFreight(AddFreightRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enableFreight(EnableFreightRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchFreightAddressResponseModel>> searchFreightAddressList(SearchFreightAddressRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addFreightAddressRule(AddOrModifyFreightAddressRuleRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<FreightAddressRuleDetailResponseModel> getFreightRuleDetail(FreightAddressRuleDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> deleteFreightAddressRule(DeleteFreightAddressRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifyFreightPrice(ModifyFreightPriceRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<FreightLogsResponseModel>> freightLogs(FreightLogsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<FreightCompanyInfoResponseModel> getFreightCompanyInfo(FreightCompanyInfoRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DriverFreightByDemandOrderIdsAndVehicleResponseModel> getDriverFreight(DriverFreightByDemandOrderIdsAndVehicleRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetPriceByAddressAndAmountResponseModel> getPriceByAddressAndAmount(GetPriceByAddressAndAmountRequestModel requestModel) {
        return Result.timeout();
    }
}
