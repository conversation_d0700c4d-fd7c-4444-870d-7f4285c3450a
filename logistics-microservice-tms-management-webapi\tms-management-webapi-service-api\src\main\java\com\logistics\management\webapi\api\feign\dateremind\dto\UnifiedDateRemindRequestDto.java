package com.logistics.management.webapi.api.feign.dateremind.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/5/31 8:59
 */
@Data
public class UnifiedDateRemindRequestDto implements Serializable{
    @ApiModelProperty("日期提醒ID列表")
    private List<String> dateRemindIds;
    @ApiModelProperty("是否提醒：0 否，1 是")
    private String ifRemind;
    @ApiModelProperty("提醒天数")
    private String remindDays;
    @ApiModelProperty("备注")
    private String remark;
}
