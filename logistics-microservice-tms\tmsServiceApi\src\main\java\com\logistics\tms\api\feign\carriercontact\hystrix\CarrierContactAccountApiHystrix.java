package com.logistics.tms.api.feign.carriercontact.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.carriercontact.CarrierContactAccountApi;
import com.logistics.tms.api.feign.carriercontact.dto.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class CarrierContactAccountApiHystrix implements CarrierContactAccountApi {

    @Override
    public Result<PageInfo<SearchCarrierContactResponseModel>> searchList(SearchCarrierContactRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchCarrierContactResponseModel>> exportCarrierContact(SearchCarrierContactRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierContactDetailResponseModel> getDetail(CarrierContactDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveAccount(SaveCarrierContactRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enableDisableClosed(CarrierContactEnableRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delCarrierAccount(DelCarrierContactRequestModel requestModel) {
        return Result.timeout();
    }
}
