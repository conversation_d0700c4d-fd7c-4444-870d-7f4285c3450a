<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TContractMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TContract" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="contract_no_internal" property="contractNoInternal" jdbcType="VARCHAR" />
    <result column="contract_no_external" property="contractNoExternal" jdbcType="VARCHAR" />
    <result column="contract_status" property="contractStatus" jdbcType="INTEGER" />
    <result column="contract_type" property="contractType" jdbcType="INTEGER" />
    <result column="contract_nature" property="contractNature" jdbcType="INTEGER" />
    <result column="contract_object_id" property="contractObjectId" jdbcType="BIGINT" />
    <result column="customer_company_name" property="customerCompanyName" jdbcType="VARCHAR" />
    <result column="contract_header" property="contractHeader" jdbcType="VARCHAR" />
    <result column="contract_start_time" property="contractStartTime" jdbcType="TIMESTAMP" />
    <result column="contract_end_time" property="contractEndTime" jdbcType="TIMESTAMP" />
    <result column="ending_cancel_by" property="endingCancelBy" jdbcType="VARCHAR" />
    <result column="ending_cancel_remark" property="endingCancelRemark" jdbcType="VARCHAR" />
    <result column="ending_cancel_datetime" property="endingCancelDatetime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, contract_no_internal, contract_no_external, contract_status, contract_type, contract_nature, 
    contract_object_id, customer_company_name, contract_header, contract_start_time, 
    contract_end_time, ending_cancel_by, ending_cancel_remark, ending_cancel_datetime, 
    remark, valid, created_by, created_time, last_modified_by, last_modified_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_contract
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_contract
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TContract" >
    insert into t_contract (id, contract_no_internal, contract_no_external, 
      contract_status, contract_type, contract_nature, 
      contract_object_id, customer_company_name, contract_header, 
      contract_start_time, contract_end_time, 
      ending_cancel_by, ending_cancel_remark, ending_cancel_datetime, 
      remark, valid, created_by, 
      created_time, last_modified_by, last_modified_time
      )
    values (#{id,jdbcType=BIGINT}, #{contractNoInternal,jdbcType=VARCHAR}, #{contractNoExternal,jdbcType=VARCHAR}, 
      #{contractStatus,jdbcType=INTEGER}, #{contractType,jdbcType=INTEGER}, #{contractNature,jdbcType=INTEGER}, 
      #{contractObjectId,jdbcType=BIGINT}, #{customerCompanyName,jdbcType=VARCHAR}, #{contractHeader,jdbcType=VARCHAR}, 
      #{contractStartTime,jdbcType=TIMESTAMP}, #{contractEndTime,jdbcType=TIMESTAMP}, 
      #{endingCancelBy,jdbcType=VARCHAR}, #{endingCancelRemark,jdbcType=VARCHAR}, #{endingCancelDatetime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{valid,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TContract" useGeneratedKeys="true" keyProperty="id">
    insert into t_contract
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="contractNoInternal != null" >
        contract_no_internal,
      </if>
      <if test="contractNoExternal != null" >
        contract_no_external,
      </if>
      <if test="contractStatus != null" >
        contract_status,
      </if>
      <if test="contractType != null" >
        contract_type,
      </if>
      <if test="contractNature != null" >
        contract_nature,
      </if>
      <if test="contractObjectId != null" >
        contract_object_id,
      </if>
      <if test="customerCompanyName != null" >
        customer_company_name,
      </if>
      <if test="contractHeader != null" >
        contract_header,
      </if>
      <if test="contractStartTime != null" >
        contract_start_time,
      </if>
      <if test="contractEndTime != null" >
        contract_end_time,
      </if>
      <if test="endingCancelBy != null" >
        ending_cancel_by,
      </if>
      <if test="endingCancelRemark != null" >
        ending_cancel_remark,
      </if>
      <if test="endingCancelDatetime != null" >
        ending_cancel_datetime,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="valid != null" >
        valid,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="contractNoInternal != null" >
        #{contractNoInternal,jdbcType=VARCHAR},
      </if>
      <if test="contractNoExternal != null" >
        #{contractNoExternal,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null" >
        #{contractStatus,jdbcType=INTEGER},
      </if>
      <if test="contractType != null" >
        #{contractType,jdbcType=INTEGER},
      </if>
      <if test="contractNature != null" >
        #{contractNature,jdbcType=INTEGER},
      </if>
      <if test="contractObjectId != null" >
        #{contractObjectId,jdbcType=BIGINT},
      </if>
      <if test="customerCompanyName != null" >
        #{customerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="contractHeader != null" >
        #{contractHeader,jdbcType=VARCHAR},
      </if>
      <if test="contractStartTime != null" >
        #{contractStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractEndTime != null" >
        #{contractEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endingCancelBy != null" >
        #{endingCancelBy,jdbcType=VARCHAR},
      </if>
      <if test="endingCancelRemark != null" >
        #{endingCancelRemark,jdbcType=VARCHAR},
      </if>
      <if test="endingCancelDatetime != null" >
        #{endingCancelDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TContract" >
    update t_contract
    <set >
      <if test="contractNoInternal != null" >
        contract_no_internal = #{contractNoInternal,jdbcType=VARCHAR},
      </if>
      <if test="contractNoExternal != null" >
        contract_no_external = #{contractNoExternal,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null" >
        contract_status = #{contractStatus,jdbcType=INTEGER},
      </if>
      <if test="contractType != null" >
        contract_type = #{contractType,jdbcType=INTEGER},
      </if>
      <if test="contractNature != null" >
        contract_nature = #{contractNature,jdbcType=INTEGER},
      </if>
      <if test="contractObjectId != null" >
        contract_object_id = #{contractObjectId,jdbcType=BIGINT},
      </if>
      <if test="customerCompanyName != null" >
        customer_company_name = #{customerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="contractHeader != null" >
        contract_header = #{contractHeader,jdbcType=VARCHAR},
      </if>
      <if test="contractStartTime != null" >
        contract_start_time = #{contractStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractEndTime != null" >
        contract_end_time = #{contractEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endingCancelBy != null" >
        ending_cancel_by = #{endingCancelBy,jdbcType=VARCHAR},
      </if>
      <if test="endingCancelRemark != null" >
        ending_cancel_remark = #{endingCancelRemark,jdbcType=VARCHAR},
      </if>
      <if test="endingCancelDatetime != null" >
        ending_cancel_datetime = #{endingCancelDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TContract" >
    update t_contract
    set contract_no_internal = #{contractNoInternal,jdbcType=VARCHAR},
      contract_no_external = #{contractNoExternal,jdbcType=VARCHAR},
      contract_status = #{contractStatus,jdbcType=INTEGER},
      contract_type = #{contractType,jdbcType=INTEGER},
      contract_nature = #{contractNature,jdbcType=INTEGER},
      contract_object_id = #{contractObjectId,jdbcType=BIGINT},
      customer_company_name = #{customerCompanyName,jdbcType=VARCHAR},
      contract_header = #{contractHeader,jdbcType=VARCHAR},
      contract_start_time = #{contractStartTime,jdbcType=TIMESTAMP},
      contract_end_time = #{contractEndTime,jdbcType=TIMESTAMP},
      ending_cancel_by = #{endingCancelBy,jdbcType=VARCHAR},
      ending_cancel_remark = #{endingCancelRemark,jdbcType=VARCHAR},
      ending_cancel_datetime = #{endingCancelDatetime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      valid = #{valid,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>