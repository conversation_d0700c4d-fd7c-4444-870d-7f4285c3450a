package com.logistics.management.webapi.client.settlestatement.packaging.response;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CarrierAssociationCarrierOrderResponseModel {

	@ApiModelProperty("运单数量合计")
	private Integer carrierOrderAmount;

	@ApiModelProperty("委托费用")
	private BigDecimal entrustFreight;
	@ApiModelProperty("临时费用")
	private BigDecimal otherFees;

	@ApiModelProperty("总吨数")
	private BigDecimal totalWeightSettlementAmount;

	@ApiModelProperty("总件数")
	private BigDecimal totalPackageSettlementAmount;

	@ApiModelProperty("总块数")
	private BigDecimal totalPieceSettlementAmount;

	@ApiModelProperty("运单列表")
	private PageInfo<CarrierAssociationCarrierOrderItemModel> carrierOrderItemList;
}
