package com.logistics.tms.controller.vehiclesafecheck.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 列表汇总
 * @Author: sj
 * @Date: 2019/11/6 13:06
 */
@Data
public class SummarySafeCheckResponseModel {
    @ApiModelProperty("未检查")
    private Integer notCheckCount;
    @ApiModelProperty("待确认")
    private Integer waitConfirmCount;
    @ApiModelProperty("待整改")
    private Integer waitReformCount;
    @ApiModelProperty("已整改")
    private Integer hasReformCount;
    @ApiModelProperty("检查完成")
    private Integer hasCheckCount;
}
