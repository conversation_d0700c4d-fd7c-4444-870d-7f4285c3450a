package com.logistics.tms.mapper;

import com.logistics.tms.biz.carrierorder.model.CarrierOrderLoadProductCodeModel;
import com.logistics.tms.biz.carrierorderloadcode.model.CarrierOrderLoadCodeListSqlConditionModel;
import com.logistics.tms.entity.TCarrierOrderLoadCode;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
* Created by Mybatis Generator on 2025/01/03
*/
@Mapper
public interface TCarrierOrderLoadCodeMapper extends BaseMapper<TCarrierOrderLoadCode> {

    Long selectIfProductCodeExist(String productCode);

    int batchUpdate(@Param("list") List<TCarrierOrderLoadCode> recordList);

    List<TCarrierOrderLoadCode> listByCondition(@Param("param") CarrierOrderLoadCodeListSqlConditionModel conditionModel);


    List<CarrierOrderLoadProductCodeModel> listCarrierOrderProductCode(@Param("param") CarrierOrderLoadCodeListSqlConditionModel conditionModel);
}