package com.logistics.management.webapi.controller.vehiclesettlement.mapping;

import com.logistics.management.webapi.base.enums.VehiclePropertyEnum;
import com.logistics.management.webapi.base.enums.VehicleSettlementStatusEnum;
import com.logistics.management.webapi.client.vehiclesettlement.response.SearchVehicleSettlementListResponseModel;
import com.logistics.management.webapi.controller.vehiclesettlement.response.SearchVehicleSettlementListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Optional;

/**
 * @author: wjf
 * @date: 2019/10/15 9:38
 */
public class VehicleSettlementListMapping extends MapperMapping<SearchVehicleSettlementListResponseModel,SearchVehicleSettlementListResponseDto> {
    @Override
    public void configure() {
        SearchVehicleSettlementListResponseModel source = getSource();
        SearchVehicleSettlementListResponseDto destination = getDestination();
        if (source != null){
            //待对账部分值不展示
            if (VehicleSettlementStatusEnum.WAIT_SETTLE_STATEMENT.getKey().equals(source.getStatus())){
                destination.setCarrierOrderCount("");
                destination.setActualExpensesPayable("");

                destination.setCarrierFreight("");
                destination.setAdjustFee("");
                destination.setDeductingFeeTotal("");
                destination.setRemark("");
            }
            destination.setVehiclePropertyLabel(VehiclePropertyEnum.getEnum(source.getVehicleProperty()).getValue());
            destination.setStatusLabel(VehicleSettlementStatusEnum.getEnum(source.getStatus()).getValue());
            destination.setRemark(StringUtils.isEmpty(source.getWithdrawRemark())? Optional.ofNullable(source.getRemark()).orElse(""):source.getWithdrawRemark());
        }
    }
}
