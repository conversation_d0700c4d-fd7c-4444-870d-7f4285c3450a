package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CarrierOrderDetailForLeYiResponseModel {

    private Long demandOrderId;

    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("客户单号")
    private String customerOrderCode;

    @ApiModelProperty("运单状态")
    private Integer status;

    @ApiModelProperty("是否取消")
    private Integer ifCancel;

    @ApiModelProperty("是否放空 0 否 1 是")
    private Integer ifEmpty;

    @ApiModelProperty("客户公司")
    private String entrustCompany;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("调度")
    private String dispatchUserName;

    @ApiModelProperty("运单生成时间")
    private Date dispatchTime;

    @ApiModelProperty("运单基本信息")
    private CarrierOrderDetailBasicInfoForLeYiModel carrierOrderDetailBasicInfo;

    @ApiModelProperty("运单货物信息")
    private List<CarrierOrderDetailGoodsInfoModel> carrierOrderDetailGoodsInfo;

    @ApiModelProperty("运单运费信息")
    private CarrierOrderDetailFreightFeeInfoModel carrierOrderDetailFreightFeeInfo;

    @ApiModelProperty("运单触达信息")
    private ReachManagementInfoModel reachManagementInfoModel;

    @ApiModelProperty("运单车辆司机信息")
    private List<CarrierOrderDetailVehicleDriverInfoModel> carrierOrderDetailVehicleDriverInfo;

    @ApiModelProperty("下单备注")
    private String remark;

    @ApiModelProperty("调度备注")
    private String dispatchRemark;

    @ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer entrustSettlementTonnage;

    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer carrierSettlementTonnage;

    @ApiModelProperty("单位")
    private Integer goodsUnit;

    @ApiModelProperty("需求类型")
    private Integer entrustType;

    @ApiModelProperty("预计数量")
    private BigDecimal expectAmount;

    @ApiModelProperty("提货数量")
    private BigDecimal loadAmount;

    @ApiModelProperty("卸货数量")
    private BigDecimal unloadAmount;

    @ApiModelProperty("签收数量")
    private BigDecimal signAmount;

    @ApiModelProperty(value = "公司级别 1 云途 2 二级承运商")
    private Integer level;

    private Long companyCarrierId;//车主id

    @ApiModelProperty("车主对账单状态, -2:未关联对账")
    private Integer carrierSettleStatementStatus;

    @ApiModelProperty("项目标签（多个标签,拼接）：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private String projectLabel;
}
