package com.logistics.appapi.client.driversafemeeting;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.driversafemeeting.hystrix.DriverSafeMeetingClientHystrix;
import com.logistics.appapi.client.driversafemeeting.request.AppletConfirmLeaningRequestModel;
import com.logistics.appapi.client.driversafemeeting.request.AppletSafeMeetingListRequestModel;
import com.logistics.appapi.client.driversafemeeting.request.DriverSafeMeetingRelationIdRequestModel;
import com.logistics.appapi.client.driversafemeeting.response.AppletSafeMeetingDetailResponseModel;
import com.logistics.appapi.client.driversafemeeting.response.AppletSafeMeetingListResponseModel;
import com.logistics.appapi.client.driversafemeeting.response.DriverSafeMeetingListCountResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/11 14:50
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/driverSafeMeeting",
        fallback = DriverSafeMeetingClientHystrix.class)
public interface DriverSafeMeetingClient {

    @ApiOperation(("小程序学习列表"))
    @PostMapping(value = "/appletSafeMeetingList")
    Result<PageInfo<AppletSafeMeetingListResponseModel>> appletSafeMeetingList(@RequestBody AppletSafeMeetingListRequestModel requestModel);

    @ApiOperation(("小程序学习列表数量统计"))
    @PostMapping(value = "/appletSafeMeetingListCount")
    Result<DriverSafeMeetingListCountResponseModel> appletSafeMeetingListCount();

    @ApiOperation(("小程序学习详情"))
    @PostMapping(value = "/appletSafeMeetingDetail")
    Result<AppletSafeMeetingDetailResponseModel> appletSafeMeetingDetail(@RequestBody DriverSafeMeetingRelationIdRequestModel requestModel);

    @ApiOperation(("小程序提交学习"))
    @PostMapping(value = "/appletConfirmLeaning")
    Result appletConfirmLeaning(@RequestBody AppletConfirmLeaningRequestModel requestModel);
}
