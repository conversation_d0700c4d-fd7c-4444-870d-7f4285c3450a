package com.logistics.management.webapi.controller.drivercostapply.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/8/3 15:03
 */
@Data
@ExcelIgnoreUnannotated
public class SearchCostApplySummaryResponseDto {

    @ApiModelProperty("司机")
    private String staffName = "";

    @ApiModelProperty("司机-导出用")
    @ExcelProperty(value = "司机")
    private String exportStaffName = "";

    @ApiModelProperty("发生时间")
    @ExcelProperty(value = "发生时间")
    private String occurrenceTime = "";

    @ApiModelProperty("人员机构")
    @ExcelProperty("司机机构")
    private String staffPropertyLabel = "";

    @ApiModelProperty("合计费用")
    @ExcelProperty(value = "合计费用（元）")
    private String costTotal = "";

    @ApiModelProperty("住宿费")
    @ExcelProperty(value = "住宿费（元）")
    private String accommodationFee = "";

    @ApiModelProperty("装卸费")
    @ExcelProperty(value = "装卸费（元）")
    private String loadingFee = "";

    @ApiModelProperty("其他费用; 1.3.6 修改, 原餐费(mealFee)改为其他费用")
    @ExcelProperty(value = "其他费用（元）")
    private String otherFee = "";

    @ApiModelProperty("加班费")
    @ExcelProperty(value = "加班费（元）")
    private String overtimePay = "";

    @ApiModelProperty("劳保费")
    @ExcelProperty(value = "劳保费（元）")
    private String laborSecurityExpense = "";

    @ApiModelProperty("电话费")
    @ExcelProperty(value = "电话费（元）")
    private String telephoneCharges = "";

    @ApiModelProperty("交通费")
    @ExcelProperty(value = "交通费（元）")
    private String transportationFee = "";

    @ApiModelProperty("维修费")
    @ExcelProperty(value = "维修费（元）")
    private String maintenanceCost = "";

    @ApiModelProperty("车辆保养费")
    @ExcelProperty(value = "车辆保养费（元）")
    private String vehicleMaintenanceCost = "";

    @ApiModelProperty("过路过桥费")
    @ExcelProperty(value = "过路过桥费（元）")
    private String tollFee = "";

    @ApiModelProperty("停车费")
    @ExcelProperty(value = "停车费（元）")
    private String parkingFee = "";

    @ApiModelProperty("尿素费")
    @ExcelProperty(value = "尿素费（元）")
    private String ureaFee = "";

    @ApiModelProperty("加油费")
    @ExcelProperty(value = "加油费（元）")
    private String oilFee = "";

    @ApiModelProperty("核酸检测费")
    @ExcelProperty(value = "核酸检测费（元）")
    private String nucleicAcidTestingFee = "";

    @ApiModelProperty("医疗防护用品")
    @ExcelProperty(value = "医疗防护用品（元）")
    private String medicalSuppliesFee = "";

    @ApiModelProperty("打印费; 1.3.6 新增")
    @ExcelProperty(value = "打印费（元）")
    private String printingFee = "";

    @ApiModelProperty("叉车费; 1.3.6 新增")
    @ExcelProperty(value = "叉车费（元）")
    private String forkliftFee = "";

    @ApiModelProperty("盖雨布; 1.3.6 新增")
    @ExcelProperty(value = "盖雨布（元）")
    private String coverTarpaulin = "";

    @ApiModelProperty("破包赔偿; 1.3.6 新增")
    @ExcelProperty(value = "破包赔偿（元）")
    private String damageWrapIndemnify = "";

    @ApiModelProperty("扣款; 1.3.6 新增")
    @ExcelProperty(value = "扣款（元）")
    private String deductions = "";

    @ApiModelProperty("(3.17.0)交通罚款")
    @ExcelProperty(value = "交通罚款（元）")
    private String TrafficFine = "";
}
