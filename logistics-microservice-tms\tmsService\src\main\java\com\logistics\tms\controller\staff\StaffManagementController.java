package com.logistics.tms.controller.staff;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.common.SrcUrlModel;
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoRequestModel;
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoResponseModel;
import com.logistics.tms.biz.staff.StaffManagementBiz;
import com.logistics.tms.controller.staff.request.*;
import com.logistics.tms.controller.staff.response.*;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Api(value = "API-StaffManagementServiceApi-人员管理")
@RequestMapping(value = "/service/staffmanagement")
public class StaffManagementController {

    @Autowired
    private StaffManagementBiz staffManagementBiz;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchStaffManagementListResponseModel>> searchStaffManagementList(@RequestBody SearchStaffManagementListRequestModel requestModel) {
        return Result.success(staffManagementBiz.searchStaffManagementList(requestModel));
    }

    /**
     * 人员信息新增/修改
     *
     * @param requestModel 人员信息
     * @return 操作结果
     */
    @ApiOperation(value = "人员信息新增/修改")
    @PostMapping(value = "/addOrModifyStaff")
    public Result<Boolean> addOrModifyStaff(@RequestBody AddOrModifyStaffTicketsRequestModel requestModel) {
        staffManagementBiz.addOrModifyStaff(requestModel);
        return Result.success(true);
    }

    /**
     * 外部人员信息新增/修改
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "外部人员信息新增/修改")
    @PostMapping(value = "/addOrModifyExternalStaff")
    public Result<Boolean> addOrModifyExternalStaff(@RequestBody AddOrModifyExternalStaffRequestModel requestModel) {
        staffManagementBiz.addOrModifyExternalStaff(requestModel);
        return Result.success(true);
    }

    /**
     * 查询人员信息详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询人员信息详情")
    @PostMapping(value = "/getStaffDetail")
    public Result<GetStaffDetailResponseModel> getStaffDetail(@RequestBody CarrierStaffIdRequestModel requestModel) {
        return Result.success(staffManagementBiz.getStaffDetail(requestModel));
    }

    /**
     * 导出
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportStaffList")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<ExportStaffManagementListResponseModel>> exportStaffList(@RequestBody SearchStaffManagementListRequestModel requestModel) {
        return Result.success(staffManagementBiz.exportStaffManagementList(requestModel));
    }

    /**
     * 从业资格证列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "从业资格证列表")
    @PostMapping(value = "/occupationalList")
    public Result<List<OccupationalListResponseModel>> occupationalList(@RequestBody StaffIdRequestModel requestModel) {
        return Result.success(staffManagementBiz.occupationalList(requestModel));
    }

    /**
     * 继续教育列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "继续教育列表")
    @PostMapping(value = "/continueLearningList")
    public Result<List<ContinueLearningListResponseModel>> continueLearningList(@RequestBody StaffIdRequestModel requestModel) {
        return Result.success(staffManagementBiz.continueLearningList(requestModel));
    }

    /**
     * 诚信考核列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "诚信考核列表")
    @PostMapping(value = "/integrityExaminationList")
    public Result<List<IntegrityExaminationListResponseModel>> integrityExaminationList(@RequestBody StaffIdRequestModel requestModel) {
        return Result.success(staffManagementBiz.integrityExaminationList(requestModel));
    }

    /**
     * 导入
     * @param importStaffRequestModel
     * @return
     */
    @ApiOperation(value = "导入")
    @PostMapping(value = "/import")
    public Result<ImportStaffResponseModel> importStaff(@RequestBody ImportStaffRequestModel importStaffRequestModel) {
        return Result.success(staffManagementBiz.importStaff(importStaffRequestModel));
    }

    /**
     * 导入人员证件
     * @param srcUrlModel
     * @return
     */
    @ApiOperation(value = "导入人员证件")
    @PostMapping(value = "/importStaffCertificate")
    public Result<Boolean> importStaffCertificate(@RequestBody SrcUrlModel srcUrlModel) {
        staffManagementBiz.importStaffCertificate(srcUrlModel);
        return Result.success(null);
    }

    /**
     * 模糊查询司机信息
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "模糊查询司机信息")
    @PostMapping(value = "/fuzzyQueryDriverInfo")
    public Result<PageInfo<FuzzyQueryDriverInfoResponseModel>> fuzzyQueryDriverInfo(@RequestBody FuzzyQueryDriverInfoRequestModel requestModel) {
        return Result.success(staffManagementBiz.fuzzyQueryDriverInfo(requestModel));
    }

    /**
     * 启动/禁用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "启动/禁用")
    @PostMapping(value = "/enableOrDisable")
    public Result<Boolean> enableOrDisable(@RequestBody EnableStaffRequestModel requestModel) {
        staffManagementBiz.enableOrDisable(requestModel);
        return Result.success(true);
    }

    /**
     * 根据accountId查询司机信息
     *
     * @return
     */
    @ApiModelProperty(value = "根据accountId查询司机信息")
    @PostMapping(value = "/getStaffDetailByAccountId")
    public Result<GetStaffDetailByAccountIdResponseModel> getStaffDetailByAccountId() {
        return Result.success(staffManagementBiz.getStaffDetailByAccountId());
    }

    /**
     * 删除外部司机
     *
     * @param requestModel 车主人员关联id
     * @return 操作结果
     */
    @ApiOperation(value = "删除司机")
    @PostMapping(value = "/delStaff")
    public Result<Boolean> delStaff(@RequestBody DelStaffRequestModel requestModel) {
        staffManagementBiz.delStaff(requestModel);
        return Result.success(true);
    }

    /**
     * 前台车主批量启用/禁用司机
     *
     * @param requestModel 司机id
     * @return 操作结果
     */
    @ApiOperation(value = "批量禁用/启用司机(前台)")
    @PostMapping(value = "/batchEnableOrDisable")
    public Result<Boolean> batchEnableOrDisable(@RequestBody BatchEnableOrDisableRequestModel requestModel) {
        staffManagementBiz.batchEnableOrDisable(requestModel);
        return Result.success(true);
    }

    /**
     * 前台车主批量删除司机
     *
     * @param requestModel 司机id
     * @return 操作结果
     */
    @ApiOperation(value = "批量删除司机(前台)")
    @PostMapping(value = "/batchDelStaff")
    public Result<Boolean> batchDelStaff(@RequestBody BatchDelStaffRequestModel requestModel) {
        staffManagementBiz.batchDelStaff(requestModel);
        return Result.success(true);
    }

    /**
     * 仓库权限开关
     */
    @ApiOperation(value = "仓库权限开关")
    @PostMapping(value = "/warehouseSwitch")
    public Result<Boolean> warehouseSwitch(@RequestBody WarehouseSwitchStaffRequestModel requestModel) {
        staffManagementBiz.warehouseSwitch(requestModel);
        return Result.success(true);
    }
}
