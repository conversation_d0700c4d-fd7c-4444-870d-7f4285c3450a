package com.logistics.management.webapi.api.feign.driversafemeeting.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.driversafemeeting.DriverSafeMeetingApi;
import com.logistics.management.webapi.api.feign.driversafemeeting.dto.*;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/1 18:56
 */
@Component
public class DriverSafeMeetingApiHystrix implements DriverSafeMeetingApi {
    @Override
    public Result<List<DriverSafeMeetingKanBanResponseDto>> driverSafeMeetingKanBan(DriverSafeMeetingKanBanRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result addDriverSafeMeeting(AddDriverSafeMeetingRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result modifyDriverSafeMeeting(ModifyDriverSafeMeetingRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result replacementDriverSafeMeeting(ReplacementDriverSafeMeetingRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<DriverSafeMeetingContentDetailResponseDto> driverSafeMeetingContentDetail(DriverSafeMeetingIdRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportDriverSafeMeetingContentDetail(DriverSafeMeetingIdRequestDto requestDto, HttpServletResponse response, HttpServletRequest request) {
        Result.timeout();
    }

    @Override
    public Result<PageInfo<DriverSafeMeetingDetailResponseDto>> driverSafeMeetingDetailList(DriverSafeMeetingDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<DriverSafeMeetingListCountResponseDto> driverSafeMeetingListCount(DriverSafeMeetingDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportDriverSafeMeetingDetailList(DriverSafeMeetingDetailRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }

    @Override
    public Result delDriverSafeMeetingRelation(DriverSafeMeetingRelationIdRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<List<String>> getYear() {
        return Result.timeout();
    }
}
