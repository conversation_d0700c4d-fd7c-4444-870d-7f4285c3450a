package com.logistics.management.webapi.controller.freightconfig;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.ExportUtils;
import com.logistics.management.webapi.client.freightconfig.CarrierFreightConfigAddressClient;
import com.logistics.management.webapi.client.freightconfig.request.address.*;
import com.logistics.management.webapi.client.freightconfig.request.ladder.CarrierFreightConfigLadderRequestCheck;
import com.logistics.management.webapi.client.freightconfig.response.address.CarrierFreightConfigAddressDetailResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.address.CarrierFreightConfigAddressListResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.address.CarrierFreightConfigAddressLogsResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.address.SearchCarrierFreightConfigResponseModel;
import com.logistics.management.webapi.controller.freightconfig.mapping.CarrierFreightConfigAddressDetailMapping;
import com.logistics.management.webapi.controller.freightconfig.mapping.CarrierFreightConfigAddressListMapping;
import com.logistics.management.webapi.controller.freightconfig.request.address.*;
import com.logistics.management.webapi.controller.freightconfig.response.address.CarrierFreightConfigAddressDetailResponseDto;
import com.logistics.management.webapi.controller.freightconfig.response.address.CarrierFreightConfigAddressItemResponseDto;
import com.logistics.management.webapi.controller.freightconfig.response.address.CarrierFreightConfigAddressListResponseDto;
import com.logistics.management.webapi.controller.freightconfig.response.address.GetAddressRuleLogsResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@RestController
@Api(tags = "路线计价配置管理")
@RequestMapping(value = "/api/freight/config/address")
public class CarrierFreightConfigAddressController {

    @Resource
    private CarrierFreightConfigAddressClient carrierFreightConfigAddressClient;

    @PostMapping(value = "/searchList")
    @ApiOperation(value = "路线计价配置列表", tags = "1.3.5")
    Result<CarrierFreightConfigAddressListResponseDto> searchList(@Valid @RequestBody CarrierFreightConfigAddressListRequestDto requestDto) {
        CarrierFreightConfigAddressListResponseDto responseDto = new CarrierFreightConfigAddressListResponseDto();
        Result<CarrierFreightConfigAddressListResponseModel> result = carrierFreightConfigAddressClient.searchList(MapperUtils.mapper(requestDto, CarrierFreightConfigAddressListRequestModel.class));
        result.throwException();
        MapperUtils.mapper(result.getData(), responseDto);
        PageInfo pageInfo = result.getData().getAddressConfigPage();
        List<CarrierFreightConfigAddressItemResponseDto> carrierFreightRuleList = MapperUtils.mapper(pageInfo.getList(),
                CarrierFreightConfigAddressItemResponseDto.class,
                new CarrierFreightConfigAddressListMapping());
        pageInfo.setList(carrierFreightRuleList);
        responseDto.setAddressConfigPage(pageInfo);
        return Result.success(responseDto);
    }

    @PostMapping(value = "/detail")
    @ApiOperation(value = "路线计价配置查看", tags = "1.3.5")
    Result<CarrierFreightConfigAddressDetailResponseDto> detail(@Valid @RequestBody CarrierFreightAddressDetailRequestDto requestDto) {
        Result<CarrierFreightConfigAddressDetailResponseModel> result = carrierFreightConfigAddressClient.detail(MapperUtils.mapper(requestDto, CarrierFreightAddressDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierFreightConfigAddressDetailResponseDto.class, new CarrierFreightConfigAddressDetailMapping()));
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "新增路线计价配置", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> add(@Valid @RequestBody CarrierFreightConfigAddressAddRequestDto requestDto) {
        CarrierFreightConfigAddressAddRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierFreightConfigAddressAddRequestModel.class);
        // 校验地址重复
        validAddress(requestModel);
        // 校验运价规则
        CarrierFreightConfigLadderRequestCheck.check(requestModel.getLadderConfigList());

        Result<Boolean> result = carrierFreightConfigAddressClient.add(requestModel);
        result.throwException();
        return Result.success(true);
    }

    /**
     * 校验地址重复
     *
     * @param requestDto
     */
    private void validAddress(CarrierFreightConfigAddressAddRequestModel requestDto) {
        List<Long> fromAreaList = new ArrayList<>();
        for (CarrierFreightConfigAddressAddRequestModel.FromArea fromArea : requestDto.getFromAreaId()) {
            if (fromAreaList.contains(fromArea.getFromAreaId())) {
                throw new BizException(ManagementWebApiExceptionEnum.FROM_AREA_NOT_DUPLICATE);
            }
            fromAreaList.add(fromArea.getFromAreaId());
        }
        List<Long> toAreaList = new ArrayList<>();
        for (CarrierFreightConfigAddressAddRequestModel.FromArea toArea : requestDto.getToAreaId()) {
            if (toAreaList.contains(toArea.getFromAreaId())) {
                throw new BizException(ManagementWebApiExceptionEnum.TO_AREA_NOT_DUPLICATE);
            }
            toAreaList.add(toArea.getFromAreaId());
        }
    }

    @PostMapping(value = "/edit")
    @ApiOperation(value = "编辑路线计价配置", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> edit(@Valid @RequestBody CarrierFreightConfigAddressEditRequestDto requestDto) {
        CarrierFreightAddressEditEnableRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierFreightAddressEditEnableRequestModel.class);
        // 校验运价规则
        CarrierFreightConfigLadderRequestCheck.check(requestModel.getLadderConfigList());
        Result<Boolean> result = carrierFreightConfigAddressClient.edit(requestModel);
        result.throwException();
        return Result.success(true);
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "删除路线计价配置", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> delete(@Valid @RequestBody CarrierFreightAddressDeleteRequestDto requestDto) {
        Result<Boolean> result = carrierFreightConfigAddressClient.delete(MapperUtils.mapper(requestDto, CarrierFreightAddressDeleteRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    @PostMapping(value = "/enable")
    @ApiOperation(value = "启用禁用路线计价配置", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> enable(@Valid @RequestBody CarrierFreightConfigAddressRequestDto requestDto) {
        Result<Boolean> result = carrierFreightConfigAddressClient.enable(MapperUtils.mapper(requestDto, CarrierFreightAddressEnableRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    @ApiOperation(value = "车主运价细则日志", tags = "1.3.5")
    @PostMapping(value = "/getAddressRuleLogs")
    public Result<List<GetAddressRuleLogsResponseDto>> getAddressRuleLogs(@RequestBody GetAddressRuleRequestDto requestDto) {
        Result<List<CarrierFreightConfigAddressLogsResponseModel>> result = carrierFreightConfigAddressClient.getAddressRuleLogs(MapperUtils.mapper(requestDto, CarrierFreightAddressDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), GetAddressRuleLogsResponseDto.class));
    }

    @GetMapping(value = "/export")
    @ApiOperation(value = "路线计价配置列表导出", tags = "1.3.5")
    void export(CarrierFreightConfigAddressListRequestDto requestDto, HttpServletResponse response) {
        Result<CarrierFreightConfigAddressListResponseModel> result = carrierFreightConfigAddressClient.export(MapperUtils.mapper(requestDto, CarrierFreightConfigAddressListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData().getAddressConfigPage();
        List<CarrierFreightConfigAddressItemResponseDto> carrierFreightRuleList = MapperUtils.mapper(pageInfo.getList(),
                CarrierFreightConfigAddressItemResponseDto.class,
                new CarrierFreightConfigAddressListMapping());

        SearchCarrierFreightConfigRequestModel requestModel = new SearchCarrierFreightConfigRequestModel();
        requestModel.setFreightConfigSchemeId(result.getData().getFreightConfigSchemeId());
        Result<SearchCarrierFreightConfigResponseModel> carrierFreightInfo = carrierFreightConfigAddressClient.getCarrierFreight(requestModel);
        carrierFreightInfo.throwException();
        // 根据车主类型判断车主名称
        SearchCarrierFreightConfigResponseModel carrierFreightData = carrierFreightInfo.getData();
        String companyCarrierName = CompanyTypeEnum.COMPANY.getKey().equals(carrierFreightData.getCompanyCarrierType()) ?
                carrierFreightData.getCompanyCarrierName() :
                carrierFreightData.getCarrierContactName() + carrierFreightData.getCarrierContactPhone();
        String fileName = "【" + companyCarrierName + "】" + "固定路线运价管理";

        ExportUtils.exportByYeloExcel(response, carrierFreightRuleList, CarrierFreightConfigAddressItemResponseDto.class, fileName);
    }
}
