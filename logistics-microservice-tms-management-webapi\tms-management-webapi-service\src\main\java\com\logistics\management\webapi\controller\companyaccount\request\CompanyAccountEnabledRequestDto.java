package com.logistics.management.webapi.controller.companyaccount.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class CompanyAccountEnabledRequestDto {

    @ApiModelProperty(value = "公司账户ID", required = true)
    @NotBlank(message = "请选择公司账户")
    private String companyAccountId;

    @ApiModelProperty(value = "禁用/启用。1：启用，0：禁用", required = true)
    @NotNull(message = "操作类型不能为空")
    @Range(min = 0, max = 1, message = "操作类型只能0或1")
    private String enabled;
}
