<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierFreightConfigMapper" >
    <select id="selectByFreightId" resultType="com.logistics.tms.controller.freightconfig.response.CarrierFreightConfigListResponseModel">
        select id                 freightConfigId,
               config_type        configType,
               entrust_type       entrustType,
               created_by         createdBy,
               created_time       createdTime,
               last_modified_by   lastModifiedBy,
               last_modified_time lastModifiedTime
        from t_carrier_freight_config
        where valid = 1
          and carrier_freight_id = #{freightId,jdbcType=INTEGER}
        order by last_modified_time desc,id desc
    </select>

    <select id="selectById" resultType="com.logistics.tms.controller.freightconfig.response.CarrierFreightConfigDetailResponseModel">
        select id                 freightConfigId,
               config_type        configType,
               entrust_type       entrustType,
               created_by         createdBy,
               created_time       createdTime,
               last_modified_by   lastModifiedBy,
               last_modified_time lastModifiedTime
        from t_carrier_freight_config
        where valid = 1
          and id = #{freightConfigId,jdbcType=BIGINT}
    </select>

    <select id="selectEntrustTypeByFreightId" resultType="String">
        select entrust_type
        from t_carrier_freight_config
        where valid = 1
          and carrier_freight_id = #{freightId,jdbcType=INTEGER}
    </select>

    <select id="selectCarrierFreightByCompanyCarrier" resultType="com.logistics.tms.biz.carrierfreight.model.CarrierFreightCarrierModel">
        select
        tcf.company_carrier_id companyCarrierId,
        tcf.id                 carrierFreightId,
        tcfc.id                carrierFreightConfigId,
        tcfc.config_type       configType,
        tcfc.entrust_type      entrustType
        from t_carrier_freight_config tcfc
        left join t_carrier_freight tcf on tcf.valid = 1 and tcf.id = tcfc.carrier_freight_id
        where tcfc.valid = 1
          and tcf.enabled = 1
        <choose>
            <when test="list != null and list.size() != 0">
                and tcf.company_carrier_id in
                <foreach collection="list" open="(" separator="," close=")" item="item">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>
</mapper>