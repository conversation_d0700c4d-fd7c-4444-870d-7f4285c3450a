package com.logistics.appapi.controller.attendance.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 考勤打卡历史请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Data
public class AttendanceHistoryListRequestDto extends AbstractPageForm<AttendanceHistoryListRequestDto> {

	@ApiModelProperty(value = "考勤日期(月份)", required = true)
	@NotBlank(message = "请选择要查询的月份")
	private String attendanceDate;
}
