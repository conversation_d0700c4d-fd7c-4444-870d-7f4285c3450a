package com.logistics.management.webapi.client.companycarrier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/9/27 17:40
 */
@Data
public class CompanyCarrierDetailResponseModel {

	@ApiModelProperty("承运商公司id")
	private Long companyCarrierId;

	@ApiModelProperty("公司水印")
	private String companyWaterMark;

	@ApiModelProperty("道路许可证号")
	private String roadTransportCertificateNumber;

	@ApiModelProperty("道路许可证图片")
	private String roadTransportCertificateImage;

	@ApiModelProperty("道路许可证有效期")
	private Date roadTransportCertificateValidityTime;

	@ApiModelProperty("道路许可证是否永久: 0 否 1 是")
	private Integer roadTransportCertificateIsForever;

	@ApiModelProperty("道路许可证是否后补: 0 否 1 是")
	private Integer roadTransportCertificateIsAmend;

	@ApiModelProperty("车主类型: 1 企业 2 个人")
	private Integer type;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("公司名字")
	private String companyCarrierName;

	@ApiModelProperty("营业执照图片")
	private String tradingCertificateImage;

	@ApiModelProperty("营业执照有效期")
	private Date tradingCertificateValidityTime;

	@ApiModelProperty("营业执照是否永久: 0 否 1 是")
	private Integer tradingCertificateIsForever;

	@ApiModelProperty("营业执照是否后补 0否1是")
	private Integer tradingCertificateIsAmend;

	@ApiModelProperty("车主账号id")
	private Long carrierContactId;

	@ApiModelProperty("姓名")
	private String contactName;

	@ApiModelProperty("手机号")
	private String contactPhone;

	@ApiModelProperty("身份证人面像")
	private String identityFaceFile;

	@ApiModelProperty("身份证人面像图片是否后补 0否1是")
	private Integer identityFaceFileIsAmend;

	@ApiModelProperty("身份证国徽像")
	private String identityNationalFile;

	@ApiModelProperty("身份证国徽图片是否后补 0否1是")
	private Integer identityNationalFileIsAmend;

	@ApiModelProperty("身份证有效期")
	private Date identityValidity;

	@ApiModelProperty("身份证是否永久: 0 否 1 是")
	private Integer identityIsForever;

	@ApiModelProperty("身份证号码")
	private String identityNumber;

	@ApiModelProperty("省ID")
	private Long provinceId;

	@ApiModelProperty("省名字")
	private String provinceName;

	@ApiModelProperty("城市ID")
	private Long cityId;

	@ApiModelProperty("城市名字")
	private String cityName;

	@ApiModelProperty("区ID")
	private Long areaId;

	@ApiModelProperty("区名字")
	private String areaName;

	@ApiModelProperty("发证机关详情")
	private String certificationDepartmentDetail;

	@ApiModelProperty("临时费用提交：0 否，1 是")
	private Integer commitOtherFee;

	@ApiModelProperty("公司级别 1 云途 2 二级承运商")
	private Integer level;

	@ApiModelProperty("临时费用费点")
	private BigDecimal otherFeeTaxPoint;

	@ApiModelProperty("运费费点")
	private BigDecimal freightTaxPoint;
}
