package com.logistics.appapi.client.driverappoint;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.driverappoint.hystrix.DriverAppointClientHystrix;
import com.logistics.appapi.client.driverappoint.request.DriverAppointAssociatedVehicleRequestModel;
import com.logistics.appapi.client.driverappoint.request.SearchAppointRequestModel;
import com.logistics.appapi.client.driverappoint.request.SearchDrierAppointDetailRequestModel;
import com.logistics.appapi.client.driverappoint.response.SearchAppointCountResponseModel;
import com.logistics.appapi.client.driverappoint.response.SearchDrierAppointDetailResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/11 14:22
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/driverApplet/driverAppoint",
        fallback = DriverAppointClientHystrix.class)
public interface DriverAppointClient {

    @ApiOperation("小程序预约记录-列表")
    @PostMapping({"/searchAppointList"})
    Result<SearchAppointCountResponseModel> searchAppointList(@RequestBody SearchAppointRequestModel requestModel);

    @ApiOperation("小程序预约记录-详情")
    @PostMapping({"/searchDrierAppointDetail"})
    Result<SearchDrierAppointDetailResponseModel> searchDrierAppointDetail(@RequestBody SearchDrierAppointDetailRequestModel requestModel);

    @ApiOperation("小程序-预约记录关联车辆（生成运单）")
    @PostMapping({"/associatedVehicle"})
    Result<Boolean> associatedVehicle(@RequestBody DriverAppointAssociatedVehicleRequestModel requestModel);
}
