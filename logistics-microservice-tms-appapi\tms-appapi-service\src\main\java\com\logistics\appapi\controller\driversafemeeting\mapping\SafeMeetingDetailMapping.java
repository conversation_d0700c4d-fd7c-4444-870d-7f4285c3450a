package com.logistics.appapi.controller.driversafemeeting.mapping;

import com.logistics.appapi.base.enums.StudyStatusEnum;
import com.logistics.appapi.base.utils.FrequentMethodUtils;
import com.logistics.appapi.client.driversafemeeting.response.AppletSafeMeetingDetailResponseModel;
import com.logistics.appapi.controller.driversafemeeting.response.SafeMeetingDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/11/8 13:12
 */
public class SafeMeetingDetailMapping extends MapperMapping<AppletSafeMeetingDetailResponseModel,SafeMeetingDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    public SafeMeetingDetailMapping(String imagePrefix , Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        AppletSafeMeetingDetailResponseModel source = getSource();
        SafeMeetingDetailResponseDto destination = getDestination();
        if (source != null){
            destination.setPeriod(FrequentMethodUtils.encodeYearMonth(source.getPeriod()));
            destination.setStatusDesc(StudyStatusEnum.getEnum(source.getStatus()).getValue());
            if (source.getCreatedTime() != null){
                destination.setCreatedTime(DateUtils.dateToString(source.getCreatedTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if (StringUtils.isNotBlank(source.getSignImageUrl())){
                destination.setSignImageUrl(imagePrefix + imageMap.get(source.getSignImageUrl()));
            }
            if (StringUtils.isNotBlank(source.getStaffDriverImageUrl())){
                destination.setStaffDriverImageUrl(imagePrefix + imageMap.get(source.getStaffDriverImageUrl()));
            }
        }
    }
}
