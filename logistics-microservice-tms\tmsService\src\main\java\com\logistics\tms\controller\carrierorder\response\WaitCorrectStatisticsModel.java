package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/1/4 14:38
 */
@Data
public class WaitCorrectStatisticsModel {
    @ApiModelProperty("发货省份")
    private String loadProvinceName;
    @ApiModelProperty("发货市")
    private String loadCityName;
    @ApiModelProperty("负责人")
    private String loadRegionContactName;
    @ApiModelProperty("下单数（单数）")
    private Integer carrierOrderCount;
    private Long loadCityId;//提货市id，用于查询最新单子存在的负责人
}
