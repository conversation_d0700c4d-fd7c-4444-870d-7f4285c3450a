<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TBiddingOrderDemandMapper" >

  <select id="selectByBiddingOrderIdAndDemandId" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
        from t_bidding_order_demand
    where valid = 1
    and bidding_order_id = #{biddingOrderId,jdbcType=BIGINT}
    and demand_order_id = #{demandId,jdbcType=BIGINT}
  </select>



  <select id="selectCountByBiddingOrderId" resultType="long">
    select
        count(*)
    from t_bidding_order_demand
    where valid = 1
    and bidding_order_id = #{biddingOrderId,jdbcType=BIGINT}
  </select>
  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TBiddingOrderDemand" >
    <foreach collection="recordList" item="item" separator=";">
      insert into t_bidding_order_demand
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.biddingOrderId != null" >
          bidding_order_id,
        </if>
        <if test="item.demandOrderId != null" >
          demand_order_id,
        </if>
        <if test="item.biddingPriceType != null" >
          bidding_price_type,
        </if>
        <if test="item.biddingPrice != null" >
          bidding_price,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.biddingOrderId != null" >
          #{item.biddingOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.demandOrderId != null" >
          #{item.demandOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.biddingPriceType != null" >
          #{item.biddingPriceType,jdbcType=INTEGER},
        </if>
        <if test="item.biddingPrice != null" >
          #{item.biddingPrice,jdbcType=DECIMAL},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TBiddingOrderDemand" >
    <foreach collection="recordList" item="item" separator=";">
      update t_bidding_order_demand
      <set >
        <if test="item.biddingOrderId != null" >
          bidding_order_id = #{item.biddingOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.demandOrderId != null" >
          demand_order_id = #{item.demandOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.biddingPriceType != null" >
          bidding_price_type = #{item.biddingPriceType,jdbcType=INTEGER},
        </if>
        <if test="item.biddingPrice != null" >
          bidding_price = #{item.biddingPrice,jdbcType=DECIMAL},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectFirstDemandInfo" resultType="com.logistics.tms.biz.biddingorder.bo.DemandInfoBo">
    select tbod.bidding_order_id as                             biddingOrderId,
           tbod.demand_order_id  as                             demandOrderId,
           tdo.demand_order_code as                             demandOrderCode,
           tdo.goods_unit        as                             goodsUnit,
           CONCAT(tdoa.load_city_name, tdoa.load_area_name)     loadAddress,
           CONCAT(tdoa.unload_city_name, tdoa.unload_area_name) unloadAddress,
           tdo.goods_amount                                     goodsCount
    from t_bidding_order_demand tbod
                 inner join t_demand_order tdo on tbod.demand_order_id = tdo.id and tdo.valid = 1
                 inner join t_demand_order_address tdoa on tdo.id = tdoa.demand_order_id and tdoa.valid = 1
            where tbod.valid = 1
              and        tbod.id in
            (select min(tbod2.id)
             from t_bidding_order_demand tbod2 where tbod2.valid = 1
    <if test="biddingOrderIds != null and biddingOrderIds.size() != 0">
      and tbod.bidding_order_id in
      <foreach collection="biddingOrderIds" item="item" open="(" close=")" separator=",">
        #{item,jdbcType=BIGINT}
      </foreach>
    </if>
    group by tbod2.bidding_order_id
            )
    <if test="loadAddress != null and loadAddress != ''">
      and instr(CONCAT(tdoa.load_province_name,tdoa.load_city_name, tdoa.load_area_name), #{loadAddress,jdbcType=VARCHAR}) > 0
    </if>
    <if test="unloadAddress != null and unloadAddress != ''">
      and instr(CONCAT(tdoa.unload_province_name,tdoa.unload_city_name, tdoa.unload_area_name), #{unloadAddress,jdbcType=VARCHAR}) > 0
    </if>
  </select>

    <select id="selectDemandByCondition" resultType="com.logistics.tms.biz.biddingorder.bo.DemandInfoBo">
        select
        tbod.bidding_order_id as                             biddingOrderId,
        tbod.demand_order_id  as                             demandOrderId,

        tdo.demand_order_code as                             demandOrderCode,
        tdo.goods_unit        as                             goodsUnit,
        tdo.goods_amount                                     goodsCount,

        CONCAT(tdoa.load_city_name, tdoa.load_area_name) as  loadAddress,
        CONCAT(tdoa.unload_city_name, tdoa.unload_area_name) as unloadAddress,

        group_concat(tdog.goods_name separator '/') as goodsName

        from t_bidding_order_demand tbod
        inner join t_demand_order tdo on tbod.demand_order_id = tdo.id and tdo.valid = 1
        inner join t_demand_order_address tdoa on tdo.id = tdoa.demand_order_id and tdoa.valid = 1
        inner join t_demand_order_goods tdog on tdo.id = tdog.demand_order_id and tdog.valid = 1
        where tbod.valid = 1
        <if test="biddingOrderId != null">
            and tbod.bidding_order_id = #{biddingOrderId,jdbcType=BIGINT}
        </if>
        group by tbod.demand_order_id
    </select>

    <select id="selectByBiddingOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_bidding_order_demand
        where valid = 1
          and bidding_order_id = #{bindingOrderId,jdbcType=BIGINT}
    </select>
</mapper>