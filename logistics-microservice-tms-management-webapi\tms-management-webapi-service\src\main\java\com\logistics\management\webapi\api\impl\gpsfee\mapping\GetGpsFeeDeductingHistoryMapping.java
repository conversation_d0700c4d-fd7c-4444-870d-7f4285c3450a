package com.logistics.management.webapi.api.impl.gpsfee.mapping;

import com.logistics.management.webapi.api.feign.gpsfee.dto.GetDeductingHistoryResponseDto;
import com.logistics.tms.api.feign.gpsfee.model.GetDeductingHistoryResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;

/**
 * @author: wjf
 * @date: 2019/10/9 13:11
 */
public class GetGpsFeeDeductingHistoryMapping extends MapperMapping<GetDeductingHistoryResponseModel,GetDeductingHistoryResponseDto> {
    @Override
    public void configure() {
        GetDeductingHistoryResponseModel source = getSource();
        GetDeductingHistoryResponseDto destination = getDestination();
        if (source != null){
            destination.setRemainingDeductingFeeTotal(ConverterUtils.toString(source.getDeductingFee().add(source.getRemainingDeductingFee())));
        }
    }
}
