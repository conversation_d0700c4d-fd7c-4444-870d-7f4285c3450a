package com.logistics.appapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 运单签收单,请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/5/30
 */
@Data
public class ReviewSignTicketsRequestDto {

	@ApiModelProperty(value = "运单ID", required = true)
	@NotBlank(message = "运单id不能为空")
	private String carrierOrderId;
}