package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.controller.carrierorder.response.CarrierOrderGoodsResponseDto;
import com.logistics.management.webapi.controller.carrierorder.response.LoadDetailResponseDto;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.client.carrierorder.response.CarrierOrderGoodsResponseModel;
import com.logistics.management.webapi.client.carrierorder.response.LoadDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-29 14:52
 */
public class GetLoadDetailMapping extends MapperMapping<LoadDetailResponseModel, LoadDetailResponseDto> {
    @Override
    public void configure() {
        LoadDetailResponseModel model = this.getSource();
        LoadDetailResponseDto dto = this.getDestination();
        if(model != null){
            dto.setDriver(model.getDriverName() + " " + model.getDriverMobile());
            dto.setGoodsUnitLabel(GoodsUnitEnum.getEnum(model.getGoodsUnit()).getUnit());
            dto.setDispatchTime(DateUtils.dateToString(getSource().getDispatchTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));

            StringBuilder loadAddress = new StringBuilder();
            loadAddress.append(model.getLoadProvinceName());
            loadAddress.append(model.getLoadCityName());
            loadAddress.append(model.getLoadAreaName());
            if(StringUtils.isNotBlank(model.getLoadWarehouse())){
                loadAddress.append("【" + model.getLoadWarehouse()+"】");
            }
            dto.setLoadAddress(loadAddress.toString());

            StringBuilder unLoadAddress = new StringBuilder();
            unLoadAddress.append(model.getUnloadProvinceName());
            unLoadAddress.append(model.getUnloadCityName());
            unLoadAddress.append(model.getUnloadAreaName());
            if(StringUtils.isNotBlank(model.getUnloadWarehouse())){
                unLoadAddress.append("【" + model.getUnloadWarehouse() +"】");
            }
            dto.setUnloadAddress(unLoadAddress.toString());

            List<CarrierOrderGoodsResponseDto> goodsList = new ArrayList<>();
            CarrierOrderGoodsResponseDto goodsDto;
            if(ListUtils.isNotEmpty(model.getGoodsList())){
                for (CarrierOrderGoodsResponseModel temp : model.getGoodsList()) {
                    goodsDto = new CarrierOrderGoodsResponseDto();
                    goodsDto.setGoodsId(ConverterUtils.toString(temp.getGoodsId()));
                    goodsDto.setGoodsName(temp.getGoodsName());
                    goodsDto.setExpectAmount(temp.getExpectAmount().stripTrailingZeros().toPlainString());
                    goodsDto.setLoadAmount(temp.getLoadAmount().stripTrailingZeros().toPlainString());
                    goodsList.add(goodsDto);
                }
            }
            dto.setGoodsList(goodsList);
        }
    }
}
