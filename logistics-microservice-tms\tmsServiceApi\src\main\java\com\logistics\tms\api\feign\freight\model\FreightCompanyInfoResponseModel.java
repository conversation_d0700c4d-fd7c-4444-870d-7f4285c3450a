package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/12/30 19:50
 */
@Data
public class FreightCompanyInfoResponseModel {
    @ApiModelProperty("运价ID")
    private Long freightId;
    @ApiModelProperty("业务表公司ID")
    private Long companyId;
    @ApiModelProperty("业务表公司名称")
    private String companyName;
    @ApiModelProperty("启用 1 禁用 0")
    private Integer enabled;
}
