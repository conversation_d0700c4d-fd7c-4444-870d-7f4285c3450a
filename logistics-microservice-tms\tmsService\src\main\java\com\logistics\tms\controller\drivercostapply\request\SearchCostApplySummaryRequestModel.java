package com.logistics.tms.controller.drivercostapply.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/8/3 15:04
 */
@Data
public class SearchCostApplySummaryRequestModel extends AbstractPageForm<SearchCostApplySummaryRequestModel> {

    @ApiModelProperty("司机")
    private String staffName;

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("发生时间-起始")
    private String occurrenceTimeStart;

    @ApiModelProperty("发生时间-结束")
    private String occurrenceTimeEnd;
}
