package com.logistics.tms.biz.region

import com.logistics.tms.controller.region.request.EnableRegionRequestModel
import com.logistics.tms.controller.region.request.RegionDetailRequestModel
import com.logistics.tms.controller.region.response.RegionDetailResponseModel
import com.logistics.tms.controller.region.request.RemoveRegionRequestModel
import com.logistics.tms.controller.region.request.SaveOrModifyRegionRequestModel
import com.logistics.tms.controller.region.response.SearchRegionDetailResponseModel
import com.logistics.tms.controller.region.request.SearchRegionRequestModel
import com.logistics.tms.controller.region.response.SearchRegionResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.controller.region.request.SearchRegionDetailRequestModel
import com.logistics.tms.entity.TRegion
import com.logistics.tms.entity.TRegionItem
import com.logistics.tms.mapper.TRegionItemMapper
import com.logistics.tms.mapper.TRegionMapper
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class RegionBizTest extends Specification {
    @Mock
    CommonBiz commonBiz
    @Mock
    TRegionMapper tRegionMapper
    @Mock
    TRegionItemMapper tRegionItemMapper
    @Mock
    Logger log
    @InjectMocks
    RegionBiz regionBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "save Or Modify Region where requestModel=#requestModel"() {
        given:
        when(tRegionMapper.findByName(anyString())).thenReturn(new TRegion(regionName: "regionName", contactName: "contactName", contactPhone: "contactPhone", enabled: 0))
        when(tRegionItemMapper.getByNotRegionId(anyLong())).thenReturn([new TRegionItem(regionId: 1l, provinceId: 1l, provinceName: "provinceName", cityId: 1l, cityName: "cityName")])
        when(tRegionItemMapper.selectByRegionId(anyLong())).thenReturn([new TRegionItem(regionId: 1l, provinceId: 1l, provinceName: "provinceName", cityId: 1l, cityName: "cityName")])
        when(tRegionItemMapper.batchInsert(any())).thenReturn(0)
        when(tRegionItemMapper.batchUpdate(any())).thenReturn(0)

        expect:
        regionBiz.saveOrModifyRegion(requestModel)
        assert expectedResult == false

        where:
        requestModel                         || expectedResult
        new SaveOrModifyRegionRequestModel() || true
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tRegionMapper.getDetail(anyLong())).thenReturn(new RegionDetailResponseModel())

        expect:
        regionBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                   || expectedResult
        new RegionDetailRequestModel() || new RegionDetailResponseModel()
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tRegionMapper.searchListIds(any())).thenReturn([1l])
        when(tRegionMapper.searchList(anyString())).thenReturn([new SearchRegionResponseModel()])

        expect:
        regionBiz.searchList(requestModel) == expectedResult

        where:
        requestModel                   || expectedResult
        new SearchRegionRequestModel() || null
    }

    @Unroll
    def "enable Or Disable where requestModel=#requestModel"() {
        given:
        when(tRegionMapper.getByIds(anyString())).thenReturn([new TRegion(enabled: 0)])

        expect:
        regionBiz.enableOrDisable(requestModel)
        assert expectedResult == false

        where:
        requestModel                   || expectedResult
        new EnableRegionRequestModel() || true
    }

    @Unroll
    def "remove Region where requestModel=#requestModel"() {
        given:
        when(tRegionItemMapper.selectByRegionId(anyLong())).thenReturn([new TRegionItem()])
        when(tRegionItemMapper.batchUpdate(any())).thenReturn(0)

        expect:
        regionBiz.removeRegion(requestModel)
        assert expectedResult == false

        where:
        requestModel                   || expectedResult
        new RemoveRegionRequestModel() || true
    }

    @Unroll
    def "search Detail List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tRegionItemMapper.searchDetailList(any())).thenReturn([new SearchRegionDetailResponseModel()])

        expect:
        regionBiz.searchDetailList(requestModel) == expectedResult

        where:
        requestModel                                                                  || expectedResult
        new SearchRegionDetailRequestModel() || null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme