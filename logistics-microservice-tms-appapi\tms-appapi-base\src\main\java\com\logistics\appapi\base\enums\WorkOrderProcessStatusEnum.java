package com.logistics.appapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum WorkOrderProcessStatusEnum {

    DEFAULT(-1, ""),
    WAITING_TASK(0, "待处理"),
    BEING_PROCESSED(10, "处理中"),
    PROCESSED(20, "已处理"),
    CLOSED(30, "已关闭"),
    RESCINDED(40, "已撤销"),
    RESUBMIT(50, "重新提交"),
    ;

    private final Integer key;
    private final String value;

    public static WorkOrderProcessStatusEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
