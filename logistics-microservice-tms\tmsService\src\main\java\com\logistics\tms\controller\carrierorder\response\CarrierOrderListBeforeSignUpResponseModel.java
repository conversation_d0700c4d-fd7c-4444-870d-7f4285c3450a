package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CarrierOrderListBeforeSignUpResponseModel {

    @ApiModelProperty("运单ID")
    private Long carrierOrderId;
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;
    @ApiModelProperty("调度单ID")
    private Long dispatchOrderId;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;

    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty("运单状态")
    private Integer status;
    @ApiModelProperty("是否取消")
    private Integer ifCancel;
    @ApiModelProperty("是否放空")
    private Integer ifEmpty;
    @ApiModelProperty("委托费用类型 1 单价 2 一口价")
    private Integer entrustFreightType;
    @ApiModelProperty("预计委托费用")
    private BigDecimal entrustFreight;
    @ApiModelProperty("签收运费")
    private BigDecimal signFreightFee;
    @ApiModelProperty("装货数量")
    private BigDecimal loadAmount;
    @ApiModelProperty("卸货数量")
    private BigDecimal unloadAmount;
    @ApiModelProperty("预提数量")
    private BigDecimal expectAmount;
    @ApiModelProperty("运单下的货物信息")
    private List<CarrierOrderListBeforeSignUpGoodsModel> goodsInfo;
    private BigDecimal signAmount;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;

    @ApiModelProperty("司机运费价格类型 1 单价 2 一口价")
    private Integer dispatchFreightFeeType;
    @ApiModelProperty("司机运费")
    private BigDecimal dispatchFreightFee;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("多装多卸加价费用")
    private BigDecimal markupFee;

    @ApiModelProperty("结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer settlementTonnage;
    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;
    @ApiModelProperty("委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生")
    private Integer source;
    @ApiModelProperty("出库状态：0 待出库，1 部分出库，2 已出库")
    private Integer outStatus;

    @ApiModelProperty("预计里程数")
    private BigDecimal expectMileage;

    @ApiModelProperty("项目标签")
    private String projectLabel;


    @ApiModelProperty("议价模式：1 指定车主，2 竞价抢单，3 临时定价，4 零担定价")
    private Integer bargainingMode;

    /**
     * 是否按码回收 0：否 1：是
     */
    @ApiModelProperty("是否按码回收 0：否 1：是")
    private Integer ifRecycleByCode;

}
