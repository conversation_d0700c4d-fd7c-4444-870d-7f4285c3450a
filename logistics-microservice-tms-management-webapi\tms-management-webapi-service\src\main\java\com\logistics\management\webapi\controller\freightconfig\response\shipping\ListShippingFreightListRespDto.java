package com.logistics.management.webapi.controller.freightconfig.response.shipping;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class ListShippingFreightListRespDto {


    /**
     * 主键id
     */
    private String shippingFreightId = "";


    /**
     * 运价规则名称
     */
    private String carrierPriceRuleName = "";


    /**
     * 适用业务类型文本
     */
    private String entrustTypeLabel = "";

    /**
     * 承运商数量
     */
    private String companyCarrierCount = "";



    /**
     * 承运商ids集合（回显关联时候用）
     */
    private List<String> companyCarrierIds = new ArrayList<>();


    /**
     * 备注
     */
    private String remark = "";


    /**
     * 创建人
     */
    private String createdBy = "";

    /**
     * 创建时间
     */
    private String createdTime = "";


    /**
     * 操作人
     */
    private String lastModifiedBy = "";

    /**
     * 操作时间
     */
    private String lastModifiedTime = "";


}
