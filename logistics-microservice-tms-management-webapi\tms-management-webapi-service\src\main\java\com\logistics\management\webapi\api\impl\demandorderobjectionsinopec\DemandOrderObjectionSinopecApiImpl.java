package com.logistics.management.webapi.api.impl.demandorderobjectionsinopec;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import com.logistics.management.webapi.api.feign.demandorderobjectionsinopec.DemandOrderObjectionSinopecApi;
import com.logistics.management.webapi.api.feign.demandorderobjectionsinopec.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.demandorderobjectionsinopec.mapping.GetSinopecObjectionDetailMapping;
import com.logistics.management.webapi.api.impl.demandorderobjectionsinopec.mapping.SearchSinopecObjectionMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.DemandOrderObjectionSinopecServiceApi;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2022/5/30 14:04
 */
@RestController
public class DemandOrderObjectionSinopecApiImpl implements DemandOrderObjectionSinopecApi {

    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private DemandOrderObjectionSinopecServiceApi demandOrderObjectionSinopecServiceApi;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 中石化需求单异常列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchDemandOrderObjectionSinopecResponseDto>> searchSinopecObjection(@RequestBody SearchDemandOrderObjectionSinopecRequestDto requestDto) {
        Result<PageInfo<SearchDemandOrderObjectionSinopecResponseModel>> result = demandOrderObjectionSinopecServiceApi.searchSinopecObjection(MapperUtils.mapper(requestDto, SearchDemandOrderObjectionSinopecRequestModel.class));
        result.throwException();
        //获取带签名的图片路径
        List<String> sourceSrcList=new ArrayList<>();
        if (ListUtils.isNotEmpty(result.getData().getList())) {
            for (SearchDemandOrderObjectionSinopecResponseModel model : result.getData().getList()) {
                if (ListUtils.isNotEmpty(model.getAuditTicketList())){
                    sourceSrcList.addAll(model.getAuditTicketList());
                }
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        //分页信息转换
        PageInfo pageInfo = result.getData();
        List<SearchDemandOrderObjectionSinopecResponseDto> list = MapperUtils.mapper(pageInfo.getList(), SearchDemandOrderObjectionSinopecResponseDto.class, new SearchSinopecObjectionMapping(configKeyConstant.fileAccessAddress,imageMap));
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 中石化需求单异常详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<GetSinopecObjectionDetailResponseDto> getSinopecObjectionDetail(@RequestBody @Valid GetSinopecObjectionDetailRequestDto requestDto) {
        Result<GetSinopecObjectionDetailResponseModel> result = demandOrderObjectionSinopecServiceApi.getSinopecObjectionDetail(MapperUtils.mapper(requestDto, GetSinopecObjectionDetailRequestModel.class));
        result.throwException();
        //获取带签名的图片路径
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(result.getData().getAuditTicketList());
        return Result.success(MapperUtils.mapper(result.getData(), GetSinopecObjectionDetailResponseDto.class, new GetSinopecObjectionDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 中石化需求单异常审核
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> sinopecObjectionAudit(@RequestBody @Valid SinopecObjectionAuditRequestDto requestDto) {
        //审核结果只能传1或2
        if (!CommonConstant.ONE.equals(requestDto.getAuditStatus()) && !CommonConstant.TWO.equals(requestDto.getAuditStatus())){
            throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
        }
        //审核异常类型只能传1或2
        if (!CommonConstant.ONE.equals(requestDto.getAuditObjectionType()) && !CommonConstant.TWO.equals(requestDto.getAuditObjectionType())){
            throw new BizException(ManagementWebApiExceptionEnum.PARAMETER_ERROR);
        }
        //审核通过、类型为取消时，审核备注必填
        if (CommonConstant.ONE.equals(requestDto.getAuditStatus()) && CommonConstant.TWO.equals(requestDto.getAuditObjectionType()) && StringUtils.isBlank(requestDto.getAuditRemark())){
            throw new BizException(ManagementWebApiExceptionEnum.REMARK_EMPTY);
        }
        //审核依据图片最多传2张
        if (requestDto.getAuditTicketList().size() > CommonConstant.INTEGER_TWO){
            throw new BizException(ManagementWebApiExceptionEnum.SINOPEC_OBJECTION_TICKETS_SIZE_MAX);
        }
        return demandOrderObjectionSinopecServiceApi.sinopecObjectionAudit(MapperUtils.mapper(requestDto, SinopecObjectionAuditRequestModel.class));
    }
}
