package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @author: wjf
 * @date: 2021/10/18 13:10
 */
@Data
public class CarrierOrderEmptyRequestDto {

    @ApiModelProperty("运单d")
    @NotBlank(message = "请选中数据")
    private String carrierOrderId;

    @ApiModelProperty("放空原因类型：10 数量问题，20 重报问题，30 联系问题，40 地址问题，50 装车问题，60 等待问题，70 其他问题")
    @NotBlank(message = "请选择放空原因")
    private String objectionType;

    @ApiModelProperty("放空描述")
    @Size(min = 1, max = 50, message = "请维护放空描述1~50字")
    private String objectionReason;
}
