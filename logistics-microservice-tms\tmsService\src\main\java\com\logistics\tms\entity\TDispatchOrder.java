package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2023/09/27
*/
@Data
public class TDispatchOrder extends BaseEntity {
    /**
    * 调度单号
    */
    @ApiModelProperty("调度单号")
    private String dispatchOrderCode;

    /**
    * 运单数
    */
    @ApiModelProperty("运单数")
    private Integer carrierOrderCount;

    /**
    * 车辆ID
    */
    @ApiModelProperty("车辆ID")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 挂车车辆ID
    */
    @ApiModelProperty("挂车车辆ID")
    private Long trailerVehicleId;

    /**
    * 挂车车牌号
    */
    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;

    /**
    * 司机ID
    */
    @ApiModelProperty("司机ID")
    private Long driverId;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String driverName;

    /**
    * 司机手机号
    */
    @ApiModelProperty("司机手机号")
    private String driverMobile;

    /**
    * 司机身份证号码
    */
    @ApiModelProperty("司机身份证号码")
    private String driverIdentity;

    /**
    * 预计到货时间
    */
    @ApiModelProperty("预计到货时间")
    private Date expectArrivalTime;

    /**
    * 乐医委托费用类型 1 单价 2 一口价
    */
    @ApiModelProperty("乐医委托费用类型 1 单价 2 一口价")
    private Integer entrustFreightType;

    /**
    * 乐医委托费用
    */
    @ApiModelProperty("乐医委托费用")
    private BigDecimal entrustFreight;

    /**
    * 司机调度运费价格类型 1 单价 2 一口价
    */
    @ApiModelProperty("司机调度运费价格类型 1 单价 2 一口价")
    private Integer dispatchFreightFeeType;

    /**
    * 合计运费
    */
    @ApiModelProperty("合计运费")
    private BigDecimal dispatchFreightFee;

    /**
    * 装货数
    */
    @ApiModelProperty("装货数")
    private Integer loadPointAmount;

    /**
    * 卸货数
    */
    @ApiModelProperty("卸货数")
    private Integer unloadPointAmount;

    /**
    * 多装多卸加价费用
    */
    @ApiModelProperty("多装多卸加价费用")
    private BigDecimal markupFee;

    /**
    * 是否调整费用0 否 1 是
    */
    @ApiModelProperty("是否调整费用0 否 1 是")
    private Integer ifAdjust;

    /**
    * 调整费用类型 1 + 2 -
    */
    @ApiModelProperty("调整费用类型 1 + 2 -")
    private Integer adjustFeeType;

    /**
    * 调整费用
    */
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;

    /**
    * 调整说明
    */
    @ApiModelProperty("调整说明")
    private String adjustRemark;

    /**
    * 货物单位：1 件，2 吨，方
    */
    @ApiModelProperty("货物单位：1 件，2 吨，方")
    private Integer goodsUnit;

    /**
    * 来源：1 后台 2 前台 3 app
    */
    @ApiModelProperty("来源：1 后台 2 前台 3 app")
    private Integer source;

    /**
    * 调度人ID
    */
    @ApiModelProperty("调度人ID")
    private Long dispatchUserId;

    /**
    * 调度人姓名
    */
    @ApiModelProperty("调度人姓名")
    private String dispatchUserName;

    /**
    * 调度时间
    */
    @ApiModelProperty("调度时间")
    private Date dispatchTime;

    /**
    * 调度单备注
    */
    @ApiModelProperty("调度单备注")
    private String remark;

    /**
    * 车主公司类型：1 公司，2 个人
    */
    @ApiModelProperty("车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;

    /**
    * 车主id
    */
    @ApiModelProperty("车主id")
    private Long companyCarrierId;

    /**
    * 车主公司名称
    */
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;

    /**
    * 车主联系人id
    */
    @ApiModelProperty("车主联系人id")
    private Long carrierContactId;

    /**
    * 车主账号名称
    */
    @ApiModelProperty("车主账号名称")
    private String carrierContactName;

    /**
    * 车主账号手机号
    */
    @ApiModelProperty("车主账号手机号")
    private String carrierContactPhone;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}