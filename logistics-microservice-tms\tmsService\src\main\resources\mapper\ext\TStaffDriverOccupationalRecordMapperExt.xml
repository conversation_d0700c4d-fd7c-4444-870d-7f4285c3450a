<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TStaffDriverOccupationalRecordMapper" >

  <select id="selectOccupationalList" resultType="com.logistics.tms.controller.staff.response.OccupationalListResponseModel">

    SELECT
    id as occupationalRecordId,
    staff_id as staffId,
    issue_date as issueDate,
    valid_date as validDate,
    remark,
    last_modified_by as lastModifiedBy,
    last_modified_time as lastModifiedTime
    from t_staff_driver_occupational_record
    where valid = 1 and staff_id = #{staffId,jdbcType=BIGINT}
    order by valid_date desc,id desc
  </select>

    <select id="getDueOccupationalCount" resultType="java.util.HashMap">
        select
        ifnull(group_concat(if(thrityDueCount = 1, staff_id, null)), '') as totalIds,
        ifnull(group_concat(if(sevenDueCount = 1, staff_id, null)), '')  as weekIds,
        ifnull(group_concat(if(dueCount = 1, staff_id, null)), '')       as hasExpiredIds,
        ifnull(sum(thrityDueCount), 0)                                   as total,
        ifnull(sum(sevenDueCount), 0)                                    as week,
        ifnull(sum(dueCount), 0)                                         as hasExpired
        from (SELECT tcdr.id                                                                                                                                        as staff_id,
               if(DATE_SUB(max(valid_date), INTERVAL #{remindDays,jdbcType=INTEGER} DAY) &lt;= DATE_FORMAT(now(), '%Y-%m-%d') and max(valid_date) >= DATE_FORMAT(now(), '%Y-%m-%d'), 1,
                  0)                                                                                                                                          as thrityDueCount,
               if(DATE_SUB(max(valid_date), INTERVAL 7 DAY) &lt;= DATE_FORMAT(now(), '%Y-%m-%d') and max(valid_date) >= DATE_FORMAT(now(), '%Y-%m-%d'), 1, 0) as sevenDueCount,
               if(max(valid_date) &lt; DATE_FORMAT(now(), '%Y-%m-%d'), 1, 0)                                                                                  as dueCount
              from t_staff_driver_occupational_record t1
              left join t_staff_basic t2 on t1.staff_id = t2.id
              left join t_carrier_driver_relation tcdr on tcdr.valid = 1 and tcdr.driver_id = t2.id and tcdr.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
              where t1.valid = 1
              and t2.valid = 1
              and tcdr.id is not null
              group by staff_id) tmp
  </select>
    
    <update id="batchUpdate">
        <foreach collection="list" separator=";" item="item">
            update t_staff_driver_occupational_record
            <set >
                <if test="item.staffId != null" >
                    staff_id = #{item.staffId,jdbcType=BIGINT},
                </if>
                <if test="item.issueDate != null" >
                    issue_date = #{item.issueDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.validDate != null" >
                    valid_date = #{item.validDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null" >
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>

    </update>
    
    <select id="selectRecordByStaffIdAndValidDate" resultType="int">
        select count(0)
        from t_staff_driver_occupational_record
        where valid = 1
        and staff_id = #{staffId,jdbcType=BIGINT}
        <if test="issueDate!=null">
            and issue_date = #{issueDate,jdbcType=BIGINT}
        </if>
        <if test="issueDate==null">
            and issue_date is null
        </if>
        <if test="validDate!=null">
            and valid_date = #{validDate,jdbcType=BIGINT}
        </if>
        <if test="validDate==null">
            and valid_date is null
        </if>
    </select>

    <select id="getOccupationalRecordsByStaffId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_staff_driver_occupational_record
        where staff_id in (${staffIds})
        and valid = 1
        order  by staff_id asc,valid_date desc
    </select>
</mapper>