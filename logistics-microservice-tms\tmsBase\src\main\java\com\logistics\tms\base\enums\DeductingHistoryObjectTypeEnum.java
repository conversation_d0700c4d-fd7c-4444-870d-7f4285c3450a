package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2019/10/8 13:54
 */
public enum DeductingHistoryObjectTypeEnum {
    T_GPS_FEE(1,"gps费用表"),
    T_PARKING_FEE(2,"停车费用表"),
    ;
    private Integer key;
    private String value;

    DeductingHistoryObjectTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
