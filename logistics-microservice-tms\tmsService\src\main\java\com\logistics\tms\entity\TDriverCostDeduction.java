package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2023/07/31
*/
@Data
public class TDriverCostDeduction extends BaseEntity {
    /**
    * 司机费用申请表id
    */
    @ApiModelProperty("司机费用申请表id")
    private Long driverCostApplyId;

    /**
    * 备用金申请表id
    */
    @ApiModelProperty("备用金申请表id")
    private Long reserveApplyId;

    /**
    * 备用金申请单号
    */
    @ApiModelProperty("备用金申请单号")
    private String reserveApplyCode;

    /**
    * 备用金类型: 1 申请，2 垫付，3 红冲退款
    */
    @ApiModelProperty("备用金类型: 1 申请，2 垫付，3 红冲退款")
    private Integer reserveType;

    /**
    * 余额
    */
    @ApiModelProperty("余额")
    private BigDecimal balanceAmount;

    /**
    * 核销金额
    */
    @ApiModelProperty("核销金额")
    private BigDecimal verificationAmount;
}