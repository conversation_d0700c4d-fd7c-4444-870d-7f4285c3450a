package com.logistics.tms.api.feign.extvehiclesettlement;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.extvehiclesettlement.hystrix.ExtVehicleSettlementServiceApiHystrix;
import com.logistics.tms.api.feign.extvehiclesettlement.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2019/11/20 13:29
 */
@Api(value = "API-外部车辆结算管理")
@FeignClient(name = "logistics-tms-services", fallback = ExtVehicleSettlementServiceApiHystrix.class)
public interface ExtVehicleSettlementServiceApi {

    @ApiOperation("列表")
    @PostMapping(value = "/service/extVehicleSettlement/searchExtVehicleSettlementList")
    Result<PageInfo<SearchExtVehicleSettlementListResponseModel>> searchExtVehicleSettlementList(@RequestBody SearchExtVehicleSettlementListRequestModel requestModel);

    @ApiOperation("详情")
    @PostMapping(value = "/service/extVehicleSettlement/extVehicleSettlementDetail")
    Result<ExtVehicleSettlementDetailResponseModel> extVehicleSettlementDetail(@RequestBody ExtVehicleSettlementIdRequestModel requestModel);

    @ApiOperation("付款")
    @PostMapping(value = "/service/extVehicleSettlement/extVehicleSettlementPayment")
    Result extVehicleSettlementPayment(@RequestBody ExtVehicleSettlementPaymentRequestModel requestModel);

    @ApiOperation("回退")
    @PostMapping(value = "/service/extVehicleSettlement/extVehicleSettlementFallback")
    Result extVehicleSettlementFallback(@RequestBody ExtVehicleSettlementFallbackRequestModel requestModel);
}
