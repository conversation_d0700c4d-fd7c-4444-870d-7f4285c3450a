package com.logistics.management.webapi.api.feign.entrustaddress.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2018/12/21 15:55
 */
@Data
public class SearchEntrustAddressRequestDto extends AbstractPageForm<SearchEntrustAddressRequestDto> {
    @ApiModelProperty("地址类型：1 发货，2 收货")
    @NotBlank(message = "地址类型不能为空")
    private String addressType;
}
