package com.logistics.tms.api.feign.carriercontact.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class SaveCarrierContactRequestModel {

    @ApiModelProperty("车主账号id")
    private Long carrierContactId;

    @ApiModelProperty(value = "公司id")
    private Long companyCarrierId;

    @ApiModelProperty("姓名")
    private String contactName;

    @ApiModelProperty(value = "手机号")
    private String contactPhone;

    @ApiModelProperty(value = "身份证号")
    private String identityNumber;

}
