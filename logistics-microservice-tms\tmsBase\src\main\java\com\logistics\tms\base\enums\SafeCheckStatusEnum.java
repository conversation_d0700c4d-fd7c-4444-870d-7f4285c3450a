package com.logistics.tms.base.enums;

/**
 * @Author: sj
 * @Date: 2019/11/8 14:41
 */
public enum SafeCheckStatusEnum {
    NULL(-1,""),
    NOT_CHECK(0,"未检查"),
    WAIT_CONFIRM(10, "待确认"),
    WAIT_REFORM(20,"待整改"),
    HAS_REFORM(30,"已整改"),
    HAS_CHECK(40,"检查完成")

    ;

    private Integer key;
    private String value;

    SafeCheckStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
