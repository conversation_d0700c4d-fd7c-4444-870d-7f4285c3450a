package com.logistics.management.webapi.base.enums;

/**
 * @Author: sj
 * @Date: 2019/6/10 17:33
 */
public enum IfInstallGpsEnum {
    NULL(-1,""),
    NOT(0,"否"),
    YES(1,"是"),
    ;

    private Integer key;
    private String value;

    IfInstallGpsEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static IfInstallGpsEnum getEnum(Integer key) {
        for (IfInstallGpsEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return NULL;
    }

    public static IfInstallGpsEnum getEnumByName(String value) {
        for (IfInstallGpsEnum t : values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return null;
    }
}
