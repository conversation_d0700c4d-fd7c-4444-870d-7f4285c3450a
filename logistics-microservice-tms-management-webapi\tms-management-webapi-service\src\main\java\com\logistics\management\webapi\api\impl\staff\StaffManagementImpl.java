package com.logistics.management.webapi.api.impl.staff;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.staff.StaffManagementApi;
import com.logistics.management.webapi.api.feign.staff.dto.*;
import com.logistics.management.webapi.api.feign.violationregulation.dto.FuzzyQueryDriverInfoRequestDto;
import com.logistics.management.webapi.api.feign.violationregulation.dto.FuzzyQueryDriverInfoResponseDto;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.staff.mapping.ExportStaffManagementListMapping;
import com.logistics.management.webapi.api.impl.staff.mapping.GetStaffDetailMapping;
import com.logistics.management.webapi.api.impl.staff.mapping.SearchStaffManagementListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportExcelStaff;
import com.logistics.management.webapi.base.constant.ImportExcelHeaders;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.staff.StaffManagementClient;
import com.logistics.management.webapi.client.staff.request.FuzzyQueryDriverInfoFeignRequestModel;
import com.logistics.management.webapi.client.staff.response.FuzzyQueryDriverInfoFeignResponseModel;
import com.logistics.tms.api.feign.common.SrcUrlModel;
import com.logistics.tms.api.feign.common.model.CertificatePictureModel;
import com.logistics.tms.api.feign.staff.StaffManagementServiceApi;
import com.logistics.tms.api.feign.staff.model.*;
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoRequestModel;
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryDriverInfoResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class StaffManagementImpl implements StaffManagementApi {

    @Autowired
    private StaffManagementServiceApi staffManagementServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;
    @Resource
    private StaffManagementClient staffManagementClient;

    /**
     * 列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchStaffManagementListResponseDto>> searchStaffManagementList(@RequestBody SearchStaffManagementListRequestDto requestDto) {
        Result<PageInfo<SearchStaffManagementListResponseModel>> result = staffManagementServiceApi.searchStaffManagementList(MapperUtils.mapperNoDefault(requestDto,SearchStaffManagementListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchStaffManagementListResponseDto> dtoList = MapperUtils.mapper(pageInfo.getList(),SearchStaffManagementListResponseDto.class,new SearchStaffManagementListMapping());
        pageInfo.setList(dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 人员信息新增/修改
     *
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addOrModifyStaff(@RequestBody @Valid AddOrModifyStaffRequestDto requestDto) {
        //身份证号码校验
        if (StringUtils.isNotBlank(requestDto.getIdentityNumber()) && !IDCardValidator.isValidatedAllIdcard(requestDto.getIdentityNumber())) {
            throw new BizException(ManagementWebApiExceptionEnum.ID_CARD_FORMAT_ERROR);
        }
        //证件校验
        List<String> fileTypeList = requestDto.getTicketsList().stream().map(AddOrModifyStaffTicketsRequestDto::getFileType).distinct().collect(Collectors.toList());
        if (ListUtils.isEmpty(fileTypeList)) {
            throw new BizException(ManagementWebApiExceptionEnum.DRIVER_TICKETS_EMPTY);
        }
        if (!fileTypeList.contains(StaffDriverCertificateEnum.STAFF_IDENTITY_FRONT.getKeyStr()) || !fileTypeList.contains(StaffDriverCertificateEnum.STAFF_IDENTITY_BACK.getKeyStr())) {
            throw new BizException(ManagementWebApiExceptionEnum.IDENTITY_NUMBER_EMPTY);
        }
        if (!fileTypeList.contains(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_FRONT.getKeyStr()) && !fileTypeList.contains(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_BACK.getKeyStr())
                && !fileTypeList.contains(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_FRONT.getKeyStr()) && !fileTypeList.contains(StaffDriverCertificateEnum.DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_BACK.getKeyStr())) {
            throw new BizException(ManagementWebApiExceptionEnum.OCCUPATIONAL_REQUIREMENTS_EMPTY);
        }

        //人员类别校验
        if (!requestDto.getType().equals(StaffTypeEnum.DRIVER.getKeyStr()) && !requestDto.getType().equals(StaffTypeEnum.SUPERCARGO.getKeyStr()) && !requestDto.getType().equals(StaffTypeEnum.DRIVER_SUPERCARGO.getKeyStr())) {
            throw new BizException(ManagementWebApiExceptionEnum.STAFF_TYPE_ERROR);
        }
        //性别校验
        if (!requestDto.getGender().equals(GenderEnum.MAN.getKeyStr()) && !requestDto.getGender().equals(GenderEnum.WOMAN.getKeyStr())) {
            throw new BizException(ManagementWebApiExceptionEnum.GENDER_ERROR);
        }

        //驾驶员校验项
        if (requestDto.getType().equals(StaffTypeEnum.DRIVER.getKeyStr()) || requestDto.getType().equals(StaffTypeEnum.DRIVER_SUPERCARGO.getKeyStr())) {
            //驾驶证
            if (!fileTypeList.contains(StaffDriverCertificateEnum.DRIVER_LICENSE_FRONT.getKeyStr())) {
                throw new BizException(ManagementWebApiExceptionEnum.DRIVER_LICENSE_EMPTY);
            }
            //驾驶证号
            if (!FrequentMethodUtils.checkReg(requestDto.getDriversLicenseNo(), "^[a-zA-Z0-9]{1,50}$")) {
                throw new BizException(ManagementWebApiExceptionEnum.DRIVER_LICENSE_NO_ERROR);
            }
            //准假车型
            if (!FrequentMethodUtils.validatePermittedType(requestDto.getPermittedType())) {
                throw new BizException(ManagementWebApiExceptionEnum.PERMITTED_TYPE_ERROR);
            }
            //驾驶证有效期
            if (StringUtils.isBlank(requestDto.getDriversLicenseDateFrom()) || StringUtils.isBlank(requestDto.getDriversLicenseDateTo())) {
                throw new BizException(ManagementWebApiExceptionEnum.DRIVER_LICENSE_DATE);
            }
        }
        Result<Boolean> result = staffManagementServiceApi.addOrModifyStaff(MapperUtils.mapperNoDefault(requestDto, AddOrModifyStaffTicketsRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 外部人员信息新增/修改
     *
     * @param requestDto
     * @return
     */
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @Override
    public Result<Boolean>  addOrModifyExternalStaff(@RequestBody @Valid AddOrModifyExternalStaffRequestDto requestDto) {
        if(StringUtils.isNotBlank(requestDto.getIdentityNumber()) && !IDCardValidator.isValidatedAllIdcard(requestDto.getIdentityNumber())){
            throw new BizException(ManagementWebApiExceptionEnum.ID_CARD_FORMAT_ERROR);
        }
        Result<Boolean> result = staffManagementServiceApi.addOrModifyExternalStaff(MapperUtils.mapper(requestDto, AddOrModifyExternalStaffRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 查询人员信息详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<GetStaffDetailResponseDto> getStaffDetail(@RequestBody @Valid CarrierStaffIdRequestDto requestDto) {
        Result<GetStaffDetailResponseModel> result = staffManagementServiceApi.getStaffDetail(MapperUtils.mapper(requestDto, CarrierStaffIdRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (GetStaffTicketsResponseModel ticketsModel : result.getData().getTicketsList()) {
            sourceSrcList.add(ticketsModel.getFilePath());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),GetStaffDetailResponseDto.class,new GetStaffDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 导出人员资产信息
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportStaffList(SearchStaffManagementListRequestDto requestDto, HttpServletResponse response) {
        Result<List<ExportStaffManagementListResponseModel>> result = staffManagementServiceApi.exportStaffList(MapperUtils.mapperNoDefault(requestDto,SearchStaffManagementListRequestModel.class));
        result.throwException();
        List<ExportStaffManagementListResponseDto> list = MapperUtils.mapper(result.getData(),ExportStaffManagementListResponseDto.class,new ExportStaffManagementListMapping());
        String fileName = "人员信息管理" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportExcelStaff.getExportStaff();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * 从业资格证列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<OccupationalListResponseDto>> occupationalList(@RequestBody@Valid  StaffIdRequestDto requestDto) {
        Result<List<OccupationalListResponseModel>> listResult = staffManagementServiceApi.occupationalList(MapperUtils.mapper(requestDto, StaffIdRequestModel.class));
        listResult.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (OccupationalListResponseModel responseModel : listResult.getData()) {
            for (CertificatePictureModel certificatePictureModel : responseModel.getImageList()) {
                sourceSrcList.add(certificatePictureModel.getFilePath());
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        List<OccupationalListResponseDto> occupationalListResponseDto = MapperUtils.mapper(listResult.getData(),OccupationalListResponseDto.class);
        if(ListUtils.isNotEmpty(occupationalListResponseDto)){
            occupationalListResponseDto.stream().forEach(tmp->{
                if(ListUtils.isNotEmpty(tmp.getImageList())){
                    tmp.getImageList().stream().forEach(tmp2->{
                        if(StringUtils.isNotBlank(tmp2.getFilePath())){
                            tmp2.setFilePathSrc(configKeyConstant.fileAccessAddress+imageMap.get(tmp2.getFilePath()));
                        }
                    });
                }
                if(StringUtils.isNotBlank(tmp.getIssueDate())){
                    tmp.setIssueDate(tmp.getIssueDate().substring(0,tmp.getIssueDate().indexOf(' ')));
                }
                if(StringUtils.isNotBlank(tmp.getValidDate())){
                    tmp.setValidDate(tmp.getValidDate().substring(0,tmp.getValidDate().indexOf(' ')));
                }
            });
        }
        return Result.success(occupationalListResponseDto);
    }

    /**
     * 继续教育列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<ContinueLearningListResponseDto>> continueLearningList(@RequestBody@Valid  StaffIdRequestDto requestDto) {
        Result<List<ContinueLearningListResponseModel>> listResult = staffManagementServiceApi.continueLearningList(MapperUtils.mapper(requestDto, StaffIdRequestModel.class));
        listResult.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (ContinueLearningListResponseModel responseModel : listResult.getData()) {
            for (CertificatePictureModel certificatePictureModel : responseModel.getImageList()) {
                sourceSrcList.add(certificatePictureModel.getFilePath());
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        List<ContinueLearningListResponseDto> continueLearningListResponseDto = MapperUtils.mapper(listResult.getData(),ContinueLearningListResponseDto.class);
        if(ListUtils.isNotEmpty(continueLearningListResponseDto)){
            continueLearningListResponseDto.stream().forEach(tmp->{
                if(ListUtils.isNotEmpty(tmp.getImageList())){
                    tmp.getImageList().stream().forEach(tmp2->{
                        if(StringUtils.isNotBlank(tmp2.getFilePath())){
                            tmp2.setFilePathSrc(configKeyConstant.fileAccessAddress+imageMap.get(tmp2.getFilePath()));
                        }
                    });
                }
                if(StringUtils.isNotBlank(tmp.getValidDate())){
                    tmp.setValidDate(tmp.getValidDate().substring(0,tmp.getValidDate().lastIndexOf(' ')));
                }
            });
        }
        return Result.success(continueLearningListResponseDto);
    }

    /**
     * 诚信考核列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<IntegrityExaminationListResponseDto>> integrityExaminationList(@RequestBody@Valid StaffIdRequestDto requestDto) {
        Result<List<IntegrityExaminationListResponseModel>> listResult = staffManagementServiceApi.integrityExaminationList(MapperUtils.mapper(requestDto, StaffIdRequestModel.class));
        listResult.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (IntegrityExaminationListResponseModel responseModel : listResult.getData()) {
            for (CertificatePictureModel certificatePictureModel : responseModel.getImageList()) {
                sourceSrcList.add(certificatePictureModel.getFilePath());
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        List<IntegrityExaminationListResponseDto> examinationListResponseDto = MapperUtils.mapper(listResult.getData(),IntegrityExaminationListResponseDto.class);
        if(ListUtils.isNotEmpty(examinationListResponseDto)){
            examinationListResponseDto.stream().forEach(tmp->{
                if(ListUtils.isNotEmpty(tmp.getImageList())){
                    tmp.getImageList().stream().forEach(tmp2->{
                        if(StringUtils.isNotBlank(tmp2.getFilePath())){
                            tmp2.setFilePathSrc(configKeyConstant.fileAccessAddress+imageMap.get(tmp2.getFilePath()));
                        }
                    });
                }
                if(StringUtils.isNotBlank(tmp.getValidDate())){
                    tmp.setValidDate(tmp.getValidDate().substring(0,tmp.getValidDate().lastIndexOf('-')));
                }
            });
        }
        return Result.success(examinationListResponseDto);
    }

    /**
     * 导入
     * @param file
     * @param request
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ImportStaffResponseDto> importStaff(MultipartFile file, HttpServletRequest request) {
        if(null==file){
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_VEHICLE_TYPE_FILE_IS_EMPTY);
        }
        InputStream in;
        try{
            in=file.getInputStream();
        }catch (IOException e){
            log.error("导入人员信息失败，",e);
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_VEHICLE_TYPE_FILE_IS_EMPTY);
        }
        List<List<Object>> excelList=new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportStaffAssetsType());//JSON处理类
        ImportStaffRequestDto responseDto=this.initImportRepeatData(excelList);
        ImportStaffRequestModel importStaffRequestModel = transformRequestDtoDataType(responseDto);
        Result<ImportStaffResponseModel> result = staffManagementServiceApi.importStaff(importStaffRequestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),ImportStaffResponseDto.class));

    }

    /**
     * 导入人员证件
     * @param file
     * @param request
     * @return
     */
    @Override
    public Result importStaffCertificate(@RequestParam("file")MultipartFile file, HttpServletRequest request) {
        return staffManagementServiceApi.importStaffCertificate(MapperUtils.mapper(commonBiz.uploadToTmpCatalog(file), SrcUrlModel.class));
    }

    //从excel提取数据
    public ImportStaffRequestDto initImportRepeatData(List<List<Object>> excelList){
        ImportStaffRequestDto requestDto=new ImportStaffRequestDto();
        if(ListUtils.isEmpty(excelList)){
            return requestDto;
        }
        requestDto.setImportList(commonBiz.transferExcelToObject(ImportStaffListRequestDto.class,excelList));
        return requestDto;
    }
    //入参校验及转换
    private ImportStaffRequestModel transformRequestDtoDataType(ImportStaffRequestDto responseDto){
        if(ListUtils.isNotEmpty(responseDto.getImportList())){
            for(ImportStaffListRequestDto tmp : responseDto.getImportList()){
                ImportStaffUserTypeEnum userTypeEnum = ImportStaffUserTypeEnum.getEnum(tmp.getType());
                if(userTypeEnum!=null){
                    tmp.setType(userTypeEnum.getKey().toString());
                }
                ImportStaffGenderEnum importStaffGenderEnum = ImportStaffGenderEnum.getEnum(tmp.getGender());
                if(importStaffGenderEnum!=null) {
                    tmp.setGender(importStaffGenderEnum.getKey().toString());
                }

                ImportStaffPropertyEnum importStaffPropertyEnum = ImportStaffPropertyEnum.getEnumByValue(tmp.getStaffProperty());
                if((importStaffPropertyEnum != null)){
                    tmp.setStaffProperty(importStaffPropertyEnum.getStrKey());
                }
            }
        }
        ImportStaffRequestDto source = responseDto;
        ImportStaffRequestModel dest = new ImportStaffRequestModel();
        Integer failureCount = CommonConstant.INTEGER_ZERO;
        if(ListUtils.isNotEmpty(source.getImportList())){
            List<ImportStaffListRequestModel> staffListRequestModels = new ArrayList<>();
            dest.setImportList(staffListRequestModels);
            for(ImportStaffListRequestDto importStaffListRequestDto : source.getImportList() ){
                ImportStaffListRequestModel importStaffListRequestModel = new ImportStaffListRequestModel();

                ImportStaffPropertyEnum staffPropertyEnum = ImportStaffPropertyEnum.getEnum(ConverterUtils.toInteger(importStaffListRequestDto.getStaffProperty()));
                if(staffPropertyEnum == null){
                    failureCount++;
                    continue;
                }else{
                    importStaffListRequestModel.setStaffProperty(staffPropertyEnum.getKey());
                }

                ImportStaffUserTypeEnum userTypeEnum = ImportStaffUserTypeEnum.getEnumByKey(ConverterUtils.toInteger(importStaffListRequestDto.getType()));
                if(userTypeEnum == null){
                    failureCount++;
                    continue;
                }else{
                    importStaffListRequestModel.setType(userTypeEnum.getKey());
                }
                ImportStaffGenderEnum importStaffGenderEnum = ImportStaffGenderEnum.getEnumByKey(ConverterUtils.toInteger(importStaffListRequestDto.getGender()));
                if(importStaffGenderEnum==null){
                    failureCount++;
                    continue;
                }else{
                    importStaffListRequestModel.setGender(importStaffGenderEnum.getKey());
                }
                if(StringUtils.isBlank(importStaffListRequestDto.getName())){
                    failureCount++;
                    continue;
                }
                if(StringUtils.isBlank(importStaffListRequestDto.getMobile())||!FrequentMethodUtils.checkReg(importStaffListRequestDto.getMobile(),"\\d{11}")){
                    failureCount++;
                    continue;
                }
                if(StringUtils.isBlank(importStaffListRequestDto.getIdentityNumber())||!FrequentMethodUtils.checkReg(importStaffListRequestDto.getIdentityNumber(),"(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}$)|(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)")){
                    failureCount++;
                    continue;
                }
                if(StringUtils.isNotBlank(importStaffListRequestDto.getIdentityValidity())){
                    if(importStaffListRequestDto.getIdentityValidity().trim().equals(CommonConstant.FOREVER)){
                        importStaffListRequestModel.setIdentityIsForever(CommonConstant.INTEGER_ONE);
                    }else{
                        importStaffListRequestModel.setIdentityIsForever(CommonConstant.INTEGER_ZERO);
                            Date date = MapperUtils.mapperNoDefault(importStaffListRequestDto.getIdentityValidity(),Date.class);
                            if(date==null){
                                failureCount++;
                                continue;
                            }
                            importStaffListRequestModel.setIdentityValidity(date);
                    }

                }
                if(StringUtils.isNotBlank(importStaffListRequestDto.getOccupationalRequirementsCredentialNo())&&!FrequentMethodUtils.checkReg(importStaffListRequestDto.getOccupationalRequirementsCredentialNo(),"^[\\dA-Z]{1,50}$")){
                    failureCount++;
                    continue;
                }
                if(StringUtils.isNotBlank(importStaffListRequestDto.getInitialIssuanceDate())){
                        Date date  = MapperUtils.mapperNoDefault(importStaffListRequestDto.getInitialIssuanceDate(),Date.class);
                        if(date==null){
                            failureCount++;
                            continue;
                        }
                        importStaffListRequestModel.setInitialIssuanceDate(date);
                }
                if(StringUtils.isNotBlank(importStaffListRequestDto.getDriversLicenseNo())&&!FrequentMethodUtils.checkReg(importStaffListRequestDto.getDriversLicenseNo(),"^[a-zA-Z0-9]{1,50}$")){
                    failureCount++;
                    continue;
                }
                if(StringUtils.isNotBlank(importStaffListRequestDto.getPermittedType())&&!FrequentMethodUtils.validatePermittedType(importStaffListRequestDto.getPermittedType())){
                    failureCount++;
                    continue;
                }
                if(StringUtils.isNotBlank(importStaffListRequestDto.getLaborContractValidDate())){
                        Date date =  MapperUtils.mapperNoDefault(importStaffListRequestDto.getLaborContractValidDate(),Date.class);
                        if(date==null){
                            failureCount++;
                            continue;
                        }
                        importStaffListRequestModel.setLaborContractValidDate(date);
                }
                if(StringUtils.isNotBlank(importStaffListRequestDto.getDriversLicenseDateFrom())){
                        Date date =  MapperUtils.mapperNoDefault(importStaffListRequestDto.getDriversLicenseDateFrom(),Date.class);
                        if(date==null){
                            failureCount++;
                            continue;
                        }
                        importStaffListRequestModel.setDriversLicenseDateFrom(date);
                }
                if(StringUtils.isNotBlank(importStaffListRequestDto.getDriversLicenseDateTo())){
                        Date date = MapperUtils.mapperNoDefault(importStaffListRequestDto.getDriversLicenseDateTo(),Date.class);
                        if(date==null){
                            failureCount++;
                            continue;
                        }
                        importStaffListRequestModel.setDriversLicenseDateTo(date);
                }
                if(StringUtils.isNotBlank(importStaffListRequestDto.getOccupationalIssueDate())){
                        Date date = MapperUtils.mapperNoDefault(importStaffListRequestDto.getOccupationalIssueDate(),Date.class);
                        if(date==null){
                            failureCount++;
                            continue;
                        }
                        importStaffListRequestModel.setOccupationalIssueDate(date);
                }
                if(StringUtils.isNotBlank(importStaffListRequestDto.getOccupationalValidDate())){

                        Date date = MapperUtils.mapperNoDefault(importStaffListRequestDto.getOccupationalValidDate(),Date.class);
                        if(date==null){
                            failureCount++;
                            continue;
                        }
                        importStaffListRequestModel.setOccupationalValidDate(date);
                }
                if(StringUtils.isNotBlank(importStaffListRequestDto.getExaminationValidDate())){
                        Date date = MapperUtils.mapperNoDefault(importStaffListRequestDto.getExaminationValidDate(),Date.class);
                        if(date==null){
                            failureCount++;
                            continue;
                        }
                        importStaffListRequestModel.setExaminationValidDate(date);
                }
                if(StringUtils.isNotBlank(importStaffListRequestDto.getLearningValidDate())){
                        Date date = MapperUtils.mapperNoDefault(importStaffListRequestDto.getLearningValidDate(),Date.class);
                        if(date==null){
                            failureCount++;
                            continue;
                        }
                        importStaffListRequestModel.setLearningValidDate(date);
                }
                importStaffListRequestModel.setName(importStaffListRequestDto.getName());
                importStaffListRequestModel.setMobile(importStaffListRequestDto.getMobile());
                importStaffListRequestModel.setIdentityNumber(importStaffListRequestDto.getIdentityNumber());
                importStaffListRequestModel.setAge(ConverterUtils.toInteger(importStaffListRequestDto.getAge()));
                importStaffListRequestModel.setLaborContractNo(importStaffListRequestDto.getLaborContractNo());
                importStaffListRequestModel.setOccupationalRequirementsCredentialNo(importStaffListRequestDto.getOccupationalRequirementsCredentialNo());
                importStaffListRequestModel.setDriversLicenseNo(importStaffListRequestDto.getDriversLicenseNo());
                importStaffListRequestModel.setPermittedType(importStaffListRequestDto.getPermittedType());

                commonBiz.convertObjectFieldToNullIfIsEmpty(importStaffListRequestModel);
                staffListRequestModels.add(importStaffListRequestModel);
            }
            dest.setFailureCount(failureCount);
        }
        return dest;
    }

    /**
     * 启动/禁用
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> enableOrDisable(@RequestBody @Valid EnableStaffRequestDto requestDto) {
        Result<Boolean> result = staffManagementServiceApi.enableOrDisable(MapperUtils.mapper(requestDto,EnableStaffRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 模糊查询司机信息
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<FuzzyQueryDriverInfoResponseDto>> fuzzyQueryDriverInfo(@RequestBody FuzzyQueryDriverInfoRequestDto requestDto) {
        requestDto.setPageNum(CommonConstant.INTEGER_ZERO);
        requestDto.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<FuzzyQueryDriverInfoFeignResponseModel>> result = staffManagementClient.fuzzyQueryDriverInfo(MapperUtils.mapper(requestDto, FuzzyQueryDriverInfoFeignRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<FuzzyQueryDriverInfoResponseDto> dtoList = MapperUtils.mapper(pageInfo.getList(),FuzzyQueryDriverInfoResponseDto.class);
        return Result.success(dtoList);
    }

    /**
     * 分页模糊查询司机信息
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<FuzzyQueryDriverInfoResponseDto>> fuzzyQueryDriverInfoPage(@RequestBody FuzzyQueryDriverInfoRequestDto requestDto) {
        Result<PageInfo<FuzzyQueryDriverInfoFeignResponseModel>> result = staffManagementClient.fuzzyQueryDriverInfo(MapperUtils.mapper(requestDto, FuzzyQueryDriverInfoFeignRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<FuzzyQueryDriverInfoResponseDto> dtoList = MapperUtils.mapper(pageInfo.getList(),FuzzyQueryDriverInfoResponseDto.class);
        pageInfo.setList(dtoList);
        return Result.success(pageInfo);
    }


    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delStaff(@RequestBody @Valid DelStaffRequestDto requestDto) {
        Result<Boolean> result = staffManagementServiceApi.delStaff(MapperUtils.mapper(requestDto, DelStaffRequestModel.class));
        result.throwException();
        return result;
    }

    /**
     * 仓库权限开关
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> warehouseSwitch(WarehouseSwitchStaffRequestDto requestDto) {
        Result<Boolean> result = staffManagementServiceApi.warehouseSwitch(MapperUtils.mapper(requestDto, WarehouseSwitchStaffRequestModel.class));
        result.throwException();
        return result;
    }
}
