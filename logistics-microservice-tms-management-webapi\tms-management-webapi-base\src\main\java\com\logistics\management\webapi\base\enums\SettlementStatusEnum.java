package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/10/8 14:13
 */
public enum SettlementStatusEnum {
    NULL(-1,""),
    WAIT_SETTLEMENT(0,"待结算"),
    PART_SETTLEMENT(1,"部分结算"),
    COMPLETE_SETTLEMENT(2,"结算完成"),
    ;
    private Integer key;
    private String value;

    SettlementStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static SettlementStatusEnum getEnum(Integer key) {
        for (SettlementStatusEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return NULL;
    }
}
