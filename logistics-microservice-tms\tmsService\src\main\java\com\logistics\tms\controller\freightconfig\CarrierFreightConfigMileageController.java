package com.logistics.tms.controller.freightconfig;

import com.logistics.tms.biz.carrierfreight.service.CarrierFreightConfigMileageService;
import com.logistics.tms.controller.freightconfig.request.mileage.CarrierFreightConfigMileageAddRequestModel;
import com.logistics.tms.controller.freightconfig.request.mileage.CarrierFreightConfigMileageEditRequestModel;
import com.logistics.tms.controller.freightconfig.request.mileage.CarrierFreightConfigMileageRequestModel;
import com.logistics.tms.controller.freightconfig.response.mileage.CarrierFreightConfigMileageResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(tags = "里程数计价配置管理")
@RequestMapping(value = "/service/freight/config/mileage")
public class CarrierFreightConfigMileageController {
    
    @Resource
    private CarrierFreightConfigMileageService carrierFreightConfigMileageService;

    @PostMapping(value = "/detail")
    @ApiOperation(value = "里程数计价配置查看", tags = "1.3.5")
    Result<CarrierFreightConfigMileageResponseModel> detail(@RequestBody CarrierFreightConfigMileageRequestModel requestModel) {
        return Result.success(carrierFreightConfigMileageService.detail(requestModel));
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "里程数计价配置新增", tags = "1.3.5")
    Result<Boolean> add(@RequestBody CarrierFreightConfigMileageAddRequestModel requestModel) {
        return Result.success(carrierFreightConfigMileageService.add(requestModel));
    }

    @PostMapping(value = "/edit")
    @ApiOperation(value = "里程数计价配置编辑", tags = "1.3.5")
    Result<Boolean> edit(@RequestBody CarrierFreightConfigMileageEditRequestModel requestModel) {
        return Result.success(carrierFreightConfigMileageService.edit(requestModel));
    }
}
