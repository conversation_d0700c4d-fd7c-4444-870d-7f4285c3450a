package com.logistics.management.webapi.api.feign.leave.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;

/**
 * 审核请假申请请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
public class AuditLeaveApplyRequestDto {

	//请假申请ID
	@ApiModelProperty(value = "请假申请ID", required = true)
	@NotBlank(message = "请选择要撤销的请假记录")
	private String leaveApplyId;

	//审核类型: 1:通过 ,2:驳
	@ApiModelProperty(value = "审核类型: 1:通过 ,2:驳回", required = true)
	@NotBlank(message = "请选择审核类型")
	@Range(min = 1, max = 2, message = "审核类型只能只1或2")
	private String auditType;

	//审核备注,驳回时必填
	@ApiModelProperty(value = "审核备注,驳回时必填,1-100个字符")
	private String remark;
}
