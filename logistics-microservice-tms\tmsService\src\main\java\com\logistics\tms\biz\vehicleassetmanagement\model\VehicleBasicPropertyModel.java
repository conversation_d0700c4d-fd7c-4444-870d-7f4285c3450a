package com.logistics.tms.biz.vehicleassetmanagement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/9 18:43
 */
@Data
public class VehicleBasicPropertyModel {

    private Long vehicleId;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("车辆机构 1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    @ApiModelProperty("车辆运营状态：1 运营中，2 已停运")
    private Integer operatingState;

    @ApiModelProperty("车辆类别 1 牵引车 2 挂车 3 一体车")
    private Integer vehicleCategory;
}
