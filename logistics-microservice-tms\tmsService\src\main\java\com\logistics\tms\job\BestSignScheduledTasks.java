package com.logistics.tms.job;

import com.logistics.tms.biz.basicinfo.common.BasicInfoCommonBiz;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/3/18 15:15
 */
@Slf4j
@Component
public class BestSignScheduledTasks {

    @Autowired
    private BasicInfoCommonBiz basicInfoCommonBiz;

    /**
     * 每1分钟去上上签检查CA认证进度
     */
    @XxlJob("logisticsTmsCheckAuthenticationProgressFromBestSign")
    public void logisticsTmsCheckAuthenticationProgressFromBestSign() {
        try {
            log.info("========checkAuthenticationProgress start========" + new Date() + "=======start checkAuthenticationProgress=======");
            basicInfoCommonBiz.checkAuthenticationProgressFromBestSign();
            log.info("========checkAuthenticationProgress end========" + new Date() + "=======end checkAuthenticationProgress=======");
        }catch (Exception e){
            log.error("定时任务，每1分钟去上上签检查CA认证进度错误: ", e);
        }
    }

    /**
     * 每1分钟去上上签拉取CA证书信息
     */
    @XxlJob("logisticsTmsFetchCACertificateFromBestSign")
    public void logisticsTmsFetchCACertificateFromBestSign() {
        try {
            log.info("========fetchCACertificate start========" + new Date() + "=======start fetchCACertificate=======");
            basicInfoCommonBiz.fetchCACertificateFromBestSign();
            log.info("========fetchCACertificate end=========" + new Date() + "=======end fetchCACertificate=======");
        }catch (Exception e){
            log.error("定时任务，每1分钟去上上签拉取CA证书信息错误: ", e);
        }
    }
}
