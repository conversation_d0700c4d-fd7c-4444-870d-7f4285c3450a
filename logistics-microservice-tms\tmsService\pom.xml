<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.logistics</groupId>
		<artifactId>microService-tms</artifactId>
		<version>3.31.0-RELEASE</version>
	</parent>

	<artifactId>tmsService</artifactId>
	<packaging>jar</packaging>
	<version>${projects.version}</version>

	<profiles>
		<profile>
			<id>develop</id>
			<properties>
				<env>dev</env>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>test</id>
			<properties>
				<env>test</env>
			</properties>
		</profile>
		<profile>
			<id>uat</id>
			<properties>
				<env>uat</env>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<env>prod</env>
			</properties>
		</profile>
	</profiles>

	<dependencies>
		<!--业务系统依赖 start-->
		<dependency>
			<groupId>com.logistics</groupId>
			<artifactId>tmsBase</artifactId>
			<version>${projects.version}</version>
		</dependency>
		<dependency>
			<groupId>com.logistics</groupId>
			<artifactId>tmsServiceApi</artifactId>
			<version>${projects.version}</version>
		</dependency>
		<!--网络货运 entrust api-->
		<dependency>
			<groupId>com.logistics</groupId>
			<artifactId>entrustServiceApi</artifactId>
		</dependency>
		<!--网络货运 carrier api-->
		<dependency>
			<groupId>com.logistics</groupId>
			<artifactId>carrierServiceApi</artifactId>
		</dependency>
		<dependency>
			<groupId>com.leyi.tray</groupId>
			<artifactId>microService-auth-service-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>swagger-annotations</artifactId>
					<groupId>io.swagger</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.yelo</groupId>
			<artifactId>basicDataServiceApi</artifactId>
		</dependency>
		<dependency>
			<groupId>com.yelolife</groupId>
			<artifactId>basicDataServiceApi</artifactId>
			<exclusions>
				<exclusion>
					<groupId>io.springfox</groupId>
					<artifactId>springfox-swagger2</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.yelolife</groupId>
			<artifactId>orderServiceApi</artifactId>
			<exclusions>
				<exclusion>
					<groupId>io.springfox</groupId>
					<artifactId>springfox-swagger2</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--中石化签名sdk-->
		<dependency>
			<groupId>com.pcitc.paas.signature</groupId>
			<artifactId>signature_client</artifactId>
			<version>1.0.0</version>
		</dependency>
		<!--业务系统依赖 end-->

		<!--封装的mq start-->
		<dependency>
			<groupId>com.yelo</groupId>
			<artifactId>tools-rabbitmq</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>mybatis-spring-boot-starter</artifactId>
					<groupId>org.mybatis.spring.boot</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--封装的mq end-->

		<!--数据库相关组件 start-->
		<dependency>
			<groupId>com.yelo</groupId>
			<artifactId>tools-mybatis</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>mybatis-spring-boot-starter</artifactId>
					<groupId>org.mybatis.spring.boot</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--数据库相关组件 end-->

		<!--xxl-job end-->
		<dependency>
			<groupId>com.yelo</groupId>
			<artifactId>tools-job</artifactId>
		</dependency>
		<!--xxl-job end-->

		<!--核心包配置包 start-->
		<dependency>
			<groupId>com.yelo</groupId>
			<artifactId>yelo-core-config</artifactId>
		</dependency>
		<!--核心包配置包 end-->

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>

		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
		</dependency>

		<!--spring cloud组件依赖 start-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-mail</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>android-json</artifactId>
					<groupId>com.vaadin.external.google</groupId>
				</exclusion>
				<exclusion>
					<artifactId>junit-jupiter</artifactId>
					<groupId>org.junit.jupiter</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-bootstrap</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-loadbalancer</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-stream-binder-rabbit</artifactId>
		</dependency>

		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.nacos</groupId>
			<artifactId>nacos-client</artifactId>
		</dependency>
		<!--spring cloud组件依赖 end-->

		<!--spock 单元测试依赖  start-->
		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-commons</artifactId>
			<version>1.5.0</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-engine</artifactId>
			<version>1.5.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.spockframework</groupId>
			<artifactId>spock-core</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.spockframework</groupId>
			<artifactId>spock-spring</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy</artifactId>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.spockframework</groupId>
			<artifactId>spock-junit4</artifactId>
			<scope>test</scope>
		</dependency>
		<!--spock 单元测试依赖  end-->

		<!--skywalking  start-->
		<dependency>
			<groupId>org.apache.skywalking</groupId>
			<artifactId>apm-toolkit-trace</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.skywalking</groupId>
			<artifactId>apm-toolkit-logback-1.x</artifactId>
		</dependency>
		<!--skywalking  end-->

		<!--中石化国密 start-->
		<dependency>
			<groupId>cn.org.secmid</groupId>
			<artifactId>phoenix-provider</artifactId>
			<version>1.0.1</version>
		</dependency>
		<dependency>
			<groupId>cn.org.secmid</groupId>
			<artifactId>phoenix-secx</artifactId>
			<version>1.0.1</version>
		</dependency>
		<dependency>
			<groupId>cn.org.secmid</groupId>
			<artifactId>phoenix-secx-extension</artifactId>
			<version>1.0.1</version>
		</dependency>
		<dependency>
			<groupId>cn.org.secmid</groupId>
			<artifactId>ucsp-secx</artifactId>
			<version>2.0.1</version>
		</dependency>
		<dependency>
			<groupId>cn.org.secmid</groupId>
			<artifactId>phoenix-assemb</artifactId>
			<version>1.0.1</version>
		</dependency>
		<!--中石化国密 end-->

		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
		</dependency>
	</dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**</include>
				</includes>
				<excludes>
					<exclude>generatorConfig.xml</exclude>
				</excludes>
				<filtering>true</filtering>
			</resource>
		</resources>

		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<configuration>
					<encoding>UTF-8</encoding>
					<!-- 过滤后缀为pem、pfx的证书文件 -->
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>pem</nonFilteredFileExtension>
						<nonFilteredFileExtension>pfx</nonFilteredFileExtension>
						<nonFilteredFileExtension>p12</nonFilteredFileExtension>
						<nonFilteredFileExtension>key</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>

			<plugin>
				<groupId>org.mybatis.generator</groupId>
				<artifactId>mybatis-generator-maven-plugin</artifactId>
				<version>1.3.2</version>
				<configuration>
					<verbose>true</verbose>
					<overwrite>true</overwrite>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>com.yelo</groupId>
						<artifactId>tools-core</artifactId>
						<version>2.0.4-RELEASE</version>
					</dependency>
				</dependencies>
			</plugin>

			<plugin>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<testFailureIgnore>true</testFailureIgnore>
					<includes>
						<include>**/*Test.java</include>
					</includes>
					<argLine>-Dfile.encoding=UTF-8</argLine>
				</configuration>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy</id>
						<phase>package</phase>
						<configuration>
							<target>
								<copy todir="..\target"
									  file="${project.build.directory}/${artifactId}.jar"/>
							</target>
						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
