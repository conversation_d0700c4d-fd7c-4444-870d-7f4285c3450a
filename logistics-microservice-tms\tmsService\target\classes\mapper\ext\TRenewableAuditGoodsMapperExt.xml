<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TRenewableAuditGoodsMapper">

  <delete id="deleteByRenewableOrderId">
    update t_renewable_audit_goods
    set valid = 0,last_modified_by=#{operatorName,jdbcType=VARCHAR},last_modified_time=now()
    where valid = 1 and renewable_order_id = #{renewableOrderId} and goods_source_type = #{goodsSourceType}
  </delete>

  <select id="queryGoodsListByRenewableOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_renewable_audit_goods
    where valid = 1 and renewable_order_id = #{renewableOrderId}
    order by created_time desc, id desc
  </select>

  <select id="queryGoodsListByOrderIdAndSourceType"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_renewable_audit_goods
    where valid = 1 and renewable_order_id = #{renewableOrderId} and goods_source_type = #{goodsSourceType}
    order by created_time desc, id desc
  </select>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TRenewableAuditGoods">
    <foreach collection="recordList" item="item" separator=";">
      insert into t_renewable_audit_goods
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          id,
        </if>
        <if test="item.renewableOrderId != null">
          renewable_order_id,
        </if>
        <if test="item.goodsSourceType != null">
          goods_source_type,
        </if>
        <if test="item.renewableSkuCode != null">
          renewable_sku_code,
        </if>
        <if test="item.goodsName != null">
          goods_name,
        </if>
        <if test="item.goodsAmount != null">
          goods_amount,
        </if>
        <if test="item.goodsUnit != null">
          goods_unit,
        </if>
        <if test="item.goodsPrice != null">
          goods_price,
        </if>
        <if test="item.createdBy != null">
          created_by,
        </if>
        <if test="item.createdTime != null">
          created_time,
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time,
        </if>
        <if test="item.valid != null">
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.renewableOrderId != null">
          #{item.renewableOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.goodsSourceType != null">
          #{item.goodsSourceType,jdbcType=INTEGER},
        </if>
        <if test="item.renewableSkuCode != null">
          #{item.renewableSkuCode,jdbcType=VARCHAR},
        </if>
        <if test="item.goodsName != null">
          #{item.goodsName,jdbcType=VARCHAR},
        </if>
        <if test="item.goodsAmount != null">
          #{item.goodsAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.goodsUnit != null">
          #{item.goodsUnit,jdbcType=INTEGER},
        </if>
        <if test="item.goodsPrice != null">
          #{item.goodsPrice,jdbcType=DECIMAL},
        </if>
        <if test="item.createdBy != null">
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>
</mapper>