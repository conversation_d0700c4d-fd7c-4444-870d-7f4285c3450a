package com.logistics.management.webapi.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/9
 */
@Data
public class StatementArchiveTicketListRequestDto {

	@ApiModelProperty(value = "对账单详情item id", required = true)
	@NotBlank(message = "请选择要查看的记录")
	private String settleStatementItemId;
}
