package com.logistics.tms.api.feign.driverpayee.model;

import com.logistics.tms.api.feign.common.model.CertificatePictureModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DriverPayeeListResponseModel {


    @ApiModelProperty("司机账号ID")
    private Long driverPayeeId;
    @ApiModelProperty("审核状态 0 待审核 1 已审核 2 已驳回")
    private Integer auditStatus;
    @ApiModelProperty("收款人姓名")
    private String name ;
    @ApiModelProperty("收款人联系方式")
    private String mobile ;
    @ApiModelProperty("收款人身份证号码")
    private String identityNo ;
    @ApiModelProperty("银行卡号")
    private String bankCardNo = "";
    @ApiModelProperty("银行名称")
    private String bankName = "";
    @ApiModelProperty("收款证件列表")
    private List<CertificatePictureModel> imageList;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime ;

}
