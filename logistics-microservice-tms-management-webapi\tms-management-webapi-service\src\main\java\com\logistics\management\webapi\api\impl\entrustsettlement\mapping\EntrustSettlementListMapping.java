package com.logistics.management.webapi.api.impl.entrustsettlement.mapping;

import com.logistics.management.webapi.api.feign.entrustsettlement.dto.EntrustSettlementRowDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.tms.api.feign.entrustsettlement.model.EntrustSettlementGoodsResponseModel;
import com.logistics.tms.api.feign.entrustsettlement.model.EntrustSettlementRowModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/11 19:52
 */
public class EntrustSettlementListMapping extends MapperMapping<EntrustSettlementRowModel,EntrustSettlementRowDto> {
    @Override
    public void configure() {
        EntrustSettlementRowModel source = getSource();
        EntrustSettlementRowDto destination = getDestination();
        if (source != null){
            //货主
            String companyEntrustName = source.getCompanyEntrust();
            if (EntrustTypeEnum.BOOKING.getKey().equals(source.getEntrustType())){
                companyEntrustName = CommonConstant.LEYI_POINTS_FOR_LOGISTICS;
            }
            destination.setCompanyEntrust(companyEntrustName);

            List<EntrustSettlementGoodsResponseModel> goodsList = source.getGoodsList();
            if(ListUtils.isNotEmpty(goodsList)){
                StringBuilder goodsName = new StringBuilder();
                StringBuilder goodsSize = new StringBuilder();
                for(int index = 0;index<goodsList.size() ;index++){
                    EntrustSettlementGoodsResponseModel tmpGoodsModel = goodsList.get(index);
                    if (source.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
                        tmpGoodsModel.setGoodsSize(tmpGoodsModel.getGoodsSize() + " " + tmpGoodsModel.getLength() + "*" + tmpGoodsModel.getWidth() + "*" + tmpGoodsModel.getHeight() + "mm");
                    }
                    if(index !=0){
                        goodsName.append("/");
                    }
                    if(StringUtils.isNotBlank(goodsSize.toString())&&StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())){
                        goodsSize.append("/");
                    }
                    goodsName.append(tmpGoodsModel.getGoodsName());
                    if(StringUtils.isNotBlank(tmpGoodsModel.getGoodsSize())){
                        goodsSize.append(tmpGoodsModel.getGoodsSize());
                    }
                }
                destination.setGoodsName(goodsName.toString());
                destination.setGoodsSize(goodsSize.toString());
            }

            StringBuilder load=new StringBuilder();
            if(StringUtils.isNotBlank(source.getLoadCityName())){
                load.append("【"+source.getLoadCityName()+"】");
            }
            load.append(source.getLoadAreaName()==null?"":source.getLoadAreaName()).
                    append(source.getLoadDetailAddress()==null?"":source.getLoadDetailAddress()).
                    append(source.getLoadWarehouse()==null?"":source.getLoadWarehouse());
            destination.setLoadDetailAddress(load.toString());
            StringBuilder unLoad=new StringBuilder();
            if(StringUtils.isNotBlank(source.getUnloadCityName())){
                unLoad.append("【"+source.getUnloadCityName()+"】");
            }
            unLoad.append(source.getUnloadAreaName()==null?"":source.getUnloadAreaName()).
                    append(source.getUnloadDetailAddress()==null?"":source.getUnloadDetailAddress()).
                    append(source.getUnloadWarehouse()==null?"":source.getUnloadWarehouse());
            destination.setUnloadDetailAddress(unLoad.toString());
            String unit = GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit();
            destination.setSettlementAmount(source.getSettlementAmount().stripTrailingZeros().toPlainString()+unit);
            if (StringUtils.isBlank(PriceTypeEnum.getEnum(source.getContractPriceType()).getValue())) {
                String settlementCostLabel = ConverterUtils.toString(source.getSettlementCostTotal().setScale(2, BigDecimal.ROUND_HALF_UP));
                destination.setSettlementCostTotal(CommonConstant.ZERO_NET_ZERO.equals(settlementCostLabel)?"":settlementCostLabel);
            }

            destination.setSettlementStatusDesc(SettlementReceivementStatusEnum.getEnum(source.getSettlementStatus()).getValue());
            //需求单状态转换
            if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())){
                destination.setEntrustStatus(DemandOrderStatusEnum.ORDER_EMPTY.getValue());
            }else {
                destination.setEntrustStatus(DemandOrderStatusEnum.getEnum(source.getEntrustStatus()).getValue());
            }
            destination.setContractPriceType(PriceTypeEnum.getEnum(source.getContractPriceType()).getValue());
        }
    }
}
