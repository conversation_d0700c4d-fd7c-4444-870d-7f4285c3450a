package com.logistics.management.webapi.controller.warehouseaddress.mapping;

import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.management.webapi.client.warehouseaddress.response.WarehouseAddressListResponseModel;
import com.logistics.management.webapi.controller.warehouseaddress.response.WarehouseAddressListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Optional;

/**
 * @author: jzh
 * @date: 2021/1/22 16:46
 */
public class WarehouseAddressListExportMapping extends MapperMapping<WarehouseAddressListResponseModel, WarehouseAddressListResponseDto> {
    @Override
    public void configure() {
        WarehouseAddressListResponseModel source = getSource();
        WarehouseAddressListResponseDto destination = getDestination();
        if (source!= null){
            if (source.getEnabled() != null){
                destination.setEnabled(EnabledEnum.getEnum(source.getEnabled()).getValue());
            }

            destination.setAddress(Optional.ofNullable(source.getProvinceName()).orElse("")+
                        Optional.ofNullable(source.getCityName()).orElse("")+
                        Optional.ofNullable(source.getAreaName()).orElse(""));


        }
    }
}