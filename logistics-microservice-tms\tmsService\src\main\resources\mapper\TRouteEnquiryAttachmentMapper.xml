<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRouteEnquiryAttachmentMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TRouteEnquiryAttachment" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="route_enquiry_id" property="routeEnquiryId" jdbcType="BIGINT" />
    <result column="attachment_type" property="attachmentType" jdbcType="INTEGER" />
    <result column="attachment_path" property="attachmentPath" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, route_enquiry_id, attachment_type, attachment_path, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_route_enquiry_attachment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_route_enquiry_attachment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TRouteEnquiryAttachment" >
    insert into t_route_enquiry_attachment (id, route_enquiry_id, attachment_type, 
      attachment_path, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{routeEnquiryId,jdbcType=BIGINT}, #{attachmentType,jdbcType=INTEGER}, 
      #{attachmentPath,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TRouteEnquiryAttachment" >
    insert into t_route_enquiry_attachment
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="routeEnquiryId != null" >
        route_enquiry_id,
      </if>
      <if test="attachmentType != null" >
        attachment_type,
      </if>
      <if test="attachmentPath != null" >
        attachment_path,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="routeEnquiryId != null" >
        #{routeEnquiryId,jdbcType=BIGINT},
      </if>
      <if test="attachmentType != null" >
        #{attachmentType,jdbcType=INTEGER},
      </if>
      <if test="attachmentPath != null" >
        #{attachmentPath,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TRouteEnquiryAttachment" >
    update t_route_enquiry_attachment
    <set >
      <if test="routeEnquiryId != null" >
        route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT},
      </if>
      <if test="attachmentType != null" >
        attachment_type = #{attachmentType,jdbcType=INTEGER},
      </if>
      <if test="attachmentPath != null" >
        attachment_path = #{attachmentPath,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TRouteEnquiryAttachment" >
    update t_route_enquiry_attachment
    set route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT},
      attachment_type = #{attachmentType,jdbcType=INTEGER},
      attachment_path = #{attachmentPath,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>