package com.logistics.management.webapi.controller.freightconfig.response.mileage;

import com.logistics.management.webapi.controller.freightconfig.response.ladder.CarrierFreightConfigPriceDesignResponseDto;
import com.logistics.management.webapi.controller.freightconfig.response.scheme.CarrierFreightConfigSchemeResponseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigMileageResponseDto extends CarrierFreightConfigSchemeResponseDto {

    @ApiModelProperty(value = "价格设计")
    private CarrierFreightConfigPriceDesignResponseDto priceDesign;
}
