package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/25
 */
@Data
public class UpdateLifeCarrierOrderVehicleMessage {

	@ApiModelProperty(value = "运单号")
	private String carrierOrderCode;

	@ApiModelProperty("客户单号")
	private String customerOrderCode;

	@ApiModelProperty(value = "车牌号")
	private String vehicleNumber;

	@ApiModelProperty(value = "司机名称")
	private String driverName;

	@ApiModelProperty(value = "司机手机号")
	private String driverMobilePhone;

	@ApiModelProperty(value = "操作人")
	private String userName;
}
