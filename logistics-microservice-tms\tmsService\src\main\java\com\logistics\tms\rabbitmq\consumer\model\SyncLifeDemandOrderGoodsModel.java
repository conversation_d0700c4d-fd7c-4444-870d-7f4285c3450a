package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 货物
 * @author: wjf
 * @date: 2024/6/18 14:04
 */
@Data
public class SyncLifeDemandOrderGoodsModel {
    @ApiModelProperty("skuCode")
    private String skuCode;

    @ApiModelProperty("货物名")
    private String goodsName;

    @ApiModelProperty("货物数量")
    private BigDecimal goodsAmount;

}
