package com.logistics.tms.biz.carrierorder

import com.logistics.carrier.api.feign.carrierorder.management.CarrierOrderManagementForManageServiceApi
import com.logistics.tms.controller.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel
import com.logistics.tms.base.enums.CarrierOrderEventsTypeEnum
import com.logistics.tms.base.enums.CarrierOrderOperateLogsTypeEnum
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.biz.demandorder.DemandOrderBiz
import com.logistics.tms.biz.dispatch.DispatchBiz
import com.logistics.tms.biz.extvehiclesettlement.ExtVehicleSettlementBiz
import com.logistics.tms.biz.staffvehiclerelation.StaffVehicleBiz
import com.logistics.tms.client.BasicDataClient
import com.logistics.tms.controller.carrierorder.request.*
import com.logistics.tms.controller.carrierorder.response.*
import com.logistics.tms.controller.companycarrier.response.CompanyCarrierDetailResponseModel
import com.logistics.tms.controller.demandorder.response.GetDemandOrderInfoByIdsModel
import com.logistics.tms.controller.dispatch.response.DriverIdVehicleIdRelationIdModel
import com.logistics.tms.controller.staffvehiclerelation.response.StaffAndVehicleSearchResponseModel
import com.logistics.tms.entity.*
import com.logistics.tms.mapper.*
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz
import com.yelo.tray.core.exception.BizException
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
@SpringBootTest
class CarrierOrderBizTest extends Specification {
    @Autowired
    TCarrierOrderMapper tCarrierOrderMapper
    @Autowired
    TCarrierOrderVehicleHistoryMapper tCarrierOrderVehicleHistoryMapper
    @Autowired
    TCarrierOrderTicketsMapper tCarrierOrderTicketsMapper
    @Autowired
    TCarrierOrderGoodsMapper tCarrierOrderGoodsMapper
    @Autowired
    CommonBiz commonBiz
    @Autowired
    TCarrierOrderEventsMapper tCarrierOrderEventsMapper
    @Autowired
    TCarrierOrderOperateLogsMapper carrierOrderOperateLogsMapper
    @Autowired
    CarrierOrderCommonBiz carrierOrderCommonBiz
    @Autowired
    TDispatchOrderMapper dispatchOrderMapper
    @Autowired
    TCarrierOrderOrderRelMapper tCarrierOrderOrderRelMapper
    @Autowired
    TCarrierOrderWxMapper tCarrierOrderWxMapper
    @Autowired
    TVehicleGpsMapper tVehicleGpsMapper
    @Autowired
    TOperateLogsMapper operateLogsMapper
    @Autowired
    DemandOrderBiz demandOrderBiz
    @Autowired
    TDemandOrderMapper tDemandOrderMapper
    @Autowired
    TCarrierOrderVehicleHistoryMapper carrierOrderVehicleHistoryMapper
    @Autowired
    StaffVehicleBiz staffVehicleBiz
    @Autowired
    TVehicleBasicMapper tVehicleBasicMapper
    @Autowired
    DispatchBiz dispatchBiz
    @Autowired
    TStaffVehicleRelationMapper tStaffVehicleRelationMapper
    @Autowired
    TCompanyEntrustMapper tCompanyEntrustMapper
    @Autowired
    ExtVehicleSettlementBiz extVehicleSettlementBiz
    @Autowired
    TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper
    @Autowired
    TCompanyCarrierMapper tCompanyCarrierMapper
    @Autowired
    CarrierOrderManagementForManageServiceApi carrierOrderManagementForManageServiceApi
    @Autowired
    BasicDataClient basicDataClient
    @Autowired
    TPaymentMapper tPaymentMapper
    @Autowired
    TReceivementMapper tReceivementMapper
    @Autowired
    RabbitMqPublishBiz rabbitMqPublishBiz
    @Autowired
    TCarrierOrderCorrectMapper tCarrierOrderCorrectMapper
    @Mock
    Logger log
    @Autowired
    CarrierOrderBiz carrierOrderBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Carrier Order List Management where searchCarrierOrderListRequestModel=#searchCarrierOrderListRequestModel then expect: #expectedResult"() {
        given:

        expect:
        carrierOrderBiz.searchCarrierOrderListManagement(searchCarrierOrderListRequestModel).getList().size() == expectedResult

        where:
        searchCarrierOrderListRequestModel       || expectedResult
        new SearchCarrierOrderListRequestModel() || 20
    }

    @Unroll
    def "wait Audit Vehicle Count Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:


        expect:
        carrierOrderBiz.waitAuditVehicleCountInfo(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new WaitAuditVehicleInfoRequestModel() ||  new WaitAuditVehicleInfoResponseModel(waitAuditVehicleCount: 0)
    }

    @Unroll
    def "get Carrier Order Detail By Id where carrierOrderDetailRequestModel=#carrierOrderDetailRequestModel then expect: #expectedResult"() {
        given:

        when:
        carrierOrderBiz.getCarrierOrderDetailById(carrierOrderDetailRequestModel) == expectedResult
        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }
        where:
        carrierOrderDetailRequestModel       || expectedResult
        null                                 || "请求参数错误"
    }

    @Unroll
    def "get Tickets By Carrier Order Id where carrierOrderId=#carrierOrderId then expect: #expectedResult"() {
        given:

        expect:
        carrierOrderBiz.getTicketsByCarrierOrderId(carrierOrderId).size() == expectedResult

        where:
        carrierOrderId || expectedResult
        null            || 0
    }

    @Unroll
    def "upload Tickets where requestModel=#requestModel"() {
        given:

        when:
        carrierOrderBiz.uploadTickets(requestModel)
        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }
        where:
        requestModel                    || expectedResult
        null                            || "请求参数错误"
    }

    @Unroll
    def "cancel Carrier Order where requestModel=#requestModel"() {
        given:

        when:
        carrierOrderBiz.cancelCarrierOrder(requestModel)

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where:
        requestModel                         || expectedResult
        new CancelCarrierOrderRequestModel() || "请求参数错误"
        new CancelCarrierOrderRequestModel(carrierOrderId:"1111,1112") || "运单不存在"
    }

    @Unroll
    def "carrier Order List Before Sign Up where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        when:
        carrierOrderBiz.carrierOrderListBeforeSignUp(requestModel) == expectedResult

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where:
        requestModel                                   || expectedResult
        new CarrierOrderListBeforeSignUpRequestModel() || "请求参数错误"
    }

    @Unroll
    def "carrier Order Sign Up where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderMapper.batchUpdateCarrierOrders(any())).thenReturn(0)
        when(tCarrierOrderMapper.selectCarrierOrderSignDetail(anyString(), anyString())).thenReturn([new CarrierOrderListBeforeSignUpResponseModel()])
        when(tCarrierOrderMapper.getNotCancelByDemandOrderIds(anyString())).thenReturn([new DemandCarrierOrderRecursiveModel()])
        when(tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(any())).thenReturn(0)
        when(tCarrierOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(carrierOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderOrderRel(carrierOrderId: 1l, orderId: 1l, orderCode: "orderCode", expectAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal)])
        when(tCarrierOrderOrderRelMapper.batchUpdateSelective(any())).thenReturn(0)
        when(tDemandOrderMapper.getDemandOrderInfoByIds(anyString())).thenReturn([new GetDemandOrderInfoByIdsModel()])
        when(tCarrierOrderCorrectMapper.getByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderCorrect(carrierOrderId: 1l)])

        when:
        carrierOrderBiz.carrierOrderSignUp(requestModel)

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where:
        requestModel                                || expectedResult
        new CarrierOrderConfirmSignUpRequestModel() || "请求参数错误"
    }

    @Unroll
    def "sign Carrier Order Syn where carrierOrderEntrustTypeMap=#carrierOrderEntrustTypeMap and synCarrierOrderList=#synCarrierOrderList and synCarrierOrderIdRecycleList=#synCarrierOrderIdRecycleList and synCarrierOrderIdList=#synCarrierOrderIdList"() {
        given:
        when(tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderOrderRel(carrierOrderId: 1l, orderId: 1l, orderCode: "orderCode", expectAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal)])
        when(tCarrierOrderOrderRelMapper.batchUpdateSelective(any())).thenReturn(0)

        expect:
        carrierOrderBiz.signCarrierOrderSyn(synCarrierOrderList, synCarrierOrderIdList, synCarrierOrderIdRecycleList, carrierOrderEntrustTypeMap)
        assert expectedResult == true

        where:
        carrierOrderEntrustTypeMap | synCarrierOrderList                                                                                                                                               | synCarrierOrderIdRecycleList | synCarrierOrderIdList || expectedResult
        [(1l): 0]                  | [new TCarrierOrder(carrierOrderCode: "carrierOrderCode", signTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), signAmount: 0 as BigDecimal)] | [1l]                         | [1l]                  || true
    }

    @Unroll
    def "audit Or Reject Vehicle where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderVehicleHistoryMapper.getDriverVehicleInfoByCarrierOrderId(anyLong())).thenReturn(new DriverAndVehicleResponseModel())
        when(tCarrierOrderVehicleHistoryMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(tCarrierOrderVehicleHistoryMapper.getCarrierDriverByHistoryId(anyLong())).thenReturn(0)
        when(commonBiz.getQiyaCompanyName()).thenReturn("getQiyaCompanyNameResponse")
        when(commonBiz.getQiyaCompanyCarrierId()).thenReturn(1l)
        when(tCarrierOrderEventsMapper.selectEventByEventTypeAndCarrieriOrderId(anyLong(), anyInt())).thenReturn(new TCarrierOrderEvents(carrierOrderId: 1l, event: 0, eventDesc: "eventDesc", eventTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), operatorName: "operatorName", operateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime()))
        when(carrierOrderCommonBiz.existSettlementData(anyString())).thenReturn(true)
        when(tVehicleGpsMapper.selectVehicleByVehicleId(anyLong())).thenReturn(new TVehicleGps(dispatchOrderId: 1l, dispatchOrderCode: "dispatchOrderCode"))
        when(carrierOrderVehicleHistoryMapper.getDriverVehicleInfoByCarrierOrderId(anyLong())).thenReturn(new DriverAndVehicleResponseModel())
        when(carrierOrderVehicleHistoryMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(carrierOrderVehicleHistoryMapper.getCarrierDriverByHistoryId(anyLong())).thenReturn(0)
        when(tVehicleBasicMapper.queryVehicleInfoByVehicleNos(anyString())).thenReturn([new FuzzyQueryVehicleInfoResponseModel()])
        when(tCarrierVehicleRelationMapper.getByVehicleId(anyLong())).thenReturn(new TCarrierVehicleRelation(companyCarrierId: 1l))
        when(tCompanyCarrierMapper.getCompanyCarrierDetailById(anyLong())).thenReturn(new CompanyCarrierDetailResponseModel())

        when:
        carrierOrderBiz.auditOrRejectVehicle(requestModel)

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where:
        requestModel                           || expectedResult
        new AuditOrRejectVehicleRequestModel(type:2) || "驳回原因不能为空，且不超过300字"
    }

    @Unroll
    def "download Lading Bill where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.downloadLadingBill(anyLong())).thenReturn(new DownloadLadingBillResponseModel())

        expect:
        carrierOrderBiz.downloadLadingBill(requestModel) == expectedResult

        where:
        requestModel                         || expectedResult
        new CarrierOrderDetailRequestModel() || null
    }

    @Unroll
    def "get Carrier Order Logs where carrierOrderId=#carrierOrderId then expect: #expectedResult"() {
        given:
        when(carrierOrderOperateLogsMapper.getLogsByCarrierOrderId(anyLong())).thenReturn([new GetCarrierOrderLogsResponseModel()])

        expect:
        carrierOrderBiz.getCarrierOrderLogs(carrierOrderId).size() == expectedResult

        where:
        carrierOrderId || expectedResult
        1l             || 1
    }

    @Unroll
    def "del Tickets where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(any())).thenReturn(new TCarrierOrderVehicleHistory(auditStatus: 0))
        when(carrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(any())).thenReturn(new TCarrierOrderVehicleHistory(auditStatus: 0))

        when:
        carrierOrderBiz.delTickets(requestModel)
        then:"验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }
        where:
        requestModel                    || expectedResult
        new DeleteTicketsRequestModel() || "运单不存在"
    }

    @Unroll
    def "get Carrier Order Weixin Push Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderWxMapper.selectCarrierOrderWeixinPushInfoByCarrierOrder(anyLong())).thenReturn([new GetCarrierOrderWeixinPushResponseModel()])

        expect:
        carrierOrderBiz.getCarrierOrderWeixinPushInfo(requestModel).size() == expectedResult

        where:
        requestModel                                || expectedResult
        new GetCarrierOrderWeixinPushRequestModel() || 0
    }

    @Unroll
    def "confirm Push Weixin where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderWxMapper.selectCarrierOrderWeixinPushInfoByCarrierOrder(anyLong())).thenReturn([new GetCarrierOrderWeixinPushResponseModel()])
        when(tCarrierOrderWxMapper.batchUpdate(any())).thenReturn(0)
        when(tCarrierOrderWxMapper.batchInsert(any())).thenReturn(0)

        expect:
        carrierOrderBiz.confirmPushWeixin(requestModel)
        assert expectedResult == true

        where:
        requestModel                        || expectedResult
        new ConfirmPushWeixinRequestModel() || true
    }

    @Unroll
    def "up Status By Demand Ids where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderMapper.batchUpdateCarrierOrders(any())).thenReturn(0)
        when(tCarrierOrderMapper.getCarrierOrderByDemandOrderIds(anyString(), anyInt())).thenReturn([new GetCarrierOrderResponseModel(demandOrderId:1l,status:1)])
        when(tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(any())).thenReturn(0)
        when(tCarrierOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(carrierOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderOrderRel(orderId: 1l, orderCode: "orderCode", expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, signAmount: 0 as BigDecimal)])
        when(tCarrierOrderOrderRelMapper.batchUpdateSelective(any())).thenReturn(0)

        where:
        requestModel                                  || expectedResult
        [new TDemandOrder(source: 0, entrustType: 0)] || "运单不是待签收状态，不能进行该操作"
    }

    @Unroll
    def "get Carrier Order Event where eventTypeEnum=#eventTypeEnum and carrierOrderId=#carrierOrderId and remark=#remark and userName=#userName then expect: #expectedResult"() {
        expect:
        carrierOrderBiz.getCarrierOrderEvent(carrierOrderId, eventTypeEnum, userName, remark)
        assert expectedResult == true
        where:
        eventTypeEnum                       | carrierOrderId | remark   | userName   || expectedResult
        CarrierOrderEventsTypeEnum.BY_ORDER | 1l             | "remark" | "userName" || true
    }

    @Unroll
    def "get Carrier Order Operate Logs where logTypeEnum=#logTypeEnum and carrierOrderId=#carrierOrderId and remark=#remark and userName=#userName then expect: #expectedResult"() {
        expect:
        carrierOrderBiz.getCarrierOrderOperateLogs(carrierOrderId, logTypeEnum, userName, remark)
        assert expectedResult == true
        where:
        logTypeEnum                                         | carrierOrderId | remark   | userName   || expectedResult
        CarrierOrderOperateLogsTypeEnum.CREATE_DEMAND_ORDER | 1l             | "remark" | "userName" || true
    }

    @Unroll
    def "modify Vehicle where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderVehicleHistoryMapper.getDriverVehicleInfoByCarrierOrderId(anyLong())).thenReturn(new DriverAndVehicleResponseModel())
        when(tCarrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(any())).thenReturn(new TCarrierOrderVehicleHistory(carrierOrderId: 1l, vehicleId: 1l, vehicleNo: "vehicleNo", driverId: 1l, driverName: "driverName", driverMobile: "driverMobile", driverIdentity: "driverIdentity", expectLoadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), expectArrivalTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), remark: "remark", vehicleHistoryId: 1l, ifInvalid: 0, auditStatus: 0))
        when(commonBiz.getQiyaCompanyName()).thenReturn("getQiyaCompanyNameResponse")
        when(commonBiz.getQiyaCompanyCarrierId()).thenReturn(1l)
        when(carrierOrderCommonBiz.existSettlementData(anyString())).thenReturn(true)
        when(carrierOrderVehicleHistoryMapper.getDriverVehicleInfoByCarrierOrderId(anyLong())).thenReturn(new DriverAndVehicleResponseModel())
        when(carrierOrderVehicleHistoryMapper.getTopByCarrierOrderId(any())).thenReturn(new TCarrierOrderVehicleHistory(carrierOrderId: 1l, vehicleId: 1l, vehicleNo: "vehicleNo", driverId: 1l, driverName: "driverName", driverMobile: "driverMobile", driverIdentity: "driverIdentity", expectLoadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), expectArrivalTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), remark: "remark", vehicleHistoryId: 1l, ifInvalid: 0, auditStatus: 0))
        when(tVehicleBasicMapper.getInfoByVehicleNo(anyString())).thenReturn(new TVehicleBasic(vehicleProperty: 0, operatingState: 0))
        when(tVehicleBasicMapper.queryVehicleInfoByVehicleNos(anyString())).thenReturn([new FuzzyQueryVehicleInfoResponseModel()])
        when(dispatchBiz.verifyDriverAndVehicle(any())).thenReturn(new DriverIdVehicleIdRelationIdModel())
        when(tCarrierVehicleRelationMapper.getByVehicleId(anyLong())).thenReturn(new TCarrierVehicleRelation(companyCarrierId: 1l))
        when(tCompanyCarrierMapper.getCompanyCarrierDetailById(anyLong())).thenReturn(new CompanyCarrierDetailResponseModel())

        when:
        carrierOrderBiz.modifyVehicle(requestModel)
        then:"验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }
        where:
        requestModel                    || expectedResult
        new ModifyVehicleRequestModel() || "运单不存在"
    }

    @Unroll
    def "get Driver And Vehicle By Vehicle Number where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleBasicMapper.fuzzyQueryVehicleInfoByVehicleNo(anyString())).thenReturn([new FuzzyQueryVehicleInfoResponseModel()])
        when(tStaffVehicleRelationMapper.getDriverAndVehicleByVehicleNumber(any())).thenReturn([new StaffAndVehicleSearchResponseModel()])

        expect:
        carrierOrderBiz.getDriverAndVehicleByVehicleNumber(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new WebDemandOrderDriverRequestModel() || [new DriverAndVehicleResponseModel()]
    }

    @Unroll
    def "update Driver Freight Fee where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderMapper.getCarrierStatus(anyLong())).thenReturn(new CarrierOrderStatusModel())
        when(carrierOrderCommonBiz.existSettlementData(anyString())).thenReturn(true)

        expect:
        carrierOrderBiz.updateDriverFreightFee(requestModel)
        assert expectedResult == false

        where:
        requestModel                             || expectedResult
        new UpdateDriverFreightFeeRequestModel() || true
    }

    @Unroll
    def "get Driver Vehicle Info By Carrier Order Id where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderVehicleHistoryMapper.getDriverVehicleInfoByCarrierOrderId(anyLong())).thenReturn(new DriverAndVehicleResponseModel())
        when(carrierOrderVehicleHistoryMapper.getDriverVehicleInfoByCarrierOrderId(anyLong())).thenReturn(new DriverAndVehicleResponseModel())

        expect:
        carrierOrderBiz.getDriverVehicleInfoByCarrierOrderId(requestModel) == expectedResult

        where:
        requestModel                         || expectedResult
        new CarrierOrderDetailRequestModel() || new DriverAndVehicleResponseModel()
    }

    @Unroll
    def "load where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderMapper.selectCarrierOrdersByIds(anyString())).thenReturn([new TCarrierOrder(demandOrderId: 1l, carrierOrderCode: "carrierOrderCode", status: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), loadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), unloadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), expectAmount: 0 as BigDecimal, loadAmountExpect: 0 as BigDecimal, loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, goodsUnit: 0, remark: "remark")])
        when(tCarrierOrderMapper.batchUpdateCarrierOrders(any())).thenReturn(0)
        when(tCarrierOrderMapper.selectCarrierOrderSignDetail(anyString(), anyString())).thenReturn([new CarrierOrderListBeforeSignUpResponseModel()])
        when(tCarrierOrderTicketsMapper.batchInsertTickets(any())).thenReturn(0)
        when(tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(carrierOrderId: 1l, demandOrderGoodsId: 1l, goodsName: "goodsName", categoryName: "categoryName", length: 0, width: 0, height: 0, expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal)])
        when(tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(tCarrierOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(carrierOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderOrderRel(carrierOrderId: 1l, orderId: 1l, orderCode: "orderCode", expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal)])
        when(tCarrierOrderOrderRelMapper.batchUpdateSelective(any())).thenReturn(0)
        when(demandOrderBiz.syncCarrierLoadBackAmount(any())).thenReturn([null])
        when(tDemandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(source: 0, entrustType: 0)])

        expect:
        carrierOrderBiz.load(requestModel)
        assert expectedResult == false

        where:
        requestModel             || expectedResult
        [new LoadRequestModel()] || true
    }

    @Unroll
    def "unload where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderMapper.selectCarrierOrdersByIds(anyString())).thenReturn([new TCarrierOrder(demandOrderId: 1l, carrierOrderCode: "carrierOrderCode", status: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), ifCancel: 0, loadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), unloadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal, unloadAmountExpect: 0 as BigDecimal, unloadAmount: 0 as BigDecimal, goodsUnit: 0, ifEmpty: 0, remark: "remark")])
        when(tCarrierOrderMapper.batchUpdateCarrierOrders(any())).thenReturn(0)
        when(tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(carrierOrderId: 1l, demandOrderGoodsId: 1l, goodsName: "goodsName", categoryName: "categoryName", length: 0, width: 0, height: 0, expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal)])
        when(tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(any())).thenReturn(0)
        when(tCarrierOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(carrierOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderOrderRel(carrierOrderId: 1l, orderId: 1l, orderCode: "orderCode", expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal)])
        when(tCarrierOrderOrderRelMapper.batchUpdateSelective(any())).thenReturn(0)
        when(demandOrderBiz.syncCarrierLoadBackAmount(any())).thenReturn([null])
        when(tDemandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(source: 0, entrustType: 0)])

        expect:
        carrierOrderBiz.unload(requestModel)
        assert expectedResult == false

        where:
        requestModel               || expectedResult
        [new UnloadRequestModel()] || true
    }

    @Unroll
    def "load Unload Sync where loadUnloadGoodsAmountMap=#loadUnloadGoodsAmountMap and carrierOrderIds=#carrierOrderIds and orders=#orders and loadUnloadAmountMap=#loadUnloadAmountMap and demandOrderIdMap=#demandOrderIdMap and outStockCarrierOrderTicketsMap=#outStockCarrierOrderTicketsMap and type=#type"() {
        given:
        when(tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(carrierOrderId: 1l, demandOrderGoodsId: 1l, goodsName: "goodsName", categoryName: "categoryName", length: 0, width: 0, height: 0, expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal)])
        when(tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderOrderRel(carrierOrderId: 1l, orderId: 1l, orderCode: "orderCode", expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal)])
        when(tCarrierOrderOrderRelMapper.batchUpdateSelective(any())).thenReturn(0)
        when(demandOrderBiz.syncCarrierLoadBackAmount(any())).thenReturn([null])

        expect:
        carrierOrderBiz.loadUnloadSync(type, carrierOrderIds, orders, loadUnloadAmountMap, loadUnloadGoodsAmountMap, outStockCarrierOrderTicketsMap, demandOrderIdMap)
        assert expectedResult == false

        where:
        loadUnloadGoodsAmountMap | carrierOrderIds   | orders                                                                                                                                                                                                                                                                                                                          | loadUnloadAmountMap     | demandOrderIdMap                                    | outStockCarrierOrderTicketsMap | type || expectedResult
        [(1l): 0 as BigDecimal]  | "carrierOrderIds" | [new TCarrierOrder(demandOrderId: 1l, carrierOrderCode: "carrierOrderCode", loadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), unloadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal, unloadAmount: 0 as BigDecimal)] | [(1l): 0 as BigDecimal] | [(1l): new TDemandOrder(source: 0, entrustType: 0)] | ["String": ["String"]]         | 0    || true
    }

    @Unroll
    def "reach Load Address where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderMapper.selectCarrierOrdersByIds(anyString())).thenReturn([new TCarrierOrder(demandOrderId: 1l, carrierOrderCode: "carrierOrderCode", status: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), ifCancel: 0, ifEmpty: 0)])
        when(tCarrierOrderMapper.batchUpdateCarrierOrders(any())).thenReturn(0)
        when(tCarrierOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(carrierOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tDemandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(source: 0, entrustType: 0)])

        expect:
        carrierOrderBiz.reachLoadAddress(requestModel)
        assert expectedResult == false

        where:
        requestModel                       || expectedResult
        new ReachLoadAddressRequestModel() || true
    }

    @Unroll
    def "reach Unload Address where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderMapper.selectCarrierOrdersByIds(anyString())).thenReturn([new TCarrierOrder(demandOrderId: 1l, carrierOrderCode: "carrierOrderCode", status: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), ifCancel: 0, loadTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 2).getTime(), expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal, ifEmpty: 0)])
        when(tCarrierOrderMapper.batchUpdateCarrierOrders(any())).thenReturn(0)
        when(tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(carrierOrderId: 1l, expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal)])
        when(tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(any())).thenReturn(0)
        when(tCarrierOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(carrierOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderOrderRel(carrierOrderId: 1l, orderId: 1l, orderCode: "orderCode", expectAmount: 0 as BigDecimal, loadAmount: 0 as BigDecimal)])
        when(tCarrierOrderOrderRelMapper.batchUpdateSelective(any())).thenReturn(0)
        when(tDemandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(source: 0, entrustType: 0)])

        expect:
        carrierOrderBiz.reachUnloadAddress(requestModel)
        assert expectedResult == false

        where:
        requestModel                         || expectedResult
        new ReachUnloadAddressRequestModel() || true
    }

    @Unroll
    def "get Carrier Order By Demand Order Ids where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.getCarrierOrderByDemandOrderIds(anyString(), anyInt())).thenReturn([new GetCarrierOrderResponseModel()])

        expect:
        carrierOrderBiz.getCarrierOrderByDemandOrderIds(requestModel) == expectedResult

        where:
        requestModel                     || expectedResult
        new DemandOrderIdsRequestModel() || [new GetCarrierOrderResponseModel()]
    }

    @Unroll
    def "get By Demand Order Ids where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.getNotCancelByDemandOrderIds(anyString())).thenReturn([new DemandCarrierOrderRecursiveModel()])

        expect:
        carrierOrderBiz.getByDemandOrderIds(requestModel) == expectedResult

        where:
        requestModel                     || expectedResult
        new DemandOrderIdsRequestModel() || [new DemandCarrierOrderRecursiveModel()]
    }

    @Unroll
    def "get Load Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.getLoadDetailByIds(anyString())).thenReturn([new LoadDetailResponseModel()])
        when(commonBiz.getLeyiCompanyEntrustId()).thenReturn(1l)
        when(carrierOrderCommonBiz.checkCarrierOrderIfExist(any())).thenReturn([new TCarrierOrder(demandOrderId: 1l, status: 0, outStatus: 0)])
        when(tDemandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(entrustType: 0)])

        expect:
        carrierOrderBiz.getLoadDetail(requestModel) == expectedResult

        where:
        requestModel                 || expectedResult
        new LoadDetailRequestModel() || [new LoadDetailResponseModel()]
    }

    @Unroll
    def "syn Carrier Order Load Amount where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderMapper.getByCode(anyString())).thenReturn(new TCarrierOrder(status: 0, ifCancel: 0, loadAmount: 0 as BigDecimal, ifEmpty: 0, outStatus: 0))
        when(tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(anyString())).thenReturn([new TCarrierOrderGoods(goodsName: "goodsName", categoryName: "categoryName", length: 0, width: 0, height: 0, loadAmount: 0 as BigDecimal)])
        when(tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(any())).thenReturn(0)

        expect:
        carrierOrderBiz.synCarrierOrderLoadAmount(requestModel)
        assert expectedResult == false

        where:
        requestModel                                 || expectedResult
        new SyncCarrierOrderLoadAmountRequestModel() || true
    }

    @Unroll
    def "export Carrier Order Tickets where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierOrderMapper.exportCarrierOrderTickets(anyString())).thenReturn([new ExportCarrierOrderTicketsModel()])
        when(basicDataClient.compressAndDownloadFile(any())).thenReturn([(byte) 0] as byte[])

        expect:
        carrierOrderBiz.exportCarrierOrderTickets(requestModel) == expectedResult

        where:
        requestModel                                || expectedResult
        new ExportCarrierOrderTicketsRequestModel() || new ExportCarrierOrderTicketsResponseModel()
    }

    @Unroll
    def "check Export Carrier Order Tickets where requestModel=#requestModel"() {
        given:
        when(tCarrierOrderMapper.exportCarrierOrderTickets(anyString())).thenReturn([new ExportCarrierOrderTicketsModel()])

        expect:
        carrierOrderBiz.checkExportCarrierOrderTickets(requestModel)
        assert expectedResult == false

        where:
        requestModel                                || expectedResult
        new ExportCarrierOrderTicketsRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme