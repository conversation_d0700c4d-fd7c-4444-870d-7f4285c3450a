package com.logistics.appapi.base.utils;

import com.logistics.appapi.base.constant.CommonConstant;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/26 10:53
 */
public class TimeSplitterUtils {

    /**
     * 获取半点时间段
     * @param startStr 起始时间字符串（整点或半点）
     * @param endStr 结束时间字符串（整点或半点）
     * @return 返回相邻半点时间拼接成字符串时间段的集合
     */
    public static List<String> getTimePeriod(String startStr, String endStr){
        return getTimePeriod(startStr, endStr, null);
    }

    /**
     * 获取半点时间段
     * @param startStr 起始时间字符串（整点或半点）
     * @param endStr 结束时间字符串（整点或半点）
     * @param separator 分隔符（返回的时间段中间分隔符）
     * @return 返回相邻半点时间拼接成字符串时间段的集合
     */
    public static List<String> getTimePeriod(String startStr, String endStr, String separator){
        //获取半点时间段
        List<LocalTime> timeSegments = splitInHalfHours(startStr, endStr);

        if (StringUtils.isBlank(separator)){
            separator = "-";
        }

        List<String> list = new ArrayList<>();
        for (int i = 0; i < timeSegments.size()-1; i++) {
            list.add(ConverterUtils.toString(timeSegments.get(i))+separator+ConverterUtils.toString(timeSegments.get(i + 1)));
        }
        return list;
    }

    /**
     * 获取半点时间段-带限制开始时间
     * @param startStr 起始时间字符串（整点或半点）
     * @param endStr 结束时间字符串（整点或半点）
     * @param limitStr 限制开始时间（以此时间作为开始）
     * @param ifContainLimit 返的时间段里是否包含限制开始时间：0 否，1 是
     * @return 返回相邻半点时间拼接成字符串时间段的集合
     */
    public static List<String> getTimePeriod(String startStr, String endStr, String limitStr, Integer ifContainLimit){
        return getTimePeriod(startStr, endStr, null, limitStr, ifContainLimit);
    }

    /**
     * 获取半点时间段-带限制开始时间
     * @param startStr 起始时间字符串（整点或半点）
     * @param endStr 结束时间字符串（整点或半点）
     * @param separator 分隔符（返回的时间段中间分隔符）
     * @param limitStr 限制开始时间（可为空，有值的话则以此时间作为开始）
     * @param ifContainLimit 返的时间段里是否包含限制开始时间：0 否，1 是
     * @return 返回相邻半点时间拼接成字符串时间段的集合
     */
    public static List<String> getTimePeriod(String startStr, String endStr, String separator, String limitStr, Integer ifContainLimit){
        //获取半点时间段
        List<LocalTime> timeSegments = splitInHalfHours(startStr, endStr);

        if (StringUtils.isBlank(separator)){
            separator = "-";
        }

        //解析限制开始时间
        LocalTime limit = null;
        if (StringUtils.isNotBlank(limitStr)){
            String[] limits = limitStr.split(":");
            if (limits.length > CommonConstant.INTEGER_ONE){
                limit = LocalTime.of(ConverterUtils.toInt(limits[0]), ConverterUtils.toInt(limits[1]));
            }else {
                limit = LocalTime.of(ConverterUtils.toInt(limits[0]), 0);
            }
        }

        List<String> list = new ArrayList<>();
        for (int i = 0; i < timeSegments.size(); i++) {
            LocalTime startTime = timeSegments.get(i);
            if (limit == null || !limit.isAfter(startTime)) {
                if (ListUtils.isEmpty(list) &&  i != 0){
                    //把上一个时间段也加进去
                    list.add(ConverterUtils.toString(timeSegments.get(i - 1))+separator+ConverterUtils.toString(startTime));
                }
                if (i !=timeSegments.size()-1) {
                    list.add(ConverterUtils.toString(startTime) + separator + ConverterUtils.toString(timeSegments.get(i + 1)));
                }
            }else if (limit.compareTo(startTime) != CommonConstant.INTEGER_ZERO
                    && CommonConstant.INTEGER_ONE.equals(ifContainLimit)){
            }
        }

        return list;
    }

    /**
     * 获取半点时间
     * @param startDate 起始时间（整点或半点）
     * @param endStrDate 结束时间（整点或半点）
     * @return 返回半点时间集合
     */
    private static List<LocalTime> splitInHalfHours(Date startDate, Date endStrDate){
        String startStr = DateUtils.dateToString(startDate, "HH:mm");
        String endStr = DateUtils.dateToString(endStrDate, "HH:mm");
        return splitInHalfHours(startStr, endStr);
    }

    /**
     * 获取半点时间
     * @param startStr 起始时间字符串（整点或半点）
     * @param endStr 结束时间字符串（整点或半点）
     * @return 返回半点时间集合
     */
    private static List<LocalTime> splitInHalfHours(String startStr, String endStr){
        boolean ifReplace = false;
        if (endStr.equals("24:00") || endStr.equals("24")
                || endStr.equals("00:00") || endStr.equals("0") || endStr.equals("00")){//不存在24:00点
            endStr = "23:59";
            ifReplace = true;
        }
        String[] starts = startStr.split(":");
        String[] ends = endStr.split(":");
        LocalTime start;
        if (starts.length > CommonConstant.INTEGER_ONE){
            start = LocalTime.of(ConverterUtils.toInt(starts[0]), ConverterUtils.toInt(starts[1]));
        }else {
            start = LocalTime.of(ConverterUtils.toInt(starts[0]), 0);
        }
        LocalTime end;
        if (ends.length > CommonConstant.INTEGER_ONE){
            end = LocalTime.of(ConverterUtils.toInt(ends[0]), ConverterUtils.toInt(ends[1]));
        }else {
            end = LocalTime.of(ConverterUtils.toInt(ends[0]), 0);
        }
        List<LocalTime> timeSegments = splitInHalfHours(start, end);
        //如果替换过结束时间，则将结束时间置为0点0分
        if (ifReplace) {
            timeSegments.remove(timeSegments.size() - 1);
            timeSegments.add(LocalTime.of(0, 0));
        }
        return timeSegments;
    }

    /**
     * 获取半点时间
     * @param startTime 起始时间（整点或半点）
     * @param endTime 结束时间（整点或半点）
     * @return 返回半点时间集合
     */
    private static List<LocalTime> splitInHalfHours(LocalTime startTime, LocalTime endTime) {
        List<LocalTime> timeSegments = new ArrayList<>();

        LocalTime end = LocalTime.of(0, 0);
        while (startTime.isBefore(endTime)) {
            timeSegments.add(startTime);
            startTime = startTime.plusMinutes(30);

            if (startTime.compareTo(end) == CommonConstant.INTEGER_ZERO){
                break;
            }
        }

        if (!startTime.isAfter(endTime)) {
            timeSegments.add(endTime);
        }

        return timeSegments;
    }

}
