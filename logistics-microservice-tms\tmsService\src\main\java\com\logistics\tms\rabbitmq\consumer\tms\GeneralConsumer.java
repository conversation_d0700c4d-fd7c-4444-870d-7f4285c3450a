package com.logistics.tms.rabbitmq.consumer.tms;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.EntrustDataExceptionEnum;
import com.logistics.tms.base.enums.MQBaseMessageKeyEnum;
import com.logistics.tms.biz.demandorder.DemandOrderForLeYiBiz;
import com.logistics.tms.rabbitmq.consumer.model.MQBaseMessageModel;
import com.logistics.tms.rabbitmq.consumer.model.SyncTrayDemandOrderMessage;
import com.logistics.tms.rabbitmq.consumer.model.TmsSyncAdditionalOrderModel;
import com.rabbitmq.client.Channel;
import com.yelo.tools.rabbitmq.annocation.EnableMqErrorMessageCollect;
import com.yelo.tools.redis.utils.RedisLockUtils;
import com.yelo.tools.utils.JacksonUtils;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tools.utils.UUIDGenerateUtil;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 * 接收中间件消息
 *
 * <AUTHOR>
 * @date ：Created in 2023/2/21
 */
@Component
@Slf4j
public class GeneralConsumer {

	private ObjectMapper objectMapper = JacksonUtils.getInstance();
	@Autowired
	private RedisLockUtils redisLockUtils;
	@Autowired
	private DemandOrderForLeYiBiz demandOrderForLeYiBiz;

	//接收中间件转发的消息
	@EnableMqErrorMessageCollect
	@RabbitListener(bindings = {@QueueBinding(
			exchange = @Exchange(name = "bridge.topic", type = "topic"),
			value = @Queue(value = "bridge.logisticsAcceptYeloBusinessMessage", durable = "true"),
			key = "logisticsAcceptYeloBusinessMessage")}, concurrency = "3")
	public void acceptYeloBusinessMessage(@Payload String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {
		log.info("接收中间件消息：" + message);
		//解析消息体
		MQBaseMessageModel baseModel = objectMapper.readValue(message, MQBaseMessageModel.class);

		//业务实体参数
		String businessMessageStr = baseModel.getBusinessMessage();
		if (StringUtils.isBlank(businessMessageStr)) {
			log.info("接收中间件消息：业务实体参数为空");
			return;
		}

		//判断业务类型

		//生成物流需求单
		if (MQBaseMessageKeyEnum.CREATE_DEMAND_ORDER.getKey().equals(baseModel.getKey())) {
			SyncTrayDemandOrderMessage syncTrayDemandOrderMessage = objectMapper.readValue(businessMessageStr, SyncTrayDemandOrderMessage.class);
			log.info("云盘/云仓同步需求单到物流系统：" + syncTrayDemandOrderMessage);
			//取出lbs code和是否固定需求
			Map<String, String> extParamsMap = objectMapper.readValue(baseModel.getExtParams(), new TypeReference<>() {
			});
			if (!MapUtils.isEmpty(extParamsMap)) {
				syncTrayDemandOrderMessage.setLBScode(extParamsMap.get(CommonConstant.LBS_CODE));
				if (CommonConstant.ONE.equals(extParamsMap.get(CommonConstant.FIXED_FLAG))){
					syncTrayDemandOrderMessage.setFixedDemand(extParamsMap.get(CommonConstant.CODE));//智慧运营配置里某个配置的唯一code
				}
			}
			//生成需求单
			demandOrderForLeYiBiz.saveSyncTrayStockPlanDemandOrderToTms(syncTrayDemandOrderMessage);
		}

		//云盘追加/补单R单
		else if (MQBaseMessageKeyEnum.ADDITIONAL_ORDER.getKey().equals(baseModel.getKey())) {
			TmsSyncAdditionalOrderModel parse = objectMapper.readValue(businessMessageStr, TmsSyncAdditionalOrderModel.class);
			log.info("云盘追加/补单R单到物流系统：" + parse.toString());

			String uuid = UUIDGenerateUtil.generateUUID();
			Long startTime = System.currentTimeMillis();
			while (!redisLockUtils.tryLock(CommonConstant.SYN_ADDITIONAL_FORM_LE_YI+parse.getDemandOrderCode(),uuid,10)){
				try {
					Thread.sleep(200);
					Long endTime = System.currentTimeMillis();
					if (endTime-startTime > 10000){
						throw new BizException(EntrustDataExceptionEnum.GET_LOCK_ERROR.getCode(), "云盘追加/补单R单到物流系统：" + EntrustDataExceptionEnum.GET_LOCK_ERROR.getMsg());
					}
				}catch (Exception e){
					log.info(e.getMessage());
				}
			}
			try {
				demandOrderForLeYiBiz.syncAdditionalOrderFromLeyiTray(parse);
			}catch (Exception e){
				throw e;
			}finally {
				redisLockUtils.releaseLock(CommonConstant.SYN_ADDITIONAL_FORM_LE_YI + parse.getDemandOrderCode(), uuid);
			}
		}
		channel.basicAck(deliveryTag, false);
	}
}
