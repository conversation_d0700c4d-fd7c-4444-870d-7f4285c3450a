package com.logistics.management.webapi.controller.reserveapply;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.enums.ReserveApplyAuditResultEnum;
import com.logistics.management.webapi.base.enums.ReserveApplyAuditorTypeEnum;
import com.logistics.management.webapi.base.utils.ConvertPageInfoUtil;
import com.logistics.management.webapi.base.utils.ExportUtils;
import com.logistics.management.webapi.client.reserveapply.DriverReserveApplyClient;
import com.logistics.management.webapi.client.reserveapply.request.*;
import com.logistics.management.webapi.client.reserveapply.response.ReserveApplyDetailResponseModel;
import com.logistics.management.webapi.client.reserveapply.response.ReserveApplyListResponseModel;
import com.logistics.management.webapi.controller.reserveapply.mapping.ReserveApplyDetailMapping;
import com.logistics.management.webapi.controller.reserveapply.mapping.ReserveApplyListMapping;
import com.logistics.management.webapi.controller.reserveapply.request.*;
import com.logistics.management.webapi.controller.reserveapply.response.ReserveApplyDetailResponseDto;
import com.logistics.management.webapi.controller.reserveapply.response.ReserveApplyListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.Range;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 备用金申请列表
 */
@Api(value = "司机备用申请列表", tags = "司机备用申请列表")
@RestController
@RequestMapping(value = "/api/DriverReserveApply")
public class DriverReserveApplyController {

    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private DriverReserveApplyClient driverReserveApplyClient;

    /**
     * 备用金申请列表查询
     *
     * @param requestDto 请求Dto
     * @return PageInfo<ReserveApplyListResponseDto>
     */
    @ApiOperation(value = "备用金申请列表查询", tags = "1.3.6")
    @PostMapping("/reserveApplyList")
    public Result<PageInfo<ReserveApplyListResponseDto>> reserveApplyList(@RequestBody ReserveApplyListRequestDto requestDto) {
        ReserveApplyListRequestModel requestModel = MapperUtils.mapper(requestDto, ReserveApplyListRequestModel.class);
        Result<PageInfo<ReserveApplyListResponseModel>> responseResult = driverReserveApplyClient.reserveApplyList(requestModel);
        responseResult.throwException();
        PageInfo<ReserveApplyListResponseDto> pageInfo =
                ConvertPageInfoUtil.convertPageInfo(responseResult.getData(), ReserveApplyListResponseDto.class, new ReserveApplyListMapping());
        return Result.success(pageInfo);
    }

    /**
     * 备用金申请列表导出
     *
     * @param requestDto 请求Dto
     * @param response response
     */
    @ApiOperation(value = "备用金申请列表导出", tags = "1.3.6")
    @GetMapping("/reserveApplyListExport")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void reserveApplyListExport(ReserveApplyListRequestDto requestDto, HttpServletResponse response) {
        ReserveApplyListRequestModel requestModel = MapperUtils.mapper(requestDto, ReserveApplyListRequestModel.class);
        Result<List<ReserveApplyListResponseModel>> responseModelResult = driverReserveApplyClient.reserveApplyListExport(requestModel);
        responseModelResult.throwException();
        var dtoList = MapperUtils.mapper(responseModelResult.getData(), ReserveApplyListResponseDto.class, new ReserveApplyListMapping(true));
        ExportUtils.exportByYeloExcel(response, dtoList, ReserveApplyListResponseDto.class, "备用金申请列表");
    }

    /**
     * 备用金申请详情
     *
     * @param requestDto 请求Dto
     * @return ReserveApplyDetailResponseDto
     */
    @ApiOperation(value = "备用金申请详情查询", tags = "1.2.5")
    @PostMapping("/reserveApplyDetail")
    public Result<ReserveApplyDetailResponseDto> reserveApplyDetail(@Valid @RequestBody ReserveApplyDetailRequestDto requestDto) {
        ReserveApplyDetailRequestModel requestModel = MapperUtils.mapper(requestDto, ReserveApplyDetailRequestModel.class);
        Result<ReserveApplyDetailResponseModel> responseModelResult = driverReserveApplyClient.reserveApplyDetail(requestModel);
        responseModelResult.throwException();

        ReserveApplyDetailResponseModel responseModel = responseModelResult.getData();
        // 获取图片访问路径
        Map<String, String> imageMap = Optional.ofNullable(responseModel.getRemitTickets())
                .map(commonBiz::batchGetOSSFileUrl)
                .orElse(new HashMap<>());
        ReserveApplyDetailResponseDto responseDto = MapperUtils.mapper(responseModel, ReserveApplyDetailResponseDto.class,
                new ReserveApplyDetailMapping(configKeyConstant.fileAccessAddress, imageMap));
        return Result.success(responseDto);
    }

    /**
     * 备用金申请审核
     *
     * @param requestDto 请求Dto
     * @return boolean
     */
    @ApiOperation(value = "备用金申请审核", tags = "1.2.5")
    @PostMapping("/reserveApplyAudit")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> reserveApplyAudit(@Valid @RequestBody ReserveApplyAuditRequestDto requestDto) {
        // 参数校验
        // 驳回备注不允许为空
        if (ReserveApplyAuditResultEnum.OVERRULE.getKey().equals(Integer.valueOf(requestDto.getAuditResult()))) {
            if (StringUtils.isBlank(requestDto.getRemark())) {
                throw new BizException(ManagementWebApiExceptionEnum.REMARKS_VERIFICATION_MESSAGE);
            }
        }
        // 业务审核
        else if (ReserveApplyAuditorTypeEnum.AUDIT_BUSINESS_TYPE.getKey().toString().equals(requestDto.getAuditorType())) {
            // 批准金额不允许为空
            if (StringUtils.isBlank(requestDto.getApproveAmount())) {
                throw new BizException(ManagementWebApiExceptionEnum.RESERVE_APPLY_AUDIT_NOT_EMPTY_APPROVE_AMOUNT);
            }
            // 当前余额不允许为空
            if (StringUtils.isBlank(requestDto.getReserveBalance())) {
                throw new BizException(ManagementWebApiExceptionEnum.RESERVE_APPLY_AUDIT_NOT_EMPTY_BALANCE_AMOUNT);
            }
            // 校验金额
            BigDecimal approveAmount = new BigDecimal(requestDto.getApproveAmount());
            Range<BigDecimal> between = Range.between(BigDecimal.ONE, CommonConstant.BIG_DECIMAL_TWENTY_THOUSAND);
            if (!between.contains(approveAmount)) {
                throw new BizException(ManagementWebApiExceptionEnum.RESERVE_APPLY_AUDIT_APPROVE_AMOUNT_EXCESS);
            }
        }

        ReserveApplyAuditRequestModel requestModel = MapperUtils.mapper(requestDto, ReserveApplyAuditRequestModel.class);
        return driverReserveApplyClient.reserveApplyAudit(requestModel);
    }

    /**
     * 备用金申请撤销
     *
     * @param requestDto 请求Dto
     * @return Boolean
     */
    @ApiOperation(value = "备用金申请撤销", tags = "1.2.5")
    @PostMapping("/reserveApplyCancel")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> reserveApplyCancel(@Valid @RequestBody ReserveApplyCancelRequestDto requestDto) {
        ReserveApplyCancelRequestModel requestModel = MapperUtils.mapper(requestDto, ReserveApplyCancelRequestModel.class);
        return driverReserveApplyClient.reserveApplyCancel(requestModel);
    }

    /**
     * 备用金申请打款
     *
     * @param requestDto 请求Dto
     * @return Boolean
     */
    @ApiOperation(value = "备用金打款", tags = "1.2.5")
    @PostMapping("/driverReserveRemit")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> driverReserveRemit(@Valid @RequestBody ReserveApplyRemitRequestDto requestDto) {
        ReserveApplyRemitRequestModel requestModel = MapperUtils.mapper(requestDto, ReserveApplyRemitRequestModel.class);
        return driverReserveApplyClient.driverReserveRemit(requestModel);
    }
}
