package com.logistics.tms.controller.drivercostapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/7
 */
@Data
public class DriverCostStatisticsModel {

	@ApiModelProperty("申请次数")
	private Integer applyCount;

	@ApiModelProperty("批准次数")
	private Integer approvalCount;

	@ApiModelProperty("批准费用")
	private BigDecimal approvalFee;
}
