package com.logistics.appapi.client.reservationorder.request;


import com.logistics.appapi.controller.reservationorder.response.ReservationCarrierOrderListResponseDto;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ReservationOrderDetail4H5RespDto {


    /**
     * 预约单id
     */
    private String reservationOrderId="";

    /**
     * 预约单号
     */
    private String reservationOrderCode="";

    /**
     * 预约类型：1 提货，2 卸货
     */
    private String reservationType="";

    /**
     * 预约类型
     */
    private String reservationTypeLabel="";

    /**
     * 发货地/收货地
     */
    private String address="";

    /**
     * 运单信息
     */
    private ReservationCarrierOrderListResponseDto responseDto;

    /**
     * 预计里程数（公里）
     */
    private String expectMileage="";

    /**
     * 预约时间
     */
    private String reservationTime="";
}
