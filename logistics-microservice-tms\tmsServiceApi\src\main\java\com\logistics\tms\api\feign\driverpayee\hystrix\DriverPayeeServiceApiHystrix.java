package com.logistics.tms.api.feign.driverpayee.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.common.SrcUrlModel;
import com.logistics.tms.api.feign.driverpayee.DriverPayeeServiceApi;
import com.logistics.tms.api.feign.driverpayee.model.*;
import org.springframework.stereotype.Component;

import java.util.List;


@Component("tmsDriverPayeeServiceApiHystrix")
public class DriverPayeeServiceApiHystrix implements DriverPayeeServiceApi {


    @Override
    public Result<PageInfo<DriverPayeeListResponseModel>> driverPayeeList(DriverPayeeListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<ExportDriverPayeeListResponseModel>> exportDriverPayeeList(DriverPayeeListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addOrModifyDriverPayee(AddOrModifyDriverPayeeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DriverPayeeDetailResponseModel> driverPayeeDetail(DriverPayeeDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> auditOrReject(AuditRejectDriverPayeeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ImportDriverPayeeResponseModel> importDriverPayee(ImportDriverPayeeRequestModel importDriverPayeeRequestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> importDriverPayeeCertificate( SrcUrlModel srcUrlModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<ViewLogResponseModel>> driverPayeeLogs(DriverPayeeDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchDriverPayeesResponseModel>> searchDriverPayees(SearchDriverPayeesRequestModel requestDto) {
        return Result.timeout();
    }
}


