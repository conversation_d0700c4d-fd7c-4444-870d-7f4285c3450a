<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleSettlementDriverRelationMapper">
  <select id="getByVehicleSettlementId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from t_vehicle_settlement_driver_relation
    where valid=1
    and vehicle_settlement_id= #{vehicleSettlementId,jdbcType=BIGINT}
  </select>


  <insert id="batchInsertSelective" parameterType="com.logistics.tms.entity.TVehicleSettlementDriverRelation">
    <foreach collection="list" item="item" separator=";">
      insert into t_vehicle_settlement_driver_relation
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          id,
        </if>
        <if test="item.vehicleSettlementId != null">
          vehicle_settlement_id,
        </if>
        <if test="item.status != null">
          status,
        </if>
        <if test="item.commitImageUrl != null">
          commit_image_url,
        </if>
        <if test="item.settlementReasonRemark != null">
          settlement_reason_remark,
        </if>
        <if test="item.reason != null">
          reason,
        </if>
        <if test="item.confirmTime != null">
          confirm_time,
        </if>
        <if test="item.driverId != null">
          driver_id,
        </if>
        <if test="item.driverName != null">
          driver_name,
        </if>
        <if test="item.driverMobile != null">
          driver_mobile,
        </if>
        <if test="item.createdBy != null">
          created_by,
        </if>
        <if test="item.createdTime != null">
          created_time,
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time,
        </if>
        <if test="item.valid != null">
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleSettlementId != null">
          #{item.vehicleSettlementId,jdbcType=BIGINT},
        </if>
        <if test="item.status != null">
          #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.commitImageUrl != null">
          #{item.commitImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.settlementReasonRemark != null">
          #{item.settlementReasonRemark,jdbcType=VARCHAR},
        </if>
        <if test="item.reason != null">
          #{item.reason,jdbcType=VARCHAR},
        </if>
        <if test="item.confirmTime != null">
          #{item.confirmTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.driverId != null">
          #{item.driverId,jdbcType=BIGINT},
        </if>
        <if test="item.driverName != null">
          #{item.driverName,jdbcType=VARCHAR},
        </if>
        <if test="item.driverMobile != null">
          #{item.driverMobile,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null">
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <select id="getByVehicleSettlementIdDriverId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_vehicle_settlement_driver_relation
    where valid = 1
    and vehicle_settlement_id= #{vehicleSettlementId,jdbcType=BIGINT}
    and driver_id = #{driverId,jdbcType=BIGINT}
  </select>
</mapper>