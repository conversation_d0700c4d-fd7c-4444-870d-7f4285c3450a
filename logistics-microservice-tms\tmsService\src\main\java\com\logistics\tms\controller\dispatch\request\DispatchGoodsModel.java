package com.logistics.tms.controller.dispatch.request;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class DispatchGoodsModel {

    private Long demandOrderGoodsId;
    private Long demandOrderId;
    private BigDecimal loadAmount;

    /**
     * 排序（零担模式时有值）
     */
    private Integer orderNum;

    /**
     * 到下个点位距离（零担模式时有值）
     */
    private BigDecimal nextPointDistance;
}
