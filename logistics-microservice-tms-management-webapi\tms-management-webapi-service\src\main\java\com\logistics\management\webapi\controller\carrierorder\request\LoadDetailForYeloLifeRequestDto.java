package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 提卸货详情请求
 *
 * <AUTHOR>
 * @date 2022/8/16 17:36
 */
@Data
public class LoadDetailForYeloLifeRequestDto {

    @ApiModelProperty(value = "运单ID",required = true)
    @NotBlank(message = "请选择运单")
    private String carrierOrderId;
    @ApiModelProperty("节点类型：1 已提货 2 已卸货")
    private String nodeType;
}
