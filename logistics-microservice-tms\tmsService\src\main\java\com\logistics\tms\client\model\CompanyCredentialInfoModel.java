package com.logistics.tms.client.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/3/16 17:39
 */
@Data
public class CompanyCredentialInfoModel {

    @ApiModelProperty("工商注册号（统一社会信用代码，如果是三证合一，regCode/taxCode/orgCode三个填一样的值")
    private String regCode;

    @ApiModelProperty("组织机构代码")
    private String orgCode;

    @ApiModelProperty("税务登记证号")
    private String taxCode;

    @ApiModelProperty("法定代表人或经办人姓名")
    private String legalPerson;

    @ApiModelProperty("法定代表人或经办人证件号")
    private String legalPersonIdentity;

    @ApiModelProperty("法定代表人或经办人证件类型：默认为“0”，0-居民身份证 1-护照 B-港澳居民往来内地通行证 C-台湾居民来往大陆通行证 E-户口簿 F-临时居民身份证")
    private String legalPersonIdentityType = "0";

    @ApiModelProperty("联系手机必填，为CA年检抽查时联系使用，可以填写经办人手机号")
    private String contactMobile;
}
