<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TWorkGroupNodeFieldMapper">
    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TWorkGroupNodeField">
        <foreach collection="recordList" item="item" separator=";">
            insert into t_work_group_node_field
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.workGroupNodeId != null">
                    work_group_node_id,
                </if>
                <if test="item.field != null">
                    field,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.workGroupNodeId != null">
                    #{item.workGroupNodeId,jdbcType=BIGINT},
                </if>
                <if test="item.field != null">
                    #{item.field,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="selectAllByWorkGroupNodeIdIn" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_work_group_node_field
        where valid = 1
        and work_group_node_id in
        <foreach collection="workGroupNodeIds" item="workGroupNodeId" open="(" separator="," close=")">
            #{workGroupNodeId}
        </foreach>
    </select>

    <update id="delAllFieldByNodeId">
        update t_work_group_node_field
        set valid              =0,
            last_modified_by   =#{userName,jdbcType=VARCHAR},
            last_modified_time = current_timestamp()
        where valid = 1
          and work_group_node_id in
        <foreach collection="nodeIdList" open="(" separator="," close=")" item="item">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>