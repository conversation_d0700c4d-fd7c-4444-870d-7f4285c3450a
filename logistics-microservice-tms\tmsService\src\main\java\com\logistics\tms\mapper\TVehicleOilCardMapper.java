package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.vehicleoilcard.model.SearchVehicleOilCardListRequestModel;
import com.logistics.tms.api.feign.vehicleoilcard.model.SearchVehicleOilCardListResponseModel;
import com.logistics.tms.entity.TVehicleOilCard;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/08/04
*/
@Mapper
public interface TVehicleOilCardMapper extends BaseMapper<TVehicleOilCard> {

    List<SearchVehicleOilCardListResponseModel> searchVehicleOilCardList(@Param("params") SearchVehicleOilCardListRequestModel requestModel);

    TVehicleOilCard getByCardNoOrVehicleId(@Param("cardNumber")String cardNumber, @Param("vehicleId")Long vehicleId);

}