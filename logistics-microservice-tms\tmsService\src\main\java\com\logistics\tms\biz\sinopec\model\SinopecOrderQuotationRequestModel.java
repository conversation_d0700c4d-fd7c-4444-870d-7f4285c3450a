package com.logistics.tms.biz.sinopec.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2021/12/5 10:23
 */
@Data
@Accessors(chain = true)
public class SinopecOrderQuotationRequestModel {

    @ApiModelProperty("租户ID")
    private Long customerId;
    @ApiModelProperty("租户名称")
    private String customerName;
    @ApiModelProperty("订单号")
    private String orderNo;
    @ApiModelProperty("委托单号")
    private String sn;
    @ApiModelProperty("运输单价")
    private BigDecimal transUnitPrice;
    @ApiModelProperty("调度员")
    private String dispatcher;
    @ApiModelProperty("调度电话")
    private String dispatcherPhoneNo;

    @ApiModelProperty("操作人")
    private String operator;
}
