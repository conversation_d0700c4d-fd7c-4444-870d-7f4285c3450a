<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TOperateLogsMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TOperateLogs">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="object_type" jdbcType="INTEGER" property="objectType" />
    <result column="object_id" jdbcType="BIGINT" property="objectId" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
    <result column="operate_contents" jdbcType="VARCHAR" property="operateContents" />
    <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, object_type, object_id, operate_type, operate_contents, operate_user_name, operate_time, 
    remark, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_operate_logs
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_operate_logs
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TOperateLogs">
    insert into t_operate_logs (id, object_type, object_id, 
      operate_type, operate_contents, operate_user_name, 
      operate_time, remark, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{objectType,jdbcType=INTEGER}, #{objectId,jdbcType=BIGINT}, 
      #{operateType,jdbcType=INTEGER}, #{operateContents,jdbcType=VARCHAR}, #{operateUserName,jdbcType=VARCHAR}, 
      #{operateTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TOperateLogs">
    insert into t_operate_logs
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="objectType != null">
        object_type,
      </if>
      <if test="objectId != null">
        object_id,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="operateContents != null">
        operate_contents,
      </if>
      <if test="operateUserName != null">
        operate_user_name,
      </if>
      <if test="operateTime != null">
        operate_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="objectType != null">
        #{objectType,jdbcType=INTEGER},
      </if>
      <if test="objectId != null">
        #{objectId,jdbcType=BIGINT},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="operateContents != null">
        #{operateContents,jdbcType=VARCHAR},
      </if>
      <if test="operateUserName != null">
        #{operateUserName,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TOperateLogs">
    update t_operate_logs
    <set>
      <if test="objectType != null">
        object_type = #{objectType,jdbcType=INTEGER},
      </if>
      <if test="objectId != null">
        object_id = #{objectId,jdbcType=BIGINT},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="operateContents != null">
        operate_contents = #{operateContents,jdbcType=VARCHAR},
      </if>
      <if test="operateUserName != null">
        operate_user_name = #{operateUserName,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        operate_time = #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TOperateLogs">
    update t_operate_logs
    set object_type = #{objectType,jdbcType=INTEGER},
      object_id = #{objectId,jdbcType=BIGINT},
      operate_type = #{operateType,jdbcType=INTEGER},
      operate_contents = #{operateContents,jdbcType=VARCHAR},
      operate_user_name = #{operateUserName,jdbcType=VARCHAR},
      operate_time = #{operateTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>