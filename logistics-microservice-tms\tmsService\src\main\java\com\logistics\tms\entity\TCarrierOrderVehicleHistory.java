package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2023/09/26
*/
@Data
public class TCarrierOrderVehicleHistory extends BaseEntity {
    /**
    * 运单ID
    */
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    /**
    * 车辆ID
    */
    @ApiModelProperty("车辆ID")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 挂车车辆ID
    */
    @ApiModelProperty("挂车车辆ID")
    private Long trailerVehicleId;

    /**
    * 挂车车牌号
    */
    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;

    /**
    * 司机ID
    */
    @ApiModelProperty("司机ID")
    private Long driverId;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String driverName;

    /**
    * 司机手机号
    */
    @ApiModelProperty("司机手机号")
    private String driverMobile;

    /**
    * 司机身份证号码
    */
    @ApiModelProperty("司机身份证号码")
    private String driverIdentity;

    /**
    * 预计提货时间
    */
    @ApiModelProperty("预计提货时间")
    private Date expectLoadTime;

    /**
    * 预计到货时间
    */
    @ApiModelProperty("预计到货时间")
    private Date expectArrivalTime;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 修改前有效数据的id
    */
    @ApiModelProperty("修改前有效数据的id")
    private Long vehicleHistoryId;

    /**
    * 是否有效 1 有效 0 无效
    */
    @ApiModelProperty("是否有效 1 有效 0 无效")
    private Integer ifInvalid;

    /**
    * 审核状态 -1 无需审核 0 待审核 1 已审核 2 已驳回
    */
    @ApiModelProperty("审核状态 -1 无需审核 0 待审核 1 已审核 2 已驳回")
    private Integer auditStatus;

    /**
    * 审核时间
    */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
    * 审核人id
    */
    @ApiModelProperty("审核人id")
    private Long auditUserId;

    /**
    * 审核人姓名
    */
    @ApiModelProperty("审核人姓名")
    private String auditUserName;

    /**
    * 审核来源：1 后台，2 前台
    */
    @ApiModelProperty("审核来源：1 后台，2 前台")
    private Integer auditSource;

    /**
    * 驳回理由
    */
    @ApiModelProperty("驳回理由")
    private String rejectReason;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}