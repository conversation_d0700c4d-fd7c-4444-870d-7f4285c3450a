package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TBank extends BaseEntity {
    /**
    * 银行名称
    */
    @ApiModelProperty("银行名称")
    private String bankName;

    /**
    * 支行名称
    */
    @ApiModelProperty("支行名称")
    private String branchName;

    /**
    * 启用/禁用
    */
    @ApiModelProperty("启用/禁用")
    private Integer enabled;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}