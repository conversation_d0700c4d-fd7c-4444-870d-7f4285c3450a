package com.logistics.tms.controller.staffvehiclerelation.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车辆资产-司机车辆关联
 * @Author: sj
 * @Date: 2019/7/26 10:05
 */
@Data
public class SaveOrModifyStaffVehicleRequestModel {

    @ApiModelProperty("车辆司机关联ID")
    private Long staffVehicleRelationId;

    @ApiModelProperty("类型 1 自主，2 外部，3 自营")
    private Integer type;

    @ApiModelProperty("车辆类别: 1 牵引车 2 挂车 3 一体车")
    private Integer vehicleCategory;

    @ApiModelProperty("司机ID")
    private Long staffId;

    @ApiModelProperty("一体车ID")
    private Long oneVehicleId;

    @ApiModelProperty("牵引车ID")
    private Long tractorVehicleId;

    @ApiModelProperty("挂车ID")
    private Long trailerVehicleId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否我司: 1:我司 2:其他车主")
    private Integer isOurCompany;

    @ApiModelProperty("车主id(其他车主时填写)")
    private Long companyCarrierId;

    @ApiModelProperty(value = "其他车主车辆ID")
    private Long vehicleId;

    @ApiModelProperty("来源 1后台 2 前台")
    private Integer source;
}
