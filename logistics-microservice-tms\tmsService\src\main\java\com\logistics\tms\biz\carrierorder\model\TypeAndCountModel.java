package com.logistics.tms.biz.carrierorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:lei.zhu
 * @date:2021/12/28 17:42:08
 */
@Data
public class TypeAndCountModel {
    @ApiModelProperty(value = "提货数量")
    private Integer loadingCount;

    @ApiModelProperty(value = "卸货数量")
    private Integer unloadingCount;

    @ApiModelProperty("sku code")
    private String productTypeCode;

    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    @ApiModelProperty(value = "托盘品名")
    private String sortName;

    @ApiModelProperty(value = "长")
    private String length;

    @ApiModelProperty(value = "宽")
    private String width;

    @ApiModelProperty(value = "高")
    private String height;
}
