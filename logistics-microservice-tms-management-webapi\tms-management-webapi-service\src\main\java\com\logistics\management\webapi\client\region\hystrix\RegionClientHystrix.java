package com.logistics.management.webapi.client.region.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.region.RegionClient;
import com.logistics.management.webapi.client.region.request.*;
import com.logistics.management.webapi.client.region.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/4 11:23
 */
@Component
public class RegionClientHystrix implements RegionClient {
    @Override
    public Result<PageInfo<SearchRegionResponseModel>> searchList(SearchRegionRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<RegionCompanyResponseModel>> getRegionCompany(RegionDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveOrModifyRegion(SaveOrModifyRegionRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<RegionDetailResponseModel> getDetail(RegionDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enableRegion(EnableRegionRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchRegionResponseModel>> export(SearchRegionRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> removeRegion(RemoveRegionRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchRegionDetailResponseModel>> searchDetailList(SearchRegionDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchRegionDetailResponseModel>> exportRegionDetail(SearchRegionDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetCompanyCarrierByRegionResponseModel>> getCompanyByRegion(GetCompanyCarrierByRegionRequestModel requestModel) {
        return Result.timeout();
    }
}
