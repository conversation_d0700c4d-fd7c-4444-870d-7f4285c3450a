package com.logistics.tms.client.feign.basicdata.ocr.hystrix;

import com.logistics.tms.client.feign.basicdata.ocr.IOCRServiceApi;
import com.logistics.tms.client.feign.basicdata.ocr.request.OCRCustomizationIdentifyRequestDto;
import com.logistics.tms.client.feign.basicdata.ocr.request.OCRIdentifyQRCodeRequestDto;
import com.logistics.tms.client.feign.basicdata.ocr.response.OCRCustomizationIdentifyResponseDto;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

@Component
public class IOCRServiceApiHystrix implements IOCRServiceApi {

    @Override
    public Result<OCRCustomizationIdentifyResponseDto> ocrPicture(OCRCustomizationIdentifyRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<String> qrCodeOSSPath(OCRIdentifyQRCodeRequestDto requestDto) {
        return Result.timeout();
    }
}
