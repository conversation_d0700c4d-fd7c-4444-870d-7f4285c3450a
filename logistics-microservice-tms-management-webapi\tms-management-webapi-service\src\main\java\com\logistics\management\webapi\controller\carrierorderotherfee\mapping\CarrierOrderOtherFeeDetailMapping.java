package com.logistics.management.webapi.controller.carrierorderotherfee.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.carrierorderotherfee.response.CarrierOrderOtherFeeDetailResponseModel;
import com.logistics.management.webapi.client.carrierorderotherfee.response.CarrierOrderOtherFeeItemDetailResponseModel;
import com.logistics.management.webapi.controller.carrierorderotherfee.response.BillPicDto;
import com.logistics.management.webapi.controller.carrierorderotherfee.response.CarrierOrderOtherFeeDetailResponseDto;
import com.logistics.management.webapi.controller.carrierorderotherfee.response.CarrierOrderOtherFeeItemDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class CarrierOrderOtherFeeDetailMapping extends MapperMapping<CarrierOrderOtherFeeDetailResponseModel, CarrierOrderOtherFeeDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    public CarrierOrderOtherFeeDetailMapping(String imagePrefix ,Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        CarrierOrderOtherFeeDetailResponseModel source = getSource();
        CarrierOrderOtherFeeDetailResponseDto destination = getDestination();

        //车主转换
        if(CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyCarrierType())){
            destination.setCompanyCarrierName(Optional.ofNullable(source.getCompanyCarrierName()).orElse(""));
        }else if(CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())){
            destination.setCompanyCarrierName(Optional.ofNullable(source.getCarrierContactName()).orElse("")+" "+Optional.ofNullable(source.getCarrierContactMobile()).orElse(""));
        }
        //司机转换
        destination.setDriver(source.getDriverName());

        //审核状态转换
        destination.setAuditStatusLabel(CarrierOrderOtherFeeStatusEnum.getEnum(source.getAuditStatus()).getValue());

        //提货地址
        destination.setLoadAddress(source.getLoadCityName());
        //卸货地址
        destination.setUnloadAddress(source.getUnloadCityName());

        //数量转换
        BigDecimal carrierOrderAmount = BigDecimal.ZERO;
        if (source.getStatus() < CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey()){//未提货
            carrierOrderAmount = source.getExpectAmount();
        }else if (source.getStatus() < CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()){//未卸货
            carrierOrderAmount = source.getLoadAmount();
        }else if (source.getStatus().equals(CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey())){//待签收
            carrierOrderAmount = source.getUnloadAmount();
        }else if (source.getStatus().equals(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey())){//已签收
            carrierOrderAmount = source.getSignAmount();
        }
        destination.setCarrierOrderAmount(carrierOrderAmount.stripTrailingZeros().toPlainString() + GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());

        //装卸方式
        destination.setLoadingUnloadingPartLabel(LoadingUnloadingPartEnum.getEnum(source.getLoadingUnloadingPart()).getValue());

        //临时费用转换
        List<CarrierOrderOtherFeeItemDetailResponseModel> otherFeeModelList = source.getOtherFeeList();
        List<CarrierOrderOtherFeeItemDetailResponseDto> otherFeeDtoList = new ArrayList<>();
        otherFeeModelList.forEach(o->{
            CarrierOrderOtherFeeItemDetailResponseDto carrierOrderOtherFeeItemDetailResponseDto = new CarrierOrderOtherFeeItemDetailResponseDto();
            carrierOrderOtherFeeItemDetailResponseDto.setCarrierOrderOtherFeeItemId(ConverterUtils.toString(o.getCarrierOrderOtherFeeItemId()));
            if (o.getFeeAmount().compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO){
                carrierOrderOtherFeeItemDetailResponseDto.setFeeAmountSymbol(CommonConstant.TWO);
                carrierOrderOtherFeeItemDetailResponseDto.setFeeAmount(ConverterUtils.toString(o.getFeeAmount().negate()));
            }else{
                carrierOrderOtherFeeItemDetailResponseDto.setFeeAmountSymbol(CommonConstant.ONE);
                carrierOrderOtherFeeItemDetailResponseDto.setFeeAmount(ConverterUtils.toString(o.getFeeAmount()));
            }
            carrierOrderOtherFeeItemDetailResponseDto.setFeeType(ConverterUtils.toString(o.getFeeType()));
            carrierOrderOtherFeeItemDetailResponseDto.setFeeTypeLabel(CarrierOrderOtherFeeTypeEnum.getEnum(o.getFeeType()).getValue());

            List<String> billsPicture = o.getBillsPicture();
            List<BillPicDto> picList= new ArrayList<>();
            for (String pic : billsPicture){
                BillPicDto billPicDto = new BillPicDto();
                billPicDto.setBillsPicture(pic);
                billPicDto.setBillsPictureUrl(imagePrefix+imageMap.get(pic));
                picList.add(billPicDto);
            }
            carrierOrderOtherFeeItemDetailResponseDto.setPicList(picList);
            carrierOrderOtherFeeItemDetailResponseDto.setFeeSource(o.getFeeSource().toString());
            carrierOrderOtherFeeItemDetailResponseDto.setFeeSourceLabel(CarrierOrderOtherFeeSourceEnum.getEnum(o.getFeeSource()).getValue());
            otherFeeDtoList.add(carrierOrderOtherFeeItemDetailResponseDto);
        });
        destination.setOtherFeeList(otherFeeDtoList);
    }
}
