package com.logistics.tms.base.utils;

import cn.hutool.crypto.CipherMode;
import cn.hutool.crypto.symmetric.AES;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;

@Slf4j
public class MysqlAESUtils {

    public static String decrypt(String source, String key) {
        try {
            SecretKey AESKey = generateMySQLAESKey(key, "ASCII");
            AES aes = new AES(AESKey);
            aes.setMode(CipherMode.decrypt);
            byte[] cleartext = Hex.decodeHex(source.toCharArray());
            byte[] decrypt = aes.decrypt(cleartext);
            return new String(decrypt, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.warn(source + " " + e.getMessage());
            return null;
        }
    }

    public static SecretKeySpec generateMySQLAESKey(String key, String encoding) {
        final byte[] finalKey = new byte[16];
        int i = 0;
        try {
            for (byte b : key.getBytes(encoding))
                finalKey[i++ % 16] ^= b;
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        return new SecretKeySpec(finalKey, "AES");
    }

    public static void main(String[] args) {
        System.out.println(decrypt("076B267A7A4058781A53EEEC47B1837E", "1W5hoD!qU^5&L#Ix"));
    }
}