<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TShippingOrderMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TShippingOrder" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shipping_order_code" property="shippingOrderCode" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="dispatch_order_id" property="dispatchOrderId" jdbcType="BIGINT" />
    <result column="dispatch_order_code" property="dispatchOrderCode" jdbcType="VARCHAR" />
    <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL" />
    <result column="goods_unit" property="goodsUnit" jdbcType="INTEGER" />
    <result column="vehicle_length" property="vehicleLength" jdbcType="DECIMAL" />
    <result column="carrier_freight" property="carrierFreight" jdbcType="DECIMAL" />
    <result column="cross_point_fee" property="crossPointFee" jdbcType="DECIMAL" />
    <result column="cross_point_distance" property="crossPointDistance" jdbcType="DECIMAL" />
    <result column="auditor_name" property="auditorName" jdbcType="VARCHAR" />
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
    <result column="audit_remark" property="auditRemark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, shipping_order_code, status, dispatch_order_id, dispatch_order_code, expect_amount, 
    goods_unit, vehicle_length, carrier_freight, cross_point_fee, cross_point_distance, 
    auditor_name, audit_time, audit_remark, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_shipping_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_shipping_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TShippingOrder" >
    insert into t_shipping_order (id, shipping_order_code, status, 
      dispatch_order_id, dispatch_order_code, expect_amount, 
      goods_unit, vehicle_length, carrier_freight, 
      cross_point_fee, cross_point_distance, auditor_name, 
      audit_time, audit_remark, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{shippingOrderCode,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{dispatchOrderId,jdbcType=BIGINT}, #{dispatchOrderCode,jdbcType=VARCHAR}, #{expectAmount,jdbcType=DECIMAL}, 
      #{goodsUnit,jdbcType=INTEGER}, #{vehicleLength,jdbcType=DECIMAL}, #{carrierFreight,jdbcType=DECIMAL}, 
      #{crossPointFee,jdbcType=DECIMAL}, #{crossPointDistance,jdbcType=DECIMAL}, #{auditorName,jdbcType=VARCHAR}, 
      #{auditTime,jdbcType=TIMESTAMP}, #{auditRemark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TShippingOrder" keyProperty="id" useGeneratedKeys="true" >
    insert into t_shipping_order
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="shippingOrderCode != null" >
        shipping_order_code,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="dispatchOrderId != null" >
        dispatch_order_id,
      </if>
      <if test="dispatchOrderCode != null" >
        dispatch_order_code,
      </if>
      <if test="expectAmount != null" >
        expect_amount,
      </if>
      <if test="goodsUnit != null" >
        goods_unit,
      </if>
      <if test="vehicleLength != null" >
        vehicle_length,
      </if>
      <if test="carrierFreight != null" >
        carrier_freight,
      </if>
      <if test="crossPointFee != null" >
        cross_point_fee,
      </if>
      <if test="crossPointDistance != null" >
        cross_point_distance,
      </if>
      <if test="auditorName != null" >
        auditor_name,
      </if>
      <if test="auditTime != null" >
        audit_time,
      </if>
      <if test="auditRemark != null" >
        audit_remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shippingOrderCode != null" >
        #{shippingOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="dispatchOrderId != null" >
        #{dispatchOrderId,jdbcType=BIGINT},
      </if>
      <if test="dispatchOrderCode != null" >
        #{dispatchOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="expectAmount != null" >
        #{expectAmount,jdbcType=DECIMAL},
      </if>
      <if test="goodsUnit != null" >
        #{goodsUnit,jdbcType=INTEGER},
      </if>
      <if test="vehicleLength != null" >
        #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="carrierFreight != null" >
        #{carrierFreight,jdbcType=DECIMAL},
      </if>
      <if test="crossPointFee != null" >
        #{crossPointFee,jdbcType=DECIMAL},
      </if>
      <if test="crossPointDistance != null" >
        #{crossPointDistance,jdbcType=DECIMAL},
      </if>
      <if test="auditorName != null" >
        #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemark != null" >
        #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TShippingOrder" >
    update t_shipping_order
    <set >
      <if test="shippingOrderCode != null" >
        shipping_order_code = #{shippingOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="dispatchOrderId != null" >
        dispatch_order_id = #{dispatchOrderId,jdbcType=BIGINT},
      </if>
      <if test="dispatchOrderCode != null" >
        dispatch_order_code = #{dispatchOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="expectAmount != null" >
        expect_amount = #{expectAmount,jdbcType=DECIMAL},
      </if>
      <if test="goodsUnit != null" >
        goods_unit = #{goodsUnit,jdbcType=INTEGER},
      </if>
      <if test="vehicleLength != null" >
        vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="carrierFreight != null" >
        carrier_freight = #{carrierFreight,jdbcType=DECIMAL},
      </if>
      <if test="crossPointFee != null" >
        cross_point_fee = #{crossPointFee,jdbcType=DECIMAL},
      </if>
      <if test="crossPointDistance != null" >
        cross_point_distance = #{crossPointDistance,jdbcType=DECIMAL},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditRemark != null" >
        audit_remark = #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TShippingOrder" >
    update t_shipping_order
    set shipping_order_code = #{shippingOrderCode,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      dispatch_order_id = #{dispatchOrderId,jdbcType=BIGINT},
      dispatch_order_code = #{dispatchOrderCode,jdbcType=VARCHAR},
      expect_amount = #{expectAmount,jdbcType=DECIMAL},
      goods_unit = #{goodsUnit,jdbcType=INTEGER},
      vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      carrier_freight = #{carrierFreight,jdbcType=DECIMAL},
      cross_point_fee = #{crossPointFee,jdbcType=DECIMAL},
      cross_point_distance = #{crossPointDistance,jdbcType=DECIMAL},
      auditor_name = #{auditorName,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      audit_remark = #{auditRemark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>