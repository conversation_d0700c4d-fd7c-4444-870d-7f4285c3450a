package com.logistics.management.webapi.client.freightconfig.request.shipping;

import lombok.Data;

import java.util.List;

@Data
public class AddShippingFreightRuleReqModel {

    /**
     * 运价规则id
     */
    private Long shippingFreightId;

    /**
     * 收货省
     */
    private Long toProvinceId;


    /**
     * 收货省name
     */
    private String toProvinceName;

    /**
     * 收货市id
     */
    private Long toCityId;

    /**
     * 收货市name
     */
    private String toCityName;


    /**
     * 收货区id
     */
    private Long toAreaId;


    /**
     * 收货区name
     */
    private String toAreaName;


    /**
     * 发货地集合
     */
    private List<AddShippingFreightRuleAddressReqModel> fromAddressItems;




}
