package com.logistics.tms.biz.vehicletype;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.EnabledEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.vehicletype.request.*;
import com.logistics.tms.controller.vehicletype.response.GetVehicleTypeSearchByNameResponseModel;
import com.logistics.tms.controller.vehicletype.response.ImportVehicleTypeResponseModel;
import com.logistics.tms.controller.vehicletype.response.VehicleTypeDetailResponseModel;
import com.logistics.tms.controller.vehicletype.response.VehicleTypeListResponseModel;
import com.logistics.tms.entity.TVehicleType;
import com.logistics.tms.mapper.TVehicleTypeMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @Author:qys
 * @Data:2019/5/30 15:36
 */

@Service
public class VehicleTypeBiz {
    @Autowired
    private TVehicleTypeMapper tVehicleTypeMapper;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 模糊查询已有的车辆类型
     *
     * @param requestModel
     * @return
     */
    public List<GetVehicleTypeSearchByNameResponseModel> fuzzyVehicleType(GetVehicleTypeSearchByNameRequestModel requestModel) {
        List<GetVehicleTypeSearchByNameResponseModel> list = tVehicleTypeMapper.fuzzyVehicleType(requestModel.getVehicleType());
        if (ListUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return list;
    }

    /**
     * 查询车辆类型列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<VehicleTypeListResponseModel> searchVehicleTypeList(VehicleTypeListRequestModel requestModel) {
        List<Long> vehicleTypeIdList=tVehicleTypeMapper.searchVehicleTypeIdList(requestModel);
        PageInfo pageInfo = new PageInfo(vehicleTypeIdList);
        pageInfo.setList(new ArrayList());
        if(ListUtils.isNotEmpty(vehicleTypeIdList)){
            List<VehicleTypeListResponseModel> vehicleTypeList=tVehicleTypeMapper.searchVehicleType(StringUtils.listToString(vehicleTypeIdList,','));
            if(ListUtils.isNotEmpty(vehicleTypeList)){
                pageInfo.setList(vehicleTypeList);
            }
        }
        return pageInfo;
    }

    /**
     * 新增/修改车辆类型
     *
     * @param requestModel
     */
    @Transactional
    public void addOrModifyVehicleType(AddOrModifyVehicleTypeRequestModel requestModel) {
        TVehicleType tqVehicleType = new TVehicleType();
        tqVehicleType.setVehicleCategory(requestModel.getVehicleCategory());
        tqVehicleType.setVehicleType(requestModel.getVehicleType());
        tqVehicleType.setRemark(requestModel.getRemark());

        TVehicleType vehicleTypeList= tVehicleTypeMapper.selectListByType(requestModel.getVehicleType());
        if(requestModel.getVehicleTypeId()==null||requestModel.getVehicleTypeId()<=CommonConstant.INTEGER_ZERO){ //新增车辆类型
            if(vehicleTypeList !=null){
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ROUTE_EXIST);
            }
            tqVehicleType.setAddUserId(BaseContextHandler.getUserId());
            tqVehicleType.setAddUserName(BaseContextHandler.getUserName());
            tqVehicleType.setSource(CommonConstant.INTEGER_ONE);
            commonBiz.setBaseEntityAdd(tqVehicleType,BaseContextHandler.getUserName());
            tVehicleTypeMapper.insertSelective(tqVehicleType);
        }else{ //修改车辆类型
            TVehicleType vehicleType = tVehicleTypeMapper.selectByPrimaryKey(requestModel.getVehicleTypeId());
            if(vehicleType==null){
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_TYPE_IS_EMPTY);
            }
            //重型半挂牵引车-不允许编辑
            if(CommonConstant.VEHICLE_TYPE_LABEL.equals(vehicleType.getVehicleType()) &&
                    (!requestModel.getVehicleType().equals(vehicleType.getVehicleType())
                    || !requestModel.getVehicleCategory().equals(vehicleType.getVehicleCategory()))){
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_TYPE_NOT_EDIT);
            }
            if(vehicleTypeList !=null && !vehicleTypeList.getId().equals(requestModel.getVehicleTypeId())){
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ROUTE_EXIST);
            }
            tqVehicleType.setId(requestModel.getVehicleTypeId());
            commonBiz.setBaseEntityModify(tqVehicleType,BaseContextHandler.getUserName());
            tVehicleTypeMapper.updateByPrimaryKeySelective(tqVehicleType);
        }
    }

    /**
     * 查询车辆类型详情
     *
     * @param requestModel
     * @return
     */
    public VehicleTypeDetailResponseModel getDetail(VehicleTypeDetailRequestModel requestModel) {
        if(requestModel.getVehicleTypeId()==null || requestModel.getVehicleTypeId()<=CommonConstant.INTEGER_ZERO){
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }
        VehicleTypeDetailResponseModel responseModel = new VehicleTypeDetailResponseModel();
        TVehicleType vehicleType=tVehicleTypeMapper.selectByPrimaryKey(requestModel.getVehicleTypeId());
        if(vehicleType==null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_TYPE_IS_EMPTY);
        }
        responseModel.setVehicleCategory(vehicleType.getVehicleCategory());
        responseModel.setVehicleType(vehicleType.getVehicleType());
        responseModel.setRemark(vehicleType.getRemark());
        return responseModel;
    }


    /**
     * 启用/禁用
     *
     * @param requestModel
     */
    @Transactional
    public void enableOrDisable(EnableVehicleTypeModel requestModel) {
        TVehicleType tqVehicleType = tVehicleTypeMapper.selectByPrimaryKey(requestModel.getVehicleTypeId());
        if (tqVehicleType == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_TYPE_IS_EMPTY);
        }

        TVehicleType upVehicleTypeEnable = new TVehicleType();
        upVehicleTypeEnable.setId(tqVehicleType.getId());
        if (EnabledEnum.ENABLED.getKey().equals(requestModel.getEnabled())) {
            if (EnabledEnum.ENABLED.getKey().equals(tqVehicleType.getEnabled())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_TYPE_ENABLE_DISABLE_ERROR);
            }
        } else if (EnabledEnum.DISABLED.getKey().equals(requestModel.getEnabled())) {
            if (EnabledEnum.DISABLED.getKey().equals(tqVehicleType.getEnabled())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_TYPE_ENABLE_DISABLE_ERROR);
            }
        } else {
            throw new BizException(CarrierDataExceptionEnum.ENABLE_DISABLE_PARAMS_ERROR);
        }
        upVehicleTypeEnable.setEnabled(requestModel.getEnabled());
        commonBiz.setBaseEntityModify(upVehicleTypeEnable, BaseContextHandler.getUserName());
        tVehicleTypeMapper.updateByPrimaryKeySelective(upVehicleTypeEnable);
    }

    /**
     * 批量导入新增车辆类型
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public ImportVehicleTypeResponseModel importVehicleType(ImportVehicleTypeRequestModel requestModel) {
        ImportVehicleTypeResponseModel responseModel = new ImportVehicleTypeResponseModel();
        responseModel.initNumber(requestModel.getNumberFailure());

        List<VehicleTypeRequestModel> importList = requestModel.getImportList();
        if(ListUtils.isEmpty(importList)){
            return responseModel;
        }
        List<VehicleTypeListResponseModel> dbVehicleTypeList = tVehicleTypeMapper.searchVehicleTypeList(new VehicleTypeListRequestModel());
        List<String>  vehicleType=dbVehicleTypeList.stream().map(VehicleTypeListResponseModel::getVehicleType).distinct().collect(Collectors.toList());
        Map<String,Long>  vehicleTypeIdMap = new HashMap<>();
        dbVehicleTypeList.stream().forEach(t->vehicleTypeIdMap.putIfAbsent(t.getVehicleType(),t.getVehicleTypeId()));
        TVehicleType tqVehicleType =  null;
        List<TVehicleType> addVehicleTypeList =new ArrayList<>();
        List<TVehicleType> upVehicleTypeList =new ArrayList<>();

        for(VehicleTypeRequestModel tmp: importList){

            tqVehicleType = new TVehicleType();
            tqVehicleType.setVehicleType(tmp.getVehicleType());
            tqVehicleType.setVehicleCategory(tmp.getVehicleCategory());
            tqVehicleType.setRemark(tmp.getRemark());
            tqVehicleType.setSource(CommonConstant.INTEGER_TWO);
            if(vehicleType.contains(tmp.getVehicleType())){
                tqVehicleType.setId(vehicleTypeIdMap.get(tmp.getVehicleType()));
                commonBiz.setBaseEntityModify(tqVehicleType,BaseContextHandler.getUserName());
                upVehicleTypeList.add(tqVehicleType);
            }else {
                tqVehicleType.setAddUserId(BaseContextHandler.getUserId());
                tqVehicleType.setAddUserName(BaseContextHandler.getUserName());
                tqVehicleType.setEnabled(EnabledEnum.ENABLED.getKey());
                commonBiz.setBaseEntityAdd(tqVehicleType,BaseContextHandler.getUserName());
                addVehicleTypeList.add(tqVehicleType);
            }
            responseModel.addSuccessful();
        }
        if(ListUtils.isNotEmpty(addVehicleTypeList)){
            tVehicleTypeMapper.batchInsert(addVehicleTypeList);
        }
        if(ListUtils.isNotEmpty(upVehicleTypeList)){
            tVehicleTypeMapper.batchUpdate(upVehicleTypeList);
        }
        return responseModel;
    }
}