package com.logistics.appapi.controller.reservationorder;

import cn.dev33.satoken.annotation.SaIgnore;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.base.enums.VerificationCodeSourceTypeEnum;
import com.logistics.appapi.base.enums.VerificationCodeTypeEnum;
import com.logistics.appapi.base.utils.FrequentMethodUtils;
import com.logistics.appapi.base.utils.TimeSplitterUtils;
import com.logistics.appapi.client.reservationorder.ReservationOrderClient;
import com.logistics.appapi.client.reservationorder.request.*;
import com.logistics.appapi.client.reservationorder.response.*;
import com.logistics.appapi.client.thirdparty.warehouse.stock.WarehouseStockClient;
import com.logistics.appapi.client.thirdparty.warehouse.stock.reservationconfig.response.SearchReservationTimeByWarehouseResponseModel;
import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.controller.reservationorder.mapping.*;
import com.logistics.appapi.controller.reservationorder.request.*;
import com.logistics.appapi.controller.reservationorder.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 预约单管理
 * @author: wjf
 * @date: 2024/8/14 14:00
 */
@Slf4j
@Api(value = "预约单管理", tags = "预约单管理")
@RestController
@RequestMapping(value = "/api/reservationOrder")
public class ReservationOrderController {

    @Resource
    private ReservationOrderClient reservationOrderClient;
    @Resource
    private WarehouseStockClient warehouseStockClient;

    /**
     * 预约汇总 3.13.0
     * @return
     */
    @PostMapping(value = "/summary")
    public Result<ReservationOrderSummaryResponseDto> summary(){
        Result<ReservationOrderSummaryResponseModel> result = reservationOrderClient.summary();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ReservationOrderSummaryResponseDto.class, new ReservationOrderSummaryMapping()));
    }

    /**
     * 待预约列表 3.13.0
     * @return
     */
    @PostMapping(value = "/waitReservationList")
    public Result<WaitReservationResponseDto> waitReservationList(@RequestBody @Valid WaitReservationRequestDto requestDto){
        WaitReservationRequestModel requestModel = MapperUtils.mapper(requestDto, WaitReservationRequestModel.class);
        Result<WaitReservationResponseModel> result = reservationOrderClient.waitReservationList(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), WaitReservationResponseDto.class, new WaitReservationListMapping()));
    }

    /**
     * 待预约详情 3.13.0
     * @return
     */
    @PostMapping(value = "/waitReservationDetail")
    public Result<WaitReservationDetailResponseDto> waitReservationDetail(@RequestBody @Valid WaitReservationDetailRequestDto requestDto){
        WaitReservationDetailRequestModel requestModel = MapperUtils.mapper(requestDto, WaitReservationDetailRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ZERO);
        Result<WaitReservationDetailResponseModel> result = reservationOrderClient.waitReservationDetail(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), WaitReservationDetailResponseDto.class, new WaitReservationDetailMapping()));
    }

    /**
     * 获取预约时间段
     * @return
     */
    @ApiOperation("获取预约时间段 v2.45")
    @PostMapping(value = "/getReservationTime")
    public Result<GetReservationTimeResponseDto> getReservationTime(@RequestBody @Valid GetReservationTimeRequestDto requestDto){
        SearchReservationTimeByWarehouseResponseModel reservationTimeModel = warehouseStockClient.searchReservationTimeByWarehouse(requestDto.getWarehouse());
        GetReservationTimeResponseDto responseDto = new GetReservationTimeResponseDto();

        boolean ifAllBlank = StringUtils.isBlank(reservationTimeModel.getTodayTimeStart())
                && StringUtils.isBlank(reservationTimeModel.getTodayTimeEnd())
                && StringUtils.isBlank(reservationTimeModel.getTomorrowTimeStart())
                && StringUtils.isBlank(reservationTimeModel.getTomorrowTimeEnd());
        if (ifAllBlank){
            return Result.success(responseDto);
        }
        String currentTime = DateUtils.currentFormatDate(CommonConstant.DATE_TO_STRING_HM_PATTERN);
        List<String> reservationTimeList = new ArrayList<>();
        List<String> tomorrowReservationTimeList = new ArrayList<>();
        if (StringUtils.isNotBlank(reservationTimeModel.getTodayTimeStart()) && StringUtils.isNotBlank(reservationTimeModel.getTodayTimeEnd())){
             reservationTimeList = TimeSplitterUtils.getTimePeriod(reservationTimeModel.getTodayTimeStart(), reservationTimeModel.getTodayTimeEnd(), currentTime, CommonConstant.INTEGER_ONE);
        }
        if (StringUtils.isNotBlank(reservationTimeModel.getTomorrowTimeStart()) && StringUtils.isNotBlank(reservationTimeModel.getTomorrowTimeEnd())){
            tomorrowReservationTimeList = TimeSplitterUtils.getTimePeriod(reservationTimeModel.getTomorrowTimeStart(), reservationTimeModel.getTomorrowTimeEnd());
        }
        responseDto.setReservationTimeList(reservationTimeList);
        responseDto.setTomorrowReservationTimeList(tomorrowReservationTimeList);
        return Result.success(responseDto);
    }

    /**
     * 确认预约 3.13.0
     * @return
     */
    @PostMapping(value = "/confirmReservation")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> confirmReservation(@RequestBody @Valid ConfirmReservationRequestDto requestDto){
        ConfirmReservationRequestModel requestModel = MapperUtils.mapper(requestDto, ConfirmReservationRequestModel.class);
        requestModel.setSource(0);
        Result<Boolean> result = reservationOrderClient.confirmReservation(requestModel);
        result.throwException();
        return result;
    }

    /**
     * 预约单详情
     * @return
     */
    @PostMapping(value = "/reservationOrderDetail")
    public Result<ReservationOrderSignDetailResponseDto> reservationOrderDetail(@RequestBody @Valid ReservationOrderSignDetailRequestDto requestDto){
        ReservationOrderSignDetailRequestModel requestModel = MapperUtils.mapper(requestDto, ReservationOrderSignDetailRequestModel.class);
        requestModel.setSource(0);
        Result<ReservationOrderSignDetailResponseModel> result = reservationOrderClient.reservationOrderDetail(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ReservationOrderSignDetailResponseDto.class, new ReservationOrderDetailMapping()));
    }

    /**
     * 确认签到 3.13.0
     * @return
     */
    @PostMapping(value = "/confirmSign")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> confirmSign(@RequestBody @Valid ReservationOrderConfirmSignRequestDto requestDto){
        ReservationOrderConfirmSignRequestModel requestModel = MapperUtils.mapper(requestDto, ReservationOrderConfirmSignRequestModel.class);
        requestModel.setSource(0);
        Result<Boolean> result = reservationOrderClient.confirmSign(requestModel);
        result.throwException();
        return result;
    }




}
