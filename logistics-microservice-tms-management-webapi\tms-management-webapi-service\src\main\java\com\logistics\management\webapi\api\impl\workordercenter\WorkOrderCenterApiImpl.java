package com.logistics.management.webapi.api.impl.workordercenter;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.workordercenter.WorkOrderCenterApi;
import com.logistics.management.webapi.api.feign.workordercenter.dto.request.*;
import com.logistics.management.webapi.api.feign.workordercenter.dto.response.WorkOrderDetailResponseDto;
import com.logistics.management.webapi.api.feign.workordercenter.dto.response.WorkOrderListResponseDto;
import com.logistics.management.webapi.api.feign.workordercenter.dto.response.WorkOrderProcessResponseDto;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.workordercenter.mapping.WorkOrderDetailMapping;
import com.logistics.management.webapi.api.impl.workordercenter.mapping.WorkOrderListMapping;
import com.logistics.management.webapi.api.impl.workordercenter.mapping.WorkOrderProcessMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.ExportUtils;
import com.logistics.tms.api.feign.workordercenter.WorkOrderCenterServiceApi;
import com.logistics.tms.api.feign.workordercenter.enums.WorkOrderTypeEnum;
import com.logistics.tms.api.feign.workordercenter.enums.WorkReportSourceEnum;
import com.logistics.tms.api.feign.workordercenter.model.request.*;
import com.logistics.tms.api.feign.workordercenter.model.response.WorkOrderDetailResponseModel;
import com.logistics.tms.api.feign.workordercenter.model.response.WorkOrderListResponseModel;
import com.logistics.tms.api.feign.workordercenter.model.response.WorkOrderProcessResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;

/**
 * @author: wjf
 * @date: 2023/4/17 9:12
 */
@RestController
public class WorkOrderCenterApiImpl implements WorkOrderCenterApi {

    @Autowired
    private WorkOrderCenterServiceApi workOrderCenterServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;


    /**
     * 工单列表
     *
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<WorkOrderListResponseDto>> workOrderList(@RequestBody WorkOrderListRequestDto requestDto) {
        Result<PageInfo<WorkOrderListResponseModel>> result = workOrderCenterServiceApi.workOrderList(MapperUtils.mapperNoDefault(requestDto, WorkOrderListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(), WorkOrderListResponseDto.class, new WorkOrderListMapping()));
        return Result.success(pageInfo);
    }

    /**
     * 工单列表导出
     * @param requestDto
     * @param response
     */
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @Override
    public void exportWorkOrderList(WorkOrderListRequestDto requestDto, HttpServletResponse response) {
        Result<List<WorkOrderListResponseModel>> result = workOrderCenterServiceApi.exportWorkOrderList(MapperUtils.mapperNoDefault(requestDto, WorkOrderListRequestModel.class));
        result.throwException();
        List<WorkOrderListResponseDto> mapperResult = MapperUtils.mapper(result.getData(), WorkOrderListResponseDto.class, new WorkOrderListMapping());
        String fileName = "工单中心" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        ExportUtils.exportByYeloExcel(response, mapperResult, WorkOrderListResponseDto.class, fileName);
    }

    /**
     * 工单详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<WorkOrderDetailResponseDto> workOrderDetail(@RequestBody @Valid WorkOrderDetailRequestDto requestDto) {
        Result<WorkOrderDetailResponseModel> result = workOrderCenterServiceApi.workOrderDetail(MapperUtils.mapperNoDefault(requestDto, WorkOrderDetailRequestModel.class));
        result.throwException();
        Map<String, String> picMap = new HashMap<>();
        if (result.getData() != null) {
            picMap = commonBiz.batchGetOSSFileUrl(Collections.singletonList(result.getData().getArriveScenePicture()));
        }
        return Result.success(MapperUtils.mapper(result.getData(), WorkOrderDetailResponseDto.class, new WorkOrderDetailMapping(configKeyConstant, picMap)));
    }

    /**
     * 工单处理过程列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<WorkOrderProcessResponseDto>> workOrderProcess(@RequestBody @Valid WorkOrderDetailRequestDto requestDto) {
        Result<List<WorkOrderProcessResponseModel>> result = workOrderCenterServiceApi.workOrderProcess(MapperUtils.mapperNoDefault(requestDto, WorkOrderDetailRequestModel.class));
        result.throwException();
        List<WorkOrderProcessResponseDto> mapperResult = MapperUtils.mapper(result.getData(), WorkOrderProcessResponseDto.class, new WorkOrderProcessMapping());
        return Result.success(mapperResult);
    }

    /**
     * 上报任务中心异常工单(需求单)
     * @param requestDto
     * @return
     */
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @Override
    public Result<Boolean> reportDemandWorkException(@RequestBody @Valid ReportDemandWorkExceptionRequestDto requestDto) {
        ReportWorkExceptionRequestModel requestModel = MapperUtils.mapper(requestDto, ReportWorkExceptionRequestModel.class);
        requestModel.setOrderId(Long.valueOf(requestDto.getDemandOrderId()));
        requestModel.setReportSource(WorkReportSourceEnum.BACK_STAGE.getKey());
        requestModel.setWorkOrderType(WorkOrderTypeEnum.DEMAND_ORDER_TYPE.getKey());
        return workOrderCenterServiceApi.reportWorkException(requestModel);
    }

    /**
     * 重新上报任务中心异常工单(需求单)
     * @param requestDto
     * @return
     */
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @Override
    public Result<Boolean> reReportDemandWorkException(@RequestBody @Valid ReportDemandWorkExceptionRequestDto requestDto) {
        if (StringUtils.isBlank(requestDto.getWorkOrderId())) {
            throw new BizException(ManagementWebApiExceptionEnum.RE_REPORT_WORK_NOT_EMPTY);
        }
        ReReportWorkExceptionRequestModel requestModel = MapperUtils.mapper(requestDto, ReReportWorkExceptionRequestModel.class);
        requestModel.setOrderId(Long.valueOf(requestDto.getDemandOrderId()));
        requestModel.setReportSource(WorkReportSourceEnum.BACK_STAGE.getKey());
        requestModel.setWorkOrderType(WorkOrderTypeEnum.DEMAND_ORDER_TYPE.getKey());
        return workOrderCenterServiceApi.reReportWorkException(requestModel);
    }

    /**
     * 上报任务中心异常工单(运单)
     * @param requestDto
     * @return
     */
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @Override
    public Result<Boolean> reportCarrierWorkException(@RequestBody @Valid ReportCarrierWorkExceptionRequestDto requestDto) {
        if (CommonConstant.INTEGER_ONE.equals(Integer.valueOf(requestDto.getIsArriveScene()))) {
            if (StringUtils.isBlank(requestDto.getArriveScenePicture())) {
                throw new BizException(ManagementWebApiExceptionEnum.RE_REPORT_WORK_ARRIVE_SCENE_PICTURE_NOT_EMPTY);
            }
        } else {
            requestDto.setArriveScenePicture("");
        }
        ReportWorkExceptionRequestModel requestModel = MapperUtils.mapper(requestDto, ReportWorkExceptionRequestModel.class);
        requestModel.setOrderId(Long.valueOf(requestDto.getCarrierOrderId()));
        requestModel.setReportSource(WorkReportSourceEnum.BACK_STAGE.getKey());
        requestModel.setWorkOrderType(WorkOrderTypeEnum.CARRIER_ORDER_TYPE.getKey());
        return workOrderCenterServiceApi.reportWorkException(requestModel);
    }

    /**
     * 重新上报任务中心异常工单(运单)
     * @param requestDto
     * @return
     */
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @Override
    public Result<Boolean> reReportCarrierWorkException(@RequestBody @Valid ReportCarrierWorkExceptionRequestDto requestDto) {
        if (StringUtils.isBlank(requestDto.getWorkOrderId())) {
            throw new BizException(ManagementWebApiExceptionEnum.RE_REPORT_WORK_NOT_EMPTY);
        }
        if (CommonConstant.INTEGER_ONE.equals(Integer.valueOf(requestDto.getIsArriveScene()))) {
            if (StringUtils.isBlank(requestDto.getArriveScenePicture())) {
                throw new BizException(ManagementWebApiExceptionEnum.RE_REPORT_WORK_ARRIVE_SCENE_PICTURE_NOT_EMPTY);
            }
        } else {
            requestDto.setArriveScenePicture("");
        }
        ReReportWorkExceptionRequestModel requestModel = MapperUtils.mapper(requestDto, ReReportWorkExceptionRequestModel.class);
        requestModel.setOrderId(Long.valueOf(requestDto.getCarrierOrderId()));
        requestModel.setReportSource(WorkReportSourceEnum.BACK_STAGE.getKey());
        requestModel.setWorkOrderType(WorkOrderTypeEnum.CARRIER_ORDER_TYPE.getKey());
        return workOrderCenterServiceApi.reReportWorkException(requestModel);
    }

    /**
     * 撤销工单
     * @param requestDto
     * @return
     */
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @Override
    public Result<Boolean> cancelWorkOrder(@RequestBody @Valid CancelWorkOrderRequestDto requestDto) {
        CancelWorkOrderRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, CancelWorkOrderRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        Result<Boolean> result = workOrderCenterServiceApi.cancelWorkOrder(requestModel);
        result.throwException();
        return result;
    }
}
