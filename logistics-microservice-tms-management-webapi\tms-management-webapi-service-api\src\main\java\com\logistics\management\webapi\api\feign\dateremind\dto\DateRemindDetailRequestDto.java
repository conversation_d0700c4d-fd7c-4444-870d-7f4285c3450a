package com.logistics.management.webapi.api.feign.dateremind.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Author: sj
 * @Date: 2019/5/31 8:56
 */
@Data
public class DateRemindDetailRequestDto implements Serializable{
    @ApiModelProperty("日期提醒ID")
    @NotBlank(message = "日期提醒ID为空")
    private String dateRemindId;
}
