package com.logistics.management.webapi.api.impl.vehicletype.mapper;

import com.logistics.management.webapi.api.feign.vehicletype.dto.VehicleTypeListResponseDto;
import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.management.webapi.base.enums.VehicleCategoryEnum;
import com.logistics.tms.api.feign.vehicletype.model.VehicleTypeListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

public class VehicleTypeListMapping extends MapperMapping<VehicleTypeListResponseModel, VehicleTypeListResponseDto> {
    @Override
    public void configure() {
        VehicleTypeListResponseModel responseModel = this.getSource();
        VehicleTypeListResponseDto dto = this.getDestination();
        if (responseModel != null) {
            dto.setEnabledLabel(EnabledEnum.getEnum(responseModel.getEnabled()).getValue());
            dto.setVehicleCategoryLabel(VehicleCategoryEnum.getEnum(responseModel.getVehicleCategory()).getValue());
            if (responseModel.getLastModifiedTime() != null) {
                dto.setLastModifiedTime(DateUtils.dateToString(responseModel.getLastModifiedTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
            }
        }
    }
}
