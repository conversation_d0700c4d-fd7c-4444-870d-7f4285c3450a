package com.logistics.management.webapi.api.feign.extvehiclesettlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/11/20 13:33
 */
@Data
public class SearchExtVehicleSettlementListResponseDto {
    @ApiModelProperty("外部车辆结算ID")
    private String extVehicleSettlementId="";
    @ApiModelProperty("支付状态：0 未支付，1 已支付")
    private String payStatus="";
    private String payStatusDesc="";
    @ApiModelProperty("运单ID")
    private String carrierOrderId="";
    @ApiModelProperty("运单状态：50000 待签收，60000 已签收，2 已放空")
    private String status="";
    @ApiModelProperty("运单状态：待签收，已签收，已放空")
    private String statusDesc="";
    @ApiModelProperty("运单号")
    private String carrierOrderCode="";
    @ApiModelProperty("需求单ID")
    private String demandOrderId="";
    @ApiModelProperty("需求单号")
    private String demandOrderCode="";
    @ApiModelProperty("车辆机构")
    private String vehicleProperty="外部";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("司机")
    private String driverName="";
    @ApiModelProperty("司机合计费用")
    private String driverTotalFee="";
    @ApiModelProperty("结算数据")
    private String settlementAmount="";
    @ApiModelProperty("实际签收时间")
    private String signTime="";
    @ApiModelProperty("发货地（市）")
    private String loadCityName="";
    @ApiModelProperty("收货地（市）")
    private String unloadCityName="";
    @ApiModelProperty("品名")
    private String goodsName;
    @ApiModelProperty("规格")
    private String goodsSize;
    @ApiModelProperty("调度人")
    private String dispatchUserName="";
    @ApiModelProperty("货主公司名称")
    private String companyEntrustName="";
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName="";
    @ApiModelProperty("运单生成时间")
    private String dispatchTime="";
    @ApiModelProperty("结算合计总费用")
    private String totalFee="";

    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private String demandOrderSource="";
}
