package com.logistics.management.webapi.client.settlestatement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TraditionAssociationCarrierOrderItemModel {

	@ApiModelProperty("对账单明细Id")
	private Long settleStatementItemId;

	@ApiModelProperty("运单id")
	private Long carrierOrderId;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("货主名称")
	private String companyEntrustName;

	@ApiModelProperty("车主类型：1 公司，2 个人")
	private Integer companyCarrierType;

	@ApiModelProperty("车主公司名称")
	private String companyCarrierName;

	@ApiModelProperty("车主联系人姓名")
	private String contactName;

	@ApiModelProperty("车主联系人手机号")
	private String contactPhone;

	@ApiModelProperty("结算数量")
	private BigDecimal settlementAmount;

	@ApiModelProperty("单位：1 件，2 吨，3 件（方），4 块")
	private Integer goodsUnit;

	@ApiModelProperty("委托费用")
	private BigDecimal entrustFreight;
}
