package com.logistics.tms.mapper;

import com.logistics.tms.entity.TCarrierFreightConfigScheme;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2023/06/30
 */
@Mapper
public interface TCarrierFreightConfigSchemeMapper extends BaseMapper<TCarrierFreightConfigScheme> {

    List<TCarrierFreightConfigScheme> selectByCarrierFreightIds(@Param("list") List<Long> carrierFreightIds);

    List<TCarrierFreightConfigScheme> selectConfigSchemeByFreightConfigId(@Param("freightConfigId") Long freightConfigId);

    TCarrierFreightConfigScheme selectByFreightConfigIdAndSchemetype(@Param("freightConfigId") Long freightConfigId, @Param("schemeType") Integer schemeType);

    int insertGeneratedKey(TCarrierFreightConfigScheme param);

    void deleteNotExistBySchemeTypeAndIds(@Param("freightConfigId") Long freightConfigId,
                                          @Param("schemeTypes") List<Integer> schemeTypes,
                                          @Param("ids") List<Long> ids,
                                          @Param("lastModifiedBy") String lastModifiedBy);
}