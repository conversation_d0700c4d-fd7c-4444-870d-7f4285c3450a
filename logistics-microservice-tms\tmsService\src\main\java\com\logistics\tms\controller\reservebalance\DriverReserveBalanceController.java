package com.logistics.tms.controller.reservebalance;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.reservebalance.request.DriverReserveBalanceListRequestModel;
import com.logistics.tms.controller.reservebalance.request.ReserveBalanceDetailRequestModel;
import com.logistics.tms.controller.reservebalance.response.DriverReserveBalanceListResponseModel;
import com.logistics.tms.controller.reservebalance.response.ReserveBalanceDetailResponseModel;
import com.logistics.tms.controller.reservebalance.response.ReserveBalanceInfoResponseModel;
import com.logistics.tms.controller.reservebalance.response.SearchDriverReserveBalanceResponseModel;
import com.logistics.tms.biz.reservebalance.ReserveBalanceBiz;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/service/DriverReserveBalance")
public class DriverReserveBalanceController {

    @Resource
    private ReserveBalanceBiz reserveBalanceBiz;

    /**
     * 后台 - 备用金余额台账搜索
     * @param requestModel 请求Model
     * @return SearchDriverReserveBalanceResponseModel
     */
    @ApiOperation(value = "备用金余额台账搜索", tags = "1.2.5")
    @PostMapping("/searchList")
    public Result<SearchDriverReserveBalanceResponseModel> searchList(@RequestBody DriverReserveBalanceListRequestModel requestModel) {
        return Result.success(reserveBalanceBiz.searchList(requestModel));
    }

    /**
     * 后台 - 导出备用金余额台账
     * @param requestModel 请求Model
     * @return List<DriverReserveBalanceListResponseModel>
     */
    @ApiOperation(value = "备用金余额台账搜索导出", tags = "1.2.5")
    @PostMapping("/searchListExport")
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<DriverReserveBalanceListResponseModel>> searchListExport(@RequestBody DriverReserveBalanceListRequestModel requestModel) {
        return Result.success(reserveBalanceBiz.searchListExport(requestModel));
    }

    /**
     * 后台 - 备用金余额台账明细查询
     * @param requestModel 请求Model
     * @return PageInfo<ReserveBalanceDetailResponseModel>
     */
    @ApiOperation(value = "备用金余额台账明细查询", tags = "1.2.5")
    @PostMapping("/reserveBalanceDetail")
    public Result<PageInfo<ReserveBalanceDetailResponseModel>> reserveBalanceDetail(@RequestBody ReserveBalanceDetailRequestModel requestModel) {
        return Result.success(reserveBalanceBiz.reserveBalanceDetail(requestModel));
    }

    /**
     * 后台 - 备用金明细导出列表
     * @param requestModel 请求Model
     * @return ReserveBalanceDetailExportResponseModel
     */
    @ApiOperation(value = "备用金余额台账明细导出", tags = "1.2.5")
    @PostMapping("/reserveBalanceDetailExport")
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<Map<String, List<ReserveBalanceDetailResponseModel>>> reserveBalanceDetailExport(@RequestBody ReserveBalanceDetailRequestModel requestModel) {
        return Result.success(reserveBalanceBiz.reserveBalanceDetailExport(requestModel));
    }

    /**
     * 小程序 - 备用金余额查询
     * @return 备用金余额
     */
    @ApiOperation(value = "当前备用金信息查询", tags = "1.0.8")
    @PostMapping(value = "/applet/reserveBalanceInfo")
    public Result<ReserveBalanceInfoResponseModel> reserveBalanceInfo() {
        return Result.success(reserveBalanceBiz.reserveBalanceInfo());
    }
}
