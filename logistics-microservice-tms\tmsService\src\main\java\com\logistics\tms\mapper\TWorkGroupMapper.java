package com.logistics.tms.mapper;

import com.logistics.tms.controller.workgroup.request.SearchWorkGroupListRequestModel;
import com.logistics.tms.controller.workgroup.response.SearchWorkGroupListResponseModel;
import com.logistics.tms.entity.TWorkGroup;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* Created by Mybatis Generator on 2023/12/21
*/
@Mapper
public interface TWorkGroupMapper extends BaseMapper<TWorkGroup> {

    TWorkGroup selectByPrimaryKeyDecrypt(Long id);

    int insertSelectiveEncrypt(TWorkGroup tWorkGroup);

    int updateByPrimaryKeySelectiveEncrypt(TWorkGroup tWorkGroup);

    List<SearchWorkGroupListResponseModel> searchList(@Param("params") SearchWorkGroupListRequestModel requestModel);

    int selectCount();

    List<TWorkGroup> selectAllByEntrustTypeGroupDecrypt(int entrustTypeGroup);

    int delWorkGroup(@Param("id") Long id, @Param("userName")String userName, @Param("updateTime") Date updateTime);
}