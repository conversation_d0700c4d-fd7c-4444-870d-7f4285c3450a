package com.logistics.management.webapi.base.enums;

/**
 * 车辆证件导入枚举
 * @Author: sj
 * @Date: 2019/6/11 13:20
 */
public enum VehicleCertificationInfoEnum {
    NULL(-1,""),
    VEHICLE_DRIVING_PERMIT_FRONT(1,"行驶证1"),
    VEHICLE_DRIVING_PERMIT_BACK(2,"行驶证2"),
    ROAD_TRANSPORT_CERTIFICATE_CARD_FRONT(3,"道路运输证卡1"),
    ROAD_TRANSPORT_CERTIFICATE_CARD_BACK(4,"道路运输证卡2"),
    ROAD_TRANSPORT_CERTIFICATE_PAPER_FRONT(5,"道路运输证纸1"),
    ROAD_TRANSPORT_CERTIFICATE_PAPER_BACK(6,"道路运输证纸2"),
    VEHICLE_BASIC_INSPECTION_PHOTOS(7,"车辆检查照片"),
    VEHICLE_BASIC_ACCESS_CHECKLIST(8,"危化品运输车辆准入检查表"),
    VEHICLE_BASIC_REGISTRATION_FRONT(9,"登记证1"),
    VEHICLE_BASIC_REGISTRATION_BACK(10,"登记证2"),
    VEHICLE_BASIC_QUALITY_CERTIFICATE(11,"合格证"),
    VEHICLE_BASIC_PURCHASE_INVOICE(12,"购置发票"),
    VEHICLE_BASIC_PURCHASE_TAX_INVOICE(13,"购置税发票"),
    VEHICLE_BASIC_VESSEL_TAX_INVOICE(14,"车船税发票"),
    ;

    private Integer key;
    private String value;

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    VehicleCertificationInfoEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static VehicleCertificationInfoEnum getEnum(Integer key) {
        for (VehicleCertificationInfoEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return NULL;
    }

    public static VehicleCertificationInfoEnum getEnumByValue(String value) {
        for (VehicleCertificationInfoEnum t : values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return NULL;
    }

}
