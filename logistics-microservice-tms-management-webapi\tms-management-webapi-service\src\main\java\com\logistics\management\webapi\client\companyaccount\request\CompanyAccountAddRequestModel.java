package com.logistics.management.webapi.client.companyaccount.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/22
 */
@Data
public class CompanyAccountAddRequestModel {

	@ApiModelProperty(value = "银行账号,9-30位")
	private String bankAccount;

	@ApiModelProperty(value = "开户银行名称,4-20位")
	private String bankAccountName;

	@ApiModelProperty(value = "开户支行名称, 非必填 填写时检验长度 0<=长度<=20 ")
	private String braBankName;

	@ApiModelProperty(value = "行号,4-30个数字")
	private String bankCode;

	@ApiModelProperty(value = "银行卡图片,1-2张图片")
	private List<String> bankAccountImage;

	@ApiModelProperty(value = "使用范围, 非必填 0-100")
	private String remark;
}
