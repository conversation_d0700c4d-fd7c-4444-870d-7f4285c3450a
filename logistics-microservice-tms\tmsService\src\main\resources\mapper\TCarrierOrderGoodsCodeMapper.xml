<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderGoodsCodeMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierOrderGoodsCode" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="carrier_order_goods_id" property="carrierOrderGoodsId" jdbcType="BIGINT" />
    <result column="yelo_good_code" property="yeloGoodCode" jdbcType="VARCHAR" />
    <result column="load_amount" property="loadAmount" jdbcType="DECIMAL" />
    <result column="unload_amount" property="unloadAmount" jdbcType="DECIMAL" />
    <result column="sign_amount" property="signAmount" jdbcType="DECIMAL" />
    <result column="unit" property="unit" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, carrier_order_goods_id, yelo_good_code, load_amount, unload_amount, sign_amount, 
    unit, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_carrier_order_goods_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_carrier_order_goods_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierOrderGoodsCode" >
    insert into t_carrier_order_goods_code (id, carrier_order_goods_id, yelo_good_code, 
      load_amount, unload_amount, sign_amount, 
      unit, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{carrierOrderGoodsId,jdbcType=BIGINT}, #{yeloGoodCode,jdbcType=VARCHAR}, 
      #{loadAmount,jdbcType=DECIMAL}, #{unloadAmount,jdbcType=DECIMAL}, #{signAmount,jdbcType=DECIMAL}, 
      #{unit,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderGoodsCode" >
    insert into t_carrier_order_goods_code
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="carrierOrderGoodsId != null" >
        carrier_order_goods_id,
      </if>
      <if test="yeloGoodCode != null" >
        yelo_good_code,
      </if>
      <if test="loadAmount != null" >
        load_amount,
      </if>
      <if test="unloadAmount != null" >
        unload_amount,
      </if>
      <if test="signAmount != null" >
        sign_amount,
      </if>
      <if test="unit != null" >
        unit,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderGoodsId != null" >
        #{carrierOrderGoodsId,jdbcType=BIGINT},
      </if>
      <if test="yeloGoodCode != null" >
        #{yeloGoodCode,jdbcType=VARCHAR},
      </if>
      <if test="loadAmount != null" >
        #{loadAmount,jdbcType=DECIMAL},
      </if>
      <if test="unloadAmount != null" >
        #{unloadAmount,jdbcType=DECIMAL},
      </if>
      <if test="signAmount != null" >
        #{signAmount,jdbcType=DECIMAL},
      </if>
      <if test="unit != null" >
        #{unit,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierOrderGoodsCode" >
    update t_carrier_order_goods_code
    <set >
      <if test="carrierOrderGoodsId != null" >
        carrier_order_goods_id = #{carrierOrderGoodsId,jdbcType=BIGINT},
      </if>
      <if test="yeloGoodCode != null" >
        yelo_good_code = #{yeloGoodCode,jdbcType=VARCHAR},
      </if>
      <if test="loadAmount != null" >
        load_amount = #{loadAmount,jdbcType=DECIMAL},
      </if>
      <if test="unloadAmount != null" >
        unload_amount = #{unloadAmount,jdbcType=DECIMAL},
      </if>
      <if test="signAmount != null" >
        sign_amount = #{signAmount,jdbcType=DECIMAL},
      </if>
      <if test="unit != null" >
        unit = #{unit,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierOrderGoodsCode" >
    update t_carrier_order_goods_code
    set carrier_order_goods_id = #{carrierOrderGoodsId,jdbcType=BIGINT},
      yelo_good_code = #{yeloGoodCode,jdbcType=VARCHAR},
      load_amount = #{loadAmount,jdbcType=DECIMAL},
      unload_amount = #{unloadAmount,jdbcType=DECIMAL},
      sign_amount = #{signAmount,jdbcType=DECIMAL},
      unit = #{unit,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>