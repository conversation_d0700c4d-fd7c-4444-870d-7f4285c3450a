package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.controller.carrierorder.response.CarrierOrderOrdersResponseDto;
import com.logistics.management.webapi.base.enums.DemandOrderOrdersEnum;
import com.logistics.management.webapi.client.carrierorder.response.CarrierOrderOrdersResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wei.wang
 * @date: 2021/12/24
 */
public class GetCarrierOrderOrdersMapping extends MapperMapping<CarrierOrderOrdersResponseModel, CarrierOrderOrdersResponseDto> {
	@Override
	public void configure() {
		CarrierOrderOrdersResponseModel model = getSource();
		CarrierOrderOrdersResponseDto dto = getDestination();

		if (model != null){
			dto.setTotalAmount(model.getTotalAmount().stripTrailingZeros().toPlainString());
			dto.setRelType(DemandOrderOrdersEnum.getEnum(model.getRelType()).getValue());
		}
	}
}
