package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TExtVehicleSettlementItem extends BaseEntity {
    /**
    * 外部车辆结算id
    */
    @ApiModelProperty("外部车辆结算id")
    private Long extVehicleSettlementId;

    /**
    * 付款通道
    */
    @ApiModelProperty("付款通道")
    private String paymentChannel;

    /**
    * 付款单号
    */
    @ApiModelProperty("付款单号")
    private String paymentNo;

    /**
    * 应收费用
    */
    @ApiModelProperty("应收费用")
    private BigDecimal paymentFee;

    /**
    * 报销费用
    */
    @ApiModelProperty("报销费用")
    private BigDecimal reimburseFee;

    /**
    * 合计总费用
    */
    @ApiModelProperty("合计总费用")
    private BigDecimal totalFee;

    /**
    * 是否回退：0 否，1 是
    */
    @ApiModelProperty("是否回退：0 否，1 是")
    private Integer ifFallback;

    /**
    * 回退理由
    */
    @ApiModelProperty("回退理由")
    private String fallbackReason;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}