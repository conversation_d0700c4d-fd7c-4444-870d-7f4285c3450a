package com.logistics.tms.api.impl.loanrecord;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.loanrecord.LoanRecordServiceApi;
import com.logistics.tms.api.feign.loanrecord.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.loanrecord.LoanRecordBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: sj
 * @Date: 2019/9/30 10:14
 */
@RestController
public class LoanRecordServiceApiImpl implements LoanRecordServiceApi {

    @Autowired
    private LoanRecordBiz loanRecordBiz;

    /**
     * 查询贷款记录列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<LoanRecordListResponseModel>> searchList(@RequestBody LoanRecordListRequestModel requestModel) {
        return Result.success(loanRecordBiz.searchList(requestModel));
    }

    /**
     * 获取列表汇总数据
     * @param requestModel
     * @return
     */
    @Override
    public Result<SummaryLoanRecordResponseModel> getSummary(@RequestBody LoanRecordListRequestModel requestModel) {
        return Result.success(loanRecordBiz.getSummary(requestModel));
    }

    /**
     * 新增/修改
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> saveOrUpdate(@RequestBody SaveOrUpdateLoanRecordRequestModel requestModel) {
        loanRecordBiz.saveOrUpdate(requestModel);
        return Result.success(true);
    }

    /**
     * 详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<LoanRecordDetailResponseModel> getDetail(@RequestBody LoanRecordDetailRequestModel requestModel) {
        return Result.success(loanRecordBiz.getDetail(requestModel));
    }

    /**
     * 查询操作记录
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<LoanOperationRecordResponseModel>> getOperationRecords(@RequestBody LoanOperationRecordRequestModel requestModel) {
        return Result.success(loanRecordBiz.getOperationRecords(requestModel).getOperationRecordList());
    }

    /**
     * 查询结算记录
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<LoanSettlementRecordResponseModel>> getSettlementRecords(@RequestBody LoanSettlementRecordRequestModel requestModel) {
        return Result.success(loanRecordBiz.getSettlementRecordList(requestModel).getSettlementRecordList());
    }

    /**
     * 导出贷款记录
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<LoanRecordListResponseModel>> exportLoanRecords(@RequestBody LoanRecordListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<LoanRecordListResponseModel> pageInfo = loanRecordBiz.searchList(requestModel);
        return Result.success(Optional.ofNullable(pageInfo.getList()).orElse(new ArrayList<>()));
    }

    /**
     * 导出结算记录
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<ExportSettlementRecordResponseModel> exportSettlementRecords(@RequestBody LoanSettlementRecordRequestModel requestModel) {
        return Result.success(loanRecordBiz.getSettlementRecordList(requestModel));
    }

    /**
     * 导出操作记录
     * @param requestModel
     * @return
     */
    @Override
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<ExportOperationRecordResponseModel> exportOperationRecords(@RequestBody LoanOperationRecordRequestModel requestModel) {
        return Result.success(loanRecordBiz.getOperationRecords(requestModel));
    }
}
