package com.logistics.tms.rabbitmq.consumer.yelolife;


import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.renewableaudit.RenewableAuditBiz;
import com.logistics.tms.rabbitmq.consumer.model.YeloLifeRecycleOrderApplyModel;
import com.logistics.tms.rabbitmq.consumer.model.YeloLifeRecycleOrderApplyResultModel;
import com.logistics.tms.rabbitmq.consumer.model.YeloLifeSyncRecycleOrderModel;
import com.rabbitmq.client.Channel;
import com.yelo.tools.rabbitmq.annocation.EnableMqErrorMessageCollect;
import com.yelo.tools.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 乐橘新生回收单mq
 */
@Component
@Slf4j
public class YeloLifeRecycleOrderConsumer {

    private ObjectMapper objectMapper = JacksonUtils.getInstance();

    @Autowired
    private RenewableAuditBiz renewableAuditBiz;

    @EnableMqErrorMessageCollect
    @RabbitListener(bindings = {@QueueBinding(
            exchange = @Exchange(name = "logistics.qiyatms.topic", type = "topic", durable = "true"),
            value = @Queue(value = "logistics.tmsSyncLifeRecycleOrder", durable = "true"),
            key = "tmsSyncLifeRecycleOrder")}
            , concurrency = "3")
    public void process(@Payload String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {
        log.info("接收新生mq：" + message);
        //此流程关闭，新生直接同步需求单到物流
//        YeloLifeSyncRecycleOrderModel yeloLifeSyncRecycleOrderModel = objectMapper.readValue(message, YeloLifeSyncRecycleOrderModel.class);
//
//        if (yeloLifeSyncRecycleOrderModel != null) {
//            if (CommonConstant.INTEGER_ONE.equals(yeloLifeSyncRecycleOrderModel.getType())) {
//                log.info("接收新生回收单信息mq：" + yeloLifeSyncRecycleOrderModel.getMsgData());
//                renewableAuditBiz.yeloLifeAddRecycleOrder(objectMapper.readValue(objectMapper.writeValueAsString(yeloLifeSyncRecycleOrderModel.getMsgData()), YeloLifeRecycleOrderApplyModel.class));
//            } else if (CommonConstant.INTEGER_TWO.equals(yeloLifeSyncRecycleOrderModel.getType())) {
//                log.info("接收新生回收单审核结果mq：" + yeloLifeSyncRecycleOrderModel.getMsgData());
//                renewableAuditBiz.yeloLifeRecycleOrderApply(objectMapper.readValue(objectMapper.writeValueAsString(yeloLifeSyncRecycleOrderModel.getMsgData()), YeloLifeRecycleOrderApplyResultModel.class));
//            }
//        }
        channel.basicAck(deliveryTag, false);
    }
}
