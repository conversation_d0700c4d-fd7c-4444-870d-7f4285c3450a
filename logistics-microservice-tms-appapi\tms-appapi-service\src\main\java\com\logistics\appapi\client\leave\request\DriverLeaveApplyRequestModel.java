package com.logistics.appapi.client.leave.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class DriverLeaveApplyRequestModel {

    @ApiModelProperty(value = "请假申请ID,重新提交时填写")
    private Long leaveApplyId;

    @ApiModelProperty(value = "请假类型: 1 事假", required = true)
    private Integer leaveType;

    @ApiModelProperty(value = "请假开始时间", required = true)
    private Date leaveStartTime;

    @ApiModelProperty(value = "请假开始时间类型, 1: 上午, 2: 下午", required = true)
    private Integer leaveStartTimeType;

    @ApiModelProperty(value = "请假结束时间", required = true)
    private Date leaveEndTime;

    @ApiModelProperty(value = "请假结束时间, 1: 上午, 2: 下午", required = true)
    private Integer leaveEndTimeType;

    @ApiModelProperty(value = "请假时长", required = true)
    private BigDecimal leaveDuration;

    @ApiModelProperty(value = "请假事由", required = true)
    private String leaveReason;
}
