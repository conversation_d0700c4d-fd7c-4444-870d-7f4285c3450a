package com.logistics.appapi.controller.verificationcode;

import cn.dev33.satoken.annotation.SaIgnore;
import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.base.enums.VerificationCodeSourceTypeEnum;
import com.logistics.appapi.base.utils.FrequentMethodUtils;
import com.logistics.appapi.base.utils.ValidateCodeUtils;
import com.logistics.appapi.client.verificationcode.VerificationCodeClient;
import com.logistics.appapi.client.verificationcode.request.VerificationCodeRequestModel;
import com.logistics.appapi.controller.verificationcode.request.GetVerificationCodeRequestDto;
import com.logistics.appapi.controller.verificationcode.response.GetImageVerificationCodeResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.model.GetImageVerificationCode;
import com.yelo.tools.redis.utils.GenRedisImgLocationUtils;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Random;
import java.util.UUID;

/**
 * @author: wjf
 * @date: 2024/3/7 17:29
 */
@Slf4j
@RestController
@Api(value = "验证码api", tags = "验证码api")
@RequestMapping(value = "/api/verificationCode")
public class VerificationCodeController {

    @Resource
    private VerificationCodeClient verificationCodeClient;
    @Resource
    private RedisUtils redisUtils;
    private Random random = new Random();
    @Resource
    private CommonBiz commonBiz;

    /**
     * 获取验证码
     * @param requestDto
     * @param request
     * @return
     */
    @ApiOperation(value = "获取验证码 v2.45")
    @PostMapping(value = "/getVerificationCode")
    @SaIgnore
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> getVerificationCode(@RequestBody @Valid GetVerificationCodeRequestDto requestDto, HttpServletRequest request) {
        //解密入参
        requestDto.setMobile(commonBiz.decode(requestDto.getMobile()));
        log.info("小程序获取验证码接口解密后的入参："+requestDto);
        //入参校验
        if (!FrequentMethodUtils.checkMobilePhone(requestDto.getMobile())){
            throw new BizException(AppApiExceptionEnum.MOBILE_PHONE_ERROR);
        }

        //1、登录前-所有获取短信验证码的地方都要校验拼图验证码
        //2、登录后-随便填写手机号(系统不存在)获取短信验证码的地方都要校验拼图验证码
        //验证滑块
        commonBiz.validationPuzzlePicture(requestDto.getLocationX(), requestDto.getVerificationId());

        //获取请求ip
        HttpServletRequest request2 = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String ipJoinStr = request2.getHeader("x-forwarded-for");
        if (StringUtils.isBlank(ipJoinStr)) {
            ipJoinStr = request.getRemoteAddr();
        }
        log.info(ipJoinStr);
        String ip = null;
        if(StringUtils.isNotBlank(ipJoinStr)) {
            String[] ips = ipJoinStr.split(",");
            if (ips.length > 0) {
                ip = ips[CommonConstant.INTEGER_ZERO];
            }
        }

        //获取短信验证码
        VerificationCodeRequestModel verifyCodeRequestModel = MapperUtils.mapper(requestDto, VerificationCodeRequestModel.class);
        verifyCodeRequestModel.setSource(VerificationCodeSourceTypeEnum.TMS_DRIVER_APPLET.getKey());//司机小程序
        verifyCodeRequestModel.setIp(ip);
        return verificationCodeClient.getVerifyCode(verifyCodeRequestModel);
    }

    /**
     * 获取滑块验证码
     * @return
     */
    @ApiOperation(value = "获取滑块验证码",tags = "1.0.7")
    @PostMapping(value = "/getImageVerificationCode")
    @SaIgnore
    public Result<GetImageVerificationCodeResponseDto> getImageVerificationCode() {
        GetImageVerificationCode getImageVerificationCode = GenRedisImgLocationUtils.getImageVerificationCode();
        return Result.success(MapperUtils.mapper(getImageVerificationCode, GetImageVerificationCodeResponseDto.class));
    }

    /**
     * 生成图片验证码返回给页面
     * @param request
     * @param response
     * @param uuid
     * @throws IOException
     */
    @SaIgnore
    @ApiOperation(value = "生成图形验证码",tags = "1.0.3")
    @GetMapping(value = "/getPictureVerificationCode")
    public void picValidationCode(HttpServletRequest request, HttpServletResponse response, @RequestParam("uuid") String uuid)  {

        BufferedImage buffImg = new BufferedImage(ValidateCodeUtils.WIDTH, ValidateCodeUtils.HEIGHT, BufferedImage.TYPE_INT_RGB);

        Graphics gd = buffImg.getGraphics();
        // 创建一个随机数生成器类
        // 将图像填充为白色
        gd.setColor(Color.WHITE);
        gd.fillRect(0, 0, ValidateCodeUtils.WIDTH, ValidateCodeUtils.HEIGHT);

        // 创建字体，字体的大小应该根据图片的高度来定。
        Font font = new Font("Times New Roman", Font.BOLD, ValidateCodeUtils.FONTHEIGHT);
        // 设置字体。
        gd.setFont(font);

        // 画边框。
        gd.setColor(Color.BLACK);
        gd.drawRect(0, 0, ValidateCodeUtils.WIDTH - 1, ValidateCodeUtils.HEIGHT - 1);

        // 随机产生40条干扰线，使图象中的认证码不易被其它程序探测到。
        gd.setColor(Color.BLACK);
        for (int i = 0; i < 10; i++) {
            int x = random.nextInt(ValidateCodeUtils.WIDTH);
            int y = random.nextInt(ValidateCodeUtils.HEIGHT);
            int xl = random.nextInt(12);
            int yl = random.nextInt(12);
            gd.drawLine(x, y, x + xl, y + yl);
        }

        // randomCode用于保存随机产生的验证码，以便用户登录后进行验证。
        StringBuilder randomCode = new StringBuilder();
        int red = 0;
        int green = 0;
        int blue = 0;

        // 随机产生codeCount数字的验证码。
        for (int i = 0; i < ValidateCodeUtils.CODECOUNT; i++) {
            // 得到随机产生的验证码数字。
            String code = String.valueOf(ValidateCodeUtils.getCodeSequence()[random.nextInt(36)]);
            // 产生随机的颜色分量来构造颜色值，这样输出的每位数字的颜色值都将不同。
            red = random.nextInt(255);
            green = random.nextInt(255);
            blue = random.nextInt(255);

            // 用随机产生的颜色将验证码绘制到图像中。
            gd.setColor(new Color(red, green, blue));
            gd.drawString(code, (i + 1) * ValidateCodeUtils.XX, ValidateCodeUtils.CODEY);

            // 将产生的四个随机数组合在一起。
            randomCode.append(code);
        }
        // 将四位数字的验证码保存到 redis中。

        if(StringUtils.isBlank(uuid)){
            uuid = UUID.randomUUID().toString();
        }
        redisUtils.set(CommonConstant.TMS_API_PICTURE_VERIFICATION_CODE_PREFIX + "_" + uuid, randomCode.toString().toUpperCase(),180l);
        log.info("图片验证码："+randomCode.toString().toUpperCase());
        // 禁止图像
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        response.setContentType("image/jpeg");

        // 将图像输出到Servlet输出流中。
        ServletOutputStream sos = null;
        try{
            sos = response.getOutputStream();
            ImageIO.write(buffImg, "jpeg", sos);
        } catch (Exception e){
            log.error("生成图形验证码失败！",e);
        } finally {
            try{
                if(sos!=null){
                    sos.close();
                }
            }catch (Exception e){
                log.error("生成图形验证码失败！",e);
            }
        }
    }
}
