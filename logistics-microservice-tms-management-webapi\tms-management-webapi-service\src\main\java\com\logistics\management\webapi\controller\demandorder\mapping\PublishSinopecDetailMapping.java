package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.client.demandorder.response.PublishSinopecResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.PublishSinopecResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wei.wang
 * @date: 2021/12/5
 */
public class PublishSinopecDetailMapping extends MapperMapping<PublishSinopecResponseModel, PublishSinopecResponseDto> {
	@Override
	public void configure() {
		PublishSinopecResponseModel source = getSource();
		PublishSinopecResponseDto target = getDestination();

		//去掉BigDecimal后面无效的0
		target.setGoodsAmount(source.getGoodsAmount().stripTrailingZeros().toPlainString());
	}
}
