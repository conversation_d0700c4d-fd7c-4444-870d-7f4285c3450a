package com.logistics.tms.biz.authentication;

import com.alibaba.fastjson.JSONObject;
import com.logistics.tms.controller.baiscinfo.applet.request.FaceRecognitionRequestModel;
import com.logistics.tms.controller.baiscinfo.applet.response.FaceRecognitionResponseModel;
import com.logistics.tms.controller.baiscinfo.web.request.VerifyPersonThreeElementsRequestModel;
import com.logistics.tms.controller.baiscinfo.web.request.VerifyPersonTwoElementsRequestModel;
import com.logistics.tms.controller.baiscinfo.web.response.VerifyPersonThreeElementsResponseModel;
import com.logistics.tms.controller.baiscinfo.web.response.VerifyPersonTwoElementsResponseModel;
import com.logistics.tms.base.constant.BestSignCommon;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.client.BasicDataClient;
import com.logistics.tms.client.model.GetVerifyCodeRequestModel;
import com.yelo.life.basicdata.api.base.enums.bestsign.UserTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2020/3/16 14:48
 */
@Slf4j
@Service
public class AuthenticationBiz {
    @Autowired
    private BasicDataClient basicDataClient;

    /**
     * 个人三要素验证（一致性验证）
     *
     * @param requestModel
     * @return
     */
    public VerifyPersonThreeElementsResponseModel verifyPersonalThreeElements(VerifyPersonThreeElementsRequestModel requestModel) {
        JSONObject requestBody = new JSONObject();
        requestBody.put(BestSignCommon.NAME, requestModel.getName());
        requestBody.put(BestSignCommon.IDENTITY, requestModel.getIdentity());
        requestBody.put(BestSignCommon.IDENTITY_TYPE, CommonConstant.INTEGER_ZERO);
        requestBody.put(BestSignCommon.MOBILE, requestModel.getMobile());

        VerifyPersonThreeElementsResponseModel responseModel = new VerifyPersonThreeElementsResponseModel();
        try {
            JSONObject responseBody = basicDataClient.personalIdentity3Mobile(requestBody);
            if (responseBody != null) {
                responseModel.setResult(responseBody.getString(BestSignCommon.RESULT));
            }
        } catch (Exception e) {
            log.info("个人三要素校验失败:", e);
            responseModel.setResult(CommonConstant.ZERO);
        }
        return responseModel;
    }

    /**
     * 注册个人/企业用户并申请证书
     *
     * @param requestModel
     */
    public com.logistics.tms.client.model.RegisterUserResponseModel registerUser(com.logistics.tms.client.model.RegisterUserRequestModel requestModel) {
        JSONObject requestBody = new JSONObject();
        requestBody.put(BestSignCommon.ACCOUNT, requestModel.getAccount());
        requestBody.put(BestSignCommon.NAME, requestModel.getName());
        requestBody.put(BestSignCommon.USER_TYPE, requestModel.getUserType());
        requestBody.put(BestSignCommon.APPLY_CERT, requestModel.getApplyCert());
        if (UserTypeEnum.COMPANY_USER.getKey().equals(requestModel.getUserType())) {
            requestBody.put(BestSignCommon.CREDENTIAL, requestModel.getCompanyCredential());
        } else {
            requestBody.put(BestSignCommon.CREDENTIAL, requestModel.getPersonalCredential());
        }

        com.logistics.tms.client.model.RegisterUserResponseModel responseModel = new com.logistics.tms.client.model.RegisterUserResponseModel();
        JSONObject responseBody = basicDataClient.registerUser(requestBody);
        if (responseBody != null) {
            responseModel.setTaskId(responseBody.getString(BestSignCommon.TASK_ID));
        }
        return responseModel;
    }

    /**
     * 获取刷脸认证签名
     *
     * @param requestModel
     * @return
     */
    public FaceRecognitionResponseModel faceRecognition(FaceRecognitionRequestModel requestModel) {
        return basicDataClient.faceRecognition(requestModel);
    }

    /**
     * 人脸识别验证结果
     *
     * @param orderNo
     * @return
     */
    public JSONObject verifyFaceRecognitionResult(String orderNo) {
        JSONObject requestBody = new JSONObject();
        requestBody.put(BestSignCommon.ORDER_NO, orderNo);
        return basicDataClient.verifyFaceRecognitionResult(requestBody);
    }

    /**
     * 个人手机号认证-获取验证码
     *
     * @param requestModel
     */
    public JSONObject getVerifyCode(GetVerifyCodeRequestModel requestModel) {
        JSONObject requestBody = new JSONObject();
        requestBody.put(BestSignCommon.ACCOUNT, requestModel.getAccount());
        requestBody.put(BestSignCommon.NAME, requestModel.getName());
        requestBody.put(BestSignCommon.IDENTITY, requestModel.getIdentity());
        requestBody.put(BestSignCommon.IDENTITY_TYPE, requestModel.getIdentityType());
        requestBody.put(BestSignCommon.MOBILE, requestModel.getMobile());
        return basicDataClient.getVerifyCode(requestBody);
    }

    /**
     * 校验验证码
     *
     * @param vcode               验证码
     * @param personalIdentityKey 用于验证的key
     */
    public JSONObject checkVerifyCode(String vcode, String personalIdentityKey) {
        JSONObject requestBody = new JSONObject();
        requestBody.put(BestSignCommon.V_CODE, vcode);
        requestBody.put(BestSignCommon.PERSONAL_IDENTITY_KEY, personalIdentityKey);
        return basicDataClient.checkVerifyCode(requestBody);
    }

    /**
     * 异步申请状态查询
     *
     * @param account 用户唯一标识：企业类型为companyCode，个人类型为手机号
     * @param taskId  任务单号
     * @return
     */
    public JSONObject asyncApplyCertStatus(String account, String taskId) {
        JSONObject requestBody = new JSONObject();
        requestBody.put(BestSignCommon.ACCOUNT, account);
        requestBody.put(BestSignCommon.TASK_ID, taskId);
        return basicDataClient.asyncApplyCertStatus(requestBody);
    }

    /**
     * 查询证书编号
     *
     * @param account 用户唯一标识：企业类型为companyCode，个人类型为手机号
     * @return
     */
    public JSONObject getCert(String account) {
        JSONObject requestBody = new JSONObject();
        requestBody.put(BestSignCommon.ACCOUNT, account);
        return basicDataClient.getCert(requestBody);
    }

    /**
     * 获取证书详细信息
     *
     * @param account 用户唯一标识：企业类型为companyCode，个人类型为手机号
     * @param certId  证书编号
     * @return
     */
    public JSONObject getCertInfo(String account, String certId) {
        JSONObject requestBody = new JSONObject();
        requestBody.put(BestSignCommon.ACCOUNT, account);
        requestBody.put(BestSignCommon.CERT_ID, certId);
        return basicDataClient.getCertInfo(requestBody);
    }

    /**
     * 个人二要素校验
     *
     * @param requestModel
     * @return
     */
    public VerifyPersonTwoElementsResponseModel verifyPersonTwoElements(VerifyPersonTwoElementsRequestModel requestModel) {
        JSONObject requestBody = new JSONObject();
        requestBody.put(BestSignCommon.NAME, requestModel.getName());
        requestBody.put(BestSignCommon.IDENTITY, requestModel.getIdentity());
        requestBody.put(BestSignCommon.IDENTITY_TYPE, requestModel.getIdentityType());

        VerifyPersonTwoElementsResponseModel responseModel = new VerifyPersonTwoElementsResponseModel();
        try {
            JSONObject responseBody = basicDataClient.verifyPersonTwoElements(requestBody);
            if (responseBody != null) {
                responseModel.setResult(responseBody.getString(BestSignCommon.RESULT));
            }
        } catch (Exception e) {
            log.info("个人二要素校验失败:", e);
            responseModel.setResult(CommonConstant.ZERO);
        }
        return responseModel;
    }
}
