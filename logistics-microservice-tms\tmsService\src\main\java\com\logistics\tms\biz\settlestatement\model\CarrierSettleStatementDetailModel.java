package com.logistics.tms.biz.settlestatement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/20 14:37
 */
@Data
public class CarrierSettleStatementDetailModel {
    @ApiModelProperty("对账单id")
    private Long settleStatementId;

    @ApiModelProperty("对账单状态,-2:已撤销 -1:待提交 0:待业务审核 1:待财务审核 2:已对账 3:已驳回")
    private Integer settleStatementStatus;

    @ApiModelProperty("是否开票：0 否，1 是")
    private Integer ifInvoice;

    @ApiModelProperty("对账单运单集合")
    private List<CarrierSettleStatementOrderDetailModel> settleStatementItemList;
}
