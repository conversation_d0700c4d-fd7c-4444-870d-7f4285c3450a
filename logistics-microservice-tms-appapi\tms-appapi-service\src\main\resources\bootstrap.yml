feign:
  sentinel:
    enabled: true

server:
  shutdown: graceful

spring:
  lifecycle:
    timeout-per-shutdown-phase: 30s
  application:
    name: logistics-tms-appapi
    platform: logistics_tms
  profiles:
    active: ${env}
  cloud:
    nacos:
      password: ${NACOS-PASSWORD:nacos}
      username: ${NACOS-USERNAME:nacos}
      discovery:
        namespace: ${env}
        server-addr: ${NACOS-HOST:${env}.nacos.yelopack.com}:${NACOS-PORT:8848}
        group: ${REGISTRY_GROUP:DEFAULT_GROUP}
      config:
        username: ${spring.cloud.nacos.username}
        password: ${spring.cloud.nacos.password}
        namespace: ${env}
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        group: ${CONFIG_GROUP:LOGISTICS_GROUP}
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  servlet:
    multipart:
      max-request-size: 10MB
      max-file-size: 10MB

management:
  endpoint:
    health:
      sensitive: false
      show-details: always
    shutdown:
      enabled: true #启用shutdown
      sensitive: false #禁用密码验证
    loggers:
      enabled: true #启用loggers
      sensitive: false #禁用密码验证
    service-registry:
      enabled: true
  endpoints:
    web:
      exposure:
        include: shutdown,refresh,health,info,prometheus,service-registry,deregister

auth:
  serviceId: saas-auth-server
  client:
    id: logistics-tms-appapi
    token-header: token
    pub-key:
      path: client/pub.key
  filter:
    excludePathPatterns: /cache,/error

wechat:
  applet:
    url: https://api.weixin.qq.com/sns/jscode2session
    appid: wx9ae70d2ce37ddee2
    secret: 42209ce93e1b63c7c4ae7d43e9d079a5

login:
  privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJDWemnwSFAVWGiYhZ7Lh316VTsUSH7idQc92ydONJl9awttXcMHBlVPMr9NHXBLXYMqFj44J3kSnjJnSGHlWs4BFRCEdjFqOQHLwZXV2nckTu898TosZeU1VdyV88C2guWWTNaFDbyDquFAhKMKxvtx52Y/S3xEca207wu39Om1AgMBAAECgYBnQQ/Z8FuNA/qX7ovrrcs0r/6M9KPOGSE2/Wj792vWNKwYZGpge+SO8rwJ1y7yfDf5JwreZmf1j0lKQ4k9iSWW/H3duloLVKgoE0bb4p4H/RjCal+QSX9b3vgbHhg1G8Uwbxidh63jjZFnnq65IUt23KqpAQcFTfdPDTSSmFNlgQJBANjf3iMbhf2Oz71dCwTew3W/S3VdZAA8KnotAiFxn3z7mIho78Q4jKyvzGSL1s/qI300vuqgqY6YY/W8qnAHu+ECQQCq96sLhYqrr3qk4g510Yj7EKBBC9dk4ayU5yqmSUqh1kSeuEFKy21Juq/zjC0s+vI5GsB4vNOMe19HH+08FIhVAkBplUOuT94BvZ9Bj6SnsFP0ZTc1YkwnGSOM65CX+5sr28k8z+m629UQ1V1qrfHcsx6n/B/eVm118JbpsfpAJGPBAkBrlAhG6bLGMVcE5VGgBUciY+XSyDayveYwk/0cQUX6666mCN7cx4BvQk9w+qV6hjVghVrTHk4nOYJMaOSIR4s5AkEArYHSGSZ7eEG+Z/yRW1ldW/Dburasy29DKF+pPH4eaEu1qsQxfpKCytUMzU7npDvdTNTS/eajF4qGFxQQbBnPKA==
