package com.logistics.management.webapi.api.impl.insuarance;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.controller.uploadfile.response.SrcUrlDto;
import com.logistics.management.webapi.api.feign.insuarance.InsuranceApi;
import com.logistics.management.webapi.api.feign.insuarance.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.insuarance.mapping.InsuranceDetailMapping;
import com.logistics.management.webapi.api.impl.insuarance.mapping.SearchInsuranceListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportExcelInsurance;
import com.logistics.management.webapi.base.constant.ImportExcelHeaders;
import com.logistics.management.webapi.base.enums.CertificationPicturesFileTypeEnum;
import com.logistics.management.webapi.base.enums.InsuranceCoverageEnum;
import com.logistics.management.webapi.base.enums.InsuranceTypeEnum;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.insuarance.InsuranceServiceApi;
import com.logistics.tms.api.feign.insuarance.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisLockUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: wjf
 * @date: 2019/6/4 19:09
 */
@Slf4j
@RestController
public class InsuranceApiImpl implements InsuranceApi {

    @Autowired
    private InsuranceServiceApi insuranceServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private RedisLockUtils redisLockUtils;
    private static final String IMPORT_INSURANCE_PIC = "IMPORT_INSURANCE_PIC";

    /**
     * 保险管理列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchInsuranceListResponseDto>> searchInsuranceList(@RequestBody SearchInsuranceListRequestDto requestDto) {
        Result<PageInfo<SearchInsuranceListResponseModel>> result = insuranceServiceApi.searchInsuranceList(MapperUtils.mapperNoDefault(requestDto, SearchInsuranceListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchInsuranceListResponseDto> dtoList = MapperUtils.mapper(pageInfo.getList(),SearchInsuranceListResponseDto.class,new SearchInsuranceListMapping());
        pageInfo.setList(dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 保险详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<GetInsuranceDetailResponseDto> getInsuranceDetail(@RequestBody @Valid InsuranceIdRequestDto requestDto) {
        Result<GetInsuranceDetailResponseModel> result = insuranceServiceApi.getInsuranceDetail(MapperUtils.mapper(requestDto, InsuranceIdRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (InsuranceTicketsResponseModel ticketsModel : result.getData().getTicketList()) {
            sourceSrcList.add(ticketsModel.getFilePath());
        }
        sourceSrcList.add(result.getData().getRefundPath());
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),GetInsuranceDetailResponseDto.class,new InsuranceDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 新增/修改保险
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result addOrModifyInsurance(@RequestBody @Valid AddOrModifyInsuranceRequestDto requestDto) {
        if(!FrequentMethodUtils.validateVehicleFormat(requestDto.getVehicleNo())){
            throw new BizException(ManagementWebApiExceptionEnum.VEHICLE_NUMBER_IS_ERROR);
        }
        if (requestDto.getInsuranceType().equals(InsuranceCoverageEnum.COMPULSORY_INSURANCE.getKeyStr())){
            if (!FrequentMethodUtils.isNumberOrFloatNumberTwo(requestDto.getPaymentOfVehicleAndVesselTax())){
                throw new BizException(ManagementWebApiExceptionEnum.PAYMENT_OF_VEHICLE_AND_VESSEL_TAX_EMPTY);
            }
        }else if (requestDto.getInsuranceType().equals(InsuranceCoverageEnum.PERSONAL_INSURANCE.getKeyStr())){
            if (StringUtils.isBlank(requestDto.getPersonalAccidentInsuranceId())){
                throw new BizException(ManagementWebApiExceptionEnum.POLICY_NUMBER_EMPTY);
            }
            if (StringUtils.isBlank(requestDto.getPolicyType()) || (!requestDto.getPolicyType().equals(InsuranceTypeEnum.POLICY.getKeyStr()) && !requestDto.getPolicyType().equals(InsuranceTypeEnum.BATCH.getKeyStr()))){
                throw new BizException(ManagementWebApiExceptionEnum.POLICY_TYPE_ERROR);
            }
            if (requestDto.getPolicyType().equals(InsuranceTypeEnum.BATCH.getKeyStr())){
                if (!FrequentMethodUtils.validateBatchNumber(requestDto.getBatchNumber())) {
                    throw new BizException(ManagementWebApiExceptionEnum.BATCH_NUMBER_ERROR);
                }
                if (ConverterUtils.toBigDecimal(requestDto.getPremium()).compareTo(BigDecimal.ZERO) == CommonConstant.INTEGER_ZERO){
                    if (StringUtils.isBlank(requestDto.getRelatedPersonalAccidentInsuranceId())){
                        throw new BizException(ManagementWebApiExceptionEnum.ASSOCIATED_WITHHOLDING_POLICY_EMPTY);
                    }
                    if (StringUtils.isBlank(requestDto.getSinglePremium()) || !FrequentMethodUtils.isNumberOrFloatNumberTwo(requestDto.getSinglePremium()) || ConverterUtils.toBigDecimal(requestDto.getSinglePremium()).compareTo(BigDecimal.ZERO) == CommonConstant.INTEGER_ZERO){
                        throw new BizException(ManagementWebApiExceptionEnum.ASSOCIATED_WITHHOLDING_POLICY_EMPTY);
                    }
                }
            }else{
                requestDto.setBatchNumber("");
            }
            requestDto.setInsuranceCompanyId("0");
            requestDto.setPolicyNumber("");
            requestDto.setPremium("0");
            requestDto.setStartTime("");
            requestDto.setEndTime("");
            requestDto.setBatchNumber("");
            requestDto.setPolicyType("0");
            requestDto.setTicketList(new ArrayList<>());
        }
        if (!requestDto.getInsuranceType().equals(InsuranceCoverageEnum.PERSONAL_INSURANCE.getKeyStr())
                && (ListUtils.isEmpty(requestDto.getTicketList())
                || requestDto.getTicketList().isEmpty()
                || requestDto.getTicketList().size() > 4)) {
            throw new BizException(ManagementWebApiExceptionEnum.INSURANCE_TICKETS_ERROR);
        }
        Result result = insuranceServiceApi.addOrModifyInsurance(MapperUtils.mapperNoDefault(requestDto, AddOrModifyInsuranceRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 作废保险
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result cancelInsurance(@RequestBody @Valid CancelInsuranceRequestDto requestDto) {
        Result result = insuranceServiceApi.cancelInsurance(MapperUtils.mapper(requestDto, CancelInsuranceRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 导出保险
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportInsurance(SearchInsuranceListRequestDto requestDto, HttpServletResponse response) {
        Result<List<SearchInsuranceListResponseModel>> result = insuranceServiceApi.exportInsurance(MapperUtils.mapperNoDefault(requestDto,SearchInsuranceListRequestModel.class));
        result.throwException();
        List<SearchInsuranceListResponseDto> list = MapperUtils.mapper(result.getData(),SearchInsuranceListResponseDto.class,new SearchInsuranceListMapping());
        String fileName = "保险数据" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportExcelInsurance.getExportInsurance();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * 导入保险
     * @param file
     * @param request
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ImportInsuranceResponseDto> importInsurance(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_PERSONAL_ACCIDENT_INSURANCE_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            log.error("导入保险失败，",e);
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_PERSONAL_ACCIDENT_INSURANCE_EMPTY);
        }

        List<List<Object>> listByExcel = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportInsuranceType());
        ImportInsuranceRequestModel model = new ImportInsuranceRequestModel();
        checkRepeatInsurance(listByExcel,model);
        if (ListUtils.isNotEmpty(model.getImportList())) {
            Result<ImportInsuranceResponseModel> result = insuranceServiceApi.importInsurance(model);
            result.throwException();
            return Result.success(MapperUtils.mapper(result.getData(),ImportInsuranceResponseDto.class));
        }
        ImportInsuranceResponseDto responseDto = new ImportInsuranceResponseDto();
        responseDto.setNumberSuccessful(CommonConstant.ZERO);
        if (model.getErrorNumber() != null && model.getErrorNumber() > 0){
            responseDto.setNumberFailures(model.getErrorNumber().toString());
        }else {
            responseDto.setNumberFailures(CommonConstant.ZERO);
        }
        return Result.success(responseDto);
    }
    //校验入参
    public void checkRepeatInsurance(List<List<Object>> listByExcel,ImportInsuranceRequestModel model){
        if (ListUtils.isEmpty(listByExcel)){
            return;
        }
        List<ImportInsuranceListRequestModel> importList = new ArrayList<>();
        ImportInsuranceListRequestModel requestModel;
        Integer errorNumber = 0;
        for (int i = 0; i < listByExcel.size(); i++) {
            List<Object> objects = listByExcel.get(i);
            if (ListUtils.isNotEmpty(objects)) {
                requestModel = new ImportInsuranceListRequestModel();
                String insuranceType = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ZERO));
                if (StringUtils.isBlank(insuranceType) || (!insuranceType.equals(InsuranceCoverageEnum.COMMERCIAL_INSURANCE.getValue()) && !insuranceType.equals(InsuranceCoverageEnum.COMPULSORY_INSURANCE.getValue())
                && !insuranceType.equals(InsuranceCoverageEnum.PERSONAL_INSURANCE.getValue()) && !insuranceType.equals(InsuranceCoverageEnum.CARGO_INSURANCE.getValue()) && !insuranceType.equals(InsuranceCoverageEnum.CARRIER_INSURANCE.getValue()))){
                    errorNumber++;
                    continue;
                }
                if (insuranceType.equals(InsuranceCoverageEnum.COMMERCIAL_INSURANCE.getValue())){
                    requestModel.setInsuranceType(InsuranceCoverageEnum.COMMERCIAL_INSURANCE.getKey());
                }else if (insuranceType.equals(InsuranceCoverageEnum.COMPULSORY_INSURANCE.getValue())){
                    requestModel.setInsuranceType(InsuranceCoverageEnum.COMPULSORY_INSURANCE.getKey());
                }else if (insuranceType.equals(InsuranceCoverageEnum.PERSONAL_INSURANCE.getValue())){
                    requestModel.setInsuranceType(InsuranceCoverageEnum.PERSONAL_INSURANCE.getKey());
                }else if (insuranceType.equals(InsuranceCoverageEnum.CARGO_INSURANCE.getValue())){
                    requestModel.setInsuranceType(InsuranceCoverageEnum.CARGO_INSURANCE.getKey());
                }else if (insuranceType.equals(InsuranceCoverageEnum.CARRIER_INSURANCE.getValue())){
                    requestModel.setInsuranceType(InsuranceCoverageEnum.CARRIER_INSURANCE.getKey());
                }
                String vehicleNo = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ONE));
                if (!FrequentMethodUtils.validateVehicleFormat(vehicleNo)){
                    errorNumber++;
                    continue;
                }
                requestModel.setVehicleNo(vehicleNo);
                String driverName = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_TWO));
                if (StringUtils.isBlank(driverName)){
                    errorNumber++;
                    continue;
                }
                requestModel.setDriverName(driverName);
                String driverPhone = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_THREE));
                if (StringUtils.isBlank(driverPhone) || !FrequentMethodUtils.validateTelFormat(driverPhone)){
                    errorNumber++;
                    continue;
                }
                requestModel.setDriverPhone(driverPhone);
                String insuranceCompanyName = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_FOUR));
                if (StringUtils.isBlank(insuranceCompanyName)  || insuranceCompanyName.length() < 2 || insuranceCompanyName.length() > 50){
                    errorNumber++;
                    continue;
                }
                requestModel.setInsuranceCompanyName(insuranceCompanyName);
                String policyNumber = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_FIVE));
                if (StringUtils.isBlank(policyNumber)){
                    errorNumber++;
                    continue;
                }else{
                    if (policyNumber.contains(".")){
                        String policy = policyNumber.substring(policyNumber.lastIndexOf('.')+1);
                        if (StringUtils.isBlank(policy) || !policy.equals("0")){
                            errorNumber++;
                            continue;
                        }
                        policyNumber = policyNumber.substring(0,policyNumber.lastIndexOf('.'));
                    }
                    if (!FrequentMethodUtils.validateBatchNumber(policyNumber)){
                        errorNumber++;
                        continue;
                    }
                }
                requestModel.setPolicyNumber(policyNumber);
                String batchNumber = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_SIX));
                if (StringUtils.isNotBlank(batchNumber)){
                    if (batchNumber.contains(".")){
                        String batch = batchNumber.substring(batchNumber.lastIndexOf('.')+1);
                        if (StringUtils.isBlank(batch) || !batch.equals("0")){
                            errorNumber++;
                            continue;
                        }
                        batchNumber = batchNumber.substring(0,batchNumber.lastIndexOf('.'));
                    }
                    if (!FrequentMethodUtils.validateBatchNumber(batchNumber)){
                        errorNumber++;
                        continue;
                    }
                }
                requestModel.setBatchNumber(batchNumber);
                String premium = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_SEVEN));
                if (StringUtils.isBlank(premium) || !FrequentMethodUtils.isNumberOrFloatNumberTwo(premium) || ConverterUtils.toBigDecimal(premium).compareTo(BigDecimal.ZERO) < 0 || ConverterUtils.toBigDecimal(premium).compareTo(BigDecimal.ZERO) > 100000){
                    errorNumber++;
                    continue;
                }
                requestModel.setPremium(ConverterUtils.toBigDecimal(premium));
                String startTime = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_EIGHT));
                String endTime = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_NINE));
                if (StringUtils.isBlank(startTime)){
                    errorNumber++;
                    continue;
                }
                if (StringUtils.isBlank(endTime)){
                    errorNumber++;
                    continue;
                }
                try {
                    Date startDate = MapperUtils.mapperNoDefault(startTime,Date.class);
                    Date endDate = MapperUtils.mapperNoDefault(endTime,Date.class);
                    if (startDate==null||endDate==null||startDate.getTime() > endDate.getTime()){
                        errorNumber++;
                        continue;
                    }
                    requestModel.setStartTime(startDate);
                    requestModel.setEndTime(endDate);
                }catch (Exception e){
                    log.info("",e);
                    errorNumber++;
                    continue;
                }
                String paymentOfVehicleAndVesselTax = ConverterUtils.toString(objects.get(CommonConstant.INT_TEN));
                if (insuranceType.equals(InsuranceCoverageEnum.COMPULSORY_INSURANCE.getValue())){
                    if (StringUtils.isBlank(paymentOfVehicleAndVesselTax) || !FrequentMethodUtils.isNumberOrFloatNumberTwo(paymentOfVehicleAndVesselTax) || ConverterUtils.toBigDecimal(paymentOfVehicleAndVesselTax).compareTo(BigDecimal.ZERO) < 0 || ConverterUtils.toBigDecimal(paymentOfVehicleAndVesselTax).compareTo(BigDecimal.ZERO) > 100000){
                        errorNumber++;
                        continue;
                    }
                    requestModel.setPaymentOfVehicleAndVesselTax(ConverterUtils.toBigDecimal(paymentOfVehicleAndVesselTax));
                }else{
                    requestModel.setPaymentOfVehicleAndVesselTax(BigDecimal.ZERO);
                }
                importList.add(requestModel);
            }
        }
        model.setErrorNumber(errorNumber);
        model.setImportList(importList);
    }

    /**
     * 导入保险证件信息
     * @param file
     * @param request
     * @return
     */
    @Override
    public Result importInsuranceCertificateInfo(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        SrcUrlDto srcUrlDto = commonBiz.uploadToTmpCatalog(file);
        ImportInsuranceCertificateRequestModel model = initImportInsuranceCertificate(srcUrlDto);
        if (model == null){
            return Result.fail("");
        }
        String uuid = UUIDGenerateUtil.generateUUID();
        Long startTime = System.currentTimeMillis();
        while (!redisLockUtils.tryLock(IMPORT_INSURANCE_PIC+model.getVehicleNo(),uuid,10)){
            try {
                Thread.sleep(200);
                Long endTime = System.currentTimeMillis();
                if (endTime-startTime > 10000){
                    throw new BizException(ManagementWebApiExceptionEnum.COMMON_IO_EXCEPTION);
                }
            }catch (Exception e){
                log.info(e.getMessage());
            }
        }
        Result result = insuranceServiceApi.importInsuranceCertificateInfo(model);
        redisLockUtils.releaseLock(IMPORT_INSURANCE_PIC+model.getVehicleNo(),uuid);
        result.throwException();
        return Result.success(true);
    }
    //初始化入参
    public ImportInsuranceCertificateRequestModel initImportInsuranceCertificate(SrcUrlDto srcUrlDto){
        if(StringUtils.isBlank(srcUrlDto.getFileName()) || StringUtils.isBlank(srcUrlDto.getRelativePath())){
            return null;
        }
        String fileName = srcUrlDto.getFileName();
        Integer fileType = null;
        String commercialRegex = "([冀豫云辽黑湘皖鲁苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼渝京津沪新军空海北沈兰济南广成使领]{1}[A-Z]{1}[A-Z0-9]{4,5}[a-zA-Z0-9挂学警港澳]{1})+((-商业险1)|(-商业险2)|(-商业险3)|(-商业险4))";
        String compulsoryRegex = "([冀豫云辽黑湘皖鲁苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼渝京津沪新军空海北沈兰济南广成使领]{1}[A-Z]{1}[A-Z0-9]{4,5}[a-zA-Z0-9挂学警港澳]{1})+((-交强险1)|(-交强险2)|(-交强险3)|(-交强险4))";
        String cargoRegex = "([冀豫云辽黑湘皖鲁苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼渝京津沪新军空海北沈兰济南广成使领]{1}[A-Z]{1}[A-Z0-9]{4,5}[a-zA-Z0-9挂学警港澳]{1})+((-货物险1)|(-货物险2)|(-货物险3)|(-货物险4))";
        String carrierRegex = "([冀豫云辽黑湘皖鲁苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼渝京津沪新军空海北沈兰济南广成使领]{1}[A-Z]{1}[A-Z0-9]{4,5}[a-zA-Z0-9挂学警港澳]{1})+((-危货承运人险1)|(-危货承运人险2)|(-危货承运人险3)|(-危货承运人险4))";
        if (FrequentMethodUtils.match(commercialRegex,fileName)){
            fileType = CertificationPicturesFileTypeEnum.COMMERCIAL_INSURANCE.getFileType();
        }else if (FrequentMethodUtils.match(compulsoryRegex,fileName)){
            fileType = CertificationPicturesFileTypeEnum.COMPULSORY_INSURANCE.getFileType();
        }else if (FrequentMethodUtils.match(cargoRegex,fileName)){
            fileType = CertificationPicturesFileTypeEnum.CARGO_INSURANCE.getFileType();
        }else if (FrequentMethodUtils.match(carrierRegex,fileName)){
            fileType = CertificationPicturesFileTypeEnum.CARRIER_INSURANCE.getFileType();
        }
        if(fileType == null){
            return null;
        }
        String vehicleNoRegex = "[冀豫云辽黑湘皖鲁苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼渝京津沪新军空海北沈兰济南广成使领]{1}[A-Z]{1}[A-Z0-9]{4,5}[a-zA-Z0-9挂学警港澳]{1}";
        Pattern pattern = Pattern.compile(vehicleNoRegex);
        Matcher matcher = pattern.matcher(fileName);
        String vehicleNo = "";
        if (!matcher.find()){
            return null;
        }
        vehicleNo = matcher.group();
        String dateRegex = "[\\d]{4}-[\\d]{4}";
        pattern = Pattern.compile(dateRegex);
        matcher = pattern.matcher(fileName);
        String date = "";
        if (!matcher.find()){
            return null;
        }
        date = matcher.group();
        ImportInsuranceCertificateRequestModel model = new ImportInsuranceCertificateRequestModel();
        model.setInsuranceType(fileType);
        model.setFilePath(srcUrlDto.getRelativePath());
        model.setVehicleNo(vehicleNo);
        model.setStartTime(ConverterUtils.toInteger(date.substring(0,4)));
        model.setEndTime(ConverterUtils.toInteger(date.substring(5,9)));
        return model;
    }

    /**
     * 根据车辆id查询保险信息（车辆退保页面）
     * @param requestDto
     * @return
     */
    @Override
    public Result<GetInsuranceInfoByVehicleIdResponseDto> getInsuranceInfoByVehicleId(@RequestBody @Valid GetInsuranceInfoByVehicleIdRequestDto requestDto) {
        Result<GetInsuranceInfoByVehicleIdResponseModel> result = insuranceServiceApi.getInsuranceInfoByVehicleId(MapperUtils.mapper(requestDto,GetInsuranceInfoByVehicleIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),GetInsuranceInfoByVehicleIdResponseDto.class));
    }

    /**
     * 确认退保
     * @param requestDto
     * @return
     */
    @Override
    public Result confirmRefund(@RequestBody @Valid ConfirmRefundRequestDto requestDto) {
        return insuranceServiceApi.confirmRefund(MapperUtils.mapper(requestDto,ConfirmRefundRequestModel.class));
    }
}
