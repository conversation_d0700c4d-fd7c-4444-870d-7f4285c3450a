package com.logistics.tms.controller.staffvehiclerelation.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/7/26 13:53
 */
@Data
public class ImportStaffVehicleResponseModel {
    @ApiModelProperty("失败数量")
    private Integer numberFailures = 0;
    @ApiModelProperty("成功数量")
    private Integer numberSuccessful = 0;

    public void addFailures(){
        this.numberFailures++;
    }

    public void addFailures(Integer number){
        this.numberFailures = numberFailures + number;
    }

    public void addSuccessful(){
        this.numberSuccessful++;
    }

    public void initNumber(Integer initFailNumber){
        if(initFailNumber != null){
            this.numberFailures = this.numberFailures + initFailNumber;
        }
    }
}
