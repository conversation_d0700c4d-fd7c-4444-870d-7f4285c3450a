package com.logistics.management.webapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/12 19:59
 */
@Data
public class VehicleSettlementKanBanResponseDto {
    @ApiModelProperty("年月")
    private String settlementMonth="";
    @ApiModelProperty("月份")
    private String month="";
    @ApiModelProperty("车辆数")
    private String vehicleCount="";
    @ApiModelProperty("待对账数")
    private String waitSettlementCount="";
    @ApiModelProperty("待发送数")
    private String waitSendCount="";
    @ApiModelProperty("待确认数")
    private String waitCommitCount="";
    @ApiModelProperty("待处理数")
    private String waitHandleCount="";
    @ApiModelProperty("待结清数")
    private String waitPaySettlementCount="";
    @ApiModelProperty("部分结清数")
    private String partPaySettlementCount="";
    @ApiModelProperty("已结清数")
    private String payedSettlementCount="";

}
