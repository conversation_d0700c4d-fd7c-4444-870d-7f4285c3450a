package com.logistics.management.webapi.api.impl.vehicleassetmanagement;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.vehicleassetmanagement.VehicleAssetManagementApi;
import com.logistics.management.webapi.api.feign.vehicleassetmanagement.dto.*;
import com.logistics.management.webapi.api.feign.violationregulation.dto.FuzzyQueryVehicleDetailRequestDto;
import com.logistics.management.webapi.api.feign.violationregulation.dto.FuzzyQueryVehicleDetailResponseDto;
import com.logistics.management.webapi.api.feign.violationregulation.dto.FuzzyQueryVehicleInfoRequestDto;
import com.logistics.management.webapi.api.feign.violationregulation.dto.FuzzyQueryVehicleInfoResponseDto;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.vehicleassetmanagement.mapper.*;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportExcelVehicleAssertInfo;
import com.logistics.management.webapi.base.constant.ImportExcelHeaders;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.enums.VehicleCertificationInfoEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.vehicleassetmanagement.VehicleAssetManagementServiceApi;
import com.logistics.tms.api.feign.vehicleassetmanagement.model.*;
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryVehicleInfoRequestModel;
import com.logistics.tms.api.feign.violationregulation.model.FuzzyQueryVehicleInfoResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @date:2019/6/5 9:30
 *
 */

@RestController
@Slf4j
public class VehicleAssetManagementApiImpl implements VehicleAssetManagementApi {

    @Autowired
    private VehicleAssetManagementServiceApi vehicleAssetManagementServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private VehicleAssetCheckMapping vehicleAssetCheckMapping;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 模糊查询车辆详情信息
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<FuzzyQueryVehicleDetailResponseDto>> fuzzyQueryVehicleDetail(@RequestBody  FuzzyQueryVehicleDetailRequestDto requestDto) {
        Result<List<FuzzyQueryVehicleInfoResponseModel>> result = vehicleAssetManagementServiceApi.fuzzyQueryVehicleInfo(MapperUtils.mapper(requestDto,FuzzyQueryVehicleInfoRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),FuzzyQueryVehicleDetailResponseDto.class));
    }

    /**
     * 模糊查询车辆车牌号信息
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<FuzzyQueryVehicleInfoResponseDto>> fuzzyQueryVehicleInfo(@RequestBody FuzzyQueryVehicleInfoRequestDto requestDto) {
        if (CommonConstant.TWO.equals(requestDto.getIsOurCompany()) && StringUtils.isBlank(requestDto.getCompanyCarrierId())) {
            return Result.success(new ArrayList<>());
        }
        Result<List<FuzzyQueryVehicleInfoResponseModel>> result = vehicleAssetManagementServiceApi.fuzzyQueryVehicleInfo(MapperUtils.mapper(requestDto,FuzzyQueryVehicleInfoRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),FuzzyQueryVehicleInfoResponseDto.class));
    }

    /**
     * 获取车辆资源管理列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<VehicleAssetManagementListResponseDto>> searchVehicleBasicList(@RequestBody VehicleAssetManagementListRequestDto requestDto) {
        Result<PageInfo<VehicleAssetManagementListResponseModel>> result=vehicleAssetManagementServiceApi.searchVehicleBasicList(MapperUtils.mapper(requestDto,VehicleAssetManagementListRequestModel.class));
        result.throwException();
        PageInfo pageInfo=result.getData();
        List<VehicleAssetManagementListResponseDto> dtoList = MapperUtils.mapper(pageInfo.getList(),VehicleAssetManagementListResponseDto.class,new SearchVehicleBasicListMapping());
        pageInfo.setList(dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @Override
    public void exportVehicleInfo(VehicleAssetManagementListRequestDto requestDto, HttpServletResponse response) {
        requestDto.setPageNum(CommonConstant.INTEGER_ZERO);
        requestDto.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<List<ExportVehicleBasicInfoResponseModel>> result = vehicleAssetManagementServiceApi.exportVehicleInfo(MapperUtils.mapper(requestDto,VehicleAssetManagementListRequestModel.class));
        result.throwException();
        String fileName="车辆信息管理"+ DateUtils.dateToString(new Date(),CommonConstant.YYYY_MM_DD_HH_MM_SS);

        List<ExportVehicleBasicInfoResponseDto> responseDtoList = MapperUtils.mapper(result.getData(),ExportVehicleBasicInfoResponseDto.class,new ExportVehicleBasicInfoListMapping());
        Map<String ,String> exportTypeMap= ExportExcelVehicleAssertInfo.getVehicleAssetInfo();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return responseDtoList;
            }
        });
    }

    /**
     * 车辆资产详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<VehicleAssetManagementDetailResponseDto> getDetailList(@RequestBody VehicleAssetManagementDetailRequestDto requestDto) {
        Result<VehicleAssetManagementDetailResponseModel> result = vehicleAssetManagementServiceApi.getDetailList(MapperUtils.mapper(requestDto, VehicleAssetManagementDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        //等级评定 附件
        for (VehicleGradeEstimationListResponseModel model : result.getData().getVehicleGradeEstimationList()) {
            if(ListUtils.isNotEmpty(model.getFileList())){
                model.getFileList().forEach(item->sourceSrcList.add(item.getRelativeFilepath()));
            }
        }
        //营运证年审记录 附件
        for (VehicleRoadTransportCertificateListResponseModel model : result.getData().getVehicleRoadTransportCertificateList()) {
            if(ListUtils.isNotEmpty(model.getFileList())){
                model.getFileList().forEach(item->sourceSrcList.add(item.getRelativeFilepath()));
            }
        }
        //GPS终端记录 附件
        for (VehicleGpsRecordListResponseModel model : result.getData().getVehicleGpsRecordList()) {
            if(ListUtils.isNotEmpty(model.getFileList())){
                model.getFileList().forEach(item->sourceSrcList.add(item.getRelativeFilepath()));
            }
        }
        //行驶证年审记录 附件
        for (DrivingLicenseAnnualReviewListResponseModel model : result.getData().getVehicleDrivingLicensePageList()) {
            if(ListUtils.isNotEmpty(model.getFileList())){
                model.getFileList().forEach(item->sourceSrcList.add(item.getRelativeFilepath()));
            }
        }
        if(ListUtils.isNotEmpty(result.getData().getOtherDocumentsRecord())) {
            result.getData().getOtherDocumentsRecord().forEach(item->sourceSrcList.add(item.getRelativeFilepath()));
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),VehicleAssetManagementDetailResponseDto.class,new VehicleAssetManagementDetailMapping(configKeyConstant,imageMap)));
    }

    /**
     * 新增/修改车辆资产信息
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveVehicleInfo(@RequestBody @Valid AddOrModifyVehicleBasicInfoRequestDto requestDto) {
        String checkMessage = vehicleAssetCheckMapping.checkVehicleBasicInfo(requestDto);
        if(StringUtils.isNotBlank(checkMessage)){
            throw new BizException(CommonConstant.NEGATIVE_INTEGER_ONE,checkMessage);
        }
        Result<Boolean> result = vehicleAssetManagementServiceApi.saveVehicleInfo(MapperUtils.mapperNoDefault(requestDto, AddOrModifyVehicleBasicInfoRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 新增外部车辆资产信息
     *
     * @param requestDto
     * @return
     */
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @Override
    public Result<Boolean> saveOrModifyExternalVehicle(@RequestBody @Valid SaveOrModifyExternalVehicleRequestDto requestDto) {
        //校验车牌号
        if (!FrequentMethodUtils.validateVehicleFormat(requestDto.getVehicleNo())) {
            throw new BizException(ManagementWebApiExceptionEnum.VEHICLE_NUMBER_IS_ERROR);
        }
        //校验可装载托盘数
        if (StringUtils.isNotBlank(requestDto.getLoadingCapacity()) && !FrequentMethodUtils.integerNum(requestDto.getLoadingCapacity())) {
            throw new BizException(ManagementWebApiExceptionEnum.LOADING_TRAYS_AMOUNT_LIMIT);
        }
        Result<Boolean> result = vehicleAssetManagementServiceApi.saveOrModifyExternalVehicle(MapperUtils.mapper(requestDto, SaveOrModifyExternalVehicleRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 外部车辆资产详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<ExternalVehicleDetailResponseDto> getExternalVehicle(@RequestBody @Valid VehicleAssetManagementDetailRequestDto requestDto) {
        Result<ExternalVehicleDetailResponseModel> result = vehicleAssetManagementServiceApi.getExternalVehicle(MapperUtils.mapper(requestDto, VehicleAssetManagementDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ExternalVehicleDetailResponseDto.class, new GetExternalVehicleMapping()));
    }

    /**
     * Excel导入车辆资产信息
     * @param file
     * @param request
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ImportVehicleBasicInfoResponseDto> importExcelInfoForVehicleBasicInfo(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_VEHICLE_FILE_IS_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            log.error("导入车辆资产信息失败，",e);
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_VEHICLE_FILE_IS_EMPTY);
        }
        List<List<Object>> excelList = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportVehicleAssetsType());
        ImportVehicleBasicInfoRequestDto requestDto = vehicleAssetCheckMapping.initImportRepeatData(excelList);
        Result<ImportVehicleBasicInfoResponseModel> result = vehicleAssetManagementServiceApi.importVehicleBasicInfo(MapperUtils.mapperNoDefault(requestDto,ImportVehicleBasicInfoRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),ImportVehicleBasicInfoResponseDto.class));
    }

    /**
     * 导入证件信息
     * @param file
     * @param request
     * @return
     */
    @Override
    public Result<Boolean> importVehicleCertificateInfo(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        ImportVehicleCertificateRequestModel requestModel = initVehicleInfo(file);
        if(StringUtils.isNotBlank(requestModel.getVehicleNO())
                && requestModel.getFileType()!=null
                && !requestModel.getFileType().equals(CommonConstant.NEGATIVE_INTEGER_ONE)){
            Result result = vehicleAssetManagementServiceApi.importVehicleCertificateInfo(requestModel);
            result.throwException();
        }
        return Result.success(true);
    }

    /**
     * 资产管理看板
     * @return
     */
    @Override
    public Result<AssetsBoardResponseDto> assetBoard() {
        Result<AssetsBoardResponseModel> assetsBoardResponseModelResult = vehicleAssetManagementServiceApi.assetBoard();
        assetsBoardResponseModelResult.throwException();
        return Result.success(MapperUtils.mapper(assetsBoardResponseModelResult.getData(),AssetsBoardResponseDto.class));
    }

    /**
     * 初始化车辆证件导入信息
     */
    private ImportVehicleCertificateRequestModel initVehicleInfo(MultipartFile file){
        ImportVehicleCertificateRequestModel requestModel = null;
        try {
            String fullPathName = file.getOriginalFilename();
            String fileName = fullPathName.substring(CommonConstant.INTEGER_ONE + fullPathName.lastIndexOf('\\'));
            String cuffixType = fileName.substring(fileName.lastIndexOf('.'));
            String prefixName = fileName.substring(CommonConstant.INTEGER_ZERO,fileName.lastIndexOf('.'));
            InputStream dis = file.getInputStream();
            ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int byteRead = 0;
            while ((byteRead = dis.read(buffer)) != -1) {
                byteStream.write(buffer, 0, byteRead);
            }
            byte[] allBytes = byteStream.toByteArray();
            int fileSize = allBytes.length / 1024;
            if (fileSize > 1024*10) {
                throw new BizException(ManagementWebApiExceptionEnum.UPLOAD_SIZE_TO_LONG);
            }

            requestModel = new ImportVehicleCertificateRequestModel();
            if(StringUtils.isNotBlank(prefixName)){
                String[] fileNameArr = prefixName.split("-");
                if(fileNameArr.length == 2){
                    String vehicleNo = fileNameArr[0];
                    Integer picType = VehicleCertificationInfoEnum.getEnumByValue(fileNameArr[1]).getKey();
                    if(!FrequentMethodUtils.validateVehicleFormat(fileNameArr[0]) || picType < 0){
                        throw new BizException(ManagementWebApiExceptionEnum.FORMAT_ERROR);
                    }
                    requestModel.setVehicleNO(vehicleNo);
                    requestModel.setFileType(picType);
                }else{
                    throw new BizException(ManagementWebApiExceptionEnum.FORMAT_ERROR);
                }
            }

            VehicleCertificateUploadModel uploadModel = new VehicleCertificateUploadModel();
            uploadModel.setFilePath(configKeyConstant.imageUploadCatalog + configKeyConstant.vehicleBasicInfoCatalog);
            String uuid = UUID.randomUUID().toString().replace("-", "");
            uploadModel.setFileName(uuid + cuffixType);
            uploadModel.setFileByte(allBytes);
            uploadModel.setFullPath(configKeyConstant.vehicleBasicInfoCatalog+File.separator+uploadModel.getFileName());
            requestModel.setUploadModel(uploadModel);

        } catch (Exception e) {
            throw new BizException(ManagementWebApiExceptionEnum.COMMON_IO_EXCEPTION);
        }
        return requestModel;
    }

    /**
     * 车辆停运
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> vehicleOutage(@RequestBody @Valid VehicleAssertOutageRequestDto requestDto) {
        Result<Boolean> result = vehicleAssetManagementServiceApi.vehicleOutage(MapperUtils.mapper(requestDto,VehicleAssertOutageRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 车辆停运检测结果
     * @param requestDto
     * @return
     */
    @Override
    public Result<VehicleAssertOutageCheckResponseDto> vehicleOutageResult(@RequestBody @Valid VehicleAssertOutageCheckRequestDto requestDto) {
        Result<VehicleAssertOutageCheckResponseModel> result =  vehicleAssetManagementServiceApi.vehicleOutageResult(MapperUtils.mapper(requestDto,VehicleAssertOutageCheckRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),VehicleAssertOutageCheckResponseDto.class));
    }

    /**
     * 根据车牌号模糊查询车辆司机GPS信息（牵引车和一体车）
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<GetGpsInfoByVehicleNoResponseDto>> getGpsInfoByVehicleNo(@RequestBody VehicleNoRequestDto requestDto) {
        Result<List<GetGpsInfoByVehicleNoResponseModel>> result = vehicleAssetManagementServiceApi.getGpsInfoByVehicleNo(MapperUtils.mapper(requestDto,VehicleNoRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),GetGpsInfoByVehicleNoResponseDto.class,new GetGpsInfoByVehicleNoMapping()));
    }

    /**
     * 车辆恢复运营
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> vehicleRestoration(@RequestBody @Valid VehicleAssertRestorationRequestDto requestDto) {
        vehicleAssetManagementServiceApi.vehicleRestoration(MapperUtils.mapper(requestDto,VehicleAssertRestorationRequestModel.class)).throwException();
        return Result.success(true);
    }

    /**
     * 车辆报废详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<VehicleAssertScrapDetailResponseDto> vehicleScrapDetail(@RequestBody @Valid VehicleAssertScrapDetailRequestDto requestDto) {
        Result<VehicleAssertScrapDetailResponseModel> result = vehicleAssetManagementServiceApi.vehicleScrapDetail(MapperUtils.mapper(requestDto, VehicleAssertScrapDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList = new ArrayList<>();
        if (ListUtils.isNotEmpty(result.getData().getPathList())) {
            sourceSrcList.addAll(result.getData().getPathList());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(), VehicleAssertScrapDetailResponseDto.class, new VehicleScrapDetailMapping(configKeyConstant, imageMap)));
    }

    /**
     * 删除其他车主车辆
     *
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delVehicle(@RequestBody @Valid DelVehicleRequestDto requestDto) {
        vehicleAssetManagementServiceApi.delVehicle(MapperUtils.mapper(requestDto, DeleteVehicleAssetManagementRequest.class)).throwException();
        return Result.success(true);
    }
}
