package com.logistics.management.webapi.client.vehiclesettlement.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.vehiclesettlement.VehicleSettlementClient;
import com.logistics.management.webapi.client.vehiclesettlement.request.*;
import com.logistics.management.webapi.client.vehiclesettlement.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/2/22 9:38
 */
@Component
public class VehicleSettlementClientHystrix implements VehicleSettlementClient {
    @Override
    public Result<PageInfo<SearchVehicleSettlementListResponseModel>> searchVehicleSettlementList(SearchVehicleSettlementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SearchVehicleSettlementListCountResponseModel> searchVehicleSettlementListCount(SearchVehicleSettlementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetVehicleSettlementDetailResponseModel> getVehicleSettlementDetail(VehicleSettlementDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchVehicleSettlementListResponseModel>> exportVehicleSettlement(SearchVehicleSettlementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result confirmSettlement(ConfirmSettlementRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<VehicleSettlementKanBanResponseModel>> vehicleSettlementKanBan(VehicleSettlementKanBanRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result generateVehicleSettlement() {
        return Result.timeout();
    }

    @Override
    public Result<CancelVehicleSettlementDetailResponseModel> cancelVehicleSettlementDetail(CancelVehicleSettlementDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancelVehicleSettlement(CancelVehicleSettlementRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SettleFreightDetailResponseModel> settleFreightDetail(VehicleSettlementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> settleFreight(SettleFreightRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetSettlementDriverResponseModel>> getDriver(GetSettlementDriverRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> confirmSendToDriver(ConfirmSendToDriverRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SettlementStatementRecordResponseModel> settlementStatementRecord(VehicleSettlementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SettlementStatementHandleDetailResponseModel> settlementStatementHandleDetail(VehicleSettlementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> settlementStatementHandle(SettlementStatementHandleRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SendDriverSettleStatementListResponseModel>> sendDriverSettleStatementList(SendDriverSettleStatementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> updateVehicleSettlementTire(UpdateVehicleSettlementTireRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetVehicleTireByVehicleSettlementIdResponseModel> getVehicleTireByVehicleSettlementId(VehicleSettlementIdRequestModel requestModel) {
        return Result.timeout();
    }
}
