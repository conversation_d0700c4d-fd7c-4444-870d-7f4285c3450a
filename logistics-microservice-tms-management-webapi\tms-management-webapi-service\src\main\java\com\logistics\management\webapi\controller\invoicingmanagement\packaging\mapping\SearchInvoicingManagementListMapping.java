package com.logistics.management.webapi.controller.invoicingmanagement.packaging.mapping;

import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.invoicingmanagement.response.SearchInvoicingManagementListResponseModel;
import com.logistics.management.webapi.controller.invoicingmanagement.packaging.response.SearchInvoicingManagementListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2024/3/21 14:38
 */
public class SearchInvoicingManagementListMapping extends MapperMapping<SearchInvoicingManagementListResponseModel, SearchInvoicingManagementListResponseDto> {
    @Override
    public void configure() {
        SearchInvoicingManagementListResponseModel source = getSource();
        SearchInvoicingManagementListResponseDto destination = getDestination();

        //承运商名称
        if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())) {
            //个人车主:姓名+手机号
            destination.setCompanyCarrierName(source.getCarrierContactName() + FrequentMethodUtils.encryptionData(source.getCarrierContactMobile(), EncodeTypeEnum.MOBILE_PHONE));
        } else {
            destination.setCompanyCarrierName(source.getCompanyCarrierName());
        }
    }
}
