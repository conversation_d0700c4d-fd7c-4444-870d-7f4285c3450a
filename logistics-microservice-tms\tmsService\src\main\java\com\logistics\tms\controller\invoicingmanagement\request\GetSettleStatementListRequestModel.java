package com.logistics.tms.controller.invoicingmanagement.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/20 9:17
 */
@Data
public class GetSettleStatementListRequestModel extends AbstractPageForm<GetSettleStatementListRequestModel> {

    @ApiModelProperty(value = "发票管理id")
    private Long invoicingId;

    @ApiModelProperty("业务类型：1 包装业务，2 自营业务")
    private Integer businessType;

}
