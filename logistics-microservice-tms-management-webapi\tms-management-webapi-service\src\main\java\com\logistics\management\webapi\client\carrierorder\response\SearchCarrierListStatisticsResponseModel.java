package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/19
 */
@Data
public class SearchCarrierListStatisticsResponseModel {

	@ApiModelProperty("全部")
	private Integer allCount = 0;

	@ApiModelProperty("待到达提货地")
	private Integer waitArriveLoadSiteCount = 0;

	@ApiModelProperty("待提货")
	private Integer waitLoadCount = 0;

	@ApiModelProperty("待到达卸货地")
	private Integer waitArriveUnloadSiteCount = 0;

	@ApiModelProperty("待卸货")
	private Integer waitUnloadCount = 0;

	@ApiModelProperty("待签收数量")
	private Integer waitSignedAccount = 0;

	@ApiModelProperty("已签收数量")
	private Integer signedAccount = 0;

	@ApiModelProperty("已取消")
	private Integer cancelCount = 0;
}
