package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/10/10 19:46
 */
@Data
public class GetOilFilledByVehicleIdResponseModel {
    @ApiModelProperty(value = "id")
    private Long oilFilledId;
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;
    @ApiModelProperty(value = "司机姓名")
    private String driverName;
    @ApiModelProperty(value = "司机手机号")
    private String driverMobile;
    @ApiModelProperty(value = "费用来源：10 充油，20 退款")
    private Integer source;
    @ApiModelProperty(value = "充油/退款金额")
    private BigDecimal oilFilledFee;
    @ApiModelProperty(value = "充值时间")
    private Date oilFilledDate;
    @ApiModelProperty(value = "充油方式")
    private Integer oilFilledType;
    @ApiModelProperty(value = "升数")
    private Integer liter;
    @ApiModelProperty(value = "充值积分")
    private BigDecimal topUpIntegral;
    @ApiModelProperty(value = "奖励积分")
    private Integer rewardIntegral;
    @ApiModelProperty(value = "副卡卡号")
    private String subCardNumber;
    @ApiModelProperty(value = "副卡所属人")
    private String subCardOwner;
    @ApiModelProperty(value = "合作公司")
    private String cooperationCompany;
    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy;
    @ApiModelProperty(value = "操作时间")
    private Date lastModifiedTime;
    @ApiModelProperty(value = "退款原因类型：0 其他，10 丢失副卡，20 车辆过户，30 车辆报废，40 充油充错")
    private Integer refundReasonType;
}
