<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TPersonalAccidentInsuranceMapper" >
  <select id="searchPersonalAccidentInsuranceIds" resultType="java.lang.Long">
    select DISTINCT
    tpai.id
    from t_personal_accident_insurance tpai
    left join t_insurance_company tic on tic.id = tpai.insurance_company_id and tic.valid = 1
    left join t_insurance ti on (ti.personal_accident_insurance_id = tpai.id or ti.related_personal_accident_insurance_id = tpai.id) and ti.status_type = 0 and ti.valid = 1
    left join t_certification_pictures tcp on tcp.object_id = tpai.id and tcp.object_type = 1 and tcp.file_type = 1 and tcp.valid = 1
    where tpai.valid = 1
    order by tpai.last_modified_time desc,tpai.id desc
  </select>
  <resultMap id="searchPersonalAccidentInsuranceList_Map" type="com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceListResponseModel">
    <id column="personalAccidentInsuranceId" property="personalAccidentInsuranceId" jdbcType="BIGINT"/>
    <result column="insuranceType" property="insuranceType" jdbcType="INTEGER"/>
    <result column="policyNumber" property="policyNumber" jdbcType="VARCHAR"/>
    <result column="batchNumber" property="batchNumber" jdbcType="VARCHAR"/>
    <result column="grossPremium" property="grossPremium" jdbcType="DECIMAL"/>
    <result column="policyPersonCount" property="policyPersonCount" jdbcType="INTEGER"/>
    <result column="startTime" property="startTime" jdbcType="TIMESTAMP"/>
    <result column="endTime" property="endTime" jdbcType="TIMESTAMP"/>
    <result column="lastModifiedBy" property="lastModifiedBy" jdbcType="VARCHAR"/>
    <result column="lastModifiedTime" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
    <result column="insuranceCompany" property="insuranceCompany" jdbcType="VARCHAR"/>
    <collection property="ticketsList" ofType="com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceTicketsResponseModel">
      <id column="ticketId" property="ticketId" jdbcType="BIGINT"/>
      <result column="filePath" property="filePath" jdbcType="VARCHAR"/>
      <result column="fileName" property="fileName" jdbcType="VARCHAR"/>
    </collection>
    <collection property="insuranceList" ofType="com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceRelatedVehicleResponseModel">
      <id column="insuranceId" property="insuranceId" jdbcType="BIGINT"/>
      <result column="vehicleId" property="vehicleId" jdbcType="BIGINT"/>
    </collection>
  </resultMap>
  <select id="searchPersonalAccidentInsuranceList" resultMap="searchPersonalAccidentInsuranceList_Map">
    select
    tpai.id as personalAccidentInsuranceId,
    tpai.type as insuranceType,
    tpai.policy_number as policyNumber,
    tpai.batch_number as batchNumber,
    tpai.gross_premium as grossPremium,
    tpai.policy_person_count as policyPersonCount,
    tpai.start_time as startTime,
    tpai.end_time as endTime,
    tpai.last_modified_by as lastModifiedBy,
    tpai.last_modified_time as lastModifiedTime,
    tic.company_name as insuranceCompany,
    tcp.id as ticketId,
    tcp.file_path as filePath,
    tcp.file_name as fileName,
    ti.id as insuranceId,
    ti.vehicle_id as vehicleId
    from t_personal_accident_insurance tpai
    left join t_insurance_company tic on tic.id = tpai.insurance_company_id and tic.valid = 1
    left join t_insurance ti on (ti.personal_accident_insurance_id = tpai.id or ti.related_personal_accident_insurance_id = tpai.id) and ti.status_type = 0 and ti.valid = 1
    left join t_certification_pictures tcp on tcp.object_id = tpai.id and tcp.object_type = 1 and tcp.file_type = 1 and tcp.valid = 1
    where tpai.valid = 1
    and tpai.id in (${ids})
    order by tpai.last_modified_time desc,tpai.id desc
  </select>

  <resultMap id="getPersonalAccidentInsuranceDetail_Map" type="com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceDetailResponseModel">
    <id column="personalAccidentInsuranceId" property="personalAccidentInsuranceId" jdbcType="BIGINT"/>
    <result column="insuranceCompanyId" property="insuranceCompanyId" jdbcType="BIGINT"/>
    <result column="insuranceCompany" property="insuranceCompany" jdbcType="VARCHAR"/>
    <result column="policyNumber" property="policyNumber" jdbcType="VARCHAR"/>
    <result column="policyNumberInsuranceId" property="policyNumberInsuranceId" jdbcType="BIGINT"/>
    <result column="batchNumber" property="batchNumber" jdbcType="VARCHAR"/>
    <result column="insuranceType" property="insuranceType" jdbcType="INTEGER"/>
    <result column="grossPremium" property="grossPremium" jdbcType="DECIMAL"/>
    <result column="startTime" property="startTime" jdbcType="TIMESTAMP"/>
    <result column="endTime" property="endTime" jdbcType="TIMESTAMP"/>
    <result column="policyPersonCount" property="policyPersonCount" jdbcType="INTEGER"/>
    <collection property="ticketsList" ofType="com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceTicketsResponseModel">
      <id column="ticketId" property="ticketId" jdbcType="BIGINT"/>
      <result column="filePath" property="filePath" jdbcType="VARCHAR"/>
      <result column="fileName" property="fileName" jdbcType="VARCHAR"/>
    </collection>
  </resultMap>
  <select id="getPersonalAccidentInsuranceDetail" resultMap="getPersonalAccidentInsuranceDetail_Map">
    select
    tpai.id as personalAccidentInsuranceId,
    tpai.type as insuranceType,
    tpai.personal_accident_insurance_id as policyNumberInsuranceId,
    tpai.policy_number as policyNumber,
    tpai.batch_number as batchNumber,
    tpai.gross_premium as grossPremium,
    tpai.policy_person_count as policyPersonCount,
    tpai.start_time as startTime,
    tpai.end_time as endTime,
    tic.id as insuranceCompanyId,
    tic.company_name as insuranceCompany,
    tcp.id as ticketId,
    tcp.file_path as filePath,
    tcp.file_name as fileName
    from t_personal_accident_insurance tpai
    left join t_insurance_company tic on tic.id = tpai.insurance_company_id and tic.valid = 1
    left join t_certification_pictures tcp on tcp.object_id = tpai.id and tcp.object_type = 1 and tcp.file_type = 1 and tcp.valid = 1
    where tpai.valid = 1
    and tpai.id = #{id,jdbcType=BIGINT}
    order by tcp.id desc
  </select>
  
  <select id="getByTypePolicyNumber" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_personal_accident_insurance
    where valid = 1
    <if test="type != null">
        and type = #{type,jdbcType=INTEGER}
    </if>
    and policy_number = #{policyNumber,jdbcType=VARCHAR}
    <if test="batchNumber != null and batchNumber != ''">
        and batch_number = #{batchNumber,jdbcType=VARCHAR}
    </if>
  </select>
  
  <resultMap id="getInsuranceByPolicyNumber_Map" type="com.logistics.tms.api.feign.personalaccidentinsurance.model.GetInsuranceByPolicyNumberResponseModel">
    <id column="id" property="personalAccidentInsuranceId" jdbcType="BIGINT"/>
    <result column="insurance_company_id" property="insuranceCompanyId" jdbcType="BIGINT"/>
    <result column="company_name" property="insuranceCompany" jdbcType="VARCHAR"/>
    <result column="policy_number" property="policyNumber" jdbcType="VARCHAR"/>
    <result column="gross_premium" property="grossPremium" jdbcType="DECIMAL"/>
    <result column="policy_person_count" property="policyPersonCount" jdbcType="INTEGER"/>
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
    <collection property="batchNumberList" resultMap="getInsuranceByPolicyNumber_Map_Map"/>
    <collection property="ticketsList" ofType="com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceTicketsResponseModel">
      <id column="ticketId" property="ticketId" jdbcType="BIGINT"/>
      <result column="filePath" property="filePath" jdbcType="VARCHAR"/>
      <result column="fileName" property="fileName" jdbcType="VARCHAR"/>
    </collection>
  </resultMap>
  <resultMap id="getInsuranceByPolicyNumber_Map_Map" type="com.logistics.tms.api.feign.personalaccidentinsurance.model.GetInsuranceByBatchNumberResponseModel">
    <id column="personalAccidentInsuranceId" property="personalAccidentInsuranceId" jdbcType="BIGINT"/>
    <result column="batch_number" property="batchNumber" jdbcType="VARCHAR"/>
    <result column="grossPremium" property="grossPremium" jdbcType="DECIMAL"/>
    <result column="policyPersonCount" property="policyPersonCount" jdbcType="INTEGER"/>
    <result column="startTime" property="startTime" jdbcType="TIMESTAMP"/>
    <result column="endTime" property="endTime" jdbcType="TIMESTAMP"/>
    <result column="companyName" property="insuranceCompany" jdbcType="VARCHAR"/>
    <collection property="ticketsList" ofType="com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceTicketsResponseModel">
      <id column="batchTicketId" property="ticketId" jdbcType="BIGINT"/>
      <result column="batchFilePath" property="filePath" jdbcType="VARCHAR"/>
      <result column="batchFileName" property="fileName" jdbcType="VARCHAR"/>
    </collection>
  </resultMap>
  <select id="getInsuranceByPolicyNumber" resultMap="getInsuranceByPolicyNumber_Map">
    select
    tpai.id,
    tpai.insurance_company_id,
    tic.company_name,
    tpai.policy_number,
    tpai.gross_premium,
    tpai.policy_person_count,
    tpai.start_time,
    tpai.end_time,
    tpa.id as personalAccidentInsuranceId,
    tpa.batch_number,
    tpa.gross_premium as grossPremium,
    tpa.policy_person_count as policyPersonCount,
    tpa.start_time as startTime,
    tpa.end_time as endTime,
    ticp.company_name as companyName,
    tcp.id as ticketId,
    tcp.file_path as filePath,
    tcp.file_name as fileName,
    tcp1.id as batchTicketId,
    tcp1.file_path as batchFilePath,
    tcp1.file_name as batchFileName
    from t_personal_accident_insurance tpai
    left join t_insurance_company tic on tic.id = tpai.insurance_company_id and tic.valid = 1
    left join t_personal_accident_insurance tpa on tpa.personal_accident_insurance_id = tpai.id and tpa.type = 2 and tpa.valid = 1
    and tpa.id not in (
        select tp.id from t_personal_accident_insurance tp
        left join t_insurance ti on (ti.personal_accident_insurance_id = tp.id or ti.related_personal_accident_insurance_id = tp.id) and ti.status_type = 0 and ti.valid = 1
        where tp.valid = 1 and tp.type = 2
        group by tp.id,tp.policy_person_count having tp.policy_person_count = count(ti.id)
    )
    left join t_insurance_company ticp on ticp.id = tpa.insurance_company_id and ticp.valid = 1
    left join t_certification_pictures tcp on tcp.object_id = tpai.id and tcp.object_type = 1 and tcp.file_type = 1 and tcp.valid = 1
    left join t_certification_pictures tcp1 on tcp1.object_id = tpa.id and tcp1.object_type = 1 and tcp1.file_type = 1 and tcp1.valid = 1
    where tpai.valid = 1
    and tpai.type = 1
    <if test="type != null">
      <choose>
        <when test="type == 1">
          and tpai.id not in (
              select tp.id from t_personal_accident_insurance tp
              left join t_insurance ti on (ti.personal_accident_insurance_id = tp.id or ti.related_personal_accident_insurance_id = tp.id) and ti.status_type = 0 and ti.valid = 1
              where tp.valid = 1 and tp.type = 1
              group by tp.id,tp.policy_person_count having tp.policy_person_count = count(ti.id)
          )
        </when>
        <otherwise>
          and (tpai.id not in (
              select tp.id from t_personal_accident_insurance tp
              left join t_insurance ti on (ti.personal_accident_insurance_id = tp.id or ti.related_personal_accident_insurance_id = tp.id) and ti.status_type = 0 and ti.valid = 1
              where tp.valid = 1 and tp.type = 1
              group by tp.id,tp.policy_person_count having tp.policy_person_count = count(ti.id)
          )
          or if(tpa.id is not null,tpa.id not in (
              select tp.id from t_personal_accident_insurance tp
              left join t_insurance ti on (ti.personal_accident_insurance_id = tp.id or ti.related_personal_accident_insurance_id = tp.id) and ti.status_type = 0 and ti.valid = 1
              where tp.valid = 1 and tp.type = 2
              group by tp.id,tp.policy_person_count having tp.policy_person_count = count(ti.id)
          ),1))
        </otherwise>
      </choose>
    </if>
    <if test="policyNumber != null and policyNumber != ''">
      and instr(tpai.policy_number,#{policyNumber,jdbcType=VARCHAR})
    </if>
  </select>
  
  <select id="searchInsuranceByPolicyNumber" resultType="com.logistics.tms.api.feign.personalaccidentinsurance.model.SearchInsuranceByPolicyNumberResponseModel">
    select
    tpai.id as personalAccidentInsuranceId,
    tpai.policy_number as policyNumber,
    tic.id as insuranceCompanyId,
    tic.company_name as insuranceCompany
    from t_personal_accident_insurance tpai
    left join t_insurance_company tic on tic.id = tpai.insurance_company_id and tic.valid = 1
    where tpai.valid = 1
    and tpai.type = 1
    and instr(tpai.policy_number,#{policyNumber,jdbcType=VARCHAR})
  </select>

  <resultMap id="getPolicyNoPremiumByPolicyNo_Map" type="com.logistics.tms.api.feign.personalaccidentinsurance.model.GetPolicyNoPremiumByPolicyNoResponseModel">
    <id column="personalAccidentInsuranceId" property="personalAccidentInsuranceId" jdbcType="BIGINT"/>
    <result column="policyNumber" property="policyNumber" jdbcType="VARCHAR"/>
    <result column="batchNumber" property="batchNumber" jdbcType="VARCHAR"/>
    <result column="grossPremium" property="grossPremium" jdbcType="DECIMAL"/>
    <result column="policyPersonCount" property="policyPersonCount" jdbcType="INTEGER"/>
    <collection property="ticketsList" ofType="com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceTicketsResponseModel">
      <id column="ticketId" property="ticketId" jdbcType="BIGINT"/>
      <result column="filePath" property="filePath" jdbcType="VARCHAR"/>
      <result column="fileName" property="fileName" jdbcType="VARCHAR"/>
    </collection>
  </resultMap>
  <select id="getPolicyNoPremiumByPolicyNo" resultMap="getPolicyNoPremiumByPolicyNo_Map">
    select
    tpai.id as personalAccidentInsuranceId,
    tpai.policy_number as policyNumber,
    tpai.batch_number as batchNumber,
    tpai.gross_premium as grossPremium,
    tpai.policy_person_count as policyPersonCount,
    tcp.id as ticketId,
    tcp.file_path as filePath,
    tcp.file_name as fileName
    from t_personal_accident_insurance tpai
    left join t_certification_pictures tcp on tcp.object_id = tpai.id and tcp.object_type = 1 and tcp.file_type = 1 and tcp.valid = 1
    where tpai.valid = 1
    and tpai.gross_premium > 0
    and tpai.id not in (
        select tp.id from t_personal_accident_insurance tp
        left join t_insurance ti on (ti.personal_accident_insurance_id = tp.id or ti.related_personal_accident_insurance_id = tp.id) and ti.status_type = 0 and ti.valid = 1
        where tp.valid = 1
        group by tp.id,tp.policy_person_count having tp.policy_person_count = count(ti.id)
    )
    and (instr(tpai.policy_number,#{policyNumber,jdbcType=VARCHAR}) or instr(tpai.batch_number,#{policyNumber,jdbcType=VARCHAR}))
  </select>

  <select id="getPersonInsurancePersonCountById" resultType="com.logistics.tms.api.feign.personalaccidentinsurance.model.GetPersonInsurancePersonCountByIdResponseModel">
    select
    tpai.id as personalAccidentInsuranceId,
    tpai.policy_number as policyNumber,
    tpai.batch_number as batchNumber,
    tpai.policy_person_count as policyPersonCount,
    count(ti.id) as associatedCount
    from t_personal_accident_insurance tpai
    left join t_insurance ti on (ti.personal_accident_insurance_id = tpai.id or ti.related_personal_accident_insurance_id = tpai.id) and ti.status_type = 0 and ti.valid = 1
    where tpai.valid = 1
    and tpai.id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getBatchInsuranceById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_personal_accident_insurance
    where valid = 1 and type = 2
    and personal_accident_insurance_id = #{id,jdbcType=BIGINT}
  </select>

  <update id="batchUpdate" parameterType="com.logistics.tms.entity.TPersonalAccidentInsurance">
    <foreach collection="list" item="item" separator=";">
      update t_personal_accident_insurance
      <set>
        <if test="item.insuranceCompanyId != null">
          insurance_company_id = #{item.insuranceCompanyId,jdbcType=BIGINT},
        </if>
        <if test="item.personalAccidentInsuranceId != null">
          personal_accident_insurance_id = #{item.personalAccidentInsuranceId,jdbcType=BIGINT},
        </if>
        <if test="item.type != null">
          type = #{item.type,jdbcType=INTEGER},
        </if>
        <if test="item.policyNumber != null">
          policy_number = #{item.policyNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.batchNumber != null">
          batch_number = #{item.batchNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.grossPremium != null">
          gross_premium = #{item.grossPremium,jdbcType=DECIMAL},
        </if>
        <if test="item.policyPersonCount != null">
          policy_person_count = #{item.policyPersonCount,jdbcType=INTEGER},
        </if>
        <if test="item.startTime != null">
          start_time = #{item.startTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.endTime != null">
          end_time = #{item.endTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.addUserId != null">
          add_user_id = #{item.addUserId,jdbcType=BIGINT},
        </if>
        <if test="item.addUserName != null">
          add_user_name = #{item.addUserName,jdbcType=VARCHAR},
        </if>
        <if test="item.source != null">
          source = #{item.source,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>