package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2024/07/09
*/
@Data
public class TRouteEnquiryAddressQuote extends BaseEntity {

    /**
    * 路线询价单地址表id
    */
    @ApiModelProperty("路线询价单地址表id")
    private Long routeEnquiryAddressId;

    /**
    * 路线询价单车主表id
    */
    @ApiModelProperty("路线询价单车主表id")
    private Long routeEnquiryCompanyId;

    /**
    * 距离（KM）
    */
    @ApiModelProperty("距离（KM）")
    private BigDecimal distance;

    /**
    * 报价金额类型：1 单价，2 一口价
    */
    @ApiModelProperty("报价金额类型：1 单价，2 一口价")
    private Integer quotePriceType;

    /**
    * 报价金额
    */
    @ApiModelProperty("报价金额")
    private BigDecimal quotePrice;

    /**
    * 报价备注
    */
    @ApiModelProperty("报价备注")
    private String quoteRemark;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}