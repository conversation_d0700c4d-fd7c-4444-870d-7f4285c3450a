package com.logistics.management.webapi.api.feign.entrustaddress.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/5/15 19:46
 */
@Data
public class GetAddressByCompanyNameOrWarehouseResponseDto {
    @ApiModelProperty("购货公司名")
    private String companyName="";
    @ApiModelProperty("发货仓")
    private String warehouse="";
    @ApiModelProperty("地址id")
    private String entrustAddressId="";
    @ApiModelProperty("省id")
    private String provinceId="";
    @ApiModelProperty("省名")
    private String provinceName="";
    @ApiModelProperty("市id")
    private String cityId="";
    @ApiModelProperty("市名")
    private String cityName="";
    @ApiModelProperty("区id")
    private String areaId="";
    @ApiModelProperty("区名")
    private String areaName="";
    @ApiModelProperty("省市区详细地址")
    private String detailAddress="";
    @ApiModelProperty("发货人姓名")
    private String contactName="";
    @ApiModelProperty("发货人手机号")
    private String contactMobile="";
    @ApiModelProperty("地址类型：1 发货，2 收货")
    private String addressType = "";
}
