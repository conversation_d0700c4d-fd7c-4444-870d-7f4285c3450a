<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderOrderRelMapper" >


  <insert id="batchInsertDemandOrderOrderRelSelective">
  <foreach collection="list" separator=";" item="item">
    insert into t_demand_order_order_rel
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="item.id != null" >
        id,
      </if>
      <if test="item.demandOrderId != null" >
        demand_order_id,
      </if>
      <if test="item.orderId != null" >
        order_id,
      </if>
      <if test="item.orderCode != null" >
        order_code,
      </if>
      <if test="item.totalAmount != null" >
        total_amount,
      </if>
      <if test="item.arrangedAmount != null" >
        arranged_amount,
      </if>
      <if test="item.backAmount != null">
        back_amount,
      </if>
      <if test="item.relType != null" >
        rel_type,
      </if>
      <if test="item.remark != null" >
        remark,
      </if>
      <if test="item.createdBy != null" >
        created_by,
      </if>
      <if test="item.createdTime != null" >
        created_time,
      </if>
      <if test="item.lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="item.lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="item.valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="item.id != null" >
        #{item.id,jdbcType=BIGINT},
      </if>
      <if test="item.demandOrderId != null" >
        #{item.demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="item.orderId != null" >
        #{item.orderId,jdbcType=BIGINT},
      </if>
      <if test="item.orderCode != null" >
        #{item.orderCode,jdbcType=VARCHAR},
      </if>
      <if test="item.totalAmount != null" >
        #{item.totalAmount,jdbcType=INTEGER},
      </if>
      <if test="item.arrangedAmount != null" >
        #{item.arrangedAmount,jdbcType=INTEGER},
      </if>
      <if test="item.backAmount != null">
        #{item.backAmount,jdbcType=INTEGER},
      </if>
      <if test="item.relType != null" >
        #{item.relType,jdbcType=INTEGER},
      </if>
      <if test="item.remark != null" >
        #{item.remark,jdbcType=VARCHAR},
      </if>
      <if test="item.createdBy != null" >
        #{item.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="item.createdTime != null" >
        #{item.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="item.lastModifiedBy != null" >
        #{item.lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="item.lastModifiedTime != null" >
        #{item.lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="item.valid != null" >
        #{item.valid,jdbcType=INTEGER},
      </if>
    </trim>
  </foreach>
  </insert>

  <select id="getByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_demand_order_order_rel
    where valid = 1
    and id in (${ids})
  </select>

  <update id="batchUpdateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDemandOrderOrderRel" >
    <foreach collection="list" separator=";" item="item">
      update t_demand_order_order_rel
      <set >
        <if test="item.demandOrderId != null" >
          demand_order_id = #{item.demandOrderId,jdbcType=BIGINT},
        </if>
        <if test="item.orderId != null" >
          order_id = #{item.orderId,jdbcType=BIGINT},
        </if>
        <if test="item.orderCode != null" >
          order_code = #{item.orderCode,jdbcType=VARCHAR},
        </if>
        <if test="item.totalAmount != null" >
          total_amount = #{item.totalAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.arrangedAmount != null" >
          arranged_amount = #{item.arrangedAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.backAmount != null">
          back_amount = #{item.backAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.relType != null" >
          rel_type = #{item.relType,jdbcType=INTEGER},
        </if>
        <if test="item.remark != null" >
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="getDemandOrderOrderRelByDemandIds"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_demand_order_order_rel
    where valid = 1
    and demand_order_id in (${demandOrderIds})
  </select>

  <select id="getInvalidByDemandOrderId"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_demand_order_order_rel
    where demand_order_id = #{demandOrderId,jdbcType=BIGINT}
  </select>

  <select id="getDemandOrderOrders" resultType="com.logistics.tms.controller.demandorder.response.DemandOrderOrderRelResponseModel">
    select
    order_code   as orderCode,
    total_amount as totalAmount,
    rel_type     as relType,
    remark       as remark,
    created_by   as createdBy,
    created_time as createdTime
    from t_demand_order_order_rel
    where valid = 1
      and demand_order_id = #{demandId}
    order by created_time desc, id desc
  </select>
</mapper>