package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TInsuranceCostsRelation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TInsuranceCostsRelationMapper extends BaseMapper<TInsuranceCostsRelation> {

    List<TInsuranceCostsRelation> getByInsuranceCostsId(@Param("insuranceCostsId")Long insuranceCostsId);

    int batchInsert(@Param("list")List<TInsuranceCostsRelation> list);

    List<TInsuranceCostsRelation> getByInsuranceId(@Param("insuranceId")Long insuranceId);

    int batchUpdateValidByInsuranceCostsIds(@Param("insuranceCostsIds")String insuranceCostsIds);

    List<TInsuranceCostsRelation> getWaitSettlementByInsuranceIds(@Param("insuranceIds")String insuranceIds);

    List<Long> getByInsuranceCostsIds(@Param("insuranceCostIds") String listToString);
}