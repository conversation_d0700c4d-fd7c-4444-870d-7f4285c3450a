package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/10/28 10:37
 */
@Data
public class LogisticsCostStatisticsResponseModel {
    @ApiModelProperty("统计年月")
    private String yearMonth;
    @ApiModelProperty("发货类型：元/件（签收运单：司机费用/运单签收数量）")
    private BigDecimal deliverCount = BigDecimal.ZERO;
    @ApiModelProperty("回收类型：元/件（签收运单：司机费用/运单签收数量）")
    private BigDecimal recycleCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨类型：元/件（签收运单：司机费用/运单签收数量）")
    private BigDecimal transfersCount = BigDecimal.ZERO;
}
