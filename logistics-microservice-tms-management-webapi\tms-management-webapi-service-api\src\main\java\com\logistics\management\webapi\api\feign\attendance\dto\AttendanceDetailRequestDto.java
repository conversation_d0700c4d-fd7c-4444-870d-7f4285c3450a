package com.logistics.management.webapi.api.feign.attendance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 考勤打卡详情查询请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
public class AttendanceDetailRequestDto {

	@ApiModelProperty(value = "考勤打卡ID", required = true)
	@NotBlank(message = "请选择要查看的记录")
	private String attendanceRecordId;
}
