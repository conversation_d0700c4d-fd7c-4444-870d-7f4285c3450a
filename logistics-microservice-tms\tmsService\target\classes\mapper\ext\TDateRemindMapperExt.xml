<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDateRemindMapper">

  <select id="searchDateRemindList" resultType="com.logistics.tms.api.feign.dateremind.model.DateRemindListResponseModel">
      select
         id as dateRemindId,
         date_name as dateName,
         if_remind as ifRemind,
         remind_days as remindDays,
         remark as remark,
         add_user_id as addUserId,
         add_user_name as addUserName,
         source as source,
         created_by as createdBy,
         created_time as createdTime,
         last_modified_by as lastModifiedBy,
         last_modified_time as lastModifiedTime,
         valid as valid
      from t_date_remind tqdr
      where tqdr.valid = 1
      order by tqdr.last_modified_time desc
  </select>

  <select id="getDateRemindByName" resultMap="BaseResultMap">
     select
        <include refid="Base_Column_List"/>
     from t_date_remind
     where valid = 1
     and date_name = #{dateRemindName,jdbcType = VARCHAR}
  </select>

  <select id="getDateRemindByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_date_remind
    where valid = 1
    and id in (${ids})
  </select>

  <update id="batchModifyDateRemind" >
    <foreach collection="list" item="item" index="index" separator=";">
      update t_date_remind
      <set>
        <if test="item.dateName != null">
          date_name = #{item.dateName,jdbcType=VARCHAR},
        </if>
        <if test="item.ifRemind != null">
          if_remind = #{item.ifRemind,jdbcType=INTEGER},
        </if>
          remind_days = #{item.remindDays,jdbcType=INTEGER},
        <if test="item.remark != null">
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.addUserId != null">
          add_user_id = #{item.addUserId,jdbcType=BIGINT},
        </if>
        <if test="item.addUserName != null">
          add_user_name = #{item.addUserName,jdbcType=VARCHAR},
        </if>
        <if test="item.source != null">
          source = #{item.source,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>


    <select id="getAllNeedRemindDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from t_date_remind where valid = 1 and if_remind = 1
    </select>

    <update id="updateByPrimaryKeySelectiveExt" parameterType="com.logistics.tms.entity.TDateRemind">
        update t_date_remind
        <set>
            <if test="params.dateName != null">
                date_name = #{params.dateName,jdbcType=VARCHAR},
            </if>
            <if test="params.ifRemind != null">
                if_remind = #{params.ifRemind,jdbcType=INTEGER},
            </if>
                remind_days = #{params.remindDays,jdbcType=INTEGER},
            <if test="params.remark != null">
                remark = #{params.remark,jdbcType=VARCHAR},
            </if>
            <if test="params.addUserId != null">
                add_user_id = #{params.addUserId,jdbcType=BIGINT},
            </if>
            <if test="params.addUserName != null">
                add_user_name = #{params.addUserName,jdbcType=VARCHAR},
            </if>
            <if test="params.source != null">
                source = #{params.source,jdbcType=INTEGER},
            </if>
            <if test="params.createdBy != null">
                created_by = #{params.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="params.createdTime != null">
                created_time = #{params.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="params.lastModifiedBy != null">
                last_modified_by = #{params.lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="params.lastModifiedTime != null">
                last_modified_time = #{params.lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="params.valid != null">
                valid = #{params.valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{params.id,jdbcType=BIGINT}
    </update>
</mapper>