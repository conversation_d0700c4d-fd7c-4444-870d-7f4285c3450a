package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierOrderListBeforeSignUpResponseDto {
    @ApiModelProperty("运单ID")
    private String carrierOrderId ="";
    @ApiModelProperty("运单号")
    private String carrierOrderCode="";
    @ApiModelProperty("货物名")
    private String goodsName="";
    @ApiModelProperty("数量")
    private String amount="";
    @ApiModelProperty("体积")
    private String capacity="";
    @ApiModelProperty("司机运费合计")
    private String dispatchFreightFee="";
    @ApiModelProperty("委托费用合计")
    private String entrustFreightFee="";
    @ApiModelProperty("是否乐医：0 否，1 是")
    private String ifLeYi="0";
}
