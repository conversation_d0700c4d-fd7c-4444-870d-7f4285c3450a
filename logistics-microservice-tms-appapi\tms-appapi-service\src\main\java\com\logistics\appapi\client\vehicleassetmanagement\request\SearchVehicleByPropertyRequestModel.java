package com.logistics.appapi.client.vehicleassetmanagement.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/8
 */
@Data
public class SearchVehicleByPropertyRequestModel extends AbstractPageForm<SearchVehicleByPropertyRequestModel> {

	@ApiModelProperty(value = "车辆机构：车辆机构：1 自主，2 外部，3 自营")
	private List<Integer> vehicleProperty;

	@ApiModelProperty(value = "车牌号")
	private String vehicleNo;

	@ApiModelProperty("车辆类别 1 牵引车 2 挂车 3 一体车,多个类型拼接传递 如: '1,2'")
	private String vehicleCategory;
}
