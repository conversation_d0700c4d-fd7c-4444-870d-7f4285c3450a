package com.logistics.tms.controller.reservationorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.reservationorder.ReservationOrderBiz;
import com.logistics.tms.controller.reservationorder.request.*;
import com.logistics.tms.controller.reservationorder.response.*;
import com.logistics.tms.controller.reserveapply.response.ReserveApplyListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 司机预约单
 * @author: wjf
 * @date: 2024/8/19 9:33
 */
@RestController
@RequestMapping("/service/reservationOrder")
public class ReservationOrderController {

    @Resource
    private ReservationOrderBiz reservationOrderBiz;

    /**
     * 预约汇总
     * @return
     */
    @PostMapping(value = "/summary")
    public Result<ReservationOrderSummaryResponseModel> summary(){
        return Result.success(reservationOrderBiz.summary());
    }

    /**
     * 待预约列表
     * @return
     */
    @PostMapping(value = "/waitReservationList")
    public Result<WaitReservationResponseModel> waitReservationList(@RequestBody WaitReservationRequestModel requestModel){
        return Result.success(reservationOrderBiz.waitReservationList(requestModel));
    }

    /**
     * 待预约详情
     * @return
     */
    @PostMapping(value = "/waitReservationDetail")
    public Result<WaitReservationDetailResponseModel> waitReservationDetail(@RequestBody WaitReservationDetailRequestModel requestModel){
        return Result.success(reservationOrderBiz.waitReservationDetail(requestModel));
    }

    /**
     * 确认预约
     * @return
     */
    @PostMapping(value = "/confirmReservation")
    public Result<Boolean> confirmReservation(@RequestBody ConfirmReservationRequestModel requestModel){
        reservationOrderBiz.confirmReservation(requestModel);
        return Result.success(true);
    }

    /**
     * 预约单详情
     * @return
     */
    @PostMapping(value = "/reservationOrderDetail")
    public Result<ReservationOrderSignDetailResponseModel> reservationOrderDetail(@RequestBody ReservationOrderSignDetailRequestModel requestModel){
        return Result.success(reservationOrderBiz.reservationOrderDetail(requestModel));
    }

    /**
     * 确认签到
     * @return
     */
    @PostMapping(value = "/confirmSign")
    public Result<Boolean> confirmSign(@RequestBody ReservationOrderConfirmSignRequestModel requestModel){
        reservationOrderBiz.confirmSign(requestModel);
        return Result.success(true);
    }

    /************************************h5*****************************/
    /**
     * 获取预约信息
     * @return
     */
    @PostMapping(value = "/getReservationInfo4H5")
    public Result<GetReservationInfo4H5RespModel> getReservationInfo4H5(@RequestBody GetReservationInfo4H5ReqModel requestModel){
        return Result.success(reservationOrderBiz.getReservationInfo4H5(requestModel));

    }






    @ApiOperation(value = "预约按钮 进入预约页面 v2.45")
    @PostMapping(value = "/enterReservation")
    public Result<Boolean> enterReservation(@RequestBody @Valid EnterReservationRequestModel requestModel){
        reservationOrderBiz.enterReservation(requestModel);
        return Result.success(true);
    }

    @ApiOperation(value = "签到按钮 进入签到页面 v2.45")
    @PostMapping(value = "/enterSignUp")
    public Result<Boolean> enterSignUp(@RequestBody @Valid EnterSignUpRequestModel requestModel){
        reservationOrderBiz.enterSignUp(requestModel);
        return Result.success(true);
    }













    /**
     * 校验预约信息返回仓库name V2.45
     *
     * @return
     */
    @PostMapping(value = "/checkReservationAndGetWarehouseName")
    public Result<CheckReservationAndGetWarehouseNameResponseModel> checkReservationAndGetWarehouseName(@RequestBody @Valid CheckReservationAndGetWarehouseNameRequestModel requestModel) {
        return Result.success(reservationOrderBiz.checkReservationAndGetWarehouseName(requestModel));
    }


    /**
     * 运单列表-预约按钮-预约详情 (V2.45)
     */
    @ApiOperation(value = "运单列表 -预约按钮-预约详情 V2.45", tags = "V2.45")
    @PostMapping(value = "/searchCarrierReservationOrder")
    public Result<CarrierOrderListStartReservationDetailResponseModel> searchCarrierReservationOrder(@RequestBody SearchCarrierReservationOrderReqModel requestModel) {
        return Result.success(reservationOrderBiz.searchCarrierReservationOrder(requestModel));
    }

    /**
     * 预约单列表 (V2.45)
     */
    @ApiOperation(value = "预约单列表 V2.45", tags = "V2.45")
    @PostMapping(value = "/searchListForDriverWeb")
    public Result<PageInfo<ReservationOrderSearchListResponseModel>> searchListForDriverWeb(@RequestBody ReservationOrderSearchListRequestModel requestModel) {
        return Result.success(reservationOrderBiz.searchListForDriverWeb(requestModel));
    }


    @ApiOperation(value = "预约单列表")
    @PostMapping("/searchListForManagementWeb")
    Result<PageInfo<ReservationOrderSearchListForManagementWebResModel>> searchListForManagementWeb(@RequestBody ReservationOrderSearchListForManagementWebReqModel requestModel){
        return Result.success(reservationOrderBiz.searchListForManagementWeb(requestModel));
    }


}
