package com.logistics.appapi.controller.reserve.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ReserveBalanceApplyCancelRequestDto {

	@ApiModelProperty(value = "申请记录id", required = true)
	@NotBlank(message = "请选择要查看的记录")
	private String applyId;

	@ApiModelProperty(value = "请填写撤销说明,1-100字", required = true)
	@NotBlank(message = "请填写撤销说明,1-100字")
	@Length(min = 1, max = 100, message = "请填写撤销说明,1-100字")
	private String remark;
}
