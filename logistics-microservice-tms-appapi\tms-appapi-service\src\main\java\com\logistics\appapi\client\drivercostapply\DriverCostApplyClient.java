package com.logistics.appapi.client.drivercostapply;


import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.drivercostapply.hystrix.DriverCostApplyClientHystrix;
import com.logistics.appapi.client.drivercostapply.request.AddDriverCostApplyRequestModel;
import com.logistics.appapi.client.drivercostapply.request.DriverCostApplyDetailRequestModel;
import com.logistics.appapi.client.drivercostapply.request.SearchCostApplyListForAppletRequestModel;
import com.logistics.appapi.client.drivercostapply.request.UndoDriverCostApplyRequestModel;
import com.logistics.appapi.client.drivercostapply.response.DriverCostApplyDetailResponseModel;
import com.logistics.appapi.client.drivercostapply.response.SearchCostApplyListCountForAppletResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/5
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
		path = "/service/driverCostApply",
		fallback = DriverCostApplyClientHystrix.class)
public interface DriverCostApplyClient {

	/**
	 * 申请记录详情
	 *
	 * @param requestModel 司机费用申请表id
	 * @return 申请记录详情
	 */
    @PostMapping(value = "/driverCostApplyDetail")
	Result<DriverCostApplyDetailResponseModel> driverCostApplyDetail(@RequestBody DriverCostApplyDetailRequestModel requestModel);

	/**
	 * 撤销费用申请
	 *
	 * @param requestModel 司机费用申请表id
	 * @return 操作结果
	 */
    @PostMapping(value = "/undoDriverCostApply")
	Result<Boolean> undoDriverCostApply(@RequestBody UndoDriverCostApplyRequestModel requestModel);

	/**
	 * 小程序费用申请列表
	 *
	 * @param requestModel 申请时间
	 * @return 司机费用费用申请列表
	 */
    @PostMapping(value = "/searchCostApplyListForApplet")
	Result<SearchCostApplyListCountForAppletResponseModel> searchCostApplyListForApplet(@RequestBody SearchCostApplyListForAppletRequestModel requestModel);

	/**
	 * 小程序提交（重新提交）费用申请
	 *
	 * @param requestModel 费用申请信息
	 * @return 操作结果
	 */
    @PostMapping(value = "/addDriverCostApply")
	Result<Boolean> addDriverCostApply(@RequestBody AddDriverCostApplyRequestModel requestModel) ;
}
