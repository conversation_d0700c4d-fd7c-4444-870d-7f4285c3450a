package com.logistics.management.webapi.base.enums;


public enum ImportStaffGenderEnum {
    DEFAULT(-1, ""),
    MEN(1,"男"),
    WOMEN(2,"女"),
    ;
    private Integer key;
    private String value;

    ImportStaffGenderEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static ImportStaffGenderEnum getEnum(String value) {
        for (ImportStaffGenderEnum t : values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return null;
    }
    public static ImportStaffGenderEnum getEnumByKey(Integer key) {
        for (ImportStaffGenderEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }

    public static ImportStaffGenderEnum getGenderValueByKey(Integer key) {
        for (ImportStaffGenderEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
