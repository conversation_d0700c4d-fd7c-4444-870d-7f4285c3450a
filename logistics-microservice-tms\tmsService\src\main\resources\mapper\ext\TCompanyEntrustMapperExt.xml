<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCompanyEntrustMapper" >
  <select id="searchList" resultType="com.logistics.tms.controller.companyentrust.response.SearchCompanyEntrustResponseModel">
    select
    tce.id as companyEntrustId,
    tce.type as type,
    tce.source,
    tce.created_time as createdTime,
    tce.last_modified_by as lastModifiedBy,
    tce.last_modified_time as lastModifiedTime,
    tc.company_name as companyName,
    tc.trading_certificate_is_amend as tradingCertificateIsAmend
    from t_company_entrust tce
    left join t_company tc on tc.id = tce.company_id
    where tce.valid = 1 and tc.valid = 1
    <if test="params.companyEntrustName != null and params.companyEntrustName != ''">
      and instr(tc.company_name,#{params.companyEntrustName,jdbcType=VARCHAR})
    </if>
    <if test="params.certificateSupplement != null and params.certificateSupplement!=''">
      <if test='params.certificateSupplement == "1"'>
        and tc.trading_certificate_is_amend = 1
      </if>
      <if test='params.certificateSupplement == "2"'>
        and tc.trading_certificate_is_amend = 0
      </if>
    </if>
    <if test="params.createdTimeStart != null and params.createdTimeStart != ''">
      and tce.created_time &gt;= DATE_FORMAT(#{params.createdTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
    </if>
    <if test="params.createdTimeEnd != null and params.createdTimeEnd != ''">
      and tce.created_time &lt;= DATE_FORMAT(#{params.createdTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>
    <if test="params.lastModifiedTimeStart != null and params.lastModifiedTimeStart != ''">
      and tce.last_modified_time &gt;= DATE_FORMAT(#{params.lastModifiedTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %k:%i:%S')
    </if>
    <if test="params.lastModifiedTimeEnd != null and params.lastModifiedTimeEnd != ''">
      and tce.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>
    <if test="params.lastModifiedBy != null and params.lastModifiedBy != ''">
      and instr(tce.last_modified_by,#{params.lastModifiedBy,jdbcType=VARCHAR})
    </if>
    <if test="params.type != null">
      and tce.type = #{params.type,jdbcType=INTEGER}
    </if>
    <if test="params.source != null">
      and tce.source = #{params.source,jdbcType=INTEGER}
    </if>
    order by tce.id desc
  </select>

  <select id="getDetail" resultType="com.logistics.tms.controller.companyentrust.response.CompanyEntrustDetailResponseModel">
    select
    tce.id as companyEntrustId,
    tce.remark as remark,
    tce.settlement_tonnage as settlementTonnage,
    tce.company_short_name as companyShortName,
    tce.if_audit as ifAudit,
    tce.sign_mode as signMode,
    tce.sign_days as signDays,
    tce.type as type,
    tc.company_name as companyName,
    tc.trading_certificate_image as tradingCertificateImage,
    tc.trading_certificate_validity_time as tradingCertificateValidityTime,
    tc.trading_certificate_is_forever as tradingCertificateIsForever,
    tc.trading_certificate_is_amend as tradingCertificateIsAmend
    from t_company_entrust tce
    left join t_company tc on tc.id = tce.company_id
    where tce.valid = 1 and tc.valid = 1
    and tce.id = #{companyEntrustId,jdbcType=BIGINT}
  </select>

  <select id="searchCompanyEntrustByName" resultType="com.logistics.tms.controller.companyentrust.response.SearchCompanyEntrustByNameResponseModel">
    select
    tce.id as companyEntrustId,
    tc.company_name as companyName
    from t_company_entrust tce
    left join t_company tc on tc.id = tce.company_id
    where tce.valid = 1 and tc.valid = 1
    <if test="companyName != null and companyName != ''">
      and instr(tc.company_name,#{companyName,jdbcType=VARCHAR})
    </if>
  </select>

  <select id="getByName" resultMap="BaseResultMap">
    select
    tce.*
    from t_company_entrust tce
    left join t_company tc on tc.id = tce.company_id
    where tce.valid = 1 and tc.valid = 1
    and tc.company_name = #{companyName,jdbcType=VARCHAR}
  </select>
</mapper>