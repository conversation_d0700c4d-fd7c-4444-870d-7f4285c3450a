package com.logistics.management.webapi.base.enums;

/**
 * @Author: sj
 * @Date: 2020/5/18 13:19
 */
public enum IfObjectionEnum {
    NO(0, ""),
    YES(1, "异常"),;

    private Integer key;
    private String value;

    IfObjectionEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static IfObjectionEnum getEnum(Integer key) {
        for (IfObjectionEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return NO;
    }
}
