<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderCorrectMapper" >
  <select id="getByCarrierOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_carrier_order_correct
    where valid=1
    and carrier_order_id in (${carrierOrderIds});
  </select>

  <select id="getByCarrierOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_carrier_order_correct
    where valid=1
    and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
  </select>
</mapper>