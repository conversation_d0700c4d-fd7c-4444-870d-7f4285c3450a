package com.logistics.tms.api.feign.driverpayee.model;

import com.logistics.tms.api.feign.common.model.CertificatePictureModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DriverPayeeDetailResponseModel {

    @ApiModelProperty("收款人账户ID")
    private Long driverPayeeId;
    @ApiModelProperty("收款人姓名")
    private String name ;
    @ApiModelProperty("收款人联系方式")
    private String mobile ;
    @ApiModelProperty("收款人身份证号码")
    private String identityNo;
    @ApiModelProperty("银行Id")
    private Long bankId;
    @ApiModelProperty("银行名称")
    private String bankName;
    @ApiModelProperty("银行卡号")
    private String bankCardNo;
    @ApiModelProperty("身份证前人相面")
    private String identityFront;
    @ApiModelProperty("身份证后人相面")
    private String identityBack;
    @ApiModelProperty("收款证件列表,相对路径")
    private List<CertificatePictureModel> imageList;
    @ApiModelProperty("备注")
    private String remark;


}
