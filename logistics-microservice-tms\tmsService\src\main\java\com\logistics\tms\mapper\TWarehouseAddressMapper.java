package com.logistics.tms.mapper;

import com.logistics.tms.controller.warehouseaddress.request.SearchWarehouseAddressRequestModel;
import com.logistics.tms.controller.warehouseaddress.request.WarehouseAddressEnableRequestModel;
import com.logistics.tms.controller.warehouseaddress.request.WarehouseAddressListRequestModel;
import com.logistics.tms.controller.warehouseaddress.response.SearchWarehouseAddressResponseModel;
import com.logistics.tms.controller.warehouseaddress.response.WarehouseAddressDetailResponseModel;
import com.logistics.tms.controller.warehouseaddress.response.WarehouseAddressListResponseModel;
import com.logistics.tms.entity.TWarehouseAddress;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TWarehouseAddressMapper extends BaseMapper<TWarehouseAddress> {

    List<WarehouseAddressListResponseModel> warehouseAddressList(@Param("params") WarehouseAddressListRequestModel requestModel);

    TWarehouseAddress getValidWarehouseByNameForSinoper(@Param("companyEntrustName")String companyEntrustName, @Param("warehouse") String warehouse, @Param("enabledTime")String enabledTime);

    TWarehouseAddress getWarehouseByName(@Param("companyEntrustId")Long companyEntrustId, @Param("warehouse") String warehouse);

    WarehouseAddressDetailResponseModel getDetail(@Param("warehouseId") Long warehouseId);

    void enable(@Param("requestModel") WarehouseAddressEnableRequestModel requestModel);

    List<String> currentEnableWarehouse();

    List<SearchWarehouseAddressResponseModel> searchWarehouseAddress(@Param("params") SearchWarehouseAddressRequestModel requestModel);
}