package com.logistics.tms.controller.reservationorder.request;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.reservationorder.response.SearchCarrierReservationOrderRespModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierOrderListStartReservationDetailResponseModel {

    /**
     * 预约车辆
     */
    @ApiModelProperty(value = "预约车辆")
    private String reservationVehicleNo = "";


    /**
     * 预约类型：1 提货，2 卸货
     */
    @ApiModelProperty(value = "预约类型：1 提货，2 卸货")
    private Integer reservationType ;


    /**
     * 运单列表
     */
    @ApiModelProperty(value = "运单列表")
    PageInfo<SearchCarrierReservationOrderRespModel> pageInfo = new PageInfo<>();



}
