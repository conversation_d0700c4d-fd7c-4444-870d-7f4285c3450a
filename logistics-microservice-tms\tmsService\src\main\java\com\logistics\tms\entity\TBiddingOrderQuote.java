package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/05/14
*/
@Data
public class TBiddingOrderQuote extends BaseEntity {
    /**
    * 竞价单id
    */
    @ApiModelProperty("竞价单id")
    private Long biddingOrderId;

    /**
    * 报价状态：0 未选择，1 已选择，2 已取消
    */
    @ApiModelProperty("报价状态：0 未选择，1 已选择，2 已取消")
    private Integer quoteStatus;

    /**
    * 车主公司类型：1 公司，2 个人
    */
    @ApiModelProperty("车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;

    /**
    * 车主ID
    */
    @ApiModelProperty("车主ID")
    private Long companyCarrierId;

    /**
    * 车主公司名称
    */
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;

    /**
    * 车主联系人id
    */
    @ApiModelProperty("车主联系人id")
    private Long carrierContactId;

    /**
    * 车主账号名称
    */
    @ApiModelProperty("车主账号名称")
    private String carrierContactName;

    /**
    * 车主账号手机号（原长度50）
    */
    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;

    /**
    * 报价金额类型：1 单价，2 一口价
    */
    @ApiModelProperty("报价金额类型：1 单价，2 一口价")
    private Integer quotePriceType;

    /**
    * 报价金额
    */
    @ApiModelProperty("报价金额")
    private BigDecimal quotePrice;

    /**
    * 报价人
    */
    @ApiModelProperty("报价人")
    private String quoteOperator;

    /**
    * 报价时间
    */
    @ApiModelProperty("报价时间")
    private Date quoteTime;

    /**
    * 车长id
    */
    @ApiModelProperty("车长id")
    private Long vehicleLengthId;

    /**
    * 车长（米）
    */
    @ApiModelProperty("车长（米）")
    private BigDecimal vehicleLength;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}