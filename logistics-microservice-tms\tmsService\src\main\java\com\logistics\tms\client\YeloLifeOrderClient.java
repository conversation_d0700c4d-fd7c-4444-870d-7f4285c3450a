package com.logistics.tms.client;

import com.yelo.life.order.api.fegin.recycle.RecycleServiceApi;
import com.yelo.life.order.api.fegin.recycle.model.GenerateRecycleOrderRequestModel;
import com.yelo.life.order.api.fegin.recycle.model.GenerateRecycleOrderResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: wjf
 * @date: 2022/8/23 18:19
 */
@Service
public class YeloLifeOrderClient {

    @Autowired
    private RecycleServiceApi recycleServiceApi;

    /**
     * 生成回收记录
     * @param requestModel
     * @return
     */
    public GenerateRecycleOrderResponseModel generateRecycleOrder(GenerateRecycleOrderRequestModel requestModel){
        Result<GenerateRecycleOrderResponseModel> result = recycleServiceApi.generateRecycleOrder(requestModel);
        result.throwException();
        return result.getData();
    }


}
