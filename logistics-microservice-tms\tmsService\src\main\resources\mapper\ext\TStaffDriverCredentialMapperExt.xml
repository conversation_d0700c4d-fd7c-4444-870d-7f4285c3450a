<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TStaffDriverCredentialMapper" >
    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TStaffDriverCredential" >
        <foreach collection="list" item="item" separator=";">
            update t_staff_driver_credential
            <set >
                <if test="item.staffId != null" >
                    staff_id = #{item.staffId,jdbcType=BIGINT},
                </if>
                <if test="item.occupationalRequirementsCredentialNo != null" >
                    occupational_requirements_credential_no = #{item.occupationalRequirementsCredentialNo,jdbcType=VARCHAR},
                </if>
                <if test="item.initialIssuanceDate != null" >
                    initial_issuance_date = #{item.initialIssuanceDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.driversLicenseNo != null" >
                    drivers_license_no = #{item.driversLicenseNo,jdbcType=VARCHAR},
                </if>
                <if test="item.permittedType != null" >
                    permitted_type = #{item.permittedType,jdbcType=VARCHAR},
                </if>
                <if test="item.driversLicenseDateFrom != null" >
                    drivers_license_date_from = #{item.driversLicenseDateFrom,jdbcType=TIMESTAMP},
                </if>
                <if test="item.driversLicenseDateTo != null" >
                    drivers_license_date_to = #{item.driversLicenseDateTo,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getDueDriverCredentialCount" resultType="java.util.HashMap">
        select
        ifnull(group_concat(if(thrityDueCount = 1, staff_id, null)), '') as totalIds,
        ifnull(group_concat(if(sevenDueCount = 1, staff_id, null)), '')  as weekIds,
        ifnull(group_concat(if(dueCount = 1, staff_id, null)), '')       as hasExpiredIds,
        ifnull(sum(thrityDueCount), 0)                                   as total,
        ifnull(sum(sevenDueCount), 0)                                    as week,
        ifnull(sum(dueCount), 0)                                         as hasExpired
        from (SELECT tcdr.id                                                                    as staff_id,
              if(DATE_SUB(max(drivers_license_date_to), INTERVAL #{remindDays,jdbcType=INTEGER} DAY) &lt;= DATE_FORMAT(now(), '%Y-%m-%d') and
                 max(drivers_license_date_to) >= DATE_FORMAT(now(), '%Y-%m-%d'), 1, 0)   as thrityDueCount,
              if(DATE_SUB(max(drivers_license_date_to), INTERVAL 7 DAY) &lt;= DATE_FORMAT(now(), '%Y-%m-%d') and max(drivers_license_date_to) >= DATE_FORMAT(now(), '%Y-%m-%d'), 1,
                 0)                                                                      as sevenDueCount,
              if(max(drivers_license_date_to) &lt; DATE_FORMAT(now(), '%Y-%m-%d'), 1, 0) as dueCount
              from t_staff_driver_credential tsdc
              left join t_carrier_driver_relation tcdr on tcdr.valid = 1 and tcdr.driver_id = tsdc.id and tcdr.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
              where tsdc.valid = 1
              and tcdr.id is not null
              group by staff_id) tmp
    </select>

    <select id="getDriverCredentialByStaffId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_staff_driver_credential
        where valid = 1
        and staff_id = #{staffId,jdbcType=BIGINT}
        LIMIT 1
    </select>

    <update id="updateByStaffIdSelective"  parameterType="com.logistics.tms.entity.TStaffDriverCredential">
        update t_staff_driver_credential
        <set >
            <if test="occupationalRequirementsCredentialNo != null" >
                occupational_requirements_credential_no = #{occupationalRequirementsCredentialNo,jdbcType=VARCHAR},
            </if>
            <if test="initialIssuanceDate != null" >
                initial_issuance_date = #{initialIssuanceDate,jdbcType=TIMESTAMP},
            </if>
            <if test="driversLicenseNo != null" >
                drivers_license_no = #{driversLicenseNo,jdbcType=VARCHAR},
            </if>
            <if test="permittedType != null" >
                permitted_type = #{permittedType,jdbcType=VARCHAR},
            </if>
            <if test="driversLicenseDateFrom != null" >
                drivers_license_date_from = #{driversLicenseDateFrom,jdbcType=TIMESTAMP},
            </if>
            <if test="driversLicenseDateTo != null" >
                drivers_license_date_to = #{driversLicenseDateTo,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null" >
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null" >
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null" >
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null" >
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where staff_id = #{staffId,jdbcType=BIGINT} and valid = 1
    </update>
</mapper>