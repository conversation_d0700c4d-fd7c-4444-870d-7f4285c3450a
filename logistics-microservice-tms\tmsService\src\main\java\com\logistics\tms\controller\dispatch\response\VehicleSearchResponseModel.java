package com.logistics.tms.controller.dispatch.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VehicleSearchResponseModel {

    @ApiModelProperty("车辆id")
    private Long vehicleId;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("车辆关联的司机id")
    private Long driverId;

    @ApiModelProperty("车辆关联的司机名字")
    private String driverName;

    @ApiModelProperty("车辆关联的司机手机号")
    private String driverPhone;

    @ApiModelProperty("车辆关联的司机身份证")
    private String driverIdentityNumber;
}
