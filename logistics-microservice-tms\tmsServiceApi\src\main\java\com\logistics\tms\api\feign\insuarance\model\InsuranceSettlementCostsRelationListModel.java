package com.logistics.tms.api.feign.insuarance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/11/1
 * @description:
 */
@Data
public class InsuranceSettlementCostsRelationListModel {

    @ApiModelProperty(value = "保险费用Id")
    private Long insuranceId;

    private List<InsuranceSettlementCostsRelationModel> insuranceSettlementCostsRelationList;


}
