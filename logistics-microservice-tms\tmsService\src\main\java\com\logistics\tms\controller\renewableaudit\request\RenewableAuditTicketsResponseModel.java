package com.logistics.tms.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class RenewableAuditTicketsResponseModel {

    /**
     * 外键类型
     */
    @ApiModelProperty("外键类型")
    private Integer objectType;

    /**
     * 外键ID
     */
    @ApiModelProperty("外键ID")
    private Long objectId;

    /**
     * 文件类型
     */
    @ApiModelProperty("文件类型")
    private Integer fileType;

    /**
     * 文件类型名
     */
    @ApiModelProperty("文件类型名")
    private String fileTypeName;

    /**
     * 文件名
     */
    @ApiModelProperty("文件名")
    private String fileName;

    /**
     * 相对路径
     */
    @ApiModelProperty("相对路径")
    private String filePath;

    /**
     * 上传人姓名
     */
    @ApiModelProperty("上传人姓名")
    private String uploadUserName;

    /**
     * 上传时间
     */
    @ApiModelProperty("上传时间")
    private Date uploadTime;

    /**
     * 文件格式
     */
    @ApiModelProperty("文件格式")
    private String suffix;
}
