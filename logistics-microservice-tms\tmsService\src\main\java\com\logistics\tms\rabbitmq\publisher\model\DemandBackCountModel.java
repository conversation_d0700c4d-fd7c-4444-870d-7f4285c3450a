package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @author:lei.zhu
 * @date:2022/1/5 18:09:52
 */
@Data
@Accessors(chain = true)
public class DemandBackCountModel {

    @ApiModelProperty("需求单号")
    private String demandCode;

    @ApiModelProperty("回退数量")
    private Integer completeBackAmount;
}
