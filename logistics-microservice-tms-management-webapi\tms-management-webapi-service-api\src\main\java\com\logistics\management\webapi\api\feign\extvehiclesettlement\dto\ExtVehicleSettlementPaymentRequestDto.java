package com.logistics.management.webapi.api.feign.extvehiclesettlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/11/20 14:27
 */
@Data
public class ExtVehicleSettlementPaymentRequestDto {
    @ApiModelProperty(value = "外部车辆结算ID",required = true)
    @NotBlank(message = "id不能为空")
    private String extVehicleSettlementId;
    @ApiModelProperty(value = "付款通道",required = true)
    @Pattern(regexp = "[\\u4E00-\\u9FA5A-Za-z0-9]{2,10}",message = "请维护付款通道：汉字、英文大小写、数字，2≤长度≤10")
    private String paymentChannel;
    @ApiModelProperty(value = "付款单号",required = true)
    @Pattern(regexp = "[A-Za-z0-9]{10,30}",message = "请维护付款单号：大小写字母、数字，10≤长度≤30")
    private String paymentNo;
    @ApiModelProperty(value = "应收费用",required = true)
    @DecimalMin(value = "0", message = "请维护应收费用")
    @DecimalMax(value = "100000", message = "请维护应收费用")
    private String paymentFee;
    @ApiModelProperty(value = "报销费用",required = true)
    @DecimalMin(value = "0", message = "请维护报销费用")
    @DecimalMax(value = "100000", message = "请维护报销费用")
    private String reimburseFee;
    @ApiModelProperty(value = "备注", required = true)
    private String remark;
    @ApiModelProperty(value = "附件",required = true)
    @NotEmpty(message = "请上传附件")
    private List<String> attachmentList;
}
