package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/10/8 14:13
 */
public enum VehicleTireSettlementStatusEnum {
    WAIT_SETTLEMENT(0,"待结算"),
    COMPLETE_SETTLEMENT(1,"已结算"),
    ;
    private Integer key;
    private String value;

    VehicleTireSettlementStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static VehicleTireSettlementStatusEnum getEnum(Integer key) {
        for (VehicleTireSettlementStatusEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return null;
    }
}
