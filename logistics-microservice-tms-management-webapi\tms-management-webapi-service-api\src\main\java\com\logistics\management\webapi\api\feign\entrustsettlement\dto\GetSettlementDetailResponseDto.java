package com.logistics.management.webapi.api.feign.entrustsettlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class GetSettlementDetailResponseDto {
    @ApiModelProperty("结算IDs")
    private String settlementIds="";
    @ApiModelProperty("总单数")
    private String totalEntrustOrderCount="";
    @ApiModelProperty("总吨数")
    private String totalWeightSettlementAmount="";
    @ApiModelProperty("总件数")
    private String totalPackageSettlementAmount="";
    @ApiModelProperty("总金额")
    private String totalSettlementCost="";
    @ApiModelProperty("需求单结算列表")
    private List<GetSettlementDetailRowDto> settlementRows;
}
