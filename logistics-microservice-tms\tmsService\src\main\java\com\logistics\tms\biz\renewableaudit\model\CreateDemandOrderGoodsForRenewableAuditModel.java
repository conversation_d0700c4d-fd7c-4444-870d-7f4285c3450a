package com.logistics.tms.biz.renewableaudit.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2022/8/20 9:32
 */
@Data
public class CreateDemandOrderGoodsForRenewableAuditModel {

    @ApiModelProperty("sku编号")
    private String skuCode;

    @ApiModelProperty("货物品名")
    private String goodsName;

    @ApiModelProperty("货物数量")
    private BigDecimal goodsAmount;

    @ApiModelProperty(value = "收货单价 0<单价<=10000")
    private BigDecimal goodsPrice;
}
