package com.logistics.tms.biz.gpsfee

import com.logistics.tms.api.feign.gpsfee.model.AddOrModifyGpsFeeRequestModel
import com.logistics.tms.api.feign.gpsfee.model.GetDeductingHistoryByGpsFeeIdResponseModel
import com.logistics.tms.api.feign.gpsfee.model.GpsFeeDetailResponseModel
import com.logistics.tms.api.feign.gpsfee.model.GpsFeeIdRequestModel
import com.logistics.tms.api.feign.gpsfee.model.GpsFeeRecordsListResponseModel
import com.logistics.tms.api.feign.gpsfee.model.SearchGpsFeeListCountResponseModel
import com.logistics.tms.api.feign.gpsfee.model.SearchGpsFeeListRequestModel
import com.logistics.tms.api.feign.gpsfee.model.SearchGpsFeeListResponseModel
import com.logistics.tms.api.feign.gpsfee.model.TerminationGpsFeeRequestModel
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TDeductingHistory
import com.logistics.tms.entity.TGpsFee
import com.logistics.tms.entity.TVehicleSettlementRelation
import com.logistics.tms.mapper.TDeductingHistoryMapper
import com.logistics.tms.mapper.TGpsFeeMapper
import com.logistics.tms.mapper.TGpsFeeRecordsMapper
import com.logistics.tms.mapper.TStaffBasicMapper
import com.logistics.tms.mapper.TVehicleBasicMapper
import com.logistics.tms.mapper.TVehicleSettlementRelationMapper
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class GpsFeeBizTest extends Specification {
    @Mock
    TGpsFeeMapper gpsFeeMapper
    @Mock
    TGpsFeeRecordsMapper gpsFeeRecordsMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TDeductingHistoryMapper deductingHistoryMapper
    @Mock
    TStaffBasicMapper staffBasicMapper
    @Mock
    TVehicleBasicMapper vehicleBasicMapper
    @Mock
    TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper
    @Mock
    Logger log
    @InjectMocks
    GpsFeeBiz gpsFeeBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Gps Fee List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(gpsFeeMapper.searchGpsFeeList(any())).thenReturn([new SearchGpsFeeListResponseModel()])

        expect:
        gpsFeeBiz.searchGpsFeeList(requestModel) == expectedResult

        where:
        requestModel                       || expectedResult
        new SearchGpsFeeListRequestModel() || [new SearchGpsFeeListResponseModel()]
    }

    @Unroll
    def "search Gps Fee List Count where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(gpsFeeMapper.searchGpsFeeListCount(any())).thenReturn(new SearchGpsFeeListCountResponseModel())

        expect:
        gpsFeeBiz.searchGpsFeeListCount(requestModel) == expectedResult

        where:
        requestModel                       || expectedResult
        new SearchGpsFeeListRequestModel() || new SearchGpsFeeListCountResponseModel()
    }

    @Unroll
    def "get Gps Fee Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(gpsFeeMapper.getGpsFeeDetail(anyLong())).thenReturn(new GpsFeeDetailResponseModel())
        when(tVehicleSettlementRelationMapper.getByObjectIdAndType(anyInt(), anyLong())).thenReturn([new TVehicleSettlementRelation()])

        expect:
        gpsFeeBiz.getGpsFeeDetail(requestModel) == expectedResult

        where:
        requestModel               || expectedResult
        new GpsFeeIdRequestModel() || new GpsFeeDetailResponseModel()
    }

    @Unroll
    def "add Or Modify Gps Fee where requestModel=#requestModel"() {
        given:
        when(gpsFeeMapper.getByVehicleStartDate(anyLong(), any(), any())).thenReturn([new TGpsFee(status: 0, vehicleId: 1l, vehicleNo: "vehicleNo", staffId: 1l, name: "name", mobile: "mobile", terminalType: "terminalType", gpsServiceProvider: "gpsServiceProvider", simNumber: "simNumber", installTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 18).getTime(), startDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 18).getTime(), endDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 18).getTime(), finishDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 18).getTime(), serviceFee: 0 as BigDecimal, cooperationPeriod: 0, cooperationStatus: 0, remark: "remark")])
        when(deductingHistoryMapper.getByObjectTypeId(anyInt(), anyLong())).thenReturn([new TDeductingHistory()])
        when(vehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())
        when(tVehicleSettlementRelationMapper.getByObjectIdAndType(anyInt(), anyLong())).thenReturn([new TVehicleSettlementRelation()])

        expect:
        gpsFeeBiz.addOrModifyGpsFee(requestModel)
        assert expectedResult == false

        where:
        requestModel                        || expectedResult
        new AddOrModifyGpsFeeRequestModel() || true
    }

    @Unroll
    def "get Cooperation Status where endDate=#endDate and startDate=#startDate then expect: #expectedResult"() {
        expect:
        gpsFeeBiz.getCooperationStatus(startDate, endDate) == expectedResult

        where:
        endDate                                                          | startDate                                                        || expectedResult
        new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 18).getTime() | new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 18).getTime() || 0
    }

    @Unroll
    def "termination Gps Fee where requestModel=#requestModel"() {
        expect:
        gpsFeeBiz.terminationGpsFee(requestModel)
        assert expectedResult == false

        where:
        requestModel                        || expectedResult
        new TerminationGpsFeeRequestModel() || true
    }

    @Unroll
    def "get Gps Fee Records where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(gpsFeeRecordsMapper.getGpsFeeRecords(anyLong())).thenReturn([new GpsFeeRecordsListResponseModel()])

        expect:
        gpsFeeBiz.getGpsFeeRecords(requestModel) == expectedResult

        where:
        requestModel               || expectedResult
        new GpsFeeIdRequestModel() || [new GpsFeeRecordsListResponseModel()]
    }

    @Unroll
    def "get Gps Fee Deducting History where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(deductingHistoryMapper.getByObjectTypeId(anyInt(), anyLong())).thenReturn([new TDeductingHistory()])

        expect:
        gpsFeeBiz.getGpsFeeDeductingHistory(requestModel) == expectedResult

        where:
        requestModel               || expectedResult
        new GpsFeeIdRequestModel() || new GetDeductingHistoryByGpsFeeIdResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme