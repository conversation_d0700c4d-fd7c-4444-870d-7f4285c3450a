package com.logistics.appapi.controller.carrierorder.mapping;

import com.logistics.appapi.controller.carrierorder.response.DownloadLadingBillGoodsResponseDto;
import com.logistics.appapi.controller.carrierorder.response.DownloadLadingBillResponseDto;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AvailableOnWeekendsEnum;
import com.logistics.appapi.base.enums.EntrustTypeEnum;
import com.logistics.appapi.base.enums.GoodsUnitEnum;
import com.logistics.appapi.base.enums.LoadingUnloadingPartEnum;
import com.logistics.appapi.client.carrierorder.response.DownloadLadingBillGoodsResponseModel;
import com.logistics.appapi.client.carrierorder.response.DownloadLadingBillResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * @author: wjf
 * @date: 2018/9/30 13:41
 */
public class DownloadLadingBillMapping extends MapperMapping<DownloadLadingBillResponseModel, DownloadLadingBillResponseDto> {
    @Override
    public void configure() {
        DownloadLadingBillResponseModel model = getSource();
        DownloadLadingBillResponseDto dto = getDestination();

        if (null != model) {
            dto.setLoadDetailAddress(model.getLoadProvinceName() + "-" + model.getLoadCityName() + "-" + model.getLoadAreaName() + " " + model.getLoadDetailAddress());
            dto.setUnloadDetailAddress(model.getUnloadProvinceName() + "-" + model.getUnloadCityName() + "-" + model.getUnloadAreaName() + " " + model.getUnloadDetailAddress());
            if (model.getExpectedLoadTime() != null) {
                dto.setExpectedLoadTime(DateUtils.dateToString(model.getExpectedLoadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            } else {
                dto.setExpectedLoadTime("");
            }
            if (model.getRecycleExpectedLoadTime() != null) {
                dto.setRecycleExpectedLoadTime(DateUtils.dateToString(model.getRecycleExpectedLoadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            } else {
                dto.setRecycleExpectedLoadTime("");
            }
            if (model.getExpectedUnloadTime() != null) {
                dto.setExpectedUnloadTime(DateUtils.dateToString(model.getExpectedUnloadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            } else {
                dto.setExpectedUnloadTime("");
            }
            if (model.getLoadTime() != null) {
                dto.setLoadTime(DateUtils.dateToString(model.getLoadTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            } else {
                dto.setLoadTime("");
            }
            if (model.getDispatchTime() != null) {
                dto.setDispatchTime(DateUtils.dateToString(model.getDispatchTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            dto.setEntrustTypeLabel(EntrustTypeEnum.getEnum(model.getEntrustType()).getValue());
            String unit = GoodsUnitEnum.getEnum(model.getGoodsUnit()).getUnit();
            dto.setExpectAmount(model.getExpectAmount().stripTrailingZeros().toPlainString());
            dto.setLoadAmount(model.getLoadAmount().stripTrailingZeros().toPlainString());
            dto.setSignAmount(model.getSignAmount().stripTrailingZeros().toPlainString());
            dto.setConsignor((StringUtils.isNotBlank(model.getConsignorName()) ? model.getConsignorName() : "") + " " + (StringUtils.isNotBlank(model.getConsignorMobile()) ? model.getConsignorMobile() : ""));
            dto.setReceiver((StringUtils.isNotBlank(model.getReceiverName()) ? model.getReceiverName() : "") + " " + (StringUtils.isNotBlank(model.getReceiverMobile()) ? model.getReceiverMobile() : ""));
            dto.setGoodsUnit(unit);
            dto.setAmountDiff(model.getLoadAmount().subtract(model.getExpectAmount()).stripTrailingZeros().toPlainString());

            //其他要求
            String availableOnWeekends = AvailableOnWeekendsEnum.getEnum(model.getAvailableOnWeekends()).getValue();//周末是否可上门：周末可上门/周末不可上门
            String loadingUnloadingPart = LoadingUnloadingPartEnum.getEnum(model.getLoadingUnloadingPart()).getValue();//装卸方式：客户装卸/我司装卸
            StringBuffer otherRequirements = new StringBuffer();
            if (StringUtils.isNotBlank(availableOnWeekends)) {
                otherRequirements.append(availableOnWeekends);
            }
            if (StringUtils.isNotBlank(loadingUnloadingPart)) {
                if (StringUtils.isNotBlank(otherRequirements)) {
                    otherRequirements.append(CommonConstant.SEMICOLON).append(loadingUnloadingPart);
                } else {
                    otherRequirements.append(loadingUnloadingPart);
                }
            }
            dto.setOtherRequirements(otherRequirements.toString());

            List<DownloadLadingBillGoodsResponseModel> goodsInfoList = model.getGoodsInfoList();
            if (ListUtils.isNotEmpty(goodsInfoList)) {
                BigDecimal totalCount = new BigDecimal(0);
                BigDecimal totalVolume = new BigDecimal(0);
                StringBuffer goodsName = new StringBuffer();
                for (DownloadLadingBillGoodsResponseModel responseModel : goodsInfoList) {
                    totalCount = (totalCount.add(responseModel.getExpectAmount())).stripTrailingZeros();
                    for (DownloadLadingBillGoodsResponseDto responseDto : dto.getGoodsInfoList()) {
                        if (responseModel.getGoodsId().toString().equals(responseDto.getGoodsId())) {
                            responseDto.setGoodsSize(responseModel.getGoodsSize());
                            responseDto.setExpectAmount(responseModel.getExpectAmount().stripTrailingZeros().toPlainString() + unit);
                            //实提小于等于0不展示
                            if (BigDecimal.ZERO.compareTo(responseModel.getLoadAmount()) < CommonConstant.INTEGER_ZERO) {
                                responseDto.setLoadAmount(responseModel.getLoadAmount().stripTrailingZeros().toPlainString() + unit);
                                responseDto.setLoadDiffAmount(responseModel.getLoadAmount().subtract(responseModel.getExpectAmount()).stripTrailingZeros().toPlainString() + unit);
                            } else {
                                responseDto.setLoadAmount(CommonConstant.BLANK_TEXT);
                            }
                            //实卸
                            if (BigDecimal.ZERO.compareTo(responseModel.getUnloadAmount()) < CommonConstant.INTEGER_ZERO) {
                                responseDto.setUnloadAmount(responseModel.getUnloadAmount().stripTrailingZeros().toPlainString() + unit);
                                responseDto.setUnloadDiffAmount(responseModel.getUnloadAmount().subtract(responseModel.getLoadAmount()).stripTrailingZeros().toPlainString() + unit);

                            } else {
                                responseDto.setUnloadAmount(CommonConstant.BLANK_TEXT);
                            }
                            if (GoodsUnitEnum.BY_VOLUME.getKey().equals(model.getGoodsUnit())) {
                                responseDto.setGoodsSize(responseDto.getGoodsSize() + responseModel.getLength() + "*" + responseModel.getWidth() + "*" + responseModel.getHeight() + "mm");
                            }
                            BigDecimal volume = ConverterUtils.toBigDecimal(responseModel.getLength()).multiply(ConverterUtils.toBigDecimal(responseModel.getWidth())).multiply(ConverterUtils.toBigDecimal(responseModel.getHeight())).multiply(ConverterUtils.toBigDecimal(responseModel.getExpectAmount())).divide(CommonConstant.CUBIC_MILLIMETER_TO_CUBIC_METER);
                            responseDto.setVolume(volume.setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
                            totalVolume = totalVolume.add(volume);

                            goodsName.append(responseDto.getGoodsName()).append("/");
                        }
                    }
                }
                dto.setTotalCount(totalCount.stripTrailingZeros().toPlainString() + unit);
                if (GoodsUnitEnum.BY_VOLUME.getKey().equals(model.getGoodsUnit())) {
                    dto.setTotalVolume(totalVolume.stripTrailingZeros().toPlainString() + CommonConstant.SQUARE);
                }
                if (StringUtils.isNotBlank(goodsName)) {
                    dto.setGoodsName(goodsName.substring(CommonConstant.INTEGER_ZERO, goodsName.length() - CommonConstant.INTEGER_ONE));
                }
            }
        }
    }
}
