package com.logistics.tms.api.feign.entrustsettlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/11 18:58
 */
@Data
public class GetSettlementDetailResponseModel {
    @ApiModelProperty("结算ID")
    private String settlementIds;
    @ApiModelProperty("总单数")
    private Integer totalEntrustOrderCount;
    @ApiModelProperty("总吨数")
    private BigDecimal totalWeightSettlementAmount;
    @ApiModelProperty("总件数")
    private BigDecimal totalPackageSettlementAmount;
    @ApiModelProperty("总金额")
    private BigDecimal totalSettlementCost;
    @ApiModelProperty("需求单结算列表")
    private List<GetSettlementDetailRowModel> settlementRows;
}
