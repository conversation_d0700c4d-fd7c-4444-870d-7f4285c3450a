package com.logistics.tms.client.feign.warehouse.lift.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/4 16:36
 */
@Data
public class GetWarehouseDetailForLifeRequestModel {
    @ApiModelProperty("工厂code")
    private List<String> warehouseCode;
    @ApiModelProperty("启用/禁用(1启用,0禁用)")
    private Integer enabled;
    @ApiModelProperty("仓库名称/仓库地址/仓库联系人")
    private String warehouseName;
    @ApiModelProperty("仓库类型,1是自有,2是第三方,4虚拟,5其他、6 粉碎工厂")
    private List<Integer> types;
}
