package com.logistics.management.webapi.controller.uploadfile.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/5/8 17:06
 */
@Data
public class BusinessLicenseResponseDto {
    @ApiModelProperty("单位名称")
    private String companyName="";
    @ApiModelProperty("类型：有限责任公司（自然人独资）")
    private String companyType="";
    @ApiModelProperty("法人")
    private String legalPerson="";
    @ApiModelProperty("地址")
    private String address="";
    @ApiModelProperty("有效期")
    private String validDate="";
    @ApiModelProperty("证件编号")
    private String certificateNumber="";
    @ApiModelProperty("社会信用代码")
    private String socialCreditCode="";
    @ApiModelProperty("经营范围")
    private String businessScope="";
    @ApiModelProperty("成立日期")
    private String setUpDate="";
    @ApiModelProperty("注册资本")
    private String registeredCapital="";
    @ApiModelProperty("组成形式")
    private String orgType="";
}
