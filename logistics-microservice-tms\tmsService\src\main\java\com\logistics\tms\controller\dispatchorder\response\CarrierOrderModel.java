package com.logistics.tms.controller.dispatchorder.response;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/1/23 15:13
 */
@Data
public class CarrierOrderModel {

    @ApiModelProperty("运单id")
    private Long carrierOrderId;

    @ApiModelProperty("需求单ID")
    private Long demandOrderId;

    @ApiModelProperty("运单状态")
    private Integer status;

    @ApiModelProperty("是否取消 0 否 1 是")
    private Integer ifCancel;

    @ApiModelProperty("是否放空 0 否 1 是")
    private Integer ifEmpty;

    @ApiModelProperty("调度司机运费")
    private BigDecimal dispatchFreightFee;

    @ApiModelProperty("货物单位")
    private Integer goodsUnit;

    @ApiModelProperty("车主公司名")
    private String companyCarrierName;

    @ApiModelProperty("车主类型: 1:企业 2:个人")
    private Integer companyCarrierType;

    @ApiModelProperty("车主联系人姓名")
    private String carrierContactName;

    @ApiModelProperty("车主联系人手机号")
    private String carrierContactPhone;

    @ApiModelProperty("运单分配的货物数量")
    private BigDecimal carrierExpectAmount;

    @ApiModelProperty("卸货数量")
    private BigDecimal carrierUnloadAmount;

    @ApiModelProperty("运单货物")
    private List<CarrierOrderGoodsModel> carrierOrderGoodsList;
}
