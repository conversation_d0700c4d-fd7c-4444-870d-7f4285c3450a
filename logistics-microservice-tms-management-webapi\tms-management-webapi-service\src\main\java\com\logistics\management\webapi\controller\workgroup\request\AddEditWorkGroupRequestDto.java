package com.logistics.management.webapi.controller.workgroup.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class AddEditWorkGroupRequestDto {

    @ApiModelProperty("智能推送配置表id,编辑时必填")
    private String workGroupId;

    @ApiModelProperty(value = "群名", required = true)
    @Length(min = 1, max = 50, message = "请输入1-50字符的群名")
    @NotBlank(message = "请输入群名")
    private String groupName;

    @ApiModelProperty(value = "负责人id", required = true)
    @NotBlank(message = "请选择负责人")
    private String groupOwnerId;

    @ApiModelProperty(value = "参与人id", required = true)
    @NotBlank(message = "请选择参与人")
    private String participantId;

    @ApiModelProperty(value = "(3.15.2)推送设置：1 新建群聊，2 已有群聊", required = true)
    @Pattern(regexp = "^([12])$", message = "请选择推送设置")
    private String workGroupSource;

    @ApiModelProperty(value = "(3.15.2)机器人编码")
    private String workGroupCode;

    @Valid
    @ApiModelProperty(value = "(3.15.2)匹配地点：1 发货信息，2 收货信息", required = true)
    @NotEmpty(message = "请选择匹配地点")
    @Size(max = 2, message = "请选择匹配地点")
    private List<@Pattern(regexp = "^([12])$", message = "请选择正确的匹配地点") String> matchingLocationList;

    @ApiModelProperty(value = "(3.15.2)匹配字段：1 区域，2 仓库", required = true)
    @Pattern(regexp = "^([12])$", message = "请选择匹配字段")
    private String matchingField;

    @ApiModelProperty(value = "(3.15.2)配置仓库")
    private String configWarehouse;

    @Valid
    @ApiModelProperty(value = "配置区域")
    private List<AddEditWorkGroupDistrictRequestDto> districtList;

    @ApiModelProperty(value = "群简介", required = true)
    @Length(min = 1, max = 300, message = "请输入1-300字符的群简介")
    @NotBlank(message = "请输入群简介")
    private String groupDesc;
}
