package com.logistics.tms.controller.reservationorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SearchCarrierReservationOrderRespModel {


    @ApiModelProperty(value = "运单id")
    private Long carrierOrderId;

    @ApiModelProperty(value = "运单号")
    private String carrierOrderCode;

    @ApiModelProperty(value = "司机姓名")
    private String driverName = "";

    /**
     * 预约车辆
     */
    @ApiModelProperty(value = "预约车辆")
    private String reservationVehicleNo = "";


    @ApiModelProperty(value = "预约类型：1 提货，2 卸货")
    private Integer reservationType ;

    @ApiModelProperty(value = "预约类型：1 提货，2 卸货")
    private String reservationTypeLabel = "";


    @ApiModelProperty(value = "提货地")
    private String loadAddress = "";

    @ApiModelProperty(value = "卸货地")
    private String unLoadAddress = "";

    @ApiModelProperty(value = "预提数")
    private BigDecimal loadAmountExpect ;

    /**
     * 仓库
     */
    @ApiModelProperty("预约仓库")
    private String warehouseName = "";




}
