package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/20
 */
@Data
public class LoadDetailForYeloLifeResponseModel {

	@ApiModelProperty("运单ID")
	private String carrierOrderId;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("车辆")
	private String vehicleNo;

	@ApiModelProperty("司机姓名")
	private String driverName;

	@ApiModelProperty("司机手机号")
	private String driverMobile;

	@ApiModelProperty("单位")
	private Integer goodsUnit;

	@ApiModelProperty("提货地址信息")
	private String loadProvinceName;
	private String loadCityName;
	private String loadAreaName;
	private String loadDetailAddress;
	private String loadWarehouse;

	@ApiModelProperty("发货人")
	private String consignorName;

	@ApiModelProperty("发货人手机号")
	private String consignorMobile;

	@ApiModelProperty("卸货地址信息")
	private String unloadProvinceName;
	private String unloadCityName;
	private String unloadAreaName;
	private String unloadDetailAddress;
	private String unloadWarehouse;

	@ApiModelProperty("收货人")
	private String receiverName;

	@ApiModelProperty("收货人手机号")
	private String receiverMobile;

	@ApiModelProperty("业务类型")
	private Integer businessType;

	@ApiModelProperty("货物列表")
	private List<LoadDetailForYeloLifeGoodsResponseModel> goodsList;
}
