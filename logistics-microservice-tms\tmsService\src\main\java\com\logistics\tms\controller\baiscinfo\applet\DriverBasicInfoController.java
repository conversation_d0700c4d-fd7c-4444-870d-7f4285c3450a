package com.logistics.tms.controller.baiscinfo.applet;

import com.logistics.tms.biz.basicinfo.applet.DriverBasicInfoBiz;
import com.logistics.tms.controller.baiscinfo.applet.request.DriverBasicInfoSubmitRequestModel;
import com.logistics.tms.controller.baiscinfo.applet.request.FaceRecognitionRequestModel;
import com.logistics.tms.controller.baiscinfo.applet.request.FaceRecognitionResultRequestModel;
import com.logistics.tms.controller.baiscinfo.applet.request.GetPersonAuthVerifyCodeRequestModel;
import com.logistics.tms.controller.baiscinfo.applet.response.DriverBasicInfoResponseModel;
import com.logistics.tms.controller.baiscinfo.applet.response.FaceRecognitionResponseModel;
import com.logistics.tms.controller.baiscinfo.applet.response.FaceRecognitionResultResponseModel;
import com.logistics.tms.controller.baiscinfo.web.request.VerifyPersonThreeElementsRequestModel;
import com.logistics.tms.controller.baiscinfo.web.request.VerifyPersonTwoElementsRequestModel;
import com.logistics.tms.controller.baiscinfo.web.response.VerifyPersonThreeElementsResponseModel;
import com.logistics.tms.controller.baiscinfo.web.response.VerifyPersonTwoElementsResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(value = "API - DriverBasicInfoServiceApi", tags = "司机基础信息管理")
@RestController
@RequestMapping("/service/driverApplet/basicInfo")
public class DriverBasicInfoController {

    @Resource
    private DriverBasicInfoBiz driverBasicInfoBiz;

    /**
     * 小程序 - 查询司机基础信息
     *
     * @return
     */
    @ApiOperation(value = "查询司机基础信息")
    @PostMapping(value = "/driverBasicInfo")
    public Result<DriverBasicInfoResponseModel> driverBasicInfo() {
        return Result.success(driverBasicInfoBiz.driverBasicInfo());
    }

    /**
     * 司机基础信息提交
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "司机基础信息提交")
    @PostMapping(value = "/driverBasicInfoSubmit")
    public Result<Boolean> driverBasicInfoSubmit(@RequestBody DriverBasicInfoSubmitRequestModel requestModel) {
        driverBasicInfoBiz.driverBasicInfoSubmit(requestModel);
        return Result.success(true);
    }

    /**
     * 个人手机号认证-获取验证码
     *
     * @return
     */
    @ApiOperation(value = "个人手机号认证-获取验证码")
    @PostMapping(value = "/getPersonAuthVerifyCode")
    public Result<Boolean> getVerifyCode(@RequestBody GetPersonAuthVerifyCodeRequestModel requestModel) {
        driverBasicInfoBiz.getVerifyCode(requestModel);
        return Result.success(true);
    }

    /**
     * 个人二要素校验
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "个人二要素校验")
    @PostMapping(value = "/verifyPersonTwoElements")
    public Result<VerifyPersonTwoElementsResponseModel> verifyPersonTwoElements(@RequestBody VerifyPersonTwoElementsRequestModel requestModel) {
        return Result.success(driverBasicInfoBiz.verifyPersonTwoElements(requestModel));
    }

    /**
     * 个人三要素校验
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "个人三要素校验")
    @PostMapping(value = "/verifyPersonThreeElements")
    public Result<VerifyPersonThreeElementsResponseModel> verifyPersonThreeElements(@RequestBody VerifyPersonThreeElementsRequestModel requestModel) {
        return Result.success(driverBasicInfoBiz.verifyPersonThreeElements(requestModel));
    }

    /**
     * 获取刷脸认证签名
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取刷脸认证签名")
    @PostMapping(value = "/faceRecognition")
    public Result<FaceRecognitionResponseModel> faceRecognition(@RequestBody FaceRecognitionRequestModel requestModel) {
        return Result.success(driverBasicInfoBiz.faceRecognition(requestModel));
    }

    /**
     * 获取刷脸认证结果
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取刷脸认证结果")
    @PostMapping(value = "/faceRecognitionResult")
    public Result<FaceRecognitionResultResponseModel> faceRecognitionResult(@RequestBody FaceRecognitionResultRequestModel requestModel) {
        return Result.success(driverBasicInfoBiz.faceRecognitionResult(requestModel));
    }
}
