package com.logistics.tms.base.enums;

/**
 * <AUTHOR>
 * @date 2024/03/22
 */
public enum  InvoiceTypeEnum {
    DEFAULT(-99,""),
    //1 电子发票，2 纸质发票
    ELECTRONIC(1,"电子发票"),
    PAPER(2,"纸质发票"),

    ;

    private Integer key;
    private String value;

    InvoiceTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static InvoiceTypeEnum getEnum(Integer key) {
        for (InvoiceTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
