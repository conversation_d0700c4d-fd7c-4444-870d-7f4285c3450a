package com.logistics.management.webapi.controller.region.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2024/6/4 11:33
 */
@Data
public class GetCompanyCarrierByRegionRequestDto {

    /**
     * 省份id
     */
    @ApiModelProperty(value = "省份id",required = true)
    @NotBlank(message = "省份id不能为空")
    private String provinceId;
    /**
     * 城市id
     */
    @ApiModelProperty(value = "城市id",required = true)
    @NotBlank(message = "城市id不能为空")
    private String cityId;

}
