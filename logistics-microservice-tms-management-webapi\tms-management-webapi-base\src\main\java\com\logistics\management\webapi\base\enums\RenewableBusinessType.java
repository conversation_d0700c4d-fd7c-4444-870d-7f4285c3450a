package com.logistics.management.webapi.base.enums;

public enum RenewableBusinessType {

    COMPANY(1,"公司"),
    PERSON(2,"个人");

    private Integer code;
    private String name;

    RenewableBusinessType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
