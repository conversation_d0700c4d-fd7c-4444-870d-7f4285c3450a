package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/10/10 current system date
 */
@Data
public class CancelDemandOrderFromLeYiRequestModel {
    @ApiModelProperty("需求单号")
    private String demandCode;
    @ApiModelProperty("取消类型 1 我司原因，2 客户原因，3 不可抗力，4 物流原因，5 平台问题")
    private Integer cancelType;
    @ApiModelProperty("取消原因")
    private String cancelReason;
    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("操作类型：1 修改取消")
    private String operateType;
}
