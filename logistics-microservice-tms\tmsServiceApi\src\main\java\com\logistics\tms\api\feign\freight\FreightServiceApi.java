package com.logistics.tms.api.feign.freight;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.freight.hystrix.FreightServiceApiHystrix;
import com.logistics.tms.api.feign.freight.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/12/24 11:59
 */
@Api(value = "API-FreightApi-费用管理")
@FeignClient(name = "logistics-tms-services", fallback = FreightServiceApiHystrix.class)
public interface FreightServiceApi {

    @ApiOperation(value = "查询运价管理列表")
    @PostMapping(value = "/service/freight/searchList")
    Result<PageInfo<SearchFreightListResponseModel>> searchList(@RequestBody SearchFreightListRequestModel requestModel);

    @ApiOperation(value = "添加运价管理")
    @PostMapping(value = "/service/freight/addFreight")
    Result<Boolean> addFreight(@RequestBody AddFreightRequestModel requestModel);

    @ApiOperation(value = "启用/禁用")
    @PostMapping(value = "/service/freight/enableFreight")
    Result<Boolean> enableFreight(@RequestBody EnableFreightRequestModel requestModel);


    @ApiOperation(value = "运价地址列表")
    @PostMapping(value = "/service/freight/searchFreightAddressList")
    Result<PageInfo<SearchFreightAddressResponseModel>> searchFreightAddressList(@RequestBody SearchFreightAddressRequestModel requestModel);

    @ApiOperation(value = "添加/修改运价地址规则")
    @PostMapping(value = "/service/freight/addFreightAddressRule")
    Result<Boolean> addFreightAddressRule(@RequestBody AddOrModifyFreightAddressRuleRequestModel requestModel);

    @ApiOperation(value = "运价地址规则详情")
    @PostMapping(value = "/service/freight/getFreightRuleDetail")
    Result<FreightAddressRuleDetailResponseModel> getFreightRuleDetail(@RequestBody FreightAddressRuleDetailRequestModel requestModel);

    @ApiOperation(value = "删除运价地址规则")
    @PostMapping(value = "/service/freight/deleteFreightAddressRule")
    Result<Boolean> deleteFreightAddressRule(@RequestBody DeleteFreightAddressRequestModel requestModel);

    @ApiOperation(value = "统一加价/减价")
    @PostMapping(value = "/service/freight/modifyFreightPrice")
    Result<Boolean> modifyFreightPrice(@RequestBody ModifyFreightPriceRequestModel requestModel);

    @ApiOperation(value = "运价日志")
    @PostMapping(value = "/service/freight/freightLogs")
    Result<List<FreightLogsResponseModel>> freightLogs(@RequestBody FreightLogsRequestModel requestModel);

    @ApiOperation(value = "运价公司信息")
    @PostMapping(value = "/service/freight/getFreightCompanyInfo")
    Result<FreightCompanyInfoResponseModel> getFreightCompanyInfo(@RequestBody FreightCompanyInfoRequestModel requestModel);

    @ApiOperation(value = "调度车辆-司机运价")
    @PostMapping(value = "/service/freight/getDriverFreight")
    Result<DriverFreightByDemandOrderIdsAndVehicleResponseModel>  getDriverFreight(@RequestBody DriverFreightByDemandOrderIdsAndVehicleRequestModel requestModel);

    @ApiOperation(value = "委托发布-根据委托方地址和数量查询价格")
    @PostMapping(value = "/service/freight/getPriceByAddressAndAmount")
    Result<GetPriceByAddressAndAmountResponseModel> getPriceByAddressAndAmount(@RequestBody GetPriceByAddressAndAmountRequestModel requestModel);
}
