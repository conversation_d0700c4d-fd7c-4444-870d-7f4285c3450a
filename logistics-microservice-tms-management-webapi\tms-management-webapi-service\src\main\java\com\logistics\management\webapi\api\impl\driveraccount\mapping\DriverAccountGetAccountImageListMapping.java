package com.logistics.management.webapi.api.impl.driveraccount.mapping;

import com.logistics.management.webapi.api.feign.driveraccount.dto.response.DriverAccountImageResponseDto;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.tms.api.feign.driveraccount.model.response.DriverAccountImageResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/27
 */
public class DriverAccountGetAccountImageListMapping extends MapperMapping<DriverAccountImageResponseModel, DriverAccountImageResponseDto> {

	private final ConfigKeyConstant configKeyConstant;

	private final Map<String, String> imageMap;

	public DriverAccountGetAccountImageListMapping(ConfigKeyConstant configKeyConstant, Map<String, String> imageMap) {
		this.configKeyConstant = configKeyConstant;
		this.imageMap = imageMap;
	}

	@Override
	public void configure() {
		DriverAccountImageResponseModel source = getSource();
		DriverAccountImageResponseDto destination = getDestination();

		//账户图片
		List<String> bankAccountImages = source.getImagePaths();
		List<String> bankAccountImagesResult = new ArrayList<>();
		if (ListUtils.isNotEmpty(bankAccountImages)) {
			for (String bankAccountImage : bankAccountImages) {
				bankAccountImagesResult.add(configKeyConstant.fileAccessAddress + imageMap.get(bankAccountImage));
			}
		}
		destination.setImagePaths(bankAccountImagesResult);
	}
}
