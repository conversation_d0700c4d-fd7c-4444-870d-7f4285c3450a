package com.logistics.tms.api.feign.entrustsettlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/10/11 19:43
 */
@Data
public class ModifyCarrierSettlementCostModel {
    private Long demandOrderId;
    @ApiModelProperty("报价类型：1单价，2一口价")
    private Integer contractPriceType;
    @ApiModelProperty("结算费用")
    private BigDecimal settlementCostTotal;
    @ApiModelProperty("货主结算吨位类型")
    private Integer settlementTonnage;
    @ApiModelProperty("车主结算吨位类型")
    private Integer carrierSettlementTonnage;
    @ApiModelProperty("单位")
    private Integer goodsUnit;
    private String userName;
}
