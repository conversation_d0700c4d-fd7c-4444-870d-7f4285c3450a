package com.logistics.tms.biz.attendancechangeapply;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.attendancechangeapply.model.AttendanceChangeCancelModel;
import com.logistics.tms.biz.attendancerecord.handle.AttendancePunchHandle;
import com.logistics.tms.biz.attendancerecord.handle.AttendancePunchHandleFactory;
import com.logistics.tms.biz.attendancerecord.model.AttendanceClockModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.attendance.request.*;
import com.logistics.tms.controller.attendance.response.AttendanceChangeDetailResponseModel;
import com.logistics.tms.controller.attendance.response.AttendanceDetailResponseModel;
import com.logistics.tms.controller.attendance.response.SearchAttendanceChangeListResponseModel;
import com.logistics.tms.controller.attendance.response.UpdateAttendanceHistoryDetailResponseModel;
import com.logistics.tms.entity.TAttendanceChangeApply;
import com.logistics.tms.entity.TAttendanceRecord;
import com.logistics.tms.mapper.TAttendanceChangeApplyMapper;
import com.logistics.tms.mapper.TAttendanceRecordMapper;
import com.logistics.tms.mapper.TStaffBasicMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.ObjectUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AttendanceChangeApplyBiz {

    @Resource
    private CommonBiz commonBiz;
    @Resource
    private TAttendanceChangeApplyMapper attendanceChangeApplyMapper;
    @Resource
    private TAttendanceRecordMapper attendanceRecordMapper;
    @Resource
    private AttendancePunchHandleFactory attendancePunchHandleFactory;
    @Resource
    private TStaffBasicMapper staffBasicMapper;

    /**
     * 修改考勤记录申请
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public boolean updateAttendanceHistory(UpdateAttendanceHistoryRequestModel requestModel) {

        Date changePunchTime = DateUtils.stringToDate(requestModel.getChangePunchTime(), CommonConstant.DATE_TO_STRING_DETAIAL_PATTERN);

        // 考勤申请检查
        checkChangeApply(requestModel, changePunchTime);

        // 构建 Model
        TAttendanceChangeApply apply = new TAttendanceChangeApply();
        apply.setAttendanceRecordId(requestModel.getAttendanceRecordId());
        apply.setChangeType(requestModel.getChangeType());
        apply.setChangeReason(requestModel.getChangeReason());
        apply.setChangePunchTime(changePunchTime);
        commonBiz.setBaseEntityAdd(apply, BaseContextHandler.getUserName());
        return attendanceChangeApplyMapper.insertSelective(apply) > CommonConstant.INTEGER_ZERO;
    }

    // 获取员工id
    private Long getLoginDriverAppletUserId() {
        // 获取员工id
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
        }
        return loginDriverAppletUserId;
    }

    // 考勤记录申请变更校验
    private void checkChangeApply(UpdateAttendanceHistoryRequestModel requestModel, Date changePunchTime) {

        boolean isOnDutyWorkChange = AttendanceChangeTypeEnum.ON_DUTY_WORK_APPLY.getKey().equals(requestModel.getChangeType());

        // 获取员工id
        Long loginDriverAppletUserId = getLoginDriverAppletUserId();

        // 查询员工身份是否变更
        staffBasicMapper.getStaffByIds(loginDriverAppletUserId.toString())
                .stream()
                // 过滤出自营自主员工
                .filter(f -> {
                    return StaffPropertyEnum.OWN_STAFF.getKey().equals(f.getStaffProperty()) ||
                            StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(f.getStaffProperty());
                })
                .findFirst().orElseThrow(() -> new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST));

        // 查询记录是否可以申请变更
        TAttendanceRecord record = Optional.ofNullable(attendanceRecordMapper.selectOneByIdAndStaffId(requestModel.getAttendanceRecordId(),
                        loginDriverAppletUserId))
                .filter(f -> {
                    // 如果是下班打卡变更校验是否下班打卡
                    return isOnDutyWorkChange || ObjectUtils.isNotEmpty(f.getOffDutyPunchTime());
                }).orElseThrow(() -> new BizException(CarrierDataExceptionEnum.ATTENDANCE_RECORD_NOT_EXISTS));

        // 判断是否修改日期是否是当日 || 考勤记录是否是本月数据
        LocalDate attendanceLocalDate = LocalDate.ofInstant(record.getAttendanceDate().toInstant(), ZoneId.systemDefault());
        LocalDate currentLocalDate = LocalDate.now();
        if (currentLocalDate.getYear() != attendanceLocalDate.getYear() || currentLocalDate.getMonthValue() != attendanceLocalDate.getMonthValue()) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_APPLY_NOT_CURRENT_MONTH);
        }
        // 修改是否跨天
        LocalDateTime changePunchLocalDateTime = LocalDateTime.ofInstant(changePunchTime.toInstant(), ZoneId.systemDefault());
        if (!attendanceLocalDate.isEqual(changePunchLocalDateTime.toLocalDate())) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_APPLY_TIME_CROSS);
        }
        // 待审核变更校验
        LocalDateTime checkChangePunchTime = null;
        List<Long> recordIds = Collections.singletonList(requestModel.getAttendanceRecordId());
        List<TAttendanceChangeApply> auditWaitApplyList = attendanceChangeApplyMapper.selectByAttendanceRecordIdIn(recordIds)
                .stream()
                // 筛选待审核状态
                .filter(f -> AttendanceChangeAuditStatusEnum.AUDIT_WAIT.getKey().equals(f.getAuditStatus()))
                .collect(Collectors.toList());
        if (ListUtils.isNotEmpty(auditWaitApplyList)) {
            // 判断是否有待审核记录
            long existsApply = auditWaitApplyList.stream()
                    // 筛选相同的打卡类型
                    .filter(f -> f.getChangeType().equals(requestModel.getChangeType()))
                    .count();
            // 判断是否是相同的打卡类型申请
            if (existsApply > CommonConstant.INTEGER_ZERO) {
                throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_APPLY_EXISTS);
            }

            Integer checkType = isOnDutyWorkChange ?
                    AttendanceChangeTypeEnum.OFF_DUTY_WORK_APPLY.getKey() : AttendanceChangeTypeEnum.ON_DUTY_WORK_APPLY.getKey();
            checkChangePunchTime = auditWaitApplyList.stream()
                    .filter(f -> AttendanceChangeAuditStatusEnum.AUDIT_WAIT.getKey().equals(f.getAuditStatus()))
                    .filter(f -> f.getChangeType().equals(checkType))
                    .findFirst()
                    .map(s -> LocalDateTime.ofInstant(s.getChangePunchTime().toInstant(), ZoneId.systemDefault()))
                    .get();
        }

        if (ObjectUtils.isEmpty(record.getOffDutyPunchTime())) {
            return;
        }

        /*
         * 校验变更打卡时间是否异常
         * 1.上班变更 ->  变更时间必须在下班打卡之前
         * 2.上班变更(存在下班打卡变更) -> 变更时间必须在下班打卡变更之前
         * 3.下班变更 ->  变更时间必须在上班打卡之后
         * 4.下班变更(存在下班打卡变更) ->  变更时间必须在上班打卡变更之后
         * 5.变更申请不能夸天，只能是当天
         */
        if (ObjectUtils.isEmpty(checkChangePunchTime)) {
            Date dutyPunchDateTime = isOnDutyWorkChange ? record.getOffDutyPunchTime() : record.getOnDutyPunchTime();
            checkChangePunchTime = LocalDateTime.ofInstant(dutyPunchDateTime.toInstant(), ZoneId.systemDefault());
        }

        boolean isChangeException = isOnDutyWorkChange ?
                !changePunchLocalDateTime.isBefore(checkChangePunchTime) : !changePunchLocalDateTime.isAfter(checkChangePunchTime);
        if (isChangeException) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_APPLY_TIME_CROSS);
        }
    }

    /**
     * 用户 - 撤销考勤变更申请
     *
     * @param requestModel
     * @return boolean
     */
    @Transactional
    public boolean cancelUpdateAttendanceHistory(CancelUpdateAttendanceHistoryRequestModel requestModel) {

        // 查询申请是否存在
        TAttendanceChangeApply changeApply = getOneChangeApply(requestModel.getAttendanceChangeApplyId())
                .filter(f -> {
                    return attendanceRecordMapper.countByIdAndStaffId(f.getAttendanceRecordId(), getLoginDriverAppletUserId())
                            > CommonConstant.LONG_ZERO;
                })
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_NOT_EXISTS));
        // 撤销校验
        if (!(AttendanceChangeAuditStatusEnum.AUDIT_WAIT.getKey().equals(changeApply.getAuditStatus()) ||
                AttendanceChangeAuditStatusEnum.AUDIT_REJECT.getKey().equals(changeApply.getAuditStatus()))) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_NOT_UNDO);
        }

        // 撤销申请
        AttendanceChangeCancelModel cancelModel = MapperUtils.mapper(requestModel, AttendanceChangeCancelModel.class);
        cancelModel.setCurrentAuditStatus(changeApply.getAuditStatus());
        cancelChangeApply(cancelModel);
        return true;
    }

    // 撤销考勤变更申请
    private void cancelChangeApply(AttendanceChangeCancelModel cancelBoModel) {
        String userName = BaseContextHandler.getUserName();
        // 构建撤销申请Model
        TAttendanceChangeApply apply = new TAttendanceChangeApply();
        apply.setAuditStatus(AttendanceChangeAuditStatusEnum.AUDIT_UNDO.getKey());
        apply.setRemark(cancelBoModel.getRemark());
        apply.setAuditTime(new Date());
        apply.setAuditorName(userName);
        commonBiz.setBaseEntityModify(apply, userName);
        attendanceChangeApplyMapper.updateByIdAndAuditStatus(apply, cancelBoModel.getAttendanceChangeApplyId(),
                cancelBoModel.getCurrentAuditStatus());
    }

    // 获取申请记录
    private Optional<TAttendanceChangeApply> getOneChangeApply(Long attendanceChangeApplyId) {
        // 查询申请是否存在
        List<Long> changeIds = Collections.singletonList(attendanceChangeApplyId);
        List<TAttendanceChangeApply> changeApplyList = attendanceChangeApplyMapper.selectAllByIdIn(changeIds);
        return changeApplyList.stream()
                .findFirst();
    }


    /**
     * 查询考勤变更详情
     *
     * @param requestModel
     * @return UpdateAttendanceHistoryDetailResponseModel
     */
    public UpdateAttendanceHistoryDetailResponseModel updateAttendanceHistoryDetail(UpdateAttendanceHistoryDetailRequestModel requestModel) {

        Long loginDriverAppletUserId = getLoginDriverAppletUserId();
        TAttendanceRecord record = attendanceRecordMapper.selectOneByIdAndStaffId(requestModel.getAttendanceRecordId(),
                loginDriverAppletUserId);
        return Optional.ofNullable(record)
                .map(r -> {
                    // 填充考勤数据
                    UpdateAttendanceHistoryDetailResponseModel responseModel = new UpdateAttendanceHistoryDetailResponseModel();
                    responseModel.setAttendanceRecordId(record.getId());
                    responseModel.setAttendanceDate(record.getAttendanceDate());
                    responseModel.setOnDutyPunchTime(record.getOnDutyPunchTime());
                    responseModel.setOnDutyPunchLocation(record.getOnDutyPunchLocation());
                    responseModel.setOffDutyPunchTime(record.getOffDutyPunchTime());
                    responseModel.setOffDutyPunchLocation(record.getOffDutyPunchLocation());
                    responseModel.setManHour(record.getManHour());
                    List<Long> recordIds = Collections.singletonList(record.getId());

                    // 查询变更申请详情
                    attendanceChangeApplyMapper.selectByAttendanceRecordIdIn(recordIds)
                            .stream()
                            .filter(f -> requestModel.getChangeType().equals(f.getChangeType()))
                            .filter(f -> {
                                return AttendanceChangeAuditStatusEnum.AUDIT_WAIT.getKey().equals(f.getAuditStatus()) ||
                                AttendanceChangeAuditTypeEnum.AUDIT_REJECTED.getKey().equals(f.getAuditStatus());
                            })
                            .findFirst()
                            .ifPresentOrElse(change -> {
                                // 填充申请变更数据
                                responseModel.setAttendanceChangeApplyId(change.getId());
                                responseModel.setChangeType(change.getChangeType());
                                responseModel.setChangePunchTime(change.getChangePunchTime());
                                responseModel.setChangeReason(change.getChangeReason());
                            }, () -> {
                                if (AttendanceChangeApplyStatusEnum.HAVE_APPLY.getKey().equals(requestModel.getChangeStatus())) {
                                    throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_HAD_REVIEWED);
                                }
                            });
                    return responseModel;
                })
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.ATTENDANCE_RECORD_NOT_EXISTS));
    }

    /**
     * 考勤变更申请列表
     * @param requestModel
     * @return PageInfo<SearchAttendanceChangeListResponseModel>
     */
    public PageInfo<SearchAttendanceChangeListResponseModel> searchAttendanceChangeList(SearchAttendanceChangeListRequestModel requestModel) {

        requestModel.enablePaging();
        List<SearchAttendanceChangeListResponseModel> changeAppleList = attendanceChangeApplyMapper.selectSearchAttendanceChangeList(requestModel);
        if (ListUtils.isEmpty(changeAppleList)) {
            return new PageInfo<>(Collections.emptyList());
        }
        return new PageInfo<>(changeAppleList);
    }

    /**
     * 考勤变更详情
     * @param requestModel
     * @return AttendanceChangeDetailResponseModel
     */
    public AttendanceChangeDetailResponseModel attendanceChangeDetail(AttendanceChangeDetailRequestModel requestModel) {

        // 变更记录
        List<Long> changeApplyIds = Collections.singletonList(requestModel.getAttendanceChangeApplyId());
        return attendanceChangeApplyMapper.selectAllByIdIn(changeApplyIds)
                .stream()
                .findFirst()
                .map(s -> {
                    // 构建详情model
                    AttendanceChangeDetailResponseModel responseModel = new AttendanceChangeDetailResponseModel();
                    responseModel.setChangeType(s.getChangeType());
                    responseModel.setChangePunchTime(s.getChangePunchTime());
                    responseModel.setChangeReason(s.getChangeReason());
                    responseModel.setAuditorName(s.getAuditorName());
                    responseModel.setAuditTime(s.getAuditTime());
                    responseModel.setAuditStatus(s.getAuditStatus());
                    responseModel.setRemark(s.getRemark());
                    // 查询考勤信息
                    AttendanceDetailResponseModel attendanceDetail = attendanceRecordMapper.selectOneDetailById(s.getAttendanceRecordId());
                    if (ObjectUtils.isNotEmpty(attendanceDetail)) {
                        responseModel.setStaffName(attendanceDetail.getStaffName());
                        responseModel.setStaffMobile(attendanceDetail.getStaffMobile());
                        responseModel.setStaffProperty(attendanceDetail.getStaffProperty());
                        responseModel.setAttendanceDate(attendanceDetail.getAttendanceDate());
                        responseModel.setOnDutyPunchTime(attendanceDetail.getOnDutyPunchTime());
                        responseModel.setOnDutyPunchPic(attendanceDetail.getOnDutyPunchPic());
                        responseModel.setOffDutyPunchTime(attendanceDetail.getOffDutyPunchTime());
                        responseModel.setOffDutyPunchPic(attendanceDetail.getOffDutyPunchPic());
                    }
                    return responseModel;
                })
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_NOT_EXISTS));
    }

    /**
     * 考勤变更审核 通过 | 驳回
     * @param requestModel
     * @return boolean
     */
    @Transactional
    public boolean auditAttendanceChangeApply(AuditAttendanceChangeApplyRequestModel requestModel) {

        // 查询变更详情
        TAttendanceChangeApply changeApply = getOneChangeApply(requestModel.getAttendanceChangeApplyId())
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_NOT_EXISTS));

        // 变更状态校验
        checkChangeApplyAuditStatus(requestModel.getAuditType(), changeApply.getAuditStatus());

        // 更新打卡记录
        if (AttendanceChangeAuditTypeEnum.AUDIT_THROUGH.getKey().equals(requestModel.getAuditType())) {
            updateDutyPunch(changeApply.getAttendanceRecordId(), changeApply.getChangePunchTime(), changeApply.getChangeType());
        }

        // 更新审核状态
        TAttendanceChangeApply updateChangeApply = new TAttendanceChangeApply();
        updateChangeApply.setAuditStatus(requestModel.getAuditType());
        updateChangeApply.setRemark(requestModel.getRemark());
        updateChangeApply.setAuditTime(new Date());
        updateChangeApply.setAuditorName(BaseContextHandler.getUserName());
        commonBiz.setBaseEntityModify(updateChangeApply, BaseContextHandler.getUserName());
        int count = attendanceChangeApplyMapper.updateByIdAndAuditStatus(updateChangeApply, changeApply.getId(), changeApply.getAuditStatus());
        if (count <= CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_FAILURE);
        }
        return true;
    }

    // 审核状态校验
    private void checkChangeApplyAuditStatus(Integer auditStatus, Integer currentAuditStatus) {

        // 员工已撤销
        if (AttendanceChangeAuditStatusEnum.AUDIT_UNDO.getKey().equals(currentAuditStatus)) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_HAD_WITHDRAWN);
        }

        // 审核 | 驳回 操作
        if (AttendanceChangeAuditTypeEnum.AUDIT_THROUGH.getKey().equals(auditStatus) ||
                AttendanceChangeAuditTypeEnum.AUDIT_REJECTED.getKey().equals(auditStatus)) {
            // 记录状态为已审核
            if (!AttendanceChangeAuditStatusEnum.AUDIT_WAIT.getKey().equals(currentAuditStatus)) {
                throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_HAD_REVIEWED);
            }
        }
    }

    // 更新打卡记录
    private void updateDutyPunch(Long recodeId, Date changePunchTime, Integer changeType) {

        AttendanceClockModel model = new AttendanceClockModel();
        model.setRecodeId(recodeId);

        if (AttendanceChangeTypeEnum.ON_DUTY_WORK_APPLY.getKey().equals(changeType)) {
            model.setOnDutyPunchTime(changePunchTime);
        } else {
            model.setOffDutyPunchTime(changePunchTime);
        }
        AttendancePunchHandle dutyPunchHandle = attendancePunchHandleFactory.getHandle(AttendancePunchTypeEnum.UPDATE_DUTY_CLOCK_IN.getKey());
        boolean isSuccess = dutyPunchHandle.handle(model);
        if (!isSuccess) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_FAILURE);
        }
    }

    /**
     * 后台考勤变更申请撤销
     * @param requestModel
     * @return boolean
     */
    @Transactional
    public boolean cancelAttendanceChangeApply(CancelAttendanceChangeApplyRequestModel requestModel) {

        // 查询变更详情
        Integer currentAuditStatus = getOneChangeApply(requestModel.getAttendanceChangeApplyId())
                .map(TAttendanceChangeApply::getAuditStatus)
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_NOT_EXISTS));

        // 撤销校验
        if (!(AttendanceChangeAuditStatusEnum.AUDIT_WAIT.getKey().equals(currentAuditStatus) ||
                AttendanceChangeAuditStatusEnum.AUDIT_REJECT.getKey().equals(currentAuditStatus))) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CHANGE_NOT_UNDO);
        }

        // 撤销申请
        AttendanceChangeCancelModel cancelModel = MapperUtils.mapper(requestModel, AttendanceChangeCancelModel.class);
        cancelModel.setCurrentAuditStatus(currentAuditStatus);
        cancelChangeApply(cancelModel);
        return true;
    }
}
