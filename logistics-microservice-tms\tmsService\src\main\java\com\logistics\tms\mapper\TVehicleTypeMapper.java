package com.logistics.tms.mapper;

import com.logistics.tms.controller.vehicletype.request.VehicleTypeListRequestModel;
import com.logistics.tms.controller.vehicletype.response.GetVehicleTypeSearchByNameResponseModel;
import com.logistics.tms.controller.vehicletype.response.VehicleTypeListResponseModel;
import com.logistics.tms.entity.TVehicleType;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TVehicleTypeMapper extends BaseMapper<TVehicleType>{
    List<GetVehicleTypeSearchByNameResponseModel> fuzzyVehicleType(@Param("vehicleType") String vehicleType);

    List<VehicleTypeListResponseModel> searchVehicleTypeList(@Param("params") VehicleTypeListRequestModel requestModel);

    int batchInsert(@Param("list") List<TVehicleType> vehicleTypeList);

    List<Long> searchVehicleTypeIdList(@Param("params") VehicleTypeListRequestModel requestModel);

    List<VehicleTypeListResponseModel> searchVehicleType(@Param("ids") String ids);

    TVehicleType selectListByType(@Param("vehicleType") String vehicleType);

    List<TVehicleType> findListByType(@Param("vehicleType") String vehicleType);

    int batchUpdate(@Param("list") List<TVehicleType> vehicleTypeList);

    TVehicleType selectListByTypeAndCategory(@Param("vehicleType") String vehicleType,@Param("vehicleCategory") Integer vehicleCategory);

    List<TVehicleType> selectByVehicleTypes(@Param("vehicleTypes") String vehicleTypes);
}