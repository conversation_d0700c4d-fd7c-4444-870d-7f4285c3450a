package com.logistics.tms.biz.carrierfreight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AddressConfigLadderModel {

    @ApiModelProperty("阶梯id")
    private Long ladderId;

    /**
     * 模式Id; 路线配置Id; 方案配置id;
     */
    @ApiModelProperty("模式Id; 路线配置Id; 方案配置id;")
    private Long modeId;

    /**
     * 价格模式; 1: 单价; 2: 总价;
     */
    @ApiModelProperty("价格模式; 1: 单价; 2: 总价;")
    private Integer priceMode;

    /**
     * 阶梯模式; 1: 路线配置; 2: 方案配置;
     */
    @ApiModelProperty("阶梯模式; 1: 路线配置; 2: 方案配置;")
    private Integer ladderMode;

    /**
     * 阶梯层级; 0: 无阶梯
     */
    @ApiModelProperty("阶梯层级; 0: 无阶梯")
    private Integer ladderLevel;

    /**
     * 阶梯层级父Id
     */
    @ApiModelProperty("阶梯层级父Id")
    private Long ladderPid;

    /**
     * 阶梯类型; 1: KM(公里); 2: 数量;
     */
    @ApiModelProperty("阶梯类型; 1: KM(公里); 2: 数量;")
    private Integer ladderType;

    /**
     * 阶梯起始
     */
    @ApiModelProperty("阶梯起始")
    private BigDecimal ladderFrom;

    /**
     * 阶梯终止（包含等于）
     */
    @ApiModelProperty("阶梯终止（包含等于）")
    private BigDecimal ladderTo;

    /**
     * 单位; 1: 件; 2: 吨; 3: 方; 4: 块
     */
    @ApiModelProperty("单位; 1: 件; 2: 吨; 3: 方; 4: 块")
    private Integer ladderUnit;

    /**
     * 价格
     */
    @ApiModelProperty("价格")
    private BigDecimal unitPrice;
}
