package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.enums.CarrierOrderTicketsTypeEnum;
import com.logistics.management.webapi.client.carrierorder.response.TicketsModel;
import com.logistics.management.webapi.controller.carrierorder.response.TicketsForYeloLifeDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/20
 */
public class GetTicketsForYeloLifeMapping extends MapperMapping<TicketsModel, TicketsForYeloLifeDto> {

	private String imagePrefix;
	private Map<String, String> imageMap;

	public GetTicketsForYeloLifeMapping(String imagePrefix, Map<String, String> imageMap) {
		this.imagePrefix = imagePrefix;
		this.imageMap = imageMap;
	}

	@Override
	public void configure() {
		TicketsModel source = getSource();
		TicketsForYeloLifeDto destination = getDestination();
		if (StringUtils.isNotBlank(source.getImagePath())) {
			destination.setImagePath(imagePrefix + imageMap.get(source.getImagePath()));
			destination.setImageTypeLabel(CarrierOrderTicketsTypeEnum.getEnum(source.getImageType()).getValue());
		}
	}
}
