package com.logistics.tms.biz.attendancechangeapply.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AttendanceChangeAuditModel {

    @ApiModelProperty("考情变更申请ID")
    private Long attendanceChangeApplyId;

    @ApiModelProperty(value = "审核类型: 1:通过 ,2:驳回", required = true)
    private Integer auditStatus;

    //审核备注,驳回时必填
    @ApiModelProperty(value = "审核备注,驳回时必填,1-100个字符")
    private String remark;
}
