/**
 * Created by yun.<PERSON><PERSON> on 2017/10/23.
 */
package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ConfigKeyEnum {

    /**
     * 微信推送角色名称
     */
    SENDER_ROLE_NAME(ConfigGroupCodeEnum.WECHAT_SENDER_ROLE.getCode(), "weixin_role_sender", "微信推送发货人角色名"),
    RECEIVER_ROLE_NAME(ConfigGroupCodeEnum.WECHAT_SENDER_ROLE.getCode(), "weixin_role_receiver", "微信推送收货人角色名"),
    ENTRUST_ROLE_NAME(ConfigGroupCodeEnum.WECHAT_SENDER_ROLE.getCode(), "weixin_role_entrust", "微信推送委托方角色名"),
    OTHER_ROLE_NAME(ConfigGroupCodeEnum.WECHAT_SENDER_ROLE.getCode(), "weixin_role_other", "微信推送其他角色名"),

    /**
     * 公司名称
     */
    QIYA_COMPANY_NAME(ConfigGroupCodeEnum.COMPANY_NAME.getCode(), "qiya_company_name", "江苏乐橘云途公司名"),
    YANGBA_COMPANY_NAME(ConfigGroupCodeEnum.COMPANY_NAME.getCode(), "yangba_company_name", "扬巴公司名"),
    LEYI_COMPANY_NAME(ConfigGroupCodeEnum.COMPANY_NAME.getCode(), "leyi_company_name", "乐橘云盘公司名"),
    YELO_LIFE_COMPANY_NAME(ConfigGroupCodeEnum.COMPANY_NAME.getCode(), "yelolife_company_name", "乐橘新生公司名称"),
    SH_YELO_TOUR_COMPANY_NAME(ConfigGroupCodeEnum.COMPANY_NAME.getCode(), "sh_yelotour_company_name", "上海乐橘云途公司名"),

    /**
     * 中石化拉取订单
     */
    KEY_API_QUERY_ORDER_DATE_INTERVAL(ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode(), "key_api_query_order_date_interval", "中石化接口查询条件间隔日期"),
    KEY_API_QUERY_ORDER_FROM_DATE(ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode(), "key_api_query_order_from_date", "中石化接口查询条件开始时间"),
    KEY_CAS_LOGIN_PAGE_URL(ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode(), "sinopec_key_cas_login_page_url", "中石化登陆页url"),
    KEY_CAS_LOGIN_URL(ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode(), "sinopec_key_cas_login_url", "中石化登陆接口url"),
    KEY_API_LOGIN_PAGE_URL(ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode(), "sinopec_key_api_login_page_url", "中石化获取APIjesessionid url"),
    KEY_API_QUERY_ORDER_URL(ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode(), "sinopec_key_api_query_order_url", "中石化查询需求单url"),
    KEY_USER_NAME(ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode(), "sinopec_key_user_name", "中石化用户名"),
    KEY_PASSWORD(ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode(), "sinopec_key_password", "中石化密码"),
    KEY_API_CHECK_ORDER_DETAIL_URL(ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode(), "sinopec_key_api_check_order_detail_url", "中石化查询需求单获取价格url"),
    KEY_API_QUERY_ORDER_SWITCH(ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode(), "key_api_query_order_switch", "从中石化拉取单子开关：0 关，1 开"),
    SINOPEC_METHOD_SWITCH(ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode(), "sinopec_method_switch", "调用中石化接口开关：0 关，1 开"),

    /**
     * 短信模板
     */
    SMS_VERIFICATION_CODE_TEMPLATE(ConfigGroupCodeEnum.SMS_TEMPLATE.getCode(), "sms_verification_code_template", "验证码短信模板"),
    SMS_FIND_PASS_VERIFICATION_CODE_TEMPLATE(ConfigGroupCodeEnum.SMS_TEMPLATE.getCode(), "sms_find_pass_verification_code_template", "找回密码验证码短信模板"),

    /**
     * 回寄地址
     */
    APPLET_RETURN_ADDRESS_CONFIG(ConfigGroupCodeEnum.RETURN_ADDRESS.getCode(), "applet_return_address", "回单寄送地址-小程序端"),
    AUTHORIZATION_RETURN_ADDRESS_CONFIG(ConfigGroupCodeEnum.RETURN_ADDRESS.getCode(), "authorization_return_address", "授权书邮寄地址-车主端"),

    /**
     * 客服电话
     */
    CUSTOMER_SERVICE_PHONE_CONFIG(ConfigGroupCodeEnum.CUSTOMER_SERVICE_PHONE.getCode(), "app_service_phone", "客服电话"),

    /**
     * websocket
     */
    WEBSOCKET_DEMAND_ORDER_BIDDING_PUBLISH(ConfigGroupCodeEnum.WEB_SOCKET.getCode(), "websocket_demand_order_bidding_publish", "需求单竞价发布"),
    WEBSOCKET_BIDDING_ORDER_SELECT_CARRIER(ConfigGroupCodeEnum.WEB_SOCKET.getCode(), "websocket_bidding_order_select_carrier", "竞价单选择车主"),
    WEBSOCKET_BIDDING_ORDER_CARRIER_QUOTE(ConfigGroupCodeEnum.WEB_SOCKET.getCode(), "websocket_bidding_order_carrier_quote", "竞价单车主报价"),

    /**
     * Default
     */
    MAP_STOP_TIME_THRESHOLD(ConfigGroupCodeEnum.DEFAULT.getCode(), "map_stop_time_threshold", "查询车辆轨迹停车阈值"),

    /**
     *  预约控制
     */
    RESERVATION_VERIFY(ConfigGroupCodeEnum.RESERVATION_VERIFY.getCode(), "reservation_verify", "访客预约是否开启验证码"),

    /**
     *  编码规则
     */
    TRAY_CODE_RULE(ConfigGroupCodeEnum.CODE_RULE.getCode(), "tray_code_rule", "托盘编码规则"),


    /**
     *  编码核销开关
     */
    DEMAND_SOURCE_FLAG(ConfigGroupCodeEnum.CODE_VERIFY_FLAG.getCode(), "demand_source_flag", "项目标签（多个标签,拼接）：1 石化板块，2 轮胎板块，3 涂料板块，4 其他"),
    DRIVER_TYPE(ConfigGroupCodeEnum.CODE_VERIFY_FLAG.getCode(), "driver_type", "人员机构：1 自主，2 外部，3 自营"),
    DRIVER_STATUS(ConfigGroupCodeEnum.CODE_VERIFY_FLAG.getCode(), "driver_status", "0 待实名 1 实名中 2 已实名"),
    DEMAND_SOURCE_TYPE(ConfigGroupCodeEnum.CODE_VERIFY_FLAG.getCode(), "demand_source_type", "对应需求单的类型枚举"),


    /**
     * 回退需求单开关
     */
    ROLLBACK_DEMAND_ORDER_FLAG(ConfigGroupCodeEnum.DEMAND_ORDER_FLAG.getCode(), "roll_back_demand_order_flag", "回退需求单开关"),



    /**
     *  补单相关配置
     */
    EXT_DEMAND_ORDER_COMPANY(ConfigGroupCodeEnum.EXT_RECYCLE_DEMAND_ORDER.getCode(), "ext_demand_order_company", "待核销专用客户"),
    EXT_DEMAND_ORDER_ADDRESS(ConfigGroupCodeEnum.EXT_RECYCLE_DEMAND_ORDER.getCode(), "ext_demand_order_address", "待核销专用仓库"),
    ;




    private final String groupCode;
    private final String value;
    private final String remark;

    public enum ConfigGroupCodeEnum {

        DEFAULT,
        WECHAT_SENDER_ROLE,
        COMPANY_NAME,
        SINOPEC_CONFIG,
        SMS_TEMPLATE,
        RETURN_ADDRESS,
        CUSTOMER_SERVICE_PHONE,
        WEB_SOCKET,
        RESERVATION_VERIFY,
        CODE_RULE,
        CODE_VERIFY_FLAG,
        DEMAND_ORDER_FLAG,
        EXT_RECYCLE_DEMAND_ORDER
        ;

        public String getCode() {
            return name().toLowerCase();
        }
    }
}
