package com.logistics.tms.mapper;

import com.logistics.tms.controller.driversafemeeting.request.AppletSafeMeetingListRequestModel;
import com.logistics.tms.controller.driversafemeeting.request.DriverSafeMeetingDetailRequestModel;
import com.logistics.tms.controller.driversafemeeting.response.*;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TDriverSafeMeetingRelation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TDriverSafeMeetingRelationMapper extends BaseMapper<TDriverSafeMeetingRelation> {

    int batchInsert(@Param("list")List<TDriverSafeMeetingRelation> list);

    List<DriverSafeMeetingDetailResponseModel> driverSafeMeetingDetailList(@Param("params") DriverSafeMeetingDetailRequestModel requestModel);

    DriverSafeMeetingListCountResponseModel driverSafeMeetingListCount(@Param("params") DriverSafeMeetingDetailRequestModel requestModel);

    List<AppletSafeMeetingListResponseModel> appletSafeMeetingList(@Param("params") AppletSafeMeetingListRequestModel requestModel, @Param("driverId")Long driverId);

    DriverSafeMeetingListCountResponseModel appletSafeMeetingListCount(@Param("driverId")Long driverId);

    AppletSafeMeetingDetailResponseModel appletSafeMeetingDetail(@Param("id")Long id, @Param("driverId")Long driverId);

    List<TDriverSafeMeetingRelation> getBySafeMeetingId(@Param("safeMeetingId")Long safeMeetingId);

    int batchUpdateForStudySignTimeNull(@Param("list")List<TDriverSafeMeetingRelation> list);

    List<DriverSafeMeetingKanBanItemResponseModel> getDriverSafeMeetingKanBanItem(@Param("safeMeetingIds")String safeMeetingIds);
}