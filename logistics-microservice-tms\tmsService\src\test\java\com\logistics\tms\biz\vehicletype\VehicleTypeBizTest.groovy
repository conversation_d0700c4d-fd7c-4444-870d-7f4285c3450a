package com.logistics.tms.biz.vehicletype

import com.logistics.tms.controller.vehicletype.request.AddOrModifyVehicleTypeRequestModel
import com.logistics.tms.controller.vehicletype.request.EnableVehicleTypeModel
import com.logistics.tms.controller.vehicletype.request.GetVehicleTypeSearchByNameRequestModel
import com.logistics.tms.controller.vehicletype.response.GetVehicleTypeSearchByNameResponseModel
import com.logistics.tms.controller.vehicletype.request.ImportVehicleTypeRequestModel
import com.logistics.tms.controller.vehicletype.request.VehicleTypeDetailRequestModel
import com.logistics.tms.controller.vehicletype.response.VehicleTypeDetailResponseModel
import com.logistics.tms.controller.vehicletype.request.VehicleTypeListRequestModel
import com.logistics.tms.controller.vehicletype.response.VehicleTypeListResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.controller.vehicletype.response.ImportVehicleTypeResponseModel
import com.logistics.tms.entity.TVehicleType
import com.logistics.tms.mapper.TVehicleTypeMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class VehicleTypeBizTest extends Specification {
    @Mock
    TVehicleTypeMapper tVehicleTypeMapper
    @Mock
    CommonBiz commonBiz
    @InjectMocks
    VehicleTypeBiz vehicleTypeBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "fuzzy Vehicle Type where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleTypeMapper.fuzzyVehicleType(anyString())).thenReturn([new GetVehicleTypeSearchByNameResponseModel()])

        expect:
        vehicleTypeBiz.fuzzyVehicleType(requestModel) == expectedResult

        where:
        requestModel                                 || expectedResult
        new GetVehicleTypeSearchByNameRequestModel() || [new GetVehicleTypeSearchByNameResponseModel()]
    }

    @Unroll
    def "search Vehicle Type List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleTypeMapper.searchVehicleTypeIdList(any())).thenReturn([1l])
        when(tVehicleTypeMapper.searchVehicleType(anyString())).thenReturn([new VehicleTypeListResponseModel()])

        expect:
        vehicleTypeBiz.searchVehicleTypeList(requestModel) == expectedResult

        where:
        requestModel                      || expectedResult
        new VehicleTypeListRequestModel() || null
    }

    @Unroll
    def "add Or Modify Vehicle Type where requestModel=#requestModel"() {
        given:
        when(tVehicleTypeMapper.selectListByType(anyString())).thenReturn(new TVehicleType(vehicleType: "vehicleType", vehicleCategory: 0, remark: "remark", addUserId: 1l, addUserName: "addUserName", source: 0))

        expect:
        vehicleTypeBiz.addOrModifyVehicleType(requestModel)
        assert expectedResult == false

        where:
        requestModel                             || expectedResult
        new AddOrModifyVehicleTypeRequestModel() || true
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        expect:
        vehicleTypeBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                        || expectedResult
        new VehicleTypeDetailRequestModel() || new VehicleTypeDetailResponseModel()
    }

    @Unroll
    def "enable Or Disable where requestModel=#requestModel"() {
        expect:
        vehicleTypeBiz.enableOrDisable(requestModel)
        assert expectedResult == false

        where:
        requestModel                 || expectedResult
        new EnableVehicleTypeModel() || true
    }

    @Unroll
    def "import Vehicle Type where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleTypeMapper.searchVehicleTypeList(any())).thenReturn([new VehicleTypeListResponseModel()])
        when(tVehicleTypeMapper.batchInsert(any())).thenReturn(0)
        when(tVehicleTypeMapper.batchUpdate(any())).thenReturn(0)

        expect:
        vehicleTypeBiz.importVehicleType(requestModel) == expectedResult

        where:
        requestModel                        || expectedResult
        new ImportVehicleTypeRequestModel() || new ImportVehicleTypeResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme