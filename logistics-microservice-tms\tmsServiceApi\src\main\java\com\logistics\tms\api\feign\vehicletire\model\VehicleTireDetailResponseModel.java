package com.logistics.tms.api.feign.vehicletire.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class VehicleTireDetailResponseModel {
    @ApiModelProperty("轮胎管理信息Id")
    private Long vehicleTireId;
    @ApiModelProperty("司机Id")
    private Long staffId;
    @ApiModelProperty("车辆Id")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("挂车ID")
    private Long trailerVehicleId;
    @ApiModelProperty("挂车号，默认空字符串")
    private String trailerVehicleNo;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driveMobile;
    @ApiModelProperty("更换日期")
    private Date replaceDate;
    @ApiModelProperty("轮胎企业")
    private String tireCompany;
    @ApiModelProperty("轮胎牌号列表")
    private List<VehicleTireNoListResponseModel> vehicleTireNoList;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("凭证列表")
    private List<CertificationPicturesResponseModel> fileList;

    @ApiModelProperty(value = "是有有结算数据，1有，0没有")
    private Integer ifSettlement = 0;
}
