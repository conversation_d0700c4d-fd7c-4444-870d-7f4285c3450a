package com.logistics.management.webapi.client.carrierorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.carrierorder.hystrix.CarrierOrderLeYiClientHystrix;
import com.logistics.management.webapi.client.carrierorder.request.*;
import com.logistics.management.webapi.client.carrierorder.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/*
  运单管理
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = CarrierOrderLeYiClientHystrix.class)
public interface CarrierOrderLeYiClient {

    /**
     * 查询云盘运单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询云盘运单列表 ")
    @PostMapping(value = "/service/carrierOrderManagement/searchCarrierOrderListForLeYi")
    Result<PageInfo<SearchCarrierOrderListForLeYiResponseModel>> searchCarrierOrderListForLeYi(@RequestBody SearchCarrierOrderListForLeYiRequestModel requestModel);

    /**
     * 导出云盘运单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出云盘运单列表 ")
    @PostMapping(value = "/service/carrierOrderManagement/exportCarrierOrderListForLeYi")
    Result<List<SearchCarrierOrderListForLeYiResponseModel>> exportCarrierOrderListForLeYi(@RequestBody SearchCarrierOrderListForLeYiRequestModel requestModel);

    /**
     * 云盘待审核车辆数
     * @param requestModel
     * @return
     */
    @ApiOperation(value="云盘待审核车辆数")
    @PostMapping(value = "/service/management/carrierOrderManagement/getWaitAuditVehicleCountInfoForLeYi")
    Result<WaitAuditVehicleInfoResponseModel> getWaitAuditVehicleCountInfoForLeYi(@RequestBody WaitAuditVehicleInfoRequestModel requestModel);

    /**
     * 云盘运单详情页
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘运单详情页")
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderDetailForLeYi")
    Result<CarrierOrderDetailForLeYiResponseModel> carrierOrderDetailForLeYi(@RequestBody CarrierOrderDetailRequestModel requestModel);

    /**
     * 云盘运单修改地址详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘运单修改地址详情")
    @PostMapping(value = "/service/management/carrierOrderManagement/updateCarrierOrderUnloadAddressDetail")
    Result<List<UpdateUnloadAddressDetailResponseModel>> updateCarrierOrderUnloadAddressDetail(@RequestBody CarrierOrderUpUnloadAddrRequestModel requestModel);

    /**
     * 云盘运单确认修改地址
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘运单确认修改地址")
    @PostMapping(value = "/service/management/carrierOrderManagement/updateCarrierOrderUnloadAddressConfirm")
    Result<Boolean> updateCarrierOrderUnloadAddressConfirm(@RequestBody UpdateCarrierOrderUnloadAddressConfirmRequestModel requestModel);

    /**
     * 云盘运单纠错详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘运单纠错详情")
    @PostMapping(value = "/service/management/carrierOrderManagement/carrierOrderCorrectDetail")
    Result<CarrierOrderCorrectDetailResponseModel> carrierOrderCorrectDetail(@RequestBody CarrierOrderCorrectDetailRequestModel requestModel);

    /**
     * 云盘运单获取客户单号详情
     *
     * @param requestModel 运单号
     * @return 客户单号详情
     */
    @ApiOperation(value = "云盘运单获取客户单号详情")
    @PostMapping(value = "/service/management/carrierOrderManagement/getCarrierOrderOrders")
    Result<List<CarrierOrderOrdersResponseModel>> getCarrierOrderOrders(@RequestBody CarrierOrderIdRequestModel requestModel);

    /**
     * 云盘运单确认纠错
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘运单确认纠错")
    @PostMapping(value = "/service/management/carrierOrderManagement/carrierOrderCorrectConfirm")
    Result<Boolean> carrierOrderCorrectConfirm(@RequestBody CarrierOrderCorrectConfirmRequestModel requestModel);

    /**
     * 运单放空详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "运单放空详情")
    @PostMapping(value = "/service/management/carrierOrderManagement/carrierOrderEmptyDetail")
    Result<CarrierOrderEmptyDetailResponseModel> carrierOrderEmptyDetail(@RequestBody CarrierOrderDetailRequestModel requestModel);

    /**
     * 确认放空
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "确认放空")
    @PostMapping(value = "/service/management/carrierOrderManagement/confirmEmpty")
    Result<Boolean> confirmEmpty(@RequestBody CarrierOrderEmptyRequestModel requestModel);

    /**
     * 复制运单信息
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "复制运单信息")
    @PostMapping(value = "/service/management/carrierOrderManagement/copyCarrierOrder")
    Result<CopyCarrierOrderResponseModel> copyCarrierOrder(@RequestBody CarrierOrderIdRequestModel requestModel);

    /**
     * 云盘物流看板-平均物流费用
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘物流看板-平均物流费用")
    @PostMapping(value = "/service/management/carrierOrderManagement/logisticsCostStatistics")
    Result<List<LogisticsCostStatisticsResponseModel>> logisticsCostStatistics(@RequestBody LogisticsCostStatisticsRequestModel requestModel);

    /**
     * 云盘物流看板-平均提货时效
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘物流看板-平均提货时效")
    @PostMapping(value = "/service/management/carrierOrderManagement/logisticsLoadValidityStatistics")
    Result<List<LogisticsLoadValidityStatisticsResponseModel>> logisticsLoadValidityStatistics(@RequestBody LogisticsLoadValidityStatisticsRequestModel requestModel);

    /**
     * 云盘物流看板-待提货、待纠错
     *
     * @param
     */
    @ApiOperation(value = "云盘物流看板-待提货、待纠错")
    @PostMapping(value = "/service/management/waitCorrectStatistics")
    Result<WaitCorrectStatisticsResponseModel> waitCorrectStatistics();

    /**
     * 云盘运单签收详情
     *
     * @param requestModel 运单id
     * @return 运单签收详情
     */
    @ApiOperation(value = "云盘运单签收详情 ")
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderListBeforeSignUpForLeyi")
    Result<List<CarrierOrderListBeforeSignUpForLeyiResponseModel>> carrierOrderListBeforeSignUpForLeyi(@RequestBody CarrierOrderListBeforeSignUpRequestModel requestModel);

    /**
     * 云盘运单签收
     *
     * @param requestModel 签收详情
     * @return 操作结果
     */
    @ApiOperation(value = "云盘运单签收 ")
    @PostMapping(value = "/service/carrierOrderManagement/carrierOrderSignUpForLeyi")
    Result<Boolean> carrierOrderSignUpForLeyi(@RequestBody CarrierOrderConfirmSignUpForLeyiRequestModel requestModel);

    /**
     * 云盘运单详情编辑费用-详情查询
     *
     * @param requestModel 运单id,费用类型
     * @return 费用信息
     */
    @ApiOperation(value = "云盘运单详情编辑费用-详情查询")
    @PostMapping(value = "/service/carrierOrderManagement/editCarrierOrderCostDetail")
    Result<EditCarrierOrderCostDetailResponseModel> editCarrierOrderCostDetail(@RequestBody EditCarrierOrderCostDetailRequestModel requestModel);

    /**
     * 云盘运单详情编辑费用
     *
     * @param requestModel 费用信息
     * @return 操作结果
     */
    @ApiOperation(value = "云盘运单详情编辑费用")
    @PostMapping(value = "/service/carrierOrderManagement/editCarrierOrderCost")
    Result<Boolean> editCarrierOrderCost(@RequestBody EditCarrierOrderCostRequestModel requestModel);

    /**
     * 根据车主id查询有效的云盘运单（排除已取消、已放空）
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "根据车主id查询有效的云盘运单（排除已取消、已放空）")
    @PostMapping(value = "/service/carrierOrderManagement/getValidCarrierOrder")
    Result<PageInfo<GetValidCarrierOrderResponseModel>> getValidCarrierOrder(@RequestBody GetValidCarrierOrderRequestModel requestModel);

    @ApiOperation(value = "查询云盘二维码")
    @PostMapping(value = "/service/carrierOrderManagement/getLeYiQrCode")
    Result<GetLeYiQrCodeResponseModel> getLeYiQrCode(@RequestBody GetLeYiQrCodeRequestModel requestModel);
}
