package com.logistics.tms.biz.demandorder.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/10/19 current system date
 */
@Data
public class CancelCarrierOrderChangeDemandStateSynchronizeModel {

    //所属需求单id, 借用的临时字段,不参与json持久化
    @JsonIgnore
    private Long demandOrderId;

    private String demandCode;
    private Integer demandState;
    private String userName;
}
