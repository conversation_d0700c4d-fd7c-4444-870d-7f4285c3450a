package com.logistics.management.webapi.api.feign.driversafemeeting.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/11/4 9:25
 */
@Data
public class DriverSafeMeetingIdRequestDto {
    @ApiModelProperty(value = "学习例会id",required = true)
    @NotBlank(message = "id不能为空")
    private String safeMeetingId="";
}
