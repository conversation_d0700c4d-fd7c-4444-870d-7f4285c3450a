package com.logistics.tms.controller.drivercostapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DriverCostApplyInvoiceResponseModel {

    @ApiModelProperty(value = "发票id")
    private Long invoiceId;

    @ApiModelProperty(value = "发票类型")
    private Integer type;

    @ApiModelProperty(value = "发票名称")
    private String invoiceName;

    @ApiModelProperty(value = "票据类型")
    private String invoiceType;

    @ApiModelProperty(value = "发票代码")
    private String invoiceCode;

    @ApiModelProperty(value = "发票号码")
    private String invoiceNum;

    @ApiModelProperty(value = "合计金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "合计税额")
    private BigDecimal totalTax;

    @ApiModelProperty(value = "价税合计")
    private BigDecimal totalTaxAndPrice;

    @ApiModelProperty(value = "图片依据路径")
    private String imagePath;
}
