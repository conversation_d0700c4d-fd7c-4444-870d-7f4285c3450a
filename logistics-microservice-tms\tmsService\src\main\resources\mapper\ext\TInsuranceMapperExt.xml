<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TInsuranceMapper" >
  <select id="searchInsuranceList" resultType="com.logistics.tms.api.feign.insuarance.model.SearchInsuranceListResponseModel">
    select
    ti.id                                as insuranceId,
    ti.insurance_type                    as insuranceType,
    ti.status_type                       as statusType,
    ti.policy_no                         as policyNumber,
    ti.settlement_status                 as settlementStatus,
    ti.premium                           as premium,
    ti.unpaid_premium                    as outstandingPremium,
    ti.refund_premium                    as refundPremium,
    ti.start_time                        as startTime,
    ti.end_time                          as endTime,
    ti.cancel_reason                     as cancelReason,
    ti.remark                            as remark,
    ti.created_by                        as addUserName,
    ti.last_modified_by                  as lastModifiedBy,
    ti.last_modified_time                as lastModifiedTime,
    ti.payment_of_vehicle_and_vessel_tax as paymentOfVehicleAndVesselTax,
    ti.vehicle_property                  as vehicleProperty,
    tic.company_name                     as insuranceCompanyName,
    tvdl.vehicle_no                      as vehicleNo,
    tsb.name                             as driverName,
    tsb.mobile                           as driverPhone,
    ticp.company_name                    as insuranceCompanyNamePerson,
    tpai.policy_number                   as policyNumberPerson,
    tpai.batch_number                    as batchNumberPerson,
    tpai.start_time                      as startTimePerson,
    tpai.end_time                        as endTimePerson,
    tpai.gross_premium                   as grossPremiumPerson,
    tpai.policy_person_count             as policyPersonCountPerson,
    tpa.gross_premium                    as grossPremium,
    tpa.policy_person_count              as policyPersonCount
    from t_insurance ti
    left join t_insurance_company tic on tic.id = ti.insurance_company_id and tic.valid = 1
    left join t_staff_basic tsb on tsb.id = ti.driver_id and tsb.valid = 1
    left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = ti.vehicle_id and tvdl.valid = 1
    left join t_personal_accident_insurance tpai on tpai.id = ti.personal_accident_insurance_id and tpai.valid = 1
    left join t_insurance_company ticp on ticp.id = tpai.insurance_company_id and ticp.valid = 1
    left join t_personal_accident_insurance tpa on tpa.id = ti.related_personal_accident_insurance_id and tpa.valid = 1
    where ti.valid = 1
    <if test="params.policyStatus != null">
      <choose>
        <when test="params.policyStatus == 1">
          and ((ti.insurance_type != 3 and ti.start_time &lt;= now() and ti.end_time &gt;= now()) or (ti.insurance_type = 3 and tpai.start_time &lt;= now() and tpai.end_time &gt;= now())) and ti.status_type = 0
        </when>
        <when test="params.policyStatus == 2">
          and ((ti.insurance_type != 3 and ti.end_time &lt;= now()) or (ti.insurance_type = 3 and tpai.end_time &lt;= now())) and ti.status_type = 0
        </when>
        <when test="params.policyStatus == 3">
          and ((ti.insurance_type != 3 and ti.start_time &gt;= now()) or (ti.insurance_type = 3 and tpai.start_time &gt;= now())) and ti.status_type = 0
        </when>
        <when test="params.policyStatus == 4">
          and ti.status_type = 1
        </when>
        <when test="params.policyStatus == 5">
          and ti.status_type = 2
        </when>
      </choose>
    </if>
    <if test="params.insuranceType != null">
      and ti.insurance_type = #{params.insuranceType,jdbcType=INTEGER}
    </if>
    <if test="params.insuranceCompanyName != null and params.insuranceCompanyName != ''">
      and (instr(tic.company_name,#{params.insuranceCompanyName,jdbcType=VARCHAR}) or instr(ticp.company_name,#{params.insuranceCompanyName,jdbcType=VARCHAR}))
    </if>
    <if test="params.vehicleNo != null and params.vehicleNo != ''">
      and instr(tvdl.vehicle_no,#{params.vehicleNo,jdbcType=VARCHAR})
    </if>
    <if test="params.driverName != null and params.driverName != ''">
      and (instr(tsb.name,#{params.driverName,jdbcType=VARCHAR}) or instr(tsb.mobile,#{params.driverName,jdbcType=VARCHAR}))
    </if>
    <if test="params.lastModifiedBy != null and params.lastModifiedBy != ''">
      and instr(ti.last_modified_by,#{params.lastModifiedBy,jdbcType=VARCHAR})
    </if>
    <if test="params.lastModifiedTimeStart != null and params.lastModifiedTimeStart != ''">
      and ti.last_modified_time &gt;= DATE_FORMAT(#{params.lastModifiedTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
    </if>
    <if test="params.lastModifiedTimeEnd != null and params.lastModifiedTimeEnd != ''">
      and ti.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>
    <if test="params.ids!=null and params.ids!=''">
      and ti.id in (${params.ids})
    </if>
    <if test="params.vehicleProperty != null">
      and ti.vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER}
    </if>
    order by ti.last_modified_time desc,ti.id desc
  </select>
  
  <resultMap id="getInsuranceDetail_Map" type="com.logistics.tms.api.feign.insuarance.model.GetInsuranceDetailResponseModel">
    <id column="id" property="insuranceId" jdbcType="BIGINT"/>
    <result column="status_type" property="statusType" jdbcType="INTEGER"/>
    <result column="insurance_type" property="insuranceType" jdbcType="INTEGER"/>
    <result column="vehicle_id" property="vehicleId" jdbcType="BIGINT"/>
    <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
    <result column="driver_id" property="driverId" jdbcType="BIGINT"/>
    <result column="driverName" property="driverName" jdbcType="VARCHAR"/>
    <result column="driverPhone" property="driverPhone" jdbcType="VARCHAR"/>
    <result column="insurance_company_id" property="insuranceCompanyId" jdbcType="BIGINT"/>
    <result column="company_name" property="insuranceCompanyName" jdbcType="VARCHAR"/>
    <result column="policy_no" property="policyNumber" jdbcType="VARCHAR"/>
    <result column="premium" property="premium" jdbcType="DECIMAL"/>
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
    <result column="remark" property="remark" jdbcType="VARCHAR"/>
    <result column="settlement_status" property="settlementStatus" jdbcType="INTEGER" />
    <result column="payment_of_vehicle_and_vessel_tax" property="paymentOfVehicleAndVesselTax" jdbcType="DECIMAL"/>
    <result column="personal_accident_insurance_id" property="personalAccidentInsuranceId" jdbcType="BIGINT"/>
    <result column="batch_number" property="batchNumber" jdbcType="VARCHAR"/>
    <result column="policyType" property="policyType" jdbcType="INTEGER"/>
    <result column="insuranceCompanyIdPerson" property="insuranceCompanyIdPerson" jdbcType="BIGINT"/>
    <result column="insuranceCompanyNamePerson" property="insuranceCompanyNamePerson" jdbcType="VARCHAR"/>
    <result column="policyNumberPerson" property="policyNumberPerson" jdbcType="VARCHAR"/>
    <result column="grossPremiumPerson" property="grossPremiumPerson" jdbcType="DECIMAL"/>
    <result column="policyPersonCountPerson" property="policyPersonCountPerson" jdbcType="INTEGER"/>
    <result column="startTimePerson" property="startTimePerson" jdbcType="TIMESTAMP"/>
    <result column="endTimePerson" property="endTimePerson" jdbcType="TIMESTAMP"/>
    <result column="relatedPersonalAccidentInsuranceId" property="relatedPersonalAccidentInsuranceId" jdbcType="BIGINT"/>
    <result column="relatedPolicyType" property="relatedPolicyType" jdbcType="INTEGER"/>
    <result column="relatedPolicyNumber" property="relatedPolicyNumber" jdbcType="VARCHAR"/>
    <result column="relatedBatchNumber" property="relatedBatchNumber" jdbcType="VARCHAR"/>
    <result column="relatedGrossPremium" property="relatedGrossPremium" jdbcType="DECIMAL"/>
    <result column="relatedPolicyPersonCount" property="relatedPolicyPersonCount" jdbcType="INTEGER"/>
    <collection property="ticketList" ofType="com.logistics.tms.api.feign.insuarance.model.InsuranceTicketsResponseModel">
      <id column="ticketId" property="ticketId" jdbcType="BIGINT"/>
      <result column="filePath" property="filePath" jdbcType="VARCHAR"/>
      <result column="fileName" property="fileName" jdbcType="VARCHAR"/>
    </collection>
  </resultMap>
  <select id="getInsuranceDetail" resultMap="getInsuranceDetail_Map">
    select
    ti.id,
    ti.status_type,
    ti.insurance_type,
    ti.vehicle_id,
    ti.driver_id,
    ti.insurance_company_id,
    ti.policy_no,
    ti.premium,
    ti.start_time,
    ti.end_time,
    ti.remark,
    ti.payment_of_vehicle_and_vessel_tax,
    ti.personal_accident_insurance_id,
    ti.settlement_status,
    tic.company_name,
    tvdl.vehicle_no,
    tsb.name as driverName,
    tsb.mobile as driverPhone,
    tpai.batch_number,
    tpai.type as policyType,
    ticp.id as insuranceCompanyIdPerson,
    ticp.company_name as insuranceCompanyNamePerson,
    tpai.policy_number as policyNumberPerson,
    tpai.start_time as startTimePerson,
    tpai.end_time as endTimePerson,
    tpai.gross_premium as grossPremiumPerson,
    tpai.policy_person_count as policyPersonCountPerson,
    tpair.gross_premium as relatedGrossPremium,
    tpair.policy_person_count as relatedPolicyPersonCount,
    tpair.id as relatedPersonalAccidentInsuranceId,
    tpair.type as relatedPolicyType,
    tpair.policy_number as relatedPolicyNumber,
    tpair.batch_number as relatedBatchNumber,
    tcp.id as ticketId,
    tcp.file_path as filePath,
    tcp.file_name as fileName
    from t_insurance ti
    left join t_insurance_company tic on tic.id = ti.insurance_company_id and tic.valid = 1
    left join t_staff_basic tsb on tsb.id = ti.driver_id and tsb.valid = 1
    left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = ti.vehicle_id and tvdl.valid = 1
    left join t_personal_accident_insurance tpai on tpai.id = ti.personal_accident_insurance_id and tpai.valid = 1
    left join t_insurance_company ticp on ticp.id = tpai.insurance_company_id and ticp.valid = 1
    left join t_personal_accident_insurance tpair on tpair.id = ti.related_personal_accident_insurance_id and tpair.valid = 1
    left join t_certification_pictures tcp on ((ti.insurance_type != 3 and tcp.object_id = ti.id and tcp.object_type = 5 and tcp.file_type = ti.insurance_type) or (ti.insurance_type = 3 and (tcp.object_id = tpai.id or tcp.object_id = tpair.id) and tcp.object_type = 1 and tcp.file_type = 1)) and tcp.valid = 1
    where ti.valid = 1
    and ti.id = #{insuranceId,jdbcType=BIGINT}
    order by tcp.id desc
  </select>

  <select id="getByTypeVehicleIdOrDriverId" resultMap="BaseResultMap">
    select
    ti.*
    from t_insurance ti
    where ti.valid = 1 and ti.status_type = 0
    <if test="params.insuranceType != null">
      and ti.insurance_type = #{params.insuranceType,jdbcType=INTEGER}
    </if>
    <if test="params.personalAccidentInsuranceId != null">
      and ti.personal_accident_insurance_id = #{params.personalAccidentInsuranceId,jdbcType=BIGINT}
    </if>
    <if test="params.relatedPersonalAccidentInsuranceId != null">
      and ti.related_personal_accident_insurance_id = #{params.relatedPersonalAccidentInsuranceId,jdbcType=BIGINT}
    </if>
    <if test="params.vehicleId != null">
      and ti.vehicle_id = #{params.vehicleId,jdbcType=BIGINT}
    </if>
    <if test="params.driverId != null">
      and ti.driver_id = #{params.driverId,jdbcType=BIGINT}
    </if>
    <if test="params.startTime != null and params.startTime != '' and params.endTime != null and params.endTime != ''">
      and ti.start_time &lt;= #{params.endTime,jdbcType=VARCHAR} and ti.end_time >= #{params.startTime,jdbcType=VARCHAR}
    </if>
  </select>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TInsurance" >
    <foreach collection="list" item="item" separator=";">
      insert into t_insurance
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.insuranceType != null" >
          insurance_type,
        </if>
        <if test="item.statusType != null">
          status_type,
        </if>
        <if test="item.cancelReason != null" >
          cancel_reason,
        </if>
        <if test="item.vehicleId != null" >
          vehicle_id,
        </if>
        <if test="item.vehicleProperty != null" >
          vehicle_property,
        </if>
        <if test="item.driverId != null" >
          driver_id,
        </if>
        <if test="item.insuranceCompanyId != null" >
          insurance_company_id,
        </if>
        <if test="item.policyNo != null" >
          policy_no,
        </if>
        <if test="item.settlementStatus != null">
          settlement_status,
        </if>
        <if test="item.premium != null" >
          premium,
        </if>
        <if test="item.unpaidPremium != null">
          unpaid_premium,
        </if>
        <if test="item.refundPremium != null">
          refund_premium,
        </if>
        <if test="item.startTime != null" >
          start_time,
        </if>
        <if test="item.endTime != null" >
          end_time,
        </if>
        <if test="item.remark != null" >
          remark,
        </if>
        <if test="item.paymentOfVehicleAndVesselTax != null" >
          payment_of_vehicle_and_vessel_tax,
        </if>
        <if test="item.personalAccidentInsuranceId != null" >
          personal_accident_insurance_id,
        </if>
        <if test="item.relatedPersonalAccidentInsuranceId != null">
          related_personal_accident_insurance_id,
        </if>
        <if test="item.source != null" >
          source,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.insuranceType != null" >
          #{item.insuranceType,jdbcType=INTEGER},
        </if>
        <if test="item.statusType != null">
          #{item.statusType,jdbcType=INTEGER},
        </if>
        <if test="item.cancelReason != null" >
          #{item.cancelReason,jdbcType=VARCHAR},
        </if>
        <if test="item.vehicleId != null" >
          #{item.vehicleId,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleProperty != null" >
          #{item.vehicleProperty,jdbcType=INTEGER},
        </if>
        <if test="item.driverId != null" >
          #{item.driverId,jdbcType=BIGINT},
        </if>
        <if test="item.insuranceCompanyId != null" >
          #{item.insuranceCompanyId,jdbcType=BIGINT},
        </if>
        <if test="item.policyNo != null" >
          #{item.policyNo,jdbcType=VARCHAR},
        </if>
        <if test="item.settlementStatus != null">
          #{item.settlementStatus,jdbcType=INTEGER},
        </if>
        <if test="item.premium != null" >
          #{item.premium,jdbcType=DECIMAL},
        </if>
        <if test="item.unpaidPremium != null">
          #{item.unpaidPremium,jdbcType=DECIMAL},
        </if>
        <if test="item.refundPremium != null">
          #{item.refundPremium,jdbcType=DECIMAL},
        </if>
        <if test="item.startTime != null" >
          #{item.startTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.endTime != null" >
          #{item.endTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.remark != null" >
          #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.paymentOfVehicleAndVesselTax != null" >
          #{item.paymentOfVehicleAndVesselTax,jdbcType=DECIMAL},
        </if>
        <if test="item.personalAccidentInsuranceId != null" >
          #{item.personalAccidentInsuranceId,jdbcType=BIGINT},
        </if>
        <if test="item.relatedPersonalAccidentInsuranceId != null">
          #{item.relatedPersonalAccidentInsuranceId,jdbcType=BIGINT},
        </if>
        <if test="item.source != null" >
          #{item.source,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <select id="getByDriverIds" resultMap="BaseResultMap">
    select
    t1.id, t1.insurance_type, t1.status_type, t1.cancel_reason, t1.vehicle_id, t1.driver_id, t1.insurance_company_id,
    t1.policy_no,  t1.remark, t1.payment_of_vehicle_and_vessel_tax,
    t1.personal_accident_insurance_id, t1.related_personal_accident_insurance_id, t1.source, t1.created_by,
    t1.created_time, t1.last_modified_by, t1.last_modified_time, t1.valid,
    case when insurance_type in (1,2,4,5) then t1.start_time when insurance_type = 3 and t1.related_personal_accident_insurance_id = 0 and t1.personal_accident_insurance_id>0  then t2.start_time when insurance_type = 3 and t1.related_personal_accident_insurance_id > 0 then t3.start_time else now() end as start_time,
    case when insurance_type in (1,2,4,5) then t1.end_time when insurance_type = 3 and t1.related_personal_accident_insurance_id = 0 and t1.personal_accident_insurance_id>0  then t2.end_time when insurance_type = 3 and t1.related_personal_accident_insurance_id > 0 then t3.end_time else now() end as end_time,
    case when insurance_type in (1,2,4,5) then t1.premium when insurance_type = 3 and t1.related_personal_accident_insurance_id = 0 and t1.personal_accident_insurance_id>0  then cast( t2.gross_premium / t2.policy_person_count as decimal(10, 2) ) when insurance_type = 3 and t1.related_personal_accident_insurance_id > 0 then cast( t3.gross_premium / t3.policy_person_count as decimal(10, 2) ) else 0 end as premium

    from t_insurance t1
    left join t_personal_accident_insurance t2 on t1.personal_accident_insurance_id = t2.id and t2.valid = 1
    left join t_personal_accident_insurance t3 on t1.related_personal_accident_insurance_id = t3.id  and t2.valid = 1
    where t1.valid = 1
    and t1.driver_id in (${driverIds})
  </select>

  <select id="getOverDueInsurance" resultMap="BaseResultMap">
    select
    t1.id, t1.insurance_type, t1.status_type, t1.cancel_reason, t1.vehicle_id, t1.driver_id, t1.insurance_company_id,
    t1.policy_no,  t1.remark, t1.payment_of_vehicle_and_vessel_tax,
    t1.personal_accident_insurance_id, t1.related_personal_accident_insurance_id, t1.source, t1.created_by,
    t1.created_time, t1.last_modified_by, t1.last_modified_time, t1.valid,
    case when insurance_type in (1,2,4,5) then t1.start_time when insurance_type = 3 and t1.related_personal_accident_insurance_id = 0 and t1.personal_accident_insurance_id>0  then t2.start_time when insurance_type = 3 and t1.related_personal_accident_insurance_id > 0 then t3.start_time else now() end as start_time,
    case when insurance_type in (1,2,4,5) then t1.end_time when insurance_type = 3 and t1.related_personal_accident_insurance_id = 0 and t1.personal_accident_insurance_id>0  then t2.end_time when insurance_type = 3 and t1.related_personal_accident_insurance_id > 0 then t3.end_time else now() end as end_time,
    case when insurance_type in (1,2,4,5) then t1.premium when insurance_type = 3 and t1.related_personal_accident_insurance_id = 0 and t1.personal_accident_insurance_id>0  then cast( t2.gross_premium / t2.policy_person_count as decimal(10, 2) ) when insurance_type = 3 and t1.related_personal_accident_insurance_id > 0 then cast( t3.gross_premium / t3.policy_person_count as decimal(10, 2) ) else 0 end as premium

    from t_insurance t1
    left join t_personal_accident_insurance t2 on t1.personal_accident_insurance_id = t2.id and t2.valid = 1
    left join t_personal_accident_insurance t3 on t1.related_personal_accident_insurance_id = t3.id  and t2.valid = 1
    where t1.valid = 1 and status_type = 0 and
    ((insurance_type in (1,2,4,5) and  t1.end_time &lt;=#{now,jdbcType=TIMESTAMP})
    or
    (insurance_type = 3 and t1.related_personal_accident_insurance_id = 0 and t1.personal_accident_insurance_id>0  and t2.end_time &lt;= #{now,jdbcType=TIMESTAMP} )
    or
    (insurance_type = 3 and t1.related_personal_accident_insurance_id > 0 and t3.end_time &lt;= #{now,jdbcType=TIMESTAMP}))
  </select>

  <select id="getAboutToOverDueInsurance" resultMap="BaseResultMap">
    select
    t1.id, t1.insurance_type, t1.status_type, t1.cancel_reason, t1.vehicle_id, t1.driver_id, t1.insurance_company_id,
    t1.policy_no,
    case when insurance_type in (1,2,4,5) then t1.start_time when insurance_type = 3 and t1.related_personal_accident_insurance_id = 0 and t1.personal_accident_insurance_id>0  then t2.start_time when insurance_type = 3 and t1.related_personal_accident_insurance_id > 0 then t3.start_time else now() end as start_time,
    case when insurance_type in (1,2,4,5) then t1.end_time when insurance_type = 3 and t1.related_personal_accident_insurance_id = 0 and t1.personal_accident_insurance_id>0  then t2.end_time when insurance_type = 3 and t1.related_personal_accident_insurance_id > 0 then t3.end_time else now() end as end_time,
    t1.remark, t1.payment_of_vehicle_and_vessel_tax,
    t1.personal_accident_insurance_id, t1.related_personal_accident_insurance_id, t1.source, t1.created_by,
    t1.created_time, t1.last_modified_by, t1.last_modified_time, t1.valid,
    case when insurance_type in (1,2,4,5) then t1.premium when insurance_type = 3 and t1.related_personal_accident_insurance_id = 0 and t1.personal_accident_insurance_id>0  then cast( t2.gross_premium / t2.policy_person_count as decimal(10, 2) )  when insurance_type = 3 and t1.related_personal_accident_insurance_id > 0 then cast( t3.gross_premium / t3.policy_person_count as decimal(10, 2) )  else 0 end as premium

    from t_insurance t1
    left join t_personal_accident_insurance t2 on t1.personal_accident_insurance_id = t2.id and t2.valid = 1
    left join t_personal_accident_insurance t3 on t1.related_personal_accident_insurance_id = t3.id  and t2.valid = 1
    where t1.valid = 1 and t1.status_type = 0 and
    ((insurance_type in (1,2,4,5) and  t1.end_time &gt;= #{now,jdbcType=TIMESTAMP} and DATE_SUB(t1.end_time,INTERVAL #{remindDays,jdbcType=INTEGER} DAY) &lt;= #{now,jdbcType=TIMESTAMP} )
    or
    (insurance_type = 3 and t1.related_personal_accident_insurance_id = 0 and t1.personal_accident_insurance_id>0  and t2.end_time &gt;= #{now,jdbcType=TIMESTAMP} and DATE_SUB(t2.end_time,INTERVAL #{remindDays,jdbcType=INTEGER} DAY) &lt;= #{now,jdbcType=TIMESTAMP} )
    or
    (insurance_type = 3 and t1.related_personal_accident_insurance_id > 0 and t3.end_time &gt;= #{now,jdbcType=TIMESTAMP} and DATE_SUB(t3.end_time,INTERVAL #{remindDays,jdbcType=INTEGER} DAY) &lt;= #{now,jdbcType=TIMESTAMP} ))
  </select>

  <select id="getByVehicleIdAndTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_insurance
    where valid = 1 and status_type = 0
    and insurance_type = #{insuranceType,jdbcType=INTEGER}
    and vehicle_id = #{vehicleId,jdbcType=BIGINT}
    and YEAR(start_time) = #{startTime,jdbcType=INTEGER}
    and YEAR(end_time) = #{endTime,jdbcType=INTEGER}
  </select>

  <select id="getByVehicleBasicId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_insurance
    where valid = 1
    and vehicle_id in (${vehicleBasicId})
  </select>

  <select id="getAllCount" resultType="java.lang.Integer">
    select count(0)
    from t_insurance
    where valid = 1
  </select>
  <select id="getByIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from t_insurance
    where valid=1
    and id in (${ids})
  </select>
  <select id="getByVehicleIdAndPeriod" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from t_insurance
    where valid=1
    and status_type = 0
    and vehicle_id = #{vehicleId,jdbcType=BIGINT}
    and start_time &lt;= #{currentTime,jdbcType=TIMESTAMP}
    and end_time &gt;= #{currentTime,jdbcType=TIMESTAMP}
  </select>

  <update id="batchUpdate" parameterType="com.logistics.tms.entity.TInsurance">
    <foreach collection="list" item="item" separator=";">
      update t_insurance
      <set>
        <if test="item.insuranceType != null">
          insurance_type = #{item.insuranceType,jdbcType=INTEGER},
        </if>
        <if test="item.statusType != null">
          status_type = #{item.statusType,jdbcType=INTEGER},
        </if>
        <if test="item.cancelReason != null">
          cancel_reason = #{item.cancelReason,jdbcType=VARCHAR},
        </if>
        <if test="item.vehicleId != null">
          vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleProperty != null">
          vehicle_property = #{item.vehicleProperty,jdbcType=INTEGER},
        </if>
        <if test="item.driverId != null">
          driver_id = #{item.driverId,jdbcType=BIGINT},
        </if>
        <if test="item.insuranceCompanyId != null">
          insurance_company_id = #{item.insuranceCompanyId,jdbcType=BIGINT},
        </if>
        <if test="item.policyNo != null">
          policy_no = #{item.policyNo,jdbcType=VARCHAR},
        </if>
        <if test="item.settlementStatus != null">
          settlement_status = #{item.settlementStatus,jdbcType=INTEGER},
        </if>
        <if test="item.premium != null">
          premium = #{item.premium,jdbcType=DECIMAL},
        </if>
        <if test="item.unpaidPremium != null">
          unpaid_premium = #{item.unpaidPremium,jdbcType=DECIMAL},
        </if>
        <if test="item.refundPremium != null">
          refund_premium = #{item.refundPremium,jdbcType=DECIMAL},
        </if>
        <if test="item.startTime != null">
          start_time = #{item.startTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.endTime != null">
          end_time = #{item.endTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.remark != null">
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.paymentOfVehicleAndVesselTax != null">
          payment_of_vehicle_and_vessel_tax = #{item.paymentOfVehicleAndVesselTax,jdbcType=DECIMAL},
        </if>
        <if test="item.personalAccidentInsuranceId != null">
          personal_accident_insurance_id = #{item.personalAccidentInsuranceId,jdbcType=BIGINT},
        </if>
        <if test="item.relatedPersonalAccidentInsuranceId != null">
          related_personal_accident_insurance_id = #{item.relatedPersonalAccidentInsuranceId,jdbcType=BIGINT},
        </if>
        <if test="item.source != null">
          source = #{item.source,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <resultMap id="getSettlementCostsRelationMap" type="com.logistics.tms.api.feign.insuarance.model.InsuranceSettlementCostsRelationListModel">
    <id column="insuranceId" jdbcType="BIGINT" property="insuranceId"/>
    <collection property="insuranceSettlementCostsRelationList" ofType="com.logistics.tms.api.feign.insuarance.model.InsuranceSettlementCostsRelationModel">
      <result column="insuranceCostsId" property="insuranceCostsId" jdbcType="BIGINT"/>
      <result column="insuranceType" property="insuranceType" jdbcType="INTEGER" />
      <result column="insuranceMonth" property="insuranceMonth" jdbcType="VARCHAR" />
    </collection>
  </resultMap>
  <select id="getSettlementCostsRelation" resultMap="getSettlementCostsRelationMap">
    SELECT
      ti.id as insuranceId,
      tic.id as insuranceCostsId,
      ti2.insurance_type as insuranceType,
      tic.settlement_month as insuranceMonth
    FROM
      t_insurance ti
        INNER JOIN t_insurance_costs_relation ticr ON ticr.insurance_id = ti.id AND ticr.valid = 1
        INNER JOIN t_insurance_costs tic ON tic.id = ticr.insurance_costs_id AND tic.valid = 1
        INNER JOIN t_insurance_costs_relation ticr2 on ticr2.insurance_costs_id = tic.id and ticr2.valid=1
        INNER JOIN t_insurance ti2 ON ti2.id = ticr2.insurance_id AND ticr2.valid = 1
    where ti.valid=1
    and ti.id in (${insuranceIds})
  </select>
</mapper>