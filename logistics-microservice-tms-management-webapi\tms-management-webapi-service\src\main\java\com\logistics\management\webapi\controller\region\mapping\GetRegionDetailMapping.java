package com.logistics.management.webapi.controller.region.mapping;

import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.client.region.response.RegionCompanyResponseModel;
import com.logistics.management.webapi.client.region.response.RegionDetailResponseModel;
import com.logistics.management.webapi.controller.region.response.RegionCompanyResponseDto;
import com.logistics.management.webapi.controller.region.response.RegionDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/5 14:40
 */
@Data
public class GetRegionDetailMapping extends MapperMapping<RegionDetailResponseModel, RegionDetailResponseDto> {
    @Override
    public void configure() {
        RegionDetailResponseModel source = getSource();
        RegionDetailResponseDto destination = getDestination();

        List<RegionCompanyResponseDto> companyCarrierList = new ArrayList<>();
        if (ListUtils.isNotEmpty(source.getCompanyCarrierList())){
            RegionCompanyResponseDto dto;
            for (RegionCompanyResponseModel model : source.getCompanyCarrierList()) {
                dto = new RegionCompanyResponseDto();
                dto.setCompanyId(ConverterUtils.toString(model.getCompanyId()));
                dto.setCompanyType(ConverterUtils.toString(model.getCompanyType()));
                //个人类型 把联系人+手机号当作公司名
                if (CompanyTypeEnum.PERSONAL.getKey().equals(model.getCompanyType())) {
                    dto.setCompanyName(model.getContactName() + " " + model.getContactPhone());
                }else{
                    dto.setCompanyName(model.getCompanyName());
                }
                companyCarrierList.add(dto);
            }
        }
        destination.setCompanyCarrierList(companyCarrierList);
    }
}
