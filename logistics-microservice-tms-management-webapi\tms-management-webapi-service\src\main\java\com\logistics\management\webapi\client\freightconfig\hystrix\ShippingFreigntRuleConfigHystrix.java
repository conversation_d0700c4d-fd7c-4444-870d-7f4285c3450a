package com.logistics.management.webapi.client.freightconfig.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.freightconfig.ShippingFreightRuleConfigClient;
import com.logistics.management.webapi.client.freightconfig.request.ConfigStringPointReqModel;
import com.logistics.management.webapi.client.freightconfig.request.ConfigVehicleReqModel;
import com.logistics.management.webapi.client.freightconfig.request.EnableOrForbidOrDeleteReqModel;
import com.logistics.management.webapi.client.freightconfig.request.ShippingFreightRuleIdReqModel;
import com.logistics.management.webapi.client.freightconfig.request.shipping.AddShippingFreightRuleReqModel;
import com.logistics.management.webapi.client.freightconfig.request.shipping.ListShippingFreightRuleConfigReqModel;
import com.logistics.management.webapi.client.freightconfig.response.GetConfigVechicleRespModel;
import com.logistics.management.webapi.client.freightconfig.response.shipping.ListShippingFreightRuleListRespModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;


@Component
public class ShippingFreigntRuleConfigHystrix implements ShippingFreightRuleConfigClient {


    @Override
    public Result<PageInfo<ListShippingFreightRuleListRespModel>> getList(ListShippingFreightRuleConfigReqModel reqModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<ListShippingFreightRuleListRespModel>> exportList(ListShippingFreightRuleConfigReqModel reqModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> add(AddShippingFreightRuleReqModel reqModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enableOrForbid(@Valid EnableOrForbidOrDeleteReqModel reqModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> configVehicle(@Valid List<ConfigVehicleReqModel> configVehicleReqModels) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> editVehicle(@Valid List<ConfigVehicleReqModel> configVehicleReqModels) {
        return Result.timeout();
    }

    @Override
    public Result<GetConfigVechicleRespModel> getConfigVehicle(@Valid ShippingFreightRuleIdReqModel reqModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> configStringPoint(@Valid List<ConfigStringPointReqModel> configVehicleReqModels) {
        return Result.timeout();
    }
}
