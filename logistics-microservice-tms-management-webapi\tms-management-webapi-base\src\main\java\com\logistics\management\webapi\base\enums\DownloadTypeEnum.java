package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/5/31 13:04
 */
public enum DownloadTypeEnum {
    VEHICLE_ASSETS("1","车辆信息导入模板","vehicle_assets_template","xlsx"),
    PERSON_ASSETS("2","人员（司机押运员）信息导入模板","person_assets_template","xlsx"),
    INSURANCE_MANAGEMENT("3", "保险信息导入模板","insurance_management_template","xlsx"),
    ILLEGAL_ACCIDENT("4","违章信息导入模板","illegal_accident_template","xlsx"),
    VEHICLE_TYPE("5","车辆类型导入模板","vehicle_type_template","xlsx"),
    INSURANCE_COMPANY("6", "保险公司导入模板","insurance_company_template","xlsx"),
    PERSONAL_ACCIDENT_INSURANCE("7", "个人意外险（雇主责任险）导入模板","personal_assident_insurance_template","xlsx"),
    VEHICLE_TIRE("8","轮胎管理导入模板","vehicle_tire_template","xlsx"),
    VEHICLE_PAYEE_REL("9","车辆收款账户关联关系导入模板","vehicle_payee_rel_template","xlsx"),
    BANK_CONFIG_TYPE("10","银行名称导入模板","bank_import_template","xlsx"),
    DRIVER_PAYEE_TYPE("11","收款人账户导入模板","driver_payee_template","xlsx"),
    STAFF_VEHICLE_TYPE("12","司机车辆导入模板","driver_vehicle_template","xlsx"),
    OIL_FILLED_CARD("13","充油卡导入模板","fuel_card_template","xlsx"),
    OIL_FILLED_CAR("14","加油车导入模板","fuelling_vehicle_template","xlsx"),
    ;

    private String downloadType;
    private String fileName;
    private String templateName;
    private String fileType;

    DownloadTypeEnum(String downloadType, String fileName, String templateName, String fileType) {
        this.downloadType = downloadType;
        this.fileName = fileName;
        this.templateName = templateName;
        this.fileType = fileType;
    }

    public String getDownloadType() {
        return downloadType;
    }

    public String getFileName() {
        return fileName;
    }

    public String getTemplateName() {
        return templateName;
    }

    public String getFileType() {
        return fileType;
    }

    public static DownloadTypeEnum getEnumByDownloadType(String downloadType) {
        for (DownloadTypeEnum t : values()) {
            if (t.getDownloadType().equals(downloadType)) {
                return t;
            }
        }
        return null;
    }
}
