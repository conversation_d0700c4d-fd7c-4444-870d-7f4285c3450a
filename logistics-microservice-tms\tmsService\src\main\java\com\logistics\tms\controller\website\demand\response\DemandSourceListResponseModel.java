package com.logistics.tms.controller.website.demand.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/31
 * @description:
 */
@Data
public class DemandSourceListResponseModel {

    @ApiModelProperty(value = "货源ID")
    private Long demandSourceId;

    @ApiModelProperty(value = "货物名称")
    private String goodsName;

    @ApiModelProperty(value = "货物吨位")
    private Integer goodsAmount;

    @ApiModelProperty(value = "货物单价")
    private Integer goodsPrice;

    @ApiModelProperty(value = "联系人名称")
    private String contactName;

    @ApiModelProperty(value = "联系电话")
    private String contactMobile;

    @ApiModelProperty(value = "装货地")
    private String loadAddress;

    @ApiModelProperty(value = "卸货地")
    private String unloadAddress;

}
