<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TAttendanceAskLeaveMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TAttendanceAskLeave" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="staff_id" property="staffId" jdbcType="BIGINT" />
    <result column="staff_name" property="staffName" jdbcType="VARCHAR" />
    <result column="staff_mobile" property="staffMobile" jdbcType="VARCHAR" />
    <result column="staff_property" property="staffProperty" jdbcType="INTEGER" />
    <result column="leave_type" property="leaveType" jdbcType="INTEGER" />
    <result column="leave_start_time" property="leaveStartTime" jdbcType="TIMESTAMP" />
    <result column="leave_start_time_type" property="leaveStartTimeType" jdbcType="INTEGER" />
    <result column="leave_end_time" property="leaveEndTime" jdbcType="TIMESTAMP" />
    <result column="leave_end_time_type" property="leaveEndTimeType" jdbcType="INTEGER" />
    <result column="leave_duration" property="leaveDuration" jdbcType="DECIMAL" />
    <result column="leave_reason" property="leaveReason" jdbcType="VARCHAR" />
    <result column="apply_time" property="applyTime" jdbcType="TIMESTAMP" />
    <result column="audit_status" property="auditStatus" jdbcType="INTEGER" />
    <result column="auditor_name" property="auditorName" jdbcType="VARCHAR" />
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, staff_id, staff_name, staff_mobile, staff_property, leave_type, leave_start_time, 
    leave_start_time_type, leave_end_time, leave_end_time_type, leave_duration, leave_reason, 
    apply_time, audit_status, auditor_name, audit_time, remark, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_attendance_ask_leave
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_attendance_ask_leave
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TAttendanceAskLeave" >
    insert into t_attendance_ask_leave (id, staff_id, staff_name, 
      staff_mobile, staff_property, leave_type, 
      leave_start_time, leave_start_time_type, leave_end_time, 
      leave_end_time_type, leave_duration, leave_reason, 
      apply_time, audit_status, auditor_name, 
      audit_time, remark, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{staffId,jdbcType=BIGINT}, #{staffName,jdbcType=VARCHAR}, 
      #{staffMobile,jdbcType=VARCHAR}, #{staffProperty,jdbcType=INTEGER}, #{leaveType,jdbcType=INTEGER}, 
      #{leaveStartTime,jdbcType=TIMESTAMP}, #{leaveStartTimeType,jdbcType=INTEGER}, #{leaveEndTime,jdbcType=TIMESTAMP}, 
      #{leaveEndTimeType,jdbcType=INTEGER}, #{leaveDuration,jdbcType=DECIMAL}, #{leaveReason,jdbcType=VARCHAR}, 
      #{applyTime,jdbcType=TIMESTAMP}, #{auditStatus,jdbcType=INTEGER}, #{auditorName,jdbcType=VARCHAR}, 
      #{auditTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TAttendanceAskLeave" >
    insert into t_attendance_ask_leave
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="staffId != null" >
        staff_id,
      </if>
      <if test="staffName != null" >
        staff_name,
      </if>
      <if test="staffMobile != null" >
        staff_mobile,
      </if>
      <if test="staffProperty != null" >
        staff_property,
      </if>
      <if test="leaveType != null" >
        leave_type,
      </if>
      <if test="leaveStartTime != null" >
        leave_start_time,
      </if>
      <if test="leaveStartTimeType != null" >
        leave_start_time_type,
      </if>
      <if test="leaveEndTime != null" >
        leave_end_time,
      </if>
      <if test="leaveEndTimeType != null" >
        leave_end_time_type,
      </if>
      <if test="leaveDuration != null" >
        leave_duration,
      </if>
      <if test="leaveReason != null" >
        leave_reason,
      </if>
      <if test="applyTime != null" >
        apply_time,
      </if>
      <if test="auditStatus != null" >
        audit_status,
      </if>
      <if test="auditorName != null" >
        auditor_name,
      </if>
      <if test="auditTime != null" >
        audit_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="staffId != null" >
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null" >
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null" >
        #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="staffProperty != null" >
        #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="leaveType != null" >
        #{leaveType,jdbcType=INTEGER},
      </if>
      <if test="leaveStartTime != null" >
        #{leaveStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveStartTimeType != null" >
        #{leaveStartTimeType,jdbcType=INTEGER},
      </if>
      <if test="leaveEndTime != null" >
        #{leaveEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveEndTimeType != null" >
        #{leaveEndTimeType,jdbcType=INTEGER},
      </if>
      <if test="leaveDuration != null" >
        #{leaveDuration,jdbcType=DECIMAL},
      </if>
      <if test="leaveReason != null" >
        #{leaveReason,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null" >
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditStatus != null" >
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null" >
        #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TAttendanceAskLeave" >
    update t_attendance_ask_leave
    <set >
      <if test="staffId != null" >
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null" >
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null" >
        staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="staffProperty != null" >
        staff_property = #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="leaveType != null" >
        leave_type = #{leaveType,jdbcType=INTEGER},
      </if>
      <if test="leaveStartTime != null" >
        leave_start_time = #{leaveStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveStartTimeType != null" >
        leave_start_time_type = #{leaveStartTimeType,jdbcType=INTEGER},
      </if>
      <if test="leaveEndTime != null" >
        leave_end_time = #{leaveEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveEndTimeType != null" >
        leave_end_time_type = #{leaveEndTimeType,jdbcType=INTEGER},
      </if>
      <if test="leaveDuration != null" >
        leave_duration = #{leaveDuration,jdbcType=DECIMAL},
      </if>
      <if test="leaveReason != null" >
        leave_reason = #{leaveReason,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null" >
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditStatus != null" >
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TAttendanceAskLeave" >
    update t_attendance_ask_leave
    set staff_id = #{staffId,jdbcType=BIGINT},
      staff_name = #{staffName,jdbcType=VARCHAR},
      staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      staff_property = #{staffProperty,jdbcType=INTEGER},
      leave_type = #{leaveType,jdbcType=INTEGER},
      leave_start_time = #{leaveStartTime,jdbcType=TIMESTAMP},
      leave_start_time_type = #{leaveStartTimeType,jdbcType=INTEGER},
      leave_end_time = #{leaveEndTime,jdbcType=TIMESTAMP},
      leave_end_time_type = #{leaveEndTimeType,jdbcType=INTEGER},
      leave_duration = #{leaveDuration,jdbcType=DECIMAL},
      leave_reason = #{leaveReason,jdbcType=VARCHAR},
      apply_time = #{applyTime,jdbcType=TIMESTAMP},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      auditor_name = #{auditorName,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>