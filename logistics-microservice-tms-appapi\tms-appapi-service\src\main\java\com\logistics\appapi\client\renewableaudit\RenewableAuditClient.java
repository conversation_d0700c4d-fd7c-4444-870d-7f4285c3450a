package com.logistics.appapi.client.renewableaudit;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.renewableaudit.hystrix.RenewableAuditClientHystrix;
import com.logistics.appapi.client.renewableaudit.request.*;
import com.logistics.appapi.client.renewableaudit.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/14 14:49
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = RenewableAuditClientHystrix.class)
public interface RenewableAuditClient {

    @ApiOperation("订单tab汇总")
    @PostMapping(value = "/service/applet/yeloLife/renewableOrderListStatistic")
    Result<RenewableOrderListStatisticResponseModel> renewableOrderListStatistic();

    @ApiOperation("新生订单确认列表")
    @PostMapping(value = "/service/applet/yeloLife/renewableOrderList")
    Result<PageInfo<RenewableOrderListResponseModel>> renewableOrderList(@RequestBody RenewableOrderListRequestModel requestModel);

    @ApiOperation("新生订单详情")
    @PostMapping(value = "/service/applet/yeloLife/renewableOrderDetail")
    Result<RenewableOrderDetailResponseModel> renewableOrderDetail(@RequestBody RenewableOrderDetailRequestModel requestModel);

    @ApiOperation("新生订单详情-确认提交货物")
    @PostMapping(value = "/service/applet/yeloLife/submitGoods")
    Result<Boolean> submitGoods(@RequestBody @Valid RenewableOrderSubmitGoodsRequestModel requestModel);

    @ApiOperation("新生订单详情-确认提交单据")
    @PostMapping(value = "/service/applet/yeloLife/submitTicket")
    Result<Boolean> submitTicket(@RequestBody RenewableOrderSubmitTicketRequestModel requestModel);

    @ApiOperation("新生订单详情-确认提交信息")
    @PostMapping(value = "/service/applet/yeloLife/submitRenewableOrder")
    Result<Boolean> submitRenewableOrder(@RequestBody SubmitRenewableOrderRequestModel requestModel);

    @ApiOperation("新生订单详情-查询收货仓库(先查询大数据再查询云仓仓库地址)")
    @PostMapping(value = "/service/applet/yeloLife/searchWarehouse")
    Result<List<SearchWarehouseResponseModel>> searchWarehouse(@RequestBody SearchWarehouseRequestModel requestModel);

    @ApiOperation("驾驶员下单-查询发货人(先查询大数据再查询新生客户地址)")
    @PostMapping(value = "/service/applet/yeloLife/searchConsignor")
    Result<List<SearchConsignorResponseModel>> searchConsignor(@RequestBody SearchConsignorRequestModel requestModel);

    @ApiOperation("驾驶员下单")
    @PostMapping(value = "/service/applet/yeloLife/publishRenewableOrder")
    Result<PublishRenewableOrderResponseModel> publishRenewableOrder(@RequestBody PublishRenewableOrderRequestModel requestModel);

    @ApiOperation("查询sku下拉列表")
    @PostMapping(value = "/service/applet/yeloLife/searchLifeSku")
    Result<List<SearchLifeSkuResponseModel>> searchLifeSku(@RequestBody SearchLifeSkuRequestModel requestModel);

    @ApiOperation("查看sku示例内容")
    @PostMapping(value = "/service/applet/yeloLife/searchSkuDetail")
    Result<SearchSkuDetailResponseModel> searchSkuDetail(@RequestBody SearchSkuDetailRequestModel requestModel);

}
