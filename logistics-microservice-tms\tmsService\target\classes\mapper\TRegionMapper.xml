<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRegionMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TRegion" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="region_name" property="regionName" jdbcType="VARCHAR" />
    <result column="contact_name" property="contactName" jdbcType="VARCHAR" />
    <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR" />
    <result column="enabled" property="enabled" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, region_name, contact_name, contact_phone, enabled, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_region
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_region
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TRegion" >
    insert into t_region (id, region_name, contact_name, 
      contact_phone, enabled, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{regionName,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR}, 
      #{contactPhone,jdbcType=VARCHAR}, #{enabled,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TRegion" keyProperty="id" useGeneratedKeys="true">
    insert into t_region
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="regionName != null" >
        region_name,
      </if>
      <if test="contactName != null" >
        contact_name,
      </if>
      <if test="contactPhone != null" >
        contact_phone,
      </if>
      <if test="enabled != null" >
        enabled,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="regionName != null" >
        #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null" >
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null" >
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null" >
        #{enabled,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TRegion" >
    update t_region
    <set >
      <if test="regionName != null" >
        region_name = #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null" >
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null" >
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null" >
        enabled = #{enabled,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TRegion" >
    update t_region
    set region_name = #{regionName,jdbcType=VARCHAR},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>