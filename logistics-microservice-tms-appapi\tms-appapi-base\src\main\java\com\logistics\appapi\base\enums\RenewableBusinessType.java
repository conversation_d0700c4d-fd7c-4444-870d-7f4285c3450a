package com.logistics.appapi.base.enums;

/**
 * <AUTHOR>
 * @date 2022/9/19 17:13
 */
public enum RenewableBusinessType {

    COMPANY(1, "公司"),

    PERSON(2, "个人");

    private Integer code;
    private String name;

    RenewableBusinessType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
