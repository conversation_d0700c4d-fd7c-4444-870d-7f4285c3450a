package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/5/30 19:35
 */
public class ExportExcelInsuranceCompanyInfo {
    private ExportExcelInsuranceCompanyInfo() {
    }

    private static final Map<String, String> INSURANCE_COMPANY_INFO;

    static {
        INSURANCE_COMPANY_INFO = new LinkedHashMap<>();
        INSURANCE_COMPANY_INFO.put("状态", "enabledLabel");
        INSURANCE_COMPANY_INFO.put("保险公司全称", "companyName");
        INSURANCE_COMPANY_INFO.put("备注", "remark");
        INSURANCE_COMPANY_INFO.put("添加人", "addUserName");
        INSURANCE_COMPANY_INFO.put("最近编辑人", "lastModifiedBy");
        INSURANCE_COMPANY_INFO.put("最新编辑时间", "lastModifiedTime");
    }

    public static Map<String, String> getInsuranceCompanyInfo() {
        return INSURANCE_COMPANY_INFO;
    }
}
