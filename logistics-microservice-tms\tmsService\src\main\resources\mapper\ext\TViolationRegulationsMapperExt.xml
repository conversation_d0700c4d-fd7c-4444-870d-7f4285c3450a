<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TViolationRegulationsMapper">
    <select id="getViolationRegulationSummaryData" resultType="com.logistics.tms.api.feign.violationregulation.model.SearchViolationRegulationListResponseModel">
       select
          ifnull(count(*),0) as countViolationRegulations,
          ifnull(sum(tqvr.deduction),0) as sumDeduction,
          ifnull(sum(tqvr.fine),0) as sumFine
       from t_violation_regulations tqvr
       left join t_staff_basic tqsb on tqsb.id = tqvr.driver_id and tqsb.valid = 1 and tqsb.type in (1,3)
       left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqvr.vehicle_id and tqvd.valid = 1
       where tqvr.valid = 1
       <!--<if test="params.vehicleNo!=null and params.vehicleNo!=''">
         and instr(tqvd.vehicle_no,#{params.vehicleNo,jdbcType = VARCHAR})>0
       </if>
       <if test="params.driverName!=null and params.driverName!=''">
         and instr(tqsb.name,#{params.driverName,jdbcType = VARCHAR})>0
       </if>
       <if test="params.occuranceTimeFrom!=null and params.occuranceTimeFrom!=''">
         and tqvr.occurance_time >= DATE_FORMAT(#{params.occuranceTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
       </if>
       <if test="params.occuranceTimeTo!=null and params.occuranceTimeTo!='' ">
         and tqvr.occurance_time &lt;= DATE_FORMAT(#{params.occuranceTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
       </if>
       <if test="params.lastModifiedTimeFrom!=null and params.lastModifiedTimeFrom!=''" >
         and tqvr.last_modified_time >= DATE_FORMAT(#{params.lastModifiedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
       </if>
       <if test="params.lastModifiedTimeTo!=null and params.lastModifiedTimeTo!=''" >
         and tqvr.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
       </if>
       <if test="params.lastModifiedBy!=null and params.lastModifiedBy!=''" >
        and instr(tqvr.last_modified_by,#{params.lastModifiedBy,jdbcType = VARCHAR})>0
       </if>-->
    </select>

    <select id="getViolationRegulationIds" resultType="java.lang.Long">
      select distinct
             tqvr.id as violationRegulationId
      from t_violation_regulations tqvr
      left join t_staff_basic tqsb on tqsb.id = tqvr.driver_id and tqsb.valid = 1 and tqsb.type in (1,3)
      left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqvr.vehicle_id and tqvd.valid = 1
      where tqvr.valid = 1
      <if test="params.vehicleNo!=null and params.vehicleNo!=''">
        and instr(tqvd.vehicle_no,#{params.vehicleNo,jdbcType = VARCHAR})>0
      </if>
      <if test="params.driverName!=null and params.driverName!='' ">
        and (instr(tqsb.name,#{params.driverName,jdbcType = VARCHAR})>0 or instr(tqsb.mobile,#{params.driverName,jdbcType = VARCHAR})>0)
      </if>
      <if test="params.occuranceTimeFrom!=null and params.occuranceTimeFrom!=''">
        and tqvr.occurance_time >= DATE_FORMAT(#{params.occuranceTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
      </if>
      <if test="params.occuranceTimeTo!=null and params.occuranceTimeTo!=''">
        and tqvr.occurance_time &lt;= DATE_FORMAT(#{params.occuranceTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
      </if>
      <if test="params.lastModifiedTimeFrom!=null and params.lastModifiedTimeFrom!=''" >
        and tqvr.last_modified_time >= DATE_FORMAT(#{params.lastModifiedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
      </if>
      <if test="params.lastModifiedTimeTo!=null and params.lastModifiedTimeTo!=''" >
        and tqvr.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
      </if>
      <if test="params.lastModifiedBy!=null and params.lastModifiedBy!=''" >
        and instr(tqvr.last_modified_by,#{params.lastModifiedBy,jdbcType = VARCHAR})>0
      </if>
      <if test="params.vehicleProperty != null">
        and tqvr.vehicle_property = #{params.vehicleProperty,jdbcType=INTEGER}
      </if>
      order by tqvr.occurance_time desc,tqvr.id desc
    </select>

    <select id="getViolationRegulationListByIds" resultMap="violationRegulationListMap" >
      select
         tqvr.id as violationRegulationId,
         tqvr.vehicle_id,
         tqvr.driver_id,
         tqvr.occurance_time,
         tqvr.occurance_address,
         tqvr.fine,
         tqvr.deduction,
         tqvr.remark,
         tqvr.last_Modified_By,
         tqvr.last_modified_time,
         tqvr.vehicle_property,
         tqvd.vehicle_no,
         tqsb.name,
         tqsb.mobile,
         tqcp.id as fileId,
         tqcp.file_path
      from t_violation_regulations tqvr
      left join t_staff_basic tqsb on tqsb.id = tqvr.driver_id and tqsb.valid = 1 and tqsb.type in (1,3)
      left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqvr.vehicle_id and tqvd.valid = 1
      left join t_certification_pictures tqcp on tqcp.object_id = tqvr.id and tqcp.object_type = 6  and tqcp.file_type=1 and tqcp.valid = 1
      where tqvr.id in (${ids})
      order by tqvr.occurance_time desc,tqvr.id desc
    </select>
    <resultMap id="violationRegulationListMap" type="com.logistics.tms.api.feign.violationregulation.model.ViolationRegulationListResponseModel">
      <id column="violationRegulationId" property="violationRegulationId" jdbcType="BIGINT"/>
      <result column="vehicle_id" property="vehicleId" jdbcType="BIGINT"/>
      <result column="driver_id" property="driverId" jdbcType="BIGINT"/>
      <result column="occurance_time" property="occuranceTime" jdbcType="TIMESTAMP"/>
      <result column="occurance_address" property="occuranceAddress" jdbcType="VARCHAR"/>
      <result column="fine" property="fine" jdbcType="DECIMAL"/>
      <result column="deduction" property="deduction" jdbcType="INTEGER"/>
      <result column="remark" property="remark" jdbcType="VARCHAR"/>
      <result column="last_Modified_By" property="lastModifiedBy" jdbcType="VARCHAR"/>
      <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
      <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
      <result column="name" property="driverName" jdbcType="VARCHAR"/>
      <result column="mobile" property="driverPhone" jdbcType="VARCHAR"/>
      <result column="vehicle_property" property="vehicleProperty" jdbcType="VARCHAR"/>
      <collection property="fileList" ofType="com.logistics.tms.api.feign.violationregulation.model.CertificationPicturesResponseModel">
        <id column="fileId" property="fileId" jdbcType="BIGINT" />
        <result column="file_path" property="relativeFilepath" jdbcType="VARCHAR"/>
      </collection>
    </resultMap>

    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TViolationRegulations" keyProperty="id" useGeneratedKeys="true">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into t_violation_regulations
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.vehicleId != null">
                    vehicle_id,
                </if>
                <if test="item.vehicleProperty != null">
                    vehicle_property,
                </if>
                <if test="item.driverId != null">
                    driver_id,
                </if>
                <if test="item.deduction != null">
                    deduction,
                </if>
                <if test="item.fine != null">
                    fine,
                </if>
                <if test="item.occuranceTime != null">
                    occurance_time,
                </if>
                <if test="item.occuranceAddress != null">
                    occurance_address,
                </if>
                <if test="item.remark != null">
                    remark,
                </if>
                <if test="item.source != null">
                    source,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleId != null">
                    #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleProperty != null">
                    #{item.vehicleProperty,jdbcType=INTEGER},
                </if>
                <if test="item.driverId != null">
                    #{item.driverId,jdbcType=BIGINT},
                </if>
                <if test="item.deduction != null">
                    #{item.deduction,jdbcType=INTEGER},
                </if>
                <if test="item.fine != null">
                    #{item.fine,jdbcType=DECIMAL},
                </if>
                <if test="item.occuranceTime != null">
                    #{item.occuranceTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.occuranceAddress != null">
                    #{item.occuranceAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null">
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.source != null">
                    #{item.source,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="getViolationRegulations" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_violation_regulations where valid = 1  and vehicle_id in (${vehicleIds})
    </select>


    <select id="selectRecordByVehicleIdAndDriverIdAndOccurTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_violation_regulations where valid = 1
        and vehicle_id =#{vehicleId,jdbcType=BIGINT}
        and driver_id  = #{driverId,jdbcType=BIGINT}
        <if test="date!=null">
           and occurance_time = #{date,jdbcType=TIMESTAMP}
        </if>
        <if test="date==null">
            and occurance_time is null
        </if>
        limit 1
    </select>
    
    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update t_violation_regulations
            <set>
                <if test="item.vehicleId != null">
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.driverId != null">
                    driver_id = #{item.driverId,jdbcType=BIGINT},
                </if>
                <if test="item.deduction != null">
                    deduction = #{item.deduction,jdbcType=INTEGER},
                </if>
                <if test="item.fine != null">
                    fine = #{item.fine,jdbcType=DECIMAL},
                </if>
                <if test="item.occuranceTime != null">
                    occurance_time = #{item.occuranceTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.occuranceAddress != null">
                    occurance_address = #{item.occuranceAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.source != null">
                    source = #{item.source,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

</mapper>