package com.logistics.tms.controller.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date:2019/6/4 13:40
 *
 */

@Data
public class CertificationPicturesResponseModel  {
    @ApiModelProperty("凭证Id")
    private String fileId = "";
    @ApiModelProperty("凭证绝对路径")
    private String absoluteFilePath = "";
    @ApiModelProperty("凭证相对路径")
    private String relativeFilepath  = "";
    @ApiModelProperty("上传时间")
    private Date uploadTime;
    @ApiModelProperty("上传人员")
    private String uploadUserName="";
}
