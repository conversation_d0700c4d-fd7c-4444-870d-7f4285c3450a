package com.logistics.tms.api.feign.vehiclepayeerel.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/7/11 14:09
 */
@Data
public class SearchVehiclePayeeRelListResponseModel {
    @ApiModelProperty("id")
    private Long vehiclePayeeRelId;
    @ApiModelProperty("车辆机构 1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("关联账户：姓名")
    private String name;
    @ApiModelProperty("关联账户：手机号")
    private String mobile;
    @ApiModelProperty("关联账户：银行名称")
    private String bankName;
    @ApiModelProperty("关联账户：银行账号")
    private String bankCardNo;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;

    @ApiModelProperty("身份证号")
    private String identityNo;
}
