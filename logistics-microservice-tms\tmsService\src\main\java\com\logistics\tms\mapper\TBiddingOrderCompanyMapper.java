package com.logistics.tms.mapper;

import com.logistics.tms.entity.TBiddingOrderCompany;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/06/04
*/
@Mapper
public interface TBiddingOrderCompanyMapper extends BaseMapper<TBiddingOrderCompany> {

    List<TBiddingOrderCompany> getByCompanyCarrierIdBiddingOrderId(@Param("companyCarrierId")Long companyCarrierId, @Param("biddingOrderId")Long biddingOrderId);
}