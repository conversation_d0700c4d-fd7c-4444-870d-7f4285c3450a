package com.logistics.appapi.client.carrierorder;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.carrierorder.hystrix.CarrierOrderClientHystrix;
import com.logistics.appapi.client.carrierorder.request.*;
import com.logistics.appapi.client.carrierorder.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2023/11/13 13:20
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = CarrierOrderClientHystrix.class)
public interface CarrierOrderClient {

    @ApiOperation(value = "查询运单列表")
    @PostMapping(value = "/service/driverApplet/carrierOrder/searchList")
    Result<PageInfo<SearchCarrierOrderListAppResponseModel>> searchList(@RequestBody SearchCarrierOrderListAppRequestModel requestModel);

    @ApiOperation(value = "查询运单列表每个状态的数量")
    @PostMapping(value = "/service/driverApplet/carrierOrder/searchListAccount")
    Result<SearchCarrierOrderCountResponseModel> searchListAccount(@RequestBody SearchCarrierOrderListAppRequestModel requestModel);

    @ApiOperation(value = "运单详情页")
    @PostMapping(value = "/service/driverApplet/carrierOrder/carrierOrderDetail")
    Result<CarrierOrderDetailAppResponseModel> carrierOrderDetail(@RequestBody CarrierOrderIdRequestModel requestModel);

    @ApiOperation(value = "物流详情页")
    @PostMapping(value = "/service/driverApplet/carrierOrder/carrierOrderLogisticsDetail")
    Result<CarrierOrderLogisticsDetailResponseModel> carrierOrderLogisticsDetail(@RequestBody CarrierOrderIdRequestModel requestModel);

    @ApiOperation("提货")
    @PostMapping({"/service/driverApplet/carrierOrder/pickUp"})
    Result pickUp(@RequestBody CarrierOrderLoadRequestModel requestModel);

    @ApiOperation(value = "到达提货地v2(司机承担触达)")
    @PostMapping(value = "/service/driverApplet/carrierOrder/arrivePickUpV2")
    Result<Boolean> arrivePickUpV2(@RequestBody ArrivePickUpV2RequestModel requestModel);

    @ApiOperation(value = "卸货")
    @PostMapping(value = "/service/driverApplet/carrierOrder/unloading")
    Result<Boolean> unloading(@RequestBody CarrierOrderUnloadRequestModel requestModel);

    @ApiOperation(value = "到达提货地")
    @PostMapping(value = "/service/driverApplet/carrierOrder/arrivePickUp")
    Result<Boolean> arrivePickUp(@RequestBody CarrierOrderArriveLoadUnloadRequestModel requestModel);

    @ApiOperation(value = "到达卸货地")
    @PostMapping(value = "/service/driverApplet/carrierOrder/arriveUnloading")
    Result<Boolean> arriveUnloading(@RequestBody CarrierOrderArriveLoadUnloadRequestModel requestModel);

    @ApiOperation(value = "扫一扫根据运单号查询运单id")
    @PostMapping(value = "/service/driverApplet/carrierOrder/queryIdByCarrierOrderCode")
    Result<QueryIdByCarrierOrderCodeResponseModel> queryIdByCarrierOrderCode(@RequestBody QueryIdByCarrierOrderCodeRequestModel requestModel);

    @ApiOperation(value = "司机打印详情")
    @PostMapping(value = "/service/driverApplet/carrierOrder/printBillDetail")
    Result<PrintBillDetailResponseModel> printBillDetail(@RequestBody CarrierOrderIdRequestModel requestModel);

    @ApiOperation(value = "(云盘)提货确认", tags = "3.7.0")
    @PostMapping(value = "/service/driverApplet/carrierOrder/pickupConfirm")
    Result<LeyiPickupConfirmResponseModel> pickupConfirm(@RequestBody PickupConfirmRequestModel requestModel);

    @ApiOperation(value = "司机申请修改提货数量-(拒绝)")
    @PostMapping(value = "/service/driverApplet/carrierOrder/denyLoadAmount")
    Result<Boolean> denyLoadAmount(@RequestBody CarrierOrderIdRequestModel requestModel) ;

    @ApiOperation(value = "查询云盘二维码")
    @PostMapping(value = "/service/carrierOrderManagement/getLeYiQrCode")
    Result<GetLeYiQrCodeResponseModel> getLeYiQrCode(@RequestBody GetLeYiQrCodeRequestModel requestModel);

    @ApiOperation(value = "下载提货单", notes = "")
    @PostMapping(value = "/service/management/carrierOrderManagement/downloadLadingBill")
    Result<DownloadLadingBillResponseModel> downloadLadingBill(@RequestBody CarrierOrderDetailRequestModel requestModel);

    @ApiOperation(value = "校验接口是否能满足多提接口")
    @PostMapping(value = "/service/carrierOrderManagement/verifyEnablePickUpMore")
    Result<Boolean> verifyEnablePickUpMore(@RequestBody VerifyEnablePickUpMoreReqModel requestModel);

    @ApiOperation(value = "补单2-关联调度单信息 v2.6.8", tags = "2.6.8")
    @PostMapping(value = "/driverApplet/carrierOrder/associateExtDemandOrder")
    Result<AssociateExtDemandOrderRespModel> associateExtDemandOrder(@RequestBody @Valid AssociateExtDemandOrderReqModel requestModel);



}
