package com.logistics.management.webapi.client.region.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/10/18 9:57
 */
@Data
public class SearchRegionResponseModel {
    @ApiModelProperty("物流大区ID")
    private Long regionId;
    @ApiModelProperty("是否禁用 1 启用  0 禁用")
    private Integer enabled;
    @ApiModelProperty("大区名称")
    private String regionName;
    @ApiModelProperty("大区负责人姓名+手机号")
    private String contactName;
    @ApiModelProperty("大区负责人手机号")
    private String contactPhone;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
}
