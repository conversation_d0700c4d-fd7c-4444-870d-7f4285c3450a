package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OCR识别模板Id枚举
 */
@Getter
@AllArgsConstructor
public enum OCRTemplateEnum {

    /**
     *  云盘回收类型模板ID
     */
    LEYI_RECYCLING_TYPE_TEMPLATE_ID("c9774d41ddfc336195010b8ab1e008d4"),

    /**
     * 云盘非回收类型模板ID
     */
    LEYI_NON_RECYCLING_TYPE_TEMPLATE_ID("5d0b1765184b1b543cfd3830407f5051"),

    /**
     * 云盘便携式模板ID
     */
    LEYI_PORTABLE_TEMPLATE_ID("ef60673402dce617e1ae8ba863caeda6"),
    ;
    
    private final String ocrTemplateId;
}
