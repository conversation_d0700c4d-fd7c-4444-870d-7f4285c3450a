package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TInsuranceRefund;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TInsuranceRefundMapper extends BaseMapper<TInsuranceRefund> {

    int batchInsert(@Param("list")List<TInsuranceRefund> list);

    int settlementOilFilledByIds(@Param("ids")String ids,@Param("userName")String userName);

    TInsuranceRefund getByInsuranceIdType(@Param("insuranceId") Long insuranceId,@Param("insuranceType") Integer insuranceType);
}