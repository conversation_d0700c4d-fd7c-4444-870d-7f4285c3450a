package com.logistics.management.webapi.client.messagenotice.response;

import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2024/6/5 15:46
 */
@Data
public class SearchMessageNoticeListResponseModel {

    /**
     * 消息通知id
     */
    private Long messageNoticeId;

    /**
     * 状态：0 未读， 1 已读
     */
    private Integer ifRead;

    /**
     * 消息内容
     */
    private String messageBody;

    /**
     * 推送时间
     */
    private Date messagePushTime;

    /**
     * 消息id（用于置为已读）
     */
    private String messageId;

    /**
     * 消息类型（用于跳转）：5000 竞价单详情
     */
    private Integer messageType;
    /**
     * 业务表id（用于跳转）
     */
    private Long objectId;

}
