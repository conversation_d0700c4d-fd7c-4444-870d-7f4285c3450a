package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/25 9:55
 */
@Data
@Accessors(chain = true)
public class WaitDispatchStatisticsResponseModel {
    @ApiModelProperty("待发布")
    private List<WaitDispatchStatisticsModel> waitPublishList;
    @ApiModelProperty("待调度")
    private List<WaitDispatchStatisticsModel> waitDispatchList;
}
