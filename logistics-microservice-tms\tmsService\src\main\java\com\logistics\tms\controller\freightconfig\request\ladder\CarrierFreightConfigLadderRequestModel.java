package com.logistics.tms.controller.freightconfig.request.ladder;

import com.yelo.tools.utils.ListUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
public class CarrierFreightConfigLadderRequestModel {

    @ApiModelProperty(value = "阶梯配置Id, 编辑时使用")
    private Long freightConfigLadderId;

    @ApiModelProperty(value = "阶梯层级; 0 为固定单价/总价", required = true)
    private Integer ladderLevel;

    @ApiModelProperty(value = "阶梯类型; 1: KM(公里); 2: 数量;", required = true)
    private Integer ladderType;

    @ApiModelProperty(value = "阶梯起始", required = true)
    private BigDecimal ladderFrom;

    @ApiModelProperty(value = "阶梯终止（包含等于）", required = true)
    private BigDecimal ladderTo;

    @ApiModelProperty(value = "单位; 1: 件; 2: 吨; 4: 块", required = true)
    private Integer ladderUnit;

    @ApiModelProperty(value = "价格模式; 1: 单价; 2: 总价", required = true)
    private Integer priceMode;

    @ApiModelProperty(value = "价格(元)", required = true)
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "多级阶梯")
    private List<CarrierFreightConfigLadderRequestModel> ladderConfigList;

    public List<Long> getAllLadderId() {
        List<Long> ids = Lists.newArrayList();
        if (Objects.nonNull(freightConfigLadderId)) {
            ids.add(freightConfigLadderId);
            if (ListUtils.isNotEmpty(ladderConfigList)) {
                ids.addAll(this.getLadderIdByList(ladderConfigList));
            }
        }
        return ids;
    }

    private List<Long> getLadderIdByList(List<CarrierFreightConfigLadderRequestModel> ladderConfigList) {
        List<Long> ids = Lists.newArrayList();
        ladderConfigList.forEach(f -> {
            if (Objects.nonNull(f.getFreightConfigLadderId())) {
                ids.add(f.getFreightConfigLadderId());
                if (ListUtils.isNotEmpty(f.getLadderConfigList())) {
                    ids.addAll(this.getLadderIdByList(f.getLadderConfigList()));
                }
            }
        });
        return ids;
    }
}
