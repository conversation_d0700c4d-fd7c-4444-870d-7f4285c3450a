package com.logistics.management.webapi.base.enums;

/**
 * 计价类型 1 基价 2 一日游
 *
 * @Author: sj
 * @Date: 2019/12/25 19:26
 */
public enum SwitchEnum {
    DEFAULT_VALUE(-1, ""),
    OPEN(0, "关"),
    CLOSE(1, "开"),
    ;

    private final Integer key;
    private final String value;

    SwitchEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static SwitchEnum getEnum(Integer key) {
        for (SwitchEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT_VALUE;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
