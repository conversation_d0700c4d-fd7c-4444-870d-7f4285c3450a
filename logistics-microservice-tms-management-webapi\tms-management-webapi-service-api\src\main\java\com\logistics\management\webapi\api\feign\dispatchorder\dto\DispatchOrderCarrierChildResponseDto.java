package com.logistics.management.webapi.api.feign.dispatchorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DispatchOrderCarrierChildResponseDto {
    @ApiModelProperty("运单id")
    private String carrierOrderId="";
    @ApiModelProperty("运单号")
    private String carrierOrderCode="";
    @ApiModelProperty("客户单号")
    private String customerOrderCode="";
    @ApiModelProperty("运单状态")
    private String status="";
    @ApiModelProperty("品名")
    private String goodsName="";
    @ApiModelProperty("卸货地址")
    private String unloadDetailAddress="";
    @ApiModelProperty("货物数量:件数/吨位")
    private String expectAmount="";

    @ApiModelProperty("预计司机运费")
    private String dispatchFreightFee="";
    @ApiModelProperty("实际司机运费")
    private String signDispatchFreightFee="";
    @ApiModelProperty("调整价")
    private String adjustFee="";
    @ApiModelProperty("多装多卸价")
    private String markupFee="";
    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private String demandOrderSource="";

}
