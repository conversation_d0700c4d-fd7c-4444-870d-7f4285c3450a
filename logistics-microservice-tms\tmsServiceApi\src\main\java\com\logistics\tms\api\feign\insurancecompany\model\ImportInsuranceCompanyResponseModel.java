package com.logistics.tms.api.feign.insurancecompany.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/5/29 14:36
 */
@Data
public class ImportInsuranceCompanyResponseModel {
    @ApiModelProperty("失败数量")
    private Integer numberFailures = 0;
    @ApiModelProperty("成功数量")
    private Integer numberSuccessful = 0;

    public void addFailures(){
        this.numberFailures++;
    }
    public void addSuccessful(){
        this.numberSuccessful++;
    }
    public void initNumber(Integer initFailNumber){
        if(initFailNumber != null){
            this.numberFailures = this.numberFailures + initFailNumber;
        }
    }
}
