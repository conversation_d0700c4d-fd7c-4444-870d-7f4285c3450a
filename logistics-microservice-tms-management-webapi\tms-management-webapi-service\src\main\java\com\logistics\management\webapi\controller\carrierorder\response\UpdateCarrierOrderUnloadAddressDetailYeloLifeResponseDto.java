package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/9/17 15:10
 */
@Data
public class UpdateCarrierOrderUnloadAddressDetailYeloLifeResponseDto {

    @ApiModelProperty("运单ID")
    private String carrierOrderId = "";

    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("卸货地址")
    private String unloadAddress = "";
}
