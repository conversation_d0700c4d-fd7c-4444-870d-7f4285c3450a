package com.logistics.tms.biz.oilfilled;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.oilfilled.request.*;
import com.logistics.tms.controller.oilfilled.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/9
 * @description:
 */
@Slf4j
@Service
public class OilFilledBiz {

    @Autowired
    private TOilFilledMapper tOilFilledMapper;
    @Autowired
    private TOilFilledRecordsMapper tOilFilledRecordsMapper;
    @Autowired
    private TStaffBasicMapper tStaffBasicMapper;
    @Autowired
    private TVehicleBasicMapper tVehicleBasicMapper;
    @Autowired
    private TCertificationPicturesMapper tCertificationPicturesMapper;
    @Autowired
    private TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 充油列表
     * @param requestModel
     * @return
     */
    public PageInfo<OilFilledListResponseModel> searchList(OilFilledListRequestModel requestModel) {
        requestModel.enablePaging();
        List<OilFilledListResponseModel> oilFilledList = tOilFilledMapper.getOilFilledList(requestModel);
        return new PageInfo<>(oilFilledList);
    }

    /**
     * 新增/修改充油
     * @param requestModel
     */
    @Transactional
    public void addOrModify(AddOrModifyOilFilledRequestModel requestModel) {
        //查询司机信息
        TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(requestModel.getStaffId());
        if (tStaffBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
        }
        //查询车辆信息
        VehicleBasicPropertyModel vehicleBasicPropertyById = tVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (vehicleBasicPropertyById == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        //查询充油记录
        TOilFilled tOilFilledExist = null;
        if (requestModel.getOilFilledId() != null && requestModel.getOilFilledId() > CommonConstant.LONG_ZERO) {
            tOilFilledExist = tOilFilledMapper.selectByPrimaryKey(requestModel.getOilFilledId());
            if (tOilFilledExist == null || IfValidEnum.INVALID.getKey().equals(tOilFilledExist.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.OIL_FILLED_NOT_EXIST);
            }
            //已结算的不能修改
            if (OilFilledStatusEnum.HAVE_SETTLE.getKey().equals(tOilFilledExist.getStatus())){
                throw new BizException(CarrierDataExceptionEnum.SETTLEMENT_STATUS_ERROR_NOT_UPDATE);
            }
        }
        //只有新增，或则修改车辆时候需要判断
        if (requestModel.getOilFilledId() == null || (tOilFilledExist != null && !tOilFilledExist.getVehicleId().equals(requestModel.getVehicleId()))) {
            //停运的车辆不管
            if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(vehicleBasicPropertyById.getOperatingState())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_OUTAGE_ERROR);
            }
            //只有自有车辆才可以新增充油
            if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasicPropertyById.getVehicleProperty()) && !VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasicPropertyById.getVehicleProperty())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
            }
            if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasicPropertyById.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasicPropertyById.getVehicleCategory())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_ERROR);
            }
            if (!StaffPropertyEnum.OWN_STAFF.getKey().equals(tStaffBasic.getStaffProperty()) && !StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(tStaffBasic.getStaffProperty())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_PROPERTY_ERROR);
            }
        }
        String userName = BaseContextHandler.getUserName();
        TOilFilled tOilFilled = new TOilFilled();
        tOilFilled.setOilFilledType(requestModel.getOilFilledType());
        tOilFilled.setStaffId(tStaffBasic.getId());
        tOilFilled.setName(tStaffBasic.getName());
        tOilFilled.setMobile(tStaffBasic.getMobile());
        tOilFilled.setOilFilledFee(requestModel.getOilFilledFee());
        tOilFilled.setOilFilledDate(requestModel.getOilFilledDate());
        tOilFilled.setLiter(requestModel.getLiter());
        tOilFilled.setTopUpIntegral(requestModel.getTopUpIntegral());
        tOilFilled.setRewardIntegral(requestModel.getRewardIntegral());
        tOilFilled.setSubCardNumber(requestModel.getSubCardNumber());
        tOilFilled.setSubCardOwner(requestModel.getSubCardOwner());
        tOilFilled.setCooperationCompany(requestModel.getCooperationCompany());
        tOilFilled.setRemark(requestModel.getRemark());
        //如果ID为空说明是新增
        if (requestModel.getOilFilledId() == null) {
            //设置车辆信息
            tOilFilled.setVehicleId(vehicleBasicPropertyById.getVehicleId());
            tOilFilled.setVehicleNo(vehicleBasicPropertyById.getVehicleNo());
            tOilFilled.setVehicleProperty(vehicleBasicPropertyById.getVehicleProperty());
            tOilFilled.setStatus(OilFilledStatusEnum.WAIT_SETTLE.getKey());
            tOilFilled.setSource(OilFilledSourceEnum.OIL_FILLED.getKey());
            commonBiz.setBaseEntityAdd(tOilFilled, userName);
            tOilFilledMapper.insertSelective(tOilFilled);
        } else {//修改
            List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.OIL.getKey(), requestModel.getOilFilledId());
            //如果已经关联了结算数据，则不允许修改以下值，故设置为null，不更新
            if (ListUtils.isNotEmpty(tVehicleSettlementRelationList)) {
                tOilFilled.setVehicleId(null);
                tOilFilled.setVehicleNo(null);
                tOilFilled.setOilFilledType(null);
                tOilFilled.setOilFilledFee(null);
                tOilFilled.setTopUpIntegral(null);
                tOilFilled.setLiter(null);
            } else {
                //车辆有变更并且未关联结算才修改车辆信息
                if (!tOilFilledExist.getVehicleId().equals(requestModel.getVehicleId())) {
                    tOilFilled.setVehicleId(vehicleBasicPropertyById.getVehicleId());
                    tOilFilled.setVehicleNo(vehicleBasicPropertyById.getVehicleNo());
                    tOilFilled.setVehicleProperty(vehicleBasicPropertyById.getVehicleProperty());
                }
            }
            tOilFilled.setId(tOilFilledExist.getId());
            commonBiz.setBaseEntityModify(tOilFilled, userName);
            tOilFilledMapper.updateByPrimaryKeySelective(tOilFilled);
        }
        //插入修改记录
        TOilFilledRecords tOilFilledRecords = new TOilFilledRecords();
        tOilFilledRecords.setOilFilledId(tOilFilled.getId());
        tOilFilledRecords.setVehicleNo(vehicleBasicPropertyById.getVehicleNo());
        tOilFilledRecords.setOilFilledDate(requestModel.getOilFilledDate());
        tOilFilledRecords.setOilFilledType(requestModel.getOilFilledType());
        tOilFilledRecords.setOilFilledFee(requestModel.getOilFilledFee());
        tOilFilledRecords.setLiter(requestModel.getLiter());
        tOilFilledRecords.setRewardIntegral(requestModel.getRewardIntegral());
        tOilFilledRecords.setTopUpIntegral(requestModel.getTopUpIntegral());
        tOilFilledRecords.setSubCardNumber(requestModel.getSubCardNumber());
        tOilFilledRecords.setSubCardOwner(requestModel.getSubCardOwner());
        tOilFilledRecords.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityAdd(tOilFilledRecords, BaseContextHandler.getUserName());
        tOilFilledRecordsMapper.insertSelective(tOilFilledRecords);
        List<TCertificationPictures> upList = new ArrayList<>();
        List<TCertificationPictures> addList = new ArrayList<>();
        TCertificationPictures certificationPictures;
        //操作附件
        List<TCertificationPictures> tCertificationPicturesList = tCertificationPicturesMapper.getByObjectIdType(tOilFilled.getId(), CertificationPicturesObjectTypeEnum.T_OIL_FILLED.getObjectType(), CertificationPicturesFileTypeEnum.OIL_FILLED_ATTACHMENT_FILE.getFileType());
        List<String> filePathList = new ArrayList<>();
        List<Long> pictureIds = new ArrayList<>();
        if (ListUtils.isNotEmpty(tCertificationPicturesList)) {
            filePathList = tCertificationPicturesList.stream().map(TCertificationPictures::getFilePath).collect(Collectors.toList());
            pictureIds = tCertificationPicturesList.stream().map(TCertificationPictures::getId).collect(Collectors.toList());
        }
        Date now = new Date();
        List<OilFilledFileModel> addPictureList = new ArrayList<>();
        //判断哪些是新增，那些是删除
        if (ListUtils.isNotEmpty(requestModel.getOilFilledFileList())) {
            List<OilFilledFileModel> updateList = requestModel.getOilFilledFileList().stream().filter(item -> item.getFileId() != null && item.getFileId() > CommonConstant.LONG_ZERO).collect(Collectors.toList());
            addPictureList = requestModel.getOilFilledFileList().stream().filter(item -> item.getFileId() == null || item.getFileId().equals(CommonConstant.LONG_ZERO)).collect(Collectors.toList());
            List<Long> idList = new ArrayList<>();
            if (ListUtils.isNotEmpty(updateList)) {
                for (OilFilledFileModel oilFilledFileModel : updateList) {
                    if (!filePathList.contains(oilFilledFileModel.getRelativeFilepath())) {
                        certificationPictures = new TCertificationPictures();
                        certificationPictures.setId(oilFilledFileModel.getFileId());
                        certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.OIL_FILLED_ATTACHMENT_FILE_TYPE.getKey(), "", oilFilledFileModel.getRelativeFilepath(), null));
                        certificationPictures.setSuffix(oilFilledFileModel.getRelativeFilepath().substring(oilFilledFileModel.getRelativeFilepath().lastIndexOf('.')));
                        commonBiz.setBaseEntityModify(certificationPictures, userName);
                        upList.add(certificationPictures);
                    }
                    idList.add(oilFilledFileModel.getFileId());
                }
                if (ListUtils.isNotEmpty(idList)) {
                    pictureIds.removeAll(idList);
                    if (ListUtils.isNotEmpty(pictureIds)) {
                        for (Long id : pictureIds) {
                            certificationPictures = new TCertificationPictures();
                            certificationPictures.setId(id);
                            certificationPictures.setValid(IfValidEnum.INVALID.getKey());
                            commonBiz.setBaseEntityModify(certificationPictures, userName);
                            upList.add(certificationPictures);
                        }
                    }
                }
            } else {
                for (TCertificationPictures item : tCertificationPicturesList) {
                    certificationPictures = new TCertificationPictures();
                    certificationPictures.setId(item.getId());
                    certificationPictures.setValid(IfValidEnum.INVALID.getKey());
                    commonBiz.setBaseEntityModify(certificationPictures, userName);
                    upList.add(certificationPictures);
                }
            }
        } else {
            //否则删除所有数据库的所有图片
            for (TCertificationPictures item : tCertificationPicturesList) {
                certificationPictures = new TCertificationPictures();
                certificationPictures.setId(item.getId());
                certificationPictures.setValid(IfValidEnum.INVALID.getKey());
                commonBiz.setBaseEntityModify(certificationPictures, userName);
                upList.add(certificationPictures);
            }
        }
        if (ListUtils.isNotEmpty(addPictureList)) {
            for (OilFilledFileModel oilFilledFileModel : addPictureList) {
                certificationPictures = new TCertificationPictures();
                certificationPictures.setObjectId(tOilFilled.getId());
                certificationPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_OIL_FILLED.getObjectType());
                certificationPictures.setFileType(CertificationPicturesFileTypeEnum.OIL_FILLED_ATTACHMENT_FILE.getFileType());
                certificationPictures.setFileName(CertificationPicturesFileTypeEnum.OIL_FILLED_ATTACHMENT_FILE.getFileName());
                certificationPictures.setFileTypeName(CertificationPicturesFileTypeEnum.OIL_FILLED_ATTACHMENT_FILE.getFileName());
                certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.OIL_FILLED_ATTACHMENT_FILE_TYPE.getKey(), "", oilFilledFileModel.getRelativeFilepath(), null));
                certificationPictures.setUploadTime(now);
                certificationPictures.setUploadUserName(userName);
                certificationPictures.setSuffix(oilFilledFileModel.getRelativeFilepath().substring(oilFilledFileModel.getRelativeFilepath().lastIndexOf(CommonConstant.POINT)));
                commonBiz.setBaseEntityAdd(certificationPictures, userName);
                addList.add(certificationPictures);
            }
        }
        if (ListUtils.isNotEmpty(addList)) {
            tCertificationPicturesMapper.batchInsert(addList);
        }
        if (ListUtils.isNotEmpty(upList)) {
            tCertificationPicturesMapper.batchUpdate(upList);
        }
    }

    /**
     * 充油列表汇总
     * @param requestModel
     * @return
     */
    public OilFilledGetSummaryResponseModel getSummary(OilFilledListRequestModel requestModel) {
        return tOilFilledMapper.getSummary(requestModel);
    }

    /**
     * 充油详情接口
     * @param requestModel
     * @return
     */
    public OilFilledDetailResponseModel getDetail(OilFilledDetailRequestModel requestModel) {
        TOilFilled tOilFilled = tOilFilledMapper.selectByPrimaryKey(requestModel.getOilFilledId());
        if (tOilFilled == null) {
            throw new BizException(CarrierDataExceptionEnum.OIL_FILLED_NOT_EXIST);
        }
        OilFilledDetailResponseModel responseModel = new OilFilledDetailResponseModel();
        responseModel.setOilFilledId(tOilFilled.getId());
        responseModel.setOilFilledType(tOilFilled.getOilFilledType());
        responseModel.setVehicleId(tOilFilled.getVehicleId());
        responseModel.setVehicleNo(tOilFilled.getVehicleNo());
        responseModel.setStaffId(tOilFilled.getStaffId());
        responseModel.setName(tOilFilled.getName());
        responseModel.setMobile(tOilFilled.getMobile());
        responseModel.setSubCardNumber(tOilFilled.getSubCardNumber());
        responseModel.setSubCardOwner(tOilFilled.getSubCardOwner());
        responseModel.setOilFilledFee(tOilFilled.getOilFilledFee());
        responseModel.setTopUpIntegral(tOilFilled.getTopUpIntegral());
        responseModel.setRewardIntegral(tOilFilled.getRewardIntegral());
        responseModel.setOilFilledDate(tOilFilled.getOilFilledDate());
        responseModel.setCooperationCompany(tOilFilled.getCooperationCompany());
        responseModel.setLiter(tOilFilled.getLiter());
        responseModel.setRemark(tOilFilled.getRemark());
        responseModel.setStatus(tOilFilled.getStatus());
        //查询该充油费用是否已经关联了结算数据
        List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.OIL.getKey(), tOilFilled.getId());
        if(ListUtils.isNotEmpty(tVehicleSettlementRelationList)){
            responseModel.setIfSettlement(CommonConstant.INTEGER_ONE);
        }
        //查询对应的附件
        List<OilFilledFileModel> oilFilledFileList = new ArrayList<>();
        OilFilledFileModel oilFilledFileModel;
        List<TCertificationPictures> tCertificationPicturesList = tCertificationPicturesMapper.getByObjectIdType(tOilFilled.getId(), CertificationPicturesObjectTypeEnum.T_OIL_FILLED.getObjectType(), CertificationPicturesFileTypeEnum.OIL_FILLED_ATTACHMENT_FILE.getFileType());
        if (ListUtils.isNotEmpty(tCertificationPicturesList)) {
            for (TCertificationPictures tCertificationPictures : tCertificationPicturesList) {
                oilFilledFileModel = new OilFilledFileModel();
                oilFilledFileModel.setFileId(tCertificationPictures.getId());
                oilFilledFileModel.setRelativeFilepath(tCertificationPictures.getFilePath());
                oilFilledFileList.add(oilFilledFileModel);
            }
        }
        responseModel.setOilFilledFileList(oilFilledFileList);
        return responseModel;
    }

    /**
     * 充油操作记录
     * @param requestModel
     * @return
     */
    public List<OilFilledOperationRecordResponseModel> getOperationRecord(OilFilledOperationRecordRequestModel requestModel) {
        TOilFilled tOilFilled = tOilFilledMapper.selectByPrimaryKey(requestModel.getOilFilledId());
        if (tOilFilled == null) {
            throw new BizException(CarrierDataExceptionEnum.OIL_FILLED_NOT_EXIST);
        }
        List<OilFilledOperationRecordResponseModel> responseModelList = new ArrayList<>();
        List<TOilFilledRecords> tOilFilledRecords = tOilFilledRecordsMapper.listByOilFilledId(tOilFilled.getId());
        if (ListUtils.isNotEmpty(tOilFilledRecords)) {
            for (TOilFilledRecords item : tOilFilledRecords) {
                OilFilledOperationRecordResponseModel model = MapperUtils.mapper(item, OilFilledOperationRecordResponseModel.class);
                responseModelList.add(model);
            }
        }
        return responseModelList;
    }

    /**
     * 新增/修改油费退款
     * @param requestModel
     */
    @Transactional
    public void addOrModifyRefund(AddOrModifyOilRefundRequestModel requestModel) {
        //查询司机信息
        TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(requestModel.getStaffId());
        if (tStaffBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.DRIVER_NOT_EXIST);
        }
        VehicleBasicPropertyModel vehicleBasicPropertyById = tVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (vehicleBasicPropertyById == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }
        TOilFilled tOilFilledExist = null;
        if (requestModel.getOilFilledId() != null && requestModel.getOilFilledId() > CommonConstant.LONG_ZERO) {
            tOilFilledExist = tOilFilledMapper.selectByPrimaryKey(requestModel.getOilFilledId());
            if (tOilFilledExist == null || IfValidEnum.INVALID.getKey().equals(tOilFilledExist.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.OIL_FILLED_NOT_EXIST);
            }
            if (OilFilledStatusEnum.HAVE_SETTLE.getKey().equals(tOilFilledExist.getStatus())){
                throw new BizException(CarrierDataExceptionEnum.SETTLEMENT_STATUS_ERROR_NOT_UPDATE);
            }
        }
        //只有新增，或则修改车辆时候需要判断
        if (requestModel.getOilFilledId() == null || (tOilFilledExist != null && !tOilFilledExist.getVehicleNo().equals(requestModel.getVehicleNo()))) {
            //停运的车辆不管
            if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(vehicleBasicPropertyById.getOperatingState())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_OUTAGE_ERROR);
            }
            //只有自有车辆才可以油费退款
            if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasicPropertyById.getVehicleProperty()) && !VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasicPropertyById.getVehicleProperty())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
            }
            if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasicPropertyById.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasicPropertyById.getVehicleCategory())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_ERROR);
            }
            if (!StaffPropertyEnum.OWN_STAFF.getKey().equals(tStaffBasic.getStaffProperty()) && !StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(tStaffBasic.getStaffProperty())) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_PROPERTY_ERROR);
            }
        }
        String userName = BaseContextHandler.getUserName();
        TOilFilled tOilFilled = new TOilFilled();
        tOilFilled.setStaffId(tStaffBasic.getId());
        tOilFilled.setName(tStaffBasic.getName());
        tOilFilled.setMobile(tStaffBasic.getMobile());
        tOilFilled.setOilFilledFee(requestModel.getOilFilledFee());
        tOilFilled.setOilFilledDate(requestModel.getOilFilledDate());
        tOilFilled.setOilFilledType(requestModel.getOilFilledType());
        tOilFilled.setRefundReasonType(requestModel.getRefundReasonType());
        if (!OilRefundReasonTypeEnum.OIL_FILLED.getKey().equals(requestModel.getRefundReasonType()) && !OilRefundReasonTypeEnum.SYB_CARD_LOST.getKey().equals(requestModel.getRefundReasonType())) {
            tOilFilled.setRefundReason("");
        }else{
            tOilFilled.setRefundReason(requestModel.getRefundReason());
        }
        tOilFilled.setRemark(requestModel.getRemark());

        int refundCount = tOilFilledMapper.getRefundCountByVehicleId(vehicleBasicPropertyById.getVehicleId());
        //如果ID为空说明是新增
        if (requestModel.getOilFilledId() == null || requestModel.getOilFilledId() <= CommonConstant.LONG_ZERO) {
            if (refundCount >= CommonConstant.INTEGER_TEN) {
                throw new BizException(CarrierDataExceptionEnum.REFUND_COUNT_MAX);
            }
            //新增设置车辆信息
            tOilFilled.setVehicleId(vehicleBasicPropertyById.getVehicleId());
            tOilFilled.setVehicleNo(vehicleBasicPropertyById.getVehicleNo());
            tOilFilled.setVehicleProperty(vehicleBasicPropertyById.getVehicleProperty());
            tOilFilled.setStatus(OilFilledStatusEnum.WAIT_SETTLE.getKey());
            tOilFilled.setSource(OilFilledSourceEnum.REFUND.getKey());
            commonBiz.setBaseEntityAdd(tOilFilled, userName);
            tOilFilledMapper.insertSelective(tOilFilled);
        } else {
            if (refundCount >= CommonConstant.INTEGER_TEN && !requestModel.getVehicleId().equals(tOilFilledExist.getVehicleId())){
                throw new BizException(CarrierDataExceptionEnum.REFUND_COUNT_MAX);
            }
            List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.OIL_REFUND.getKey(), requestModel.getOilFilledId());
            //如果已经关联了结算数据，则不允许修改以下值，故设置为null，不更新
            if (ListUtils.isNotEmpty(tVehicleSettlementRelationList)) {
                tOilFilled.setVehicleId(null);
                tOilFilled.setVehicleNo(null);
                tOilFilled.setOilFilledType(null);
                tOilFilled.setOilFilledFee(null);
            } else {
                //车辆有变更并且未关联结算才修改车辆信息
                if (!tOilFilledExist.getVehicleId().equals(requestModel.getVehicleId())) {
                    tOilFilled.setVehicleId(vehicleBasicPropertyById.getVehicleId());
                    tOilFilled.setVehicleNo(vehicleBasicPropertyById.getVehicleNo());
                    tOilFilled.setVehicleProperty(vehicleBasicPropertyById.getVehicleProperty());
                }
            }
            tOilFilled.setId(tOilFilledExist.getId());
            commonBiz.setBaseEntityModify(tOilFilled, userName);
            tOilFilledMapper.updateByPrimaryKeySelective(tOilFilled);

            List<TCertificationPictures> tCertificationPicturesList = tCertificationPicturesMapper.getByObjectIdType(tOilFilled.getId(), CertificationPicturesObjectTypeEnum.T_OIL_FILLED.getObjectType(), CertificationPicturesFileTypeEnum.OIL_REFUND_ATTACHMENT_FILE.getFileType());
            if (ListUtils.isNotEmpty(tCertificationPicturesList)){
                TCertificationPictures tCertificationPictures = tCertificationPicturesList.get(CommonConstant.INTEGER_ZERO);
                if (StringUtils.isNotBlank(requestModel.getRefundFile())){
                    if (!tCertificationPictures.getFilePath().equals(requestModel.getRefundFile())){//修改
                        TCertificationPictures certificationPictures = new TCertificationPictures();
                        certificationPictures.setId(tCertificationPictures.getId());
                        certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.OIL_FILLED_ATTACHMENT_FILE_TYPE.getKey(), "", requestModel.getRefundFile(), null));
                        certificationPictures.setSuffix(requestModel.getRefundFile().substring(requestModel.getRefundFile().lastIndexOf(CommonConstant.POINT)));
                        commonBiz.setBaseEntityModify(certificationPictures, userName);
                        tCertificationPicturesMapper.updateByPrimaryKeySelective(certificationPictures);
                    }
                    requestModel.setRefundFile(null);
                }else{//删除
                    TCertificationPictures certificationPictures = new TCertificationPictures();
                    certificationPictures.setId(tCertificationPictures.getId());
                    certificationPictures.setValid(IfValidEnum.INVALID.getKey());
                    commonBiz.setBaseEntityModify(certificationPictures, userName);
                    tCertificationPicturesMapper.updateByPrimaryKeySelective(certificationPictures);
                }
            }
        }
        //新增操作记录
        TOilFilledRecords tOilFilledRecords = new TOilFilledRecords();
        tOilFilledRecords.setOilFilledId(tOilFilled.getId());
        tOilFilledRecords.setVehicleNo(vehicleBasicPropertyById.getVehicleNo());
        tOilFilledRecords.setOilFilledDate(requestModel.getOilFilledDate());
        tOilFilledRecords.setOilFilledType(requestModel.getOilFilledType());
        tOilFilledRecords.setOilFilledFee(requestModel.getOilFilledFee());
        tOilFilledRecords.setRefundReasonType(requestModel.getRefundReasonType());
        tOilFilledRecords.setRefundReason(requestModel.getRefundReason());
        tOilFilledRecords.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityAdd(tOilFilledRecords, BaseContextHandler.getUserName());
        tOilFilledRecordsMapper.insertSelective(tOilFilledRecords);
        //新增附件
        if (StringUtils.isNotBlank(requestModel.getRefundFile())){
            TCertificationPictures certificationPictures = new TCertificationPictures();
            certificationPictures.setObjectId(tOilFilled.getId());
            certificationPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_OIL_FILLED.getObjectType());
            certificationPictures.setFileType(CertificationPicturesFileTypeEnum.OIL_REFUND_ATTACHMENT_FILE.getFileType());
            certificationPictures.setFileName(CertificationPicturesFileTypeEnum.OIL_REFUND_ATTACHMENT_FILE.getFileName());
            certificationPictures.setFileTypeName(CertificationPicturesFileTypeEnum.OIL_REFUND_ATTACHMENT_FILE.getFileName());
            certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.OIL_FILLED_ATTACHMENT_FILE_TYPE.getKey(), "", requestModel.getRefundFile(), null));
            certificationPictures.setUploadTime(new Date());
            certificationPictures.setUploadUserName(userName);
            certificationPictures.setSuffix(requestModel.getRefundFile().substring(requestModel.getRefundFile().lastIndexOf(CommonConstant.POINT)));
            commonBiz.setBaseEntityAdd(certificationPictures, userName);
            tCertificationPicturesMapper.insertSelective(certificationPictures);
        }
    }

    /**
     * 油费退款详情
     * @param requestModel
     * @return
     */
    public OilRefundDetailResponseModel getOilRefundDetail(OilFilledDetailRequestModel requestModel) {
        TOilFilled tOilFilled = tOilFilledMapper.selectByPrimaryKey(requestModel.getOilFilledId());
        if (tOilFilled == null || tOilFilled.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.OIL_FILLED_NOT_EXIST);
        }
        OilRefundDetailResponseModel responseModel = MapperUtils.mapperNoDefault(tOilFilled,OilRefundDetailResponseModel.class);
        responseModel.setOilFilledId(tOilFilled.getId());
        List<TCertificationPictures> tCertificationPicturesList = tCertificationPicturesMapper.getByObjectIdType(tOilFilled.getId(), CertificationPicturesObjectTypeEnum.T_OIL_FILLED.getObjectType(), CertificationPicturesFileTypeEnum.OIL_REFUND_ATTACHMENT_FILE.getFileType());
        if (ListUtils.isNotEmpty(tCertificationPicturesList)){
            responseModel.setRefundFile(tCertificationPicturesList.get(CommonConstant.INTEGER_ZERO).getFilePath());
        }
        //查询该充油费用是否已经关联了结算数据
        List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.OIL_REFUND.getKey(), tOilFilled.getId());
        if(ListUtils.isNotEmpty(tVehicleSettlementRelationList)){
            responseModel.setIfSettlement(CommonConstant.INTEGER_ONE);
        }
        return responseModel;
    }

    /**
     * 导入充油卡
     * @param requestModel
     * @return
     */
    @Transactional
    public ImportOilFilledResponseModel importRefuelCard(ImportOilFilledCardInfoRequestModel requestModel) {
        ImportOilFilledResponseModel responseModel = new ImportOilFilledResponseModel();
        List<ImportOilFilledCardListRequestModel> importList = requestModel.getImportList();
        List<TOilFilledRecords> addRecordsList = new ArrayList<>();

        responseModel.initNumber(requestModel.getNumberFailures());
        if(ListUtils.isEmpty(importList)){
            return responseModel;
        }

        List<String> vehicleNoList = importList.stream().map(ImportOilFilledCardListRequestModel::getVehicleNo).collect(Collectors.toList());
        List<String> driverPhoneList = importList.stream().map(ImportOilFilledCardListRequestModel::getDriverPhone).collect(Collectors.toList());
        List<VehicleBasicPropertyModel> vehicleBasicPropertyList = tVehicleBasicMapper.getVehicleNoByNos(LocalStringUtil.listTostring(vehicleNoList,','));
        List<TStaffBasic> getStaffByMobiles = tStaffBasicMapper.getStaffByMobiles(LocalStringUtil.listTostring(driverPhoneList,','));
        Map<String, VehicleBasicPropertyModel> vehicleBasicMap = vehicleBasicPropertyList.stream().collect(Collectors.toMap(VehicleBasicPropertyModel::getVehicleNo, Function.identity()));
        Map<String, TStaffBasic> staffBasicMap = getStaffByMobiles.stream().collect(Collectors.toMap(TStaffBasic::getMobile, Function.identity()));

        VehicleBasicPropertyModel vehicleBasic;
        TStaffBasic staffBasic;
        for (ImportOilFilledCardListRequestModel itemModel : importList) {
            vehicleBasic = vehicleBasicMap.get(itemModel.getVehicleNo());
            staffBasic = staffBasicMap.get(itemModel.getDriverPhone());
            if (vehicleBasic == null || staffBasic == null) {
                responseModel.addFailures();
                continue;
            }
            if (!itemModel.getDriverName().equals(staffBasic.getName())) {
                responseModel.addFailures();
                continue;
            }
            if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(vehicleBasic.getOperatingState())) {
                responseModel.addFailures();
                continue;
            }
            if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty()) && !VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty())) {
                responseModel.addFailures();
                continue;
            }
            if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasic.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasic.getVehicleCategory())) {
                responseModel.addFailures();
                continue;
            }
            if (!StaffPropertyEnum.OWN_STAFF.getKey().equals(staffBasic.getStaffProperty()) && !StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(staffBasic.getStaffProperty())) {
                responseModel.addFailures();
                continue;
            }
            TOilFilled tOilFilled = buildRefuelCard(vehicleBasic,staffBasic,itemModel);
            tOilFilledMapper.insertSelective(tOilFilled);

            //插入修改记录
            TOilFilledRecords tOilFilledRecords = buildRefuelCardRecord(itemModel);
            tOilFilledRecords.setOilFilledId(tOilFilled.getId());
            addRecordsList.add(tOilFilledRecords);

            responseModel.addSuccessful();
        }

        if (ListUtils.isNotEmpty(addRecordsList)) {
            tOilFilledRecordsMapper.batchInsert(addRecordsList);
        }
        return responseModel;
    }

    /**
     * 导入加油车
     * @param requestModel
     * @return
     */
    @Transactional
    public ImportOilFilledResponseModel importRefuelCar(ImportOilFilledCarInfoRequestModel requestModel) {
        ImportOilFilledResponseModel responseModel = new ImportOilFilledResponseModel();
        List<ImportOilFilledCarListRequestModel> importList = requestModel.getImportList();
        List<TOilFilledRecords> addRecordsList = new ArrayList<>();
        responseModel.initNumber(requestModel.getNumberFailures());
        if(ListUtils.isEmpty(importList)){
            return responseModel;
        }

        List<String> vehicleNoList = importList.stream().map(ImportOilFilledCarListRequestModel::getVehicleNo).collect(Collectors.toList());
        List<String> driverPhoneList = importList.stream().map(ImportOilFilledCarListRequestModel::getDriverPhone).collect(Collectors.toList());
        List<VehicleBasicPropertyModel> vehicleBasicPropertyList = tVehicleBasicMapper.getVehicleNoByNos(LocalStringUtil.listTostring(vehicleNoList,','));
        List<TStaffBasic> getStaffByMobiles = tStaffBasicMapper.getStaffByMobiles(LocalStringUtil.listTostring(driverPhoneList,','));
        Map<String, VehicleBasicPropertyModel> vehicleBasicMap = vehicleBasicPropertyList.stream().collect(Collectors.toMap(VehicleBasicPropertyModel::getVehicleNo, Function.identity()));
        Map<String, TStaffBasic> staffBasicMap = getStaffByMobiles.stream().collect(Collectors.toMap(TStaffBasic::getMobile, Function.identity()));

        VehicleBasicPropertyModel vehicleBasic;
        TStaffBasic staffBasic;
        for (ImportOilFilledCarListRequestModel itemModel : requestModel.getImportList()) {
            vehicleBasic = vehicleBasicMap.get(itemModel.getVehicleNo());
            staffBasic = staffBasicMap.get(itemModel.getDriverPhone());
            if (vehicleBasic == null || staffBasic == null) {
                responseModel.addFailures();
                continue;
            }
            if (!itemModel.getDriverName().equals(staffBasic.getName())) {
                responseModel.addFailures();
                continue;
            }
            if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(vehicleBasic.getOperatingState())) {
                responseModel.addFailures();
                continue;
            }
            if (!VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty()) && !VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasic.getVehicleProperty())) {
                responseModel.addFailures();
                continue;
            }
            if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasic.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasic.getVehicleCategory())) {
                responseModel.addFailures();
                continue;
            }
            if (!StaffPropertyEnum.OWN_STAFF.getKey().equals(staffBasic.getStaffProperty()) && !StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(staffBasic.getStaffProperty())) {
                responseModel.addFailures();
                continue;
            }
            TOilFilled tOilFilled = buildRefuelCar(vehicleBasic,staffBasic,itemModel);
            tOilFilledMapper.insertSelective(tOilFilled);

            //插入修改记录
            TOilFilledRecords tOilFilledRecords = buildRefuelCarRecord(itemModel);
            tOilFilledRecords.setOilFilledId(tOilFilled.getId());
            addRecordsList.add(tOilFilledRecords);

            responseModel.addSuccessful();
        }
        if (ListUtils.isNotEmpty(addRecordsList)) {
            tOilFilledRecordsMapper.batchInsert(addRecordsList);
        }
        return responseModel;
    }


    private TOilFilled buildRefuelCar(VehicleBasicPropertyModel vehicleBasic, TStaffBasic staffBasic, ImportOilFilledCarListRequestModel itemModel) {
        TOilFilled tOilFilled = new TOilFilled();
        tOilFilled.setCooperationCompany(itemModel.getCooperationCompany());
        tOilFilled.setStatus(OilFilledStatusEnum.WAIT_SETTLE.getKey());
        tOilFilled.setVehicleProperty(vehicleBasic.getVehicleProperty());
        tOilFilled.setVehicleId(vehicleBasic.getVehicleId());
        tOilFilled.setStaffId(staffBasic.getId());
        tOilFilled.setVehicleNo(itemModel.getVehicleNo());
        tOilFilled.setMobile(itemModel.getDriverPhone());
        tOilFilled.setName(itemModel.getDriverName());
        tOilFilled.setOilFilledDate(itemModel.getOilFilledDate());
        tOilFilled.setSource(OilFilledSourceEnum.OIL_FILLED.getKey());
        tOilFilled.setOilFilledType(OilFilledTypeEnum.OIL_FILLED_CAR.getKey());
        tOilFilled.setLiter(itemModel.getLiter());
        tOilFilled.setOilFilledFee(itemModel.getTotalAmount());
        tOilFilled.setRemark(itemModel.getRemark());
        commonBiz.setBaseEntityAdd(tOilFilled,BaseContextHandler.getUserName());
        return tOilFilled;
    }

    private TOilFilledRecords buildRefuelCarRecord(ImportOilFilledCarListRequestModel itemModel) {
        TOilFilledRecords tOilFilledRecords = new TOilFilledRecords();
        tOilFilledRecords.setVehicleNo(itemModel.getVehicleNo());
        tOilFilledRecords.setOilFilledDate(itemModel.getOilFilledDate());
        tOilFilledRecords.setOilFilledType(OilFilledTypeEnum.OIL_FILLED_CAR.getKey());
        tOilFilledRecords.setOilFilledFee((itemModel.getTotalAmount()));
        tOilFilledRecords.setLiter(itemModel.getLiter());
        tOilFilledRecords.setRemark(itemModel.getRemark());
        commonBiz.setBaseEntityAdd(tOilFilledRecords, BaseContextHandler.getUserName());
        return tOilFilledRecords;
    }

    private TOilFilled buildRefuelCard(VehicleBasicPropertyModel vehicleBasic, TStaffBasic staffBasic, ImportOilFilledCardListRequestModel itemModel) {
        TOilFilled tOilFilled = new TOilFilled();
        tOilFilled.setStatus(OilFilledStatusEnum.WAIT_SETTLE.getKey());
        tOilFilled.setVehicleId(vehicleBasic.getVehicleId());
        tOilFilled.setVehicleProperty(vehicleBasic.getVehicleProperty());
        tOilFilled.setVehicleNo(itemModel.getVehicleNo());
        tOilFilled.setStaffId(staffBasic.getId());
        tOilFilled.setName(itemModel.getDriverName());
        tOilFilled.setMobile(itemModel.getDriverPhone());
        tOilFilled.setSource(OilFilledSourceEnum.OIL_FILLED.getKey());
        tOilFilled.setOilFilledDate(itemModel.getOilFilledDate());
        tOilFilled.setOilFilledType(OilFilledTypeEnum.OIL_FILLED_CARD.getKey());
        tOilFilled.setOilFilledFee(itemModel.getApplyAmount());
        tOilFilled.setSubCardNumber(itemModel.getSubCardNumber());
        tOilFilled.setRemark(itemModel.getRemark());
        commonBiz.setBaseEntityAdd(tOilFilled, BaseContextHandler.getUserName());
        return tOilFilled;
    }

    private TOilFilledRecords buildRefuelCardRecord(ImportOilFilledCardListRequestModel itemModel) {
        TOilFilledRecords tOilFilledRecords = new TOilFilledRecords();
        tOilFilledRecords.setVehicleNo(itemModel.getVehicleNo());
        tOilFilledRecords.setOilFilledDate(itemModel.getOilFilledDate());
        tOilFilledRecords.setOilFilledType(OilFilledTypeEnum.OIL_FILLED_CARD.getKey());
        tOilFilledRecords.setOilFilledFee((itemModel.getApplyAmount()));
        tOilFilledRecords.setSubCardNumber(itemModel.getSubCardNumber());
        tOilFilledRecords.setRemark(itemModel.getRemark());
        commonBiz.setBaseEntityAdd(tOilFilledRecords, BaseContextHandler.getUserName());
        return tOilFilledRecords;
    }
}
