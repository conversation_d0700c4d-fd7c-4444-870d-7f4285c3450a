package com.logistics.management.webapi.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;

@Data
public class CarrierChangeSettleStatementStatsRequestDto {

	@ApiModelProperty(value = "对账单id", required = true)
	@NotBlank(message = "对账单id不能为空")
	private String settleStatementId;

	@ApiModelProperty(value = "1:业务人员操作,2:财务人员操作", required = true)
	@NotBlank(message = "操作标识不能为空")
	@Range(min = 1, max = 2, message = "操作标识错误")
	private String operationType;

	@ApiModelProperty(value = "通过：1 驳回：2 ", required = true)
	@NotBlank(message = "操作类型不能为空")
	@Range(min = 1, max = 2, message = "请选择通过或驳回")
	private String settleStatementStatus;

	@ApiModelProperty(value = "备注原因,1-50字符")
	@Length(min = 1, max = 50, message = "请填写备注,1-50字符")
	private String remark;
}
