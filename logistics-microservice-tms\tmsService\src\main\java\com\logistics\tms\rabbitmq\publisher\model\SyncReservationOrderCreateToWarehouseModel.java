package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/26 9:10
 */
@Data
public class SyncReservationOrderCreateToWarehouseModel {

    /**
     * 预约单code
     */
    private String reservationCode;

    /**
     * 仓库name
     */
    private String warehouseName;

    /**
     * 仓库省
     */
    private String warehouseProvince;

    /**
     * 仓库市
     */
    private String warehouseCity;

    /**
     * 仓库区
     */
    private String warehouseArea;

    /**
     * 仓库地址详情
     */
    private String warehouseDetail;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverMobilePhone;

    /**
     * 身份证号
     */
    private String identityCardNumber;

    /**
     * 车牌号
     */
    private String vehicleNumber;

    /**
     * 1 提货 2卸货
     */
    private Integer operationNode;

    /**
     * 预约开始时间
     */
    private Date reservationTimeStart;

    /**
     * 预约结束时间
     */
    private Date reservationTimeEnd;

    /**
     * 运单集合
     */
    private List<SyncReservationCarrierOrderCreateToWarehouseModel> itemModels;

    /**
     * 操作人
     */
    private String userName;

    /**
     * 预约角色0.司机 1.访客 2.车主
     */
    @ApiModelProperty("预约角色0.司机 1.访客 2.车主")
    private Integer reservationRole;

    /**
     * 预约来源 0.小程序 1.微信 2.车主
     */
    @ApiModelProperty("预约来源 0.小程序 1.微信 2.车主")
    private Integer reservationSource;


//    /**
//     * 预计入库数
//     */
//    @ApiModelProperty("预计入库数")
//    private BigDecimal expectedStockIn;




}
