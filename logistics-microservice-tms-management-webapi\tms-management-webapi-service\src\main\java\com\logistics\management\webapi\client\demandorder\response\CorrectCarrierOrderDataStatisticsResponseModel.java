package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/1/10 9:55
 */
@Data
public class CorrectCarrierOrderDataStatisticsResponseModel {
    @ApiModelProperty("纠错运单数量")
    private Integer waitCorrectCount;
    @ApiModelProperty("纠错运单货物数量")
    private BigDecimal waitCorrectAmount;
}
