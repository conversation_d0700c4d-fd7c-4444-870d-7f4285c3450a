package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.vehicleassetmanagement.response.VehicleRoadTransportCertificateListResponseModel;
import com.logistics.tms.entity.TVehicleRoadTransportCertificateAnnualReview;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface TVehicleRoadTransportCertificateAnnualReviewMapper extends BaseMapper<TVehicleRoadTransportCertificateAnnualReview>{
    int batchInsert(@Param("list") List<TVehicleRoadTransportCertificateAnnualReview> list);

    Map<String,String> getDueRoadTransportCertificateCount(@Param("companyCarrierId") Long companyCarrierId,@Param("remindDays") Integer remindDays);

    List<VehicleRoadTransportCertificateListResponseModel> getVehicleRoadTransportCertificateAnnualReviewByVehicleIds(@Param("vehicleIds") String vehicleIds);

    int batchUpdate(@Param("list") List<TVehicleRoadTransportCertificateAnnualReview> list);

    int countRoadTransportReviewByDate(@Param("roadTransportCertificateId") Long roadTransportCertificateId, @Param("checkValidDate") Date checkValidDate);
}