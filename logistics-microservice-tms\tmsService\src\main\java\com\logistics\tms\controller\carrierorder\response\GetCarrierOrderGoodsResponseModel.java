package com.logistics.tms.controller.carrierorder.response;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class GetCarrierOrderGoodsResponseModel {
    private Long goodsId;
    private BigDecimal expectAmount;
    private BigDecimal loadAmount;
    private BigDecimal unloadAmount;
    private BigDecimal signAmount;
    private Integer length;
    private Integer width;
    private Integer height;

}
