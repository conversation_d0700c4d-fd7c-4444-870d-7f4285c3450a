package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:lei.zhu
 * @date:2022/1/4 15:21:19
 */
@Data
public class SyncDemandOrderUnLoadAddressMessage {
    @ApiModelProperty("需求单号")
    private String  demandOrderCode;

    @ApiModelProperty("卸货仓库id")
    private Long warehouseId;

    @ApiModelProperty("操作人姓名")
    private String  operateUserName;
}
