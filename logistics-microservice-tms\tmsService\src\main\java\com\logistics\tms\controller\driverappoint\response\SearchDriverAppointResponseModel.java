package com.logistics.tms.controller.driverappoint.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 驾驶员预约列表
 *
 * <AUTHOR>
 * @date 2022/8/17 10:14
 */
@ApiModel("查询驾驶员预约列表信息")
@Data
public class SearchDriverAppointResponseModel {

    @ApiModelProperty("预约记录id")
    private Long driverAppointId;
    @ApiModelProperty("司机")
    private String driver;
    private String staffName;
    private String staffMobile;
    @ApiModelProperty("司机机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;
    private String staffPropertyLabel;
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("业务类型：1 公司，2 个人")
    private Integer businessType;
    @ApiModelProperty("乐橘新生客户名称（企业）")
    private String customerName;
    @ApiModelProperty("乐橘新生客户姓名（个人）")
    private String customerUserName;
    @ApiModelProperty("乐橘新生客户手机号（个人）")
    private String customerUserMobile;

    @ApiModelProperty("发货地址")
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String loadAddress;//拼接后的数据，列表展示
    @ApiModelProperty("发货人")
    private String consignorName;
    private String consignorMobile;
    @ApiModelProperty("下单总数量(KG)")
    private BigDecimal goodsAmountTotal;
    @ApiModelProperty("合计")
    private BigDecimal goodsPriceTotal;
    @ApiModelProperty("下单时间")
    private Date publishTime;
}
