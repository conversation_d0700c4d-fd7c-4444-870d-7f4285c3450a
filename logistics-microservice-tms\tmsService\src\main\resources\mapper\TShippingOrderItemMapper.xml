<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TShippingOrderItemMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TShippingOrderItem" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shipping_order_id" property="shippingOrderId" jdbcType="BIGINT" />
    <result column="carrier_order_id" property="carrierOrderId" jdbcType="BIGINT" />
    <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR" />
    <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT" />
    <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR" />
    <result column="expect_amount" property="expectAmount" jdbcType="DECIMAL" />
    <result column="order_num" property="orderNum" jdbcType="INTEGER" />
    <result column="next_point_distance" property="nextPointDistance" jdbcType="DECIMAL" />
    <result column="load_province_id" property="loadProvinceId" jdbcType="BIGINT" />
    <result column="load_province_name" property="loadProvinceName" jdbcType="VARCHAR" />
    <result column="load_city_id" property="loadCityId" jdbcType="BIGINT" />
    <result column="load_city_name" property="loadCityName" jdbcType="VARCHAR" />
    <result column="load_area_id" property="loadAreaId" jdbcType="BIGINT" />
    <result column="load_area_name" property="loadAreaName" jdbcType="VARCHAR" />
    <result column="load_detail_address" property="loadDetailAddress" jdbcType="VARCHAR" />
    <result column="load_warehouse" property="loadWarehouse" jdbcType="VARCHAR" />
    <result column="load_longitude" property="loadLongitude" jdbcType="VARCHAR" />
    <result column="load_latitude" property="loadLatitude" jdbcType="VARCHAR" />
    <result column="consignor_name" property="consignorName" jdbcType="VARCHAR" />
    <result column="consignor_mobile" property="consignorMobile" jdbcType="VARCHAR" />
    <result column="unload_province_id" property="unloadProvinceId" jdbcType="BIGINT" />
    <result column="unload_province_name" property="unloadProvinceName" jdbcType="VARCHAR" />
    <result column="unload_city_id" property="unloadCityId" jdbcType="BIGINT" />
    <result column="unload_city_name" property="unloadCityName" jdbcType="VARCHAR" />
    <result column="unload_area_id" property="unloadAreaId" jdbcType="BIGINT" />
    <result column="unload_area_name" property="unloadAreaName" jdbcType="VARCHAR" />
    <result column="unload_detail_address" property="unloadDetailAddress" jdbcType="VARCHAR" />
    <result column="unload_warehouse" property="unloadWarehouse" jdbcType="VARCHAR" />
    <result column="unload_longitude" property="unloadLongitude" jdbcType="VARCHAR" />
    <result column="unload_latitude" property="unloadLatitude" jdbcType="VARCHAR" />
    <result column="receiver_name" property="receiverName" jdbcType="VARCHAR" />
    <result column="receiver_mobile" property="receiverMobile" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, shipping_order_id, carrier_order_id, carrier_order_code, demand_order_id, demand_order_code, 
    expect_amount, order_num, next_point_distance, load_province_id, load_province_name, 
    load_city_id, load_city_name, load_area_id, load_area_name, load_detail_address, 
    load_warehouse, load_longitude, load_latitude, consignor_name, consignor_mobile, 
    unload_province_id, unload_province_name, unload_city_id, unload_city_name, unload_area_id, 
    unload_area_name, unload_detail_address, unload_warehouse, unload_longitude, unload_latitude, 
    receiver_name, receiver_mobile, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_shipping_order_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_shipping_order_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TShippingOrderItem" >
    insert into t_shipping_order_item (id, shipping_order_id, carrier_order_id, 
      carrier_order_code, demand_order_id, demand_order_code, 
      expect_amount, order_num, next_point_distance, 
      load_province_id, load_province_name, load_city_id, 
      load_city_name, load_area_id, load_area_name, 
      load_detail_address, load_warehouse, load_longitude, 
      load_latitude, consignor_name, consignor_mobile, 
      unload_province_id, unload_province_name, unload_city_id, 
      unload_city_name, unload_area_id, unload_area_name, 
      unload_detail_address, unload_warehouse, unload_longitude, 
      unload_latitude, receiver_name, receiver_mobile, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{shippingOrderId,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, 
      #{carrierOrderCode,jdbcType=VARCHAR}, #{demandOrderId,jdbcType=BIGINT}, #{demandOrderCode,jdbcType=VARCHAR}, 
      #{expectAmount,jdbcType=DECIMAL}, #{orderNum,jdbcType=INTEGER}, #{nextPointDistance,jdbcType=DECIMAL}, 
      #{loadProvinceId,jdbcType=BIGINT}, #{loadProvinceName,jdbcType=VARCHAR}, #{loadCityId,jdbcType=BIGINT}, 
      #{loadCityName,jdbcType=VARCHAR}, #{loadAreaId,jdbcType=BIGINT}, #{loadAreaName,jdbcType=VARCHAR}, 
      #{loadDetailAddress,jdbcType=VARCHAR}, #{loadWarehouse,jdbcType=VARCHAR}, #{loadLongitude,jdbcType=VARCHAR}, 
      #{loadLatitude,jdbcType=VARCHAR}, #{consignorName,jdbcType=VARCHAR}, #{consignorMobile,jdbcType=VARCHAR}, 
      #{unloadProvinceId,jdbcType=BIGINT}, #{unloadProvinceName,jdbcType=VARCHAR}, #{unloadCityId,jdbcType=BIGINT}, 
      #{unloadCityName,jdbcType=VARCHAR}, #{unloadAreaId,jdbcType=BIGINT}, #{unloadAreaName,jdbcType=VARCHAR}, 
      #{unloadDetailAddress,jdbcType=VARCHAR}, #{unloadWarehouse,jdbcType=VARCHAR}, #{unloadLongitude,jdbcType=VARCHAR}, 
      #{unloadLatitude,jdbcType=VARCHAR}, #{receiverName,jdbcType=VARCHAR}, #{receiverMobile,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TShippingOrderItem" >
    insert into t_shipping_order_item
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="shippingOrderId != null" >
        shipping_order_id,
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id,
      </if>
      <if test="carrierOrderCode != null" >
        carrier_order_code,
      </if>
      <if test="demandOrderId != null" >
        demand_order_id,
      </if>
      <if test="demandOrderCode != null" >
        demand_order_code,
      </if>
      <if test="expectAmount != null" >
        expect_amount,
      </if>
      <if test="orderNum != null" >
        order_num,
      </if>
      <if test="nextPointDistance != null" >
        next_point_distance,
      </if>
      <if test="loadProvinceId != null" >
        load_province_id,
      </if>
      <if test="loadProvinceName != null" >
        load_province_name,
      </if>
      <if test="loadCityId != null" >
        load_city_id,
      </if>
      <if test="loadCityName != null" >
        load_city_name,
      </if>
      <if test="loadAreaId != null" >
        load_area_id,
      </if>
      <if test="loadAreaName != null" >
        load_area_name,
      </if>
      <if test="loadDetailAddress != null" >
        load_detail_address,
      </if>
      <if test="loadWarehouse != null" >
        load_warehouse,
      </if>
      <if test="loadLongitude != null" >
        load_longitude,
      </if>
      <if test="loadLatitude != null" >
        load_latitude,
      </if>
      <if test="consignorName != null" >
        consignor_name,
      </if>
      <if test="consignorMobile != null" >
        consignor_mobile,
      </if>
      <if test="unloadProvinceId != null" >
        unload_province_id,
      </if>
      <if test="unloadProvinceName != null" >
        unload_province_name,
      </if>
      <if test="unloadCityId != null" >
        unload_city_id,
      </if>
      <if test="unloadCityName != null" >
        unload_city_name,
      </if>
      <if test="unloadAreaId != null" >
        unload_area_id,
      </if>
      <if test="unloadAreaName != null" >
        unload_area_name,
      </if>
      <if test="unloadDetailAddress != null" >
        unload_detail_address,
      </if>
      <if test="unloadWarehouse != null" >
        unload_warehouse,
      </if>
      <if test="unloadLongitude != null" >
        unload_longitude,
      </if>
      <if test="unloadLatitude != null" >
        unload_latitude,
      </if>
      <if test="receiverName != null" >
        receiver_name,
      </if>
      <if test="receiverMobile != null" >
        receiver_mobile,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shippingOrderId != null" >
        #{shippingOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null" >
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null" >
        #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="demandOrderId != null" >
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null" >
        #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="expectAmount != null" >
        #{expectAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderNum != null" >
        #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="nextPointDistance != null" >
        #{nextPointDistance,jdbcType=DECIMAL},
      </if>
      <if test="loadProvinceId != null" >
        #{loadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="loadProvinceName != null" >
        #{loadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="loadCityId != null" >
        #{loadCityId,jdbcType=BIGINT},
      </if>
      <if test="loadCityName != null" >
        #{loadCityName,jdbcType=VARCHAR},
      </if>
      <if test="loadAreaId != null" >
        #{loadAreaId,jdbcType=BIGINT},
      </if>
      <if test="loadAreaName != null" >
        #{loadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="loadDetailAddress != null" >
        #{loadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="loadWarehouse != null" >
        #{loadWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="loadLongitude != null" >
        #{loadLongitude,jdbcType=VARCHAR},
      </if>
      <if test="loadLatitude != null" >
        #{loadLatitude,jdbcType=VARCHAR},
      </if>
      <if test="consignorName != null" >
        #{consignorName,jdbcType=VARCHAR},
      </if>
      <if test="consignorMobile != null" >
        #{consignorMobile,jdbcType=VARCHAR},
      </if>
      <if test="unloadProvinceId != null" >
        #{unloadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="unloadProvinceName != null" >
        #{unloadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="unloadCityId != null" >
        #{unloadCityId,jdbcType=BIGINT},
      </if>
      <if test="unloadCityName != null" >
        #{unloadCityName,jdbcType=VARCHAR},
      </if>
      <if test="unloadAreaId != null" >
        #{unloadAreaId,jdbcType=BIGINT},
      </if>
      <if test="unloadAreaName != null" >
        #{unloadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="unloadDetailAddress != null" >
        #{unloadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="unloadWarehouse != null" >
        #{unloadWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="unloadLongitude != null" >
        #{unloadLongitude,jdbcType=VARCHAR},
      </if>
      <if test="unloadLatitude != null" >
        #{unloadLatitude,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null" >
        #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="receiverMobile != null" >
        #{receiverMobile,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TShippingOrderItem" >
    update t_shipping_order_item
    <set >
      <if test="shippingOrderId != null" >
        shipping_order_id = #{shippingOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null" >
        carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="demandOrderId != null" >
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null" >
        demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="expectAmount != null" >
        expect_amount = #{expectAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderNum != null" >
        order_num = #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="nextPointDistance != null" >
        next_point_distance = #{nextPointDistance,jdbcType=DECIMAL},
      </if>
      <if test="loadProvinceId != null" >
        load_province_id = #{loadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="loadProvinceName != null" >
        load_province_name = #{loadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="loadCityId != null" >
        load_city_id = #{loadCityId,jdbcType=BIGINT},
      </if>
      <if test="loadCityName != null" >
        load_city_name = #{loadCityName,jdbcType=VARCHAR},
      </if>
      <if test="loadAreaId != null" >
        load_area_id = #{loadAreaId,jdbcType=BIGINT},
      </if>
      <if test="loadAreaName != null" >
        load_area_name = #{loadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="loadDetailAddress != null" >
        load_detail_address = #{loadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="loadWarehouse != null" >
        load_warehouse = #{loadWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="loadLongitude != null" >
        load_longitude = #{loadLongitude,jdbcType=VARCHAR},
      </if>
      <if test="loadLatitude != null" >
        load_latitude = #{loadLatitude,jdbcType=VARCHAR},
      </if>
      <if test="consignorName != null" >
        consignor_name = #{consignorName,jdbcType=VARCHAR},
      </if>
      <if test="consignorMobile != null" >
        consignor_mobile = #{consignorMobile,jdbcType=VARCHAR},
      </if>
      <if test="unloadProvinceId != null" >
        unload_province_id = #{unloadProvinceId,jdbcType=BIGINT},
      </if>
      <if test="unloadProvinceName != null" >
        unload_province_name = #{unloadProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="unloadCityId != null" >
        unload_city_id = #{unloadCityId,jdbcType=BIGINT},
      </if>
      <if test="unloadCityName != null" >
        unload_city_name = #{unloadCityName,jdbcType=VARCHAR},
      </if>
      <if test="unloadAreaId != null" >
        unload_area_id = #{unloadAreaId,jdbcType=BIGINT},
      </if>
      <if test="unloadAreaName != null" >
        unload_area_name = #{unloadAreaName,jdbcType=VARCHAR},
      </if>
      <if test="unloadDetailAddress != null" >
        unload_detail_address = #{unloadDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="unloadWarehouse != null" >
        unload_warehouse = #{unloadWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="unloadLongitude != null" >
        unload_longitude = #{unloadLongitude,jdbcType=VARCHAR},
      </if>
      <if test="unloadLatitude != null" >
        unload_latitude = #{unloadLatitude,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null" >
        receiver_name = #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="receiverMobile != null" >
        receiver_mobile = #{receiverMobile,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TShippingOrderItem" >
    update t_shipping_order_item
    set shipping_order_id = #{shippingOrderId,jdbcType=BIGINT},
      carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      expect_amount = #{expectAmount,jdbcType=DECIMAL},
      order_num = #{orderNum,jdbcType=INTEGER},
      next_point_distance = #{nextPointDistance,jdbcType=DECIMAL},
      load_province_id = #{loadProvinceId,jdbcType=BIGINT},
      load_province_name = #{loadProvinceName,jdbcType=VARCHAR},
      load_city_id = #{loadCityId,jdbcType=BIGINT},
      load_city_name = #{loadCityName,jdbcType=VARCHAR},
      load_area_id = #{loadAreaId,jdbcType=BIGINT},
      load_area_name = #{loadAreaName,jdbcType=VARCHAR},
      load_detail_address = #{loadDetailAddress,jdbcType=VARCHAR},
      load_warehouse = #{loadWarehouse,jdbcType=VARCHAR},
      load_longitude = #{loadLongitude,jdbcType=VARCHAR},
      load_latitude = #{loadLatitude,jdbcType=VARCHAR},
      consignor_name = #{consignorName,jdbcType=VARCHAR},
      consignor_mobile = #{consignorMobile,jdbcType=VARCHAR},
      unload_province_id = #{unloadProvinceId,jdbcType=BIGINT},
      unload_province_name = #{unloadProvinceName,jdbcType=VARCHAR},
      unload_city_id = #{unloadCityId,jdbcType=BIGINT},
      unload_city_name = #{unloadCityName,jdbcType=VARCHAR},
      unload_area_id = #{unloadAreaId,jdbcType=BIGINT},
      unload_area_name = #{unloadAreaName,jdbcType=VARCHAR},
      unload_detail_address = #{unloadDetailAddress,jdbcType=VARCHAR},
      unload_warehouse = #{unloadWarehouse,jdbcType=VARCHAR},
      unload_longitude = #{unloadLongitude,jdbcType=VARCHAR},
      unload_latitude = #{unloadLatitude,jdbcType=VARCHAR},
      receiver_name = #{receiverName,jdbcType=VARCHAR},
      receiver_mobile = #{receiverMobile,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>