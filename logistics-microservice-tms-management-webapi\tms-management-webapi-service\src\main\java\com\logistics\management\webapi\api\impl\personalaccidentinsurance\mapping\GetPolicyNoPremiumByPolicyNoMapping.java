package com.logistics.management.webapi.api.impl.personalaccidentinsurance.mapping;

import com.logistics.management.webapi.api.feign.personalaccidentinsurance.dto.GetPolicyNoPremiumByPolicyNoResponseDto;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.GetPolicyNoPremiumByPolicyNoResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/6/5 11:47
 */
public class GetPolicyNoPremiumByPolicyNoMapping extends MapperMapping<GetPolicyNoPremiumByPolicyNoResponseModel,GetPolicyNoPremiumByPolicyNoResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    public GetPolicyNoPremiumByPolicyNoMapping(String imagePrefix ,Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        GetPolicyNoPremiumByPolicyNoResponseModel source = getSource();
        GetPolicyNoPremiumByPolicyNoResponseDto destination = getDestination();
        if (source != null){
            if (StringUtils.isNotBlank(source.getBatchNumber())){
                destination.setPolicyNumber(source.getBatchNumber());
            }
            destination.setSinglePremium(ConverterUtils.toString(source.getGrossPremium().divide(ConverterUtils.toBigDecimal(source.getPolicyPersonCount()),2, BigDecimal.ROUND_HALF_UP)));
            if (ListUtils.isNotEmpty(source.getTicketsList())){
                destination.getTicketsList().stream().forEach(tickets ->
                    tickets.setFilePathSrc(imagePrefix+imageMap.get(tickets.getFilePath()))
                );
            }
        }
    }
}
