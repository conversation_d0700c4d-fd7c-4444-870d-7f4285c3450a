package com.logistics.tms.controller.vehiclesafecheck.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/11/6 14:37
 */
@Data
public class SafeCheckItemResponseModel {
    @ApiModelProperty("检查项Id")
    private Long checkItemId;
    @ApiModelProperty("检查类型： 车辆安全性能：100转向系统，101制动系统，102灯光，103邮箱-轮胎; 安全器材：200防护用品使用情况，201安全带，202消防器材，203各部门紧固; 车辆其他性能：300传动-悬挂，301GPS设备使用情况 ")
    private Integer itemType;
    @ApiModelProperty("检查状态： 0待检查，1合格，2不合格")
    private Integer itemStatus;
}

