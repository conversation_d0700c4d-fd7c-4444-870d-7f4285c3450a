package com.logistics.appapi.client.thirdparty.basicdata.ocr;


import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.thirdparty.basicdata.ocr.hystrix.OCRServiceApiHystrix;
import com.logistics.appapi.client.thirdparty.basicdata.ocr.response.OcrMultipleInvoiceResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/5
 */
@FeignClient(name = FeignClientName.BASIC_DATA_SERVICES,
        path = "/service/ocr",
        fallback = OCRServiceApiHystrix.class)
public interface OCRServiceApi {


    /**
     * 发票数据ocr识别
     *
     * @param file 要识别的ocr数量
     * @return 费用申请列表
     */
    @PostMapping(value = "/multipleInvoice", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    Result<OcrMultipleInvoiceResponseModel> multipleInvoice(@RequestPart(value = "file") MultipartFile file, @RequestParam("type") String type);


}
