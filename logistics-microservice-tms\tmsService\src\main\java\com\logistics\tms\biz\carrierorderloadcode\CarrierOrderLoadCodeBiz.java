package com.logistics.tms.biz.carrierorderloadcode;


import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.carrierorderloadcode.model.CarrierOrderLoadCodeFileListSqlConditionModel;
import com.logistics.tms.biz.carrierorderloadcode.model.CarrierOrderLoadCodeListSqlConditionModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.client.WarehouseStockClient;
import com.logistics.tms.client.feign.warehouse.stock.reponse.GetCompanyNameByCodeRespModel;
import com.logistics.tms.controller.carrierorderapplet.request.CarrierOrderIdRequestModel;
import com.logistics.tms.controller.carrierorderapplet.response.GetCodeCompanyInfoRespModel;
import com.logistics.tms.controller.carrierorderloadcode.request.*;
import com.logistics.tms.controller.carrierorderloadcode.response.*;
import com.logistics.tms.entity.TCarrierOrder;
import com.logistics.tms.entity.TCarrierOrderLoadCode;
import com.logistics.tms.entity.TCarrierOrderLoadCodeFile;
import com.logistics.tms.mapper.TCarrierOrderLoadCodeFileMapper;
import com.logistics.tms.mapper.TCarrierOrderLoadCodeMapper;
import com.logistics.tms.mapper.TCarrierOrderMapper;
import com.yelo.life.basicdata.api.base.enums.ValidTypeEnum;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.MapUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
public class CarrierOrderLoadCodeBiz {

    @Resource
    CommonBiz commonBiz;

    @Resource
    TCarrierOrderMapper tCarrierOrderMapper;

    @Resource
    TCarrierOrderLoadCodeMapper tCarrierOrderLoadCodeMapper;

    @Resource
    TCarrierOrderLoadCodeFileMapper tCarrierOrderLoadCodeFileMapper;

    @Resource
    private SysConfigBiz sysConfigBiz;

    @Resource
    private WarehouseStockClient warehouseStockClient;

    @Resource
    private ConfigKeyConstant configKeyConstant;

    /**
     * 提货-正确临时编码列表
     */
    public PageInfo<CorrectLoadCodeListResponseModel> correctLoadCodeList(CorrectLoadCodeListRequestModel requestModel) {

        CarrierOrderLoadCodeListSqlConditionModel conditionModel = new CarrierOrderLoadCodeListSqlConditionModel();
        conditionModel.setState(CarrierOrderLoadCodeStateEnum.CORRECT_CODE.getKey());
        conditionModel.setCarrierOrderId(requestModel.getCarrierOrderId());
        //启用分页
        requestModel.enablePaging();
        List<TCarrierOrderLoadCode> tCarrierOrderLoadCodes = tCarrierOrderLoadCodeMapper.listByCondition(conditionModel);
        PageInfo pageInfo = new PageInfo<>(tCarrierOrderLoadCodes);

        //组装数据返回
        List<CorrectLoadCodeListResponseModel> responseModels = new ArrayList<>();
        for (TCarrierOrderLoadCode tCarrierOrderLoadCode : tCarrierOrderLoadCodes) {
            CorrectLoadCodeListResponseModel responseModel = new CorrectLoadCodeListResponseModel();
            responseModel.setCarrierOrderLoadCodeId(tCarrierOrderLoadCode.getId());
            responseModel.setProductCode(tCarrierOrderLoadCode.getProductCode());
            responseModel.setTrayCustomerCompanyName(tCarrierOrderLoadCode.getTrayCustomerCompanyName());
            responseModels.add(responseModel);
        }
        pageInfo.setList(responseModels);
        return pageInfo;
    }


    /**
     * 提货-有误临时编码列表
     */
    public PageInfo<ErrorLoadCodeListResponseModel> errorLoadCodeList(ErrorLoadCodeListRequestModel requestModel) {

        CarrierOrderLoadCodeListSqlConditionModel conditionModel = new CarrierOrderLoadCodeListSqlConditionModel();
        conditionModel.setState(CarrierOrderLoadCodeStateEnum.CAN_NOT_IDENTIFY_CODE.getKey());
        conditionModel.setCarrierOrderId(requestModel.getCarrierOrderId());
        //启用分页
        requestModel.enablePaging();
        List<TCarrierOrderLoadCode> tCarrierOrderLoadCodes = tCarrierOrderLoadCodeMapper.listByCondition(conditionModel);
        PageInfo pageInfo = new PageInfo<>(tCarrierOrderLoadCodes);
        List<Long> carrierOrderLoadCodeIds = tCarrierOrderLoadCodes.stream().map(TCarrierOrderLoadCode::getId).collect(Collectors.toList());

        //查询编码图片
        CarrierOrderLoadCodeFileListSqlConditionModel fileListSqlConditionModel = new CarrierOrderLoadCodeFileListSqlConditionModel();
        fileListSqlConditionModel.setCarrierOrderLoadCodeIds(carrierOrderLoadCodeIds);
        List<TCarrierOrderLoadCodeFile> tCarrierOrderLoadCodeFiles = tCarrierOrderLoadCodeFileMapper.listByCarrierOrderLoadCodeByCondition(fileListSqlConditionModel);
        Map<Long, List<TCarrierOrderLoadCodeFile>> tCarrierOrderLoadCodeFilesMap = tCarrierOrderLoadCodeFiles.stream().collect(Collectors.groupingBy(TCarrierOrderLoadCodeFile::getCarrierOrderLoadCodeId));
        List<String> filePaths = tCarrierOrderLoadCodeFiles.stream().map(TCarrierOrderLoadCodeFile::getFilePath).collect(Collectors.toList());

        Map<String, String> srcFilePathMap = commonBiz.batchGetOSSFileUrl(filePaths);


        //组装数据返回
        List<ErrorLoadCodeListResponseModel> responseModels = new ArrayList<>();
        for (TCarrierOrderLoadCode tCarrierOrderLoadCode : tCarrierOrderLoadCodes) {

            List<TCarrierOrderLoadCodeFile> carrierOrderLoadCodeFiles = tCarrierOrderLoadCodeFilesMap.getOrDefault(tCarrierOrderLoadCode.getId(), new ArrayList<>());

            List<String> fileSrcs = new ArrayList<>();
            for (TCarrierOrderLoadCodeFile carrierOrderLoadCodeFile : carrierOrderLoadCodeFiles) {
                String fileSrc = configKeyConstant.fileAccessAddress + srcFilePathMap.getOrDefault(carrierOrderLoadCodeFile.getFilePath(), "");
                fileSrcs.add(fileSrc);
            }

            ErrorLoadCodeListResponseModel responseModel = new ErrorLoadCodeListResponseModel();
            responseModel.setCarrierOrderLoadCodeId(tCarrierOrderLoadCode.getId());
            responseModel.setFileSrc(fileSrcs);
            responseModels.add(responseModel);
        }
        pageInfo.setList(responseModels);
        return pageInfo;

    }



    /**
     * 提货-保存正确的临时编码 (v2.46)
     */
    @Transactional
    public SaveCorrectLoadCodeResponseModel saveCorrectLoadCode(SaveCorrectLoadCodeRequestModel requestModel) {

        //校验托盘编码
        checkSaveCorrectLoadCode(requestModel);

        //查询云仓最后一次出库公司名称
        String trayCompanyNameByProductCode = "";
        GetCodeCompanyInfoRespModel respModel = new GetCodeCompanyInfoRespModel();
        respModel.setCode(requestModel.getProductCode());
        //查询云仓
        GetCompanyNameByCodeRespModel companyNameByCode = warehouseStockClient.getCompanyNameByCode(requestModel.getProductCode());
        if (companyNameByCode != null) {
            trayCompanyNameByProductCode =  companyNameByCode.getCompanyName();
        }

        //保存编码
        TCarrierOrderLoadCode addCarrierOrderLoadCode = new TCarrierOrderLoadCode();
        addCarrierOrderLoadCode.setCarrierOrderId(requestModel.getCarrierOrderId());
        addCarrierOrderLoadCode.setProductCode(requestModel.getProductCode());
        addCarrierOrderLoadCode.setTrayCustomerCompanyName(trayCompanyNameByProductCode);
        addCarrierOrderLoadCode.setState(CarrierOrderLoadCodeStateEnum.CORRECT_CODE.getKey());
        commonBiz.setBaseEntityAdd(addCarrierOrderLoadCode, BaseContextHandler.getUserName());
        tCarrierOrderLoadCodeMapper.insertSelective(addCarrierOrderLoadCode);

        //返回参数
        SaveCorrectLoadCodeResponseModel responseModel = new SaveCorrectLoadCodeResponseModel();
        responseModel.setCarrierOrderLoadCodeId(addCarrierOrderLoadCode.getId());
        responseModel.setTrayCustomerCompanyName(trayCompanyNameByProductCode);
        return responseModel;
    }

    /**
     * 请注意同一编码，不可同时出现在2个【待提货、待到达卸货地、待卸货、待纠错、待签收】的YR单【回收入库/回收出库】，提示“XXXXXXXX编码重复”；
     */
    private void checkSaveCorrectLoadCode(SaveCorrectLoadCodeRequestModel requestModel) {

        String productCode = requestModel.getProductCode();
        //规则校验
        Map<String, String> configMap = sysConfigBiz.getSysConfig(ConfigKeyEnum.ConfigGroupCodeEnum.CODE_RULE.getCode());
        if (MapUtils.isEmpty(configMap)) {
            throw new BizException(CarrierDataExceptionEnum.CODE_RULE_GROUP_NOT_EXIST);
        }

        String productCodeReg = configMap.get(ConfigKeyEnum.TRAY_CODE_RULE.getValue());
        if (StringUtils.isEmpty(productCodeReg)) {
            throw new BizException(CarrierDataExceptionEnum.CODE_RULE_GROUP_NOT_EXIST);
        }
        Pattern pattern = Pattern.compile(productCodeReg);

        String trimProductCode = productCode.trim();
        if (trimProductCode.length() > CommonConstant.SEVENTEEN) {
            trimProductCode = trimProductCode.substring(CommonConstant.EIGHTEEN).trim();
        }
        Matcher matcher = pattern.matcher(trimProductCode.replace(" ", ""));
        //如果是符合公司托盘编码，才进入上传逻辑
        if (!matcher.find()) {
            throw new BizException(CarrierDataExceptionEnum.TRAY_CODE_RULE_ERROR);
        }

        // 校验是否在其他待提货、待到达卸货地、待卸货、待纠错、待签收运单中存在
        Long exist = tCarrierOrderLoadCodeMapper.selectIfProductCodeExist(productCode);
        if (exist != null) {
            throw new BizException(CarrierDataExceptionEnum.PRODUCT_CODE_REPEAT.getKey(), String.format(CarrierDataExceptionEnum.PRODUCT_CODE_REPEAT.getValue(), productCode));
        }


        //如果是回收出库 还需要校验 扫码数量 不能大于预计提货数
        List<TCarrierOrder> tCarrierOrders = tCarrierOrderMapper.selectCarrierOrdersByIds(requestModel.getCarrierOrderId().toString());
        if (CollectionUtil.isEmpty(tCarrierOrders)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        TCarrierOrder tCarrierOrder = tCarrierOrders.get(0);
        if (EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
            CarrierOrderLoadCodeListSqlConditionModel conditionModel = new CarrierOrderLoadCodeListSqlConditionModel();
            conditionModel.setCarrierOrderId(tCarrierOrder.getId());
            List<TCarrierOrderLoadCode> tCarrierOrderLoadCodes = tCarrierOrderLoadCodeMapper.listByCondition(conditionModel);
            if (new BigDecimal(tCarrierOrderLoadCodes.size()).compareTo(tCarrierOrder.getExpectAmount()) >= 0) {
                throw new BizException(CarrierDataExceptionEnum.RECYCLE_OUT_LOAD_AMOUNT_MAX);
            }
        }

    }


    /**
     * 提货-保存错误的临时编码附件
     */
    @Transactional
    public SaveErrorLoadCodeFileResponseModel saveErrorLoadCodeFile(SaveErrorLoadCodeFileRequestModel requestModel) {

        //如果是回收出库 还需要校验 扫码数量 不能大于预计提货数
        List<TCarrierOrder> tCarrierOrders = tCarrierOrderMapper.selectCarrierOrdersByIds(requestModel.getCarrierOrderId().toString());
        if (CollectionUtil.isEmpty(tCarrierOrders)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        TCarrierOrder tCarrierOrder = tCarrierOrders.get(0);
        if (EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
            CarrierOrderLoadCodeListSqlConditionModel conditionModel = new CarrierOrderLoadCodeListSqlConditionModel();
            conditionModel.setCarrierOrderId(tCarrierOrder.getId());
            List<TCarrierOrderLoadCode> tCarrierOrderLoadCodes = tCarrierOrderLoadCodeMapper.listByCondition(conditionModel);
            if (new BigDecimal(tCarrierOrderLoadCodes.size()).compareTo(tCarrierOrder.getExpectAmount()) >= 0) {
                throw new BizException(CarrierDataExceptionEnum.RECYCLE_OUT_LOAD_AMOUNT_MAX);
            }
        }

        //保存编码
        TCarrierOrderLoadCode addCarrierOrderLoadCode = new TCarrierOrderLoadCode();
        addCarrierOrderLoadCode.setCarrierOrderId(requestModel.getCarrierOrderId());
        addCarrierOrderLoadCode.setState(CarrierOrderLoadCodeStateEnum.CAN_NOT_IDENTIFY_CODE.getKey());
        commonBiz.setBaseEntityAdd(addCarrierOrderLoadCode, BaseContextHandler.getUserName());
        tCarrierOrderLoadCodeMapper.insertSelective(addCarrierOrderLoadCode);

        //保存附件
        List<TCarrierOrderLoadCodeFile> addCarrierOrderLoadCodeFiles = new ArrayList<>();
        for (String filePath : requestModel.getFilePaths()) {
            TCarrierOrderLoadCodeFile addCarrierOrderLoadCodeFile = new TCarrierOrderLoadCodeFile();
            addCarrierOrderLoadCodeFile.setCarrierOrderLoadCodeId(addCarrierOrderLoadCode.getId());
            addCarrierOrderLoadCodeFile.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CARRIER_ORDER_CODE_FILE.getKey(), filePath));
            commonBiz.setBaseEntityAdd(addCarrierOrderLoadCodeFile, BaseContextHandler.getUserName());
            addCarrierOrderLoadCodeFiles.add(addCarrierOrderLoadCodeFile);
        }
        if (CollectionUtil.isNotEmpty(addCarrierOrderLoadCodeFiles)) {
            tCarrierOrderLoadCodeFileMapper.insertList(addCarrierOrderLoadCodeFiles);
        }

        //组装参数返回
        SaveErrorLoadCodeFileResponseModel responseModel = new SaveErrorLoadCodeFileResponseModel();
        responseModel.setCarrierOrderLoadCodeId(addCarrierOrderLoadCode.getId());
        return responseModel;
    }


    /**
     * 提货-删除正确的临时编码
     */
    @Transactional
    public void deleteCarrierOrderLoadCode(CarrierOrderLoadCodeIdRequestModel requestModel) {

        //运单提货编码
        TCarrierOrderLoadCode tCarrierOrderLoadCode = tCarrierOrderLoadCodeMapper.selectByPrimaryKey(requestModel.getCarrierOrderLoadCodeId());
        if (tCarrierOrderLoadCode == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_LOAD_CODE_NOT_EXIST);
        }

        //删除
        TCarrierOrderLoadCode updateCarrierOrderLoadCode = new TCarrierOrderLoadCode();
        updateCarrierOrderLoadCode.setId(requestModel.getCarrierOrderLoadCodeId());
        updateCarrierOrderLoadCode.setValid(ValidTypeEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(updateCarrierOrderLoadCode, BaseContextHandler.getUserName());
        tCarrierOrderLoadCodeMapper.batchUpdate(Collections.singletonList(updateCarrierOrderLoadCode));


    }


    /**
     * 提货详情(编码提货)
     */
    public CodePickUpDetailResponseModel codePickUpDetail(CarrierOrderIdRequestModel requestModel) {

        //查询运单
        List<TCarrierOrder> tCarrierOrders = tCarrierOrderMapper.selectCarrierOrdersByIds(Stream.of(requestModel.getCarrierOrderId().toString()).collect(Collectors.joining(",")));
        if (CollectionUtil.isEmpty(tCarrierOrders)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }


        //查询运单下扫到的编码
        CarrierOrderLoadCodeListSqlConditionModel conditionModel = new CarrierOrderLoadCodeListSqlConditionModel();
        conditionModel.setCarrierOrderId(requestModel.getCarrierOrderId());
        List<TCarrierOrderLoadCode> tCarrierOrderLoadCodes = tCarrierOrderLoadCodeMapper.listByCondition(conditionModel);

        long correctCodeCount = tCarrierOrderLoadCodes.stream().filter(tCarrierOrderLoadCode -> CarrierOrderLoadCodeStateEnum.CORRECT_CODE.getKey().equals(tCarrierOrderLoadCode.getState())).count();
        long canNotIdentifyCodeCount = tCarrierOrderLoadCodes.stream().filter(tCarrierOrderLoadCode -> CarrierOrderLoadCodeStateEnum.CAN_NOT_IDENTIFY_CODE.getKey().equals(tCarrierOrderLoadCode.getState())).count();

        //计算数量
        CodePickUpDetailResponseModel responseModel = new CodePickUpDetailResponseModel();
        responseModel.setCarrierOrderId(tCarrierOrders.get(0).getId());
        responseModel.setCodeErrorCount(new BigDecimal(canNotIdentifyCodeCount));
        responseModel.setLoadAmountActual(new BigDecimal(canNotIdentifyCodeCount + correctCodeCount));
        responseModel.setLoadAmountExpect( tCarrierOrders.get(0).getExpectAmount());
        responseModel.setScanAmountActual(new BigDecimal(correctCodeCount));
        return responseModel;

    }


    /**
     * 提货-有误临时编码查看
     */
    public ErrorLoadCodeGetDetailResponseModel errorLoadCodeGetDetail(ErrorLoadCodeGetDetailRequestModel requestModel) {

        //运单提货编码
        TCarrierOrderLoadCode tCarrierOrderLoadCode = tCarrierOrderLoadCodeMapper.selectByPrimaryKey(requestModel.getCarrierOrderLoadCodeId());
        if (tCarrierOrderLoadCode == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_LOAD_CODE_NOT_EXIST);
        }

        //运单提货编码文件
        CarrierOrderLoadCodeFileListSqlConditionModel conditionModel = new CarrierOrderLoadCodeFileListSqlConditionModel();
        conditionModel.setCarrierOrderLoadCodeIds(Arrays.asList(tCarrierOrderLoadCode.getId()));
        List<TCarrierOrderLoadCodeFile> tCarrierOrderLoadCodeFiles = tCarrierOrderLoadCodeFileMapper.listByCarrierOrderLoadCodeByCondition(conditionModel);
        List<String> filePaths = tCarrierOrderLoadCodeFiles.stream().map(TCarrierOrderLoadCodeFile::getFilePath).collect(Collectors.toList());

        //获取浏览路径
        Map<String, String> srcFilePathMap = commonBiz.batchGetOSSFileUrl(filePaths);


        //组装数据返回
        ErrorLoadCodeGetDetailResponseModel responseModel = new ErrorLoadCodeGetDetailResponseModel();
        responseModel.setCarrierOrderId(tCarrierOrderLoadCode.getCarrierOrderId());
        responseModel.setCarrierOrderLoadCodeId(tCarrierOrderLoadCode.getId());
        responseModel.setFilePaths(new ArrayList<>(srcFilePathMap.values()));
        return responseModel;

    }

}
