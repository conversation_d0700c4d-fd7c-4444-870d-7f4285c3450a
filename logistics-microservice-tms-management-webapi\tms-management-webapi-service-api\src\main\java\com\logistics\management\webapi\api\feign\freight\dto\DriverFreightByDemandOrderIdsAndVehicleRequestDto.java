package com.logistics.management.webapi.api.feign.freight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@Data
public class DriverFreightByDemandOrderIdsAndVehicleRequestDto {
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("装车数")
    private String loadCount;
    @ApiModelProperty("卸车数")
    private String unloadCount;
    @ApiModelProperty("费用类型 1 单价 2 一口价")
    private String freightType;
    @ApiModelProperty("调度列表")
    private List<DemandOrderIdAndExpectAmountRequestDto> demandOrderList;
}
