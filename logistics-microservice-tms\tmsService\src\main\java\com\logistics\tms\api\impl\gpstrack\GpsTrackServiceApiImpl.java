package com.logistics.tms.api.impl.gpstrack;

import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.gpstrack.GpsTrackServiceApi;
import com.logistics.tms.api.feign.gpstrack.model.*;
import com.logistics.tms.biz.gpstrack.GpsTrackBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class GpsTrackServiceApiImpl implements GpsTrackServiceApi {

    @Autowired
    private GpsTrackBiz gpsTrackBiz;

    /**
     * 获取车辆追踪信息列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<AllVehicleTrackInfoResponseModel> getVehicleTrackInfoList(@RequestBody AllVehicleTrackInfoRequestModel requestModel) {
        return Result.success(gpsTrackBiz.getVehicleTrackInfoList(requestModel));
    }

    /**
     * 根据车牌号获取调度单和运单地址等详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<SearchCarrierOrderDestinationByVehicleNoResponseModel> getDestinationByVehicleNo(@RequestBody SearchCarrierOrderDestinationByVehicleNoRequestModel requestModel) {
        return Result.success(gpsTrackBiz.getDestinationByVehicleNo(requestModel));
    }

    /**
     * 根据车牌号获取车辆历史轨迹
     * @param requestModel
     * @return
     */
    @Override
    public Result<OpGpHisTrackResponseModel> getVehicleTrackHistory(@RequestBody OpGpHisTrackRequestModel requestModel) {
        return Result.success(gpsTrackBiz.getVehicleTrackHistory(requestModel));
    }

    /**
     * 刷新本地车辆定位信息
     * @return
     */
    @Override
    public Result refreshLocation() {
        gpsTrackBiz.refreshVehicleLocation();
        return Result.success(null);
    }
}
