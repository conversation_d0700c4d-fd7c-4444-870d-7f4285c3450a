package com.logistics.appapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2018/10/16 13:52
 */
@Data
public class CarrierOrderLogisticsDetailResponseDto {

    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";
    @ApiModelProperty("车辆")
    private String vehicleNo = "";
    @ApiModelProperty("司机姓名")
    private String driverName = "";
    @ApiModelProperty("司机电话")
    private String driverPhone = "";
    private List<CarrierOrderLogisticsEventResponseDto> eventList;
    private List<CarrierOrderBillResponseDto> ticketList;
}
