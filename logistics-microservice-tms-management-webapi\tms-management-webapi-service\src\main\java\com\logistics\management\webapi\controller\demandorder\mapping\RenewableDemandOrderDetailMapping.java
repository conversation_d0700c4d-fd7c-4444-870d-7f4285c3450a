package com.logistics.management.webapi.controller.demandorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderCarrierResponseModel;
import com.logistics.management.webapi.client.demandorder.response.DemandOrderGoodsResponseModel;
import com.logistics.management.webapi.client.demandorder.response.RenewableDemandOrderDetailResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.RenewableDemandOrderCarrierResponseDto;
import com.logistics.management.webapi.controller.demandorder.response.RenewableDemandOrderDetailResponseDto;
import com.logistics.management.webapi.controller.demandorder.response.RenewableDemandOrderGoodsResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/29
 */
public class RenewableDemandOrderDetailMapping extends MapperMapping<RenewableDemandOrderDetailResponseModel, RenewableDemandOrderDetailResponseDto> {

	@Override
	public void configure() {
		RenewableDemandOrderDetailResponseModel source = getSource();
		RenewableDemandOrderDetailResponseDto destination = getDestination();

		if (source != null) {
			if (source.getContractPrice() != null && source.getContractPrice().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
				destination.setContractPrice(ConverterUtils.toString(source.getContractPrice()));
			} else {
				destination.setContractPrice("");
			}
			//状态转换
			if (CommonConstant.INTEGER_ONE.equals(source.getIfCancel())) {
				destination.setStatus(DemandOrderStatusEnum.CANCEL_DISPATCH.getKey().toString());
				destination.setStatusLabel(DemandOrderStatusEnum.CANCEL_DISPATCH.getValue());
			} else {
				destination.setStatus(source.getEntrustStatus().toString());
				destination.setStatusLabel(DemandOrderStatusEnum.getEnum(source.getEntrustStatus()).getValue());
			}
			//单位转换
			destination.setGoodsUnit(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());
			//业务类型
			destination.setBusinessType(CompanyTypeEnum.getEnum(source.getBusinessType()).getValue());
			//下单人
			if (CustomerOrderSourceEnum.YELO_LIFE.getKey().equals(source.getCustomerOrderSource())) {
				destination.setPublishName(source.getCustomerName() + " " + source.getPublishName() + " " + source.getPublishMobile());
			} else if (CustomerOrderSourceEnum.DRIVER.getKey().equals(source.getCustomerOrderSource())) {
				destination.setPublishName(source.getCompanyCarrierName() + " " + source.getPublishName());
			}

			//转换客户
			if(LifeBusinessTypeEnum.COMPANY.getKey().equals(source.getBusinessType())){
				destination.setCustomerName(source.getCustomerName());
			}else if (LifeBusinessTypeEnum.PERSONAGE.getKey().equals(source.getBusinessType())) {
				destination.setCustomerName(source.getCustomerUserName() + " " + source.getCustomerUserMobile());
			}

			//个人车主展示姓名+手机号
			if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())){
				destination.setCompanyCarrierName(source.getCarrierContactName()+" "+source.getCarrierContactPhone());
			}

			//货物信息
			List<DemandOrderGoodsResponseModel> goodsResponseModel = source.getGoodsResponseModel();
			if (ListUtils.isNotEmpty(goodsResponseModel)) {
				List<RenewableDemandOrderGoodsResponseDto> list = new ArrayList<>();
				for (DemandOrderGoodsResponseModel model : goodsResponseModel) {
					RenewableDemandOrderGoodsResponseDto responseDto = new RenewableDemandOrderGoodsResponseDto();
					responseDto.setGoodsName(model.getGoodsName());
					responseDto.setDemandOrderGoodsId(ConverterUtils.toString(model.getDemandOrderGoodsId()));
					responseDto.setGoodsSize(model.getGoodsSize());
					//委托
					responseDto.setGoodsAmountNumber(model.getGoodsAmountNumber().stripTrailingZeros().toPlainString());
					//已安排
					responseDto.setArrangedAmountNumber(model.getArrangedAmountNumber().stripTrailingZeros().toPlainString());
					//未安排
					responseDto.setNotArrangedAmountNumber(model.getNotArrangedAmountNumber().stripTrailingZeros().toPlainString());
					//已退回
					responseDto.setBackAmountNumber(model.getBackAmountNumber().stripTrailingZeros().toPlainString());
					list.add(responseDto);
				}
				destination.setGoodsResponseModel(list);
			}

			//发货地址信息
			destination.setConsignorName(source.getConsignorName() + " " + source.getConsignorMobile());
			StringBuilder load = new StringBuilder();
			if (StringUtils.isNotBlank(source.getLoadWarehouse())) {
				load.append("【").append(source.getLoadWarehouse()).append("】");
			}
			load.append(Optional.ofNullable(source.getLoadProvinceName()).orElse("")).
					append(Optional.ofNullable(source.getLoadCityName()).orElse("")).
					append(Optional.ofNullable(source.getLoadAreaName()).orElse("")).
					append(Optional.ofNullable(source.getLoadDetailAddress()).orElse(""));
			destination.setLoadDetailAddress(load.toString());

			//收货地址信息
			destination.setReceiverName(source.getReceiverName() + " " + source.getReceiverMobile());
			StringBuilder unload = new StringBuilder();
			if (StringUtils.isNotBlank(source.getUnloadWarehouse())) {
				unload.append("【").append(source.getUnloadWarehouse()).append("】");
			}
			unload.append(Optional.ofNullable(source.getUnloadProvinceName()).orElse("")).
					append(Optional.ofNullable(source.getUnloadCityName()).orElse("")).
					append(Optional.ofNullable(source.getUnloadAreaName()).orElse("")).
					append(Optional.ofNullable(source.getUnloadDetailAddress()).orElse(""));
			destination.setUnloadDetailAddress(unload.toString());

			String priceUnit = GoodsUnitEnum.getEnum(source.getGoodsUnit()).getPriceUnit();
			//货主费用（暂时为空）
			destination.setExpectContractPriceType("");
			destination.setExpectContractPrice("");
			destination.setContractPriceTotal("");

			//预计货主费用
			if(PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getExpectContractPriceType())){
				//单价：单价*委托数量；
				destination.setExpectContractPriceType(ConverterUtils.toString(source.getExpectContractPriceType()));
				destination.setExpectContractPrice(source.getExpectContractPrice()+priceUnit);
				destination.setContractPriceTotal(ConverterUtils.toString(source.getGoodsAmount().multiply(source.getExpectContractPrice()).setScale(2,BigDecimal.ROUND_HALF_UP))+CommonConstant.YUAN);
			}else if(PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getExpectContractPriceType())){
				destination.setExpectContractPriceType(ConverterUtils.toString(source.getExpectContractPriceType()));
				destination.setExpectContractPrice(source.getExpectContractPrice()+CommonConstant.YUAN);
				destination.setContractPriceTotal(ConverterUtils.toString(source.getExpectContractPrice().setScale(2,BigDecimal.ROUND_HALF_UP))+CommonConstant.YUAN);
			}

			//实际货主费用,已签收展示结算费用
			if (DemandOrderStatusEnum.COMPLETE_SIGN.getKey().equals(source.getEntrustStatus())) {
				destination.setContractPriceType(ConverterUtils.toString(source.getSettlementPriceType()));

				//一口价单价处理
				if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getSettlementPriceType())) {
					if (source.getSettlementAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
						destination.setContractPrice(source.getSettlementCostTotal().divide(source.getSettlementAmount(), 2, BigDecimal.ROUND_HALF_UP) + priceUnit);
					}
					destination.setActualContractPriceTotal(source.getSettlementCostTotal()+CommonConstant.YUAN);
				}else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getSettlementPriceType())){
					destination.setContractPrice(ConverterUtils.toString(source.getSettlementCostTotal())+CommonConstant.YUAN);
					destination.setActualContractPriceTotal(ConverterUtils.toString(source.getSettlementCostTotal())+CommonConstant.YUAN);
				}
			}else{
				destination.setContractPriceType("");
				destination.setContractPrice("");
				destination.setActualContractPriceTotal("");
			}

			List<RenewableDemandOrderCarrierResponseDto> carrierOrders = new ArrayList<>();
			//运单信息转换
			if (ListUtils.isNotEmpty(source.getCarrierResponseModel())) {
				for (DemandOrderCarrierResponseModel model : source.getCarrierResponseModel()) {
					RenewableDemandOrderCarrierResponseDto carrierOrder = new RenewableDemandOrderCarrierResponseDto();
					carrierOrder.setCarrierOrderCode(model.getCarrierOrderCode());
					carrierOrder.setCarrierOrderId(ConverterUtils.toString(model.getCarrierOrderId()));
					if (model.getDispatchTime() != null) {
						carrierOrder.setDispatchTime(DateUtils.dateToString(model.getDispatchTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
					}
					//状态转换
					if (CommonConstant.INTEGER_ONE.equals(model.getIfCancel())) {
						carrierOrder.setStatus(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getKey().toString());
						carrierOrder.setStatusLabel(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getValue());
					} else if (CommonConstant.INTEGER_ONE.equals(model.getIfEmpty())) {
						carrierOrder.setStatus(CarrierOrderStatusEnum.EMPTY.getKey().toString());
						carrierOrder.setStatusLabel(CarrierOrderStatusEnum.EMPTY.getValue());
					} else {
						carrierOrder.setStatus(model.getStatus().toString());
						carrierOrder.setStatusLabel(CarrierOrderStatusEnum.getEnum(model.getStatus()).getValue());
					}
					carrierOrder.setVehicleNumber(model.getVehicleNumber());
					carrierOrder.setDriverName(model.getDriverName() == null ? "" : model.getDriverName() + " " + model.getDriverMobile());
					carrierOrder.setDispatchUserName(model.getDispatchUserName());
					carrierOrder.setExpectAmount(model.getExpectAmount().stripTrailingZeros().toPlainString());
					//运输价格处理
					if (model.getDispatchFreightFee() != null) {
						if (PriceTypeEnum.UNIT_PRICE.getKey().equals(model.getDispatchFreightFeeType())) {
							BigDecimal dispatchFee = model.getDispatchFreightFee().multiply(model.getExpectAmount()).setScale(2, RoundingMode.HALF_UP);
							carrierOrder.setCarrierPrice(ConverterUtils.toString(dispatchFee));
						} else {
							carrierOrder.setCarrierPrice(ConverterUtils.toString(model.getDispatchFreightFee()));
						}
					}
					carrierOrders.add(carrierOrder);
				}
			}
			destination.setCarrierResponseModel(carrierOrders);
		}
	}
}
