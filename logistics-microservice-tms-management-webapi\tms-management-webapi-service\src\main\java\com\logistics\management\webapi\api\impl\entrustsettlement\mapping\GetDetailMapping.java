package com.logistics.management.webapi.api.impl.entrustsettlement.mapping;

import com.logistics.management.webapi.api.feign.entrustsettlement.dto.GetDetailResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.entrustsettlement.model.GetDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * <AUTHOR>
 * @date 2020-08-20 14:08
 */
public class GetDetailMapping extends MapperMapping<GetDetailResponseModel, GetDetailResponseDto> {
    @Override
    public void configure() {
        GetDetailResponseModel source = this.getSource();
        GetDetailResponseDto dto = this.getDestination();
        if(source != null){
            if(source.getContractPriceType() == null || CommonConstant.INTEGER_ZERO.equals(source.getContractPriceType())){
                dto.setContractPriceType(CommonConstant.BLANK_TEXT);
                dto.setSettlementCostTotal(CommonConstant.BLANK_TEXT);
            }
        }
    }
}
