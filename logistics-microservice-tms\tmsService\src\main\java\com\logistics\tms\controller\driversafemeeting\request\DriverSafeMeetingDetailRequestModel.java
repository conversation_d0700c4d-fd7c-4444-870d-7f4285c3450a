package com.logistics.tms.controller.driversafemeeting.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/11/4 9:43
 */
@Data
public class DriverSafeMeetingDetailRequestModel extends AbstractPageForm<DriverSafeMeetingDetailRequestModel> {
    @ApiModelProperty(value = "学习状态：空 全部，0未学习，1已学习")
    private Integer status;
    @ApiModelProperty(value = "学习月份")
    private String periodStart;
    private String periodEnd;
    @ApiModelProperty("驾驶员姓名")
    private String staffName;
    @ApiModelProperty("驾驶员手机号")
    private String staffMobile;
    @ApiModelProperty("安全例会关系ids（多选导出使用）")
    private String safeMeetingRelationIds;
    @ApiModelProperty(value = "安全例会id")
    @NotBlank(message = "安全例会id不能为空")
    private Long driverSafeMeetingId;
    @ApiModelProperty("人员机构 1 自主 2外部 3 自营")
    private Integer staffProperty;
}
