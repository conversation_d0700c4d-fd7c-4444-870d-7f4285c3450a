package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TCarrierOrderOperateLogs extends BaseEntity {
    /**
    * 运单ID
    */
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    /**
    * 操作类型 各种操作类型枚举
    */
    @ApiModelProperty("操作类型 各种操作类型枚举")
    private Integer operationType;

    /**
    * 操作内容
    */
    @ApiModelProperty("操作内容")
    private String operationContent;

    /**
    * 操作备注
    */
    @ApiModelProperty("操作备注")
    private String remark;

    /**
    * 操作人名称
    */
    @ApiModelProperty("操作人名称")
    private String operatorName;

    /**
    * 操作时间
    */
    @ApiModelProperty("操作时间")
    private Date operateTime;
}