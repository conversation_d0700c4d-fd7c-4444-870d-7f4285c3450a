package com.logistics.tms.rabbitmq.consumer.tms;

import com.logistics.tms.biz.demandorder.DemandOrderForLeYiBiz;
import com.logistics.tms.mapper.message.DealReplenishOrderMessage;
import com.rabbitmq.client.Channel;
import com.yelo.tools.rabbitmq.annocation.EnableMqErrorMessageCollect;
import com.yelo.tools.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * @Author: 刘世威
 * @CreateTime: 2025-07-24
 * @Description:
 * @Version: 1.0
 */
@Slf4j
@Component
public class DealReplenishOrderConsumer {

    private final ObjectMapper objectMapper = JacksonUtils.getInstance();

    @Resource
    private DemandOrderForLeYiBiz demandOrderForLeYiBiz;

    @EnableMqErrorMessageCollect
    @RabbitListener(bindings = {@QueueBinding(
            exchange = @Exchange(name = "logistics.topic", type = "topic"),
            value = @Queue(value = "logistics.dealReplenishOrder", durable = "true"),
            key = "dealReplenishOrder")}, concurrency = "3")
    public void dealReplenishOrder(@Payload String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {
        DealReplenishOrderMessage dealReplenishOrderMessage = objectMapper.readValue(message, DealReplenishOrderMessage.class);
        log.info("接收延时队列处理后补需求单消息" + dealReplenishOrderMessage.toString());
        demandOrderForLeYiBiz.dealReplenishOrder(dealReplenishOrderMessage);
        channel.basicAck(deliveryTag, false);
    }
}
