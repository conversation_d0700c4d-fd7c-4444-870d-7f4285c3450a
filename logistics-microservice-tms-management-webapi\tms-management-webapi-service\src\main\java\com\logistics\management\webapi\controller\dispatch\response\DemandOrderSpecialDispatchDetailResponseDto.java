package com.logistics.management.webapi.controller.dispatch.response;

import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/6 10:13
 */
@Data
public class DemandOrderSpecialDispatchDetailResponseDto {

    /**
     * 需求单货物
     */
    private List<DemandOrderSpecialDispatchDetailListResponseDto> goodsList;

    /**
     * 车主id
     */
    private String companyCarrierId="";
    /**
     * 车主
     */
    private String companyCarrierName = "";
    /**
     * 是否我司 1:我司,2:其他车主
     */
    private String isOurCompany = "";

    /**
     * 货物单位 1 件 2 吨
     */
    private String goodsUnit="";

    /**
     * 装卸数-装
     */
    private String loadPointAmount="";

    /**
     * 装卸数-卸
     */
    private String unloadPointAmount="";

    /**
     * 串点距离
     */
    private String crossPointDistance="";

}
