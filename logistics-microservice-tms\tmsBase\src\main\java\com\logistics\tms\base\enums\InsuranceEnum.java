package com.logistics.tms.base.enums;


public enum InsuranceEnum {
    NULL(-99,""),
    COMMERCIAL(1,"商业险"),
    COMPULSORY(2,"交强险"),
    PERSONAL(3,"个人意外险"),
    CARGO(4,"货物险"),
    CARRIER(5,"承运人险"),

    ;

    private Integer key;
    private String value;

    InsuranceEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static InsuranceEnum getEnum(Integer key) {
        for (InsuranceEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return NULL;
    }
}
