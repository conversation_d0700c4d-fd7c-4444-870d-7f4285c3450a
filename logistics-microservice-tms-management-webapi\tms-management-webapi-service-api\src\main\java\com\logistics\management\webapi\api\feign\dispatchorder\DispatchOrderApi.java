package com.logistics.management.webapi.api.feign.dispatchorder;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.dispatchorder.dto.*;
import com.logistics.management.webapi.api.feign.dispatchorder.hystrix.DispatchOrderApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Api(value = "API-DispatchOrderApiImpl-调度单")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = DispatchOrderApiHystrix.class)
public interface DispatchOrderApi {

    @ApiOperation(value = "获取调度单列表")
    @PostMapping(value = "/api/dispatchOrder/searchList")
    Result<PageInfo<DispatchOrderSearchResponseDto>> searchList(@RequestBody DispatchOrderSearchRequestDto requestDto);

    @ApiOperation(value = "获取调度单子列表")
    @PostMapping(value = "/api/dispatchOrder/getCarrierOrder")
    Result<List<DispatchOrderCarrierChildResponseDto>> getCarrierOrder(@RequestBody @Valid DispatchOrderCarrierRequestDto requestDto);

    @ApiOperation(value = "调度单详情")
    @PostMapping(value = "/api/dispatchOrder/getDetail")
    Result<DispatchOrderDetailResponseDto> getDetail(@RequestBody @Valid DispatchOrderCarrierRequestDto requestDto);


}
