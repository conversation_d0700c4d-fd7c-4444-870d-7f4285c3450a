package com.logistics.tms.controller.carrierorderapplet.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2018/10/17 16:22
 */
@Data
public class CarrierOrderLoadRequestModel {

    @ApiModelProperty("运单Id")
    private Long carrierOrderId;

    @ApiModelProperty("提货数量")
    private List<CarrierOrderGoodsLoadUnloadRequestModel> goodsList;

    @ApiModelProperty("图片路径")
    private List<CarrierOrderTicketRequestModel> tickets;

    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;

    @ApiModelProperty(value = "现场其他托盘信息")
    private SiteOtherPalletsRequestModel siteOtherPallets;

    @ApiModelProperty(value = "提货方式 1 票据提货，2 二维码提货")
    public Integer deliveryMethod;
}
