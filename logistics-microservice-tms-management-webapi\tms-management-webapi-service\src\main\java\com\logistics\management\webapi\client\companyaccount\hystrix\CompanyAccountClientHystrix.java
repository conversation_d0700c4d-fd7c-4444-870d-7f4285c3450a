package com.logistics.management.webapi.client.companyaccount.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.companyaccount.CompanyAccountClient;
import com.logistics.management.webapi.client.companyaccount.request.CompanyAccountAddRequestModel;
import com.logistics.management.webapi.client.companyaccount.request.CompanyAccountEnabledRequestModel;
import com.logistics.management.webapi.client.companyaccount.request.CompanyAccountImageRequestModel;
import com.logistics.management.webapi.client.companyaccount.request.SearchCompanyAccountRequestModel;
import com.logistics.management.webapi.client.companyaccount.response.CompanyAccountImageResponseModel;
import com.logistics.management.webapi.client.companyaccount.response.SearchCompanyAccountResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/6
 */
@Component
public class CompanyAccountClientHystrix implements CompanyAccountClient {
	@Override
	public Result<CompanyAccountImageResponseModel> getAccountImageList(CompanyAccountImageRequestModel requestModel) {
		return Result.timeout();
	}

	@Override
	public Result<Boolean> enabled(CompanyAccountEnabledRequestModel requestModel) {
		return Result.timeout();
	}

	@Override
	public Result<Boolean> addAccount(CompanyAccountAddRequestModel requestModel) {
		return Result.timeout();
	}

	@Override
	public Result<PageInfo<SearchCompanyAccountResponseModel>> searchList(SearchCompanyAccountRequestModel requestModel) {
		return Result.timeout();
	}
}
