package com.logistics.tms.controller.driverappoint;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.driverappoint.DriverAppointBiz;
import com.logistics.tms.controller.driverappoint.request.DriverAppointAssociatedVehicleRequestModel;
import com.logistics.tms.controller.driverappoint.request.SearchAppointRequestModel;
import com.logistics.tms.controller.driverappoint.request.SearchDrierAppointDetailRequestModel;
import com.logistics.tms.controller.driverappoint.request.SearchDriverAppointRequestModel;
import com.logistics.tms.controller.driverappoint.response.SearchAppointCountResponseModel;
import com.logistics.tms.controller.driverappoint.response.SearchDrierAppointDetailResponseModel;
import com.logistics.tms.controller.driverappoint.response.SearchDriverAppointResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: wjf
 * @date: 2024/3/8 9:24
 */
@Api(value = "驾驶员预约记录管理")
@RestController
public class DriverAppointController {

    @Resource
    private DriverAppointBiz driverAppointBiz;

    /**
     * 查询驾驶员预约列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询驾驶员预约列表")
    @PostMapping(value = "/service/driverAppointManagement/searchDriverAppointList")
    public Result<PageInfo<SearchDriverAppointResponseModel>> searchDriverAppointList(@RequestBody SearchDriverAppointRequestModel requestModel) {
        return Result.success(driverAppointBiz.searchDriverAppointList(requestModel));
    }

    /**
     * 小程序-预约记录-详情
     * @param requestModel
     * @return
     */
    @ApiOperation("小程序预约记录-详情")
    @PostMapping(value = "/service/driverApplet/driverAppoint/searchDrierAppointDetail")
    public Result<SearchDrierAppointDetailResponseModel> searchDrierAppointDetail(@RequestBody SearchDrierAppointDetailRequestModel requestModel) {
        return Result.success(driverAppointBiz.searchDrierAppointDetail(requestModel));
    }

    /**
     * 小程序-预约记录关联车辆（生成运单）
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "小程序-预约记录关联车辆（生成运单）")
    @PostMapping(value = "/service/driverApplet/driverAppoint/associatedVehicle")
    public Result<Boolean> associatedVehicle(@RequestBody DriverAppointAssociatedVehicleRequestModel requestModel) {
        driverAppointBiz.associatedVehicle(requestModel);
        return Result.success(true);
    }

    /**
     * 小程序-预约记录-列表
     * @param requestModel
     * @return
     */
    @ApiOperation("小程序预约记录-列表")
    @PostMapping(value = "/service/driverApplet/driverAppoint/searchAppointList")
    public Result<SearchAppointCountResponseModel> searchAppointList(@RequestBody SearchAppointRequestModel requestModel) {
        return Result.success(driverAppointBiz.searchAppointList(requestModel));
    }
}
