package com.logistics.management.webapi.controller.workgroup.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2023/12/21 14:29
 */
@Data
public class SearchWorkGroupListResponseDto {

    @ApiModelProperty("智能推送配置表id")
    private String workGroupId="";

    @ApiModelProperty("群名")
    private String groupName="";

    @ApiModelProperty("负责人")
    private String groupOwnerUser="";

    @ApiModelProperty("群简介")
    private String groupDesc="";

    @ApiModelProperty("状态：0禁用，1启用")
    private String enabled="";
    @ApiModelProperty("状态")
    private String enabledLabel="";

    @ApiModelProperty("创建人")
    private String createdBy="";

    @ApiModelProperty("创建时间")
    private String createdTime="";

}
