package com.logistics.tms.base.utils;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2018/12/29 10:29
 */
public class StripTrailingZerosUtils {

    private StripTrailingZerosUtils(){}

    public static String stripTrailingZerosToString(BigDecimal amount){
        return amount.stripTrailingZeros().toPlainString();
    }

    public static int stripTrailingZerosToInt(BigDecimal amount){
        return amount.stripTrailingZeros().intValue();
    }
}
