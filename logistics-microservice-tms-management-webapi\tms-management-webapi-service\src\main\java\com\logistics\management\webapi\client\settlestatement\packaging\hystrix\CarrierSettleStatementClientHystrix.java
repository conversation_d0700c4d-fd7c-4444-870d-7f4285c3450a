package com.logistics.management.webapi.client.settlestatement.packaging.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.settlestatement.packaging.CarrierSettleStatementClient;
import com.logistics.management.webapi.client.settlestatement.packaging.request.*;
import com.logistics.management.webapi.client.settlestatement.packaging.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/20 9:46
 */
@Component
public class CarrierSettleStatementClientHystrix implements CarrierSettleStatementClient {
    @Override
    public Result<PageInfo<CarrierWaitSettleStatementListResponseModel>> waitSettleStatementList(CarrierWaitSettleStatementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<CarrierWaitSettleStatementListResponseModel>> exportWaitSettleStatementList(CarrierWaitSettleStatementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> createSettleStatement(CarrierCreateSettleStatementRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierTaxPointResponseModel> queryTaxPoint(CarrierTaxPointRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<CarrierSettleStatementListResponseModel>> settleStatementList(CarrierSettleStatementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierAssociationCarrierOrderResponseModel> associationCarrierOrder(CarrierAssociationCarrierOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifySettleStatementMonth(ModifySettleStatementMonthRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifyPlatformCompany(ModifyPlatformCompanyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifyTaxPoint(CarrierModifyTaxPointRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> renameStatement(CarrierEditSettleStatementNameRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierAdjustCostResponseModel> queryAdjustCost(CarrierQueryAdjustCostRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> adjustCost(CarrierAdjustRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> applyInvoicing(SettleStatementApplyInvoicingRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancel(CarrierCancelRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<StatementArchiveListResponseModel>> statementArchiveList(StatementArchiveListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> statementArchive(StatementArchiveRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<String>> archiveTicketList(StatementArchiveTicketListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<StatementArchiveDetailResponseModel> statementArchiveDetail(StatementArchiveDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<StatementWaitArchiveListResponseModel>> statementWaitArchiveList(StatementWaitArchiveListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> submitSettleStatement(CarrierSettleStatementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> auditOrReject(CarrierChangeSettleStatementStatsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierSettleStatementDetailTotalResponseModel> settleStatementDetailTotal(CarrierSettleStatementIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<CarrierSettleStatementDetailListResponseModel>> settleStatementDetailList(CarrierSettleStatementDetailListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<CarrierSettleStatementDetailListResponseModel>> exportSettleStatementDetailList(CarrierSettleStatementDetailListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<CarrierWaitSettleStatementListResponseModel>> addCarrierOrderList(CarrierAddCarrierOrderListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addCarrierOrderConfirm(CarrierAddCarrierOrderConfirmRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancelCarrierOrder(CarrierUndoCarrierOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> rejectCompleteSettle(CarrierSettleStatementIdRequestModel requestModel) {
        return Result.timeout();
    }
}
