<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRouteEnquiryCompanyMapper" >
  <sql id="Base_Column_List_Decrypt" >
    id, route_enquiry_id, company_carrier_type, company_carrier_id, company_carrier_name, 
    carrier_contact_id, carrier_contact_name,
    AES_DECRYPT(UNHEX(carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
    quote_status, quote_operator,
    AES_DECRYPT(UNHEX(quote_operator_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as quote_operator_phone,
    quote_time, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>

  <select id="selectByPrimaryKeyDecrypt" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List_Decrypt" />
    from t_route_enquiry_company
    where id = #{id,jdbcType=BIGINT}
    and valid = 1
  </select>

  <insert id="batchInsertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TRouteEnquiryCompany" >
    <foreach collection="list" item="item" separator=";">
      insert into t_route_enquiry_company
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.routeEnquiryId != null" >
          route_enquiry_id,
        </if>
        <if test="item.companyCarrierType != null" >
          company_carrier_type,
        </if>
        <if test="item.companyCarrierId != null" >
          company_carrier_id,
        </if>
        <if test="item.companyCarrierName != null" >
          company_carrier_name,
        </if>
        <if test="item.carrierContactId != null" >
          carrier_contact_id,
        </if>
        <if test="item.carrierContactName != null" >
          carrier_contact_name,
        </if>
        <if test="item.carrierContactPhone != null" >
          carrier_contact_phone,
        </if>
        <if test="item.quoteStatus != null" >
          quote_status,
        </if>
        <if test="item.quoteOperator != null" >
          quote_operator,
        </if>
        <if test="item.quoteOperatorPhone != null" >
          quote_operator_phone,
        </if>
        <if test="item.quoteTime != null" >
          quote_time,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.routeEnquiryId != null" >
          #{item.routeEnquiryId,jdbcType=BIGINT},
        </if>
        <if test="item.companyCarrierType != null" >
          #{item.companyCarrierType,jdbcType=INTEGER},
        </if>
        <if test="item.companyCarrierId != null" >
          #{item.companyCarrierId,jdbcType=BIGINT},
        </if>
        <if test="item.companyCarrierName != null" >
          #{item.companyCarrierName,jdbcType=VARCHAR},
        </if>
        <if test="item.carrierContactId != null" >
          #{item.carrierContactId,jdbcType=BIGINT},
        </if>
        <if test="item.carrierContactName != null" >
          #{item.carrierContactName,jdbcType=VARCHAR},
        </if>
        <if test="item.carrierContactPhone != null" >
          HEX(AES_ENCRYPT(#{item.carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
        </if>
        <if test="item.quoteStatus != null" >
          #{item.quoteStatus,jdbcType=INTEGER},
        </if>
        <if test="item.quoteOperator != null" >
          #{item.quoteOperator,jdbcType=VARCHAR},
        </if>
        <if test="item.quoteOperatorPhone != null" >
          HEX(AES_ENCRYPT(#{item.quoteOperatorPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
        </if>
        <if test="item.quoteTime != null" >
          #{item.quoteTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TRouteEnquiryCompany" >
    update t_route_enquiry_company
    <set >
      <if test="routeEnquiryId != null" >
        route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierType != null" >
        company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null" >
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null" >
        company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null" >
        carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null" >
        carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null" >
        carrier_contact_phone = HEX(AES_ENCRYPT(#{carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="quoteStatus != null" >
        quote_status = #{quoteStatus,jdbcType=INTEGER},
      </if>
      <if test="quoteOperator != null" >
        quote_operator = #{quoteOperator,jdbcType=VARCHAR},
      </if>
      <if test="quoteOperatorPhone != null" >
        quote_operator_phone = HEX(AES_ENCRYPT(#{quoteOperatorPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
      </if>
      <if test="quoteTime != null" >
        quote_time = #{quoteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getQuotedCompanyCount" resultType="com.logistics.tms.biz.routeenquiry.model.RouteEnquiryQuotedCompanyCountModel">
    select
    route_enquiry_id as routeEnquiryId,
    sum(1) as quotedCarrierCount
    from t_route_enquiry_company
    where route_enquiry_id in
    <foreach collection="routeEnquiryIdList" item="routeEnquiryId" open="(" close=")" separator=",">
      #{routeEnquiryId,jdbcType=BIGINT}
    </foreach>
    and valid = 1
    and quote_status in (0, 1, 2)
    group by route_enquiry_id
  </select>

  <select id="getQuotedCompanyByRouteEnquiryId" resultType="com.logistics.tms.controller.routeenquiry.response.RouteEnquiryQuoteListResponseModel">
    select
    id as routeEnquiryCompanyId,
    company_carrier_type as companyCarrierType,
    company_carrier_name as companyCarrierName,
    carrier_contact_name as carrierContactName,
    AES_DECRYPT(UNHEX(carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactPhone,
    quote_time as quoteTime,
    quote_status as quoteStatus
    from t_route_enquiry_company
    where route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT}
    and valid = 1
    and quote_status in (0, 1, 2)
    order by quote_time desc, id desc
  </select>

  <select id="getByRouteEnquiryIdAndCompanyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List_Decrypt"/>
    from t_route_enquiry_company
    where route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT}
    and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
    and valid = 1
  </select>
</mapper>