package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/24 13:56
 */
@Data
public class LoadDetailResponseDto {
    @ApiModelProperty("运单ID")
    private String carrierOrderId = "";
    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";
    @ApiModelProperty("货主")
    private String companyEntrustName = "";
    @ApiModelProperty("车辆")
    private String vehicleNo = "";
    @ApiModelProperty("司机")
    private String driver = "";
    @ApiModelProperty("单位")
    private String goodsUnit = "";
    @ApiModelProperty("单位文本")
    private String goodsUnitLabel = "";
    @ApiModelProperty("发货地址")
    private String loadAddress = "";
    @ApiModelProperty("收货地址")
    private String unloadAddress = "";
    @ApiModelProperty("调度时间/运单生成时间")
    private String dispatchTime="";
    @ApiModelProperty("是否云盘数据: 1 是 0 否")
    private String ifTray = "";
    private List<CarrierOrderGoodsResponseDto> goodsList = new ArrayList<>();
}
