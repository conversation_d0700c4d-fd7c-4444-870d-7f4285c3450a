package com.logistics.tms.biz.sysmailingaddress;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoDetailRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoListRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoModifyRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoDetailResponseModel;
import com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoListResponseModel;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.ConfigKeyEnum;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.entity.TSysConfig;
import com.yelo.tray.core.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SysMailingAddressBiz {

    private final SysConfigBiz sysConfigBiz;

    /**
     * 查询系统邮寄信息配置列表
     * @param requestModel 请求Model
     * @return PageInfo<MailingInfoListResponseModel>
     */
    public PageInfo<MailingInfoListResponseModel> mailingInfoList(MailingInfoListRequestModel requestModel) {
        requestModel.enablePaging();
        List<TSysConfig> configDetail = sysConfigBiz.getSysConfigDetail(ConfigKeyEnum.ConfigGroupCodeEnum.RETURN_ADDRESS.getCode());
        List<MailingInfoListResponseModel> mailingInfoList = configDetail.stream()
                .map(s -> {
                    MailingInfoListResponseModel model = JSONObject.parseObject(s.getConfigValue(), MailingInfoListResponseModel.class);
                    model.setMailingInfoId(s.getConfigKey());
                    model.setLastModifiedBy(s.getLastModifiedBy());
                    model.setLastModifiedTime(s.getLastModifiedTime());
                    return model;
                })
                .collect(Collectors.toList());
        return new PageInfo<>(mailingInfoList);
    }

    /**
     * 查询邮寄信息配置详情
     * @param requestModel 请求Model
     * @return MailingInfoDetailResponseModel
     */
    public MailingInfoDetailResponseModel mailingInfoDetail(MailingInfoDetailRequestModel requestModel) {
        MailingInfoDetailResponseModel model = sysConfigBiz.getSysConfig(ConfigKeyEnum.ConfigGroupCodeEnum.RETURN_ADDRESS.getCode(),
                requestModel.getMailingInfoId(), MailingInfoDetailResponseModel.class);
        if (Objects.isNull(model)) {
            throw new BizException(CarrierDataExceptionEnum.SYS_MAILING_ADDRESS_NOT_EXIST);
        }
        model.setMailingInfoId(requestModel.getMailingInfoId());
        return model;
    }

    /**
     * 邮寄信息地址修改
     * @param requestModel 请求Model
     * @return Boolean
     */
    @Transactional
    public Boolean mailingInfoModify(MailingInfoModifyRequestModel requestModel) {
        String key = requestModel.getMailingInfoId();
        requestModel.setMailingInfoId(null);
        String configValue = JSONObject.toJSONString(requestModel, SerializerFeature.PrettyFormat);
        sysConfigBiz.editSysConfigValue(ConfigKeyEnum.ConfigGroupCodeEnum.RETURN_ADDRESS.getCode(), key, configValue);
        return true;
    }

}
