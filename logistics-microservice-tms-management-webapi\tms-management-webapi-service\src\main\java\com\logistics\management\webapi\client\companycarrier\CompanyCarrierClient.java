package com.logistics.management.webapi.client.companycarrier;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.companycarrier.hystrix.CompanyCarrierClientHystrix;
import com.logistics.management.webapi.client.companycarrier.request.*;
import com.logistics.management.webapi.client.companycarrier.response.CompanyCarrierDetailResponseModel;
import com.logistics.management.webapi.client.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.management.webapi.client.companycarrier.response.SearchCompanyCarrierListResponseModel;
import com.logistics.management.webapi.client.companycarrier.response.UserCompanyCarrierInfoResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/9/27 17:22
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,path ="/service/companyCarrier" ,fallback = CompanyCarrierClientHystrix.class)
public interface CompanyCarrierClient {

    /**
     * 查询车主公司列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询车主公司列表")
    @PostMapping(value = "/searchList")
    Result<PageInfo<SearchCompanyCarrierListResponseModel>> searchList(@RequestBody SearchCompanyCarrierListRequestModel requestModel);

    /**
     * 公司详情
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "公司详情")
    @PostMapping(value = "/detail")
    Result<CompanyCarrierDetailResponseModel> getDetail(@RequestBody CompanyCarrierDetailRequestModel requestModel);

    /**
     * 修改/保存
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "新增/编辑-个人/企业车主")
    @PostMapping(value = "/saveOrUpdate")
    Result<Boolean> saveOrUpdate(@RequestBody SaveOrModifyCompanyCarrierRequestModel requestModel);

    /**
     * 模糊查询
     *
     * @param requestModel
     * @return
     */
    @ApiOperation("模糊查询")
    @PostMapping(value = "/fuzzyQuery")
    Result<List<FuzzySearchCompanyCarrierResponseModel>> fuzzyQuery(@RequestBody FuzzySearchCompanyCarrierRequestModel requestModel);

    /**
     * 根据登录人查询公司信息，界面上部bar
     *
     * @return
     */
    @ApiOperation(value = "根据登录人查询公司信息，界面上部bar")
    @PostMapping(value = "/getUserAndCompanyInfo")
    Result<UserCompanyCarrierInfoResponseModel> getUserAndCompanyInfo();

    /**
     * 开启/关闭黑名单
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "开启/关闭黑名单")
    @PostMapping(value = "/openOrClose")
    Result<Boolean> openOrClose(@RequestBody OpenCloseBlacklistRequestModel requestModel);

    /**
     * 开启/关闭零担模式
     *
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/openOrCloseLessThanTruckload")
    Result<Boolean> openOrCloseLessThanTruckload(@RequestBody OpenCloseLessThanTruckloadRequestModel requestModel);

}
