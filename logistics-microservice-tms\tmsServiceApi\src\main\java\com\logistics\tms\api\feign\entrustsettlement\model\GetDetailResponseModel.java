package com.logistics.tms.api.feign.entrustsettlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/10/11 18:58
 */
@Data
public class GetDetailResponseModel {
    @ApiModelProperty("结算ID")
    private Long settlementId;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty("报价类型")
    private Integer contractPriceType;
    @ApiModelProperty("结算费用")
    private BigDecimal settlementCostTotal;
}
