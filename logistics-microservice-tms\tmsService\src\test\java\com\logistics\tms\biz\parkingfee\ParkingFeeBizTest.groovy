package com.logistics.tms.biz.parkingfee

import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeDeductingHistoryRequestModel
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeDeductingHistoryResponseModel
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeDetailRequestModel
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeDetailResponseModel
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeListRequestModel
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeListResponseModel
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeOperationRecordRequestModel
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeOperationRecordResponsesModel
import com.logistics.tms.api.feign.parkingfee.dto.SaveOrUpdateParkingFeeRequestModel
import com.logistics.tms.api.feign.parkingfee.dto.SummaryParkingFeeResponseModel
import com.logistics.tms.api.feign.parkingfee.dto.TerminateParkingFeeRequestModel
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TDeductingHistory
import com.logistics.tms.entity.TParkingFee
import com.logistics.tms.entity.TParkingFeeRecords
import com.logistics.tms.entity.TVehicleSettlementRelation
import com.logistics.tms.mapper.TDeductingHistoryMapper
import com.logistics.tms.mapper.TParkingFeeMapper
import com.logistics.tms.mapper.TParkingFeeRecordsMapper
import com.logistics.tms.mapper.TStaffBasicMapper
import com.logistics.tms.mapper.TVehicleBasicMapper
import com.logistics.tms.mapper.TVehicleSettlementRelationMapper
import org.slf4j.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class ParkingFeeBizTest extends Specification {
    @Mock
    CommonBiz commonBiz
    @Mock
    TParkingFeeMapper tParkingFeeMapper
    @Mock
    TParkingFeeRecordsMapper tParkingFeeRecordsMapper
    @Mock
    TVehicleBasicMapper tVehicleBasicMapper
    @Mock
    TStaffBasicMapper tStaffBasicMapper
    @Mock
    TDeductingHistoryMapper deductingHistoryMapper
    @Mock
    TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper
    @Mock
    Logger log
    @InjectMocks
    ParkingFeeBiz parkingFeeBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tParkingFeeMapper.getParkingFeeList(any())).thenReturn([new ParkingFeeListResponseModel()])

        expect:
        parkingFeeBiz.searchList(requestModel) == expectedResult

        where:
        requestModel                     || expectedResult
        new ParkingFeeListRequestModel() || null
    }

    @Unroll
    def "get Summary where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tParkingFeeMapper.getSummary(any())).thenReturn(new SummaryParkingFeeResponseModel())

        expect:
        parkingFeeBiz.getSummary(requestModel) == expectedResult

        where:
        requestModel                     || expectedResult
        new ParkingFeeListRequestModel() || new SummaryParkingFeeResponseModel()
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tParkingFeeMapper.getDetailById(anyLong())).thenReturn(new ParkingFeeDetailResponseModel())
        when(deductingHistoryMapper.getByObjectTypeId(anyInt(), anyLong())).thenReturn([new TDeductingHistory(deductingMonth: "deductingMonth", totalFee: 0 as BigDecimal, deductingFee: 0 as BigDecimal, remainingDeductingFee: 0 as BigDecimal)])
        when(tVehicleSettlementRelationMapper.getByObjectIdAndType(anyInt(), anyLong())).thenReturn([new TVehicleSettlementRelation()])

        expect:
        parkingFeeBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                       || expectedResult
        new ParkingFeeDetailRequestModel() || new ParkingFeeDetailResponseModel()
    }

    @Unroll
    def "save Or Update where requestModel=#requestModel"() {
        given:
        when(tParkingFeeMapper.getByVehicleStartDate(anyLong(), any(), any())).thenReturn([new TParkingFee(status: 0, vehicleId: 1l, vehicleNo: "vehicleNo", staffId: 1l, name: "name", mobile: "mobile", cooperationCompany: "cooperationCompany", startDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 36).getTime(), endDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 36).getTime(), finishDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 36).getTime(), parkingFee: 0 as BigDecimal, cooperationPeriod: 0, cooperationStatus: 0, remark: "remark")])
        when(tVehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())
        when(deductingHistoryMapper.getByObjectTypeId(anyInt(), anyLong())).thenReturn([new TDeductingHistory()])
        when(tVehicleSettlementRelationMapper.getByObjectIdAndType(anyInt(), anyLong())).thenReturn([new TVehicleSettlementRelation()])

        expect:
        parkingFeeBiz.saveOrUpdate(requestModel)
        assert expectedResult == false

        where:
        requestModel                             || expectedResult
        new SaveOrUpdateParkingFeeRequestModel() || true
    }

    @Unroll
    def "get Cooperation Status where endDate=#endDate and startDate=#startDate then expect: #expectedResult"() {
        expect:
        parkingFeeBiz.getCooperationStatus(startDate, endDate) == expectedResult

        where:
        endDate                                                          | startDate                                                        || expectedResult
        new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 36).getTime() | new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 36).getTime() || 0
    }

    @Unroll
    def "add Parking Fee Operator Record where tParkingFee=#tParkingFee"() {
        expect:
        parkingFeeBiz.addParkingFeeOperatorRecord(tParkingFee)
        assert expectedResult == false

        where:
        tParkingFee                                                                                                                                                                                                                                                                                                                                                          || expectedResult
        new TParkingFee(cooperationCompany: "cooperationCompany", startDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 36).getTime(), endDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 36).getTime(), finishDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 36).getTime(), parkingFee: 0 as BigDecimal, cooperationPeriod: 0, remark: "remark") || true
    }

    @Unroll
    def "terminate Parking Fee where requestModel=#requestModel"() {
        expect:
        parkingFeeBiz.terminateParkingFee(requestModel)
        assert expectedResult == false

        where:
        requestModel                          || expectedResult
        new TerminateParkingFeeRequestModel() || true
    }

    @Unroll
    def "get Deducting History List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(deductingHistoryMapper.getByObjectTypeId(anyInt(), anyLong())).thenReturn([new TDeductingHistory(deductingMonth: "deductingMonth", totalFee: 0 as BigDecimal, deductingFee: 0 as BigDecimal, remainingDeductingFee: 0 as BigDecimal)])

        expect:
        parkingFeeBiz.getDeductingHistoryList(requestModel) == expectedResult

        where:
        requestModel                                 || expectedResult
        new ParkingFeeDeductingHistoryRequestModel() || [new ParkingFeeDeductingHistoryResponseModel()]
    }

    @Unroll
    def "get Operation Record List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tParkingFeeRecordsMapper.getByParkingFeeId(anyLong())).thenReturn([new TParkingFeeRecords(parkingFeeId: 1l, cooperationCompany: "cooperationCompany", startDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 36).getTime(), endDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 36).getTime(), finishDate: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 36).getTime(), parkingFee: 0 as BigDecimal, cooperationPeriod: 0, remark: "remark")])

        expect:
        parkingFeeBiz.getOperationRecordList(requestModel) == expectedResult

        where:
        requestModel                                || expectedResult
        new ParkingFeeOperationRecordRequestModel() || [new ParkingFeeOperationRecordResponsesModel()]
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme