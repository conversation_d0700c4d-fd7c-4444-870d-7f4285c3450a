package com.logistics.appapi.client.thirdparty.basicdata.datamap.hystrix;

import com.logistics.appapi.client.thirdparty.basicdata.datamap.DataMapServiceApi;
import com.logistics.appapi.client.thirdparty.basicdata.datamap.request.GetMapListRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.datamap.response.GetMapListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/7 13:17
 */
@Component
public class DataMapServiceApiHystrix implements DataMapServiceApi {
    @Override
    public Result<List<GetMapListResponseModel>> getMapList(GetMapListRequestModel requestModel) {
        return Result.timeout();
    }
}
