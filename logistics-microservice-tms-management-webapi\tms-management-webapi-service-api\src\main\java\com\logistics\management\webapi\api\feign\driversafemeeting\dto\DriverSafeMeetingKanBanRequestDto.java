package com.logistics.management.webapi.api.feign.driversafemeeting.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2019/11/1 19:55
 */
@Data
public class DriverSafeMeetingKanBanRequestDto {
    @ApiModelProperty(value = "选择年起",required = true)
    @NotBlank(message = "请选择年份")
    private String meetingYearStart;

    @ApiModelProperty(value = "选择年止",required = true)
    @NotBlank(message = "请选择年份")
    private String meetingYearEnd;


    @ApiModelProperty(value = "安全例会类型：1 安全例会，2 紧急培训",required = true)
    private String type;

}
