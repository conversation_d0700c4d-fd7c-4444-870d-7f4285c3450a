package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DemandOrderGoodsForPublishResponseDto {
    @ApiModelProperty("货物信息id")
    private String demandOrderGoodsId="";
    @ApiModelProperty("品名")
    private String goodsName="";
    @ApiModelProperty("规格")
    private String goodsSize="";
    @ApiModelProperty("委托件数")
    private String goodsAmountNumber="";
    @ApiModelProperty("委托体积")
    private String goodsAmountVolume="";
    @ApiModelProperty("委托预计重量")
    private String goodsAmountExpectWeight="";

}
