package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/5
 */
@Data
public class CarrierOrderSignUpForLeyiRequestModel {

	@ApiModelProperty(value = "运单ID")
	private Long carrierOrderId;

	@ApiModelProperty(value = "实际货主费用(元)")
	private BigDecimal actualEntrustFee;

	@ApiModelProperty(value = "实际车主运费(元)")
	private BigDecimal signCarrierFreight;
}
