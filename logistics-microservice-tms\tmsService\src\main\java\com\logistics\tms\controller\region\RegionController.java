package com.logistics.tms.controller.region;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.region.RegionBiz;
import com.logistics.tms.controller.region.request.*;
import com.logistics.tms.controller.region.response.*;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/5 9:30
 */
@Api(value = "物流区域配置")
@RestController
@RequestMapping("/service/region")
public class RegionController {

    @Resource
    private RegionBiz regionBiz;

    /**
     * 物流大区列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "物流大区列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchRegionResponseModel>> searchList(@RequestBody SearchRegionRequestModel requestModel) {
        return Result.success(regionBiz.searchList(requestModel));
    }

    /**
     * 获取物流大区下车主列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取物流大区下车主列表")
    @PostMapping(value = "/getRegionCompany")
    public Result<List<RegionCompanyResponseModel>> getRegionCompany(@RequestBody RegionDetailRequestModel requestModel){
        return Result.success(regionBiz.getRegionCompany(requestModel));
    }

    /**
     * 物流大区新增修改
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "物流大区新增修改")
    @PostMapping(value = "/saveOrModifyRegion")
    public Result<Boolean> saveOrModifyRegion(@RequestBody SaveOrModifyRegionRequestModel requestModel) {
        regionBiz.saveOrModifyRegion(requestModel);
        return Result.success(true);
    }

    /**
     * 编辑详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "编辑详情")
    @PostMapping(value = "/getDetail")
    public Result<RegionDetailResponseModel> getDetail(@RequestBody RegionDetailRequestModel requestModel) {
        return Result.success(regionBiz.getDetail(requestModel));
    }

    /**
     * 启用/禁用大区信息
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "启用/禁用大区信息")
    @PostMapping(value = "/enableRegion")
    public Result<Boolean> enableRegion(@RequestBody EnableRegionRequestModel requestModel) {
        regionBiz.enableOrDisable(requestModel);
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/export")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchRegionResponseModel>> export(@RequestBody SearchRegionRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return Result.success(regionBiz.searchList(requestModel).getList());
    }

    /**
     * 移除
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "移除")
    @PostMapping(value = "/removeRegion")
    public Result<Boolean> removeRegion(@RequestBody RemoveRegionRequestModel requestModel) {
        regionBiz.removeRegion(requestModel);
        return Result.success(true);
    }

    /**
     * 大区详情列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "大区详情列表")
    @PostMapping(value = "/searchDetailList")
    public Result<PageInfo<SearchRegionDetailResponseModel>> searchDetailList(@RequestBody SearchRegionDetailRequestModel requestModel) {
        return Result.success(regionBiz.searchDetailList(requestModel));
    }

    /**
     * 大区详情列表导出
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "大区详情列表导出")
    @PostMapping(value = "/exportRegionDetail")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<SearchRegionDetailResponseModel>> exportRegionDetail(@RequestBody SearchRegionDetailRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return Result.success(regionBiz.searchDetailList(requestModel).getList());
    }

    /**
     * 根据省市查询区域下的车主（排除加入黑名单的车主）
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "根据省市查询区域下的车主（排除加入黑名单的车主）")
    @PostMapping(value = "/getCompanyByRegion")
    public Result<List<GetCompanyCarrierByRegionResponseModel>> getCompanyByRegion(@RequestBody GetCompanyCarrierByRegionRequestModel requestModel){
        return Result.success(regionBiz.getCompanyByRegion(requestModel));
    }
}
