package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TDriverSafePromiseRelation extends BaseEntity {
    /**
    * 安全承诺书ID
    */
    @ApiModelProperty("安全承诺书ID")
    private Long safePromiseId;

    /**
    * 人员ID
    */
    @ApiModelProperty("人员ID")
    private Long staffId;

    /**
    * 人员机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    /**
    * 人员姓名
    */
    @ApiModelProperty("人员姓名")
    private String staffName;

    /**
    * 司机电话
    */
    @ApiModelProperty("司机电话")
    private String staffMobile;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 签订状态0待签订、1已签订
    */
    @ApiModelProperty("签订状态0待签订、1已签订")
    private Integer status;

    /**
    * 签订时间
    */
    @ApiModelProperty("签订时间")
    private Date signTime;

    /**
    * 手持承诺书图片地址
    */
    @ApiModelProperty("手持承诺书图片地址")
    private String handPromiseUrl;

    /**
    * 签字责任书图片地址
    */
    @ApiModelProperty("签字责任书图片地址")
    private String signResponsibilityUrl;
}