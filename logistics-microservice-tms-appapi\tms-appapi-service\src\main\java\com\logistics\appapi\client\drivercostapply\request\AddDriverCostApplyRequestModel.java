package com.logistics.appapi.client.drivercostapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/7
 */
@Data
public class AddDriverCostApplyRequestModel {

	@ApiModelProperty("司机费用申请表id（重新提交时必填）")
	private Long driverCostApplyId;

	@ApiModelProperty(value = "车辆id")
	private Long vehicleId;

	@ApiModelProperty(value = "费用类型：100 住宿费，101 装卸费，102 其他费用，103 加班费，104 劳保费，105 电话费，106 交通费， 107 打印费，108 叉车费，109 盖雨布， 110破包赔偿，111 交通罚款；200 维修费，201 车辆保养费，202 过路过桥费，203 停车费，204 尿素费，205 加油费；300 核酸检测费，301 医疗防护用品；400 扣款")
	private Integer costType;

	@ApiModelProperty(value = "申请费用")
	private BigDecimal applyCost;

	@ApiModelProperty(value = "发生时间")
	private Date occurrenceTime;

	@ApiModelProperty(value = "申请说明")
	private String applyRemark;

	@ApiModelProperty(value = "费用依据（1-6张图片）")
	private List<DriverCostApplyTickDetail> ticketList;

	@ApiModelProperty("发票信息")
	private List<DriverCostApplyInvoiceRequestModel> invoiceInfoList;

	@Data
	public static class DriverCostApplyTickDetail {

		@ApiModelProperty(value = "图片类型: 1 现场图片, 2 支付图片")
		private Integer type;

		@ApiModelProperty(value = "图片依据路径")
		private List<String> imagePathList;
	}
}
