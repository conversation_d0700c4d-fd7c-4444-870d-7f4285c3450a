package com.logistics.tms.api.feign.platformcompany.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2022/11/11 17:39
 */
@Data
public class SearchPlatformCompanyListResponseModel {

	@ApiModelProperty("结算主体id")
	private Long platformCompanyId;

	@ApiModelProperty("结算主体名")
	private String platformCompanyName;

	@ApiModelProperty(value = "新增人")
	private String createdBy;

	@ApiModelProperty(value = "新增时间")
	private Date createdTime;

	@ApiModelProperty(value = "最新操作人")
	private String lastModifiedBy;

	@ApiModelProperty(value = "最新操作时间")
	private Date lastModifiedTime;
}
