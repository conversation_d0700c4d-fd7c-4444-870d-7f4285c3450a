package com.logistics.management.webapi.client.settlestatement.packaging.response;

import com.logistics.management.webapi.client.carrierorder.response.CarrierOrderOtherFeeItemModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/17
 */
@Data
public class CarrierSettleStatementDetailListResponseModel {

	//导出结算数据使用
	private Long serialNumber;

	@ApiModelProperty("对账单详情item id")
	private Long settleStatementItemId;

	@ApiModelProperty("对账单id")
	private Long settleStatementId;

	@ApiModelProperty("对账单号")
	private String settleStatementCode;

	@ApiModelProperty("对账单名称")
	private String settleStatementName;

	@ApiModelProperty("运单id")
	private Long carrierOrderId;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("平台主体")
	private String platformCompanyName;

	@ApiModelProperty("需求单id")
	private Long demandOrderId;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("调度单id")
	private Long dispatchOrderId;

	@ApiModelProperty("调度单号")
	private String dispatchOrderCode;

	@ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
	private Integer entrustType;

	@ApiModelProperty("车主id")
	private Long companyCarrierId;

	@ApiModelProperty(value = "车主公司类型：1 公司，2 个人")
	private Integer companyCarrierType;

	@ApiModelProperty("车主公司名称")
	private String companyCarrierName;

	@ApiModelProperty("车主联系人")
	private String carrierContactName;

	@ApiModelProperty("车主联系人电话")
	private String carrierContactMobile;

	@ApiModelProperty("货主公司名称")
	private String companyEntrustName;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("司机名手机号")
	private String driverPhone;

	@ApiModelProperty("司机姓名")
	private String driverName;

	@ApiModelProperty("报价类型：1 单价  2 一口价")
	private Integer carrierPriceType;

	@ApiModelProperty("车主价格")
	private BigDecimal carrierPrice;

	@ApiModelProperty("货物单位")
	private Integer goodsUnit;

	@ApiModelProperty("运费")
	private BigDecimal carrierFreight;

	@ApiModelProperty("临时费用")
	private BigDecimal otherFee;

	@ApiModelProperty("预提数量")
	private BigDecimal expectAmount;

	@ApiModelProperty("提货数量")
	private BigDecimal loadAmount;

	@ApiModelProperty("卸货数量")
	private BigDecimal unloadAmount;

	@ApiModelProperty("签收数量")
	private BigDecimal signAmount;

	@ApiModelProperty("结算数量")
	private BigDecimal settlementAmount;

	@ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
	private Integer carrierSettlement;

	@ApiModelProperty("提货时间")
	private Date loadTime;

	@ApiModelProperty("卸货时间")
	private Date unloadTime;

	@ApiModelProperty("签收时间")
	private Date signTime;

	/*提货*/
	private String loadProvinceName;
	private String loadCityName;
	private String loadAreaName;
	private String loadDetailAddress;
	private String loadWarehouse;

	/*卸货*/
	private String unloadProvinceName;
	private String unloadCityName;
	private String unloadAreaName;
	private String unloadDetailAddress;
	private String unloadWarehouse;

	@ApiModelProperty("提货人")
	private String consignorName;

	@ApiModelProperty("提货人")
	private String consignorMobile;

	@ApiModelProperty("卸货人")
	private String receiverName;

	@ApiModelProperty("卸货人")
	private String receiverMobile;

	@ApiModelProperty("调度人")
	private String dispatchUserName;

	@ApiModelProperty("预计里程数")
	private BigDecimal expectMileage;

	@ApiModelProperty("凭证数")
	private Integer ticketCount;

	@ApiModelProperty("是否放空：0 否，1 是")
	private Integer ifEmpty;

	@ApiModelProperty("运单货物")
	private List<CarrierOrderGoodsModel> goodsList;

	@ApiModelProperty("临时费用")
	private List<CarrierOrderOtherFeeItemModel> otherFeeItemModelList;

	@ApiModelProperty("配置距离")
	private String configDistance;

	/**
	 * 议价模式：1 指定车主，2 竞价抢单，3 临时定价，4 零担定价
	 */
	@ApiModelProperty("议价模式：1 指定车主，2 竞价抢单，3 临时定价，4 零担定价")
	private Integer bargainingMode;


	@ApiModelProperty("临时费用费点")
	private BigDecimal otherFeeTaxPoint;

	@ApiModelProperty("运费费点")
	private BigDecimal freightTaxPoint;

}
