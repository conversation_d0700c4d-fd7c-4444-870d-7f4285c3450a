package com.logistics.management.webapi.client.workgroup.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/22 15:44
 */
@Data
public class AddEditWorkGroupNodeRequestModel {

    @ApiModelProperty(value = "智能推送配置表id")
    private Long workGroupId;

    @ApiModelProperty(value = "需求类型：1.回收业务(回收入库、回收出库) 2.采购业务（供应商直配、采购) 3.仓库业务（发货、调拨、退货、退货仓库送、退货调拨、其他）")
    private List<Integer> entrustTypeList;

    @ApiModelProperty(value = "项目标签：0 标签为空，1 石化板块，2 轮胎板块，3 涂料板块，4 其他板块")
    private List<Integer> projectLabelList;

    @NotEmpty(message = "请添加节点")
    private List<AddEditWorkGroupNodeListRequestModel> nodeList;

}
