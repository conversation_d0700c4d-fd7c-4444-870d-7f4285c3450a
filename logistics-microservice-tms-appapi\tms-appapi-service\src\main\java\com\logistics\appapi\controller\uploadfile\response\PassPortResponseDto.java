package com.logistics.appapi.controller.uploadfile.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/5/8 17:25
 */
@Data
public class PassPortResponseDto {
    @ApiModelProperty("姓名")
    private String name="";
    @ApiModelProperty("护照签发地点")
    private String placeOfIssue="";
    @ApiModelProperty("签发日期")
    private String issuingDate="";
    @ApiModelProperty("护照号码")
    private String passportNumber="";
    @ApiModelProperty("出生地点")
    private String placeOfBirth="";
    @ApiModelProperty("生日")
    private String birthday="";
    @ApiModelProperty("国家码")
    private String countryCode="";
    @ApiModelProperty("有效期至")
    private String validDate="";
    @ApiModelProperty("姓名拼音")
    private String namePinyin="";
    @ApiModelProperty("性别")
    private String sex="";
}
