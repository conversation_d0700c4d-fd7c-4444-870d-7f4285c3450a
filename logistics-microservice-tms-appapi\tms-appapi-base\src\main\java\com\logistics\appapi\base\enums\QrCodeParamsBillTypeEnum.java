package com.logistics.appapi.base.enums;

/**
 * @author: wjf
 * @date: 2023/5/5 10:50
 */
public enum QrCodeParamsBillTypeEnum {

    //此枚举如果更改，则云仓也需更改（云仓生成的二维码物流司机会扫）
    SMALL("s", "小单据（小程序司机打印）"),
    RECYCLE("r", "大单据-回收"),
    OTHER("o", "大单据-非回收"),
    ;

    private String key;
    private String remark;

    QrCodeParamsBillTypeEnum(String key, String remark) {
        this.key = key;
        this.remark = remark;
    }

    public String getKey() {
        return key;
    }

    public String getRemark() {
        return remark;
    }

}
