package com.logistics.management.webapi.controller.freightconfig.response.shipping;

import com.logistics.management.webapi.controller.freightconfig.request.shipping.ConfigVehicleItemDto;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class GetConfigVechicleDetailRespDto {

    /**
     * 车长
     */
    private String vehicleLength = "";


    private List<ConfigVehicleItemDto> itemReqDtos = new ArrayList<>();

}
