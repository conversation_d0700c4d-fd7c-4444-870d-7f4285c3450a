package com.logistics.tms.controller.staff.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ContinueLearningAddOrModifyRequestModel {
    @ApiModelProperty("继续教育记录Id")
    private Long continueLearningRecordId;
    @ApiModelProperty("人员Id")
    private Long staffId;
    @ApiModelProperty("继续教育有效期")
    private Date validDate;
    @ApiModelProperty("凭证地址")
    private List<String> imagePaths;
    @ApiModelProperty("备注")
    private String remark;
}
