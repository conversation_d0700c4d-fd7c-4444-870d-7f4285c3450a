package com.logistics.management.webapi.client.freightconfig.hystrix;

import com.logistics.management.webapi.client.freightconfig.CarrierFreightConfigClient;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigAddRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigEditRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigListRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigRequestModel;
import com.logistics.management.webapi.client.freightconfig.response.CarrierFreightConfigDetailResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.CarrierFreightConfigListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: xuanjia.liang
 * @Date: 2023/6/30 11:23
 */
@Component
public class CarrierFreightConfigHystrix implements CarrierFreightConfigClient {

    @Override
    public Result<List<CarrierFreightConfigListResponseModel>> searchList(CarrierFreightConfigListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CarrierFreightConfigDetailResponseModel> detail(CarrierFreightConfigRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> add(CarrierFreightConfigAddRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> edit(CarrierFreightConfigEditRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delete(CarrierFreightConfigRequestModel requestModel) {
        return Result.timeout();
    }
}
