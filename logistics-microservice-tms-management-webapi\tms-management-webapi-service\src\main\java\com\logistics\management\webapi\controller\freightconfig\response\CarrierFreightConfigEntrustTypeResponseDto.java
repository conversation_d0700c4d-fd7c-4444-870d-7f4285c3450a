package com.logistics.management.webapi.controller.freightconfig.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierFreightConfigEntrustTypeResponseDto {

    @ApiModelProperty(value = "需求类型;1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private String entrustType;

    @ApiModelProperty(value = "需求类型文本")
    private String entrustTypeLabel;
}
