package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CarrierOrderDeliverMethodEnum {
    BILL_PICKUP(1, "票据提货"),
    QRCODE_PICKUP(2, "二维码提货"),
    ;
    private final Integer key;
    private final String value;

    public static Integer getEnumKey(Integer key) {
        return getEnumByKey(key).getKey();
    }
    public static CarrierOrderDeliverMethodEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(BILL_PICKUP);
    }
}
