<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleSettlementPaymentMapper" >
    <select id="getByVehicleSettlementId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_vehicle_settlement_payment
        where valid = 1
        and vehicle_settlement_id = #{vehicleSettlementId,jdbcType=BIGINT}
    </select>

    <select id="driverReconciliationBillingRecords" resultType="com.logistics.tms.controller.vehiclesettlement.response.ReconciliationBillingRecordsResponseModel">
        select
        tvsp.pay_company as payCompany,
        tvsp.receiver_name as receiverName,
        tvsp.pay_time as payTime,
        tvsp.pay_fee as payFee
        from t_vehicle_settlement_payment tvsp
        left join t_vehicle_settlement_driver_relation tvsdr on tvsdr.vehicle_settlement_id = tvsp.vehicle_settlement_id and tvsdr.valid = 1
        where tvsp.valid = 1
        and tvsp.vehicle_settlement_id = #{vehicleSettlementId,jdbcType=BIGINT}
        and tvsdr.driver_id = #{driverId,jdbcType=BIGINT}
        order by tvsp.id asc
    </select>
</mapper>