package com.logistics.management.webapi.controller.uploadfile;

import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.DownloadTypeEnum;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.enums.OcrInvoiceTypeEnum;
import com.logistics.management.webapi.client.thirdparty.basicdata.BasicServiceClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.FileClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.response.FileUploadForAIResponseModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.response.OcrMultipleInvoiceRes;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.response.OcrMultipleInvoiceResponseModel;
import com.logistics.management.webapi.controller.uploadfile.response.BillIdentifyResponseDto;
import com.logistics.management.webapi.controller.uploadfile.response.FileUploadForAIResponseDto;
import com.logistics.management.webapi.controller.uploadfile.response.SrcUrlDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 上传文件
 */
@Api(value = "上传文件")
@RestController
@Slf4j
public class UploadFileController {

    @Resource
    private FileClient fileClient;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private BasicServiceClient basicServiceClient;

    /**
     * 上传图片
     * @param file
     * @param request
     * @return
     */
    @PostMapping(value = "/api/uploadFile/uploadFileImage")
    @ApiOperation(value = "上传图片")
    public Result<SrcUrlDto> uploadFileImage(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        return Result.success(commonBiz.uploadToTmpCatalog(file));
    }

    /**
     * 上传图片并识别文字
     * @param file
     * @param request
     * @param picType
     * @param idCardSide
     * @return
     */
    @PostMapping(value = "/api/uploadFile/uploadFileImageForAI")
    @ApiOperation(value = "上传图片并识别文字")
    public Result<FileUploadForAIResponseDto> uploadFileImageForAI(@RequestParam("file") MultipartFile file, HttpServletRequest request, @RequestParam("picType")String picType, @RequestParam("idCardSide")String idCardSide) {
        if (!commonBiz.checkFileSize(file.getSize(), 10, "M")) {
            throw new BizException(ManagementWebApiExceptionEnum.UPLOAD_SIZE_TO_LONG);
        }
        Result<FileUploadForAIResponseModel> result = fileClient.uploadOSSFileForAI(file,picType,idCardSide);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), FileUploadForAIResponseDto.class));
    }

    /**
     * 下载导入模板
     * @param downloadType
     * @param request
     * @param response
     */
    @ApiOperation(value = "下载导入模板")
    @GetMapping(value = "/api/uploadFile/downloadExcel")
    public void downloadExcel(@RequestParam("downloadType")String downloadType, HttpServletRequest request, HttpServletResponse response) {
        if (StringUtils.isBlank(downloadType)){
            return;
        }
        DownloadTypeEnum downloadTypeEnum = DownloadTypeEnum.getEnumByDownloadType(downloadType);
        if (downloadTypeEnum == null){
            return;
        }
        InputStream inputStream = UploadFileController.class.getResourceAsStream("/template/" + downloadTypeEnum.getTemplateName() + "." + downloadTypeEnum.getFileType());
        try {
            log.info("inputStream.available"+inputStream.available());
        } catch (IOException e) {
            log.info("",e);
        }
        try {
            commonBiz.downLoadFile(downloadTypeEnum.getFileName(), downloadTypeEnum.getFileType(), inputStream, response);
        } catch (Exception e) {
            log.info("",e);
        }
    }

    /**
     * 发票识别 3.24.0
     * @param file 需要识别的发票
     * @param type 发票类型：1 增值税发票，2 出租车发票，3 火车票，4 定额发票，5 卷票，6 机打发票，7 过路过桥费发票
     */
    @ApiOperation(value = "发票识别")
    @PostMapping(value = "/api/uploadFile/billIdentify")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<BillIdentifyResponseDto> billIdentify(@RequestParam("file") MultipartFile file, @RequestParam("type") String type) {
        if (StringUtils.isBlank(type)) {
            throw new BizException(ManagementWebApiExceptionEnum.OCR_TYPE_CANNOT_BLANK);
        }
        OcrMultipleInvoiceResponseModel ocrMultipleInvoiceResponseModel = basicServiceClient.multipleInvoice(file, type);
        if (ocrMultipleInvoiceResponseModel == null) {
            throw new BizException(ManagementWebApiExceptionEnum.ERROR_OCR_INVOICE);
        }

        BillIdentifyResponseDto responseDto = new BillIdentifyResponseDto();
        responseDto.setSrc(ocrMultipleInvoiceResponseModel.getSrc());
        responseDto.setRelativePath(ocrMultipleInvoiceResponseModel.getRelativePath());
        OcrMultipleInvoiceRes ocrMultipleInvoiceRes = ocrMultipleInvoiceResponseModel.getOcrInvoiceRes();
        if (Objects.nonNull(ocrMultipleInvoiceRes)) {
            if (OcrInvoiceTypeEnum.VALUE_ADDED_TAX.getKey().equals(ocrMultipleInvoiceRes.getType())) {
                responseDto.setType(OcrInvoiceTypeEnum.VALUE_ADDED_TAX.getValue().getStringKey());
                responseDto.setInvoiceName(ocrMultipleInvoiceRes.getInvoiceTypeOrg());
                responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                responseDto.setInvoiceCode(ocrMultipleInvoiceRes.getInvoiceCode());
                responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getInvoiceNum());
                responseDto.setTotalPrice(ocrMultipleInvoiceRes.getTotalAmount());
                responseDto.setTotalTax(ocrMultipleInvoiceRes.getTotalTax());
                responseDto.setTotalTaxAndPrice(ocrMultipleInvoiceRes.getAmountInFiguers());
            } else if(OcrInvoiceTypeEnum.ROLL_TICKET.getKey().equals(ocrMultipleInvoiceRes.getType())){
                responseDto.setType(OcrInvoiceTypeEnum.ROLL_TICKET.getValue().getStringKey());
                responseDto.setInvoiceName(ocrMultipleInvoiceRes.getInvoiceTypeOrg());
                responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                responseDto.setInvoiceCode(ocrMultipleInvoiceRes.getInvoiceCode());
                responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getInvoiceNum());
                responseDto.setTotalPrice(CommonConstant.ZERO_NET_ZERO);
                responseDto.setTotalTax(CommonConstant.ZERO_NET_ZERO);
                responseDto.setTotalTaxAndPrice(CommonConstant.ZERO_NET_ZERO);
            }else if (OcrInvoiceTypeEnum.TAXI.getKey().equals(ocrMultipleInvoiceRes.getType())) {
                responseDto.setType(OcrInvoiceTypeEnum.TAXI.getValue().getStringKey());
                responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                responseDto.setInvoiceCode(ocrMultipleInvoiceRes.getInvoiceCode());
                responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getInvoiceNum());
                responseDto.setTotalPrice(ocrMultipleInvoiceRes.getTotalFare().replace("￥", "").replace("元", ""));
                try {
                    responseDto.setTotalTaxAndPrice(new BigDecimal(responseDto.getTotalPrice()).add(new BigDecimal(responseDto.getTotalTax())).toPlainString());
                }catch (Exception e){
                    responseDto.setTotalTaxAndPrice("");
                }
            } else if (OcrInvoiceTypeEnum.TRAIN.getKey().equals(ocrMultipleInvoiceRes.getType())) {
                responseDto.setType(OcrInvoiceTypeEnum.TRAIN.getValue().getStringKey());
                responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getTicket_num());
                responseDto.setTotalPrice(ocrMultipleInvoiceRes.getTicket_rates().replace("￥", "").replace("元", ""));
                try {
                    responseDto.setTotalTaxAndPrice(new BigDecimal(responseDto.getTotalPrice()).add(new BigDecimal(responseDto.getTotalTax())).toPlainString());
                }catch (Exception e){
                    responseDto.setTotalTaxAndPrice("");
                }
            } else if (OcrInvoiceTypeEnum.QUOTA.getKey().equals(ocrMultipleInvoiceRes.getType())) {
                responseDto.setType(OcrInvoiceTypeEnum.QUOTA.getValue().getStringKey());
                responseDto.setInvoiceName(ocrMultipleInvoiceRes.getInvoiceTypeOrg());
                responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                responseDto.setInvoiceCode(ocrMultipleInvoiceRes.getInvoice_code());
                responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getInvoice_number());
                responseDto.setTotalPrice(ocrMultipleInvoiceRes.getInvoice_rate_lowercase().replace("￥", "").replace("元", ""));
                try {
                    responseDto.setTotalTaxAndPrice(new BigDecimal(responseDto.getTotalPrice()).add(new BigDecimal(responseDto.getTotalTax())).toPlainString());
                }catch (Exception e){
                    responseDto.setTotalTaxAndPrice("");
                }
            } else if (OcrInvoiceTypeEnum.MACHINE_PRINTING.getKey().equals(ocrMultipleInvoiceRes.getType())) {
                responseDto.setType(OcrInvoiceTypeEnum.MACHINE_PRINTING.getValue().getStringKey());
                responseDto.setInvoiceName(ocrMultipleInvoiceRes.getInvoiceType());
                responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                responseDto.setInvoiceCode(ocrMultipleInvoiceRes.getInvoiceCode());
                responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getInvoiceNum());
                responseDto.setTotalPrice(ocrMultipleInvoiceRes.getAmountInFiguers().replace("￥", "").replace("元", ""));
                responseDto.setTotalTax(CommonConstant.ZERO_NET_ZERO);
                try {
                    responseDto.setTotalTaxAndPrice(new BigDecimal(responseDto.getTotalPrice()).add(new BigDecimal(responseDto.getTotalTax())).toPlainString());
                }catch (Exception e){
                    responseDto.setTotalTaxAndPrice("");
                }
            } else if (OcrInvoiceTypeEnum.PASSING_BY.getKey().equals(ocrMultipleInvoiceRes.getType())) {
                responseDto.setType(OcrInvoiceTypeEnum.PASSING_BY.getValue().getStringKey());
                responseDto.setInvoiceType(ocrMultipleInvoiceRes.getInvoiceType());
                responseDto.setInvoiceCode(ocrMultipleInvoiceRes.getInvoiceCode());
                responseDto.setInvoiceNum(ocrMultipleInvoiceRes.getInvoiceNum());
                responseDto.setTotalPrice(ocrMultipleInvoiceRes.getFare().replace("￥", "").replace("元", ""));
                try {
                    responseDto.setTotalTaxAndPrice(new BigDecimal(responseDto.getTotalPrice()).add(new BigDecimal(responseDto.getTotalTax())).toPlainString());
                }catch (Exception e){
                    responseDto.setTotalTaxAndPrice("");
                }
            }
        }
        return Result.success(responseDto);
    }
}
