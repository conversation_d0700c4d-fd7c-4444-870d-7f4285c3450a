package com.logistics.tms.controller.demandorder.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CancelDemandByDemandRecoverOrderMessage implements Serializable {
    /**
     * 需求单Code集合
     */
    private List<String> demandCodeList;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 取消类型
     */
    private Integer cancelType;

    /**
     * 操作来源：1.网络货运系统 2.TMS系统
     */
    private String operateSource="2";
}
