package com.logistics.tms.mapper;

import com.logistics.tms.controller.carrierorderotherfee.request.SearchOtherFeeListRequestModel;
import com.logistics.tms.controller.carrierorderotherfee.response.CarrierOrderOtherFeeDetailResponseModel;
import com.logistics.tms.controller.carrierorderotherfee.response.SearchOtherFeeListResponseModel;
import com.logistics.tms.entity.TCarrierOrderOtherFee;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/09/02
*/
@Mapper
public interface TCarrierOrderOtherFeeMapper extends BaseMapper<TCarrierOrderOtherFee> {

    /**
     * 前台-临时费用列表id
     * @param requestModel
     * @return
     */
    List<Long> searchListId(@Param("params") SearchOtherFeeListRequestModel requestModel);

    List<SearchOtherFeeListResponseModel> searchList(@Param("ids")String ids);

    CarrierOrderOtherFeeDetailResponseModel getCarrierOrderOtherFeeDetail(@Param("carrierOrderOtherFeeId") Long carrierOrderOtherFeeId);

    List<TCarrierOrderOtherFee> getAuditByCarrierOrderIds(@Param("carrierOrderIds")String carrierOrderIds);

    List<TCarrierOrderOtherFee> getNoCancelByCarrierOrderId(@Param("carrierOrderId")Long carrierOrderId);

    TCarrierOrderOtherFee getTopWaitCommitByCarrierOrderId(@Param("carrierOrderId")Long carrierOrderId);

}