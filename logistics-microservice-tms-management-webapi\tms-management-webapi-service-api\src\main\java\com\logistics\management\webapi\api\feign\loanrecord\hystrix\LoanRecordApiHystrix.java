package com.logistics.management.webapi.api.feign.loanrecord.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.loanrecord.LoanRecordApi;
import com.logistics.management.webapi.api.feign.loanrecord.dto.*;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/9/30 9:35
 */
@Component
public class LoanRecordApiHystrix implements LoanRecordApi {
    @Override
    public Result<PageInfo<LoanRecordListResponseDto>> searchList(LoanRecordListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<SummaryLoanRecordResponseDto> getSummary(LoanRecordListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveOrUpdate(SaveOrUpdateLoanRecordRequestDto recordRequestDto) {
        return Result.timeout();
    }

    @Override
    public Result<LoanRecordDetailResponseDto> getDetail(LoanRecordDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportLoanRecords(LoanRecordListRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }

    @Override
    public void exportSettlementRecords(LoanSettlementRecordRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }

    @Override
    public void exportOperationRecords(LoanOperationRecordRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }

    @Override
    public Result<List<LoanOperationRecordResponseDto>> getOperationRecords(LoanOperationRecordRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<List<LoanSettlementRecordResponseDto>> getSettlementRecords(LoanSettlementRecordRequestDto requestDto) {
        return Result.timeout();
    }
}
