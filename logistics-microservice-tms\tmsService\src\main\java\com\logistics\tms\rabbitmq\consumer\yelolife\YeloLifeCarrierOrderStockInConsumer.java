package com.logistics.tms.rabbitmq.consumer.yelolife;

import com.logistics.tms.biz.carrierorder.CarrierOrderForYloLifeBiz;
import com.logistics.tms.rabbitmq.consumer.model.YeloLifeCarrierOrderInStatusModel;
import com.rabbitmq.client.Channel;
import com.yelo.tools.rabbitmq.annocation.EnableMqErrorMessageCollect;
import com.yelo.tools.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 乐橘新生运单入库状态mq
 */
@Component
@Slf4j
public class YeloLifeCarrierOrderStockInConsumer {

    private ObjectMapper objectMapper = JacksonUtils.getInstance();

    @Autowired
    private CarrierOrderForYloLifeBiz carrierOrderForYloLifeBiz;

    @EnableMqErrorMessageCollect
    @RabbitListener(bindings = {@QueueBinding(
            exchange = @Exchange(name = "logistics.qiyatms.topic", type = "topic", durable = "true"),
            value = @Queue(value = "logistics.tmsSyncLifeCarrierOrderStockIn", durable = "true"),
            key = "tmsSyncLifeCarrierOrderStockIn")}
            , concurrency = "3")
    public void process(@Payload String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {
        log.info("接收新生运单入库状态mq：" + message);
        YeloLifeCarrierOrderInStatusModel yeloLifeRecycleOrderApplyModel = objectMapper.readValue(message, YeloLifeCarrierOrderInStatusModel.class);
        //接收新生运单入库状态mq
        carrierOrderForYloLifeBiz.updateLifeCarrierCodeStockInState(yeloLifeRecycleOrderApplyModel);
        channel.basicAck(deliveryTag, false);
    }
}
