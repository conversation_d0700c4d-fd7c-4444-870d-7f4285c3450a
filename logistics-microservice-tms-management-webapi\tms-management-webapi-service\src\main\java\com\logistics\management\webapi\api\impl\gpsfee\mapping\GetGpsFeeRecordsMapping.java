package com.logistics.management.webapi.api.impl.gpsfee.mapping;

import com.logistics.management.webapi.api.feign.gpsfee.dto.GpsFeeRecordsListResponseDto;
import com.logistics.tms.api.feign.gpsfee.model.GpsFeeRecordsListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @author: wjf
 * @date: 2019/10/9 13:07
 */
public class GetGpsFeeRecordsMapping extends MapperMapping<GpsFeeRecordsListResponseModel,GpsFeeRecordsListResponseDto> {
    @Override
    public void configure() {
        GpsFeeRecordsListResponseModel source = getSource();
        GpsFeeRecordsListResponseDto destination = getDestination();
        if (source != null){
            destination.setServiceFee(source.getServiceFee() + "*" + source.getCooperationPeriod());
            destination.setStartDate(DateUtils.dateToString(source.getStartDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            destination.setEndDate(DateUtils.dateToString(source.getEndDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            destination.setFinishDate(DateUtils.dateToString(source.getFinishDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
    }
}
