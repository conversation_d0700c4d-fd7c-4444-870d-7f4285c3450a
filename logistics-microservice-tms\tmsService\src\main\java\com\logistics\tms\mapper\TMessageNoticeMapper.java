package com.logistics.tms.mapper;

import com.logistics.tms.biz.messagenotice.model.ReadMessageNoticeModel;
import com.logistics.tms.biz.messagenotice.model.SearchMessageNoticeListModel;
import com.logistics.tms.controller.messagenotice.response.SearchMessageNoticeListResponseModel;
import com.logistics.tms.entity.TMessageNotice;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/06/04
*/
@Mapper
public interface TMessageNoticeMapper extends BaseMapper<TMessageNotice> {

    List<SearchMessageNoticeListResponseModel> searchList(@Param("params") SearchMessageNoticeListModel searchMessageNoticeListModel);

    void readMessageNotice(@Param("params")ReadMessageNoticeModel readMessageNoticeModel);

}