package com.logistics.management.webapi.base.enums;

public enum RenewableAuditOrderSourceEnum {

    DEFAULT(-1,""),
    CUSTOMER_SOURCE(1,"新生同步"),
    DRIVER_SOURCE(2,"司机下单");

    private Integer code;
    private String name;

    RenewableAuditOrderSourceEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RenewableAuditOrderSourceEnum getEnum(Integer key) {
        for (RenewableAuditOrderSourceEnum t : values()) {
            if (t.getCode().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
