package com.logistics.appapi.client.reservationorder;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.reservationorder.hystrix.ReservationOrderClientHystrix;
import com.logistics.appapi.client.reservationorder.request.*;
import com.logistics.appapi.client.reservationorder.response.ReservationOrderSignDetailResponseModel;
import com.logistics.appapi.client.reservationorder.response.ReservationOrderSummaryResponseModel;
import com.logistics.appapi.client.reservationorder.response.WaitReservationDetailResponseModel;
import com.logistics.appapi.client.reservationorder.response.WaitReservationResponseModel;
import com.logistics.appapi.client.reservationorder.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2024/8/15 9:02
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/reservationOrder",
        fallback = ReservationOrderClientHystrix.class)
public interface ReservationOrderClient {

    /**
     * 预约汇总
     * @return
     */
    @PostMapping(value = "/summary")
    Result<ReservationOrderSummaryResponseModel> summary();

    /**
     * 待预约列表
     * @return
     */
    @PostMapping(value = "/waitReservationList")
    Result<WaitReservationResponseModel> waitReservationList(@RequestBody WaitReservationRequestModel requestModel);

    /**
     * 待预约详情
     * @return
     */
    @PostMapping(value = "/waitReservationDetail")
    Result<WaitReservationDetailResponseModel> waitReservationDetail(@RequestBody WaitReservationDetailRequestModel requestModel);

    /**
     * 确认预约
     * @return
     */
    @PostMapping(value = "/confirmReservation")
    Result<Boolean> confirmReservation(@RequestBody ConfirmReservationRequestModel requestModel);

    /**
     * 预约单详情
     * @return
     */
    @PostMapping(value = "/reservationOrderDetail")
    Result<ReservationOrderSignDetailResponseModel> reservationOrderDetail(@RequestBody ReservationOrderSignDetailRequestModel requestModel);

    /**
     * 确认签到
     * @return
     */
    @PostMapping(value = "/confirmSign")
    Result<Boolean> confirmSign(@RequestBody ReservationOrderConfirmSignRequestModel requestModel);


    @ApiOperation(value = "预约按钮 进入预约页面 v2.45")
    @PostMapping(value = "/enterReservation")
    Result<Boolean> enterReservation(@RequestBody @Valid EnterReservationRequestModel requestDto);

    @ApiOperation(value = "签到按钮 进入签到页面 v2.45")
    @PostMapping(value = "/enterSignUp")
    Result<Boolean> enterSignUp(@RequestBody @Valid EnterSignUpRequestModel requestDto);


    @PostMapping(value = "/getReservationInfo4H5")
    Result<GetReservationInfo4H5RespModel> getReservationInfo4H5(@RequestBody GetReservationInfo4H5ReqModel requestModel);

}
