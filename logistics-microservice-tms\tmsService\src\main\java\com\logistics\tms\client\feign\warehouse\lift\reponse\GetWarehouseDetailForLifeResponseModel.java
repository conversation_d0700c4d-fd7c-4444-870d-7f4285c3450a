package com.logistics.tms.client.feign.warehouse.lift.reponse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2023/12/4 16:36
 */
@Data
public class GetWarehouseDetailForLifeResponseModel {
    @ApiModelProperty("仓库code")
    private String warehouseCode;
    @ApiModelProperty("仓库Name")
    private String warehouseName;
    @ApiModelProperty("1云仓仓库 2新生仓库")
    private Integer addressType;
    @ApiModelProperty("启用/禁用(1启用,0禁用)")
    private Integer enabled;
    @ApiModelProperty("收货省id")
    private Long unloadProvinceId;
    @ApiModelProperty("收货省")
    private String unloadProvinceName = "";
    @ApiModelProperty("收货市id")
    private Long unloadCityId;
    @ApiModelProperty("收货市")
    private String unloadCityName = "";
    @ApiModelProperty("收货区id")
    private Long unloadAreaId;
    @ApiModelProperty("收货区")
    private String unloadAreaName = "";
    @ApiModelProperty("收货地址详细")
    private String unloadDetailAddress = "";
    @ApiModelProperty("收货人")
    private String receiverName = "";
    @ApiModelProperty("收货人手机号")
    private String receiverMobile = "";
    @ApiModelProperty("作业时间")
    private String jobTime = "";
}
