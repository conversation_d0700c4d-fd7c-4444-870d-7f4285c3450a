package com.logistics.management.webapi.controller.shippingorder.response;

import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/8/6 15:43
 */
@Data
public class GetShippingOrderDetailOrderListResponseDto {

    /**
     * 需求单号
     */
    private String demandOrderCode="";

    /**
     * 发货地
     */
    private String loadAddress="";

    /**
     * 收货地
     */
    private String unloadAddress="";

    /**
     * 品名
     */
    private String goodsName="";

    /**
     * 规格
     */
    private String goodsSize="";

    /**
     * 预提
     */
    private String expectAmount="";

}
