package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class GetTicketsResponseModel {
    @ApiModelProperty("图片Id")
    private Long imageId;
    @ApiModelProperty("图片路径URL")
    private String imagePath;
    @ApiModelProperty("图片名")
    private String imageName;
    @ApiModelProperty("上传人姓名")
    private String uploadUserName;
    @ApiModelProperty("上传时间")
    private Date uploadTime;
    @ApiModelProperty("图片类型：1 提货单，2 出库单，3 签收单，4 其他，5 到达提货地凭证，6 到达卸货地凭证，7 入库单，8 提货现场图片，9 卸货现场图片")
    private Integer imageType;
}
