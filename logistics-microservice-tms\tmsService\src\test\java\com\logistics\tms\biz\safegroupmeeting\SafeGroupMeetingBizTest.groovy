package com.logistics.tms.biz.safegroupmeeting

import com.logistics.tms.api.feign.safegroupmeeting.model.AddSafeGroupMeetingRequestModel
import com.logistics.tms.api.feign.safegroupmeeting.model.SafeGroupMeetingDetailRequestModel
import com.logistics.tms.api.feign.safegroupmeeting.model.SafeGroupMeetingDetailResponseModel
import com.logistics.tms.api.feign.safegroupmeeting.model.SafeGroupMeetingRequestModel
import com.logistics.tms.api.feign.safegroupmeeting.model.SafeGroupMeetingResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TSafetyGroupMeeting
import com.logistics.tms.mapper.TSafetyGroupMeetingAttachmentMapper
import com.logistics.tms.mapper.TSafetyGroupMeetingMapper
import com.logistics.tms.mapper.TSafetyGroupMeetingRelationMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class SafeGroupMeetingBizTest extends Specification {
    @Mock
    TSafetyGroupMeetingMapper tSafetyGroupMeetingMapper
    @Mock
    TSafetyGroupMeetingAttachmentMapper tSafetyGroupMeetingAttachmentMapper
    @Mock
    TSafetyGroupMeetingRelationMapper tSafetyGroupMeetingRelationMapper
    @Mock
    CommonBiz commonBiz
    @InjectMocks
    SafeGroupMeetingBiz safeGroupMeetingBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "add Safe Group Meeting where requestModel=#requestModel"() {
        given:
        when(tSafetyGroupMeetingMapper.getSafetyGroupMeetingByYearAndSeason(anyString(), anyInt())).thenReturn(new TSafetyGroupMeeting(content: "content"))
        when(tSafetyGroupMeetingAttachmentMapper.batchInsertSelective(any())).thenReturn(0)
        when(tSafetyGroupMeetingRelationMapper.batchInsertSelective(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(commonBiz.processReplaceTempPicture(anyString(), any(), anyString())).thenReturn("processReplaceTempPictureResponse")

        expect:
        safeGroupMeetingBiz.addSafeGroupMeeting(requestModel)
        assert expectedResult == false

        where:
        requestModel                          || expectedResult
        new AddSafeGroupMeetingRequestModel() || true
    }

    @Unroll
    def "safe Group Meeting Data where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tSafetyGroupMeetingMapper.getSafeGroupMeetingData(anyString())).thenReturn([new SafeGroupMeetingResponseModel()])

        expect:
        safeGroupMeetingBiz.safeGroupMeetingData(requestModel) == expectedResult

        where:
        requestModel                       || expectedResult
        new SafeGroupMeetingRequestModel() || [new SafeGroupMeetingResponseModel()]
    }

    @Unroll
    def "safe Group Meeting Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tSafetyGroupMeetingMapper.getSafeGroupMeetingDetail(anyLong())).thenReturn(new SafeGroupMeetingDetailResponseModel())
        when(commonBiz.addRealPath(anyString())).thenReturn("addRealPathResponse")

        expect:
        safeGroupMeetingBiz.safeGroupMeetingDetail(requestModel) == expectedResult

        where:
        requestModel                             || expectedResult
        new SafeGroupMeetingDetailRequestModel() || new com.logistics.tms.api.feign.safegroupmeeting.model.SafeGroupMeetingDetailResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme