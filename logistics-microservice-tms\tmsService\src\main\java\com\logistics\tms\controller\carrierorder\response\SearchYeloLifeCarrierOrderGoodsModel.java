package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/23
 */
@Data
public class SearchYeloLifeCarrierOrderGoodsModel {


	private Long goodId;

	@ApiModelProperty("sku名")
	private String skuName;

	@ApiModelProperty("预提数量")
	private BigDecimal expectAmount;

	@ApiModelProperty("提货数量")
	private BigDecimal loadAmount;

	@ApiModelProperty("卸货数量")
	private BigDecimal unloadAmount;

	@ApiModelProperty("签收件数")
	private BigDecimal signAmount;

	// 回收编码 v2.2.3
	@ApiModelProperty("回收编码")
	private String recycleBagCode="";

	// 回收编码 v2.2.3
	@ApiModelProperty("单位 1.kg")
	private Integer unit;

}
