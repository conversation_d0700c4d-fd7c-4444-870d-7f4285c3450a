package com.logistics.tms.biz.demandorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车主信息model
 *
 * <AUTHOR>
 * @date ：Created in 2022/7/12
 */
@Data
public class CompanyCarrierByIdModel {

	@ApiModelProperty("车主公司表id")
	private Long companyCarrierId;

	@ApiModelProperty("车主类型 1公司 2 个人")
	private Integer companyCarrierType;

	@ApiModelProperty("车主公司名称")
	private String companyCarrierName;

	@ApiModelProperty("车主账号联系人id")
	private Long carrierContactId;

	@ApiModelProperty("车主联系人")
	private String carrierContactName;

	@ApiModelProperty("车主联系人电话")
	private String carrierContactPhone;

	@ApiModelProperty("车主公司级别：1 云途，2 二级承运商")
	private Integer companyCarrierLevel;

	@ApiModelProperty("是否加入黑名单：0 否，1 是")
	private Integer ifAddBlacklist;

}
