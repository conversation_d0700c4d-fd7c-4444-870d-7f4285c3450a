package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 多装多卸
 * @Author: sj
 * @Date: 2019/12/24 13:15
 */
@Data
public class FreightAddressRuleMarkupModel {
    @ApiModelProperty("运价地址多装多卸加价ID")
    private Long freightAddressRuleMarkupId;
    @ApiModelProperty("序列")
    private Integer markIndex;
    @ApiModelProperty("阶梯序列")
    private Integer ruleIndex;
    @ApiModelProperty("装货点数")
    private Integer loadAmount;
    @ApiModelProperty("卸货点数")
    private Integer unloadAmount;
    @ApiModelProperty("加价金额")
    private BigDecimal markupFreightFee;
}
