package com.logistics.appapi.controller.attendance.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 考勤打卡详情响应dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Data
public class AttendanceClockDetailResponseDto {

	@ApiModelProperty("已完成的打卡类型,0:未打卡 1:上班打卡 2:下班打卡")
	private String isOverClockInType = "";

	@ApiModelProperty("已完成的打卡类型Label")
	private String isOverClockInTypeLabel = "";

	@ApiModelProperty("上班打卡时间,HH:mm")
	private String onDutyPunchTime = "";

	@ApiModelProperty("下班打卡时间,HH:mm")
	private String offDutyPunchTime = "";
}
