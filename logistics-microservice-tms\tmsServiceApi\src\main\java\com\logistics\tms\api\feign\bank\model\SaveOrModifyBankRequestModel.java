package com.logistics.tms.api.feign.bank.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/7/10 14:06
 */
@Data
public class SaveOrModifyBankRequestModel {
    @ApiModelProperty("银行ID")
    private Long bankId;
    @ApiModelProperty("银行名称")
    private String bankName;
    @ApiModelProperty("支行名称")
    private String branchName;
    @ApiModelProperty("备注")
    private String remark;
}
