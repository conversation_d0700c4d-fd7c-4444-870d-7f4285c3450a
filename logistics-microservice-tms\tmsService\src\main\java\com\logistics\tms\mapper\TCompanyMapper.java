package com.logistics.tms.mapper;

import com.logistics.tms.entity.TCompany;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TCompanyMapper extends BaseMapper<TCompany> {

    TCompany getByName(@Param("companyName")String companyName);

    int updateByPrimaryKeySelectiveForTime(TCompany company);

    List<TCompany> getByIdsName(@Param("companyName")String companyName, @Param("ids")String ids);
} 