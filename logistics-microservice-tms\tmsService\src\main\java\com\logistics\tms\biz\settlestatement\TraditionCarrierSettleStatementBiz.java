package com.logistics.tms.biz.settlestatement;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.biz.carrierorder.model.GetTicketsAmountByCarrierOrderIdsModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.settlestatement.model.CarrierSettleStatementDetailModel;
import com.logistics.tms.biz.settlestatement.model.CarrierSettleStatementOrderDetailModel;
import com.logistics.tms.biz.settlestatement.model.WaitSettleStatementCarrierOrderModel;
import com.logistics.tms.controller.dispatchorder.response.CarrierOrderGoodsModel;
import com.logistics.tms.controller.settlestatement.packaging.request.CarrierSettleStatementDetailListRequestModel;
import com.logistics.tms.controller.settlestatement.packaging.request.CarrierSettleStatementListRequestModel;
import com.logistics.tms.controller.settlestatement.packaging.request.StatementWaitArchiveListRequestModel;
import com.logistics.tms.controller.settlestatement.packaging.response.*;
import com.logistics.tms.controller.settlestatement.tradition.request.*;
import com.logistics.tms.controller.settlestatement.tradition.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.redisdistributedlock.DistributedLock;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TraditionCarrierSettleStatementBiz {

    @Autowired
    private TSettleStatementMapper tSettleStatementMapper;
    @Autowired
    private TSettleStatementItemMapper tSettleStatementItemMapper;
    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private TCarrierOrderGoodsMapper tCarrierOrderGoodsMapper;
    @Autowired
    private TCarrierOrderTicketsMapper tCarrierOrderTicketsMapper;
    @Autowired
    private TPlatformCompanyMapper tPlatformCompanyMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private CarrierOrderCommonBiz carrierOrderCommonBiz;
    @Autowired
    private TPaymentMapper tPaymentMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TSettleStatementArchiveAttachmentMapper tSettleStatementArchiveAttachmentMapper;
    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    @Resource
    private TInvoicingManagementMapper tInvoicingManagementMapper;
    @Resource
    private TInvoicingSettleStatementMapper tInvoicingSettleStatementMapper;

    /**
     * 待对账运单列表
     * @param requestModel
     * @return
     */
    public PageInfo<TraditionWaitSettleStatementListResponseModel> waitSettleStatementList(TraditionWaitSettleStatementListRequestModel requestModel) {
        //前台请求
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (CommonConstant.LONG_ZERO.equals(companyCarrierId)){
                return new PageInfo<>(new ArrayList<>());
            }
            requestModel.setCompanyCarrierId(companyCarrierId);
        }
        //查询待对账运单
        requestModel.enablePaging();
        List<Long> carrierOrderIds = tCarrierOrderMapper.selectCarrierTraditionWaitSettleStatementListIds(requestModel);
        if (ListUtils.isEmpty(carrierOrderIds)) {
            return new PageInfo<>(new ArrayList<>());
        }
        String carrierOrderIdsStr = StringUtils.join(carrierOrderIds, ',');
        PageInfo pageInfo = new PageInfo(carrierOrderIds);

        //查询运单详细信息
        List<TraditionWaitSettleStatementListResponseModel> carrierWaitSettleStatementListResponseModelList = tCarrierOrderMapper.selectCarrierTraditionWaitSettleStatementList(carrierOrderIdsStr);

        //查询货物信息
        Map<Long, List<TCarrierOrderGoods>> goodsGroupMap = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(carrierOrderIdsStr).stream().collect(Collectors.groupingBy(TCarrierOrderGoods::getCarrierOrderId));

        //根据运单id查询票据信息
        List<GetTicketsAmountByCarrierOrderIdsModel> ticketsAmountList = tCarrierOrderTicketsMapper.getTicketsAmountByCarrierOrderIds(carrierOrderIdsStr);
        Map<Long, Integer> carrierOrderTicketsAmountMap = ticketsAmountList.stream().collect(
                Collectors.toMap(GetTicketsAmountByCarrierOrderIdsModel::getCarrierOrderId, GetTicketsAmountByCarrierOrderIdsModel::getAmount));

        //查询结算费用
        List<TPayment> tPaymentList = tPaymentMapper.getByCarrierOrderIds(carrierOrderIdsStr);
        Map<Long, TPayment> carrierOrderPaymentMap = new HashMap<>();
        if (ListUtils.isNotEmpty(tPaymentList)) {
            carrierOrderPaymentMap = tPaymentList.stream().collect(Collectors.toMap(TPayment::getCarrierOrderId, Function.identity()));
        }

        //拼接数据
        for (TraditionWaitSettleStatementListResponseModel waitSettleStatementModel : carrierWaitSettleStatementListResponseModelList) {
            //货物信息
            List<TCarrierOrderGoods> tCarrierOrderGoodList = goodsGroupMap.get(waitSettleStatementModel.getCarrierOrderId());
            if (ListUtils.isNotEmpty(tCarrierOrderGoodList)) {
                waitSettleStatementModel.setGoodsList(MapperUtils.mapper(tCarrierOrderGoodList, CarrierOrderGoodsModel.class));
            }
            //票据数量
            waitSettleStatementModel.setTicketCount(Optional.ofNullable(carrierOrderTicketsAmountMap.get(waitSettleStatementModel.getCarrierOrderId())).orElse(CommonConstant.INTEGER_ZERO));

            //结算运费
            TPayment tPayment = carrierOrderPaymentMap.get(waitSettleStatementModel.getCarrierOrderId());
            if (tPayment != null) {
                waitSettleStatementModel.setCarrierFee(tPayment.getSettlementCostTotal());
            } else {
                /*结算表还没数据的时候,计算结算运费*/
                Integer carrierPriceType = waitSettleStatementModel.getCarrierPriceType();
                if (FreightTypeEnum.FIXED_PRICE.getKey().equals(carrierPriceType)) {
                    //一口价,结算金额=车主价格
                    waitSettleStatementModel.setCarrierFee(waitSettleStatementModel.getCarrierPrice());
                } else {
                    //获取结算吨位
                    BigDecimal settlementAmount = getSettlementAmount(waitSettleStatementModel.getCarrierSettlement(), waitSettleStatementModel.getLoadAmount(), waitSettleStatementModel.getUnloadAmount(), waitSettleStatementModel.getSignAmount(), waitSettleStatementModel.getExpectAmount());
                    //单价,结算金额=车主价格*结算数量
                    waitSettleStatementModel.setCarrierFee(waitSettleStatementModel.getCarrierPrice().multiply(settlementAmount));
                }
            }
        }
        pageInfo.setList(carrierWaitSettleStatementListResponseModelList);
        return pageInfo;
    }

    /**
     * 获取运单结算数量
     *
     * @param carrierSettlement 车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位
     * @param loadAmount        提货数量
     * @param unloadAmount      卸货数量
     * @param signAmount        签收数量
     * @param expectAmount      预计数量
     */
    private BigDecimal getSettlementAmount(Integer carrierSettlement, BigDecimal loadAmount, BigDecimal unloadAmount, BigDecimal signAmount, BigDecimal expectAmount) {
        BigDecimal settlementAmount = BigDecimal.ZERO;
        if (SettlementTonnageEnum.LOAD.getKey().equals(carrierSettlement)) {
            settlementAmount = loadAmount;
        } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(carrierSettlement)) {
            settlementAmount = unloadAmount;
        } else if (SettlementTonnageEnum.SIGN.getKey().equals(carrierSettlement)) {
            settlementAmount = signAmount;
        } else if (SettlementTonnageEnum.EXPECT.getKey().equals(carrierSettlement)) {
            settlementAmount = expectAmount;
        }
        return settlementAmount;
    }

    /**
     * 生成对账单
     *
     * @param requestModel 对账单信息
     */
    @Transactional
    public void createSettleStatement(TraditionCreateSettleStatementRequestModel requestModel) {
        Long companyCarrierId = null;
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (CommonConstant.LONG_ZERO.equals(companyCarrierId)){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
        }
        //没有勾选，则根据筛选条件查询运单
        if (StringUtils.isBlank(requestModel.getCarrierOrderIds())) {
            requestModel.setCompanyCarrierId(companyCarrierId);
            List<Long> carrierOrderIdList = tCarrierOrderMapper.selectCarrierTraditionWaitSettleStatementListIds(MapperUtils.mapper(requestModel, TraditionWaitSettleStatementListRequestModel.class));
            if (ListUtils.isEmpty(carrierOrderIdList)) {
                throw new BizException(CarrierDataExceptionEnum.CHOOSE_SETTLE_STATEMENT_CARRIER_ORDER);
            }
            requestModel.setCarrierOrderIds(StringUtils.join(carrierOrderIdList, ','));
        }

        //最多对5000条数据生成对账单
        List<WaitSettleStatementCarrierOrderModel> tCarrierOrders = tCarrierOrderMapper.selectCarrierWaitSettleStatementCarrierOrdersByIds(requestModel.getCarrierOrderIds(), companyCarrierId);
        if (ListUtils.isEmpty(tCarrierOrders)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        List<Long> companyCarrierIdList = new ArrayList<>();
        BigDecimal carrierFeeTotal = BigDecimal.ZERO;
        for (WaitSettleStatementCarrierOrderModel carrierOrder : tCarrierOrders) {
            if (!companyCarrierIdList.contains(carrierOrder.getCompanyCarrierId())) {
                companyCarrierIdList.add(carrierOrder.getCompanyCarrierId());
            }

            /*结算信息*/
            if (carrierOrder.getDbSettlementCost() != null) {
                carrierOrder.setSettlementAmount(carrierOrder.getDbSettlementAmount());
                carrierOrder.setSettlementCost(carrierOrder.getDbSettlementCost());
            } else {
                setCarrierOrderSettlementInfo(carrierOrder);
            }

            //运单申请费用
            carrierFeeTotal = carrierFeeTotal.add(carrierOrder.getSettlementCost());
        }

        //只有同一车主的运单才能进行对账操作
        if (companyCarrierIdList.size() > CommonConstant.INTEGER_ONE) {
            throw new BizException(CarrierDataExceptionEnum.SETTLEMENT_STATEMENT_PLATFORM_OR_COMPANY_DIFFERENT);
        }
        //对账单没有对账费用，无法对账
        if (carrierFeeTotal.compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.CREATE_SETTLE_APPLY_FEE_ZERO);
        }

        //后台请求获取车主公司id
        WaitSettleStatementCarrierOrderModel carrierOrderModel = tCarrierOrders.get(CommonConstant.INTEGER_ZERO);
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getSource())) {//后台
            companyCarrierId = carrierOrderModel.getCompanyCarrierId();
        }

        //查询公司信息
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(companyCarrierId);
        if (tCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(tCompanyCarrier.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        //前台请求使用公司维护的税点
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            requestModel.setFreightTaxPoint(tCompanyCarrier.getFreightTaxPoint());
        }

        //查询结算主体
        String defaultPlatformCompanyName = commonBiz.getDefaultPlatformCompanyName();
        TPlatformCompany tPlatformCompany = tPlatformCompanyMapper.getByName(defaultPlatformCompanyName);

        //获取锁
        String redisKey = CommonConstant.CARRIER_TRADITION_CREATE_SETTLE_STATEMENT_LOCK_KEY + companyCarrierId;
        String uuid = commonBiz.getDistributedLock(redisKey);

        try {
            //查询运单
            tCarrierOrders = tCarrierOrderMapper.selectCarrierWaitSettleStatementCarrierOrdersByIds(requestModel.getCarrierOrderIds(), companyCarrierId);
            if (ListUtils.isEmpty(tCarrierOrders)) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
            }

            List<TSettleStatementItem> tSettleStatementItemList = new ArrayList<>();
            List<Long> upCarrierOrderIdList = new ArrayList<>();
            for (WaitSettleStatementCarrierOrderModel carrierOrder : tCarrierOrders) {

                /*结算信息*/
                if (carrierOrder.getDbSettlementCost() != null) {
                    carrierOrder.setSettlementAmount(carrierOrder.getDbSettlementAmount());
                    carrierOrder.setSettlementCost(carrierOrder.getDbSettlementCost());
                } else {
                    //结算信息
                    setCarrierOrderSettlementInfo(carrierOrder);
                }

                //对账单运单
                tSettleStatementItemList.add(setSettleStatementItemEntity(carrierOrder));

                //修改运单状态
                upCarrierOrderIdList.add(carrierOrder.getCarrierOrderId());
            }

            //新增对账单信息
            TSettleStatement tSettleStatement = new TSettleStatement();
            tSettleStatement.setSettleStatementCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.TRADITION_SETTLE_STATEMENT_CODE, "", BaseContextHandler.getUserName()));
            tSettleStatement.setSettleStatementType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
            tSettleStatement.setSettleStatementName(requestModel.getSettleStatementName());
            tSettleStatement.setSettleStatementMonth(requestModel.getSettleStatementMonth());
            tSettleStatement.setCompanyRole(CommonConstant.INTEGER_TWO);
            tSettleStatement.setFreightTaxPoint(requestModel.getFreightTaxPoint());
            tSettleStatement.setSettleStatementStatus(SettleStatementStatusEnum.WAIT_SUBMITTED.getKey());
            tSettleStatement.setPlatformCompanyId(tPlatformCompany == null ? null : tPlatformCompany.getId());
            tSettleStatement.setPlatformCompanyName(tPlatformCompany == null ? null : tPlatformCompany.getCompanyName());
            commonBiz.setBaseEntityAdd(tSettleStatement, BaseContextHandler.getUserName());
            tSettleStatementMapper.insertSelective(tSettleStatement);

            for (TSettleStatementItem settleStatementItem : tSettleStatementItemList) {
                settleStatementItem.setSettleStatementId(tSettleStatement.getId());
            }
            //生成对账单条目
            batchInsertSettleStatementItem(tSettleStatementItemList);

            //更新运单状态
            carrierOrderCommonBiz.updateSettleStatementCarrierOrderStatus(upCarrierOrderIdList, CarrierSettleStatementStatusEnum.WAIT_SUBMITTED,
                    tSettleStatement.getOtherFeeTaxPoint(),tSettleStatement.getFreightTaxPoint());
        } catch (Exception e) {
            throw e;
        } finally {
            //释放锁
            commonBiz.removeDistributedLock(redisKey, uuid);
        }
    }

    /**
     * 批量插入对账单条目信息
     *
     * @param tSettleStatementItemList 对账单条目
     */
    @Transactional
    public void batchInsertSettleStatementItem(List<TSettleStatementItem> tSettleStatementItemList) {
        if (ListUtils.isNotEmpty(tSettleStatementItemList)) {
            //使用批量模式插入数据
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            TSettleStatementItemMapper tSettleStatementItemMapper = sqlSession.getMapper(TSettleStatementItemMapper.class);

            try {
                //拆分集合
                List<List<TSettleStatementItem>> partitionSettleStatementList = org.apache.commons.collections4.ListUtils.partition(tSettleStatementItemList, CommonConstant.INTEGER_TEN);
                for (List<TSettleStatementItem> tSettleStatementItems : partitionSettleStatementList) {
                    tSettleStatementItemMapper.batchValuesInsert(tSettleStatementItems);
                }
                sqlSession.commit();
            } catch (Exception e) {
                sqlSession.rollback();
                throw new BizException(CarrierDataExceptionEnum.CREATE_SETTLE_FAIL);
            } finally {
                sqlSession.close();
            }
        }
    }

    /**
     * 设置运单结算数量和结算金额
     *
     * @param tCarrierOrder 运单信息
     */
    private void setCarrierOrderSettlementInfo(WaitSettleStatementCarrierOrderModel tCarrierOrder) {
        //是否已放空
        BigDecimal settlementAmount;
        if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())) {
            //已放空取预计数量
            settlementAmount = tCarrierOrder.getExpectAmount();
        } else {
            settlementAmount = getSettlementAmount(tCarrierOrder.getCarrierSettlement(), tCarrierOrder.getLoadAmount(), tCarrierOrder.getUnloadAmount(), tCarrierOrder.getSignAmount(), tCarrierOrder.getExpectAmount());
        }
        tCarrierOrder.setSettlementAmount(settlementAmount);
        Integer carrierPriceType = tCarrierOrder.getCarrierPriceType();
        if (FreightTypeEnum.FIXED_PRICE.getKey().equals(carrierPriceType)) {
            //一口价,结算金额=车主价格
            tCarrierOrder.setSettlementCost(tCarrierOrder.getCarrierPrice());
        } else {
            //单价,结算金额=车主价格*结算数量
            tCarrierOrder.setSettlementCost(tCarrierOrder.getCarrierPrice().multiply(settlementAmount));
        }
    }

    /**
     * 设置有默认值的对账单运单属性信息
     *
     * @param tCarrierOrder 运单数据
     * @return 对账单运单
     */
    private TSettleStatementItem setSettleStatementItemEntity(WaitSettleStatementCarrierOrderModel tCarrierOrder) {
        //生成批量插入对象,设置默认值,保证生成的DML语句除预占位符外格式一致
        TSettleStatementItem tSettleStatementItem = new TSettleStatementItem();
        tSettleStatementItem.setCarrierOrderId(Optional.ofNullable(tCarrierOrder.getCarrierOrderId()).orElse(CommonConstant.LONG_ZERO));
        tSettleStatementItem.setCarrierOrderCode(Optional.ofNullable(tCarrierOrder.getCarrierOrderCode()).orElse(CommonConstant.BLANK_TEXT));
        tSettleStatementItem.setSettlementAmount(Optional.ofNullable(tCarrierOrder.getSettlementAmount()).orElse(BigDecimal.ZERO));
        tSettleStatementItem.setGoodsUnit(Optional.ofNullable(tCarrierOrder.getGoodsUnit()).orElse(CommonConstant.INTEGER_ZERO));
        tSettleStatementItem.setEntrustFreight(Optional.ofNullable(tCarrierOrder.getSettlementCost()).orElse(BigDecimal.ZERO));
        tSettleStatementItem.setOtherFees(Optional.ofNullable(tCarrierOrder.getOtherFees()).orElse(BigDecimal.ZERO));
        tSettleStatementItem.setCompanyEntrustName(Optional.ofNullable(tCarrierOrder.getCompanyEntrustName()).orElse(CommonConstant.BLANK_TEXT));
        tSettleStatementItem.setCompanyCarrierId(Optional.ofNullable(tCarrierOrder.getCompanyCarrierId()).orElse(CommonConstant.LONG_ZERO));
        tSettleStatementItem.setCompanyCarrierName(Optional.ofNullable(tCarrierOrder.getCompanyCarrierName()).orElse(CommonConstant.BLANK_TEXT));
        tSettleStatementItem.setType(Optional.ofNullable(tCarrierOrder.getCompanyCarrierType()).orElse(CommonConstant.INTEGER_ZERO));
        tSettleStatementItem.setContactName(Optional.ofNullable(tCarrierOrder.getCarrierContactName()).orElse(CommonConstant.BLANK_TEXT));
        tSettleStatementItem.setContactPhone(Optional.ofNullable(tCarrierOrder.getCarrierContactPhone()).orElse(CommonConstant.BLANK_TEXT));
        commonBiz.setBaseEntityAdd(tSettleStatementItem, BaseContextHandler.getUserName());
        return tSettleStatementItem;
    }

    /**
     * 查询车主税点
     * @param requestModel
     * @return
     */
    public TraditionCarrierTaxPointResponseModel queryTaxPoint(TraditionCarrierTaxPointRequestModel requestModel) {
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {//前台
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (CommonConstant.LONG_ZERO.equals(companyCarrierId)){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
            requestModel.setCompanyCarrierId(companyCarrierId);
        }

        TraditionCarrierTaxPointResponseModel responseModel = new TraditionCarrierTaxPointResponseModel();
        CarrierTaxPointResponseModel carrierTaxPointResponseModel = tCompanyCarrierMapper.selectTaxPointById(requestModel.getCompanyCarrierId());
        if (carrierTaxPointResponseModel != null) {
            responseModel.setFreightTaxPoint(carrierTaxPointResponseModel.getFreightTaxPoint());
        }
        return responseModel;
    }

    /**
     * 对账单列表
     *
     * @return
     */
    public PageInfo<CarrierTraditionStatementListResponseModel> settleStatementList(CarrierTraditionStatementListRequestModel requestModel) {
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {//前台
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
                return new PageInfo<>(new ArrayList<>());
            }
            requestModel.setCompanyCarrierId(companyCarrierId);
        }
        //分页查询
        requestModel.enablePaging();
        List<Long> idList = tSettleStatementMapper.carrierSettleStatementIdList(MapperUtils.mapperNoDefault(requestModel, CarrierSettleStatementListRequestModel.class)
                , SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        //获取分页结果
        PageInfo pageInfo = new PageInfo(idList);
        if (ListUtils.isNotEmpty(idList)) {
            String settleStatementIds = StringUtils.listToString(idList, ',');
            //查询对账单信息
            List<CarrierSettleStatementListResponseModel> responseModels = tSettleStatementMapper.carrierSettleStatementList(settleStatementIds);
            //对账单子表
            List<CarrierSettleStatementItemModel> itemList = tSettleStatementItemMapper.getBySettleStatementIds(settleStatementIds);
            Map<Long, List<CarrierSettleStatementItemModel>> itemListMap = itemList.stream().collect(Collectors.groupingBy(CarrierSettleStatementItemModel::getSettleStatementId, Collectors.toList()));

            //拼数据
            for (CarrierSettleStatementListResponseModel model : responseModels) {
                model.setItemList(itemListMap.get(model.getSettleStatementId()));
            }
            pageInfo.setList(MapperUtils.mapper(responseModels, CarrierTraditionStatementListResponseModel.class));
        }
        return pageInfo;
    }

    /**
     * 关联运单号
     *
     * @param requestModel
     * @return
     */
    public TraditionAssociationCarrierOrderResponseModel associationCarrierOrder(TraditionAssociationCarrierOrderRequestModel requestModel) {
        //查询对账单是否存在
        TSettleStatement tSettleStatement = tSettleStatementMapper.selectByPrimaryKey(requestModel.getSettleStatementId());
        if (tSettleStatement == null || IfValidEnum.INVALID.getKey().equals(tSettleStatement.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }
        //自营业务
        if (!SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey().equals(tSettleStatement.getSettleStatementType())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }
        if (SettleStatementStatusEnum.CANCEL.getKey().equals(tSettleStatement.getSettleStatementStatus())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
        }

        //查询对账单子条目(运单信息)
        CarrierAssociationCarrierOrderResponseModel associationCarrierOrderInfo = tSettleStatementItemMapper.carrierAssociationCarrierOrderCount(requestModel.getSettleStatementId());
        TraditionAssociationCarrierOrderResponseModel responseModel = MapperUtils.mapper(associationCarrierOrderInfo, TraditionAssociationCarrierOrderResponseModel.class);
        //分页查询运单信息
        requestModel.enablePaging();
        List<CarrierAssociationCarrierOrderItemModel> list = tSettleStatementItemMapper.carrierAssociationCarrierOrder(requestModel.getSettleStatementId());

        //查询运单上需求类型
        List<Long> carrierOrderIdList = list.stream().map(CarrierAssociationCarrierOrderItemModel::getCarrierOrderId).collect(Collectors.toList());
        List<TCarrierOrder> tCarrierOrderList = tCarrierOrderMapper.selectCarrierOrdersByIds(StringUtils.listToString(carrierOrderIdList, ','));
        Map<Long, Integer> entrustTypeMap = tCarrierOrderList.stream().collect(Collectors.toMap(TCarrierOrder::getId, TCarrierOrder::getDemandOrderEntrustType));

        //拼接数据
        list.forEach(item -> item.setDemandOrderEntrustType(entrustTypeMap.get(item.getCarrierOrderId())));

        PageInfo pageInfo = new PageInfo(list);
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(), TraditionAssociationCarrierOrderItemModel.class));
        responseModel.setCarrierOrderItemList(pageInfo);
        return responseModel;
    }

    /**
     * 编辑对账月份
     *
     * @param requestModel
     */
    @Transactional
    public void modifySettleStatementMonth(ModifyTraditionStatementMonthRequestModel requestModel) {
        //判断是否有操作权限
        this.checkModifyPermission(requestModel.getSettleStatementId(), CommonConstant.INTEGER_ONE, requestModel.getSource());

        //更新对账单
        TSettleStatement upSettleStatement = new TSettleStatement();
        upSettleStatement.setId(requestModel.getSettleStatementId());
        upSettleStatement.setSettleStatementMonth(requestModel.getSettleStatementMonth());
        commonBiz.setBaseEntityModify(upSettleStatement, BaseContextHandler.getUserName());
        tSettleStatementMapper.updateByPrimaryKeySelective(upSettleStatement);
    }

    //修改对账月份、修改结算主体、修改对账单费点、修改对账单名称，判断是否有操作权限
    private TSettleStatement checkModifyPermission(Long settleStatementId, Integer operateType, Integer source){
        //查询对账单是否存在
        TSettleStatement tSettleStatement = tSettleStatementMapper.selectByPrimaryKey(settleStatementId);
        if (tSettleStatement == null || IfValidEnum.INVALID.getKey().equals(tSettleStatement.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }
        //自营业务
        if (!SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey().equals(tSettleStatement.getSettleStatementType())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }

        CarrierDataExceptionEnum carrierDataExceptionEnum;
        if (CommonConstant.INTEGER_ONE.equals(operateType)){
            carrierDataExceptionEnum = CarrierDataExceptionEnum.SETTLE_STATEMENT_CANNOT_EDIT_MONTH;
        }else if (CommonConstant.INTEGER_TWO.equals(operateType)){
            carrierDataExceptionEnum = CarrierDataExceptionEnum.SETTLE_STATEMENT_CANNOT_EDIT_PLATFORM_NAME;
        }else if (CommonConstant.INTEGER_THREE.equals(operateType)){
            carrierDataExceptionEnum = CarrierDataExceptionEnum.SETTLE_STATEMENT_CANNOT_EDIT_TAX_POINT;
        }else if (CommonConstant.INTEGER_FOUR.equals(operateType)){
            carrierDataExceptionEnum = CarrierDataExceptionEnum.SETTLE_STATEMENT_CANNOT_EDIT_NAME;
        }else{
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        //前后台不同权限判断
        if (CommonConstant.INTEGER_TWO.equals(source)) {//前台
            //判断对账单是否是该车主的
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            TSettleStatementItem tSettleStatementItem = tSettleStatementItemMapper.getTopBySettleStatementId(tSettleStatement.getId());
            if (tSettleStatementItem == null || IfValidEnum.INVALID.getKey().equals(tSettleStatementItem.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
            }
            if (!companyCarrierId.equals(tSettleStatementItem.getCompanyCarrierId())){
                throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
            }

            //判断对账单状态(已驳回，待提交)
            if (!SettleStatementStatusEnum.WAIT_SUBMITTED.getKey().equals(tSettleStatement.getSettleStatementStatus())
                    && !SettleStatementStatusEnum.REJECT.getKey().equals(tSettleStatement.getSettleStatementStatus())) {
                throw new BizException(carrierDataExceptionEnum);
            }
        }else{//后台
            //判断对账单状态(已驳回，待提交，待业务审核，待财务审核)
            if (!SettleStatementStatusEnum.WAIT_SUBMITTED.getKey().equals(tSettleStatement.getSettleStatementStatus())
                    && !SettleStatementStatusEnum.WAIT_BUSINESS_AUDIT.getKey().equals(tSettleStatement.getSettleStatementStatus())
                    && !SettleStatementStatusEnum.WAIT_FINANCIAL_AUDIT.getKey().equals(tSettleStatement.getSettleStatementStatus())
                    && !SettleStatementStatusEnum.REJECT.getKey().equals(tSettleStatement.getSettleStatementStatus())) {
                throw new BizException(carrierDataExceptionEnum);
            }
        }

        return tSettleStatement;
    }

    /**
     * 对账单详情-审核/驳回
     *
     * @param requestModel 对账单id
     */
    @Transactional
    public void auditOrReject(ChangeTraditionStatementStatsRequestModel requestModel) {
        //查询对账单是否存在
        TSettleStatement tSettleStatement = tSettleStatementMapper.selectByPrimaryKey(requestModel.getSettleStatementId());
        if (tSettleStatement == null || IfValidEnum.INVALID.getKey().equals(tSettleStatement.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }
        //自营业务
        if (!SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey().equals(tSettleStatement.getSettleStatementType())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }
        //查询对账单下运单信息
        List<TSettleStatementItem> settleStatementItemList = tSettleStatementItemMapper.selectByStatementId(tSettleStatement.getId(), null, null);
        if (ListUtils.isEmpty(settleStatementItemList)) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
        }
        //业务人员操作,判断对账单状态
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getOperationType())) {
            //业务人员操作
            if (!(SettleStatementStatusEnum.WAIT_BUSINESS_AUDIT.getKey().equals(tSettleStatement.getSettleStatementStatus()))) {
                throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
            }
        } else if (CommonConstant.INTEGER_TWO.equals(requestModel.getOperationType())) {
            //财务人员操作
            if (!SettleStatementStatusEnum.WAIT_FINANCIAL_AUDIT.getKey().equals(tSettleStatement.getSettleStatementStatus())) {
                throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
            }
        } else {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
        }

        //判断取哪种状态
        SettleStatementStatusEnum statusEnum;
        CarrierSettleStatementStatusEnum carrierOrderStatusEnum;
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getSettleStatementStatus())) {
            if (SettleStatementStatusEnum.WAIT_BUSINESS_AUDIT.getKey().equals(tSettleStatement.getSettleStatementStatus())) {
                //待业务审核通过后变为待财务审核
                statusEnum = SettleStatementStatusEnum.WAIT_FINANCIAL_AUDIT;
                carrierOrderStatusEnum = CarrierSettleStatementStatusEnum.WAIT_FINANCIAL_AUDIT;
            } else {
                //待财务审核通过后变为已对账
                statusEnum = SettleStatementStatusEnum.ACCOUNT_CHECKED;
                carrierOrderStatusEnum = CarrierSettleStatementStatusEnum.ACCOUNT_CHECKED;
            }
        } else {
            statusEnum = SettleStatementStatusEnum.REJECT;
            carrierOrderStatusEnum = CarrierSettleStatementStatusEnum.REJECT;
        }

        ///获取费点
        BigDecimal freightTaxPoint = tSettleStatement.getFreightTaxPoint();//运费费点
        //更新应付金额
        List<Long> carrierOrderIds = new ArrayList<>();
        List<TSettleStatementItem> updateList = new ArrayList<>();
        BigDecimal totalPay = BigDecimal.ZERO.add(Optional.ofNullable(tSettleStatement.getAdjustFee()).orElse(BigDecimal.ZERO));//对账单运单总费用
        BigDecimal allCarrierFreight = BigDecimal.ZERO;//全部运单运费之和
        for (TSettleStatementItem settleStatementItem : settleStatementItemList) {
            if (!CommonConstant.INTEGER_TWO.equals(requestModel.getSettleStatementStatus())) {
                //结算运费
                BigDecimal carrierFreightScope = settleStatementItem.getEntrustFreight();
                //累加运费
                allCarrierFreight = allCarrierFreight.add(carrierFreightScope);

                //单条运单运费费额合计
                BigDecimal carrierFreight = carrierFreightScope;
                carrierFreight = getFreightTotal(freightTaxPoint, carrierFreight);

                TSettleStatementItem tSettleStatementItem = new TSettleStatementItem();
                tSettleStatementItem.setId(settleStatementItem.getId());
                //设置单条运单的总费用
                tSettleStatementItem.setPayableFee(carrierFreight);
                totalPay = totalPay.add(tSettleStatementItem.getPayableFee());
                commonBiz.setBaseEntityModify(tSettleStatementItem, BaseContextHandler.getUserName());
                updateList.add(tSettleStatementItem);
            }
            carrierOrderIds.add(settleStatementItem.getCarrierOrderId());
        }

        //审核时，总费用不大于0，不能操作
        if (!CommonConstant.INTEGER_TWO.equals(requestModel.getSettleStatementStatus()) &&
                totalPay.compareTo(CommonConstant.BIG_DECIMAL_ZERO) <= CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.CREATE_SETTLE_APPLY_FEE_ZERO);
        }

        //更新对账单信息
        TSettleStatement updateSettleStatement = new TSettleStatement();
        updateSettleStatement.setId(requestModel.getSettleStatementId());
        updateSettleStatement.setSettleStatementStatus(statusEnum.getKey());
        updateSettleStatement.setRemark(requestModel.getRemark());
        updateSettleStatement.setAuditName(BaseContextHandler.getUserName());
        updateSettleStatement.setAuditTime(new Date());
        //审核通过记录对账单总费用
        if (SettleStatementStatusEnum.ACCOUNT_CHECKED.getKey().equals(statusEnum.getKey())) {
            //计算对账单总费用 (运单总运费费额合计+运单总临时费用费额合计+调整费用)
            BigDecimal applyTotalFee;
            //运费费额合计
            BigDecimal carrierFreightTotal = getFreightTotal(freightTaxPoint, allCarrierFreight);

            applyTotalFee = carrierFreightTotal.add(tSettleStatement.getAdjustFee());
            updateSettleStatement.setApplyTotalFee(applyTotalFee);
        }
        commonBiz.setBaseEntityModify(updateSettleStatement, BaseContextHandler.getUserName());
        tSettleStatementMapper.updateByPrimaryKeySelective(updateSettleStatement);

        //更新对账单下运单应付费用信息
        if (ListUtils.isNotEmpty(updateList)) {
            tSettleStatementItemMapper.batchUpdate(updateList);
        }

        // 更新运单状态
        if (ListUtils.isNotEmpty(carrierOrderIds)) {
            carrierOrderCommonBiz.updateSettleStatementCarrierOrderStatus(carrierOrderIds, carrierOrderStatusEnum);
        }
    }

    /**
     * 对账单详情-合计数据
     *
     * @param requestModel 对账单id
     * @return 合计
     */
    public CarrierTraditionStatementDetailTotalResponseModel settleStatementDetailTotal(CarrierTraditionStatementIdRequestModel requestModel) {
        //前台请求
        Long companyCarrierId = this.getLoginUserCompanyCarrierId(requestModel.getSource());
        // 查询对账单是否存在
        CarrierSettleStatementDetailTotalResponseModel settleStatementDetail =
                tSettleStatementMapper.selectDetailById(requestModel.getSettleStatementId(), companyCarrierId,
                        SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        if (settleStatementDetail == null) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }
        //已撤销不能查看详情
        if (SettleStatementStatusEnum.CANCEL.getKey().equals(settleStatementDetail.getSettleStatementStatus())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
        }
        return MapperUtils.mapper(settleStatementDetail, CarrierTraditionStatementDetailTotalResponseModel.class);
    }

    /**
     * 对账单详情-对账单运单条目信息
     *
     * @param requestModel 对账单id,筛选条件
     * @return 运单条目信息
     */
    public PageInfo<CarrierTraditionStatementDetailListResponseModel> settleStatementDetailList(CarrierTraditionStatementDetailListRequestModel requestModel, boolean isExport) {
        Long loginUserCompanyCarrierId = this.getLoginUserCompanyCarrierId(requestModel.getSource());
        //查询对账单
        TSettleStatement settleStatement = this.getSettleStatement(requestModel.getSettleStatementId());
        if (SettleStatementStatusEnum.CANCEL.getKey().equals(settleStatement.getSettleStatementStatus())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
        }
        //查询对账单下运单信息
        List<TSettleStatementItem> settleStatementItemList =
                tSettleStatementItemMapper.selectByStatementId(settleStatement.getId(),
                        requestModel.getSettleStatementItemIds(), loginUserCompanyCarrierId);
        if (ListUtils.isEmpty(settleStatementItemList)) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }
        //对账单下运单id集合
        Map<Long, TSettleStatementItem> tSettleStatementItemMap = new HashMap<>();
        List<Long> carrierIdListScope = new ArrayList<>();
        for (TSettleStatementItem tSettleStatementItem : settleStatementItemList) {
            carrierIdListScope.add(tSettleStatementItem.getCarrierOrderId());
            tSettleStatementItemMap.put(tSettleStatementItem.getCarrierOrderId(), tSettleStatementItem);
        }
        //对账单下运单表ids
        String carrierOrderIdsStr = StringUtils.join(new ArrayList<>(carrierIdListScope), ',');

        //查询运单信息
        CarrierSettleStatementDetailListRequestModel queryStatementCarrierOrder =
                MapperUtils.mapper(requestModel, CarrierSettleStatementDetailListRequestModel.class);
        Page<CarrierTraditionStatementDetailListResponseModel> page = PageHelper.startPage(requestModel);
        if (isExport) {
            page.count(false)
                .pageSizeZero(Boolean.TRUE)
                .setPageSize(CommonConstant.INTEGER_ZERO);
        }
        List<CarrierSettleStatementDetailListResponseModel> carrierOrder =
                tCarrierOrderMapper.selectSettleStatementCarrierOrder(queryStatementCarrierOrder, carrierOrderIdsStr);
        List<CarrierTraditionStatementDetailListResponseModel> responseModelList =
                MapperUtils.mapper(carrierOrder, CarrierTraditionStatementDetailListResponseModel.class);
        if (ListUtils.isNotEmpty(responseModelList)) {
            String responseCarrierIdsStr = responseModelList
                    .stream()
                    .map(s -> String.valueOf(s.getCarrierOrderId()))
                    .collect(Collectors.joining(","));
            //查询货物信息
            Map<Long, List<TCarrierOrderGoods>> carrierOrderGoodsGroupMap = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(responseCarrierIdsStr).stream().collect(Collectors.groupingBy(TCarrierOrderGoods::getCarrierOrderId));
            //根据运单id查询票据信息
            List<GetTicketsAmountByCarrierOrderIdsModel> ticketsAmountList = tCarrierOrderTicketsMapper.getTicketsAmountByCarrierOrderIds(responseCarrierIdsStr);
            Map<Long, Integer> carrierOrderTicketsAmountMap = ticketsAmountList.stream().collect(
                    Collectors.toMap(GetTicketsAmountByCarrierOrderIdsModel::getCarrierOrderId, GetTicketsAmountByCarrierOrderIdsModel::getAmount));

            //数据拼接
            Long count = CommonConstant.LONG_ONE;
            for (CarrierTraditionStatementDetailListResponseModel responseModelListItem : responseModelList) {
                responseModelListItem.setSettleStatementId(settleStatement.getId());
                responseModelListItem.setSettleStatementName(settleStatement.getSettleStatementName());
                responseModelListItem.setSettleStatementCode(settleStatement.getSettleStatementCode());
                responseModelListItem.setPlatformCompanyName(settleStatement.getPlatformCompanyName());

                //对账单运单
                TSettleStatementItem tSettleStatementItem = tSettleStatementItemMap.get(responseModelListItem.getCarrierOrderId());
                responseModelListItem.setSettleStatementItemId(tSettleStatementItem.getId());

                responseModelListItem.setSettlementAmount(tSettleStatementItem.getSettlementAmount());
                responseModelListItem.setCarrierFreight(tSettleStatementItem.getEntrustFreight());

                //票据数量
                responseModelListItem.setTicketCount(Optional.ofNullable(carrierOrderTicketsAmountMap.get(responseModelListItem.getCarrierOrderId())).orElse(CommonConstant.INTEGER_ZERO));

                //货物信息
                List<TCarrierOrderGoods> tCarrierOrderGoodListScope = carrierOrderGoodsGroupMap.get(responseModelListItem.getCarrierOrderId());
                responseModelListItem.setGoodsList(MapperUtils.mapper(tCarrierOrderGoodListScope, CarrierOrderGoodsModel.class));

                responseModelListItem.setSerialNumber(count);
                count++;
            }
        }
        PageInfo<CarrierTraditionStatementDetailListResponseModel> pageInfo = new PageInfo<>(page);
        pageInfo.setList(responseModelList);
        return pageInfo;
    }

    /**
     * 编辑结算主体
     *
     * @param requestModel
     */
    @Transactional
    public void modifyPlatformCompany(TraditionModifyPlatformCompanyRequestModel requestModel) {
        //判断是否有操作权限
        TSettleStatement dbSettleStatement = this.checkModifyPermission(requestModel.getSettleStatementId(), CommonConstant.INTEGER_TWO, null);

        //校验结算主体是否存在
        TPlatformCompany dbPlatformCompany = null;
        if (!dbSettleStatement.getPlatformCompanyId().equals(requestModel.getSettleStatementId())) {
            dbPlatformCompany = tPlatformCompanyMapper.selectByPrimaryKey(requestModel.getPlatformCompanyId());
            if (dbPlatformCompany == null || IfValidEnum.INVALID.getKey().equals(dbPlatformCompany.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.PLATFORM_COMPANY_NOT_EXIST);
            }
        }

        //更新对账单
        TSettleStatement upSettleStatement = new TSettleStatement();
        upSettleStatement.setId(requestModel.getSettleStatementId());
        if (dbPlatformCompany != null) {
            upSettleStatement.setPlatformCompanyId(dbPlatformCompany.getId());
            upSettleStatement.setPlatformCompanyName(dbPlatformCompany.getCompanyName());
        }
        if (StringUtils.isBlank(requestModel.getContractCode())) {
            upSettleStatement.setContractCode("");
        } else {
            upSettleStatement.setContractCode(requestModel.getContractCode());
        }
        commonBiz.setBaseEntityModify(upSettleStatement, BaseContextHandler.getUserName());
        tSettleStatementMapper.updateByPrimaryKeySelective(upSettleStatement);
    }

    /**
     * 修改对账单费点
     *
     * @param requestModel
     */
    @Transactional
    public void modifyTaxPoint(TraditionCarrierModifyTaxPointRequestModel requestModel) {
        //判断是否有操作权限
        this.checkModifyPermission(requestModel.getSettleStatementId(), CommonConstant.INTEGER_THREE, null);

        //更新对账单
        TSettleStatement upSettleStatement = new TSettleStatement();
        upSettleStatement.setId(requestModel.getSettleStatementId());
        upSettleStatement.setFreightTaxPoint(requestModel.getFreightTaxPoint());
        commonBiz.setBaseEntityModify(upSettleStatement, BaseContextHandler.getUserName());
        tSettleStatementMapper.updateByPrimaryKeySelective(upSettleStatement);
    }

    /**
     * 修改对账单名称
     *
     * @param requestModel
     */
    @Transactional
    public void renameStatement(EditTraditionStatementNameRequestModel requestModel) {
        //判断是否有操作权限
        this.checkModifyPermission(requestModel.getSettleStatementId(), CommonConstant.INTEGER_FOUR, requestModel.getSource());

        //更新对账单
        TSettleStatement upSettleStatement = new TSettleStatement();
        upSettleStatement.setId(requestModel.getSettleStatementId());
        upSettleStatement.setSettleStatementName(requestModel.getSettleStatementName());
        commonBiz.setBaseEntityModify(upSettleStatement, BaseContextHandler.getUserName());
        tSettleStatementMapper.updateByPrimaryKeySelective(upSettleStatement);
    }

    /**
     * 差异调整-回显
     *
     * @param requestModel
     * @return
     */
    public TraditionCarrierAdjustCostResponseModel queryAdjustCost(TraditionCarrierQueryAdjustCostRequestModel requestModel) {
        //查询对账单是否存在
        TSettleStatement tSettleStatement = tSettleStatementMapper.selectByPrimaryKey(requestModel.getSettleStatementId());
        if (tSettleStatement == null || IfValidEnum.INVALID.getKey().equals(tSettleStatement.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }
        //自营业务
        if (!SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey().equals(tSettleStatement.getSettleStatementType())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }

        //判断对账单状态(待业务审核，待财务审核)
        if (!SettleStatementStatusEnum.WAIT_BUSINESS_AUDIT.getKey().equals(tSettleStatement.getSettleStatementStatus())
                && !SettleStatementStatusEnum.WAIT_FINANCIAL_AUDIT.getKey().equals(tSettleStatement.getSettleStatementStatus())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_CANNOT_EDIT_ADJUST);
        }
        return MapperUtils.mapper(tSettleStatementMapper.queryAdjustCost(requestModel.getSettleStatementId()), TraditionCarrierAdjustCostResponseModel.class);
    }

    /**
     * 差异调整-发起
     *
     * @param requestModel
     */
    @Transactional
    public void adjustCost(TraditionCarrierAdjustRequestModel requestModel) {
        //查询差异调整回显信息
        TraditionCarrierQueryAdjustCostRequestModel queryAdjustCostRequestModel = new TraditionCarrierQueryAdjustCostRequestModel();
        queryAdjustCostRequestModel.setSettleStatementId(requestModel.getSettleStatementId());
        TraditionCarrierAdjustCostResponseModel detail = queryAdjustCost(queryAdjustCostRequestModel);

        //如果调整后对账单费用小于等于0，则不允许调整
        BigDecimal adjustFee;
        if (CommonConstant.TWO.equals(requestModel.getAdjustCostSymbol())) {
            adjustFee = requestModel.getAdjustCost().negate();
        } else {
            adjustFee = requestModel.getAdjustCost();
        }
        //计算调整后的对账费用
        //车主费用
        BigDecimal carrierFreight = detail.getCarrierFreight().setScale(2, RoundingMode.HALF_UP);
        //费额合计
        //申请费用总额
        BigDecimal applyFeeTotal = getFreightTotal(detail.getFreightTaxPoint(), carrierFreight);
        //对账费用
        BigDecimal reconciliationFee = (applyFeeTotal.add(adjustFee).setScale(2, RoundingMode.HALF_UP));

        //如果调整后对账单费用小于等于0，则不允许调整
        if (reconciliationFee.compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.CREATE_SETTLE_APPLY_FEE_ZERO);
        }

        //更新对账单
        TSettleStatement upSettleStatement = new TSettleStatement();
        upSettleStatement.setId(requestModel.getSettleStatementId());
        upSettleStatement.setAdjustFee(adjustFee);
        upSettleStatement.setAdjustRemark(requestModel.getAdjustReason());
        commonBiz.setBaseEntityModify(upSettleStatement, BaseContextHandler.getUserName());
        tSettleStatementMapper.updateByPrimaryKeySelective(upSettleStatement);
    }

    /**
     * 申请开票
     * @param requestModel
     */
    @Transactional
    @DistributedLock(prefix = CommonConstant.TMS_APPLY_INVOICING_LOCK,
            keys = "#requestModel.settleStatementIdList",
            waitTime = 3)
    public void applyInvoicing(TraditionSettleStatementApplyInvoicingRequestModel requestModel) {
        //查询对账单信息
        List<CarrierSettleStatementDetailModel> settleStatementDetailList = tSettleStatementMapper.getDetailByIds(StringUtils.listToString(requestModel.getSettleStatementIdList(),','), SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        if (ListUtils.isEmpty(settleStatementDetailList)){
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }
        //条件判断
        Set<Long> companyCarrierIdSet = new HashSet<>();
        for (CarrierSettleStatementDetailModel model : settleStatementDetailList) {
            //非已对账，不能操作
            if (!SettleStatementStatusEnum.ACCOUNT_CHECKED.getKey().equals(model.getSettleStatementStatus())) {
                throw new BizException(CarrierDataExceptionEnum.APPLY_INVOICING_STATE_ERROR);
            }
            //非未关联发票
            if (CommonConstant.INTEGER_ONE.equals(model.getIfInvoice())){
                throw new BizException(CarrierDataExceptionEnum.APPLY_INVOICING_STATE_ERROR);
            }

            model.getSettleStatementItemList().forEach(item -> companyCarrierIdSet.add(item.getCompanyCarrierId()));
        }
        //只有同一车主的对账单才能进行开票操作
        if (companyCarrierIdSet.size() > CommonConstant.INTEGER_ONE) {
            throw new BizException(CarrierDataExceptionEnum.APPLY_INVOICING_STATE_ERROR);
        }

        CarrierSettleStatementOrderDetailModel orderDetailModel = settleStatementDetailList.get(CommonConstant.INTEGER_ZERO).getSettleStatementItemList().get(CommonConstant.INTEGER_ZERO);

        //生成开票数据
        TInvoicingManagement addInvoicingManagement = new TInvoicingManagement();
        addInvoicingManagement.setBusinessName(requestModel.getBusinessName());
        addInvoicingManagement.setInvoicingMonth(requestModel.getInvoicingMonth());
        addInvoicingManagement.setBusinessType(SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey());
        addInvoicingManagement.setCompanyCarrierId(orderDetailModel.getCompanyCarrierId());
        addInvoicingManagement.setCompanyCarrierName(orderDetailModel.getCompanyCarrierName());
        addInvoicingManagement.setContactName(orderDetailModel.getCarrierContactName());
        addInvoicingManagement.setContactPhone(orderDetailModel.getCarrierContactMobile());
        addInvoicingManagement.setCompanyCarrierType(orderDetailModel.getCompanyCarrierType());
        addInvoicingManagement.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityAdd(addInvoicingManagement, BaseContextHandler.getUserName());
        tInvoicingManagementMapper.insertSelectiveEncrypt(addInvoicingManagement);

        //生成发票管理-对账单信息
        TInvoicingSettleStatement addInvoicingSettleStatement;
        List<TInvoicingSettleStatement> addInvoicingSettleStatementList = new ArrayList<>();
        TSettleStatement upSettleStatement;
        List<TSettleStatement> upSettleStatementList = new ArrayList<>();
        for (CarrierSettleStatementDetailModel model : settleStatementDetailList) {
            //组装对账单信息
            addInvoicingSettleStatement = new TInvoicingSettleStatement();
            addInvoicingSettleStatement.setInvoicingId(addInvoicingManagement.getId());
            addInvoicingSettleStatement.setSettleStatementId(model.getSettleStatementId());
            commonBiz.setBaseEntityAdd(addInvoicingSettleStatement, BaseContextHandler.getUserName());
            addInvoicingSettleStatementList.add(addInvoicingSettleStatement);

            //更新对账单【关联开票】字段
            upSettleStatement = new TSettleStatement();
            upSettleStatement.setId(model.getSettleStatementId());
            upSettleStatement.setIfInvoice(CommonConstant.INTEGER_ONE);
            commonBiz.setBaseEntityModify(upSettleStatement, BaseContextHandler.getUserName());
            upSettleStatementList.add(upSettleStatement);
        }
        //新增发票管理-对账单信息
        if (ListUtils.isNotEmpty(addInvoicingSettleStatementList)){
            tInvoicingSettleStatementMapper.batchInsert(addInvoicingSettleStatementList);
        }

        //更新对账单【关联开票】字段
        if (ListUtils.isNotEmpty(upSettleStatementList)){
            tSettleStatementMapper.batchUpdateSelective(upSettleStatementList);
        }
    }

    /**
     * 计算费额合计
     *
     * @param taxPoint 费点
     * @param freight  金额
     * @return
     */
    private BigDecimal getFreightTotal(BigDecimal taxPoint, BigDecimal freight) {
        return freight.add(freight.multiply(taxPoint).divide(CommonConstant.BIG_DECIMAL_ONE_HUNDRED, CommonConstant.INTEGER_TWO, RoundingMode.HALF_UP));
    }

    /**
     * 撤销对账单
     *
     * @param requestModel
     */
    @Transactional
    public void cancel(TraditionStatementCancelRequestModel requestModel) {
        //查询对账单是否存在
        TSettleStatement dbSettleStatement = tSettleStatementMapper.selectByPrimaryKey(requestModel.getSettleStatementId());
        if (dbSettleStatement == null || IfValidEnum.INVALID.getKey().equals(dbSettleStatement.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }
        //自营业务
        if (!SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey().equals(dbSettleStatement.getSettleStatementType())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }

        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {//前台
            //判断对账单是否是该车主的
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            TSettleStatementItem tSettleStatementItem = tSettleStatementItemMapper.getTopBySettleStatementId(dbSettleStatement.getId());
            if (tSettleStatementItem == null || IfValidEnum.INVALID.getKey().equals(tSettleStatementItem.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
            }
            if (!companyCarrierId.equals(tSettleStatementItem.getCompanyCarrierId())) {
                throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
            }
        }

        //判断对账单状态(仅已驳回,待提交)
        if (!SettleStatementStatusEnum.REJECT.getKey().equals(dbSettleStatement.getSettleStatementStatus())
                && !SettleStatementStatusEnum.WAIT_SUBMITTED.getKey().equals(dbSettleStatement.getSettleStatementStatus())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
        }

        //更新对账单状态
        TSettleStatement tSettleStatement = new TSettleStatement();
        tSettleStatement.setId(requestModel.getSettleStatementId());
        tSettleStatement.setSettleStatementStatus(SettleStatementStatusEnum.CANCEL.getKey());
        tSettleStatement.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityModify(tSettleStatement, BaseContextHandler.getUserName());
        tSettleStatementMapper.updateByPrimaryKeySelective(tSettleStatement);

        // 更新运单状态为未关联对账单
        List<Long> carrierOrderIdBySettleStatementIds = tSettleStatementItemMapper.getCarrierOrderIdBySettleStatementId(requestModel.getSettleStatementId());
        if (ListUtils.isNotEmpty(carrierOrderIdBySettleStatementIds)) {
            carrierOrderCommonBiz.updateSettleStatementCarrierOrderStatus(carrierOrderIdBySettleStatementIds, CarrierSettleStatementStatusEnum.NOT_RELATED);
        }
    }

    /**
     * 对账单归档列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<TraditionStatementArchiveListResponseModel> statementArchiveList(TraditionStatementArchiveListRequestModel requestModel) {
        requestModel.enablePaging();
        List<StatementArchiveListResponseModel> responseModeList = tSettleStatementItemMapper.statementArchiveList(requestModel.getSettleStatementId());
        PageInfo statementArchiveListResponseModelPageInfo = new PageInfo(responseModeList);
        statementArchiveListResponseModelPageInfo.setList(MapperUtils.mapper(statementArchiveListResponseModelPageInfo.getList(), TraditionStatementArchiveListResponseModel.class));
        return statementArchiveListResponseModelPageInfo;
    }

    /**
     * 对账单归档/编辑
     *
     * @param requestModel
     */
    @Transactional
    public void statementArchive(TraditionStatementArchiveRequestModel requestModel) {
        //查询要归档的对账单运单item
        TSettleStatementItem tSettleStatementItem = tSettleStatementItemMapper.selectByPrimaryKey(requestModel.getSettleStatementItemId());
        if (tSettleStatementItem == null || IfValidEnum.INVALID.getKey().equals(tSettleStatementItem.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //赋值归档操作人 操作时间
        tSettleStatementItem.setArchiveRemark(requestModel.getArchiveRemark());
        tSettleStatementItem.setArchiveBy(BaseContextHandler.getUserName());
        tSettleStatementItem.setArchiveTime(new Date());

        //查询对账单
        TSettleStatement tSettleStatement = tSettleStatementMapper.selectByPrimaryKey(tSettleStatementItem.getSettleStatementId());
        if (tSettleStatement == null || IfValidEnum.INVALID.getKey().equals(tSettleStatement.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }
        //自营业务
        if (!SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey().equals(tSettleStatement.getSettleStatementType())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }
        //已对账才能操作
        if (!SettleStatementStatusEnum.ACCOUNT_CHECKED.getKey().equals(tSettleStatement.getSettleStatementStatus())) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_IS_NOT_ACCOUNT_CHECKED);
        }

        if (CommonConstant.INTEGER_ONE.equals(requestModel.getOperateType())) {
            /*归档操作*/
            //已归档之后不能再归档
            if (CommonConstant.INTEGER_ONE.equals(tSettleStatementItem.getIfArchive())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_ARCHIVE);
            }
            //更新对账单运单item归档状态
            tSettleStatementItem.setIfArchive(CommonConstant.INTEGER_ONE);
            commonBiz.setBaseEntityModify(tSettleStatementItem, BaseContextHandler.getUserName());
            tSettleStatementItemMapper.updateByPrimaryKeySelective(tSettleStatementItem);
        } else if (CommonConstant.INTEGER_TWO.equals(requestModel.getOperateType())) {
            /*编辑操作*/
            //归档后才能编辑
            if (!CommonConstant.INTEGER_ONE.equals(tSettleStatementItem.getIfArchive())) {
                throw new BizException(CarrierDataExceptionEnum.ARCHIVED_RECORD_NO_OPERATION_ALLOWED);
            }
            //更新对账单运单归档原因
            commonBiz.setBaseEntityModify(tSettleStatementItem, BaseContextHandler.getUserName());
            tSettleStatementItemMapper.updateByPrimaryKeySelective(tSettleStatementItem);

            //清除存在的凭证
            tSettleStatementArchiveAttachmentMapper.delBySettleStatementItemId(tSettleStatementItem.getId());
        }

        //插入凭证
        TSettleStatementArchiveAttachment tSettleStatementArchiveAttachment;
        List<TSettleStatementArchiveAttachment> tSettleStatementArchiveAttachmentList = new ArrayList<>();
        for (String picPath : requestModel.getArchiveTicketList()) {
            tSettleStatementArchiveAttachment = new TSettleStatementArchiveAttachment();
            tSettleStatementArchiveAttachment.setSettleStatementItemId(tSettleStatementItem.getId());
            tSettleStatementArchiveAttachment.setImagePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CARRIER_ORDER_SETTLE_STATEMENT_ARCHIVE_TICKET.getKey(), tSettleStatementItem.getCarrierOrderCode(), picPath, null));
            commonBiz.setBaseEntityAdd(tSettleStatementArchiveAttachment, BaseContextHandler.getUserName());
            tSettleStatementArchiveAttachmentList.add(tSettleStatementArchiveAttachment);
        }
        //插入归档图片
        tSettleStatementArchiveAttachmentMapper.batchInsertSelective(tSettleStatementArchiveAttachmentList);
    }

    /**
     * 查看归档图片
     *
     * @param requestModel
     * @return
     */
    public List<String> archiveTicketList(TraditionStatementArchiveTicketListRequestModel requestModel) {
        return tSettleStatementArchiveAttachmentMapper.archiveTicketPathByItemId(requestModel.getSettleStatementItemId());
    }

    /**
     * 查看归档详情
     *
     * @param requestModel
     * @return
     */
    public TraditionStatementArchiveDetailResponseModel statementArchiveDetail(TraditionStatementArchiveDetailRequestModel requestModel) {
        return MapperUtils.mapper(tSettleStatementItemMapper.statementArchiveDetail(requestModel.getSettleStatementItemId()), TraditionStatementArchiveDetailResponseModel.class);
    }

    /**
     * 查询对账单下待归档运单
     *
     * @param requestModel
     * @return
     */
    public List<TraditionStatementWaitArchiveListResponseModel> statementWaitArchiveList(TraditionStatementWaitArchiveListRequestModel requestModel) {
        return MapperUtils.mapper(tSettleStatementItemMapper.statementWaitArchiveList(MapperUtils.mapperNoDefault(requestModel, StatementWaitArchiveListRequestModel.class)), TraditionStatementWaitArchiveListResponseModel.class);
    }

    /**
     * 对账单详情-查询添加运单列表
     *
     * @param requestModel 请求Model
     * @return 添加运单列表
     */
    public PageInfo<TraditionWaitSettleStatementListResponseModel> addCarrierOrderList(TraditionAddCarrierOrderListRequestModel requestModel) {
        TraditionWaitSettleStatementListRequestModel requestModelMScope =
                MapperUtils.mapper(requestModel, TraditionWaitSettleStatementListRequestModel.class);
        Optional.ofNullable(this.getLoginUserCompanyCarrierId(requestModel.getSource()))
                        .ifPresent(requestModelMScope::setCompanyCarrierId);
        //查询对账单
        TSettleStatement tSettleStatement = this.getSettleStatement(requestModel.getSettleStatementId());
        //对账单状态判断
        checkTraditionSettleStatementAddCarrierOrder(requestModel.getSource(), tSettleStatement.getSettleStatementStatus());

        requestModelMScope.setVehicleNo(requestModel.getVehicleNumber());
        return waitSettleStatementList(requestModelMScope);
    }

    /**
     * 校验对账单添加运单对账单状态
     *
     * @param source                请求来源:1 后台，2 前台
     * @param settleStatementStatus 对账单状态
     */
    private void checkTraditionSettleStatementAddCarrierOrder(Integer source, Integer settleStatementStatus) {
        //判断对账单状态
        if (CommonConstant.INTEGER_TWO.equals(source)) {
            //前台仅【待提交】/【已驳回】
            if (!(SettleStatementStatusEnum.WAIT_SUBMITTED.getKey().equals(settleStatementStatus) ||
                    SettleStatementStatusEnum.REJECT.getKey().equals(settleStatementStatus))) {
                throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
            }
        } else {
            //后台仅提交/审核/驳回
            if (!(SettleStatementStatusEnum.WAIT_BUSINESS_AUDIT.getKey().equals(settleStatementStatus) ||
                    SettleStatementStatusEnum.WAIT_FINANCIAL_AUDIT.getKey().equals(settleStatementStatus) ||
                    SettleStatementStatusEnum.WAIT_SUBMITTED.getKey().equals(settleStatementStatus) ||
                    SettleStatementStatusEnum.REJECT.getKey().equals(settleStatementStatus))) {
                throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
            }
        }
    }

    /**
     * 对账单详情-添加运单
     *
     * @param requestModel 对账单id,运单筛选条件
     */
    @Transactional
    public void addCarrierOrderConfirm(TraditionAddCarrierOrderConfirmRequestModel requestModel) {
        Long loginUserCompanyCarrierId = this.getLoginUserCompanyCarrierId(requestModel.getSource());

        //查询对账单是否存在
        TSettleStatement tSettleStatement = this.getSettleStatement(requestModel.getSettleStatementId());
        //对账单状态判断
        checkTraditionSettleStatementAddCarrierOrder(requestModel.getSource(), tSettleStatement.getSettleStatementStatus());

        //查询对账单下运单信息
        List<TSettleStatementItem> tSettleStatementItemList = tSettleStatementItemMapper.selectByStatementId(tSettleStatement.getId(), null, loginUserCompanyCarrierId);
        if (ListUtils.isEmpty(tSettleStatementItemList)) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
        }

        //查询添加的运单信息
        Long companyCarrierId = tSettleStatementItemList.get(CommonConstant.INTEGER_ZERO).getCompanyCarrierId();
        requestModel.setCompanyCarrierId(companyCarrierId);
        if (StringUtils.isBlank(requestModel.getCarrierOrderIds())) {//没有勾选，则根据筛选条件查询运单
            TraditionWaitSettleStatementListRequestModel requestModel1 = MapperUtils.mapper(requestModel, TraditionWaitSettleStatementListRequestModel.class);
            requestModel1.setVehicleNo(requestModel.getVehicleNumber());
            requestModel1.setDriver(requestModel.getDriverName());
            List<Long> carrierOrderIdList = tCarrierOrderMapper.selectCarrierTraditionWaitSettleStatementListIds(requestModel1);
            if (ListUtils.isEmpty(carrierOrderIdList) ||
                    (ListUtils.isNotEmpty(requestModel1.getCarrierOrderCodeList())
                            && requestModel1.getCarrierOrderCodeList().size() != carrierOrderIdList.size())) {
                throw new BizException(CarrierDataExceptionEnum.CHOOSE_TRADITION_SETTLE_STATEMENT_CARRIER_ORDER);
            }
            requestModel.setCarrierOrderIds(StringUtils.join(carrierOrderIdList, ','));
        }

        //最多对5000条数据生成对账单
        List<WaitSettleStatementCarrierOrderModel> tCarrierOrders = tCarrierOrderMapper.selectCarrierWaitSettleStatementCarrierOrdersByIds(requestModel.getCarrierOrderIds(), companyCarrierId);
        if (ListUtils.isEmpty(tCarrierOrders)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //最多3000条运单生成一个对账单
        if (tSettleStatementItemList.size() + tCarrierOrders.size() > CommonConstant.INTEGER_FIVE_THOUSAND_HUNDRED) {
            throw new BizException(CarrierDataExceptionEnum.APPLY_SETTLE_STATEMENT_MAX);
        }

        //生成对账单条目信息
        List<Long> carrierOrderIdList = new ArrayList<>();
        Set<Long> companyCarrierIdSet = new HashSet<>();
        for (WaitSettleStatementCarrierOrderModel tCarrierOrder : tCarrierOrders) {
            companyCarrierIdSet.add(tCarrierOrder.getCompanyCarrierId());//车主id
            carrierOrderIdList.add(tCarrierOrder.getCarrierOrderId());//运单id

            /*结算信息*/
            if (tCarrierOrder.getDbSettlementCost() != null) {
                tCarrierOrder.setSettlementAmount(tCarrierOrder.getDbSettlementAmount());
                tCarrierOrder.setSettlementCost(tCarrierOrder.getDbSettlementCost());
            } else {
                //结算信息
                setCarrierOrderSettlementInfo(tCarrierOrder);
            }
        }

        //只有同一车主的运单才能进行对账操作
        if (companyCarrierIdSet.size() > CommonConstant.INTEGER_ONE) {
            throw new BizException(CarrierDataExceptionEnum.SETTLEMENT_STATEMENT_PLATFORM_OR_COMPANY_DIFFERENT);
        }

        //生成对账单条目
        List<TSettleStatementItem> settleStatementItemList = new ArrayList<>();
        for (WaitSettleStatementCarrierOrderModel tCarrierOrder : tCarrierOrders) {
            TSettleStatementItem tSettleStatementItem = setSettleStatementItemEntity(tCarrierOrder);
            tSettleStatementItem.setSettleStatementId(tSettleStatement.getId());
            settleStatementItemList.add(tSettleStatementItem);
        }
        //批量插入对账单运单信息
        if (ListUtils.isNotEmpty(settleStatementItemList)) {
            batchInsertSettleStatementItem(settleStatementItemList);
        }

        // 更新运单对账单状态
        if (ListUtils.isNotEmpty(carrierOrderIdList)) {
            CarrierSettleStatementStatusEnum statusEnum =
                    CarrierSettleStatementStatusEnum.getEnumByKey(tSettleStatement.getSettleStatementStatus())
                            .orElse(CarrierSettleStatementStatusEnum.NOT_RELATED);
            carrierOrderCommonBiz.updateSettleStatementCarrierOrderStatus(carrierOrderIdList, statusEnum);
        }
    }

    /**
     * 对账单详情-提交对账单
     *
     * @param requestModel 对账单id
     */
    @Transactional
    public void submitSettleStatement(CarrierTraditionStatementIdRequestModel requestModel) {

        Long loginUserCompanyCarrierId = this.getLoginUserCompanyCarrierId(requestModel.getSource());

        //查询对账单是否存在
        TSettleStatement tSettleStatement = this.getSettleStatement(requestModel.getSettleStatementId());
        //不是以驳回或待提交状态不能操作
        if (!(SettleStatementStatusEnum.REJECT.getKey().equals(tSettleStatement.getSettleStatementStatus())
                || SettleStatementStatusEnum.WAIT_SUBMITTED.getKey().equals(tSettleStatement.getSettleStatementStatus()))) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
        }
        //对账单运单非空判断
        List<TSettleStatementItem> settleStatementItemList = tSettleStatementItemMapper.selectByStatementId(tSettleStatement.getId(), null, loginUserCompanyCarrierId);
        if (ListUtils.isEmpty(settleStatementItemList)) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
        }
        List<Long> carrierOrderIdList = new ArrayList<>();
        BigDecimal freightTotal = BigDecimal.ZERO.add(Optional.ofNullable(tSettleStatement.getAdjustFee()).orElse(BigDecimal.ZERO));
        for (TSettleStatementItem itemModel : settleStatementItemList) {
            carrierOrderIdList.add(itemModel.getCarrierOrderId());

            //单条运单运费费额合计
            BigDecimal carrierFreight = itemModel.getEntrustFreight();
            carrierFreight = getFreightTotal(tSettleStatement.getFreightTaxPoint(), carrierFreight);
            //单条运单临时费用费额合计
            BigDecimal otherFees = itemModel.getOtherFees();
            if (otherFees.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                otherFees = getFreightTotal(tSettleStatement.getOtherFeeTaxPoint(), otherFees);
            }

            freightTotal = freightTotal.add(carrierFreight.add(otherFees));
        }

        //无对账费用，不能操作
        if (freightTotal.compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.CREATE_SETTLE_APPLY_FEE_ZERO);
        }

        //更新对账单状态
        tSettleStatement.setSettleStatementStatus(SettleStatementStatusEnum.WAIT_BUSINESS_AUDIT.getKey());
        tSettleStatement.setAuditName(CommonConstant.BLANK_TEXT);
        tSettleStatement.setAuditTime(null);
        tSettleStatement.setRemark(CommonConstant.BLANK_TEXT);
        commonBiz.setBaseEntityModify(tSettleStatement, BaseContextHandler.getUserName());
        tSettleStatementMapper.updateByPrimaryKey(tSettleStatement);

        //更新运单对账单状态
        if (ListUtils.isNotEmpty(carrierOrderIdList)) {
            carrierOrderCommonBiz.updateSettleStatementCarrierOrderStatus(carrierOrderIdList, CarrierSettleStatementStatusEnum.WAIT_BUSINESS_AUDIT);
        }
    }

    /**
     * 对账单详情-撤销运单
     *
     * @param requestModel 对账单运单id
     */
    @Transactional
    public void cancelCarrierOrder(TraditionUndoCarrierOrderRequestModel requestModel) {
        Long loginUserCompanyCarrierId = this.getLoginUserCompanyCarrierId(requestModel.getSource());
        //查询选择的运单信息是否存在
        List<TSettleStatementItem> settleStatementItemList = tSettleStatementItemMapper.selectByStatementItemIds(requestModel.getSettleStatementItemIds(), loginUserCompanyCarrierId);
        if (ListUtils.isEmpty(settleStatementItemList)) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST);
        }

        //判断是否都属于一个对账单下
        Set<Long> settleStatementIdSet = new HashSet<>();
        for (TSettleStatementItem settleStatementItemModel : settleStatementItemList) {
            settleStatementIdSet.add(settleStatementItemModel.getSettleStatementId());
        }
        if (settleStatementIdSet.size() > CommonConstant.INTEGER_ONE) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_STATE_ERROR);
        }

        //取其中一个运单信息
        TSettleStatementItem settleStatementItemOne = settleStatementItemList.get(CommonConstant.INTEGER_ZERO);
        //查询对账单是否存在
        TSettleStatement tSettleStatement = this.getSettleStatement(settleStatementItemOne.getSettleStatementId());
        //对账单状态判断
        checkTraditionSettleStatementAddCarrierOrder(requestModel.getSource(), tSettleStatement.getSettleStatementStatus());

        //不能把对账单下所有运单都撤销（最少保留一个）
        List<TSettleStatementItem> dbItemList = tSettleStatementItemMapper.selectByStatementId(tSettleStatement.getId(), null, null);
        List<Long> dbItemIdList = dbItemList.stream().map(TSettleStatementItem::getId).collect(Collectors.toList());
        List<Long> requestItemIdList = settleStatementItemList.stream().map(TSettleStatementItem::getId).collect(Collectors.toList());
        dbItemIdList.removeAll(requestItemIdList);
        if (ListUtils.isEmpty(dbItemIdList)) {
            throw new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_CARRIER_ORDER_EMPTY);
        }

        //运单ID集合
        List<Long> tSettleStatementItemsIdUpList = new ArrayList<>();
        //删除对账单下运单
        TSettleStatementItem settleStatementItem;
        //对账单明细集合
        List<TSettleStatementItem> tSettleStatementItemsUpList = new ArrayList<>();
        BigDecimal totalEntrustFreight = CommonConstant.BIG_DECIMAL_ZERO;
        for (TSettleStatementItem tSettleStatementDetail : settleStatementItemList) {
            //加入运单ID
            tSettleStatementItemsIdUpList.add(tSettleStatementDetail.getCarrierOrderId());
            totalEntrustFreight = totalEntrustFreight.add(tSettleStatementDetail.getEntrustFreight());
            //加入对账单明细
            settleStatementItem = new TSettleStatementItem();
            settleStatementItem.setId(tSettleStatementDetail.getId());
            settleStatementItem.setValid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(settleStatementItem, BaseContextHandler.getUserName());
            tSettleStatementItemsUpList.add(settleStatementItem);
        }
        //更新待对账单明细
        tSettleStatementItemMapper.batchUpdate(tSettleStatementItemsUpList);

        //更新运单
        if (ListUtils.isNotEmpty(tSettleStatementItemsIdUpList)) {
            carrierOrderCommonBiz.updateSettleStatementCarrierOrderStatus(tSettleStatementItemsIdUpList, CarrierSettleStatementStatusEnum.NOT_RELATED);
        }
    }

    // 查询对账单是否存在
    private TSettleStatement getSettleStatement(Long settleStatementId) {
        return Optional.ofNullable(tSettleStatementMapper.selectByPrimaryKey(settleStatementId))
                .filter(f -> IfValidEnum.VALID.getKey().equals(f.getValid()))
                .filter(f -> SettleStatementTypeEnum.TRADITIONAL_BUSINESS.getKey().equals(f.getSettleStatementType()))
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.SETTLE_STATEMENT_NOT_EXIST));
    }

    private Long getLoginUserCompanyCarrierId(Integer source) {
        Long loginUserCompanyCarrierId = null;
        if (CommonConstant.INTEGER_TWO.equals(source)) {
            loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (Objects.equals(loginUserCompanyCarrierId, CommonConstant.LONG_ZERO)) {
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
        }
        return loginUserCompanyCarrierId;
    }
}
