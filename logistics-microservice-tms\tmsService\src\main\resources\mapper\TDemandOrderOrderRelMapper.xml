<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderOrderRelMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDemandOrderOrderRel" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="order_code" property="orderCode" jdbcType="VARCHAR" />
    <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
    <result column="arranged_amount" property="arrangedAmount" jdbcType="DECIMAL" />
    <result column="back_amount" property="backAmount" jdbcType="DECIMAL" />
    <result column="rel_type" property="relType" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, demand_order_id, order_id, order_code, total_amount, arranged_amount, back_amount, 
    rel_type, remark, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_demand_order_order_rel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_demand_order_order_rel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDemandOrderOrderRel" >
    insert into t_demand_order_order_rel (id, demand_order_id, order_id, 
      order_code, total_amount, arranged_amount, 
      back_amount, rel_type, remark, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{demandOrderId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, 
      #{orderCode,jdbcType=VARCHAR}, #{totalAmount,jdbcType=DECIMAL}, #{arrangedAmount,jdbcType=DECIMAL}, 
      #{backAmount,jdbcType=DECIMAL}, #{relType,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDemandOrderOrderRel"  useGeneratedKeys="true" keyProperty="id">
    insert into t_demand_order_order_rel
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="demandOrderId != null" >
        demand_order_id,
      </if>
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="orderCode != null" >
        order_code,
      </if>
      <if test="totalAmount != null" >
        total_amount,
      </if>
      <if test="arrangedAmount != null" >
        arranged_amount,
      </if>
      <if test="backAmount != null" >
        back_amount,
      </if>
      <if test="relType != null" >
        rel_type,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="demandOrderId != null" >
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderCode != null" >
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null" >
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="arrangedAmount != null" >
        #{arrangedAmount,jdbcType=DECIMAL},
      </if>
      <if test="backAmount != null" >
        #{backAmount,jdbcType=DECIMAL},
      </if>
      <if test="relType != null" >
        #{relType,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDemandOrderOrderRel" >
    update t_demand_order_order_rel
    <set >
      <if test="demandOrderId != null" >
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderCode != null" >
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null" >
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="arrangedAmount != null" >
        arranged_amount = #{arrangedAmount,jdbcType=DECIMAL},
      </if>
      <if test="backAmount != null" >
        back_amount = #{backAmount,jdbcType=DECIMAL},
      </if>
      <if test="relType != null" >
        rel_type = #{relType,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDemandOrderOrderRel" >
    update t_demand_order_order_rel
    set demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      order_code = #{orderCode,jdbcType=VARCHAR},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      arranged_amount = #{arrangedAmount,jdbcType=DECIMAL},
      back_amount = #{backAmount,jdbcType=DECIMAL},
      rel_type = #{relType,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>