package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CopyCarrierRecycleOrderByDispatchVehicleModel {
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("预提数")
    private Integer loadAmount;

    @ApiModelProperty("订单集合")
    private List<CopyCarrierRecycleOrderOrderRelByDispatchVehicleModel> orderRelList;

    @ApiModelProperty("卸货仓库")
    private String warehouseName;
}
