package com.logistics.management.webapi.controller.settlestatement.tradition.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.settlestatement.tradition.response.TraditionWaitSettleStatementListResponseModel;
import com.logistics.management.webapi.controller.settlestatement.tradition.response.TraditionAddCarrierOrderListResponseDto;
import com.logistics.tms.api.feign.dispatchorder.model.CarrierOrderGoodsModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.RoundingMode;
import java.util.List;

public class TraditionAddCarrierOrderListMapping extends MapperMapping<TraditionWaitSettleStatementListResponseModel, TraditionAddCarrierOrderListResponseDto> {
    @Override
    public void configure() {
        TraditionWaitSettleStatementListResponseModel source = getSource();
        TraditionAddCarrierOrderListResponseDto destination = getDestination();

        //车主名处理
        if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())) {
            destination.setCompanyCarrierName(source.getCarrierContactName() + " " + FrequentMethodUtils.encryptionData(source.getCarrierContactMobile(), EncodeTypeEnum.MOBILE_PHONE));
        }

        //拼接货物名
        StringBuilder goodsNameSB = new StringBuilder();
        List<CarrierOrderGoodsModel> goodsList = source.getGoodsList();
        for (int i = 0; i < goodsList.size(); i++) {
            if (i > CommonConstant.INTEGER_ZERO) {
                goodsNameSB.append(CommonConstant.SLASH);
            }
            goodsNameSB.append(goodsList.get(i).getGoodsName());
        }
        destination.setGoodsName(goodsNameSB.toString());

        //提货地址
        String loadAddress = StringUtils.defaultString(source.getLoadProvinceName()) +
				StringUtils.defaultString(source.getLoadCityName()) +
				StringUtils.defaultString(source.getLoadAreaName()) +
				StringUtils.defaultString(source.getLoadDetailAddress());
        destination.setLoadAddress(loadAddress);

        //卸货地址

        String unloadAddress = StringUtils.defaultString(source.getUnloadProvinceName()) +
				StringUtils.defaultString(source.getUnloadCityName()) +
				StringUtils.defaultString(source.getUnloadAreaName()) +
				StringUtils.defaultString(source.getUnloadDetailAddress());
        destination.setUnloadAddress(unloadAddress);
        //结算费用
        destination.setSettlementCost(source.getCarrierFee().setScale(2, RoundingMode.HALF_UP).toPlainString());

        //提货时间
        destination.setLoadTime(source.getLoadTime() != null ? DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(source.getLoadTime()) : CommonConstant.BLANK_TEXT);
        //签收时间
        destination.setSignTime(source.getSignTime() != null ? DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(source.getSignTime()) : CommonConstant.BLANK_TEXT);
    }
}
