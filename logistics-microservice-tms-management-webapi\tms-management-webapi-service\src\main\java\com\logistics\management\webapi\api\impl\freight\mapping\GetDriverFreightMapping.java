package com.logistics.management.webapi.api.impl.freight.mapping;

import com.logistics.management.webapi.api.feign.freight.dto.DriverFreightByDemandOrderIdsAndVehicleResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.freight.model.DriverFreightByDemandOrderIdsAndVehicleResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @Author: sj
 * @Date: 2020/2/5 19:50
 */
public class GetDriverFreightMapping extends MapperMapping<DriverFreightByDemandOrderIdsAndVehicleResponseModel,DriverFreightByDemandOrderIdsAndVehicleResponseDto> {
    @Override
    public void configure() {
        DriverFreightByDemandOrderIdsAndVehicleResponseModel model = this.getSource();
        DriverFreightByDemandOrderIdsAndVehicleResponseDto dto = this.getDestination();

        if(model !=null ){
            if(CommonConstant.ZERO.equals(model.getPrice().stripTrailingZeros().toPlainString())){
                dto.setPrice("");
            }else{
                dto.setPrice(model.getPrice().stripTrailingZeros().toPlainString());
            }
            if(CommonConstant.ZERO.equals(model.getMultipleMarkupPrice().stripTrailingZeros().toPlainString())){
                dto.setMultipleMarkupPrice("");
            }else{
                dto.setMultipleMarkupPrice(model.getMultipleMarkupPrice().stripTrailingZeros().toPlainString());
            }
        }

    }
}
