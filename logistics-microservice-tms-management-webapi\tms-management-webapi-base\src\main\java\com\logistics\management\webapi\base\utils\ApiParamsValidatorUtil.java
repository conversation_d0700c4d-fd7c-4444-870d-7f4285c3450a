package com.logistics.management.webapi.base.utils;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;

/**
 * 参数校验工具类
 *
 * <AUTHOR>
 * @date ：Created in 2022/7/12
 */
public class ApiParamsValidatorUtil {

	/**
	 * 验证单价：0<价格<=1000
	 *
	 * @param unitPrice 单价
	 * @return 验证结果: true为验证通过,false为验证不通过
	 */
	public static boolean verifyUnitPrice(String unitPrice) {
		if (StringUtils.isBlank(unitPrice)){
			return false;
		}
		boolean result = true;
		try {
			BigDecimal bigDecimal = new BigDecimal(unitPrice);
			if (bigDecimal.compareTo(CommonConstant.BIG_DECIMAL_ZERO) <= CommonConstant.INTEGER_ZERO ||
					bigDecimal.compareTo(CommonConstant.BIG_DECIMAL_ONE_THOUSAND) > CommonConstant.INTEGER_ZERO) {
				result = false;
			}
		} catch (Exception e) {
			result = false;
		}
		return result;
	}

	/**
	 * 验证一口价：0<价格<=50000
	 *
	 * @param fixedPrice 一口价
	 * @return 验证结果: true为验证通过,false为验证不通过
	 */
	public static boolean verifyFixedPrice(String fixedPrice) {
		if (StringUtils.isBlank(fixedPrice)){
			return false;
		}
		boolean result = true;
		try {
			BigDecimal bigDecimal = new BigDecimal(fixedPrice);
			if (bigDecimal.compareTo(CommonConstant.BIG_DECIMAL_ZERO) <= CommonConstant.INTEGER_ZERO ||
					bigDecimal.compareTo(CommonConstant.BIG_DECIMAL_FIFTY_THOUSAND) > CommonConstant.INTEGER_ZERO) {
				result = false;
			}
		} catch (Exception e) {
			result = false;
		}
		return result;
	}
}
