package com.logistics.management.webapi.controller.region.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/18 10:33
 */
@Data
public class RegionDetailResponseDto {
    @ApiModelProperty("大区ID")
    private String regionId = "";
    @ApiModelProperty("大区名称")
    private String regionName = "";
    @ApiModelProperty("大区负责人")
    private String regionContactName = "";
    @ApiModelProperty("大区负责人手机号")
    private String regionContactPhone = "";
    @ApiModelProperty("省份 集合")
    private List<ProvinceResponseDto> provinceResponseDtoList;

    /**
     * (3.22.0)车主
     */
    @ApiModelProperty("车主")
    private List<RegionCompanyResponseDto> companyCarrierList;
}
