package com.logistics.management.webapi.controller.companyentrust.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/9/27 14:24
 */
@Data
public class SearchCompanyEntrustResponseDto {
    private String companyEntrustId="";
    private String companyName="";
    private String createdTime="";
    private String lastModifiedBy="";
    private String lastModifiedTime="";
    @ApiModelProperty("证件补充")
    private String tradingCertificateIsAmend="";
    @ApiModelProperty("货主类型")
    private String type="";
    private String typeLabel="";
    @ApiModelProperty("来源：1后台添加 2 web注册")
    private String source="";
}
