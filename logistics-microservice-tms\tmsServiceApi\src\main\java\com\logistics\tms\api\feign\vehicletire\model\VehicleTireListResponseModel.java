package com.logistics.tms.api.feign.vehicletire.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class VehicleTireListResponseModel {
    @ApiModelProperty("轮胎管理信息Id")
    private Long vehicleTireId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主 2 外部 3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driveMobile;
    @ApiModelProperty("轮胎牌号列表")
    private List<VehicleTireNoListResponseModel> vehicleTireNoList;
    @ApiModelProperty("未扣费用")
    private String notBuckleCost;
    @ApiModelProperty("结算状态：0 待结算，1 已结算")
    private Integer settlementStatus;
    @ApiModelProperty("轮胎企业")
    private String tireCompany;
    @ApiModelProperty("更换日期")
    private Date replaceDate;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
    private String remark;
}
