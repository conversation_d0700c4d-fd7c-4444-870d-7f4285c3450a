package com.logistics.management.webapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2021/7/20 16:03
 */
@Data
public class GetVehicleTireListByVehicleSettlementIdResponseModel {
    @ApiModelProperty("轮胎id")
    private Long vehicleTireId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("手机号")
    private String driverMobile;
    @ApiModelProperty("更换日期")
    private Date replaceDate;

    @ApiModelProperty("轮胎数据")
    private List<VehicleTireNoModel> tireNoList;

    @ApiModelProperty("司机id")
    private Long staffId;

    @ApiModelProperty("操作时间  排序用")
    private Date lastModifiedTime;
}
