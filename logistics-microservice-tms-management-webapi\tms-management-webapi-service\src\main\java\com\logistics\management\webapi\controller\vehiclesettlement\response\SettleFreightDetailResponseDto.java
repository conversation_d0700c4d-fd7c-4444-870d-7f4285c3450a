package com.logistics.management.webapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:lei.zhu
 * @date:2021/4/9 13:06
 */
@Data
public class SettleFreightDetailResponseDto {
    @ApiModelProperty("车辆运费账单id")
    private String vehicleSettlementId="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("账单月  年-月")
    private String settlementMonth="";
    @ApiModelProperty("状态：0 待对账，1 待发送，2 待确认，3 待处理，4 待结清，5 部分结清，6 已结清")
    private String status="";
    @ApiModelProperty("状态文本")
    private String statusLabel="";

    @ApiModelProperty("月应付运费")
    private String actualExpensesPayable="";
    @ApiModelProperty("未付运费")
    private String notPayMoney="";
}
