package com.logistics.tms.base.utils;

import com.yelo.tray.core.base.enums.BaseExceptionEnum;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ExceptionUtils {

    public static ThrowExceptionFunction isTure(boolean b) {
        return (emptyAction, code, msg) -> {
            if (b) {
                emptyAction.run();
                throw new BizException(code, msg);
            }
        };
    }

    /**
     * 抛异常接口
     **/
    @FunctionalInterface
    public interface ThrowExceptionFunction {

        /**
         * 抛出异常信息
         **/
        void throwMessage(Runnable emptyAction, int code, String msg);

        default void throwMessage(BaseExceptionEnum exception) {
            this.throwMessage(() -> {}, exception);
        }

        default void throwMessage(Runnable emptyAction, BaseExceptionEnum exception) {
            this.throwMessage(emptyAction, exception.getCode(), exception.getMsg());
        }

        default void throwMessage(BaseExceptionEnum exception, String msg) {
            this.throwMessage(() -> {}, exception.getCode(), msg);
        }

        default void throwFormatMessage(BaseExceptionEnum exception, Object... msg) {
            this.throwMessage(() -> {}, exception.getCode(), String.format(exception.getMsg(), msg));
        }
    }
}
