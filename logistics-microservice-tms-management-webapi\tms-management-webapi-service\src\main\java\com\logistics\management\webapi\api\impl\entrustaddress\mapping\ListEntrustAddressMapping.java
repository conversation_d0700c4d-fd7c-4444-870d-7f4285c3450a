package com.logistics.management.webapi.api.impl.entrustaddress.mapping;

import com.logistics.management.webapi.api.feign.entrustaddress.dto.SearchEntrustAddressResponseDto;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.tms.api.feign.entrustaddress.model.SearchEntrustAddressResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2018/12/26 17:30
 */
public class ListEntrustAddressMapping extends MapperMapping<SearchEntrustAddressResponseModel, SearchEntrustAddressResponseDto> {
    @Override
    public void configure() {
        SearchEntrustAddressResponseModel model = getSource();
        SearchEntrustAddressResponseDto dto = getDestination();
        if (model != null){
            dto.setAddress(model.getProvinceName()+model.getCityName()+model.getAreaName()+model.getDetailAddress());
            dto.setTypeLabel(CompanyTypeEnum.getEnum(model.getType()).getValue());
        }
    }
}
