package com.logistics.appapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum LeaveApplyTypeEnum {

    DEFAULT(-1, ""),
    PERSONAL_LEAVE(1, "事假"),
    ;

    private Integer key;
    private String value;

    public static LeaveApplyTypeEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
