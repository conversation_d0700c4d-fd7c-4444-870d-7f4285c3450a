package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/1/4 14:00
 */
@Data
public class WaitDispatchStatisticsDto {
    @ApiModelProperty("统计类型：1是0-3天，2是4-10天，3是11-20天，4是21-31天，5是大于31天")
    private String dayType = "";
    @ApiModelProperty("需求单单数")
    private String orderCount ="0";
    @ApiModelProperty("需求单货物数量")
    private String orderAmount = "0";
    @ApiModelProperty("百分比")
    private String proportion = "0";
}
