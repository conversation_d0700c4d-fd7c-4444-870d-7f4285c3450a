package com.logistics.tms.controller.warehouseaddress.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WarehouseAddressDetailResponseModel {

    @ApiModelProperty("ID")
    private Long warehouseAddressId;
    @ApiModelProperty("仓库")
    private String warehouse;
    @ApiModelProperty("省ID")
    private Long provinceId;
    @ApiModelProperty("市ID")
    private Long cityId;
    @ApiModelProperty("区ID")
    private Long areaId;
    @ApiModelProperty("省")
    private String provinceName;
    @ApiModelProperty("市")
    private String cityName;
    @ApiModelProperty("区")
    private String areaName;
    @ApiModelProperty("委托方公司名称")
    private String companyEntrustName;

}
