package com.logistics.tms.api.feign.vehicleoilcard;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.vehicleoilcard.hystrix.VehicleOilCardServiceApiHystrix;
import com.logistics.tms.api.feign.vehicleoilcard.model.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/4 15:12
 */
@Api(value = "API-VehicleOilCardServiceApi-车辆油卡管理")
@FeignClient(name = "logistics-tms-services", fallback = VehicleOilCardServiceApiHystrix.class)
public interface VehicleOilCardServiceApi {

    @ApiOperation(value = "车辆油卡列表")
    @PostMapping("/service/vehicleOilCard/searchVehicleOilCardList")
    Result<PageInfo<SearchVehicleOilCardListResponseModel>> searchVehicleOilCardList(@RequestBody SearchVehicleOilCardListRequestModel requestModel);

    @ApiOperation(value = "新增车辆油卡")
    @PostMapping("/service/vehicleOilCard/addVehicleOilCard")
    Result<Boolean> addVehicleOilCard(@RequestBody AddVehicleOilCardRequestModel requestModel);

    @ApiOperation(value = "车辆油卡详情")
    @PostMapping("/service/vehicleOilCard/vehicleOilCardDetail")
    Result<VehicleOilCardDetailResponseModel> vehicleOilCardDetail(@RequestBody VehicleOilCardIdRequestModel requestModel);

    @ApiOperation(value = "解绑车辆油卡")
    @PostMapping("/service/vehicleOilCard/unBindVehicleOilCard")
    Result<Boolean> unBindVehicleOilCard(@RequestBody VehicleOilCardIdRequestModel requestModel);

    @ApiOperation(value = "绑定车辆油卡")
    @PostMapping("/service/vehicleOilCard/bindVehicleOilCard")
    Result<Boolean> bindVehicleOilCard(@RequestBody BindVehicleOilCardRequestModel requestModel);

    @ApiOperation(value = "操作记录")
    @PostMapping("/service/vehicleOilCard/getVehicleOilCardRecord")
    Result<List<GetVehicleOilCardRecordResponseModel>> getVehicleOilCardRecord(@RequestBody VehicleOilCardIdRequestModel requestModel);

}
