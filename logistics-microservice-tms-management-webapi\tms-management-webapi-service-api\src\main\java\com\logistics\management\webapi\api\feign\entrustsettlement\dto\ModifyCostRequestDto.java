package com.logistics.management.webapi.api.feign.entrustsettlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ModifyCostRequestDto {
    @ApiModelProperty("结算ID")
    @NotBlank(message = "结算ID不能为空")
    private String settlementId;
    @ApiModelProperty("报价类型")
    @NotBlank(message = "报价类型不能为空")
    private String contractPriceType;
    @ApiModelProperty("结算费用")
    @NotBlank(message = "结算费用不能为空")
    private String settlementCostTotal;
}
