package com.logistics.tms.biz.platformcompany;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.platformcompany.model.*;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.IfValidEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TPlatformCompany;
import com.logistics.tms.mapper.TPlatformCompanyMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/11/11 17:46
 */
@Service
public class PlatformCompanyBiz {

    @Autowired
    private TPlatformCompanyMapper tPlatformCompanyMapper;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 结算主体列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchPlatformCompanyListResponseModel> searchPlatformCompanyList(SearchPlatformCompanyListRequestModel requestModel) {
        requestModel.enablePaging();
        return new PageInfo<>(tPlatformCompanyMapper.searchPlatformCompanyList(requestModel));
    }

    /**
     * 新增结算主体
     * @param requestModel
     * @return
     */
    @Transactional
    public void addPlatformCompany(AddPlatformCompanyRequestModel requestModel) {
        //查询公司名称是否存在
        TPlatformCompany dbPlatformCompany = tPlatformCompanyMapper.getByName(requestModel.getPlatformCompanyName());
        if (dbPlatformCompany != null){
            throw new BizException(CarrierDataExceptionEnum.PLATFORM_COMPANY_EXIST);
        }

        //新增
        TPlatformCompany platformCompany = new TPlatformCompany();
        platformCompany.setCompanyName(requestModel.getPlatformCompanyName());
        commonBiz.setBaseEntityAdd(platformCompany, BaseContextHandler.getUserName());
        tPlatformCompanyMapper.insertSelective(platformCompany);
    }

    /**
     * 删除结算主体
     * @param requestModel
     * @return
     */
    @Transactional
    public void delPlatformCompany(DelPlatformCompanyRequestModel requestModel) {
        //查询公司名称是否存在
        TPlatformCompany dbPlatformCompany = tPlatformCompanyMapper.selectByPrimaryKey(requestModel.getPlatformCompanyId());
        if (dbPlatformCompany == null || dbPlatformCompany.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.PLATFORM_COMPANY_NOT_EXIST);
        }

        //删除
        TPlatformCompany platformCompany = new TPlatformCompany();
        platformCompany.setId(requestModel.getPlatformCompanyId());
        platformCompany.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(platformCompany, BaseContextHandler.getUserName());
        tPlatformCompanyMapper.updateByPrimaryKeySelective(platformCompany);
    }

    /**
     * 查询结算主体(下拉列表使用)
     * @param requestModel
     * @return
     */
    public List<PlatformCompanySelectListResponseModel> platformCompanySelectList(PlatformCompanySelectListRequestModel requestModel) {
        return tPlatformCompanyMapper.platformCompanySelectList(requestModel);
    }
}
