package com.logistics.tms.controller.leave.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DriverLeaveApplyListItemModel {

    @ApiModelProperty("请假申请ID")
    private Long leaveApplyId;

    @ApiModelProperty("请假申请审核状态,审核状态: 0 待审核，1 已审核，2 已驳回，3 已撤销")
    private Integer leaveAuditStatus;

    @ApiModelProperty("申请人名子")
    private String staffName;

    @ApiModelProperty("申请人名子")
    private String staffMobile;

    @ApiModelProperty("请假类型")
    private Integer leaveType;

    @ApiModelProperty("请假申请开始时间")
    private Date leaveStartTime;

    @ApiModelProperty("请假申请开始时间")
    private Integer leaveStartTimeType;

    @ApiModelProperty("请假申请结束时间")
    private Date leaveEndTime;

    @ApiModelProperty("请假申请结束时间类型")
    private Integer leaveEndTimeType;

    @ApiModelProperty("请假申请审核状态")
    private Integer auditStatus;

}
