package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/7/10 13:03
 */
@Getter
@AllArgsConstructor
public enum RouteEnquiryStatusEnum {

    DEFAULT(0, ""),
    WAIT_BUSINESS_AUDIT(1, "待业务审核"),
    WAIT_CARRIER_CONFIRM(2, "待车主确认"),
    WAIT_SETTLE_AUDIT(3, "待结算审核"),
    COMPLETE_QUOTATION(4, "完成竞价"),
    QUOTATION_CANCEL(5, "竞价取消"),
    ;

    private final Integer key;
    private final String value;

    public static RouteEnquiryStatusEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

}
