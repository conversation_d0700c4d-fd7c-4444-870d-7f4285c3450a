package com.logistics.tms.biz.email;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.ConfigKeyEnum;
import com.logistics.tms.base.enums.DemandOrderSourceEnum;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.DemandOrderCommonBiz;
import com.logistics.tms.biz.demandorder.SinopecDemandOrderBiz;
import com.logistics.tms.biz.email.model.SinopecEntrustOrderModel;
import com.logistics.tms.biz.email.model.SinopecEntrustOrderResultModel;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.config.cache.CacheManager;
import com.logistics.tms.config.cache.ExpiryMap;
import com.logistics.tms.entity.TDemandOrder;
import com.logistics.tms.entity.TDemandOrderAddress;
import com.logistics.tms.mapper.TDemandOrderMapper;
import com.yelo.tools.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.BasicCookieStore;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class PullFromSinopecBiz {

    private ObjectMapper objectMapper = JacksonUtils.getInstance();
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TDemandOrderMapper demandOrderMapper;
    @Autowired
    private SinopecDemandOrderBiz sinopecDemandOrderBiz;
    @Autowired
    private DemandOrderCommonBiz demandOrderCommonBiz;
    @Resource
    private SysConfigBiz sysConfigBiz;

    private String vcode = "1234";
    private String smscode = "123456";

    /**
     * 从中石化网拉取订单
     */
    @Transactional
    public void pullDemandOrderByRequestSinopec() {
        if (!ifLogin) {
            loginSinopec(vcode, smscode);
        }
        Map<String, String> configMap = sysConfigBiz.getSysConfig(ConfigKeyEnum.ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode());
        log.info("获取中石化系统查询需求单数据开发、url、查询周期范围相关配置："+configMap.toString());
        //判断开关是否关闭
        String orderSwitch = configMap.get(ConfigKeyEnum.KEY_API_QUERY_ORDER_SWITCH.getValue());
        if (CommonConstant.ZERO.equals(orderSwitch)){
            return;
        }
        //判断查询的开始时间是否存在
        Date configFromDate = DateUtils.stringToDate(configMap.get(ConfigKeyEnum.KEY_API_QUERY_ORDER_FROM_DATE.getValue()), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
        if (configFromDate == null) {
            return;
        }

        String apiQueryOrderUrl = configMap.get(ConfigKeyEnum.KEY_API_QUERY_ORDER_URL.getValue());
        Integer days = ConverterUtils.toInt(configMap.get(ConfigKeyEnum.KEY_API_QUERY_ORDER_DATE_INTERVAL.getValue()), 5);

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("pageNo", "1");
        queryParams.put("pageSize", "500");
        Calendar today = Calendar.getInstance();
        today.set(Calendar.HOUR_OF_DAY, 23);
        today.set(Calendar.MINUTE, 59);
        today.set(Calendar.SECOND, 59);
        Date endDate = today.getTime();
        today.add(Calendar.DAY_OF_MONTH, -days);
        today.set(Calendar.HOUR_OF_DAY, 0);
        today.set(Calendar.MINUTE, 0);
        today.set(Calendar.SECOND, 0);
        today.set(Calendar.MILLISECOND, 0);
        Date fromDate = today.getTime();
        if (fromDate.compareTo(configFromDate) < 0) {
            fromDate = configFromDate;
        }
        today.add(Calendar.DAY_OF_MONTH, -days);
        Date localSystemFromDate = today.getTime();

        queryParams.put("consignDateStart", DateUtils.dateToString(fromDate, "yyyy/MM/dd HH:mm:ss"));
        queryParams.put("consignDateEnd", DateUtils.dateToString(endDate, "yyyy/MM/dd HH:mm:ss"));

        SinopecEntrustOrderResultModel sinopecEntrustOrderModelResult = null;
        try {
            log.info("获取中石化系统查询需求单数据，url为："+apiQueryOrderUrl+"，参数为："+queryParams);
            String result = HttpClientUtils.requestUsingPost(apiQueryOrderUrl, queryParams);
            if (StringUtils.isNotBlank(result)) {
                sinopecEntrustOrderModelResult = objectMapper.readValue(result, SinopecEntrustOrderResultModel.class);
            } else {
                log.info("获取中石化系统查询需求单数据，未查询到数据！");
                return;
            }
        } catch (Exception e) {
            loginSinopec(vcode, smscode);
            return;
        }
        if (sinopecEntrustOrderModelResult != null && ListUtils.isNotEmpty(sinopecEntrustOrderModelResult.getRows())) {
            processSaveDemandOrder(sinopecEntrustOrderModelResult.getRows(), localSystemFromDate, endDate, days, configMap);
        }
    }

    private void processSaveDemandOrder(List<SinopecEntrustOrderModel> sinopecEntrustOrderModels, Date fromDate, Date endDate, Integer days, Map<String, String> configMap) {
        List<SinopecEntrustOrderModel> needSaveEntrustOrders = new ArrayList<>();
        List<TDemandOrder> demandOrderList = demandOrderMapper.selectDemandOrdersBySourceAndPubDate(DemandOrderSourceEnum.SINOPEC_SYSTEM.getKey(), fromDate, endDate);

        for (SinopecEntrustOrderModel sinpopecEntrustOrder : sinopecEntrustOrderModels) {
            boolean ifNeedAdd = true;
            if (StringUtils.isNotBlank(sinpopecEntrustOrder.getSapOrderNo())) {
                for (TDemandOrder demandOrder : demandOrderList) {
                    if (sinpopecEntrustOrder.getSapOrderNo().equals(demandOrder.getCustomerOrderCode())) {
                        ifNeedAdd = false;
                        break;
                    }
                }
            }
            if (ifNeedAdd) {
                needSaveEntrustOrders.add(sinpopecEntrustOrder);
            }
        }
        if (ListUtils.isEmpty(needSaveEntrustOrders)) {
            return;
        }

        ExpiryMap expiryMap = CacheManager.getInstance();
        Object object;
        List<String> sapOrderNoList = null;
        List<TDemandOrderAddress> addDemandOrderAddressList = new ArrayList<>();
        for (SinopecEntrustOrderModel tmpEntrustOrder :
                needSaveEntrustOrders) {
            try {
                object = expiryMap.get(tmpEntrustOrder.getOutWarehouseName());
                if (object instanceof ArrayList){
                    sapOrderNoList = (List<String>) object;
                }
                if (object == null || ListUtils.isEmpty(sapOrderNoList) || !sapOrderNoList.contains(tmpEntrustOrder.getSapOrderNo())) {
                    sinopecDemandOrderBiz.saveDemandOrder(tmpEntrustOrder, configMap, addDemandOrderAddressList);
                    if (ListUtils.isEmpty(sapOrderNoList)){
                        sapOrderNoList = new ArrayList<>();
                    }
                    sapOrderNoList.add(tmpEntrustOrder.getSapOrderNo());
                    expiryMap.put(tmpEntrustOrder.getOutWarehouseName(), sapOrderNoList, days*24*3600*1000L);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }

        //异步查询地址经纬度
        if (ListUtils.isNotEmpty(addDemandOrderAddressList)) {
            AsyncProcessQueue.execute(() -> demandOrderCommonBiz.updateAddressLonAndLat(addDemandOrderAddressList));
        }
    }

    private boolean ifLogin = false;

    /**
     * 中石化网登陆接口
     * @param vcode
     * @param smscode
     */
    public void loginSinopec(String vcode, String smscode) {
        Map<String, String> configMap = sysConfigBiz.getSysConfig(ConfigKeyEnum.ConfigGroupCodeEnum.SINOPEC_CONFIG.getCode());
        log.info("获取中石化系统登录相关url、账密相关配置："+configMap.toString());
        String casLoginPageUrl = configMap.get(ConfigKeyEnum.KEY_CAS_LOGIN_PAGE_URL.getValue());
        String casLoginUrl = configMap.get(ConfigKeyEnum.KEY_CAS_LOGIN_URL.getValue());
        String apiLoginPageUrl = configMap.get(ConfigKeyEnum.KEY_API_LOGIN_PAGE_URL.getValue());
        String userName = configMap.get(ConfigKeyEnum.KEY_USER_NAME.getValue());
        String password = configMap.get(ConfigKeyEnum.KEY_PASSWORD.getValue());
        //清空cookie
        HttpClientUtils.cookieStore = new BasicCookieStore();
        //请求首页，获取sessionId
        HttpClientUtils.requestUsingGet(casLoginPageUrl);
        //第二次请求
        HttpClientUtils.requestUsingPost(casLoginUrl, null);
        //第三次请求
        HttpClientUtils.requestUsingPost(casLoginUrl, null);

        Map<String, String> loginInfoMap = new HashMap<>();
        loginInfoMap.put("username", userName);
        loginInfoMap.put("password", password);
        loginInfoMap.put("execution", "e3s1");
        loginInfoMap.put("_eventId", "submit");
        loginInfoMap.put("geolocation", "");
        loginInfoMap.put("submit", "登录");
        loginInfoMap.put("vcode", vcode);
        loginInfoMap.put("smscode", smscode);
        log.info("登录中石化系统，第一次请求post，url为："+casLoginUrl+"，参数为："+loginInfoMap);
        HttpClientUtils.requestUsingPost(casLoginUrl, loginInfoMap);
        log.info("登录中石化系统，第二次跳转请求get，url为："+apiLoginPageUrl);
        HttpClientUtils.requestUsingGet(apiLoginPageUrl);
        ifLogin = true;
        log.info("登录中石化系统成功");
        log.info("https://portal.sclexter.sinopec.com/web/ login succeed!");
    }


}
