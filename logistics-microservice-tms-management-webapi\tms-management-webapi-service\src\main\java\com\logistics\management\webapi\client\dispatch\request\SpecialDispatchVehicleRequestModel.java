package com.logistics.management.webapi.client.dispatch.request;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/7 9:11
 */
@Data
public class SpecialDispatchVehicleRequestModel {

    /**
     * 需求单货物信息
     */
    private List<SpecialDispatchVehicleListRequestModel> vehicleRequestModels;

    /**
     * 预计提货/到货时间
     */
    private Date expectArrivalTime;

    /**
     * 车辆ID
     */
    private Long vehicleId;

    /**
     * 挂车车辆ID
     */
    private Long trailerVehicleId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 装卸数-装
     */
    private Integer loadPointAmount;

    /**
     * 装卸数-卸
     */
    private Integer unloadPointAmount;

    /**
     * 车长（选择“零担”时传-1，其他选择什么传什么）
     */
    private BigDecimal vehicleLength;

    /**
     * 串点费用
     */
    private BigDecimal crossPointFee;

    /**
     * 整车运费
     */
    private BigDecimal carrierFreight;

    /**
     * 备注
     */
    private String remark;

    private Integer source;//来源：1 后台，2 前台


    /**
     * 是否匹配了车长和串点 0：否 1：是   v2.42
     */
    private Integer ifMatch;
    /**
     * 是否加入新的零担标识 有就是加入旧的  v2.42
     */
    private String shippingOrderCode;
}
