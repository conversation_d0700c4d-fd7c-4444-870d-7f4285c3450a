package com.logistics.management.webapi.api.feign.carriercontact.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: sj
 * @Date: 2020/3/24 19:04
 */
@Data
public class DelCarrierContactRequestDto {

    @ApiModelProperty(value = "车主账号id",required = true)
    @NotBlank(message = "车主账号id不能为空")
    private String carrierContactId;
}
