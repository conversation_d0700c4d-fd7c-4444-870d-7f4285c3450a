package com.logistics.tms.biz.bank

import com.yelo.tray.core.exception.BizException
import com.logistics.tms.api.feign.bank.model.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.annotation.Rollback
import spock.lang.Specification
import spock.lang.Unroll
/**
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */

@Rollback
@SpringBootTest
class BankBizTest extends Specification {

    @Autowired
    BankBiz bankBiz

    @Unroll
    def "search Bank List where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        bankBiz.searchBankList(requestModel).getList().size() == expectedResult

        where:
        requestModel                 || expectedResult
        new SearchBankRequestModel() || 0
    }

    @Unroll
    def "save Or Modify Bank where requestModel=#requestModel"() {
        expect:
        bankBiz.saveOrModifyBank(requestModel)
        assert expectedResult == true

        where:
        requestModel                       || expectedResult
        new SaveOrModifyBankRequestModel() || true
    }

    @Unroll
    def "batch Import Bank Info where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        bankBiz.batchImportBankInfo(requestModel) == expectedResult

        where:
        requestModel                 || expectedResult
        new ImportBankRequestModel() || new ImportBankResponseModel()
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        bankBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new BankDetailRequestModel(bankId: 82) || new BankDetailResponseModel(bankId: 82,bankName: "fake_data",branchName: "fake_data",enable: 52)
    }

    @Unroll
    def "enable Or Disable where requestModel=#requestModel"() {
        when:
        bankBiz.enableOrDisable(requestModel)

        then: "验证结果"
        def e = thrown(BizException)
        with(e) {
            message == expectedResult
        }

        where:
        requestModel                 || expectedResult
        new EnableBankRequestModel() || "银行信息不存在"
    }

    @Unroll
    def "fuzzy Query Insurance Company where requestModel=#requestModel then expect: #expectedResult"() {
        given:

        expect:
        bankBiz.fuzzyQueryInsuranceCompany(requestModel) == expectedResult

        where:
        requestModel                     || expectedResult
        new FuzzyQueryBankRequestModel() || [new com.logistics.tms.api.feign.bank.model.FuzzyQueryBankListResponseModel()]
    }
}
