package com.logistics.tms.mapper;

import com.logistics.tms.entity.TRenewableAuditGoods;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/08/15
*/
@Mapper
public interface TRenewableAuditGoodsMapper extends BaseMapper<TRenewableAuditGoods> {

    /**
     * 查询新生订单货物列表信息
     *
     * @param renewableOrderId
     * @return
     */
    List<TRenewableAuditGoods> queryGoodsListByRenewableOrderId(@Param("renewableOrderId") Long renewableOrderId);

    /**
     * 查询新生订单货物列表信息
     *
     * @param renewableOrderId
     * @param goodsSourceType 货物类型：1 新生同步，2 司机确认
     * @return
     */
    List<TRenewableAuditGoods> queryGoodsListByOrderIdAndSourceType(@Param("renewableOrderId") Long renewableOrderId,
                                                                @Param("goodsSourceType") Integer goodsSourceType);

    /**
     * 删除新生订单绑定的货物
     *
     * @param renewableOrderId 新生订单id
     * @param goodsSourceType 货物类型
     * @return
     */
    int deleteByRenewableOrderId(@Param("renewableOrderId") Long renewableOrderId,
                                 @Param("goodsSourceType") Integer goodsSourceType,
                                 @Param("operatorName") String operatorName);
}