package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CopyCarrierRecycleOrderOrderRelByDispatchVehicleModel {
    @ApiModelProperty("运单code")
    private String carrierOrderCode;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("订单code")
    private String orderCode;

    @ApiModelProperty("订单数量")
    private Integer expectAmount;
}
