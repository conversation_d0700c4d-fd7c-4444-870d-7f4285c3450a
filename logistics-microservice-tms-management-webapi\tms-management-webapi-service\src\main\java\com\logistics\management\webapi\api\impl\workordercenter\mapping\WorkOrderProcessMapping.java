package com.logistics.management.webapi.api.impl.workordercenter.mapping;

import com.logistics.management.webapi.api.feign.workordercenter.dto.response.WorkOrderProcessResponseDto;
import com.logistics.tms.api.feign.workordercenter.enums.WorkOrderProcessSolveLabelEnum;
import com.logistics.tms.api.feign.workordercenter.enums.WorkOrderProcessStatusEnum;
import com.logistics.tms.api.feign.workordercenter.model.response.WorkOrderProcessResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/17
 */
public class WorkOrderProcessMapping extends MapperMapping<WorkOrderProcessResponseModel, WorkOrderProcessResponseDto> {

	@Override
	public void configure() {
		WorkOrderProcessResponseModel source = getSource();
		WorkOrderProcessResponseDto destination = getDestination();

		//状态
		destination.setStatusLabel(WorkOrderProcessStatusEnum.getEnumByKey(source.getStatus()).getValue());
		destination.setSolveSourceLabel(WorkOrderProcessSolveLabelEnum.getEnumByKey(source.getSolveSource()).getValue());
		String remark = StringUtils.isNotBlank(source.getSolveRemark()) ? "。 " + source.getSolveRemark() : "";
		destination.setSolveRemark(source.getSolveDesc() + remark);
	}
}
