package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TContract extends BaseEntity {
    /**
    * 内部合同编号
    */
    @ApiModelProperty("内部合同编号")
    private String contractNoInternal;

    /**
    * 外部合同编号
    */
    @ApiModelProperty("外部合同编号")
    private String contractNoExternal;

    /**
    * 合同状态：1 待执行，2 执行中，3 已终止，4 已作废
    */
    @ApiModelProperty("合同状态：1 待执行，2 执行中，3 已终止，4 已作废")
    private Integer contractStatus;

    /**
    * 合同类型：1 框架合同，2 单次合同
    */
    @ApiModelProperty("合同类型：1 框架合同，2 单次合同")
    private Integer contractType;

    /**
    * 合同性质：1 货源合同，2 车源合同，3租赁合同
    */
    @ApiModelProperty("合同性质：1 货源合同，2 车源合同，3租赁合同")
    private Integer contractNature;

    /**
    * 签订对象的ID：根据合同性质确定，货源合同对应委托方，车源合同对应承运商
    */
    @ApiModelProperty("签订对象的ID：根据合同性质确定，货源合同对应委托方，车源合同对应承运商")
    private Long contractObjectId;

    /**
    * 客户公司名称，租赁合同使用
    */
    @ApiModelProperty("客户公司名称，租赁合同使用")
    private String customerCompanyName;

    /**
    * 我司抬头
    */
    @ApiModelProperty("我司抬头")
    private String contractHeader;

    /**
    * 合同有效期起始时间
    */
    @ApiModelProperty("合同有效期起始时间")
    private Date contractStartTime;

    /**
    * 合同有效期结束时间
    */
    @ApiModelProperty("合同有效期结束时间")
    private Date contractEndTime;

    /**
    * 终止或作废操作人
    */
    @ApiModelProperty("终止或作废操作人")
    private String endingCancelBy;

    /**
    * 终止或作废原因
    */
    @ApiModelProperty("终止或作废原因")
    private String endingCancelRemark;

    /**
    * 终止或作废时间
    */
    @ApiModelProperty("终止或作废时间")
    private Date endingCancelDatetime;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}