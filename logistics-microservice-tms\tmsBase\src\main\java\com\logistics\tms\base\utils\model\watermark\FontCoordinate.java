package com.logistics.tms.base.utils.model.watermark;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.awt.*;

/**
 * 文字水印
 *
 * <AUTHOR>
 * @date 2022/10/14 14:56
 */
@Data
@NoArgsConstructor
public class FontCoordinate extends Coordinate {

    /**
     * 文字内容
     */
    private String text;

    /**
     * 字体颜色和透明度
     */
    private Color color;

    /**
     * 字体和大小
     */
    private Font font;

    public FontCoordinate(String text, Color color, Font font, int x, int y) {
        this.text = text;
        this.color = color;
        this.font = font;
        setX(x);
        setY(y);
    }
}
