package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/10/24
*/
@Data
public class TCarrierOrderGoodsCode extends BaseEntity {
    /**
    * 运单货物表id
    */
    @ApiModelProperty("运单货物表id")
    private Long carrierOrderGoodsId;

    /**
    * 新生货物编码
    */
    @ApiModelProperty("新生货物编码")
    private String yeloGoodCode;

    /**
    * 装货数量/实提数量
    */
    @ApiModelProperty("装货数量/实提数量")
    private BigDecimal loadAmount;

    /**
    * 卸货数量/实卸数量
    */
    @ApiModelProperty("卸货数量/实卸数量")
    private BigDecimal unloadAmount;

    /**
    * 签收件数
    */
    @ApiModelProperty("签收件数")
    private BigDecimal signAmount;

    /**
    * 单位 1.kg'
    */
    @ApiModelProperty("单位 1.kg'")
    private Integer unit;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}