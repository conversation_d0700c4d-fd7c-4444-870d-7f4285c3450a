package com.logistics.management.webapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author:lei.zhu
 * @date:2021/4/9 13:49
 */
@Data
public class SettlementStatementHandleDetailResponseModel {
    @ApiModelProperty("车辆运费账单id")
    private Long vehicleSettlementId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("账单月")
    private String settlementMonth;
    @ApiModelProperty("实付运费")
    private BigDecimal actualExpensesPayable;
    @ApiModelProperty("司机异议")
    private String settlementReasonRemark;

    @ApiModelProperty("司机 姓名")
    private String driverName;
    @ApiModelProperty("司机 手机号")
    private String driverPhone;

    @ApiModelProperty("调整费用符号：1 ‘+’， 2 ‘-’ ")
    private Integer adjustCostSymbol;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustFee;
    @ApiModelProperty("调整费用原因")
    private String adjustRemark;

    private Integer status;
}
