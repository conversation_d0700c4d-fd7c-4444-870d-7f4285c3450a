package com.logistics.tms.mapper;

import com.logistics.tms.biz.reservebalance.model.DriverBalanceDetailModel;
import com.logistics.tms.controller.drivercostapply.request.SearchCostApplyListRequestModel;
import com.logistics.tms.controller.drivercostapply.request.SearchCostApplySummaryRequestModel;
import com.logistics.tms.controller.drivercostapply.response.DriverCostApplyDetailResponseModel;
import com.logistics.tms.controller.drivercostapply.response.DriverCostStatisticsModel;
import com.logistics.tms.controller.drivercostapply.response.SearchCostApplyListResponseModel;
import com.logistics.tms.controller.drivercostapply.response.SearchCostApplySummaryResponseModel;
import com.logistics.tms.entity.TDriverCostApply;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created by Mybatis Generator on 2022/08/04
 */
@Mapper
public interface TDriverCostApplyMapper extends BaseMapper<TDriverCostApply> {

	TDriverCostApply selectByPrimaryKeyDecrypt(Long id);

	int insertSelectiveEncrypt(TDriverCostApply tDriverCostApply);

	int updateByPrimaryKeySelectiveEncrypt(TDriverCostApply tDriverCostApply);

	List<SearchCostApplyListResponseModel> searchCostApplyList(SearchCostApplyListRequestModel requestModel);

	DriverCostApplyDetailResponseModel selectDriverCostApplyDetail(@Param("driverCostApplyId") Long driverCostApplyId, @Param("driverId") Long driverId);

	DriverCostStatisticsModel searchCostStatisticsByApplyTime(@Param("applyTime") String applyTime, @Param("loginDriverUserId") Long loginDriverAppletUserId);

	TDriverCostApply selectByIdAndDriverId(@Param("driverCostApplyId") Long driverCostApplyId, @Param("staffId") Long staffId);

	List<String> searchCostApplySummaryIds(SearchCostApplySummaryRequestModel requestModel);

	List<SearchCostApplySummaryResponseModel> searchCostApplySummary(@Param("ids") List<Long> ids);

	TDriverCostApply selectByOilCardNumAndStatus(@Param("cardNumber") String cardNumber, @Param("auditStatus") String auditStatus);

	@MapKey("driverId")
	Map<Long, DriverBalanceDetailModel> statisticsAwaitVerificationAmountByDriver(@Param("driverIds") Collection<Long> driverIds);
}