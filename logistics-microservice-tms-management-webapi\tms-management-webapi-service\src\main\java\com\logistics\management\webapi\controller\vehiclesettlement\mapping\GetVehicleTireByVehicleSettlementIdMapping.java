package com.logistics.management.webapi.controller.vehiclesettlement.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.vehiclesettlement.response.GetVehicleTireByVehicleSettlementIdResponseModel;
import com.logistics.management.webapi.client.vehiclesettlement.response.GetVehicleTireListByVehicleSettlementIdResponseModel;
import com.logistics.management.webapi.client.vehiclesettlement.response.VehicleTireNoModel;
import com.logistics.management.webapi.controller.vehiclesettlement.response.GetVehicleTireByVehicleSettlementIdResponseDto;
import com.logistics.management.webapi.controller.vehiclesettlement.response.GetVehicleTireListByVehicleSettlementIdResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @author:lei.zhu
 * @date:2021/7/27 11:31
 */
public class GetVehicleTireByVehicleSettlementIdMapping  extends MapperMapping<GetVehicleTireByVehicleSettlementIdResponseModel, GetVehicleTireByVehicleSettlementIdResponseDto> {
    @Override
    public void configure() {
        GetVehicleTireByVehicleSettlementIdResponseDto destination = getDestination();
        GetVehicleTireByVehicleSettlementIdResponseModel source = getSource();
        List<GetVehicleTireListByVehicleSettlementIdResponseDto> vehicleTireList =new ArrayList<>();
        for (GetVehicleTireListByVehicleSettlementIdResponseModel tire : source.getVehicleTireList()) {
            GetVehicleTireListByVehicleSettlementIdResponseDto mapper = MapperUtils.mapper(tire, GetVehicleTireListByVehicleSettlementIdResponseDto.class);
            List<VehicleTireNoModel> tireNoList = tire.getTireNoList();
            BigDecimal tireCost=BigDecimal.ZERO;
            for (VehicleTireNoModel vehicleTireNoModel : tireNoList) {
                tireCost=tireCost.add(vehicleTireNoModel.getAmount().multiply(vehicleTireNoModel.getUnitPrice()));
            }
            mapper.setTireCost(tireCost.setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
            mapper.setReplaceDate(DateUtils.dateToString(tire.getReplaceDate(), CommonConstant.DATE_TO_STRING_YMD_PATTERN));
            vehicleTireList.add(mapper);
        }
        destination.setVehicleTireList(vehicleTireList);
    }
}
