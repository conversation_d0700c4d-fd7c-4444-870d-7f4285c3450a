package com.logistics.management.webapi.api.feign.loanrecord.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/9/30 9:40
 */
@Data
public class SaveOrUpdateLoanRecordRequestDto {
    @ApiModelProperty("贷款记录ID")
    private String loanRecordId;
    @ApiModelProperty("车辆ID")
    @NotBlank(message = "请选择车牌号")
    private String vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("品牌")
    @NotBlank(message = "品牌型号为空")
    private String brand;
    @ApiModelProperty("型号")
    @NotBlank(message = "品牌型号为空")
    private String model;
    @ApiModelProperty("车辆识别号")
    @NotBlank(message = "车辆识别号必填")
    private String vehicleIdentificationNumber;
    @ApiModelProperty("发动机号码")
    @NotBlank(message = "发动机号为空")
    private String engineNumber;
    @ApiModelProperty("车身颜色")
    private String bodyColor;
    @ApiModelProperty("生产地")
    @Size(max = 50,message = "最多50字")
    private String producer;
    @ApiModelProperty("生产厂商")
    @Size(max = 20,message = "最多20字")
    private String manufacturers;
    @ApiModelProperty("生产日期")
    private String productionDate;
    @ApiModelProperty("司机id")
    @NotBlank(message = "请选择司机")
    private String staffId;
    @ApiModelProperty("司机姓名")
    private String name;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("身份证号码")
    @NotBlank(message = "身份证号为空")
    private String identityNumber;
    @ApiModelProperty("裸车价")
    @NotBlank(message = "裸车价为空")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "裸车价,大于0小于2000000，且保留两位小数")
    @DecimalMin(value = "0.01",message = "裸车价,大于0小于2000000，且保留两位小数")
    @DecimalMax(value = "1999999.99")
    private String nakedCarPrice;
    @ApiModelProperty("保险费")
    @NotBlank(message = "保险费为空")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "保险费,大于0小于500000，且保留两位小数")
    @DecimalMin(value = "0.01",message = "保险费,大于0小于500000，且保留两位小数")
    @DecimalMax(value = "499999.99",message = "保险费,大于0小于500000，且保留两位小数")
    private String insurancePremium;
    @ApiModelProperty("购置税")
    @NotBlank(message = "购置税为空")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "购置税,大于0小于500000，且保留两位小数")
    @DecimalMin(value = "0.01",message = "购置税,大于0小于500000，且保留两位小数")
    @DecimalMax(value = "499999.99",message = "购置税,大于0小于500000，且保留两位小数")
    private String purchaseTax;
    @ApiModelProperty("购车总价")
    @NotBlank(message = "购车总价为空")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "请维护正确的购车总价")
    @DecimalMin(value = "0.01",message = "0<购车总价<总价")
    private String carPrice;
    @ApiModelProperty("司机已承担费用")
    @NotBlank(message = "司机已承担费用为空")
    private String driverExpense;
    @ApiModelProperty("总贷款费用")
    @NotBlank(message = "请维护总贷款费用")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "请维护正确的总贷款数")
    @DecimalMin(value = "0.01",message = "0<总贷款费用<总价")
    private String loanFee;
    @ApiModelProperty("贷款总期数")
    @NotBlank(message = "贷款总期数为空")
    @Pattern(regexp = "(^[0-9]*$)",message = "贷款总期数为整数0<总期数≤500")
    @Min(value = 1,message = "贷款总期数为整数0<总期数≤500")
    @Max(value = 500,message = "贷款总期数为整数0<总期数≤500")
    private String loanPeriods;
    @ApiModelProperty("贷款开始时间")
    @NotBlank(message = "贷款开始时间为空")
    private String loanStartTime;
    @ApiModelProperty("贷款利率")
    @NotBlank(message = "贷款利率为空")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "贷款利率,大于0小于100，且保留两位小数")
    @DecimalMin(value = "0.01",message = "贷款利率,大于0小于100，且保留两位小数")
    @DecimalMax(value = "99.99",message = "贷款利率,大于0小于100，且保留两位小数")
    private String loanRate;
    @ApiModelProperty("贷款手续费")
    @NotBlank(message = "贷款手续费为空")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "贷款手续费,大于0小于500000，且保留两位小数")
    @DecimalMin(value = "0.01",message = "贷款手续费,大于0小于500000，且保留两位小数")
    @DecimalMax(value = "499999.99",message = "贷款手续费,大于0小于500000，且保留两位小数")
    private String loanCommission;
    @ApiModelProperty("贷款利息")
    @NotBlank(message = "贷款利息为空")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "贷款利息,大于0小于500000，且保留两位小数")
    @DecimalMin(value = "0.01",message = "贷款利息,大于0小于500000，且保留两位小数")
    @DecimalMax(value = "499999.99",message = "贷款利息,大于0小于500000，且保留两位小数")
    private String loanInterest;
    @ApiModelProperty("备注")
    @Size(max = 300,message = "备注不超过300字")
    private String remark;
    @ApiModelProperty("附件列表")
    private List<String> fileList;

}
