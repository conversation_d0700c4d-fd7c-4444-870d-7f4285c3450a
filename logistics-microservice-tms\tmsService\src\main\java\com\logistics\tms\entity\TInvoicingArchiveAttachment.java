package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/07/02
*/
@Data
public class TInvoicingArchiveAttachment extends BaseEntity {
    /**
    * 发票管理id
    */
    @ApiModelProperty("发票管理id")
    private Long invoicingId;

    /**
    * 图片路径
    */
    @ApiModelProperty("图片路径")
    private String imagePath;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}