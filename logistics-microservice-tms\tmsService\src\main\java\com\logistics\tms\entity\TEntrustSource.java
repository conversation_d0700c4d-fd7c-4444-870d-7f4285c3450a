package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TEntrustSource extends BaseEntity {
    /**
    * 公司名称
    */
    @ApiModelProperty("公司名称")
    private String companyName;

    /**
    * 联系人姓名
    */
    @ApiModelProperty("联系人姓名")
    private String contactName;

    /**
    * 联系人手机号
    */
    @ApiModelProperty("联系人手机号")
    private String contactMobile;

    /**
    * 发货备注
    */
    @ApiModelProperty("发货备注")
    private String remark;
}