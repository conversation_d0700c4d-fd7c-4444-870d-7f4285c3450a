<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDriverPayeeMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDriverPayee" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="mobile" property="mobile" jdbcType="VARCHAR" />
    <result column="identity_no" property="identityNo" jdbcType="VARCHAR" />
    <result column="bank_id" property="bankId" jdbcType="BIGINT" />
    <result column="bank_card_no" property="bankCardNo" jdbcType="VARCHAR" />
    <result column="audit_status" property="auditStatus" jdbcType="INTEGER" />
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
    <result column="auditor_id" property="auditorId" jdbcType="BIGINT" />
    <result column="auditor_name" property="auditorName" jdbcType="VARCHAR" />
    <result column="audit_reason" property="auditReason" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, name, mobile, identity_no, bank_id, bank_card_no, audit_status, audit_time, auditor_id, 
    auditor_name, audit_reason, remark, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_driver_payee
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_driver_payee
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDriverPayee" >
    insert into t_driver_payee (id, name, mobile,
      identity_no, bank_id, bank_card_no, 
      audit_status, audit_time, auditor_id, 
      auditor_name, audit_reason, remark, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{identityNo,jdbcType=VARCHAR}, #{bankId,jdbcType=BIGINT}, #{bankCardNo,jdbcType=VARCHAR}, 
      #{auditStatus,jdbcType=INTEGER}, #{auditTime,jdbcType=TIMESTAMP}, #{auditorId,jdbcType=BIGINT}, 
      #{auditorName,jdbcType=VARCHAR}, #{auditReason,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDriverPayee" keyProperty="id" useGeneratedKeys="true" >
    insert into t_driver_payee
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="mobile != null" >
        mobile,
      </if>
      <if test="identityNo != null" >
        identity_no,
      </if>
      <if test="bankId != null" >
        bank_id,
      </if>
      <if test="bankCardNo != null" >
        bank_card_no,
      </if>
      <if test="auditStatus != null" >
        audit_status,
      </if>
      <if test="auditTime != null" >
        audit_time,
      </if>
      <if test="auditorId != null" >
        auditor_id,
      </if>
      <if test="auditorName != null" >
        auditor_name,
      </if>
      <if test="auditReason != null" >
        audit_reason,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="identityNo != null" >
        #{identityNo,jdbcType=VARCHAR},
      </if>
      <if test="bankId != null" >
        #{bankId,jdbcType=BIGINT},
      </if>
      <if test="bankCardNo != null" >
        #{bankCardNo,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null" >
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditorId != null" >
        #{auditorId,jdbcType=BIGINT},
      </if>
      <if test="auditorName != null" >
        #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditReason != null" >
        #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDriverPayee" >
    update t_driver_payee
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="identityNo != null" >
        identity_no = #{identityNo,jdbcType=VARCHAR},
      </if>
      <if test="bankId != null" >
        bank_id = #{bankId,jdbcType=BIGINT},
      </if>
      <if test="bankCardNo != null" >
        bank_card_no = #{bankCardNo,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null" >
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null" >
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditorId != null" >
        auditor_id = #{auditorId,jdbcType=BIGINT},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditReason != null" >
        audit_reason = #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDriverPayee" >
    update t_driver_payee
    set name = #{name,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      identity_no = #{identityNo,jdbcType=VARCHAR},
      bank_id = #{bankId,jdbcType=BIGINT},
      bank_card_no = #{bankCardNo,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      auditor_id = #{auditorId,jdbcType=BIGINT},
      auditor_name = #{auditorName,jdbcType=VARCHAR},
      audit_reason = #{auditReason,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>