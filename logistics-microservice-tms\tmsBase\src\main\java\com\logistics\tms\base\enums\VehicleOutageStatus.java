package com.logistics.tms.base.enums;

/**
 * 车辆停运状态枚举
 * @Author: sj
 * @Date: 2019/7/29 13:39
 */
public enum VehicleOutageStatus {
    NULL(0,""),
    IN_OPERATION(1,"运营中"),
    OUT_SERVICE(2, "已停运"),
    TRANSFER(3,"过户"),
    SCRAP(4,"报废"),
    ;

    private Integer key;
    private String value;

    VehicleOutageStatus(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
