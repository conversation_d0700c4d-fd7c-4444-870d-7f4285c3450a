package com.logistics.management.webapi.api.feign.gpsfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/9 10:11
 */
@Data
public class GetDeductingHistoryResponseDto {
    @ApiModelProperty("扣减月份")
    private String deductingMonth="";
    @ApiModelProperty("总金额")
    private String totalFee="";
    @ApiModelProperty("未扣减费用合计")
    private String remainingDeductingFeeTotal="";
    @ApiModelProperty("扣减费用")
    private String deductingFee="";
    @ApiModelProperty("剩余未扣减费用")
    private String remainingDeductingFee="";
    @ApiModelProperty("最后修改人")
    private String lastModifiedBy="";
    @ApiModelProperty("最后修改时间")
    private String lastModifiedTime="";
}
