package com.logistics.tms.biz.shippingfreight;

import cn.hutool.core.collection.CollectionUtil;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.EnabledEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.shippingfreight.model.ShippingFreightRuleVehicleLengthSqlConditionModel;
import com.logistics.tms.controller.freightconfig.request.ConfigVehicleItemModel;
import com.logistics.tms.controller.freightconfig.request.ConfigVehicleReqModel;
import com.logistics.tms.controller.freightconfig.request.EnableOrForbidOrDeleteReqModel;
import com.logistics.tms.controller.freightconfig.request.ShippingFreightRuleIdReqModel;
import com.logistics.tms.controller.freightconfig.response.GetConfigVechicleDetailRespModel;
import com.logistics.tms.controller.freightconfig.response.GetConfigVechicleRespModel;
import com.logistics.tms.entity.TShippingFreightAddress;
import com.logistics.tms.entity.TShippingFreightRuleVehicleLength;
import com.logistics.tms.mapper.TShippingFreightAddressMapper;
import com.logistics.tms.mapper.TShippingFreightRuleVehicleLengthMapper;
import com.yelo.life.basicdata.api.base.enums.ValidTypeEnum;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ShippingFreightRuleVehicleLengthBiz {

    @Autowired
    TShippingFreightAddressMapper tShippingFreightAddressMapper;

    @Autowired
    TShippingFreightRuleVehicleLengthMapper tShippingFreightRuleVehicleLengthMapper;
    @Autowired
    CommonBiz commonBiz;


    /**
     * 零担运价管理启用禁用 v2.42
     */
    @Transactional
    public void enableOrForbid(EnableOrForbidOrDeleteReqModel reqModel) {
        String[] split = reqModel.getShippingFreightRuleId().split(",");
        List<Long> shippingFreightAddressIds = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
        //查询零担运费规则
        List<TShippingFreightAddress> tShippingFreightAddresses = tShippingFreightAddressMapper.selectByIds(shippingFreightAddressIds);
        if (CollectionUtil.isEmpty(tShippingFreightAddresses)) {
            throw new BizException(CarrierDataExceptionEnum.SHIPPING_FREIGHT_RULE_NOT_EXIST);
        }
        //更新零担运费规则
        ArrayList<TShippingFreightAddress> updateShippingFreightAddresses = new ArrayList<>();
        for (TShippingFreightAddress tShippingFreightAddress : tShippingFreightAddresses) {
            TShippingFreightAddress updateShippingFreightRule = new TShippingFreightAddress();
            updateShippingFreightRule.setId(tShippingFreightAddress.getId());
            if (CommonConstant.NEGATIVE_INTEGER_ONE.equals(reqModel.getOperateType())) {
                updateShippingFreightRule.setValid(ValidTypeEnum.INVALID.getKey());
            } else if (CommonConstant.INTEGER_ZERO.equals(reqModel.getOperateType())) {
                updateShippingFreightRule.setEnabled(EnabledEnum.DISABLED.getKey());
            } else if (CommonConstant.INTEGER_ONE.equals(reqModel.getOperateType())) {
                updateShippingFreightRule.setEnabled(EnabledEnum.ENABLED.getKey());
            }
            commonBiz.setBaseEntityModify(updateShippingFreightRule, BaseContextHandler.getUserName());
            updateShippingFreightAddresses.add(updateShippingFreightRule);
        }

        if (CollectionUtil.isNotEmpty(updateShippingFreightAddresses)) {
            tShippingFreightAddressMapper.batchUpdate(updateShippingFreightAddresses);
//            tShippingFreightAddressMapper.updateByPrimaryKeySelective(updateShippingFreightRule);
        }
    }


    /**
     * 新增编辑 车长运价 (多运价地址维度)
     */
    @Transactional
    public void configVehicles(List<ConfigVehicleReqModel> configVehicleReqModels) {
        Map<Long, List<ConfigVehicleReqModel>> configVehicleReqModelsMap = configVehicleReqModels.stream().collect(Collectors.groupingBy(ConfigVehicleReqModel::getShippingFreightRuleId));
        for (Map.Entry<Long, List<ConfigVehicleReqModel>> longListEntry : configVehicleReqModelsMap.entrySet()) {
            this.configVehicle(longListEntry.getValue());
        }
    }
    /**
     * 配置 车长运价 (单运价地址维度)
     */
    public void configVehicle(List<ConfigVehicleReqModel> configVehicleReqModels) {
        //查询下单的历史车次运价
        ShippingFreightRuleVehicleLengthSqlConditionModel sqlConditionModel = new ShippingFreightRuleVehicleLengthSqlConditionModel();
        sqlConditionModel.setShippingFreightRuleIds(Arrays.asList(configVehicleReqModels.get(0).getShippingFreightRuleId()));
        List<TShippingFreightRuleVehicleLength> tShippingFreightRuleVehicleLengths = tShippingFreightRuleVehicleLengthMapper.selectListByCondition(sqlConditionModel);

        //新增或更新数据
        this.addOrUpdateVehicle(configVehicleReqModels, tShippingFreightRuleVehicleLengths);

        //删除车长下带全部阶梯
        this.deleteVehicleExcludeOtherLength(configVehicleReqModels, tShippingFreightRuleVehicleLengths);

    }

    private void deleteVehicleExcludeOtherLength(List<ConfigVehicleReqModel> configVehicleReqModels, List<TShippingFreightRuleVehicleLength> tShippingFreightRuleVehicleLengths) {
        Map<BigDecimal, ConfigVehicleReqModel> reqModelVehicleLengthMap = configVehicleReqModels.stream().collect(Collectors.toMap(ConfigVehicleReqModel::getVehicleLength, Function.identity()));


        List<TShippingFreightRuleVehicleLength> deleteShippingFreightRuleVehicleLengths = new ArrayList<>();

        for (TShippingFreightRuleVehicleLength tShippingFreightRuleVehicleLength : tShippingFreightRuleVehicleLengths) {

            //如果数据库数据找不到对应入参 continue
            ConfigVehicleReqModel reqModel = reqModelVehicleLengthMap.get(tShippingFreightRuleVehicleLength.getVehicleLength());
            if (reqModel == null) {
                continue;
            }
            //如果当前排序数据库找的到 也continue
            List<ConfigVehicleItemModel> itemReqDtos = reqModel.getItemReqDtos();
            ConfigVehicleItemModel configVehicleItemModel = itemReqDtos.stream().filter(vehicleItemModel -> vehicleItemModel.getSort().equals(tShippingFreightRuleVehicleLength.getSort())).findFirst().orElse(null);
            if (configVehicleItemModel != null) {
                continue;
            }

            TShippingFreightRuleVehicleLength updateShippingFreightRuleVehicleLength = new TShippingFreightRuleVehicleLength();
            updateShippingFreightRuleVehicleLength.setId(tShippingFreightRuleVehicleLength.getId());
            updateShippingFreightRuleVehicleLength.setValid(ValidTypeEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(updateShippingFreightRuleVehicleLength, BaseContextHandler.getUserName());
            deleteShippingFreightRuleVehicleLengths.add(updateShippingFreightRuleVehicleLength);

        }


        if (CollectionUtil.isNotEmpty(deleteShippingFreightRuleVehicleLengths)) {
            tShippingFreightRuleVehicleLengthMapper.batchUpdate(deleteShippingFreightRuleVehicleLengths);
        }
    }

    /**
     * 编辑 车长运价
     * @param configVehicleReqModels
     */
    public void editVehicle(List<ConfigVehicleReqModel> configVehicleReqModels) {


        //查询下单的历史车次运价
        ShippingFreightRuleVehicleLengthSqlConditionModel sqlConditionModel = new ShippingFreightRuleVehicleLengthSqlConditionModel();
        sqlConditionModel.setShippingFreightRuleIds(Arrays.asList(configVehicleReqModels.get(0).getShippingFreightRuleId()));
        List<TShippingFreightRuleVehicleLength> tShippingFreightRuleVehicleLengths = tShippingFreightRuleVehicleLengthMapper.selectListByCondition(sqlConditionModel);

        //新增或更新数据
        this.addOrUpdateVehicle(configVehicleReqModels, tShippingFreightRuleVehicleLengths);
        //删除数据
        this.deleteVehicle(configVehicleReqModels, tShippingFreightRuleVehicleLengths);
    }

    private void deleteVehicle(List<ConfigVehicleReqModel> configVehicleReqModels, List<TShippingFreightRuleVehicleLength> tShippingFreightRuleVehicleLengths) {

        Map<BigDecimal, ConfigVehicleReqModel> reqModelVehicleLengthMap = configVehicleReqModels.stream().collect(Collectors.toMap(ConfigVehicleReqModel::getVehicleLength, Function.identity()));


        List<TShippingFreightRuleVehicleLength> deleteShippingFreightRuleVehicleLengths = new ArrayList<>();

        for (TShippingFreightRuleVehicleLength tShippingFreightRuleVehicleLength : tShippingFreightRuleVehicleLengths) {

            //如果数据库数据能匹配对应排序的入参 就continue
            ConfigVehicleReqModel reqModel = reqModelVehicleLengthMap.get(tShippingFreightRuleVehicleLength.getVehicleLength());
            if (reqModel != null) {
                List<ConfigVehicleItemModel> itemReqDtos = reqModel.getItemReqDtos();
                ConfigVehicleItemModel configVehicleItemModel = itemReqDtos.stream().filter(vehicleItemModel -> vehicleItemModel.getSort().equals(tShippingFreightRuleVehicleLength.getSort())).findFirst().orElse(null);
                if (configVehicleItemModel != null) {
                    continue;
                }
            }

            TShippingFreightRuleVehicleLength updateShippingFreightRuleVehicleLength = new TShippingFreightRuleVehicleLength();
            updateShippingFreightRuleVehicleLength.setId(tShippingFreightRuleVehicleLength.getId());
            updateShippingFreightRuleVehicleLength.setValid(ValidTypeEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(updateShippingFreightRuleVehicleLength, BaseContextHandler.getUserName());
            deleteShippingFreightRuleVehicleLengths.add(updateShippingFreightRuleVehicleLength);

        }


        if (CollectionUtil.isNotEmpty(deleteShippingFreightRuleVehicleLengths)) {
            tShippingFreightRuleVehicleLengthMapper.batchUpdate(deleteShippingFreightRuleVehicleLengths);
        }
    }

    private void addOrUpdateVehicle(List<ConfigVehicleReqModel> configVehicleReqModels, List<TShippingFreightRuleVehicleLength> tShippingFreightRuleVehicleLengths) {

        Map<String, TShippingFreightRuleVehicleLength> tShippingFreightRuleVehicleLengthsMap = tShippingFreightRuleVehicleLengths.stream()
                .collect(Collectors.toMap(tShippingFreightRuleVehicleLength -> tShippingFreightRuleVehicleLength.getVehicleLength() + "-" + tShippingFreightRuleVehicleLength.getSort(), Function.identity()));


        List<TShippingFreightRuleVehicleLength> addShippingFreightRuleVehicleLengths = new ArrayList<>();
        List<TShippingFreightRuleVehicleLength> updateShippingFreightRuleVehicleLengths = new ArrayList<>();

        for (ConfigVehicleReqModel configVehicleReqModel : configVehicleReqModels) {

            for (ConfigVehicleItemModel itemReqDto : configVehicleReqModel.getItemReqDtos()) {

                //获取原本数据库中的 对应车长对应排序的数据
                TShippingFreightRuleVehicleLength tShippingFreightRuleVehicleLength = tShippingFreightRuleVehicleLengthsMap.get(configVehicleReqModel.getVehicleLength() + "-" + itemReqDto.getSort());

                //如果数据为空 就新增
                if (tShippingFreightRuleVehicleLength == null) {
                    TShippingFreightRuleVehicleLength addShippingFreightRuleVehicleLength = new TShippingFreightRuleVehicleLength();
                    addShippingFreightRuleVehicleLength.setCountEnd(new BigDecimal(itemReqDto.getCountEnd()));
                    addShippingFreightRuleVehicleLength.setCountStart(new BigDecimal(itemReqDto.getCountStart()));
                    addShippingFreightRuleVehicleLength.setPrice(itemReqDto.getPrice());
                    addShippingFreightRuleVehicleLength.setPriceType(itemReqDto.getPriceType());
                    addShippingFreightRuleVehicleLength.setShippingFreightAddressId(configVehicleReqModel.getShippingFreightRuleId());
                    addShippingFreightRuleVehicleLength.setSort(itemReqDto.getSort());
                    //写死 件
                    addShippingFreightRuleVehicleLength.setUnit(CommonConstant.INTEGER_THREE);
                    addShippingFreightRuleVehicleLength.setVehicleLength(configVehicleReqModel.getVehicleLength());
                    commonBiz.setBaseEntityModify(addShippingFreightRuleVehicleLength, BaseContextHandler.getUserName());
                    addShippingFreightRuleVehicleLengths.add(addShippingFreightRuleVehicleLength);
                }
                //如果数据存在 就更新
                else {
                    TShippingFreightRuleVehicleLength updateShippingFreightRuleVehicleLength = new TShippingFreightRuleVehicleLength();
                    updateShippingFreightRuleVehicleLength.setId(tShippingFreightRuleVehicleLength.getId());
                    if (!itemReqDto.getCountEnd().equals(tShippingFreightRuleVehicleLength.getCountEnd())) {
                        updateShippingFreightRuleVehicleLength.setCountEnd(new BigDecimal(itemReqDto.getCountEnd()));
                    }
                    if (!itemReqDto.getCountStart().equals(tShippingFreightRuleVehicleLength.getCountStart())) {
                        updateShippingFreightRuleVehicleLength.setCountStart(new BigDecimal(itemReqDto.getCountStart()));
                    }
                    if (!itemReqDto.getPrice().equals(tShippingFreightRuleVehicleLength.getPrice())) {
                        updateShippingFreightRuleVehicleLength.setPrice(itemReqDto.getPrice());
                    }
                    if (!itemReqDto.getPriceType().equals(tShippingFreightRuleVehicleLength.getPriceType())) {
                        updateShippingFreightRuleVehicleLength.setPriceType(itemReqDto.getPriceType());
                    }
//                    if (!itemReqDto.getUnit().equals(tShippingFreightRuleVehicleLength.getUnit())) {
//                        updateShippingFreightRuleVehicleLength.setUnit(itemReqDto.getUnit());
//                    }
                    commonBiz.setBaseEntityModify(updateShippingFreightRuleVehicleLength, BaseContextHandler.getUserName());
                    updateShippingFreightRuleVehicleLengths.add(updateShippingFreightRuleVehicleLength);

                }

            }

        }

        if (CollectionUtil.isNotEmpty(addShippingFreightRuleVehicleLengths)) {
            tShippingFreightRuleVehicleLengthMapper.batchInsert(addShippingFreightRuleVehicleLengths);
        }
        if (CollectionUtil.isNotEmpty(updateShippingFreightRuleVehicleLengths)) {
            tShippingFreightRuleVehicleLengthMapper.batchUpdate(updateShippingFreightRuleVehicleLengths);
        }
    }




    /**
     * 获取车长费用配置
     */
    public GetConfigVechicleRespModel getConfigVehicle(ShippingFreightRuleIdReqModel reqModel) {

        TShippingFreightAddress tShippingFreightAddress = tShippingFreightAddressMapper.selectByPrimaryKey(reqModel.getShippingFreightRuleId());
        if (tShippingFreightAddress == null) {
            throw new BizException(CarrierDataExceptionEnum.SHIPPING_FREIGHT_RULE_NOT_EXIST);
        }

        ShippingFreightRuleVehicleLengthSqlConditionModel conditionModel = new ShippingFreightRuleVehicleLengthSqlConditionModel();
        conditionModel.setShippingFreightRuleIds(Collections.singletonList(reqModel.getShippingFreightRuleId()));
        List<TShippingFreightRuleVehicleLength> tShippingFreightRuleVehicleLengths = tShippingFreightRuleVehicleLengthMapper.selectListByCondition(conditionModel);

        List<GetConfigVechicleDetailRespModel> getConfigVechicleRespModels = new ArrayList<>();

        Map<BigDecimal, List<TShippingFreightRuleVehicleLength>> tShippingFreightRuleVehicleLengthsMap = tShippingFreightRuleVehicleLengths.stream().collect(Collectors.groupingBy(TShippingFreightRuleVehicleLength::getVehicleLength));
        for (Map.Entry<BigDecimal, List<TShippingFreightRuleVehicleLength>> bigDecimalListEntry : tShippingFreightRuleVehicleLengthsMap.entrySet()) {

            BigDecimal vehicleLength = bigDecimalListEntry.getKey();
            List<TShippingFreightRuleVehicleLength> value = bigDecimalListEntry.getValue();

            List<ConfigVehicleItemModel> itemReqDtos = new ArrayList<>();
            for (TShippingFreightRuleVehicleLength tShippingFreightRuleVehicleLength : value) {
                ConfigVehicleItemModel configVehicleItemModel = new ConfigVehicleItemModel();
                configVehicleItemModel.setCountEnd(tShippingFreightRuleVehicleLength.getCountEnd().stripTrailingZeros().toPlainString());
                configVehicleItemModel.setCountStart(tShippingFreightRuleVehicleLength.getCountStart().stripTrailingZeros().toPlainString());
                configVehicleItemModel.setPrice(tShippingFreightRuleVehicleLength.getPrice());
                configVehicleItemModel.setPriceType(tShippingFreightRuleVehicleLength.getPriceType());
                configVehicleItemModel.setSort(tShippingFreightRuleVehicleLength.getSort());
                configVehicleItemModel.setUnit(tShippingFreightRuleVehicleLength.getUnit());
                itemReqDtos.add(configVehicleItemModel);
            }

            GetConfigVechicleDetailRespModel respModel = new GetConfigVechicleDetailRespModel();
            respModel.setItemReqDtos(itemReqDtos);
            respModel.setVehicleLength(vehicleLength.toString());

            getConfigVechicleRespModels.add(respModel);
        }
        //根据车长排序
        getConfigVechicleRespModels = getConfigVechicleRespModels.stream().sorted(Comparator.comparing(getConfigVechicleDetailRespModel -> new BigDecimal(getConfigVechicleDetailRespModel.getVehicleLength()))).collect(Collectors.toList());

        GetConfigVechicleRespModel respModel = new GetConfigVechicleRespModel();
        respModel.setLoadAddress(tShippingFreightAddress.getFromProvinceName() + "" + tShippingFreightAddress.getFromCityName() + "" + tShippingFreightAddress.getFromAreaName());
        respModel.setUnloadAddress(tShippingFreightAddress.getToProvinceName() + "" + tShippingFreightAddress.getToCityName() + "" + tShippingFreightAddress.getToAreaName());
        respModel.setDetailRespDtos(getConfigVechicleRespModels);
        return respModel;


    }



}
