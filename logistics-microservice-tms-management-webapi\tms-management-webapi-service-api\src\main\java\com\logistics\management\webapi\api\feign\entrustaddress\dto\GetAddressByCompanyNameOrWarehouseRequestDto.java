package com.logistics.management.webapi.api.feign.entrustaddress.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/5/15 19:44
 */
@Data
public class GetAddressByCompanyNameOrWarehouseRequestDto extends AbstractPageForm<GetAddressByCompanyNameOrWarehouseRequestDto> {

    @ApiModelProperty("仓库名字")
    private String warehouse;

    @ApiModelProperty("收发货类型: 1发货 2收货")
    private String addressType;

    @ApiModelProperty("货主")
    private String companyEntrustId;
}
