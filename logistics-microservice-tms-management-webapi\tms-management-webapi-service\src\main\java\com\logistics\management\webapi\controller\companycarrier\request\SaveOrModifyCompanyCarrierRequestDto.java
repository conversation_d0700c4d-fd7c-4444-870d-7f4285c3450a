package com.logistics.management.webapi.controller.companycarrier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @Author: sj
 * @Date: 2019/9/27 17:42
 */
@Data
public class SaveOrModifyCompanyCarrierRequestDto {

    @ApiModelProperty("公司id")
    private String companyCarrierId;

    @ApiModelProperty(value = "公司名字")
    private String companyCarrierName;

    @ApiModelProperty("公司水印")
    @Size(max = 20, message = "水印名称最多20字符")
    private String companyWaterMark;

    @NotBlank(message = "类型不能为空")
    @Range(min = 1, max = 2, message = "企业类型只能为1或2")
    @ApiModelProperty(value = "类型: 1 企业，2 个人", required = true)
    private String type;

    @ApiModelProperty("营业执照路径相对")
    private String fileSrcPathTradingCertificateImage;

    @ApiModelProperty("营业执照有效期")
    private String tradingCertificateValidityTime;

    @ApiModelProperty("公司有效期是否永久: 0 否 1 是")
    private String tradingCertificateIsForever;

    @ApiModelProperty("营业执照是否后补: 0 否 1 是")
    private String tradingCertificateIsAmend;

    @ApiModelProperty("道路许可证号")
    private String roadTransportCertificateNumber;

    @ApiModelProperty("道路许可证号路径相对")
    private String fileSrcPathRoadTransportCertificateImage;

    @ApiModelProperty("道路许可证号有效期")
    private String roadTransportCertificateValidityTime;

    @ApiModelProperty("许可证号是否永久: 0 否 1 是")
    private String roadTransportCertificateIsForever;

    @ApiModelProperty("道路许可证是否后补: 0 否 1 是")
    private String roadTransportCertificateIsAmend;

    @ApiModelProperty("备注")
    @Size(max = 100, message = "备注最多100字")
    private String remark;

    @ApiModelProperty("车主账号id")
    private String carrierContactId;

    @ApiModelProperty("姓名")
    private String contactName;

    @ApiModelProperty("手机号")
    private String contactPhone;

    @ApiModelProperty("身份证人面像")
    private String identityFaceFile;

    @ApiModelProperty("身份证人面像图片是否后补 0否1是")
    private String identityFaceFileIsAmend;

    @ApiModelProperty("身份证国徽像")
    private String identityNationalFile;

    @ApiModelProperty("身份证国徽图片是否后补 0否1是")
    private String identityNationalFileIsAmend;

    @ApiModelProperty("身份证有效期")
    private String identityValidity;

    @ApiModelProperty("身份证是否永久: 0 否 1 是")
    private String identityIsForever;

    @ApiModelProperty("身份证号码")
    private String identityNumber;

    @ApiModelProperty("省ID")
    private String provinceId;

    @ApiModelProperty("省名字")
    private String provinceName;

    @ApiModelProperty("城市ID")
    private String cityId;

    @ApiModelProperty("城市名字")
    private String cityName;

    @ApiModelProperty("区ID")
    private String areaId;

    @ApiModelProperty("区名字")
    private String areaName;

    @ApiModelProperty("发证机关详情")
    private String certificationDepartmentDetail;

    @ApiModelProperty(value = "临时费用提交：0 否，1 是", required = true)
    @NotBlank(message = "请选择临时费用提交")
    private String commitOtherFee;

    @ApiModelProperty(value = "临时费用费点,0<=数值范围<=20", required = true)
    @NotBlank(message = "请填写临时费用费点")
    @DecimalMin(value = "0", message = "请正确填写临时费用费点")
    @DecimalMax(value = "20", message = "请正确填写临时费用费点")
    private String otherFeeTaxPoint;

    @ApiModelProperty(value = "运费费点,0<=数值范围<=20", required = true)
    @NotBlank(message = "请填写运费费点")
    @DecimalMin(value = "0", message = "请正确填写运费费点")
    @DecimalMax(value = "20", message = "请正确填写运费费点")
    private String freightTaxPoint;
}
