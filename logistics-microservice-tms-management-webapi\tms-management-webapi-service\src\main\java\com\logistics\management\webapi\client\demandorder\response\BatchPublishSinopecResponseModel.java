package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 获取需求单批量发布详情(中石化) - 响应model
 *
 * @author: wei.wang
 * @date: 2021/12/4
 */
@Data
@Accessors(chain = true)
public class BatchPublishSinopecResponseModel {

	@ApiModelProperty("货主公司名称")
	private String companyEntrustName;
	@ApiModelProperty("需求单数量")
	private Integer demandOrderCount;
	@ApiModelProperty("货物总计数量")
	private BigDecimal goodsAmountCount; //固定是吨

	@ApiModelProperty("需求单详情列表")
	private List<BatchPublishSinopecDetailResponseModel> sinopecDemands;
}
