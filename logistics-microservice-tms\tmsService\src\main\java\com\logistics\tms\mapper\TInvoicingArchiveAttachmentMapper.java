package com.logistics.tms.mapper;

import com.logistics.tms.controller.invoicingmanagement.response.GetInvoicingArchiveListResponseModel;
import com.logistics.tms.entity.TInvoicingArchiveAttachment;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2024/07/02
*/
@Mapper
public interface TInvoicingArchiveAttachmentMapper extends BaseMapper<TInvoicingArchiveAttachment> {

    List<GetInvoicingArchiveListResponseModel> getInvoicingArchiveList(@Param("invoicingId") Long invoicingId);

}