package com.logistics.management.webapi.api.feign.entrustsettlement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class RefundRequestDto {

    @ApiModelProperty("结算ID,逗号分隔")
    @NotBlank(message = "结算ID不能为空")
    private String settlementIds;
    @ApiModelProperty("回退原因")
    @NotBlank(message = "回退原因不能为空")
    private String refundReason;
}
