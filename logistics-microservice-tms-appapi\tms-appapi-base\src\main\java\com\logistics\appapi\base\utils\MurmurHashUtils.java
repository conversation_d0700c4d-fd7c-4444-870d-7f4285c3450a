package com.logistics.appapi.base.utils;

import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;

public class MurmurHashUtils {

    /**
     * MurMurHash算法, 性能高, 碰撞率低
     * @param str String
     * @return Long
     */
    private static Integer hash32(String str) {
        HashFunction hashFunction = Hashing.murmur3_32();
        return hashFunction.hashUnencodedChars(str).asInt();
    }

    private static Long hash128(String str) {
        HashFunction hashFunction = Hashing.murmur3_128();
        return hashFunction.hashUnencodedChars(str).asLong();
    }

    /**
     * 返回无符号murmur hash值
     * Long转换成无符号长整型（C中数据类型）
     * Java的数据类型long与C语言中无符号长整型uint64_t有区别，导致Java输出版本存在负数
     * @param key
     * @return
     */
    public static Long hash128Unsigned(String key) {
        Long hashCode = hash128(key);
        if (hashCode >= 0){
            return hashCode;
        }
        return hashCode & Long.MAX_VALUE;
    }

    public static Integer hash32Unsigned(String key) {
        Integer hashCode = hash32(key);
        if (hashCode >= 0){
            return hashCode;
        }
        return hashCode & Integer.MAX_VALUE;
    }

}
