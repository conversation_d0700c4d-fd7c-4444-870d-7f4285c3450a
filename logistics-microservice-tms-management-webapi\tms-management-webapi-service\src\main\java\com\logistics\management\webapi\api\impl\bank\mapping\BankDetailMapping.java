package com.logistics.management.webapi.api.impl.bank.mapping;

import com.logistics.management.webapi.api.feign.bank.dto.BankDetailResponseDto;
import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.tms.api.feign.bank.model.BankDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @Author: sj
 * @Date: 2019/7/11 13:38
 */
public class BankDetailMapping extends MapperMapping<BankDetailResponseModel,BankDetailResponseDto> {
    @Override
    public void configure() {
        BankDetailResponseModel source = this.getSource();
        BankDetailResponseDto destination = this.getDestination();

        if(source!=null){
            destination.setEnableLabel(EnabledEnum.getEnum(source.getEnable()).getValue());
            destination.setLastModifiedTime(DateUtils.dateToString(source.getLastModifiedTime(),DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
        }
    }
}
