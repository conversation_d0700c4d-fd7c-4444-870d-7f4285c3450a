package com.logistics.tms.api.feign.personalaccidentinsurance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/6/5 11:31
 */
@Data
public class GetPolicyNoPremiumByPolicyNoResponseModel {
    @ApiModelProperty("个人意外险表id")
    private Long personalAccidentInsuranceId;
    @ApiModelProperty("保单号")
    private String policyNumber;
    @ApiModelProperty("批单号")
    private String batchNumber;
    @ApiModelProperty("保单总额")
    private BigDecimal grossPremium;
    @ApiModelProperty("保单人数")
    private Integer policyPersonCount;
    @ApiModelProperty("个人意外险单据")
    private List<PersonalAccidentInsuranceTicketsResponseModel> ticketsList;
}
