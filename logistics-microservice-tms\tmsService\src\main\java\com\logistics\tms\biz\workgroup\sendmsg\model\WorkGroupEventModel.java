package com.logistics.tms.biz.workgroup.sendmsg.model;

import com.logistics.tms.base.enums.WorkGroupOrderNodeEnum;
import com.logistics.tms.base.enums.WorkGroupOrderTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

@Data
@Accessors(chain = true)
public class WorkGroupEventModel {
    private List<WorkGroupPushBoModel> workGroupPushBoModels;

    public WorkGroupEventModel setWorkGroupPushBoModels(Long orderId,
                                                        Integer orderSource,
                                                        Integer orderEntrustType,
                                                        String projectLabel,
                                                        WorkGroupOrderTypeEnum workGroupOrderTypeEnum,
                                                        WorkGroupOrderNodeEnum workGroupOrderNodeEnum) {
        if (this.workGroupPushBoModels == null) {
            this.workGroupPushBoModels = Lists.newArrayList();
        }
        this.workGroupPushBoModels.add(new WorkGroupPushBoModel()
                .setOrderId(orderId)
                .setOrderSource(orderSource)
                .setProjectLabel(projectLabel)
                .setEntrustTypeGroup(orderEntrustType)
                .setOrderType(workGroupOrderTypeEnum)
                .setOrderNode(workGroupOrderNodeEnum));
        return this;
    }
}
