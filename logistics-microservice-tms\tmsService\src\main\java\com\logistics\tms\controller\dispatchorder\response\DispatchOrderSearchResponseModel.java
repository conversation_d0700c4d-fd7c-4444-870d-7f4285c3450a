package com.logistics.tms.controller.dispatchorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DispatchOrderSearchResponseModel {
    @ApiModelProperty("调度单ID")
    private Long dispatchOrderId;
    @ApiModelProperty("调度单编码")
    private String dispatchOrderCode;
    @ApiModelProperty("运单数量")
    private Integer carrierOrderCount;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driverMobile;
    @ApiModelProperty("调度时间")
    private Date dispatchTime;
    @ApiModelProperty("调度人姓名")
    private String dispatchUserName;

    @ApiModelProperty("装货数")
    private Integer loadPointAmount;
    @ApiModelProperty("卸货数")
    private Integer unloadPointAmount;
    @ApiModelProperty("司机调度运费价格类型 1 单价 2 一口价")
    private Integer dispatchFreightFeeType;
    @ApiModelProperty("调整费用类型：1 加价，2 减价")
    private Integer adjustFeeType;
    @ApiModelProperty("调整价")
    private BigDecimal adjustFee;
    @ApiModelProperty("多装多卸价")
    private BigDecimal markupFee;
    private List<CarrierOrderModel> carrierOrderList;
}
