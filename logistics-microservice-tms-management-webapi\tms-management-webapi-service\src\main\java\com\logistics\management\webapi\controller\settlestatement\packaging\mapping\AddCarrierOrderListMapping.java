package com.logistics.management.webapi.controller.settlestatement.packaging.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.settlestatement.packaging.response.CarrierOrderGoodsModel;
import com.logistics.management.webapi.client.settlestatement.packaging.response.CarrierWaitSettleStatementListResponseModel;
import com.logistics.management.webapi.controller.settlestatement.packaging.response.CarrierAddCarrierOrderListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/17
 */
public class AddCarrierOrderListMapping extends MapperMapping<CarrierWaitSettleStatementListResponseModel, CarrierAddCarrierOrderListResponseDto> {
	@Override
	public void configure() {
		CarrierWaitSettleStatementListResponseModel source = getSource();
		CarrierAddCarrierOrderListResponseDto destination = getDestination();

		//车主名处理
		if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())) {
			destination.setCompanyCarrierName(source.getCarrierContactName() + " " + FrequentMethodUtils.encryptionData(source.getCarrierContactMobile(), EncodeTypeEnum.MOBILE_PHONE));
		}

		//拼接货物名
		StringBuilder goodsNameSB = new StringBuilder();
		List<CarrierOrderGoodsModel> goodsList = source.getGoodsList();
		for (int i = 0; i < goodsList.size(); i++) {
			if (i > CommonConstant.INTEGER_ZERO) {
				goodsNameSB.append(CommonConstant.SLASH);
			}
			goodsNameSB.append(goodsList.get(i).getGoodsName());
		}
		destination.setGoodsName(goodsNameSB.toString());

		//提货地址
		String loadAddress = Optional.ofNullable(source.getLoadProvinceName()).orElse("") +
				Optional.ofNullable(source.getLoadCityName()).orElse("") +
				Optional.ofNullable(source.getLoadAreaName()).orElse("") +
				Optional.ofNullable(source.getLoadDetailAddress()).orElse("");
		destination.setLoadAddress(loadAddress);

		//卸货地址
		String unloadAddress = Optional.ofNullable(source.getUnloadProvinceName()).orElse("") +
				Optional.ofNullable(source.getUnloadCityName()).orElse("") +
				Optional.ofNullable(source.getUnloadAreaName()).orElse("") +
				Optional.ofNullable(source.getUnloadDetailAddress()).orElse("");
		destination.setUnloadAddress(unloadAddress);
		//结算费用
		destination.setSettlementCost(source.getCarrierFee().setScale(2, RoundingMode.HALF_UP).toPlainString());

		//提货时间
		destination.setLoadTime(source.getLoadTime() != null ? DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(source.getLoadTime()) : CommonConstant.BLANK_TEXT);
		//签收时间
		destination.setSignTime(source.getSignTime() != null ? DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(source.getSignTime()) : CommonConstant.BLANK_TEXT);
	}
}
