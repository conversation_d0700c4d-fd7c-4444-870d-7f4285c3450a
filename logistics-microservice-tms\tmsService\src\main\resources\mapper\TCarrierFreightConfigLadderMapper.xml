<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierFreightConfigLadderMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierFreightConfigLadder" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="mode_id" property="modeId" jdbcType="BIGINT" />
    <result column="price_mode" property="priceMode" jdbcType="INTEGER" />
    <result column="ladder_mode" property="ladderMode" jdbcType="INTEGER" />
    <result column="ladder_level" property="ladderLevel" jdbcType="INTEGER" />
    <result column="ladder_pid" property="ladderPid" jdbcType="BIGINT" />
    <result column="ladder_type" property="ladderType" jdbcType="INTEGER" />
    <result column="ladder_from" property="ladderFrom" jdbcType="DECIMAL" />
    <result column="ladder_to" property="ladderTo" jdbcType="DECIMAL" />
    <result column="ladder_unit" property="ladderUnit" jdbcType="INTEGER" />
    <result column="unit_price" property="unitPrice" jdbcType="DECIMAL" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, mode_id, price_mode, ladder_mode, ladder_level, ladder_pid, ladder_type, ladder_from, 
    ladder_to, ladder_unit, unit_price, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_carrier_freight_config_ladder
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_carrier_freight_config_ladder
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierFreightConfigLadder" >
    insert into t_carrier_freight_config_ladder (id, mode_id, price_mode, 
      ladder_mode, ladder_level, ladder_pid, 
      ladder_type, ladder_from, ladder_to, 
      ladder_unit, unit_price, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{modeId,jdbcType=BIGINT}, #{priceMode,jdbcType=INTEGER}, 
      #{ladderMode,jdbcType=INTEGER}, #{ladderLevel,jdbcType=INTEGER}, #{ladderPid,jdbcType=BIGINT}, 
      #{ladderType,jdbcType=INTEGER}, #{ladderFrom,jdbcType=DECIMAL}, #{ladderTo,jdbcType=DECIMAL}, 
      #{ladderUnit,jdbcType=INTEGER}, #{unitPrice,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierFreightConfigLadder" >
    insert into t_carrier_freight_config_ladder
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="modeId != null" >
        mode_id,
      </if>
      <if test="priceMode != null" >
        price_mode,
      </if>
      <if test="ladderMode != null" >
        ladder_mode,
      </if>
      <if test="ladderLevel != null" >
        ladder_level,
      </if>
      <if test="ladderPid != null" >
        ladder_pid,
      </if>
      <if test="ladderType != null" >
        ladder_type,
      </if>
      <if test="ladderFrom != null" >
        ladder_from,
      </if>
      <if test="ladderTo != null" >
        ladder_to,
      </if>
      <if test="ladderUnit != null" >
        ladder_unit,
      </if>
      <if test="unitPrice != null" >
        unit_price,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="modeId != null" >
        #{modeId,jdbcType=BIGINT},
      </if>
      <if test="priceMode != null" >
        #{priceMode,jdbcType=INTEGER},
      </if>
      <if test="ladderMode != null" >
        #{ladderMode,jdbcType=INTEGER},
      </if>
      <if test="ladderLevel != null" >
        #{ladderLevel,jdbcType=INTEGER},
      </if>
      <if test="ladderPid != null" >
        #{ladderPid,jdbcType=BIGINT},
      </if>
      <if test="ladderType != null" >
        #{ladderType,jdbcType=INTEGER},
      </if>
      <if test="ladderFrom != null" >
        #{ladderFrom,jdbcType=DECIMAL},
      </if>
      <if test="ladderTo != null" >
        #{ladderTo,jdbcType=DECIMAL},
      </if>
      <if test="ladderUnit != null" >
        #{ladderUnit,jdbcType=INTEGER},
      </if>
      <if test="unitPrice != null" >
        #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierFreightConfigLadder" >
    update t_carrier_freight_config_ladder
    <set >
      <if test="modeId != null" >
        mode_id = #{modeId,jdbcType=BIGINT},
      </if>
      <if test="priceMode != null" >
        price_mode = #{priceMode,jdbcType=INTEGER},
      </if>
      <if test="ladderMode != null" >
        ladder_mode = #{ladderMode,jdbcType=INTEGER},
      </if>
      <if test="ladderLevel != null" >
        ladder_level = #{ladderLevel,jdbcType=INTEGER},
      </if>
      <if test="ladderPid != null" >
        ladder_pid = #{ladderPid,jdbcType=BIGINT},
      </if>
      <if test="ladderType != null" >
        ladder_type = #{ladderType,jdbcType=INTEGER},
      </if>
      <if test="ladderFrom != null" >
        ladder_from = #{ladderFrom,jdbcType=DECIMAL},
      </if>
      <if test="ladderTo != null" >
        ladder_to = #{ladderTo,jdbcType=DECIMAL},
      </if>
      <if test="ladderUnit != null" >
        ladder_unit = #{ladderUnit,jdbcType=INTEGER},
      </if>
      <if test="unitPrice != null" >
        unit_price = #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierFreightConfigLadder" >
    update t_carrier_freight_config_ladder
    set mode_id = #{modeId,jdbcType=BIGINT},
      price_mode = #{priceMode,jdbcType=INTEGER},
      ladder_mode = #{ladderMode,jdbcType=INTEGER},
      ladder_level = #{ladderLevel,jdbcType=INTEGER},
      ladder_pid = #{ladderPid,jdbcType=BIGINT},
      ladder_type = #{ladderType,jdbcType=INTEGER},
      ladder_from = #{ladderFrom,jdbcType=DECIMAL},
      ladder_to = #{ladderTo,jdbcType=DECIMAL},
      ladder_unit = #{ladderUnit,jdbcType=INTEGER},
      unit_price = #{unitPrice,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>