package com.logistics.tms.base.enums;

import lombok.Getter;

/**
 * @Author: sj
 * @Date: 2019/7/26 11:09
 */
@Getter
public enum VehicleCategoryEnum {
    NULL(0,""),
    TRACTOR(1,"牵引车"),
    TRAILER(2, "挂车"),
    WHOLE(3, "一体车"),
    ;

    private Integer key;
    private String value;

    VehicleCategoryEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }
}
