package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TPersonalAccidentInsurance extends BaseEntity {
    /**
    * 保险公司表id
    */
    @ApiModelProperty("保险公司表id")
    private Long insuranceCompanyId;

    /**
    * 保单id
    */
    @ApiModelProperty("保单id")
    private Long personalAccidentInsuranceId;

    /**
    * 保单类型：1 保单，2 批单
    */
    @ApiModelProperty("保单类型：1 保单，2 批单")
    private Integer type;

    /**
    * 保单号
    */
    @ApiModelProperty("保单号")
    private String policyNumber;

    /**
    * 批单号
    */
    @ApiModelProperty("批单号")
    private String batchNumber;

    /**
    * 保费总额
    */
    @ApiModelProperty("保费总额")
    private BigDecimal grossPremium;

    /**
    * 保单人数
    */
    @ApiModelProperty("保单人数")
    private Integer policyPersonCount;

    /**
    * 保险生效时间
    */
    @ApiModelProperty("保险生效时间")
    private Date startTime;

    /**
    * 保险截止时间
    */
    @ApiModelProperty("保险截止时间")
    private Date endTime;

    /**
    * 添加人id
    */
    @ApiModelProperty("添加人id")
    private Long addUserId;

    /**
    * 添加人名字
    */
    @ApiModelProperty("添加人名字")
    private String addUserName;

    /**
    * 车辆添加来源：1新增 2导入
    */
    @ApiModelProperty("车辆添加来源：1新增 2导入")
    private Integer source;
}