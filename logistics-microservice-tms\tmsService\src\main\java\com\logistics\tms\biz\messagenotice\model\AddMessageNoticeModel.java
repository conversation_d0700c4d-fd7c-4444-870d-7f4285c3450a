package com.logistics.tms.biz.messagenotice.model;

import com.logistics.tms.base.enums.MessageNoticeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/5 11:14
 */
@Data
public class AddMessageNoticeModel {

    /**
     * 消息通知枚举
     */
    @ApiModelProperty("消息通知枚举")
    private MessageNoticeEnum messageNoticeEnum;

    /**
     * id：根据类型确定表id
     */
    @ApiModelProperty("id：根据类型确定表id")
    private Long objectId;

    /**
     * code：根据类型确定表code
     */
    @ApiModelProperty("code：根据类型确定表code")
    private String objectCode;

    /**
     * 消息体
     */
    @ApiModelProperty("消息体")
    private String messageBody;

    /**
     * 消息接收方（后台：空，前台：车主id）
     */
    @ApiModelProperty("消息接收方（后台：空，前台：车主id）")
    private List<Long> messageReceiverList;

    /**
     * 消息推送人
     */
    @ApiModelProperty("消息推送人")
    private String messagePusher;
}
