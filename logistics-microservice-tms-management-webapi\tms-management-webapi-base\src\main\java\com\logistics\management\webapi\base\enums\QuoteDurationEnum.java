package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/4/26 13:20
 */
@Getter
@AllArgsConstructor
public enum QuoteDurationEnum {

    DEFAULT(0, 0,""),
    THIRTY(1, 30,"30分钟"),
    SIXTY(2, 60,"60分钟"),
    NINETY(3, 90,"90分钟"),
    ;

    private final Integer key;
    private final Integer value;
    private final String remark;

    public static QuoteDurationEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

    public static QuoteDurationEnum getEnumByStringKey(String key) {
        return Stream.of(values())
                .filter(f -> f.getKey().toString().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

}
