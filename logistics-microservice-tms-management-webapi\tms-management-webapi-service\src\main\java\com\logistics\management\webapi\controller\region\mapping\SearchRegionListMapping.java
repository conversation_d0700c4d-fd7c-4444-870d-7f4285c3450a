package com.logistics.management.webapi.controller.region.mapping;

import com.logistics.management.webapi.base.enums.EnabledEnum;
import com.logistics.management.webapi.client.region.response.SearchRegionResponseModel;
import com.logistics.management.webapi.controller.region.response.SearchRegionResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * <AUTHOR>
 * @date 2021/10/21 18:28
 */
public class SearchRegionListMapping extends MapperMapping<SearchRegionResponseModel, SearchRegionResponseDto> {
    @Override
    public void configure() {
        SearchRegionResponseModel source = this.getSource();
        SearchRegionResponseDto destination = this.getDestination();

        destination.setEnabledLabel(EnabledEnum.getEnum(source.getEnabled()).getValue());
        destination.setContactName(source.getContactName() + " " +source.getContactPhone());
    }
}