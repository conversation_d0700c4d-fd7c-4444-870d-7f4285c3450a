package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchListStatisticsResponseDto {

    @ApiModelProperty("全部")
    private String allCount = "0";
    @ApiModelProperty("待发布")
    private String waitPublishCount ="0";
    @ApiModelProperty("待调度")
    private String waitDispatchCount = "0";
    @ApiModelProperty("部分调度")
    private String partDispatchCount = "0";
    @ApiModelProperty("调度完成")
    private String completeDispatchCount = "0";
    @ApiModelProperty("待签收数量")
    private String waitSignedAccount="0";
    @ApiModelProperty("已签收数量")
    private String signedAccount="0";
    @ApiModelProperty("已取消")
    private String cancelCount = "0";
}
