package com.logistics.management.webapi.controller.carrierorder;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.leyi.auth.service.client.common.PlatformProdEnums;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.base.utils.ConversionUtils;
import com.logistics.management.webapi.base.utils.MurmurHashUtils;
import com.logistics.management.webapi.base.utils.WordBarCodeUtils;
import com.logistics.management.webapi.client.auth.AuthTokenServiceApi;
import com.logistics.management.webapi.client.auth.request.CreateToken;
import com.logistics.management.webapi.client.auth.response.TokenModule;
import com.logistics.management.webapi.client.carrierorder.CarrierOrderClient;
import com.logistics.management.webapi.client.carrierorder.CarrierOrderLeYiClient;
import com.logistics.management.webapi.client.carrierorder.request.*;
import com.logistics.management.webapi.client.carrierorder.response.*;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.OCRClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.response.OcrPictureRequestModel;
import com.logistics.management.webapi.controller.carrierorder.mapping.*;
import com.logistics.management.webapi.controller.carrierorder.request.*;
import com.logistics.management.webapi.controller.carrierorder.response.*;
import com.yelo.basicdata.api.feign.file.FileServiceApi;
import com.yelo.basicdata.api.feign.file.model.PdfWriteToImgRequestModel;
import com.yelo.basicdata.api.feign.file.model.PdfWriteToImgResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2023/11/13 9:19
 */
@Slf4j
@Api(value = "云盘运单管理", tags = "云盘运单管理")
@RestController
public class CarrierOrderForLeYiController {

    @Resource
    private CarrierOrderClient carrierOrderClient;
    @Resource
    private CarrierOrderLeYiClient carrierOrderLeYiClient;
    @Resource
    private FileServiceApi fileServiceApi;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private OCRClient ocrClient;
    @Resource
    private AuthTokenServiceApi authTokenServiceApi;

    @ApiOperation(value = "查询云盘运单列表 v2.6.8", tags = "v2.6.8")
    @PostMapping(value = "/api/carrierOrderManagement/searchCarrierOrderListForLeYi")
    public Result<PageInfo<SearchCarrierOrderListForLeYiResponseDto>> searchCarrierOrderListForLeYi(@RequestBody SearchCarrierOrderListForLeYiRequestDto requestDto) {
        //判断入参
        if (ListUtils.isNotEmpty(requestDto.getCarrierOrderCodeList()) && requestDto.getCarrierOrderCodeList().size() > CommonConstant.INTEGER_ONE_THOUSAND_HUNDRED) {
            throw new BizException(ManagementWebApiExceptionEnum.ORDER_BATCH_SEARCH_MAX_ONE_THOUSAND);
        }
        if (ListUtils.isNotEmpty(requestDto.getDemandOrderCodeList()) && requestDto.getDemandOrderCodeList().size() > CommonConstant.INTEGER_ONE_THOUSAND_HUNDRED) {
            throw new BizException(ManagementWebApiExceptionEnum.ORDER_BATCH_SEARCH_MAX_ONE_THOUSAND);
        }

        Result<PageInfo<SearchCarrierOrderListForLeYiResponseModel>> pageInfoResult = carrierOrderLeYiClient.searchCarrierOrderListForLeYi(MapperUtils.mapper(requestDto, SearchCarrierOrderListForLeYiRequestModel.class));
        pageInfoResult.throwException();
        PageInfo pageInfo = pageInfoResult.getData();
        List<SearchCarrierOrderListForLeYiResponseDto> searchCarrierOrderListRequestDtos = MapperUtils.mapper(pageInfo.getList(),SearchCarrierOrderListForLeYiResponseDto.class,new CarrierOrderSearchListForLeYiMapping());
        pageInfo.setList(searchCarrierOrderListRequestDtos);
        return Result.success(pageInfo);
    }

    @ApiOperation(value = "导出云盘运单列表", tags = "3.15.0")
    @PostMapping(value = "/api/carrierOrderManagement/exportCarrierOrderListForLeYi")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportCarrierOrderListForLeYi(@RequestBody SearchCarrierOrderListForLeYiRequestDto requestDto, HttpServletResponse response) {
        SearchCarrierOrderListForLeYiRequestModel requestModel = MapperUtils.mapper(requestDto, SearchCarrierOrderListForLeYiRequestModel.class);
        Result<List<SearchCarrierOrderListForLeYiResponseModel>> result = carrierOrderLeYiClient.exportCarrierOrderListForLeYi(requestModel);
        result.throwException();

        List<SearchCarrierOrderListForLeYiResponseDto> list = MapperUtils.mapper(result.getData(), SearchCarrierOrderListForLeYiResponseDto.class, new CarrierOrderSearchListForLeYiMapping());
        String fileName = "运单列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM);
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, list, false, SearchCarrierOrderListForLeYiResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

    @ApiOperation(value = "导出云盘运单临时费用列表", tags = "3.15.0")
    @GetMapping(value = "/api/carrierOrderManagement/exportCarrierOrderOtherFeeForLeYi")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportCarrierOrderOtherFeeForLeYi(SearchCarrierOrderListForLeYiRequestDto requestDto, HttpServletResponse response) {
        SearchCarrierOrderListForLeYiRequestModel requestModel = MapperUtils.mapper(requestDto, SearchCarrierOrderListForLeYiRequestModel.class);
        Result<List<SearchCarrierOrderListForLeYiResponseModel>> result = carrierOrderLeYiClient.exportCarrierOrderListForLeYi(requestModel);
        result.throwException();

        List<ExportCarrierOrderOtherFeeForLeYiResponseDto> list = MapperUtils.mapper(result.getData(), ExportCarrierOrderOtherFeeForLeYiResponseDto.class, new ExportCarrierOrderOtherFeeForLeYiMapping());
        String fileName = "运单杂费导出列表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM);
        try {
            YeloExcelUtils.doExportMoreThreadByOneSheet(response, list, false, ExportCarrierOrderOtherFeeForLeYiResponseDto.class, fileName);
        } catch (Exception e) {
            return;
        }
    }

    /**
     * 云盘待审核车辆信息
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "云盘待审核车辆信息")
    @PostMapping(value = "/api/carrierOrderManagement/getWaitAuditVehicleCountInfoForLeYi")
    public Result<WaitAuditVehicleInfoResponseDto> getWaitAuditVehicleCountInfoForLeYi(@RequestBody WaitAuditVehicleInfoRequestDto requestDto) {
        Result<WaitAuditVehicleInfoResponseModel> result = carrierOrderLeYiClient.getWaitAuditVehicleCountInfoForLeYi(MapperUtils.mapperNoDefault(requestDto, WaitAuditVehicleInfoRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),WaitAuditVehicleInfoResponseDto.class));
    }

    /**
     * 云盘运单详情页
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "云盘运单详情页", tags = "1.3.7")
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderDetailForLeYi")
    public Result<CarrierOrderDetailForLeYiResponseDto> carrierOrderDetailForLeYi(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<CarrierOrderDetailForLeYiResponseModel> result = carrierOrderLeYiClient.carrierOrderDetailForLeYi(MapperUtils.mapper(requestDto, CarrierOrderDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),CarrierOrderDetailForLeYiResponseDto.class,new CarrierOrderDetailForLeYiMapping()));
    }

    /**
     * 云盘运单获取客户单号详情
     *
     * @param requestDto 运单号
     * @return 客户单号详情
     */
    @ApiOperation(value = "云盘运单获取客户单号详情 v1.1.3")
    @PostMapping(value = "/api/carrierOrderManagement/getCarrierOrderOrders")
    public Result<List<CarrierOrderOrdersResponseDto>> getCarrierOrderOrders(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<List<CarrierOrderOrdersResponseModel>> result = carrierOrderLeYiClient.getCarrierOrderOrders(MapperUtils.mapper(requestDto, CarrierOrderIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierOrderOrdersResponseDto.class, new GetCarrierOrderOrdersMapping()));
    }

    /**
     * 云盘运单修改地址详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "云盘运单批量修改地址详情 v1.2.3")
    @PostMapping(value = "/api/carrierOrderManagement/updateCarrierOrderUnloadAddressDetail")
    public Result<UpdateCarrierOrderUnloadAddressDetailResponseDto> updateCarrierOrderUnloadAddressDetail(@RequestBody @Valid UpdateCarrierOrderUnloadAddressDetailRequestDto requestDto) {
        UpdateCarrierOrderUnloadAddressDetailResponseDto responseDto = new UpdateCarrierOrderUnloadAddressDetailResponseDto();
        Result<List<UpdateUnloadAddressDetailResponseModel>> result = carrierOrderLeYiClient.updateCarrierOrderUnloadAddressDetail(MapperUtils.mapper(requestDto, CarrierOrderUpUnloadAddrRequestModel.class));
        result.throwException();
        responseDto.setCarrierOrderId(result.getData().stream().map(model -> ConverterUtils.toString(model.getCarrierOrderId())).collect(Collectors.toList()));
        return Result.success(responseDto);
    }

    /**
     * 云盘运单确认修改地址
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "云盘运单批量确认修改地址 v1.2.3")
    @PostMapping(value = "/api/carrierOrderManagement/updateCarrierOrderUnloadAddressConfirm")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> updateCarrierOrderUnloadAddressConfirm(@RequestBody @Valid UpdateCarrierOrderUnloadAddressConfirmRequestDto requestDto) {
        return carrierOrderLeYiClient.updateCarrierOrderUnloadAddressConfirm(MapperUtils.mapper(requestDto, UpdateCarrierOrderUnloadAddressConfirmRequestModel.class));
    }

    /**
     * 云盘运单纠错详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "云盘运单纠错详情", tags = "1.3.3")
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderCorrectDetail")
    public Result<CarrierOrderCorrectDetailResponseDto> carrierOrderCorrectDetail(@RequestBody @Valid CarrierOrderCorrectDetailRequestDto requestDto) {
        Result<CarrierOrderCorrectDetailResponseModel> result = carrierOrderLeYiClient.carrierOrderCorrectDetail(MapperUtils.mapper(requestDto, CarrierOrderCorrectDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        if(ListUtils.isNotEmpty(result.getData().getStockInTicketsList())) {
            sourceSrcList.addAll(result.getData().getStockInTicketsList());
        }
        if(ListUtils.isNotEmpty(result.getData().getSignTicketsList())) {
            sourceSrcList.addAll(result.getData().getSignTicketsList());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(), CarrierOrderCorrectDetailResponseDto.class, new CarrierOrderCorrectDetailMapping(configKeyConstant.fileAccessAddress, imageMap, requestDto.getOperateType())));
    }

    /**
     * 云盘运单确认纠错
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "云盘运单确认纠错 v1.2.2")
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderCorrectConfirm")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> carrierOrderCorrectConfirm(@RequestBody @Valid CarrierOrderCorrectConfirmRequestDto requestDto) {
        //校验车主价格类型
        if (StringUtils.isNotBlank(requestDto.getCarrierFreightType())
                && !CommonConstant.ZERO.equals(requestDto.getCarrierFreightType())) {
            if (!CommonConstant.ONE.equals(requestDto.getCarrierFreightType())
                    && !CommonConstant.TWO.equals(requestDto.getCarrierFreightType())) {
                throw new BizException(ManagementWebApiExceptionEnum.CARRIER_PRICE_TYPE_ERROR);
            }
            if (StringUtils.isBlank(requestDto.getCarrierFreight())){
                throw new BizException(ManagementWebApiExceptionEnum.PRICE_ERROR);
            }

            //校验车主价格
            try {
                if (new BigDecimal(requestDto.getCarrierFreight()).compareTo(CommonConstant.BIG_DECIMAL_ZERO) < CommonConstant.INTEGER_ZERO ||
                        new BigDecimal(requestDto.getCarrierFreight()).compareTo(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_THOUSAND) > CommonConstant.INTEGER_ZERO) {
                    throw new BizException(ManagementWebApiExceptionEnum.PRICE_ERROR);
                }
            } catch (BizException e) {
                throw new BizException(ManagementWebApiExceptionEnum.PRICE_ERROR);
            }
        }else{
            requestDto.setCarrierFreightType(null);
            requestDto.setCarrierFreight(null);
        }
        return carrierOrderLeYiClient.carrierOrderCorrectConfirm(MapperUtils.mapper(requestDto, CarrierOrderCorrectConfirmRequestModel.class));
    }

    /**
     * 运单放空详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "运单放空详情 v1.2.2")
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderEmptyDetail")
    public Result<CarrierOrderEmptyDetailResponseDto> carrierOrderEmptyDetail(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<CarrierOrderEmptyDetailResponseModel> result = carrierOrderLeYiClient.carrierOrderEmptyDetail(MapperUtils.mapper(requestDto, CarrierOrderDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierOrderEmptyDetailResponseDto.class));
    }

    /**
     * 确认放空
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "确认放空 v1.2.2")
    @PostMapping(value = "/api/carrierOrderManagement/confirmEmpty")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> confirmEmpty(@RequestBody @Valid CarrierOrderEmptyRequestDto requestDto) {
        carrierOrderLeYiClient.confirmEmpty(MapperUtils.mapper(requestDto, CarrierOrderEmptyRequestModel.class)).throwException();
        return Result.success(true);
    }

    /**
     * 复制运单信息
     *
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "复制运单信息 v1.2.3")
    @PostMapping(value = "/api/carrierOrderManagement/copyCarrierOrder")
    public Result<CopyCarrierOrderResponseDto> copyCarrierOrder(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        Result<CopyCarrierOrderResponseModel> result = carrierOrderLeYiClient.copyCarrierOrder(MapperUtils.mapper(requestDto, CarrierOrderIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CopyCarrierOrderResponseDto.class, new CopyCarrierOrderMapping()));
    }

    /**
     * 云盘物流看板-平均物流费用
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "云盘物流看板-平均物流费用 v1.1.1")
    @PostMapping(value = "/api/carrierOrderManagement/logisticsCostStatistics")
    public Result<List<LogisticsCostStatisticsResponseDto>> logisticsCostStatistics(@RequestBody @Valid LogisticsCostStatisticsRequestDto requestDto) {
        Result<List<LogisticsCostStatisticsResponseModel>> result = carrierOrderLeYiClient.logisticsCostStatistics(MapperUtils.mapper(requestDto, LogisticsCostStatisticsRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), LogisticsCostStatisticsResponseDto.class));
    }

    /**
     * 云盘物流看板-平均提货时效
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "云盘物流看板-平均提货时效 v1.1.1")
    @PostMapping(value = "/api/carrierOrderManagement/logisticsLoadValidityStatistics")
    public Result<List<LogisticsLoadValidityStatisticsResponseDto>> logisticsLoadValidityStatistics(@RequestBody @Valid LogisticsLoadValidityStatisticsRequestDto requestDto) {
        Result<List<LogisticsLoadValidityStatisticsResponseModel>> result = carrierOrderLeYiClient.logisticsLoadValidityStatistics(MapperUtils.mapper(requestDto, LogisticsLoadValidityStatisticsRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), LogisticsLoadValidityStatisticsResponseDto.class));
    }

    /**
     * 云盘物流看板-待提货、待纠错
     * @return
     */
    @ApiOperation(value = "云盘物流看板-待提货、待纠错 v1.1.4")
    @PostMapping(value = "/api/carrierOrderManagement/waitCorrectStatistics")
    public Result<WaitCorrectStatisticsResponseDto> waitCorrectStatistics() {
        Result<WaitCorrectStatisticsResponseModel> result = carrierOrderLeYiClient.waitCorrectStatistics();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), WaitCorrectStatisticsResponseDto.class, new WaitCorrectStatisticsResponseMapping()));
    }

    /**
     * 云盘运单签收详情
     *
     * @param requestDto 运单集合
     * @return 签收详情
     */
    @ApiOperation(value = "云盘运单签收详情", tags = "1.3.5")
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderListBeforeSignUpForLeyi")
    public Result<List<CarrierOrderListBeforeSignUpForLeyiResponseDto>> carrierOrderListBeforeSignUpForLeyi(@RequestBody @Valid CarrierOrderListBeforeSignUpRequestDto requestDto) {
        Result<List<CarrierOrderListBeforeSignUpForLeyiResponseModel>> result = carrierOrderLeYiClient.carrierOrderListBeforeSignUpForLeyi(MapperUtils.mapper(requestDto, CarrierOrderListBeforeSignUpRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierOrderListBeforeSignUpForLeyiResponseDto.class, new CarrierOrderListBeforeSignUpForLeyiMapping()));
    }

    /**
     * 云盘运单签收
     *
     * @param requestDto 签收信息
     * @return 操作结果
     */
    @ApiOperation(value = "云盘运单签收 v1.2.2")
    @PostMapping(value = "/api/carrierOrderManagement/carrierOrderSignUpForLeyi")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> carrierOrderSignUpForLeyi(@RequestBody @Valid CarrierOrderConfirmSignUpForLeyiRequestDto requestDto) {
        Result<Boolean> result = carrierOrderLeYiClient.carrierOrderSignUpForLeyi(MapperUtils.mapper(requestDto, CarrierOrderConfirmSignUpForLeyiRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 云盘运单详情编辑费用-详情查询
     *
     * @param requestDto 运单ID
     * @return 货物详情
     */
    @ApiOperation(value = "云盘运单详情编辑费用-详情查询 v1.2.2")
    @PostMapping(value = "/api/carrierOrderManagement/editCarrierOrderCostDetail")
    public Result<EditCarrierOrderCostDetailResponseDto> editCarrierOrderCostDetail(@RequestBody @Valid EditCarrierOrderCostDetailRequestDto requestDto) {
        Result<EditCarrierOrderCostDetailResponseModel> result = carrierOrderLeYiClient.editCarrierOrderCostDetail(MapperUtils.mapper(requestDto, EditCarrierOrderCostDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), EditCarrierOrderCostDetailResponseDto.class, new EditCarrierOrderCostDetailMapping()));
    }

    /**
     * 云盘运单详情编辑费用
     *
     * @param requestDto 费用信息
     * @return 操作结果
     */
    @ApiOperation(value = "云盘运单详情编辑费用 v1.2.2")
    @PostMapping(value = "/api/carrierOrderManagement/editCarrierOrderCost")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> editCarrierOrderCost(@RequestBody @Valid EditCarrierOrderCostRequestDto requestDto) {
        Result<Boolean> result = carrierOrderLeYiClient.editCarrierOrderCost(MapperUtils.mapper(requestDto, EditCarrierOrderCostRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 根据车主id查询有效的云盘运单（排除已取消、已放空）
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "根据车主id查询有效的云盘运单（排除已取消、已放空）", tags = "1.2.5")
    @PostMapping(value = "/api/carrierOrderManagement/getValidCarrierOrder")
    public Result<PageInfo<GetValidCarrierOrderResponseDto>> getValidCarrierOrder(@RequestBody @Valid GetValidCarrierOrderRequestDto requestDto) {
        //其他车主，车主id必填
        if (CommonConstant.TWO.equals(requestDto.getIsOurCompany()) && StringUtils.isBlank(requestDto.getCompanyCarrierId())){
            throw new BizException(ManagementWebApiExceptionEnum.COMPANY_CARRIER_ID_IS_NULL);
        }
        GetValidCarrierOrderRequestModel requestModel = MapperUtils.mapper(requestDto, GetValidCarrierOrderRequestModel.class);
        requestModel.setRequestSource(CommonConstant.ONE);
        Result<PageInfo<GetValidCarrierOrderResponseModel>> result = carrierOrderLeYiClient.getValidCarrierOrder(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<GetValidCarrierOrderResponseDto> list = MapperUtils.mapper(pageInfo.getList(), GetValidCarrierOrderResponseDto.class, new GetValidCarrierOrderMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    @ApiOperation(value = "查询云盘二维码", tags = "3.12.0")
    @PostMapping(value = "/api/carrierOrderManagement/getLeYiQrCode")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<GetLeYiQrCodeResponseDto> getLeYiQrCode(@RequestBody @Valid GetLeYiQrCodeRequestDto requestDto){
        Result<GetLeYiQrCodeResponseModel> result = carrierOrderLeYiClient.getLeYiQrCode(MapperUtils.mapper(requestDto, GetLeYiQrCodeRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), GetLeYiQrCodeResponseDto.class));
    }

    @ApiOperation(value = "查看运单签收单2(二部提货单和签收单二合一)", tags = "3.13.0")
    @PostMapping(value = "/api/carrierOrderManagement/reviewSignTicketsTwo")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ReviewSignTicketsResponseDto> reviewSignTicketsTwo(@RequestBody @Valid DownloadLadingBillRequestDto requestDto, HttpServletRequest httpServletRequest) {
        GetPdfPathForBillDto getPdfPathForBillDto = getPdfPathForBill(requestDto,httpServletRequest);
        String pdfPath = getPdfPathForBillDto.getPdfPath();
        String wordPath = getPdfPathForBillDto.getWordPath();
        List<String> signTicketsPath = new ArrayList<>();
        try (InputStream is = new FileInputStream(pdfPath); ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
            PdfWriteToImgRequestModel pdfWriteToImgRequestModel = new PdfWriteToImgRequestModel();
            pdfWriteToImgRequestModel.setBytes(bos.toByteArray());
            pdfWriteToImgRequestModel.setPath(getPdfPathForBillDto.getCarrierOrderCode());
            pdfWriteToImgRequestModel.setDpi(150);//图片清晰度
            Result<PdfWriteToImgResponseModel> imgResult = fileServiceApi.pdfWriteToImgOSS(pdfWriteToImgRequestModel);
            imgResult.throwException();
            List<String> ticketsPathList = imgResult.getData().getImgPathList();
            for (String path : ticketsPathList) {
                signTicketsPath.add(configKeyConstant.fileAccessAddressTemp + commonBiz.getImageURL(path.substring(path.lastIndexOf("/"))));
            }
        } catch (Exception e) {
            log.info("downLoad pick up bill pdf File error", e);
        } finally {
            File wordFile = new File(wordPath);
            if (wordFile.isFile() && wordFile.exists()) {
                if (!wordFile.delete()) {
                    log.info("下载签收单2 " + wordPath + " 删除失败 ");
                }
            }
            File pdfFile = new File(pdfPath);
            if (pdfFile.isFile() && pdfFile.exists()) {
                if (!pdfFile.delete()) {
                    log.info("下载签收单2 " + pdfPath + " 删除失败 ");
                }
            }
        }
        ReviewSignTicketsResponseDto responseDto = new ReviewSignTicketsResponseDto();
        responseDto.setSignTicketsPath(signTicketsPath);
        return Result.success(responseDto);
    }

    public GetPdfPathForBillDto getPdfPathForBill(DownloadLadingBillRequestDto requestDto, HttpServletRequest httpServletRequest) {
        Result<DownloadLadingBillResponseModel> result = carrierOrderClient.downloadLadingBill(MapperUtils.mapper(requestDto, CarrierOrderDetailRequestModel.class));
        result.throwException();
        DownloadLadingBillResponseDto responseDto = MapperUtils.mapper(result.getData(), DownloadLadingBillResponseDto.class, new DownloadLadingBillMapping());

        GetPdfPathForBillDto getPdfPathForBillDto = new GetPdfPathForBillDto();
        if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(responseDto.getDemandOrderSource())) {
            return getPdfPathForBillDto;
        }

        List<String[]> tableItems = new ArrayList<>();
        Map<String, String> templateValues = new HashMap<>();
        Map<String, InputStream> picMap = new HashMap<>();
        templateValues.put("carrierOrder", responseDto.getCarrierOrderCode());
        templateValues.put("expectedLoadTime", responseDto.getExpectedLoadTime());
        templateValues.put("unloadWarehouse", responseDto.getUnloadWarehouse());
        templateValues.put("receiver", Optional.ofNullable(responseDto.getReceiverName()).orElse("") + " " + Optional.ofNullable(responseDto.getReceiverMobile()).orElse(""));
        templateValues.put("unloadWarehouseAddress", responseDto.getUnloadDetailAddress());
        templateValues.put("driver", Optional.ofNullable(responseDto.getDriverName()).orElse("") + " " + Optional.ofNullable(responseDto.getDriverMobile()).orElse(""));
        templateValues.put("vehicleNumber", responseDto.getVehicleNo());
        templateValues.put("driverIdCardNum", responseDto.getDriverIdentityNumber());
        templateValues.put("loadWarehouse", responseDto.getLoadWarehouse());
        templateValues.put("consignor", Optional.ofNullable(responseDto.getConsignorName()).orElse("") + " " + Optional.ofNullable(responseDto.getConsignorMobile()).orElse(""));
        templateValues.put("loadWarehouseAddress", responseDto.getLoadDetailAddress());
        templateValues.put("publisher", Optional.ofNullable(responseDto.getPublishName()).orElse(CommonConstant.BLANK_TEXT) + Optional.ofNullable(responseDto.getPublishMobile()).orElse(CommonConstant.BLANK_TEXT));
        templateValues.put("dispatchTime", responseDto.getDispatchTime());

        //获取二维码图片
        byte[] qrCodePicByte = result.getData().getQrCodePicByte();
        //获取二维码图片流
        String qrCodePicPath = responseDto.getQrCodePicPath();
        if (qrCodePicByte == null && StringUtils.isNotBlank(qrCodePicPath)){
            qrCodePicByte = commonBiz.getOssFileByte(configKeyConstant.imageUploadCatalog + qrCodePicPath);
        }
        ByteArrayInputStream qrCodeInputStream = null;
        if (qrCodePicByte != null) {
            qrCodeInputStream = new ByteArrayInputStream(qrCodePicByte);
        }
        //需要重新生成二维码
        if ((CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey().equals(responseDto.getStatus())
                || CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(responseDto.getStatus())
                || CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey().equals(responseDto.getStatus())
                || CarrierOrderStatusEnum.WAIT_UNLOAD.getKey().equals(responseDto.getStatus()))
                && "0".equals(responseDto.getIfCancel())
                && "0".equals(responseDto.getIfEmpty())) {
            //重新构建二维码
            //二维码参数
            Map<String, Object> qrCodeParamsMap = DriverAppletQrCodeJumpPageEnum.CARRIER_ORDER_DETAIL_PAGE.getParamsMap();
            qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_CODE.getKey(), responseDto.getCarrierOrderCode());
            if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(responseDto.getEntrustType())
                    || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(responseDto.getEntrustType())) {
                qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.RECYCLE.getKey());
            } else {
                qrCodeParamsMap.put(QrCodeParamsEnum.CARRIER_ORDER_BILL_TYPE.getKey(), QrCodeParamsBillTypeEnum.OTHER.getKey());
            }
            String key = (String)redisUtils.get(CommonConstant.KEY_TOKEN_REDIS+responseDto.getCarrierOrderCode());
            if (StringUtils.isEmpty(key)) {
                Long hashCodeUnsigned = MurmurHashUtils.hash128Unsigned(JSONObject.toJSONString(responseDto) + CommonConstant.SHORT_URL_HASH_COLLISION_SUFFIX);
                // 对哈希值进行62进制转换
                key = ConversionUtils.base62(hashCodeUnsigned);
                redisUtils.getRedisTemplate().opsForValue().set(CommonConstant.KEY_TOKEN_REDIS + responseDto.getCarrierOrderCode(), key);
            }
            qrCodeParamsMap.put("f", "1");
            qrCodeParamsMap.put("k", key);
            qrCodePicByte = commonBiz.createQrCode(configKeyConstant.qrcodeCommonPrefix, qrCodeParamsMap).getFileByte();
            qrCodeInputStream = new ByteArrayInputStream(qrCodePicByte);

        }
        //替换二维码
        if (qrCodeInputStream != null) {
            picMap.put(CommonConstant.PIC_RID2, qrCodeInputStream);
        }

        //要填充的货物数据
        List<Map<String, List<String[]>>> tableInsertItems = new ArrayList<>();

        //提货货物
        Map<String, List<String[]>> tableValueLoadMap = new HashMap<>();
        List<String[]> tableCellValueLoadList = new ArrayList<>();
        tableValueLoadMap.put(CommonConstant.TABLE_KEYWORD_2, tableCellValueLoadList);

        //卸货货物
        Map<String, List<String[]>> tableValueUnloadMap = new HashMap<>();
        List<String[]> tableCellValueUnloadList = new ArrayList<>();
        tableValueUnloadMap.put(CommonConstant.TABLE_KEYWORD_1, tableCellValueUnloadList);

        tableInsertItems.add(tableValueLoadMap);
        tableInsertItems.add(tableValueUnloadMap);
        for (DownloadLadingBillGoodsResponseDto goodsDtoItem : responseDto.getGoodsInfoList()) {
            tableCellValueLoadList.add(new String[]{CommonConstant.BLANK_TEXT, goodsDtoItem.getGoodsName(), goodsDtoItem.getExpectAmount(), goodsDtoItem.getLoadAmount(), goodsDtoItem.getLoadDiffAmount()});
            tableCellValueUnloadList.add(new String[]{CommonConstant.BLANK_TEXT, goodsDtoItem.getGoodsName(), goodsDtoItem.getLoadAmount(), goodsDtoItem.getUnloadAmount(), goodsDtoItem.getUnloadDiffAmount()});
        }

        String tmpFileName = "/template/load_sign_bills_template.docx";
        String pdfBasePath = "/tmp/htmlfont";//临时保存word和pdf的文件夹
        commonBiz.dirIfExist(pdfBasePath);
        String tempBasePath = pdfBasePath + "/";
        String fileName = "云途签收单2" + responseDto.getCarrierOrderCode();
        String filePath = tempBasePath + fileName;
        String wordPath = filePath + ".docx";//word文档全路径
        //替换word模板内容生成新word文档保存到指定的文件夹
        InputStream inputStream = CarrierOrderForLeYiController.class.getResourceAsStream(tmpFileName);
        WordBarCodeUtils.fillWordTemplateValues(inputStream, wordPath, templateValues, picMap, tableItems, tableInsertItems);
        //将生成的word文档转换为pdf，保存在同一个路径下
        WordToPdfUtils.wordConverterToPdf(filePath);

        getPdfPathForBillDto.setCarrierOrderCode(responseDto.getCarrierOrderCode());
        getPdfPathForBillDto.setPdfPath(filePath + ".pdf");
        getPdfPathForBillDto.setWordPath(wordPath);
        getPdfPathForBillDto.setFileName(fileName);
        return getPdfPathForBillDto;
    }


    @ApiOperation(value = "校验接口是否能满足多提接口 v2.6.8", tags = "2.6.8")
    @PostMapping(value = "/api/driverApplet/carrierOrder/verifyEnablePickUpMore")
    public Result<Boolean> verifyEnablePickUpMore(@RequestBody @Valid VerifyEnablePickUpMoreReqDto requestDto) {
        Result<Boolean> result = carrierOrderClient.verifyEnablePickUpMore(MapperUtils.mapper(requestDto, VerifyEnablePickUpMoreReqModel.class));
        result.throwException();
        return result;
    }

    @ApiOperation(value = "补单1-确认关联需求单信息 v2.6.8", tags = "2.6.8")
    @PostMapping(value = "/api/driverApplet/carrierOrder/commitExtDemandOrder")
    public Result<CommitExtDemandOrderRespDto> commitExtDemandOrder(@RequestBody @Valid CommitExtDemandOrderReqDto requestDto) {
        return null;
    }

    @ApiOperation(value = "补单2-关联调度单信息 v2.6.8", tags = "2.6.8")
    @PostMapping(value = "/api/driverApplet/carrierOrder/associateExtDemandOrder")
    public Result<AssociateExtDemandOrderRespDto> associateExtDemandOrder(@RequestBody @Valid AssociateExtDemandOrderReqDto requestDto) {
        AssociateExtDemandOrderReqModel mapper = MapperUtils.mapper(requestDto, AssociateExtDemandOrderReqModel.class);
        mapper.setSource(1);
        Result<AssociateExtDemandOrderRespModel> result = carrierOrderClient.associateExtDemandOrder(mapper);
        result.throwException();
        AssociateExtDemandOrderRespDto associateExtDemandOrderRespDto = new AssociateExtDemandOrderRespDto();
        associateExtDemandOrderRespDto.setCarrierOrderId(result.getData().getCarrierOrderId());
        return Result.success(associateExtDemandOrderRespDto);
    }



}
