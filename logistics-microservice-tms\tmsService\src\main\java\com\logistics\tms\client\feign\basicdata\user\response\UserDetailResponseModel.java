package com.logistics.tms.client.feign.basicdata.user.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2023/12/25 13:37
 */
@Data
public class UserDetailResponseModel {
    @ApiModelProperty("员工id")
    private Long userId;
    @ApiModelProperty("员工名称")
    private String userName;
    @ApiModelProperty("员工账号")
    private String userAccount;
    @ApiModelProperty("手机号码")
    private String mobilePhone;
    @ApiModelProperty("是否是负责人")
    private Integer isAdmin;
    @ApiModelProperty("是否禁用")
    private Integer enabled;
    private String publicKey;
    private String privateKey;
    private Integer isSignatureValidation;
    private String crmId;
}
