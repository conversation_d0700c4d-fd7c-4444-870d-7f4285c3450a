package com.logistics.tms.controller.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class VehicleGpsRecordListResponseModel {
    @ApiModelProperty("GPS终端记录列表Id")
    private Long vehicleGpsRecordId;
    @ApiModelProperty("安装日期")
    private Date installTime;
    @ApiModelProperty("终端型号")
    private String terminalType;
    @ApiModelProperty("SIM卡号")
    private String simNumber;
    @ApiModelProperty("服务提供商")
    private String gpsServiceProvider;
    @ApiModelProperty("凭证列表")
    private List<CertificationPicturesResponseModel> fileList;
    @ApiModelProperty("最后操作人")
    private String lastModifiedBy;
    @ApiModelProperty("最后操作时间")
    private Date lastModifiedTime;
    @ApiModelProperty("备注")
    private String remark;
}
