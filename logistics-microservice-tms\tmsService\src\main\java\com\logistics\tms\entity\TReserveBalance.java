package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/12/06
*/
@Data
public class TReserveBalance extends BaseEntity {
    /**
    * 司机id
    */
    @ApiModelProperty("司机id")
    private Long driverId;

    /**
    * 余额
    */
    @ApiModelProperty("余额")
    private BigDecimal balanceAmount;

    /**
    * 充值金额
    */
    @ApiModelProperty("充值金额")
    private BigDecimal rechargeAmount;

    /**
    * 核销金额
    */
    @ApiModelProperty("核销金额")
    private BigDecimal verificationAmount;
}