package com.logistics.management.webapi.client.demandorder.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.demandorder.DemandOrderForLeYiClient;
import com.logistics.management.webapi.client.demandorder.request.*;
import com.logistics.management.webapi.client.demandorder.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/1/9 9:48
 */
@Component
public class DemandOrderForLeYiClientHystrix implements DemandOrderForLeYiClient {
    @Override
    public Result<List<BatchPublishDetailResponseModel>> publishDetail(BatchPublishDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> confirmPublish(BatchPublishRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<DemandOrderForLeYiResponseModel>> searchListForLeYi(DemandOrderSearchForLeYiRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<DemandOrderForLeYiResponseModel>> exportDemandOrderForLeYi(DemandOrderSearchForLeYiRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<DemandOrderDetailForLeYiResponseModel> getDetailForLeYi(DemandOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<DemandOrderOrderRelResponseModel>> getDemandOrderOrders(DemandOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchDemandUnLoadAddressResponseModel>> searchYPWarehouse(SearchDemandUnLoadAddressRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> confirmEmpty(DemandOrderEmptyRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<DispatchAlarmStatisticsResponseModel>> dispatchAlarmStatistics() {
        return Result.timeout();
    }

    @Override
    public Result<WaitDispatchStatisticsResponseModel> waitDispatchStatistics() {
        return Result.timeout();
    }

    @Override
    public Result<AggregateDataStatisticsResponseModel> aggregateDataStatistics() {
        return Result.timeout();
    }

    @Override
    public Result<List<MapDataStatisticsResponseModel>> mapDataStatistics() {
        return Result.timeout();
    }

    @Override
    public Result<SmartSpellListResponseModel> smartSpellList() {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifyCarrierForLeyi(ModifyCarrierForLeyiRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<ModifyCarrierDetailForLeyiResponseModel>> modifyCarrierDetailForLeyi(ModifyCarrierDetailForLeyiRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> rollbackDemandOrder(RollbackDemandOrderRequestModel requestModel) {
        return Result.timeout();
    }
}
