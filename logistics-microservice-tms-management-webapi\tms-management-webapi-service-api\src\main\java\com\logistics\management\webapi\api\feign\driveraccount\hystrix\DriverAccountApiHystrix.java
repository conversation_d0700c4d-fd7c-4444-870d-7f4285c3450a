package com.logistics.management.webapi.api.feign.driveraccount.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.driveraccount.DriverAccountApi;
import com.logistics.management.webapi.api.feign.driveraccount.dto.request.*;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.DriverAccountDetailResponseDto;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.DriverAccountImageResponseDto;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.DriverAccountOperateLogResponseDto;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.SearchDriverAccountResponseDto;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DriverAccountApiHystrix implements DriverAccountApi {

    @Override
    public Result<Boolean> addAccount(DriverAccountAddRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchDriverAccountResponseDto>> searchList(SearchDriverAccountRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<DriverAccountDetailResponseDto> getDetail(DriverAccountDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<DriverAccountImageResponseDto> getAccountImageList(DriverAccountImageRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<List<DriverAccountOperateLogResponseDto>> getOperateLogList(DriverAccountOperateLogRequestDto requestDto) {
        return Result.timeout();
    }
}
