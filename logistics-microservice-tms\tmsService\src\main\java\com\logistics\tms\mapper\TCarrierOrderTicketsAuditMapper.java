package com.logistics.tms.mapper;

import com.logistics.tms.controller.carrierorderticketsaudit.response.SearchReceiptAuditListResponseModel;
import com.logistics.tms.controller.carrierorderticketsaudit.request.SearchReceiptAuditListRequestModel;
import com.logistics.tms.entity.TCarrierOrderTicketsAudit;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2023/04/04
*/
@Mapper
public interface TCarrierOrderTicketsAuditMapper extends BaseMapper<TCarrierOrderTicketsAudit> {

    List<SearchReceiptAuditListResponseModel> searchList(@Param("requestModel") SearchReceiptAuditListRequestModel requestModel);

    TCarrierOrderTicketsAudit selectOneById(@Param("auditId") Long auditId);

    TCarrierOrderTicketsAudit selectOneByCarrierOrderId(@Param("carrierOrderId") Long carrierOrderId);

    List<TCarrierOrderTicketsAudit> selectAllByCarrierOrderIds(@Param("carrierOrderIds") List<Long> carrierOrderId);

    int updateAudit(@Param("entity") TCarrierOrderTicketsAudit entity, @Param("currentStatus") Integer currentStatus);

    int batchInsert(@Param("list") List<TCarrierOrderTicketsAudit> list);
}