package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.gpstrack.model.AllVehicleTrackInfoRequestModel;
import com.logistics.tms.api.feign.gpstrack.model.AllVehicleTrackInfoResponseModel;
import com.logistics.tms.api.feign.gpstrack.model.SearchCarrierOrderDestinationByVehicleNoResponseModel;
import com.logistics.tms.api.feign.gpstrack.model.VehicleTrackInfoItem;
import com.logistics.tms.entity.TVehicleGps;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TVehicleGpsMapper extends BaseMapper<TVehicleGps>{

    List<VehicleTrackInfoItem> getVehicleInfoByCondition(@Param("condition") AllVehicleTrackInfoRequestModel condition);

    List<SearchCarrierOrderDestinationByVehicleNoResponseModel> getCarrierOrdersByVehicleNo(@Param("vehicleNo") String vehicleNo);

    List<TVehicleGps> selectAllVehicles();

    List<TVehicleGps> selectVehicleGpsByVehicleNo(@Param("vehicleNo") String vehicleNo);

    TVehicleGps selectVehicleByVehicleId(@Param("id") Long id);

    int batchUpdateVehicleGps(@Param("list") List<TVehicleGps> tVehicleGps);

    AllVehicleTrackInfoResponseModel getVehicleGpsStatistics();

    int batchUpdateVehicleGpsByDriverId(@Param("vehicleList") List<TVehicleGps> vehicleGps);

    List<TVehicleGps> getVehicleGpsByVehicleIds(@Param("vehicleIds") String vehicleIds);

    int batchInsertByVehicle(@Param("list") List<TVehicleGps> list);
}