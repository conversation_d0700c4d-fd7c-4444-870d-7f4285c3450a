package com.logistics.tms.job;

import com.logistics.tms.biz.vehiclesettlement.VehicleSettlementBiz;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2022/9/5 15:49
 */
@Slf4j
@Component
public class CreateVehicleSettlementScheduledTasks {

    @Autowired
    private VehicleSettlementBiz vehicleSettlementBiz;

    /**
     * 生成自有车辆结算数据（每月1号凌晨1点）
     */
    @XxlJob("logisticsTmsGenerateVehicleSettlement")
    public void generateVehicleSettlement(){
        try {
            log.info("tms定时任务：生成自有车辆结算数据-开始");
            vehicleSettlementBiz.generateVehicleSettlement();
            log.info("tms定时任务：生成自有车辆结算数据-结束");
        }catch (Exception e){
            log.error("定时任务，生成自有车辆结算数据错误: ", e);
        }
    }
}
