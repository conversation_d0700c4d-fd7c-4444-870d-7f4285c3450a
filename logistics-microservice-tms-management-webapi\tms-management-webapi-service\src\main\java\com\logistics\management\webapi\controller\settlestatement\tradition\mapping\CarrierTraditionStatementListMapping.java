package com.logistics.management.webapi.controller.settlestatement.tradition.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.SettleStatementStatusEnum;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.settlestatement.tradition.response.CarrierTraditionStatementItemModel;
import com.logistics.management.webapi.client.settlestatement.tradition.response.CarrierTraditionStatementListResponseModel;
import com.logistics.management.webapi.controller.settlestatement.tradition.response.CarrierTraditionStatementListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/11/17 13:27
 */
public class CarrierTraditionStatementListMapping extends MapperMapping<CarrierTraditionStatementListResponseModel, CarrierTraditionStatementListResponseDto> {

    @Override
    public void configure() {
        CarrierTraditionStatementListResponseModel source = getSource();
        CarrierTraditionStatementListResponseDto destination = getDestination();

        //对账单状态
        destination.setSettleStatementStatusLabel(SettleStatementStatusEnum.getEnum(source.getSettleStatementStatus()).getValue());

        //对账单明细
        List<CarrierTraditionStatementItemModel> itemList = source.getItemList();

        //关联运单数量
        destination.setCarrierOrderAmount(ConverterUtils.toString(itemList.size()));

        //车主
        CarrierTraditionStatementItemModel itemModel = itemList.get(CommonConstant.INTEGER_ZERO);
        if (CompanyTypeEnum.PERSONAL.getKey().equals(itemModel.getCompanyCarrierType())) {
            //个人车主:姓名+手机号
            destination.setCompanyCarrierName(itemModel.getCarrierContactName() + FrequentMethodUtils.encryptionData(itemModel.getCarrierContactMobile(), EncodeTypeEnum.MOBILE_PHONE));
            destination.setExportCompanyCarrierName(itemModel.getCarrierContactName() + itemModel.getCarrierContactMobile());
        } else {
            destination.setCompanyCarrierName(itemModel.getCompanyCarrierName());
            destination.setExportCompanyCarrierName(itemModel.getCompanyCarrierName());
        }
        destination.setCompanyCarrierId(ConverterUtils.toString(itemModel.getCompanyCarrierId()));

        BigDecimal carrierFreight = BigDecimal.ZERO;//车主运费
        //计算数量
        for (CarrierTraditionStatementItemModel statementItemModel : itemList) {
            carrierFreight = carrierFreight.add(statementItemModel.getEntrustFreight());
        }

        //车主运费
        carrierFreight = carrierFreight.setScale(2, RoundingMode.HALF_UP);
        destination.setCarrierFreight(ConverterUtils.toString(carrierFreight));

        //费额合计
        BigDecimal carrierFreightTotal = carrierFreight.add(carrierFreight.multiply(source.getFreightTaxPoint()).divide(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_NET_ZERO, 2, RoundingMode.HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_UP);
        destination.setCarrierFreightTotal(ConverterUtils.toString(carrierFreightTotal));

        //申请运费总额
        destination.setApplyFeeTotal(ConverterUtils.toString(carrierFreightTotal));

        //差异调整
        if (source.getAdjustFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
            destination.setAdjustFee(CommonConstant.PLUS_SYMBOL + ConverterUtils.toString(source.getAdjustFee()));
        } else {
            destination.setAdjustFee(ConverterUtils.toString(source.getAdjustFee()));
        }

        //对账费用
        if (SettleStatementStatusEnum.CANCEL.getKey().equals(source.getSettleStatementStatus())) {
            //已撤销不展示
            destination.setReconciliationFee(CommonConstant.BLANK_TEXT);
        } else {
            destination.setReconciliationFee(ConverterUtils.toString(carrierFreightTotal.add(source.getAdjustFee()).setScale(2, RoundingMode.HALF_UP)));
        }

        //生成日志
        destination.setCreatedTime(DateUtils.dateToString(source.getCreatedTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));

        //关联开票
        if (CommonConstant.INTEGER_ONE.equals(source.getIfInvoice())) {
            destination.setIfInvoiceLabel(CommonConstant.YES);
        }else{
            destination.setIfInvoiceLabel(CommonConstant.NO);
        }
    }
}
