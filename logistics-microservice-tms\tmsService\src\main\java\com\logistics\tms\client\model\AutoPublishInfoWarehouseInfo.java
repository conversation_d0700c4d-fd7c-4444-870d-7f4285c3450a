package com.logistics.tms.client.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class AutoPublishInfoWarehouseInfo {

    @ApiModelProperty(value = "仓库ID")
    private Long warehouseId;

    @ApiModelProperty(value = "仓库属性")
    private Integer property;

    @ApiModelProperty(value = "仓库名称")
    private String whsName;

    @ApiModelProperty(value = "地址经度")
    private String longitude;

    @ApiModelProperty(value = "地址纬度")
    private String latitude;

    @ApiModelProperty(value = "仓库类型,1是自有,2是第三方,4虚拟,5其他、6 粉碎工厂")
    private Integer whsType;

    @ApiModelProperty(value = "省份Id")
    private Long provinceId;

    @ApiModelProperty(value = "省份名字")
    private String provinceName;

    @ApiModelProperty(value = "城市Id")
    private Long cityId;

    @ApiModelProperty(value = "城市名字")
    private String cityName;

    @ApiModelProperty(value = "县区Id")
    private Long areaId;

    @ApiModelProperty(value = "县区名字")
    private String areaName;

    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    @ApiModelProperty(value = "联系人名字")
    private String contactName;

    @ApiModelProperty(value = "联系人联系方式")
    private String contactPhone;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdTime;

    @ApiModelProperty(value = "备注")
    private String remark;
}
