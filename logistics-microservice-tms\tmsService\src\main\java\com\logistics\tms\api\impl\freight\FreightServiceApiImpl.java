package com.logistics.tms.api.impl.freight;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.freight.FreightServiceApi;
import com.logistics.tms.api.feign.freight.model.*;
import com.logistics.tms.biz.freight.FreightBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/12/24 16:48
 */
@RestController
public class FreightServiceApiImpl implements FreightServiceApi {

    @Autowired
    private FreightBiz freightBiz;

    /**
     * 查询运价管理列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchFreightListResponseModel>> searchList(@RequestBody SearchFreightListRequestModel requestModel) {
        return Result.success(freightBiz.searchList(requestModel));
    }

    /**
     * 添加运价管理
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> addFreight(@RequestBody AddFreightRequestModel requestModel) {
        freightBiz.addFreight(requestModel);
        return Result.success(true);
    }

    /**
     * 启用/禁用
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> enableFreight(@RequestBody EnableFreightRequestModel requestModel) {
        freightBiz.enableFreight(requestModel);
        return Result.success(true);
    }

    /**
     * 运价地址列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<PageInfo<SearchFreightAddressResponseModel>> searchFreightAddressList(@RequestBody SearchFreightAddressRequestModel requestModel) {
        return Result.success(freightBiz.searchFreightAddressList(requestModel));
    }

    /**
     * 添加/修改运价地址规则
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> addFreightAddressRule(@RequestBody AddOrModifyFreightAddressRuleRequestModel requestModel) {
        freightBiz.addFreightAddressRule(requestModel);
       return Result.success(true);
    }

    /**
     * 运价地址规则详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<FreightAddressRuleDetailResponseModel> getFreightRuleDetail(@RequestBody FreightAddressRuleDetailRequestModel requestModel) {
        return Result.success(freightBiz.getFreightRuleDetail(requestModel));
    }

    /**
     * 删除运价地址规则
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> deleteFreightAddressRule(@RequestBody DeleteFreightAddressRequestModel requestModel) {
        freightBiz.deleteFreightAddressRule(requestModel);
        return Result.success(true);
    }

    /**
     * 统一加价/减价
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> modifyFreightPrice(@RequestBody ModifyFreightPriceRequestModel requestModel) {
        freightBiz.modifyFreightPrice(requestModel);
        return Result.success(true);
    }

    /**
     * 运价日志
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<FreightLogsResponseModel>> freightLogs(@RequestBody FreightLogsRequestModel requestModel) {
        return Result.success(freightBiz.freightLogs(requestModel));
    }

    /**
     * 运价公司信息
     * @param requestModel
     * @return
     */
    @Override
    public Result<FreightCompanyInfoResponseModel> getFreightCompanyInfo(@RequestBody FreightCompanyInfoRequestModel requestModel) {
        return  Result.success(freightBiz.getFreightCompanyInfo(requestModel));
    }

    /**
     * 调度车辆-司机运价
     * @param requestModel
     * @return
     */
    @Override
    public Result<DriverFreightByDemandOrderIdsAndVehicleResponseModel> getDriverFreight(@RequestBody DriverFreightByDemandOrderIdsAndVehicleRequestModel requestModel) {
        return Result.success(freightBiz.getDriverFreight(requestModel));
    }

    /**
     * 委托发布-根据委托方地址和数量查询价格
     * @param requestModel
     * @return
     */
    @Override
    public Result<GetPriceByAddressAndAmountResponseModel> getPriceByAddressAndAmount(@RequestBody GetPriceByAddressAndAmountRequestModel requestModel) {
        return Result.success(freightBiz.getEntrustFreightInfo(requestModel));
    }
}
