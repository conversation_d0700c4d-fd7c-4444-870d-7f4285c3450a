package com.logistics.management.webapi.base.enums;

/**
 * 新生运单来源枚举
 */
public enum YeloLifeCarrierOrderSourceEnum {
    DEFAULT(-1, ""),
    CUSTOMER(1, "客户"),
    DRIVER(2, "司机");

    private final Integer key;
    private final String value;

    YeloLifeCarrierOrderSourceEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static YeloLifeCarrierOrderSourceEnum getEnum(Integer key) {
        for (YeloLifeCarrierOrderSourceEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
