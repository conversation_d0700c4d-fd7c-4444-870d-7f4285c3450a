package com.logistics.management.webapi.api.feign.driversafepromise.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/11/4 10:10
 */
@Data
public class SearchSafePromiseListResponseDto {
    @ApiModelProperty("承诺书ID")
    private String safePromiseId = "";
    @ApiModelProperty("标题")
    private String title = "";
    @ApiModelProperty("周期")
    private String period = "";
    @ApiModelProperty("经办人")
    private String agent = "";
    @ApiModelProperty("已签订人数")
    private String hasSignCount;
    @ApiModelProperty("未签订人数")
    private String notSignCount;
    @ApiModelProperty("上传时间")
    private String uploadTime ="";

}
