package com.logistics.appapi.controller.baiscinfo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/2
 */
@Data
public class VerifyPersonTwoElementsRequestDto {

	@ApiModelProperty(value = "姓名", required = true)
	@NotBlank(message = "姓名不能为空")
	private String name;

	@ApiModelProperty(value = "证件号（身份证号）", required = true)
	@NotBlank(message = "证件号不能为空")
	private String identity;
}
