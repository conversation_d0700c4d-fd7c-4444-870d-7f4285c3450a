package com.logistics.management.webapi.api.impl.driverpayee;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.controller.uploadfile.response.SrcUrlDto;
import com.logistics.management.webapi.api.feign.driverpayee.DriverPayeeApi;
import com.logistics.management.webapi.api.feign.driverpayee.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.driverpayee.mapping.DriverPayeeDetailMapping;
import com.logistics.management.webapi.api.impl.driverpayee.mapping.DriverPayeeListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportDriverPayeeInfo;
import com.logistics.management.webapi.base.constant.ImportExcelHeaders;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.common.SrcUrlModel;
import com.logistics.tms.api.feign.common.model.CertificatePictureModel;
import com.logistics.tms.api.feign.driverpayee.DriverPayeeServiceApi;
import com.logistics.tms.api.feign.driverpayee.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.redis.utils.RedisLockUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;


@Slf4j
@RestController
public class DriverPayeeApiImpl implements DriverPayeeApi {
    @Autowired
    private DriverPayeeServiceApi driverPayeeServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private RedisLockUtils redisLockUtils;
    private static final String IMPORT_DRIVERPAYEE_PIC = "IMPORT_DRIVERPAYEE_PIC";

    /**
     * 司机收款人账户列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<DriverPayeeListResponseDto>> driverPayeeList(@RequestBody @Valid DriverPayeeListRequestDto requestDto) {
        Result<PageInfo<DriverPayeeListResponseModel>> result = driverPayeeServiceApi.driverPayeeList(MapperUtils.mapper(requestDto, DriverPayeeListRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (DriverPayeeListResponseModel responseModel : result.getData().getList()) {
            for (CertificatePictureModel pictureModel : responseModel.getImageList()) {
                sourceSrcList.add(pictureModel.getFilePath());
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        PageInfo pageInfo = result.getData();
        Optional.ofNullable(result.getData()).ifPresent(t->{
            List<DriverPayeeListResponseDto> list = MapperUtils.mapper(pageInfo.getList(),DriverPayeeListResponseDto.class,new DriverPayeeListMapping(configKeyConstant,imageMap));
            pageInfo.setList(list);
        });
        return Result.success(pageInfo);
    }

    /**
     * 司机收款人账户新增/修改
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addOrModifyDriverPayee(@RequestBody @Valid  AddOrModifyDriverPayeeRequestDto requestDto) {
        if(StringUtils.isNotBlank(requestDto.getIdentityNo()) && !IDCardValidator.isValidatedAllIdcard(requestDto.getIdentityNo())){
            throw new BizException(ManagementWebApiExceptionEnum.ID_CARD_FORMAT_ERROR);
        }
        return driverPayeeServiceApi.addOrModifyDriverPayee(MapperUtils.mapper(requestDto, AddOrModifyDriverPayeeRequestModel.class));
    }

    /**
     * 司机收款人账户详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<DriverPayeeDetailResponseDto> driverPayeeDetail(@RequestBody @Valid  DriverPayeeDetailRequestDto requestDto) {
        Result<DriverPayeeDetailResponseModel> result = driverPayeeServiceApi.driverPayeeDetail(MapperUtils.mapper(requestDto, DriverPayeeDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        sourceSrcList.add(result.getData().getIdentityBack());
        sourceSrcList.add(result.getData().getIdentityFront());
        for (CertificatePictureModel pictureModel : result.getData().getImageList()) {
            sourceSrcList.add(pictureModel.getFilePath());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),DriverPayeeDetailResponseDto.class,new DriverPayeeDetailMapping(configKeyConstant,imageMap)));
    }

    /**
     * 司机收款人审核驳回作废
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> auditOrReject(@RequestBody @Valid  AuditRejectDriverPayeeRequestDto requestDto) {
        return driverPayeeServiceApi.auditOrReject(MapperUtils.mapper(requestDto,AuditRejectDriverPayeeRequestModel.class));
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @Override
    public void driverPayeeExport(DriverPayeeListRequestDto requestDto, HttpServletResponse response) {
        Result<List<ExportDriverPayeeListResponseModel>> result = driverPayeeServiceApi.exportDriverPayeeList(MapperUtils.mapper(requestDto, DriverPayeeListRequestModel.class));
        result.throwException();
        String fileName = "司机收款人信息" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        List<ExportDriverPayeeListResponseModel> resultList = result.getData();
        Map<String, String> exportTypeMap = ExportDriverPayeeInfo.getDriverPayeeInfo();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return resultList;
            }
        });
    }

    /**
     * 导入
     * @param file
     * @param request
     * @return
     */
    @Override
    public Result<ImportDriverPayeeResponseDto> importDriverPayee(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_DRIVER_PAYEE_FILE_IS_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            log.error("导入收款人账户失败，",e);
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_DRIVER_PAYEE_FILE_IS_EMPTY);
        }

        List<List<Object>> excelList = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportDriverPayeeType());
        ImportDriverPayeeRequestModel requestModel = this.initImportRepeatData(excelList);
        Result<ImportDriverPayeeResponseModel> result = driverPayeeServiceApi.importDriverPayee(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ImportDriverPayeeResponseDto.class));
    }
    //初始化导入的数据
    private ImportDriverPayeeRequestModel initImportRepeatData(List<List<Object>> excelList ){
        ImportDriverPayeeRequestModel requestModel=new ImportDriverPayeeRequestModel();
        requestModel.setImportList(new ArrayList<>());
        List<ImportDriverPayeeListDto> requestDtoList = commonBiz.transferExcelToObject(ImportDriverPayeeListDto.class,excelList);
        if(ListUtils.isEmpty(requestDtoList)){
            return requestModel;
        }
        for(ImportDriverPayeeListDto tmpDto : requestDtoList){

            if(StringUtils.isBlank(tmpDto.getName())|| !FrequentMethodUtils.checkReg(tmpDto.getName().trim(),"[\\u4E00-\\u9FA5a-zA-Z]{2,50}")){
                tmpDto.setName(tmpDto.getName().trim());
                requestModel.setNumberFailure(requestModel.getNumberFailure()+1);
                continue;
            }
            if(StringUtils.isNotBlank(tmpDto.getMobile())&& !FrequentMethodUtils.validateTelFormat(tmpDto.getMobile().trim())){
                tmpDto.setMobile(tmpDto.getMobile().trim());
                requestModel.setNumberFailure(requestModel.getNumberFailure()+1);
                continue;
            }
            if(StringUtils.isBlank(tmpDto.getIdentityNo())|| !IDCardValidator.isValidatedAllIdcard(tmpDto.getIdentityNo().trim())){
                tmpDto.setIdentityNo(tmpDto.getIdentityNo().trim());
                requestModel.setNumberFailure(requestModel.getNumberFailure()+1);
                continue;
            }
            if(StringUtils.isBlank(tmpDto.getBankCardNo())|| !FrequentMethodUtils.checkReg(tmpDto.getBankCardNo().trim(),"[\\d]{1,50}")){
                tmpDto.setBankCardNo(tmpDto.getBankCardNo().trim());
                requestModel.setNumberFailure(requestModel.getNumberFailure()+1);
                continue;
            }
            if(StringUtils.isBlank(tmpDto.getBankName())|| !FrequentMethodUtils.checkReg(tmpDto.getBankName().trim(),"[\\u4E00-\\u9FA5]{2,50}")){
                tmpDto.setBankName(tmpDto.getBankName().trim());
                requestModel.setNumberFailure(requestModel.getNumberFailure()+1);
                continue;
            }
            if(StringUtils.isNotBlank(tmpDto.getRemark())&& tmpDto.getRemark().length()>300){
                tmpDto.setRemark(tmpDto.getRemark().trim());
                requestModel.setNumberFailure(requestModel.getNumberFailure()+1);
                continue;
            }
            ImportDriverPayeeListModel importStaffListRequestModel = MapperUtils.mapper(tmpDto,ImportDriverPayeeListModel.class);
            commonBiz.convertObjectFieldToNullIfIsEmpty(importStaffListRequestModel);
            requestModel.getImportList().add(importStaffListRequestModel);
        }
        return requestModel;
    }

    /**
     * 导入证件信息
     * @param file
     * @return
     */
    @Override
    public Result<Boolean> importDriverPayeeInfo(@RequestParam("file") MultipartFile file) {
        SrcUrlDto srcUrlDto = commonBiz.uploadToTmpCatalog(file);
        String driverName = getImportDriverPayeeCertificateDriverName(srcUrlDto.getFileName());
        if(StringUtils.isEmpty(driverName)){
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_FAILURE);
        }
        String uuid = UUIDGenerateUtil.generateUUID();
        Long startTime = System.currentTimeMillis();
        while (!redisLockUtils.tryLock(IMPORT_DRIVERPAYEE_PIC+driverName,uuid,10)){
            try {
                Thread.sleep(200);
                Long endTime = System.currentTimeMillis();
                if (endTime-startTime > 10000){
                    throw new BizException(ManagementWebApiExceptionEnum.COMMON_IO_EXCEPTION);
                }
            }catch (Exception e){
                log.info(e.getMessage());
            }
        }
        Result result = driverPayeeServiceApi.importDriverPayeeCertificate(MapperUtils.mapper(srcUrlDto, SrcUrlModel.class));
        redisLockUtils.releaseLock(IMPORT_DRIVERPAYEE_PIC+driverName,uuid);
        result.throwException();
        return Result.success(true);
    }
    //获取文件名
    private String getImportDriverPayeeCertificateDriverName(String fileName){
        if(FrequentMethodUtils.match("^([\\u4e00-\\u9fa5A-Za-z])+[123456]{1}$",fileName)){
            return fileName.substring(0,fileName.length()-1);
        }else{
            return "";
        }

    }

    /**
     * 查看日志
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<ViewLogsResponseDto>> driverPayeeLogs(@RequestBody @Valid DriverPayeeDetailRequestDto requestDto) {
        Result<List<ViewLogResponseModel>> listResult = driverPayeeServiceApi.driverPayeeLogs(MapperUtils.mapper(requestDto,DriverPayeeDetailRequestModel.class));
        listResult.throwException();
        return Result.success(MapperUtils.mapper(listResult.getData(),ViewLogsResponseDto.class));
    }

    /**
     * 查询已审核的收款账户信息
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<SearchDriverPayeesResponseDto>> searchDriverPayees(@RequestBody SearchDriverPayeesRequestDto requestDto) {
        Result<List<SearchDriverPayeesResponseModel>> listResult = driverPayeeServiceApi.searchDriverPayees(MapperUtils.mapper(requestDto,SearchDriverPayeesRequestModel.class));
        if(listResult.isSuccess()){
            return Result.success(MapperUtils.mapper(listResult.getData(),SearchDriverPayeesResponseDto.class));
        }else{
            return Result.success(new ArrayList<>());
        }
    }


}
