package com.logistics.management.webapi.base.enums;

/**
 * liang current user system login name
 * 2018/11/7 current system date
 */
public enum CertificateStatusEnum {
    DISABLED(0, "齐全"),
    ENABLED(1, "待补充"),;

    private Integer key;
    private String value;

    CertificateStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CertificateStatusEnum getEnum(Integer key) {
        for (CertificateStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
