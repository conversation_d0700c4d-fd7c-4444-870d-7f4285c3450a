<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TTerminalCustomerAddressMapper" >
    <select id="searchTerminalCustomerAddressList" resultType="com.logistics.tms.api.feign.terminalcustomeraddress.model.SearchTerminalCustomerAddressListResponseModel">
        SELECT
        ttca.id as terminalCustomerAddressId,
        ttca.collect_warehouse as collectWarehouse,
        ttca.collect_province_name as collectProvinceName,
        ttca.collect_city_name as collectCityName,
        ttca.collect_area_name as collectAreaName,
        ttca.collect_detail_address as collectDetailAddress,
        ttca.collect_contact_name as collectContactName,
        ttca.collect_contact_mobile as collectContactMobile,
        ttca.map_link_path as mapLinkPath,
        ttca.customer_situation as customerSituation,
        ttca.remark,
        ttca.last_modified_by as lastModifiedBy,
        ttca.last_modified_time as lastModifiedTime
        FROM t_terminal_customer_address ttca
        WHERE ttca.valid = 1
        <if test="params.loadAddress != null and params.loadAddress != ''">
            and (instr(ttca.collect_warehouse,#{params.loadAddress,jdbcType=VARCHAR}) or
            instr(ttca.collect_province_name,#{params.loadAddress,jdbcType=VARCHAR}) or
            instr(ttca.collect_city_name,#{params.loadAddress,jdbcType=VARCHAR}) or
            instr(ttca.collect_area_name,#{params.loadAddress,jdbcType=VARCHAR}) or
            instr(ttca.collect_detail_address,#{params.loadAddress,jdbcType=VARCHAR})
            )
        </if>
        <if test="params.collectContactName != null and params.collectContactName != ''">
            and (instr(ttca.collect_contact_name,#{params.collectContactName,jdbcType=VARCHAR}) or instr(ttca.collect_contact_mobile,#{params.collectContactName,jdbcType=VARCHAR}))
        </if>
        <if test="params.terminalCustomerAddressIds != null and params.terminalCustomerAddressIds != ''">
            and ttca.id in (${params.terminalCustomerAddressIds})
        </if>
        order by ttca.last_modified_time desc, ttca.id desc
    </select>

    <select id="getTerminalCustomerAddressDetail" resultType="com.logistics.tms.api.feign.terminalcustomeraddress.model.GetTerminalCustomerAddressDetailResponseModel">
        select
        ttca.id as terminalCustomerAddressId,
        ttca.collect_warehouse as collectWarehouse,
        ttca.collect_province_id as collectProvinceId,
        ttca.collect_province_name as collectProvinceName,
        ttca.collect_city_id as collectCityId,
        ttca.collect_city_name as collectCityName,
        ttca.collect_area_id as collectAreaId,
        ttca.collect_area_name as collectAreaName,
        ttca.collect_detail_address as collectDetailAddress,
        ttca.collect_contact_name as collectContactName,
        ttca.collect_contact_mobile as collectContactMobile,
        ttca.map_link_path as mapLinkPath,
        ttca.customer_situation as customerSituation,
        ttca.remark
        from t_terminal_customer_address ttca
        where ttca.valid = 1
        and ttca.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_terminal_customer_address
        where valid = 1
        and id in (${ids})
    </select>

    <update id="delTerminalCustomerAddress">
        update t_terminal_customer_address
        set
        valid = 0,
        last_modified_by = #{operator},
        last_modified_time = #{operateTime}
        where valid = 1
        and id in (${ids})
    </update>
</mapper>