package com.logistics.tms.controller.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/8 17:37
 */
@Data
public class GetGpsInfoByVehicleNoResponseModel {
    @ApiModelProperty("车牌号")
    private Long vehicleId;
    private String vehicleNo;
    @ApiModelProperty("司机")
    private Long staffId;
    private String driverName;
    private String driverPhone;
    @ApiModelProperty("GPS安装日期")
    private Date installTime;
    @ApiModelProperty("终端型号")
    private String terminalType;
    @ApiModelProperty("GPS终端SIM卡号")
    private String simNumber;
    @ApiModelProperty("GPS服务商名称")
    private String gpsServiceProvider;
    private List<GetGpsInfoByVehicleNoModel> gpsList;
}
