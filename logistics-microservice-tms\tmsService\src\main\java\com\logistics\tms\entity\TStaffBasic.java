package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2023/02/10
*/
@Data
public class TStaffBasic extends BaseEntity {
    /**
    * 人员类别 1 驾驶员 2 押运员 3 驾驶员&押运员
    */
    @ApiModelProperty("人员类别 1 驾驶员 2 押运员 3 驾驶员&押运员")
    private Integer type;

    /**
    * 人员机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    /**
    * 性别  1男 2 女
    */
    @ApiModelProperty("性别  1男 2 女")
    private Integer gender;

    /**
    * 人员姓名
    */
    @ApiModelProperty("人员姓名")
    private String name;

    /**
    * 手机号
    */
    @ApiModelProperty("手机号")
    private String mobile;

    /**
    * 身份证号码
    */
    @ApiModelProperty("身份证号码")
    private String identityNumber;

    /**
    * 年龄
    */
    @ApiModelProperty("年龄")
    private Integer age;

    /**
    * 身份证有效期
    */
    @ApiModelProperty("身份证有效期")
    private Date identityValidity;

    /**
    * 身份证是否永久: 0 否 1 是
    */
    @ApiModelProperty("身份证是否永久: 0 否 1 是")
    private Integer identityIsForever;

    /**
    * 劳动合同编号
    */
    @ApiModelProperty("劳动合同编号")
    private String laborContractNo;

    /**
    * 劳动合同有效期
    */
    @ApiModelProperty("劳动合同有效期")
    private Date laborContractValidDate;

    /**
    * 司机账号开通状态 0 待开通 1 已开通 2 已关闭
    */
    @ApiModelProperty("司机账号开通状态 0 待开通 1 已开通 2 已关闭")
    private Integer openStatus;

    /**
    * 关闭原因
    */
    @ApiModelProperty("关闭原因")
    private String closeReason;

    /**
    * 车辆添加来源：1新增 2导入
    */
    @ApiModelProperty("车辆添加来源：1新增 2导入")
    private Integer source;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 实名认证(个人): 0 待实名 1 实名中 2 已实名
    */
    @ApiModelProperty("实名认证(个人): 0 待实名 1 实名中 2 已实名")
    private Integer realNameAuthenticationStatus;

    /**
    * 仓库权限开关, 0: 关 1: 开
    */
    @ApiModelProperty("仓库权限开关, 0: 关 1: 开")
    private Integer warehouseSwitch;
}