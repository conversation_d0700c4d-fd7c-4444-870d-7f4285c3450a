package com.logistics.management.webapi.controller.freightconfig.request;

import com.logistics.management.webapi.controller.freightconfig.request.shipping.ConfigVehicleItemDto;
import lombok.Data;

import java.util.List;

@Data
public class ConfigVehicleItemReqDto {


    /**
     * 车长（米） -1就是零担
     */
    private List<String> vehicleLength;


    /**
     * 阶梯详细信息
     */
    private List<ConfigVehicleItemDto> ladderConfigList;


}
