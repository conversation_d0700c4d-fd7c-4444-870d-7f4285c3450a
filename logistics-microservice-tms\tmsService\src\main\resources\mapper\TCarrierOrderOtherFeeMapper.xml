<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderOtherFeeMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierOrderOtherFee" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="carrier_order_id" property="carrierOrderId" jdbcType="BIGINT" />
    <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR" />
    <result column="audit_status" property="auditStatus" jdbcType="INTEGER" />
    <result column="auditor_name" property="auditorName" jdbcType="VARCHAR" />
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
    <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, carrier_order_id, carrier_order_code, audit_status, auditor_name, audit_time, 
    total_amount, remark, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_carrier_order_other_fee
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_carrier_order_other_fee
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierOrderOtherFee" >
    insert into t_carrier_order_other_fee (id, carrier_order_id, carrier_order_code, 
      audit_status, auditor_name, audit_time, 
      total_amount, remark, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, #{carrierOrderCode,jdbcType=VARCHAR}, 
      #{auditStatus,jdbcType=INTEGER}, #{auditorName,jdbcType=VARCHAR}, #{auditTime,jdbcType=TIMESTAMP}, 
      #{totalAmount,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderOtherFee" keyProperty="id" useGeneratedKeys="true">
    insert into t_carrier_order_other_fee
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id,
      </if>
      <if test="carrierOrderCode != null" >
        carrier_order_code,
      </if>
      <if test="auditStatus != null" >
        audit_status,
      </if>
      <if test="auditorName != null" >
        auditor_name,
      </if>
      <if test="auditTime != null" >
        audit_time,
      </if>
      <if test="totalAmount != null" >
        total_amount,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null" >
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null" >
        #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null" >
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null" >
        #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalAmount != null" >
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierOrderOtherFee" >
    update t_carrier_order_other_fee
    <set >
      <if test="carrierOrderId != null" >
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null" >
        carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null" >
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null" >
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalAmount != null" >
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierOrderOtherFee" >
    update t_carrier_order_other_fee
    set carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      auditor_name = #{auditorName,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>