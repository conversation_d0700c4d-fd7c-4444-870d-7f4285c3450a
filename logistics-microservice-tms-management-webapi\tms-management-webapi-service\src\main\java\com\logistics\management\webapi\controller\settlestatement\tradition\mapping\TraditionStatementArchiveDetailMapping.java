package com.logistics.management.webapi.controller.settlestatement.tradition.mapping;

import com.logistics.management.webapi.client.settlestatement.tradition.response.TraditionStatementArchiveDetailResponseModel;
import com.logistics.management.webapi.controller.settlestatement.tradition.response.TraditionStatementArchiveDetailResponseDto;
import com.logistics.management.webapi.controller.settlestatement.tradition.response.TraditionStatementArchiveDetailTicketResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2022/11/15 13:30
 */
public class TraditionStatementArchiveDetailMapping extends MapperMapping<TraditionStatementArchiveDetailResponseModel, TraditionStatementArchiveDetailResponseDto> {

    private final String imagePrefix;
    private final Map<String, String> imageMap;

    public TraditionStatementArchiveDetailMapping(String imagePrefix, Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap = imageMap;
    }

    @Override
    public void configure() {
        TraditionStatementArchiveDetailResponseModel source = getSource();
        TraditionStatementArchiveDetailResponseDto destination = getDestination();

        //图片路径转换
        List<TraditionStatementArchiveDetailTicketResponseDto> archiveTicketList = new ArrayList<>();
        if (ListUtils.isNotEmpty(source.getArchiveTicketList())) {
            TraditionStatementArchiveDetailTicketResponseDto ticketResponseDto;
            for (String path : source.getArchiveTicketList()) {
                ticketResponseDto = new TraditionStatementArchiveDetailTicketResponseDto();
                ticketResponseDto.setSrc(imagePrefix + imageMap.get(path));
                ticketResponseDto.setRelativePath(path);
                archiveTicketList.add(ticketResponseDto);
            }
        }
        destination.setArchiveTicketList(archiveTicketList);
    }
}
