package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TOilFilled extends BaseEntity {
    /**
    * 结算状态：0 待结算，1 已结算
    */
    @ApiModelProperty("结算状态：0 待结算，1 已结算")
    private Integer status;

    /**
    * 车辆id
    */
    @ApiModelProperty("车辆id")
    private Long vehicleId;

    /**
    * 车辆机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 司机id
    */
    @ApiModelProperty("司机id")
    private Long staffId;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String name;

    /**
    * 手机号
    */
    @ApiModelProperty("手机号")
    private String mobile;

    /**
    * 费用来源：10 充油，20 退款
    */
    @ApiModelProperty("费用来源：10 充油，20 退款")
    private Integer source;

    /**
    * 充油金额
    */
    @ApiModelProperty("充油金额")
    private BigDecimal oilFilledFee;

    /**
    * 充油时间
    */
    @ApiModelProperty("充油时间")
    private Date oilFilledDate;

    /**
    * 充油方式：1 充油卡，2 加油车
    */
    @ApiModelProperty("充油方式：1 充油卡，2 加油车")
    private Integer oilFilledType;

    /**
    * 升
    */
    @ApiModelProperty("升")
    private Integer liter;

    /**
    * 充值积分
    */
    @ApiModelProperty("充值积分")
    private BigDecimal topUpIntegral;

    /**
    * 奖励积分
    */
    @ApiModelProperty("奖励积分")
    private Integer rewardIntegral;

    /**
    * 副卡卡号
    */
    @ApiModelProperty("副卡卡号")
    private String subCardNumber;

    /**
    * 副卡所属人
    */
    @ApiModelProperty("副卡所属人")
    private String subCardOwner;

    /**
    * 合作公司
    */
    @ApiModelProperty("合作公司")
    private String cooperationCompany;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 退款原因类型：0 其他，10 丢失副卡，20 车辆过户，30 车辆报废，40 充油充错
    */
    @ApiModelProperty("退款原因类型：0 其他，10 丢失副卡，20 车辆过户，30 车辆报废，40 充油充错")
    private Integer refundReasonType;

    /**
    * 退款原因
    */
    @ApiModelProperty("退款原因")
    private String refundReason;
}