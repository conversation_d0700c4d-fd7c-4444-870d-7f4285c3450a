package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/12/30 19:35
 */
public class ExportCarrierFreightAddressInfo {
    private ExportCarrierFreightAddressInfo() {
    }
    private static final Map<String,String> CARRIER_FREIGHT_ADDRESS_INFO;
    static{
        CARRIER_FREIGHT_ADDRESS_INFO=new LinkedHashMap<>();
        CARRIER_FREIGHT_ADDRESS_INFO.put("发货市","fromProvinceAndCityLabel");
        CARRIER_FREIGHT_ADDRESS_INFO.put("发货区","fromAreaName");
        CARRIER_FREIGHT_ADDRESS_INFO.put("卸货市","toProvinceAndCityLabel");
        CARRIER_FREIGHT_ADDRESS_INFO.put("卸货区","toAreaName");
        CARRIER_FREIGHT_ADDRESS_INFO.put("操作人","lastModifiedBy");
        CARRIER_FREIGHT_ADDRESS_INFO.put("操作时间","lastModifiedTime");
    }

    public static Map<String, String> getCarrierFreightAddressInfo() {
        return CARRIER_FREIGHT_ADDRESS_INFO;
    }

}
