package com.logistics.management.webapi.controller.freightconfig;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.freightconfig.CarrierFreightConfigMileageClient;
import com.logistics.management.webapi.client.freightconfig.request.ladder.CarrierFreightConfigLadderRequestCheck;
import com.logistics.management.webapi.client.freightconfig.request.mileage.CarrierFreightConfigMileageAddRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.mileage.CarrierFreightConfigMileageEditRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.mileage.CarrierFreightConfigMileageRequestModel;
import com.logistics.management.webapi.client.freightconfig.response.mileage.CarrierFreightConfigMileageResponseModel;
import com.logistics.management.webapi.controller.freightconfig.request.mileage.CarrierFreightConfigMileageAddRequestDto;
import com.logistics.management.webapi.controller.freightconfig.request.mileage.CarrierFreightConfigMileageEditRequestDto;
import com.logistics.management.webapi.controller.freightconfig.request.mileage.CarrierFreightConfigMileageRequestDto;
import com.logistics.management.webapi.controller.freightconfig.response.mileage.CarrierFreightConfigMileageResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Api(tags = "里程数计价配置管理")
@RequestMapping(value = "/api/freight/config/mileage")
public class CarrierFreightConfigMileageController {

    @Resource
    private CarrierFreightConfigMileageClient carrierFreightConfigMileageClient;

    @PostMapping(value = "/detail")
    @ApiOperation(value = "里程数计价配置查看", tags = "1.3.5")
    Result<CarrierFreightConfigMileageResponseDto> detail(@Valid @RequestBody CarrierFreightConfigMileageRequestDto requestDto) {
        CarrierFreightConfigMileageRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierFreightConfigMileageRequestModel.class);
        Result<CarrierFreightConfigMileageResponseModel> result = carrierFreightConfigMileageClient.detail(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), CarrierFreightConfigMileageResponseDto.class));
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "里程数计价配置新增", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> add(@Valid @RequestBody CarrierFreightConfigMileageAddRequestDto requestDto) {
        CarrierFreightConfigMileageAddRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierFreightConfigMileageAddRequestModel.class);
        CarrierFreightConfigLadderRequestCheck.check(requestModel.getLadderConfigList());
        return carrierFreightConfigMileageClient.add(requestModel);
    }

    @PostMapping(value = "/edit")
    @ApiOperation(value = "里程数计价配置编辑", tags = "1.3.5")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    Result<Boolean> edit(@Valid @RequestBody CarrierFreightConfigMileageEditRequestDto requestDto) {
        CarrierFreightConfigMileageEditRequestModel requestModel = MapperUtils.mapper(requestDto, CarrierFreightConfigMileageEditRequestModel.class);
        CarrierFreightConfigLadderRequestCheck.check(requestModel.getLadderConfigList());
        return carrierFreightConfigMileageClient.edit(requestModel);
    }
}
