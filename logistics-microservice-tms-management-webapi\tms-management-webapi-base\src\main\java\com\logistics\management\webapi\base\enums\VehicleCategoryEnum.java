package com.logistics.management.webapi.base.enums;

/**
 * @Author: sj
 * @Date: 2019/7/26 11:09
 */
public enum VehicleCategoryEnum {
    NULL(0,""),
    TRACTOR(1,"牵引车"),
    TRAILER(2, "挂车"),
    WHOLE(3, "一体车"),
    ;

    private Integer key;
    private String value;

    VehicleCategoryEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static VehicleCategoryEnum getEnum(Integer key) {
        for (VehicleCategoryEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return NULL;
    }

    public static Integer getKeyByValue(String value){
        for (VehicleCategoryEnum t : values()) {
            if (t.getValue().equals(value) ) {
                return t.getKey();
            }
        }
        return NULL.getKey();
    }
}
