<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TFreightAddressMapper">
    <select id="searchFreightAddressList" resultType="com.logistics.tms.api.feign.freight.model.SearchFreightAddressResponseModel">
        select
            tfa.id as freightAddressId,
            tfa.freight_id as freightId,
            tfa.calc_type as calcType,
            tfa.from_province_name as fromProvinceName,
            tfa.from_city_name as fromCityName,
            tfa.from_area_name as fromAreaName,
            tfa.to_province_name as toProvinceName,
            tfa.to_city_name as toCityName,
            tfa.to_area_name as toAreaName ,
            tfa.last_modified_by as lastModifiedBy,
            tfa.last_modified_time as lastModifiedTime,
            twa.warehouse as warehouseName
        from t_freight_address tfa
            left join t_warehouse_address twa on tfa.warehouse_id = twa.id and tfa.valid = 1
        where tfa.valid = 1
        <if test="params.freightAddressIds !=null and params.freightAddressIds !=''">
            and tfa.id in (${params.freightAddressIds})
        </if>
        <if test="params.freightId != null">
            and  tfa.freight_id = #{params.freightId,jdbcType = BIGINT}
        </if>

        <if test="params.warehouseName!=null and params.warehouseName!=''">
            and instr(twa.warehouse,#{params.warehouseName,jdbcType = VARCHAR}) > 0
        </if>
        <if test="params.fromAddress!=null and params.fromAddress!=''">
            and (
              instr(tfa.from_province_name,#{params.fromAddress,jdbcType = VARCHAR}) > 0
              or instr(tfa.from_city_name,#{params.fromAddress,jdbcType = VARCHAR}) > 0
              or instr(tfa.from_area_name,#{params.fromAddress,jdbcType = VARCHAR}) > 0
            )
        </if>
        <if test="params.toAddress!=null and params.toAddress != ''">
            and (
            instr(tfa.to_province_name,#{params.toAddress,jdbcType = VARCHAR}) > 0
            or instr(tfa.to_city_name,#{params.toAddress,jdbcType = VARCHAR}) > 0
            or instr(tfa.to_area_name,#{params.toAddress,jdbcType = VARCHAR}) > 0
            )
        </if>
        <if test="params.calcType!=null">
            and tfa.calc_type = #{params.calcType,jdbcType = INTEGER}
        </if>
        order by tfa.last_modified_time desc
    </select>

    <resultMap id="freightAddressDetailMap" type="com.logistics.tms.api.feign.freight.model.FreightAddressRuleDetailResponseModel">
        <id column="freightAddressId" property="freightAddressId" jdbcType="BIGINT"/>
        <result column="freightId" property="freightId" jdbcType="BIGINT"/>
        <result column="calcType" property="calcType" jdbcType="INTEGER"/>
        <result column="toProvinceName" property="toProvinceName" jdbcType="VARCHAR"/>
        <result column="toProvinceId" property="toProvinceId" jdbcType="BIGINT"/>
        <result column="toCityName" property="toCityName" jdbcType="VARCHAR"/>
        <result column="toCityId" property="toCityId" jdbcType="BIGINT"/>
        <result column="toAreaName" property="toAreaName" jdbcType="VARCHAR"/>
        <result column="toAreaId" property="toAreaId" jdbcType="BIGINT"/>
        <result column="fromProvinceName" property="fromProvinceName" jdbcType="VARCHAR"/>
        <result column="fromProvinceId" property="fromProvinceId" jdbcType="BIGINT"/>
        <result column="fromCityName" property="fromCityName" jdbcType="VARCHAR"/>
        <result column="fromCityId" property="fromCityId" jdbcType="BIGINT"/>
        <result column="fromAreaName" property="fromAreaName" jdbcType="VARCHAR"/>
        <result column="fromAreaId" property="fromAreaId" jdbcType="BIGINT"/>
        <result column="warehouseId" property="warehouseId" jdbcType="BIGINT"/>
        <result column="warehouseName" property="warehouseName" jdbcType="VARCHAR"/>
        <collection property="freightAddressRuleList" resultMap="freightAddressRuleMap"/>
    </resultMap>

    <resultMap id="freightAddressRuleMap" type="com.logistics.tms.api.feign.freight.model.FreightAddressRuleModel">
        <id column="freightAddressRuleId" property="freightAddressRuleId" jdbcType="BIGINT"/>
        <result column="ruleIndex" property="ruleIndex" jdbcType="INTEGER"/>
        <result column="amountFrom" property="amountFrom" jdbcType="DECIMAL"/>
        <result column="fromSymbol" property="fromSymbol" jdbcType="VARCHAR"/>
        <result column="amountTo" property="amountTo" jdbcType="DECIMAL"/>
        <result column="toSymbol" property="toSymbol" jdbcType="VARCHAR"/>
        <result column="freightType" property="freightType" jdbcType="INTEGER"/>
        <result column="freightFee" property="freightFee" jdbcType="DECIMAL"/>
        <collection property="freightAddressRuleMarkupList" ofType="com.logistics.tms.api.feign.freight.model.FreightAddressRuleMarkupModel">
            <id column="freightAddressRuleMarkupId" property="freightAddressRuleMarkupId" jdbcType="BIGINT"/>
            <result column="markIndex" property="markIndex" jdbcType="INTEGER"/>
            <result column="loadAmount" property="loadAmount" jdbcType="INTEGER"/>
            <result column="unloadAmount" property="unloadAmount" jdbcType="INTEGER"/>
            <result column="markupFreightFee" property="markupFreightFee" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="getFreightAddressRuleDetail" resultMap="freightAddressDetailMap">
        select
            tfa.id as freightAddressId,
            tfa.freight_id as freightId,
            tfa.calc_type as calcType,
            tfa.to_province_name as toProvinceName,
            tfa.to_province_id as toProvinceId,
            tfa.to_city_name as toCityName,
            tfa.to_city_id as toCityId,
            tfa.to_area_name as toAreaName,
            tfa.to_area_id as toAreaId,
            tfa.from_province_name as fromProvinceName,
            tfa.from_province_id as fromProvinceId,
            tfa.from_city_name as fromCityName,
            tfa.from_city_id as fromCityId,
            tfa.from_area_name as fromAreaName,
            tfa.from_area_id as fromAreaId,
            tfa.warehouse_Id as warehouseId,
            twa.warehouse as warehouseName,

            tfar.id as freightAddressRuleId,
            tfar.rule_index as ruleIndex,
            tfar.amount_from as amountFrom ,
            tfar.from_symbol as fromSymbol,
            tfar.amount_to as amountTo,
            tfar.to_symbol as toSymbol,
            tfar.freight_type as freightType,
            tfar.freight_fee as freightFee,

            tfarm.id as freightAddressRuleMarkupId,
            tfarm.mark_index as markIndex,
            tfarm.load_amount as loadAmount,
            tfarm.unload_amount as unloadAmount,
            tfarm.markup_freight_fee as markupFreightFee

        from t_freight_address tfa
            left join t_warehouse_address twa on tfa.warehouse_id = twa.id and tfa.valid = 1
            left join t_freight_address_rule tfar on tfar.freight_address_id = tfa.id and tfar.valid = 1
            left join t_freight_address_rule_markup tfarm on tfarm.freight_address_rule_id = tfar.id and tfarm.valid = 1
        where tfa.valid = 1
            and tfa.id = #{freightAddressId,jdbcType = BIGINT}
    </select>


    <select id="getListByCondition" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_freight_address
        where valid = 1
        <if test="params.freightId!=null">
            and freight_id = #{params.freightId,jdbcType = BIGINT}
        </if>
        <if test="params.calcType!=null">
            and calc_Type = #{params.calcType,jdbcType = INTEGER}
        </if>
        <if test="params.warehouseId!=null">
            and warehouse_Id = #{params.warehouseId,jdbcType = BIGINT}
        </if>
        <if test="params.fromProvinceId!=null">
            and from_province_id = #{params.fromProvinceId,jdbcType = BIGINT}
        </if>
        <if test="params.fromCityId!=null">
            and from_city_id = #{params.fromCityId,jdbcType = BIGINT}
        </if>
        <if test="params.fromAreaId!=null">
            and from_area_id = #{params.fromAreaId,jdbcType = BIGINT}
        </if>
        <if test="params.toProvinceId!=null">
            and to_province_id = #{params.toProvinceId,jdbcType = BIGINT}
        </if>
        <if test="params.toCityId!=null">
            and to_city_id = #{params.toCityId,jdbcType = BIGINT}
        </if>
        <if test="params.toAreaId!=null">
            and to_area_id = #{params.toAreaId,jdbcType = BIGINT}
        </if>
    </select>

    <update id="deleteFreightAddress">
        update t_freight_address tfa
            left join t_freight_address_rule tfar on tfar.freight_address_id = tfa.id and tfar.valid = 1
            left join t_freight_address_rule_markup tfarm on tfarm.freight_address_rule_id = tfar.id and tfarm.valid = 1
        set
            tfa.valid = 0,tfa.last_modified_by = #{lastModifiedBy,jdbcType = VARCHAR},tfa.last_modified_time = #{lastModifiedTime,jdbcType = TIMESTAMP},
            tfar.valid = 0,tfar.last_modified_by = #{lastModifiedBy,jdbcType = VARCHAR},tfar.last_modified_time = #{lastModifiedTime,jdbcType = TIMESTAMP},
            tfarm.valid = 0,tfarm.last_modified_by = #{lastModifiedBy,jdbcType = VARCHAR},tfarm.last_modified_time = #{lastModifiedTime,jdbcType = TIMESTAMP}
        where tfa.id in (${freightAddressIds})
    </update>
</mapper>