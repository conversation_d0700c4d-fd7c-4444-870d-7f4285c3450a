package com.logistics.management.webapi.client.thirdparty.basicdata.user;

import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.thirdparty.basicdata.user.hystrix.UserClientHystrix;
import com.logistics.management.webapi.client.thirdparty.basicdata.user.request.GetUserInfoListRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.user.response.GetUserInfoListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/22 15:21
 */
@FeignClient(name = FeignClientName.BASIC_DATA_SERVICES, fallback = UserClientHystrix.class)
public interface UserClient {

    @ApiOperation(value = "查询用户列表（用户名+手机号模糊）")
    @PostMapping(value = "/service/user/getUserInfoList")
    Result<List<GetUserInfoListResponseModel>> getUserInfoList(@RequestBody GetUserInfoListRequestModel requestModel);

}
