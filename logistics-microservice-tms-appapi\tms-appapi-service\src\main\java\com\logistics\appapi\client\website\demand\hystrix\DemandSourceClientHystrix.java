package com.logistics.appapi.client.website.demand.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.website.demand.DemandSourceClient;
import com.logistics.appapi.client.website.demand.request.AddDemandSourceRequestModel;
import com.logistics.appapi.client.website.demand.request.DemandSourceListRequestModel;
import com.logistics.appapi.client.website.demand.response.DemandSourceListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/15 11:04
 */
@Component
public class DemandSourceClientHystrix implements DemandSourceClient {
    @Override
    public Result<PageInfo<DemandSourceListResponseModel>> searchList(DemandSourceListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> add(AddDemandSourceRequestModel requestModel) {
        return Result.timeout();
    }
}
