/**
 * Created by yun<PERSON>zhou on 2017/12/12.
 */
package com.logistics.tms.base.enums;

public enum FreightTypeEnum {

    DEFAULT(0, ""),
    UNIT_PRICE(1, "单价"),
    FIXED_PRICE(2, "一口价"),;

    private Integer key;
    private String value;

    FreightTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static FreightTypeEnum getEnum(Integer key) {
        for (FreightTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
