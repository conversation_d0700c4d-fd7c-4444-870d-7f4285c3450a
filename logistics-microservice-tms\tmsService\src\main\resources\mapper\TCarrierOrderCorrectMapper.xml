<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderCorrectMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierOrderCorrect" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="carrier_order_id" property="carrierOrderId" jdbcType="BIGINT" />
    <result column="correct_status" property="correctStatus" jdbcType="INTEGER" />
    <result column="stock_in_state" property="stockInState" jdbcType="INTEGER" />
    <result column="stock_in_count" property="stockInCount" jdbcType="DECIMAL" />
    <result column="stock_in_remark" property="stockInRemark" jdbcType="VARCHAR" />
    <result column="correct_type" property="correctType" jdbcType="INTEGER" />
    <result column="correct_user" property="correctUser" jdbcType="VARCHAR" />
    <result column="correct_time" property="correctTime" jdbcType="TIMESTAMP" />
    <result column="load_error_amount" property="loadErrorAmount" jdbcType="DECIMAL" />
    <result column="lose_error_amount" property="loseErrorAmount" jdbcType="DECIMAL" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, carrier_order_id, correct_status, stock_in_state, stock_in_count, stock_in_remark, 
    correct_type, correct_user, correct_time, load_error_amount, lose_error_amount, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_carrier_order_correct
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_carrier_order_correct
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierOrderCorrect" >
    insert into t_carrier_order_correct (id, carrier_order_id, correct_status, 
      stock_in_state, stock_in_count, stock_in_remark, 
      correct_type, correct_user, correct_time, 
      load_error_amount, lose_error_amount, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, #{correctStatus,jdbcType=INTEGER}, 
      #{stockInState,jdbcType=INTEGER}, #{stockInCount,jdbcType=DECIMAL}, #{stockInRemark,jdbcType=VARCHAR}, 
      #{correctType,jdbcType=INTEGER}, #{correctUser,jdbcType=VARCHAR}, #{correctTime,jdbcType=TIMESTAMP}, 
      #{loadErrorAmount,jdbcType=DECIMAL}, #{loseErrorAmount,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderCorrect" >
    insert into t_carrier_order_correct
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id,
      </if>
      <if test="correctStatus != null" >
        correct_status,
      </if>
      <if test="stockInState != null" >
        stock_in_state,
      </if>
      <if test="stockInCount != null" >
        stock_in_count,
      </if>
      <if test="stockInRemark != null" >
        stock_in_remark,
      </if>
      <if test="correctType != null" >
        correct_type,
      </if>
      <if test="correctUser != null" >
        correct_user,
      </if>
      <if test="correctTime != null" >
        correct_time,
      </if>
      <if test="loadErrorAmount != null" >
        load_error_amount,
      </if>
      <if test="loseErrorAmount != null" >
        lose_error_amount,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null" >
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="correctStatus != null" >
        #{correctStatus,jdbcType=INTEGER},
      </if>
      <if test="stockInState != null" >
        #{stockInState,jdbcType=INTEGER},
      </if>
      <if test="stockInCount != null" >
        #{stockInCount,jdbcType=DECIMAL},
      </if>
      <if test="stockInRemark != null" >
        #{stockInRemark,jdbcType=VARCHAR},
      </if>
      <if test="correctType != null" >
        #{correctType,jdbcType=INTEGER},
      </if>
      <if test="correctUser != null" >
        #{correctUser,jdbcType=VARCHAR},
      </if>
      <if test="correctTime != null" >
        #{correctTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loadErrorAmount != null" >
        #{loadErrorAmount,jdbcType=DECIMAL},
      </if>
      <if test="loseErrorAmount != null" >
        #{loseErrorAmount,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierOrderCorrect" >
    update t_carrier_order_correct
    <set >
      <if test="carrierOrderId != null" >
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="correctStatus != null" >
        correct_status = #{correctStatus,jdbcType=INTEGER},
      </if>
      <if test="stockInState != null" >
        stock_in_state = #{stockInState,jdbcType=INTEGER},
      </if>
      <if test="stockInCount != null" >
        stock_in_count = #{stockInCount,jdbcType=DECIMAL},
      </if>
      <if test="stockInRemark != null" >
        stock_in_remark = #{stockInRemark,jdbcType=VARCHAR},
      </if>
      <if test="correctType != null" >
        correct_type = #{correctType,jdbcType=INTEGER},
      </if>
      <if test="correctUser != null" >
        correct_user = #{correctUser,jdbcType=VARCHAR},
      </if>
      <if test="correctTime != null" >
        correct_time = #{correctTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loadErrorAmount != null" >
        load_error_amount = #{loadErrorAmount,jdbcType=DECIMAL},
      </if>
      <if test="loseErrorAmount != null" >
        lose_error_amount = #{loseErrorAmount,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierOrderCorrect" >
    update t_carrier_order_correct
    set carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      correct_status = #{correctStatus,jdbcType=INTEGER},
      stock_in_state = #{stockInState,jdbcType=INTEGER},
      stock_in_count = #{stockInCount,jdbcType=DECIMAL},
      stock_in_remark = #{stockInRemark,jdbcType=VARCHAR},
      correct_type = #{correctType,jdbcType=INTEGER},
      correct_user = #{correctUser,jdbcType=VARCHAR},
      correct_time = #{correctTime,jdbcType=TIMESTAMP},
      load_error_amount = #{loadErrorAmount,jdbcType=DECIMAL},
      lose_error_amount = #{loseErrorAmount,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>