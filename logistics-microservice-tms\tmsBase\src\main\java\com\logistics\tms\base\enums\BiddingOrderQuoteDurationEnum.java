package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/04/29
 */
@Getter
@AllArgsConstructor
public enum BiddingOrderQuoteDurationEnum {
    //报价时长：1 30分钟，2 60分钟，3 90分钟
    DEFAULT(0, -999999999),
    Q_30_MINUTES(1, 30),
    Q_60_MINUTES(2, 60),
    Q_90_MINUTES(3, 90),
    ;

    private final Integer key;
    private final Integer value;

    public static BiddingOrderQuoteDurationEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
