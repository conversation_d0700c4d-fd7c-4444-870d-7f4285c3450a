package com.logistics.appapi.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/17
 */
@Data
public class PublishRenewableOrderRequestDto {

	//发货地址
	@ApiModelProperty("发货地址code（新生客户地址code）")
	private String loadAddressCode;

	@ApiModelProperty(value = "发货省id", required = true)
	@NotBlank(message = "发货省不能为空")
	private String loadProvinceId;

	@ApiModelProperty(value = "发货省", required = true)
	@NotBlank(message = "发货省不能为空")
	private String loadProvinceName;

	@ApiModelProperty(value = "发货市id", required = true)
	@NotBlank(message = "发货市不能为空")
	private String loadCityId;

	@ApiModelProperty(value = "发货市", required = true)
	@NotBlank(message = "发货市不能为空")
	private String loadCityName;

	@ApiModelProperty(value = "发货区ID", required = true)
	@NotBlank(message = "发货区不能为空")
	private String loadAreaId;

	@ApiModelProperty(value = "发货区", required = true)
	@NotBlank(message = "发货区不能为空")
	private String loadAreaName;

	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress;

	@ApiModelProperty("发货仓库")
	private String loadWarehouse;

	@ApiModelProperty(value = "发货人", required = true)
	@NotBlank(message = "发货人不能为空")
	private String consignorName;

	@ApiModelProperty(value = "发货人手机号", required = true)
	@NotBlank(message = "发货人手机号不能为空")
	private String consignorMobile;

	@ApiModelProperty(value = "发货人所属客户账号（手机号）",required = true)
	@NotBlank(message = "客户账号不能为空")
	private String customerAccount;


	//收货地址
	@ApiModelProperty(value = "收货地址code（云仓仓库地址code）",required = true)
	@NotBlank(message = "收货地址编码不能为空")
	private String unloadAddressCode;

	@ApiModelProperty(value = "收货省id", required = true)
	@NotBlank(message = "收货省不能为空")
	private String unloadProvinceId;

	@ApiModelProperty(value = "收货省", required = true)
	@NotBlank(message = "收货省不能为空")
	private String unloadProvinceName;

	@ApiModelProperty(value = "收货市id", required = true)
	@NotBlank(message = "收货市不能为空")
	private String unloadCityId;

	@ApiModelProperty(value = "收货市", required = true)
	@NotBlank(message = "收货市不能为空")
	private String unloadCityName;

	@ApiModelProperty(value = "收货区id", required = true)
	@NotBlank(message = "收货区不能为空")
	private String unloadAreaId;

	@ApiModelProperty(value = "收货区", required = true)
	@NotBlank(message = "收货区不能为空")
	private String unloadAreaName;

	@ApiModelProperty("收货地址详细")
	private String unloadDetailAddress;

	@ApiModelProperty(value = "收货仓库", required = true)
	@NotBlank(message = "收货仓库不能为空")
	private String unloadWarehouse;

	@ApiModelProperty(value = "收货人", required = true)
	@NotBlank(message = "收货人不能为空")
	private String receiverName;

	@ApiModelProperty(value = "收货人手机号", required = true)
	@NotBlank(message = "收货人手机号不能为空")
	private String receiverMobile;


	//确认单据
	@ApiModelProperty(value = "现场图片",required = true)
	@NotEmpty(message = "现场图片不能为空")
	@Size(min = 1, max = 6, message = "现场图片最少1张，最多6张")
	private List<String> scenePictureList;

	@ApiModelProperty(value = "确认单据",required = true)
	@NotEmpty(message = "确认单据不能为空")
	@Size(min = 1, max = 6, message = "确认单据最少1张，最多6张")
	private List<String> confirmPictureList;


	//货物信息
	@Valid
	@ApiModelProperty(value = "新生订单货物信息", required = true)
	@NotEmpty(message = "货物不能为空，每单最多5种货物")
	@Size(min = 1, max = 5, message = "货物不能为空，每单最多5种货物")
	private List<RenewableOrderGoodRequestDto> renewableOrderGoods;
}
