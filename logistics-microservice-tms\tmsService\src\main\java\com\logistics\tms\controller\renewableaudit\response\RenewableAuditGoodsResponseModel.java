package com.logistics.tms.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RenewableAuditGoodsResponseModel {

    @ApiModelProperty(value = "货物id")
    private Long renewableGoodsId;
    @ApiModelProperty(value = "sku编码")
    private String skuCode;
    @ApiModelProperty(value = "货物名称")
    private String goodsName;
    @ApiModelProperty(value = "数量")
    private BigDecimal goodsAmount;
    @ApiModelProperty(value = "单价")
    private BigDecimal goodsPrice;
    @ApiModelProperty(value = "货物单位：1 件，2 KG")
    private Integer goodsUnit;
    @ApiModelProperty(value = "货物类型：1 新生同步，2 司机确认")
    private Integer goodsSourceType;
}
