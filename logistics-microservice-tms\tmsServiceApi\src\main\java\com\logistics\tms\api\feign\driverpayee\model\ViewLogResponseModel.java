package com.logistics.tms.api.feign.driverpayee.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * Created by yuhong.lin on 2019/1/22
 */
@Data
public class ViewLogResponseModel {
    @ApiModelProperty("操作人ID")
    private Long objectId;
    @ApiModelProperty("操作人")
    private String operateUserName;
    @ApiModelProperty("操作时间")
    private Date operateTime;
    @ApiModelProperty("操作类型")
    private Integer operateType;
    @ApiModelProperty("操作内容")
    private String operateContents;
    @ApiModelProperty("备注")
    private String remark;
}
