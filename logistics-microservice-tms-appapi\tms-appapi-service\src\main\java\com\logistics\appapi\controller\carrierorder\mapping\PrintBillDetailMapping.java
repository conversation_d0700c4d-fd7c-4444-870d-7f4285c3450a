package com.logistics.appapi.controller.carrierorder.mapping;

import com.logistics.appapi.base.enums.CarrierOrderStatusEnum;
import com.logistics.appapi.base.enums.EncodeTypeEnum;
import com.logistics.appapi.base.enums.GoodsUnitEnum;
import com.logistics.appapi.base.utils.FrequentMethodUtils;
import com.logistics.appapi.client.carrierorder.response.PrintBillDetailResponseModel;
import com.logistics.appapi.controller.carrierorder.response.PrintBillDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2023/5/5 14:26
 */
public class PrintBillDetailMapping extends MapperMapping<PrintBillDetailResponseModel, PrintBillDetailResponseDto> {
    @Override
    public void configure() {
        PrintBillDetailResponseModel source = getSource();
        PrintBillDetailResponseDto destination = getDestination();

        String unit = GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit();
        //预计货物数量
        destination.setExpectAmount(source.getExpectAmount().stripTrailingZeros().toPlainString() + unit);
        //实际提货数量
        if (source.getStatus() > CarrierOrderStatusEnum.WAIT_LOAD.getKey()){
            destination.setLoadAmount(source.getLoadAmount().stripTrailingZeros().toPlainString() + unit);
        }else{
            destination.setLoadAmount("");
        }

        //发货联系人
        destination.setLoadPerson(FrequentMethodUtils.encryptionData(source.getLoadPerson(), EncodeTypeEnum.CUSTOMER_NAME));
        //发货联系方式
        destination.setLoadMobile(FrequentMethodUtils.encryptionData(source.getLoadMobile(), EncodeTypeEnum.MOBILE_PHONE));

        //收货联系人
        destination.setUnloadPerson(FrequentMethodUtils.encryptionData(source.getUnloadPerson(), EncodeTypeEnum.CUSTOMER_NAME));
        //收货联系方式
        destination.setUnloadMobile(FrequentMethodUtils.encryptionData(source.getUnloadMobile(), EncodeTypeEnum.MOBILE_PHONE));
        //收货地址
        destination.setUnloadAddress(source.getUnloadProvinceName()+source.getUnloadCityName()+source.getUnloadAreaName()+source.getUnloadDetailAddress());
    }
}
