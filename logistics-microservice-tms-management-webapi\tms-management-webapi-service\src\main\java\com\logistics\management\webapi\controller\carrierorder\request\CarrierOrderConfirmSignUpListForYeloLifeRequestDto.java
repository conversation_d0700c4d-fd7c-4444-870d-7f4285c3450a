package com.logistics.management.webapi.controller.carrierorder.request;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2024/7/22 10:05
 */
@Data
public class CarrierOrderConfirmSignUpListForYeloLifeRequestDto {

    /**
     * 运单ID
     */
    @NotBlank(message = "id不能为空")
    private String carrierOrderId;

    /**
     * 实际货主费用
     */
    @NotBlank(message = "请填写实际货主费用")
    @DecimalMin(value = "0", message = "实际货主费用要大于等于0元")
    @DecimalMax(value = "10000000", message = "实际货主费用要小于等于10000000元")
    private String actualEntrustFee;

    /**
     * 实际车主运费
     */
    @NotBlank(message = "请填写实际车主费用")
    @DecimalMin(value = "0", message = "实际车主费用要大于等于0元")
    @DecimalMax(value = "10000000", message = "实际车主费用要小于等于10000000元")
    private String signCarrierFreight;

}
