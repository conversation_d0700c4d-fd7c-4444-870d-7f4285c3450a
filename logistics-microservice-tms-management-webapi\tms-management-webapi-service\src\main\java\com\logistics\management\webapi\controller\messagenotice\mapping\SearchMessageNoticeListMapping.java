package com.logistics.management.webapi.controller.messagenotice.mapping;

import com.logistics.management.webapi.base.enums.ReadStatusEnum;
import com.logistics.management.webapi.client.messagenotice.response.SearchMessageNoticeListResponseModel;
import com.logistics.management.webapi.controller.messagenotice.response.SearchMessageNoticeListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;

/**
 * @author: wjf
 * @date: 2024/6/5 16:21
 */
public class SearchMessageNoticeListMapping extends MapperMapping<SearchMessageNoticeListResponseModel, SearchMessageNoticeListResponseDto> {
    @Override
    public void configure() {
        SearchMessageNoticeListResponseModel source = getSource();
        SearchMessageNoticeListResponseDto destination = getDestination();

        destination.setMessageStatus(ConverterUtils.toString(source.getIfRead()));
        destination.setMessageStatusLabel(ReadStatusEnum.getEnumByKey(source.getIfRead()).getValue());
    }
}
