package com.logistics.management.webapi.client.settlestatement.tradition.response;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TraditionAssociationCarrierOrderResponseModel {

	@ApiModelProperty("运单数量合计")
	private Integer carrierOrderAmount;

	@ApiModelProperty("委托费用")
	private BigDecimal entrustFreight;

	@ApiModelProperty("总吨数")
	private BigDecimal totalWeightSettlementAmount;

	@ApiModelProperty("总件数")
	private BigDecimal totalPackageSettlementAmount;

	@ApiModelProperty("总块数")
	private BigDecimal totalPieceSettlementAmount;

	@ApiModelProperty("运单列表")
	private PageInfo<TraditionAssociationCarrierOrderItemModel> carrierOrderItemList;
}
