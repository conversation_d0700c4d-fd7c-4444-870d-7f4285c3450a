package com.logistics.tms.controller.companycarrier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/6 13:43
 */
@Data
public class OpenCloseLessThanTruckloadRequestModel {

    /**
     * 车主id
     */
    @ApiModelProperty(value = "车主id")
    private List<Long> companyCarrierIdList;

    /**
     * 操作类型：1 开启，2 关闭
     */
    @ApiModelProperty(value = "操作类型：1 开启，2 关闭")
    private Integer operateType;

}
