package com.logistics.management.webapi.client.biddingorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ModifyQuoteRequestModel {

    @ApiModelProperty("报价单id")
    private Long biddingOrderQuoteId;

    @ApiModelProperty("竞价金额类型：1 单价，2 一口价")
    private Integer biddingPriceType;

    @ApiModelProperty("报价金额")
    private BigDecimal biddingPrice;


    @ApiModelProperty("车长id")
    private Long vehicleLengthId;

    @ApiModelProperty("车长")
    private BigDecimal vehicleLength;




}
