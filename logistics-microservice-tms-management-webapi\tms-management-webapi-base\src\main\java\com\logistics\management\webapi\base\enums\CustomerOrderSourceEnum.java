package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2022/9/20 17:31
 */
public enum CustomerOrderSourceEnum {
    DEFAULT(0,""),
    YELO_LIFE(1,"乐橘新生客户"),
    DRIVER(2,"司机"),
    ;


    private Integer key;
    private String value;

    CustomerOrderSourceEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }
    public static CustomerOrderSourceEnum getEnum(Integer key) {
        for (CustomerOrderSourceEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
