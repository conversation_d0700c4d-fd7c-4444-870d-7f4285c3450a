package com.logistics.management.webapi.client.region.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/10/18 10:33
 */
@Data
public class SaveOrModifyRegionRequestModel {
    @ApiModelProperty("大区ID")
    private Long regionId;
    @ApiModelProperty("大区名称")
    private String regionName;
    @ApiModelProperty("大区负责人")
    private String regionContactName;
    @ApiModelProperty("大区负责人手机号")
    private String regionContactPhone;
    @ApiModelProperty("城市集合 批量字符串")
    private List<ProvinceRequestModel> provinceDtoList;

    /**
     * 车主id
     */
    @ApiModelProperty("车主id")
    private List<Long> companyCarrierIdList;
}
