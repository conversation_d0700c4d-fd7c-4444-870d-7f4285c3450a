package com.logistics.tms.client.feign.warehouse.lift.hystrix;

import com.logistics.tms.client.feign.warehouse.lift.WarehouseLifeServiceApi;
import com.logistics.tms.client.feign.warehouse.lift.reponse.GetWarehouseDetailForLifeResponseModel;
import com.logistics.tms.client.feign.warehouse.lift.request.GetWarehouseDetailForLifeRequestModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/4 16:36
 */
@Component
public class WarehouseLifeServiceApiHystrix implements WarehouseLifeServiceApi {
    @Override
    public Result<List<GetWarehouseDetailForLifeResponseModel>> getWarehouseDetailForLife(GetWarehouseDetailForLifeRequestModel requestModel) {
        return Result.timeout();
    }
}
