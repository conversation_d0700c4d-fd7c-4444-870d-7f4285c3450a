package com.logistics.management.webapi.api.impl.freight.mapping;

import com.logistics.management.webapi.api.feign.freight.dto.FreightAddressRuleDetailResponseDto;
import com.logistics.management.webapi.api.feign.freight.dto.FreightAddressRuleDto;
import com.logistics.management.webapi.api.feign.freight.dto.FreightAddressRuleMarkupDto;
import com.logistics.tms.api.feign.freight.model.FreightAddressRuleDetailResponseModel;
import com.logistics.tms.api.feign.freight.model.FreightAddressRuleMarkupModel;
import com.logistics.tms.api.feign.freight.model.FreightAddressRuleModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/12/26 15:33
 */
public class GetFreightRuleDetailMapping extends MapperMapping<FreightAddressRuleDetailResponseModel,FreightAddressRuleDetailResponseDto> {
    @Override
    public void configure() {
        FreightAddressRuleDetailResponseModel model = this.getSource();
        FreightAddressRuleDetailResponseDto dto  = this.getDestination();
        if(model != null){
            if(ListUtils.isNotEmpty(model.getFreightAddressRuleList())){

                List<FreightAddressRuleDto> ruleDtoList = new ArrayList<>();
                FreightAddressRuleDto ruleDto;

                List<FreightAddressRuleMarkupDto> markupDtoList = new ArrayList<>();
                FreightAddressRuleMarkupDto markupDto;
                List<FreightAddressRuleModel> freightAddressRuleList =  model.getFreightAddressRuleList();
                if(ListUtils.isNotEmpty(freightAddressRuleList)){
                    Collections.sort(freightAddressRuleList,(o1,o2)->o1.getRuleIndex().compareTo(o2.getRuleIndex()));
                }
                for (FreightAddressRuleModel tempModel : freightAddressRuleList) {
                    ruleDto = new FreightAddressRuleDto();
                    ruleDto.setFreightAddressRuleId(ConverterUtils.toString(tempModel.getFreightAddressRuleId()));
                    ruleDto.setAmountFrom(tempModel.getAmountFrom().stripTrailingZeros().toPlainString());
                    ruleDto.setAmountTo(tempModel.getAmountTo().stripTrailingZeros().toPlainString());
                    ruleDto.setFreightFee(tempModel.getFreightFee().stripTrailingZeros().toPlainString());
                    ruleDto.setFromSymbol(ConverterUtils.toString(tempModel.getFromSymbol()));
                    ruleDto.setToSymbol(ConverterUtils.toString(tempModel.getToSymbol()));
                    ruleDto.setFreightType(ConverterUtils.toString(tempModel.getFreightType()));
                    ruleDto.setRuleIndex(ConverterUtils.toString(tempModel.getRuleIndex()));
                    ruleDtoList.add(ruleDto);

                    if(ListUtils.isNotEmpty( tempModel.getFreightAddressRuleMarkupList())){
                        for (FreightAddressRuleMarkupModel markupModel : tempModel.getFreightAddressRuleMarkupList()) {
                            markupDto = new FreightAddressRuleMarkupDto();
                            markupDto.setFreightAddressRuleMarkupId(ConverterUtils.toString(markupModel.getFreightAddressRuleMarkupId()));
                            markupDto.setRuleIndex(ConverterUtils.toString(tempModel.getRuleIndex()));
                            markupDto.setMarkIndex(ConverterUtils.toString(markupModel.getMarkIndex()));
                            markupDto.setLoadAmount(ConverterUtils.toString(markupModel.getLoadAmount()));
                            markupDto.setUnloadAmount(ConverterUtils.toString(markupModel.getUnloadAmount()));
                            markupDto.setMarkupFreightFee(markupModel.getMarkupFreightFee().stripTrailingZeros().toPlainString());
                            markupDtoList.add(markupDto);
                        }
                    }
                }
                if(ListUtils.isNotEmpty(markupDtoList)){
                    Collections.sort(markupDtoList,(o1,o2)->ConverterUtils.toInt(o1.getMarkIndex()).compareTo(ConverterUtils.toInt(o2.getMarkIndex())));
                }

                dto.setFreightAddressRuleList(ruleDtoList);
                dto.setFreightAddressRuleMarkupList(markupDtoList);
            }
        }
    }
}
