package com.logistics.tms.api.feign.vehiclepayeerel.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/7/11 14:08
 */
@Data
public class SearchVehiclePayeeRelListRequestModel extends AbstractPageForm<SearchVehiclePayeeRelListRequestModel> {
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("关联收款账户")
    private String driverPayee;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("操作时间")
    private String lastModifiedTimeStart;
    private String lastModifiedTimeEnd;
}
