package com.logistics.tms.biz.demandorder

import com.fasterxml.jackson.databind.ObjectMapper
import com.logistics.entrust.api.feign.demandorder.DemandOrderServiceApi
import com.logistics.tms.api.feign.freight.model.GetPriceByAddressAndAmountResponseModel
import com.logistics.tms.base.enums.DemandOrderEventsTypeEnum
import com.logistics.tms.base.enums.DemandOrderOperateLogsEnum
import com.logistics.tms.biz.carrierorder.CarrierOrderBiz
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz
import com.logistics.tms.biz.carrierorder.model.CancelCarrierOrderImpactDemandOrderModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.biz.demandorder.model.SyncTMSDemandOrderModel
import com.logistics.tms.biz.freight.FreightBiz
import com.logistics.tms.controller.carrierorder.response.CarrierOrderLoadAmountSyncModel
import com.logistics.tms.controller.carrierorder.response.GetCarrierOrderResponseModel
import com.logistics.tms.controller.demandorder.request.*
import com.logistics.tms.controller.demandorder.response.*
import com.logistics.tms.entity.*
import com.logistics.tms.mapper.*
import com.logistics.tms.rabbitmq.consumer.model.SyncLogisticsWithdrawOrderModel
import com.logistics.tms.rabbitmq.consumer.model.SyncTrayDemandOrderMessage
import com.yelo.tools.rabbitmq.producer.RabbitMqSender
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class DemandOrderBizTest extends Specification {
    @Mock
    TDemandOrderMapper demandOrderMapper
    @Mock
    TDemandOrderAddressMapper demandOrderAddressMapper
    @Mock
    TDemandOrderGoodsMapper demandOrderGoodsMapper
    @Mock
    TDemandOrderEventsMapper demandOrderEventsMapper
    @Mock
    TDemandOrderOperateLogsMapper demandOrderOperateLogsMapper
    @Mock
    TDemandOrderOrderRelMapper tDemandOrderOrderRelMapper
    @Mock
    TCompanyEntrustMapper companyEntrustMapper
    @Mock
    TEntrustAddressMapper entrustAddressMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    CarrierOrderBiz carrierOrderBiz
    @Mock
    TDemandPaymentMapper tDemandPaymentMapper
    @Mock
    TDemandReceivementMapper tDemandReceivementMapper
    @Mock
    CarrierOrderCommonBiz carrierOrderCommonBiz
    @Mock
    TCompanyCarrierMapper companyCarrierMapper
    @Mock
    FreightBiz freightBiz
    @Mock
    TDemandOrderGoodsRelMapper tDemandOrderGoodsRelMapper
    @Mock
    RabbitMqSender rabbitMqSender
    @Mock
    DemandOrderServiceApi entrustDemandOrderServiceApi
    @Mock
    DemandOrderCommonBiz demandOrderCommonBiz
    @Mock
    TDemandOrderObjectionMapper tDemandOrderObjectionMapper
    @Mock
    ObjectMapper objectMapper
    @Mock
    Logger log
    @InjectMocks
    DemandOrderBiz demandOrderBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "save Demand Order where requestModel=#requestModel"() {
        given:
        when(demandOrderGoodsMapper.batchInsert(any())).thenReturn(0)
        when(companyEntrustMapper.getByName(anyString())).thenReturn(new TCompanyEntrust(settlementTonnage: 0))
        when(entrustAddressMapper.getByCompanyAndAddress(any())).thenReturn(new TEntrustAddress(companyEntrustId: 1l, companyName: "companyName", addressType: 0, provinceId: 1l, provinceName: "provinceName", cityId: 1l, cityName: "cityName", areaId: 1l, areaName: "areaName", detailAddress: "detailAddress", warehouse: "warehouse", contactName: "contactName", contactMobile: "contactMobile"))
        when(entrustAddressMapper.batchInsert(any())).thenReturn(0)
        when(entrustAddressMapper.batchUpdate(any())).thenReturn(0)
        when(commonBiz.getBusinessTypeCode(any(), anyString(), anyString())).thenReturn("getBusinessTypeCodeResponse")
        when(commonBiz.getBaseConfigMap(any())).thenReturn(["String": "String"])
        when(companyCarrierMapper.getByName(anyString())).thenReturn(new TCompanyCarrier())
        when(freightBiz.getEntrustFreightInfo(any())).thenReturn(new GetPriceByAddressAndAmountResponseModel())

        expect:
        demandOrderBiz.saveDemandOrder(requestModel)
        assert expectedResult == false

        where:
        requestModel                      || expectedResult
        new SaveDemandOrderRequestModel() || true
    }

    @Unroll
    def "operation Demand Order By Dispatch Vehicle where requestModel=#requestModel"() {
        given:
        when(demandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(entrustStatus: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 12).getTime(), status: 0, source: 0, demandOrderCode: "demandOrderCode", publishTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 12).getTime(), arrangedAmount: 0 as BigDecimal, notArrangedAmount: 0 as BigDecimal, dispatchVehicleCount: 0, entrustType: 0, ifUrgent: 0, dispatchValidity: 0, ifOverdue: 0)])
        when(demandOrderMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderGoodsMapper.getDemandOrderGoodsByGoodsIds(anyString())).thenReturn([new TDemandOrderGoods(demandOrderId: 1l, arrangedAmount: 0 as BigDecimal, notArrangedAmount: 0 as BigDecimal)])
        when(demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(demandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.getByIds(anyString())).thenReturn([new TDemandOrderOrderRel(arrangedAmount: 0 as BigDecimal)])
        when(tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(commonBiz.differentDays(any(), any())).thenReturn(0)
        when(tDemandOrderGoodsRelMapper.getByDemandOrderIds(anyString())).thenReturn([new TDemandOrderGoodsRel(demandOrderGoodsId: 1l, bookingOrderGoodsId: 1l)])

        expect:
        demandOrderBiz.operationDemandOrderByDispatchVehicle(requestModel)
        assert expectedResult == false

        where:
        requestModel                                     || expectedResult
        new OperationDemandOrderByDispatchVehicleModel() || true
    }

    @Unroll
    def "create Carrier Order To Demand Order Log where demandOrderLogMap=#demandOrderLogMap and completeDispatchList=#completeDispatchList"() {
        given:
        when(demandOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(demandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)

        expect:
        demandOrderBiz.createCarrierOrderToDemandOrderLog(demandOrderLogMap, completeDispatchList)
        assert expectedResult == false

        where:
        demandOrderLogMap | completeDispatchList || expectedResult
        [(1l): "String"]  | [1l]                 || true
    }

    @Unroll
    def "get Demand Order Operate Logs where demandOrderId=#demandOrderId and remark=#remark and logsEnum=#logsEnum and userName=#userName then expect: #expectedResult"() {
        expect:
        demandOrderBiz.getDemandOrderOperateLogs(demandOrderId, logsEnum, userName, remark) == expectedResult

        where:
        demandOrderId | remark   | logsEnum                                       | userName   || expectedResult
        1l            | "remark" | DemandOrderOperateLogsEnum.CREATE_DEMAND_ORDER | "userName" || new TDemandOrderOperateLogs(demandOrderId: 1l, operationType: 0, operationContent: "operationContent", remark: "remark", operatorName: "operatorName", operateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 12).getTime())
    }

    @Unroll
    def "get Demand Order Event where typeEnum=#typeEnum and demandOrderId=#demandOrderId and userName=#userName then expect: #expectedResult"() {
        expect:
        demandOrderBiz.getDemandOrderEvent(demandOrderId, typeEnum, userName) == expectedResult

        where:
        typeEnum                                      | demandOrderId | userName   || expectedResult
        DemandOrderEventsTypeEnum.ACCEPT_CARRIERORDER | 1l            | "userName" || new TDemandOrderEvents(demandOrderId: 1l, event: 0, eventDesc: "eventDesc", eventTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 12).getTime(), operatorName: "operatorName", operateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 12).getTime())
    }

    @Unroll
    def "cancel Carrier Order Update Demand Order where responseModels=#responseModels"() {
        given:
        when(demandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(entrustStatus: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 12).getTime(), status: 0, source: 0, demandOrderCode: "demandOrderCode", arrangedAmount: 0 as BigDecimal, notArrangedAmount: 0 as BigDecimal, entrustType: 0, dispatchValidity: 0, ifOverdue: 0)])
        when(demandOrderMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderGoodsMapper.getDemandOrderGoodsByGoodsIds(anyString())).thenReturn([new TDemandOrderGoods(arrangedAmount: 0 as BigDecimal, notArrangedAmount: 0 as BigDecimal)])
        when(demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.getByIds(anyString())).thenReturn([new TDemandOrderOrderRel(arrangedAmount: 0 as BigDecimal)])
        when(tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)

        expect:
        demandOrderBiz.cancelCarrierOrderUpdateDemandOrder(responseModels)
        assert expectedResult == false

        where:
        responseModels                                 || expectedResult
        new CancelCarrierOrderImpactDemandOrderModel() || true
    }

    @Unroll
    def "update Demand Order Status By Ids where requestModel=#requestModel"() {
        given:
        when(demandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(entrustStatus: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 12).getTime(), ifCancel: 0, source: 0, demandOrderCode: "demandOrderCode", settlementTonnage: 0, carrierSettlement: 0, goodsAmount: 0 as BigDecimal, differenceAmount: 0 as BigDecimal, companyEntrustId: 1l, companyCarrierId: 1l, entrustType: 0, carrierPriceType: 0, carrierPrice: 0 as BigDecimal, ifEmpty: 0)])
        when(demandOrderMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(demandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(commonBiz.getQiyaCompanyCarrierId()).thenReturn(1l)
        when(commonBiz.getLeyiCompanyEntrustId()).thenReturn(1l)
        when(carrierOrderBiz.getCarrierOrderByDemandOrderIds(any())).thenReturn([new GetCarrierOrderResponseModel()])
        when(tDemandPaymentMapper.batchInsert(any())).thenReturn(0)
        when(tDemandReceivementMapper.batchInsert(any())).thenReturn(0)

        expect:
        demandOrderBiz.updateDemandOrderStatusByIds(requestModel)
        assert expectedResult == false

        where:
        requestModel                                   || expectedResult
        new UpdateDemandOrderStatusByIdsRequestModel() || true
    }

    @Unroll
    def "create Settlement Cost where model=#model"() {
        given:
        when(demandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(source: 0, demandOrderCode: "demandOrderCode", settlementTonnage: 0, carrierSettlement: 0, goodsAmount: 0 as BigDecimal, companyEntrustId: 1l, companyCarrierId: 1l, entrustType: 0, carrierPriceType: 0, carrierPrice: 0 as BigDecimal)])
        when(commonBiz.getQiyaCompanyCarrierId()).thenReturn(1l)
        when(commonBiz.getLeyiCompanyEntrustId()).thenReturn(1l)
        when(carrierOrderBiz.getCarrierOrderByDemandOrderIds(any())).thenReturn([new GetCarrierOrderResponseModel()])
        when(tDemandPaymentMapper.batchInsert(any())).thenReturn(0)
        when(tDemandReceivementMapper.batchInsert(any())).thenReturn(0)

        expect:
        demandOrderBiz.createSettlementCost(model)
        assert expectedResult == false

        where:
        model                                         || expectedResult
        new CreateSettlementForEntrustConsumerModel() || true
    }

    @Unroll
    def "get Demand Actual Fee where demandOrderIdList=#demandOrderIdList then expect: #expectedResult"() {
        given:
        when(demandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(source: 0, settlementTonnage: 0)])
        when(carrierOrderBiz.getCarrierOrderByDemandOrderIds(any())).thenReturn([new GetCarrierOrderResponseModel()])

        expect:
        demandOrderBiz.getDemandActualFee(demandOrderIdList) == expectedResult

        where:
        demandOrderIdList || expectedResult
        [1l]              || [(1l): 0 as BigDecimal]
    }

    @Unroll
    def "search List Manage where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(demandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(source: 0, settlementTonnage: 0)])
        when(demandOrderMapper.searchListManageAddressGoodsDemand(any(), anyString())).thenReturn([new DemandOrderResponseModel()])
        when(demandOrderMapper.searchListManageIds(any())).thenReturn([1l])
        when(carrierOrderBiz.getCarrierOrderByDemandOrderIds(any())).thenReturn([new GetCarrierOrderResponseModel()])

        expect:
        demandOrderBiz.searchListManage(requestModel) == expectedResult

        where:
        requestModel                        || expectedResult
        new DemandOrderSearchRequestModel() || null
    }

    @Unroll
    def "search List Statistics where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(demandOrderMapper.searchListStatistics(any())).thenReturn(new SearchListStatisticsResponseModel())

        expect:
        demandOrderBiz.searchListStatistics(requestModel) == expectedResult

        where:
        requestModel                        || expectedResult
        new DemandOrderSearchRequestModel() || new SearchListStatisticsResponseModel()
    }

    @Unroll
    def "cancel Demand Order where requestModel=#requestModel"() {
        given:
        when(demandOrderMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderMapper.getDemandOrderInfoByIds(anyString())).thenReturn([new GetDemandOrderInfoByIdsModel()])
        when(demandOrderGoodsMapper.getDemandOrderGoodsByDemandOrderIds(anyString())).thenReturn([new TDemandOrderGoods(notArrangedAmount: 0 as BigDecimal, backAmount: 0 as BigDecimal)])
        when(demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(demandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(anyString())).thenReturn([new TDemandOrderOrderRel(demandOrderId: 1l, orderId: 1l, totalAmount: 0 as BigDecimal, arrangedAmount: 0 as BigDecimal, backAmount: 0 as BigDecimal)])
        when(tDemandOrderObjectionMapper.batchInsert(any())).thenReturn(0)
        when(tDemandOrderObjectionMapper.batchUpdate(any())).thenReturn(0)
        when(tDemandOrderObjectionMapper.getByDemandOrderIds(anyString())).thenReturn([new TDemandOrderObjection(demandOrderId: 1l, customerName: "customerName", objectionType: 0, objectionReason: "objectionReason", reportContactName: "reportContactName", reportTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 12).getTime())])

        expect:
        demandOrderBiz.cancelDemandOrder(requestModel)
        assert expectedResult == false

        where:
        requestModel                        || expectedResult
        new DemandOrderCancelRequestModel() || true
    }

    @Unroll
    def "get Demand Order Logs where demandOrderId=#demandOrderId then expect: #expectedResult"() {
        given:
        when(demandOrderOperateLogsMapper.getDemandOrderLogs(anyLong())).thenReturn([new GetDemandOrderLogsResponseModel()])

        expect:
        demandOrderBiz.getDemandOrderLogs(demandOrderId) == expectedResult

        where:
        demandOrderId || expectedResult
        1l            || [new GetDemandOrderLogsResponseModel()]
    }

    @Unroll
    def "auto Sign Demand Order"() {
        given:
        when(demandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(entrustStatus: 0, statusUpdateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 12).getTime(), source: 0, demandOrderCode: "demandOrderCode", settlementTonnage: 0, carrierSettlement: 0, goodsAmount: 0 as BigDecimal, companyEntrustId: 1l, companyCarrierId: 1l, entrustType: 0, carrierPriceType: 0, carrierPrice: 0 as BigDecimal)])
        when(demandOrderMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(demandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(commonBiz.getQiyaCompanyCarrierId()).thenReturn(1l)
        when(commonBiz.getLeyiCompanyEntrustId()).thenReturn(1l)
        when(carrierOrderBiz.getCarrierOrderByDemandOrderIds(any())).thenReturn([new GetCarrierOrderResponseModel()])
        when(tDemandPaymentMapper.batchInsert(any())).thenReturn(0)
        when(tDemandReceivementMapper.batchInsert(any())).thenReturn(0)

        expect:
        demandOrderBiz.autoSignDemandOrder()
        assert expectedResult == false

        where:
        expectedResult << true
    }

    @Unroll
    def "get Publish Demand Order Detail where demandOrderIdRequestModel=#demandOrderIdRequestModel then expect: #expectedResult"() {
        given:
        when(demandOrderMapper.getPublishDemandOrderDetail(anyLong())).thenReturn(new GetPublishDemandOrderDetailResponseModel())

        expect:
        demandOrderBiz.getPublishDemandOrderDetail(demandOrderIdRequestModel) == expectedResult

        where:
        demandOrderIdRequestModel       || expectedResult
        new DemandOrderIdRequestModel() || new GetPublishDemandOrderDetailResponseModel()
    }

    @Unroll
    def "save Sync Tray Stock Plan Demand Order To Tms where message=#message"() {
        given:
        when(demandOrderGoodsMapper.batchInsert(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.batchInsertDemandOrderOrderRelSelective(any())).thenReturn(0)
        when(companyEntrustMapper.getByName(anyString())).thenReturn(new TCompanyEntrust(settlementTonnage: 0))
        when(commonBiz.getLeyiCompanyName()).thenReturn("getLeyiCompanyNameResponse")
        when(tDemandOrderGoodsRelMapper.batchInsert(any())).thenReturn(0)

        expect:
        demandOrderBiz.saveSyncTrayStockPlanDemandOrderToTms(message)
        assert expectedResult == false

        where:
        message                          || expectedResult
        new SyncTrayDemandOrderMessage() || true
    }

    @Unroll
    def "sync Carrier Load Back Amount where syncModels=#syncModels then expect: #expectedResult"() {
        given:
        when(demandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(status: 0, source: 0, demandOrderCode: "demandOrderCode", backAmount: 0 as BigDecimal, entrustType: 0)])
        when(demandOrderMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderGoodsMapper.getDemandOrderGoodsByDemandOrderIds(anyString())).thenReturn([new TDemandOrderGoods(demandOrderId: 1l, goodsName: "goodsName", categoryName: "categoryName", length: 0, width: 0, height: 0, arrangedAmount: 0 as BigDecimal, notArrangedAmount: 0 as BigDecimal, backAmount: 0 as BigDecimal)])
        when(demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(anyString())).thenReturn([new TDemandOrderOrderRel(demandOrderId: 1l, orderId: 1l, arrangedAmount: 0 as BigDecimal, backAmount: 0 as BigDecimal)])

        expect:
        demandOrderBiz.syncCarrierLoadBackAmount(syncModels) == expectedResult

        where:
        syncModels                              || expectedResult
        [new CarrierOrderLoadAmountSyncModel()] || [new LogisticsDemandStateSynchronizeModel()]
    }

    @Unroll
    def "confirm Syn Network Freight where requestModel=#requestModel"() {
        given:
        when(demandOrderMapper.getByIds(anyString())).thenReturn([new TDemandOrder(entrustStatus: 0, ifCancel: 0, source: 0, demandOrderCode: "demandOrderCode", entrustType: 0, ifEmpty: 0)])
        when(demandOrderMapper.getSyncTMSDemandOrderModelById(anyString())).thenReturn([new SyncTMSDemandOrderModel()])
        when(demandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)

        expect:
        demandOrderBiz.confirmSynNetworkFreight(requestModel)
        assert expectedResult == false

        where:
        requestModel                        || expectedResult
        new DemandOrderIdListRequestModel() || true
    }

    @Unroll
    def "withdraw Order where model=#model"() {
        given:
        when(demandOrderMapper.getInvalidTopByDemandOrderCode(anyString())).thenReturn(new TDemandOrder(goodsAmount: 0 as BigDecimal, notArrangedAmount: 0 as BigDecimal, entrustType: 0))
        when(demandOrderAddressMapper.getInvalidByDemandOrderId(anyLong())).thenReturn(new TDemandOrderAddress(demandOrderId: 1l))
        when(demandOrderGoodsMapper.getInvalidByDemandOrderId(anyLong())).thenReturn([new TDemandOrderGoods(demandOrderId: 1l, goodsAmount: 0 as BigDecimal, notArrangedAmount: 0 as BigDecimal)])
        when(demandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(demandOrderOperateLogsMapper.getInvalidByDemandOrderId(anyLong())).thenReturn([new TDemandOrderOperateLogs(demandOrderId: 1l, operationType: 0, operationContent: "operationContent", remark: "remark", operatorName: "operatorName", operateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 12).getTime())])
        when(tDemandOrderOrderRelMapper.batchInsertDemandOrderOrderRelSelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.getInvalidByDemandOrderId(anyLong())).thenReturn([new TDemandOrderOrderRel(demandOrderId: 1l, orderId: 1l, orderCode: "orderCode", totalAmount: 0 as BigDecimal, relType: 0, remark: "remark")])
        when(tDemandOrderGoodsRelMapper.batchInsert(any())).thenReturn(0)
        when(tDemandOrderGoodsRelMapper.getInvalidByDemandOrderId(anyLong())).thenReturn([new TDemandOrderGoodsRel(demandOrderGoodsId: 1l)])

        expect:
        demandOrderBiz.withdrawOrder(model)
        assert expectedResult == false

        where:
        model                                 || expectedResult
        new SyncLogisticsWithdrawOrderModel() || true
    }

    @Unroll
    def "cancel Demand Order Verify From Le Yi where requestModel=#requestModel"() {
        given:
        when(demandOrderMapper.getByCode(anyString())).thenReturn(new TDemandOrder(arrangedAmount: 0 as BigDecimal))

        expect:
        demandOrderBiz.cancelDemandOrderVerifyFromLeYi(requestModel)
        assert expectedResult == false

        where:
        requestModel                                      || expectedResult
        new CancelDemandOrderVerifyFromLeYiRequestModel() || true
    }

    @Unroll
    def "cancel Demand Order From Le Yi where requestModel=#requestModel"() {
        given:
        when(demandOrderMapper.getByCode(anyString())).thenReturn(new TDemandOrder(ifCancel: 0, cancelType: 0, cancelTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 12).getTime(), cancelReason: "cancelReason", notArrangedAmount: 0 as BigDecimal, backAmount: 0 as BigDecimal))
        when(demandOrderMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderMapper.getDemandOrderInfoByIds(anyString())).thenReturn([new GetDemandOrderInfoByIdsModel()])
        when(demandOrderGoodsMapper.getDemandOrderGoodsByDemandOrderIds(anyString())).thenReturn([new TDemandOrderGoods(notArrangedAmount: 0 as BigDecimal, backAmount: 0 as BigDecimal)])
        when(demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(demandOrderEventsMapper.batchInsertSelective(any())).thenReturn(0)
        when(demandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(any())).thenReturn(0)
        when(tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(anyString())).thenReturn([new TDemandOrderOrderRel(demandOrderId: 1l, orderId: 1l, totalAmount: 0 as BigDecimal, arrangedAmount: 0 as BigDecimal, backAmount: 0 as BigDecimal)])
        when(tDemandOrderObjectionMapper.batchInsert(any())).thenReturn(0)
        when(tDemandOrderObjectionMapper.batchUpdate(any())).thenReturn(0)
        when(tDemandOrderObjectionMapper.getByDemandOrderIds(anyString())).thenReturn([new TDemandOrderObjection(demandOrderId: 1l, customerName: "customerName", objectionType: 0, objectionReason: "objectionReason", reportContactName: "reportContactName", reportTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 12).getTime())])

        expect:
        demandOrderBiz.cancelDemandOrderFromLeYi(requestModel)
        assert expectedResult == false

        where:
        requestModel                                || expectedResult
        new CancelDemandOrderFromLeYiRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme