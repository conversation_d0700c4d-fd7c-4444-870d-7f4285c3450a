package com.logistics.tms.biz.gpstrack

import com.logistics.tms.api.feign.gpstrack.model.*
import com.logistics.tms.mapper.TVehicleGpsMapper
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class GpsTrackBizTest extends Specification {
    @Mock
    TVehicleGpsMapper tVehicleGpsMapper
    @Mock
    GpsTrackAsyncBiz gpsTrackAsyncBiz
    @Mock
    Logger log
    @InjectMocks
    GpsTrackBiz gpsTrackBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "get Vehicle Track Info List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleGpsMapper.getVehicleInfoByCondition(any())).thenReturn([new VehicleTrackInfoItem()])
        when(tVehicleGpsMapper.getVehicleGpsStatistics()).thenReturn(new AllVehicleTrackInfoResponseModel())

        expect:
        gpsTrackBiz.getVehicleTrackInfoList(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new AllVehicleTrackInfoRequestModel() || new AllVehicleTrackInfoResponseModel()
    }

    @Unroll
    def "check Truck Exist where searchVehicleNo=#searchVehicleNo"() {
        expect:
        gpsTrackBiz.checkTruckExist(searchVehicleNo)
        assert expectedResult == false

        where:
        searchVehicleNo   || expectedResult
        "searchVehicleNo" || true
    }

    @Unroll
    def "get Destination By Vehicle No where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleGpsMapper.getCarrierOrdersByVehicleNo(anyString())).thenReturn([new SearchCarrierOrderDestinationByVehicleNoResponseModel()])
        when(gpsTrackAsyncBiz.getVehicleLatestPositionAndUpdateLocal(anyString())).thenReturn(new VehicleLatestPositionModel())

        expect:
        gpsTrackBiz.getDestinationByVehicleNo(requestModel) == expectedResult

        where:
        requestModel                                               || expectedResult
        new SearchCarrierOrderDestinationByVehicleNoRequestModel() || new SearchCarrierOrderDestinationByVehicleNoResponseModel()
    }

    @Unroll
    def "get Vehicle Track History where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleGpsMapper.getCarrierOrdersByVehicleNo(anyString())).thenReturn([new SearchCarrierOrderDestinationByVehicleNoResponseModel()])
        when(gpsTrackAsyncBiz.getVehicleLatestPositionAndUpdateLocal(anyString())).thenReturn(new VehicleLatestPositionModel())

        expect:
        gpsTrackBiz.getVehicleTrackHistory(requestModel) == expectedResult

        where:
        requestModel                   || expectedResult
        new OpGpHisTrackRequestModel() || new OpGpHisTrackResponseModel()
    }

    @Unroll
    def "refresh Vehicle Location"() {
        expect:
        gpsTrackBiz.refreshVehicleLocation()
        assert expectedResult == false

        where:
        expectedResult << true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme