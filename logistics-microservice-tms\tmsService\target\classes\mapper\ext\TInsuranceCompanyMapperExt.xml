<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TInsuranceCompanyMapper" >
  <select id="searchInsuranceCompanyList" resultType="com.logistics.tms.api.feign.insurancecompany.model.InsuranceCompanyListResponseModel">
    select
      tqic.id as insuranceCompanyId,
      tqic.company_name as companyName,
      tqic.remark as remark,
      tqic.add_user_name as addUserName,
      tqic.enabled as enabled,
      tqic.last_modified_by as lastModifiedBy,
      tqic.last_modified_time as lastModifiedTime
    from t_insurance_company tqic
    where tqic.valid = 1
    order by tqic.last_modified_time desc
  </select>

  <select id="findInsuranceCompanyByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_insurance_company
    where valid = 1
    and company_name = #{insuranceCompanyName,jdbcType = VARCHAR}
  </select>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TInsuranceCompany">
    <foreach collection="list" item="item" index="index" separator=";">
      insert into t_insurance_company
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          id,
        </if>
        <if test="item.companyName != null">
          company_name,
        </if>
        <if test="item.remark != null">
          remark,
        </if>
        <if test="item.addUserId != null">
          add_user_id,
        </if>
        <if test="item.addUserName != null">
          add_user_name,
        </if>
        <if test="item.source != null">
          source,
        </if>
        <if test="item.enabled != null">
          enabled,
        </if>
        <if test="item.createdBy != null">
          created_by,
        </if>
        <if test="item.createdTime != null">
          created_time,
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time,
        </if>
        <if test="item.valid != null">
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.companyName != null">
          #{item.companyName,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null">
          #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.addUserId != null">
          #{item.addUserId,jdbcType=BIGINT},
        </if>
        <if test="item.addUserName != null">
          #{item.addUserName,jdbcType=VARCHAR},
        </if>
        <if test="item.source != null">
          #{item.source,jdbcType=INTEGER},
        </if>
        <if test="item.enabled != null">
          #{item.enabled,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <select id="queryInsuranceCompanyByName" resultType="com.logistics.tms.api.feign.insurancecompany.model.FuzzyQueryInsuranceCompanyListResponseModel">
    select
         tqic.id as insuranceCompanyId,
         tqic.company_name as companyName
    from t_insurance_company tqic
    where tqic.valid = 1 and tqic.enabled = 1
    <if test="params.companyName!=null and params.companyName!=''">
      and instr(tqic.company_name,#{params.companyName,jdbcType=VARCHAR}) > 0
    </if>
    order by tqic.last_modified_time desc
  </select>

  <update id="batchUpdate" parameterType="com.logistics.tms.entity.TInsuranceCompany">
    <foreach collection="list" item="item" separator=";">
      update t_insurance_company
      <set>
        <if test="item.companyName != null">
          company_name = #{item.companyName,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null">
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.addUserId != null">
          add_user_id = #{item.addUserId,jdbcType=BIGINT},
        </if>
        <if test="item.addUserName != null">
          add_user_name = #{item.addUserName,jdbcType=VARCHAR},
        </if>
        <if test="item.source != null">
          source = #{item.source,jdbcType=INTEGER},
        </if>
        <if test="item.enabled != null">
          enabled = #{item.enabled,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>