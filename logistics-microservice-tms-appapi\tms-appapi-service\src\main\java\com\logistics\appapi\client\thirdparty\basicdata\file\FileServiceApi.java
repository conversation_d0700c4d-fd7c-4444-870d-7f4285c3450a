package com.logistics.appapi.client.thirdparty.basicdata.file;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.thirdparty.basicdata.file.hystrix.FileServiceApiHystrix;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.BatchGetOSSFileUrlRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.GetFileByFilePathRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.GetOSSUrlRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.PdfWriteToImgRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/1/22 15:11
 */
@FeignClient(name = FeignClientName.BASIC_DATA_SERVICES,
        fallback = FileServiceApiHystrix.class)
public interface FileServiceApi {

    @ApiOperation("上传图片到oss并识别文字信息")
    @PostMapping(
            name = "OCR",
            value = {"/file/api/uploadOSSFileForAI"},
            consumes = {"multipart/form-data"},
            produces = {"application/json;charset=UTF-8"}
    )
    Result<FileUploadForAIResponseModel> uploadOSSFileForAI(@RequestPart("file") MultipartFile file, @RequestParam("picType") String picType, @RequestParam(value = "idCardSide",required = false) String idCardSide);

    @ApiOperation(value = "上传文件到oss")
    @PostMapping(value = "/file/api/uploadMultiPartFileOSS", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    Result<UploadFileOSSResponseModel> uploadMultiPartFileOSS(@RequestPart("file") MultipartFile file);

    @ApiOperation(value = "pdf转图片并落库OSS")
    @PostMapping(value = "/file/api/pdfWriteToImgOSS")
    Result<PdfWriteToImgResponseModel> pdfWriteToImgOSS(@RequestBody PdfWriteToImgRequestModel requestModel);

    @ApiOperation(value = "获取oss图片预览url")
    @PostMapping(value = "/file/api/getOSSFileUrl")
    Result<GetOSSUrlResponseModel> getOSSFileUrl(@RequestBody GetOSSUrlRequestModel requestModel);

    @ApiOperation(value = "批量获取oss图片预览url")
    @PostMapping(value = "/file/api/batchGetOSSFileUrl")
    Result<List<GetOSSUrlResponseModel>> batchGetOSSFileUrl(@RequestBody BatchGetOSSFileUrlRequestModel requestModel);

    @ApiOperation(value = "获取oss File流")
    @PostMapping(value = "/file/api/getFileByteOSS")
    Result<GetFileByteOSSResponseModel> getFileByteOSS(@RequestBody GetFileByFilePathRequestModel requestModel);

}
