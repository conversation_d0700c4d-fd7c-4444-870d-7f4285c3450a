package com.logistics.management.webapi.client.thirdparty.basicdata.ocr.response;

import lombok.Data;

@Data
public class OcrMultipleInvoiceRes {

    /**
     * vat_invoice          增值税发票
     * taxi_receipt         出租车票
     * train_ticket         火车票
     * quota_invoice        定额发票
     * invoice              机打发票
     * toll_invoice         过路过桥费发票
     */
    private String type;

    /** 发票名称 */
    private String InvoiceTypeOrg;
    /** 增值税发票的细分类型 */
    private String InvoiceType;
    /** 发票代码 */
    private String InvoiceCode;
    /** 发票号码 */
    private String InvoiceNum;
    /** 合计金额 */
    private String TotalAmount;
    /** 合计税额 */
    private String TotalTax;
    /** 价税合计(小写) */
    private String AmountInFiguers;
    /** 发票消费类型 */
    // private String ServiceType;
    /** 总金额 */
    private String TotalFare;
    /** 车票号 */
    private String ticket_num;
    /** 车票金额 */
    private String ticket_rates;
    /** 发票名称 */
    // private String invoice_type;
    /** 发票代码 */
    private String invoice_code;
    /** 发票号码 */
    private String invoice_number;
    /** 金额小写 */
    private String invoice_rate_lowercase;
    /** 金额 */
    private String Fare;


}