package com.logistics.tms.api.feign.insuarance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/6/18 16:28
 */
@Data
public class VerifyInsuranceUniquenessRequestModel {
    @ApiModelProperty("险种：1 商业险，2 交强险，3 个人意外险，4 货物险，5 危货承运人险")
    private Integer insuranceType;
    @ApiModelProperty("车辆id")
    private Long vehicleId;
    @ApiModelProperty("司机Id")
    private Long driverId;
    @ApiModelProperty("个人意外险id")
    private Long personalAccidentInsuranceId;
    @ApiModelProperty("保险生效时间（年月）")
    private String startTime;
    @ApiModelProperty("保险截止时间（年月）")
    private String endTime;
    @ApiModelProperty("关联扣费保单id")
    private Long relatedPersonalAccidentInsuranceId;
}
