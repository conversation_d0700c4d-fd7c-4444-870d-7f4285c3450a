package com.logistics.tms.biz.dateremind

import com.logistics.tms.api.feign.dateremind.model.DateRemindDetailRequestModel
import com.logistics.tms.api.feign.dateremind.model.DateRemindDetailResponseModel
import com.logistics.tms.api.feign.dateremind.model.DateRemindListRequestModel
import com.logistics.tms.api.feign.dateremind.model.DateRemindListResponseModel
import com.logistics.tms.api.feign.dateremind.model.SaveOrModifyDateRemindRequestModel
import com.logistics.tms.api.feign.dateremind.model.UnifiedDateRemindRequestModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TDateRemind
import com.logistics.tms.mapper.TDateRemindMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class DateRemindBizTest extends Specification {
    @Mock
    TDateRemindMapper tDateRemindMapper
    @Mock
    CommonBiz commonBiz
    @InjectMocks
    DateRemindBiz dateRemindBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Date Remind List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tDateRemindMapper.searchDateRemindList(any())).thenReturn([new DateRemindListResponseModel()])

        expect:
        dateRemindBiz.searchDateRemindList(requestModel) == expectedResult

        where:
        requestModel                     || expectedResult
        new DateRemindListRequestModel() || null
    }

    @Unroll
    def "get Date Remind Detail where requestModel=#requestModel then expect: #expectedResult"() {
        expect:
        dateRemindBiz.getDateRemindDetail(requestModel) == expectedResult

        where:
        requestModel                       || expectedResult
        new DateRemindDetailRequestModel() || new DateRemindDetailResponseModel()
    }

    @Unroll
    def "save Or Modify Date Remind where requestModel=#requestModel"() {
        given:
        when(tDateRemindMapper.getDateRemindByName(anyString())).thenReturn(new TDateRemind(dateName: "dateName", ifRemind: 0, remindDays: 0, remark: "remark", addUserId: 1l, addUserName: "addUserName"))
        when(tDateRemindMapper.updateByPrimaryKeySelectiveExt(any())).thenReturn(0)

        expect:
        dateRemindBiz.saveOrModifyDateRemind(requestModel)
        assert expectedResult == false

        where:
        requestModel                             || expectedResult
        new SaveOrModifyDateRemindRequestModel() || true
    }

    @Unroll
    def "unified Date Remind where requestModel=#requestModel"() {
        given:
        when(tDateRemindMapper.getDateRemindByIds(anyString())).thenReturn([new TDateRemind(ifRemind: 0, remindDays: 0, remark: "remark")])

        expect:
        dateRemindBiz.unifiedDateRemind(requestModel)
        assert expectedResult == false

        where:
        requestModel                        || expectedResult
        new UnifiedDateRemindRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme