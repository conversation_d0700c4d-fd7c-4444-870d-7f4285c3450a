package com.logistics.management.webapi.api.impl.vehicleoilcard.mapping;

import com.logistics.management.webapi.api.feign.vehicleoilcard.dto.GetVehicleOilCardRecordResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.vehicleoilcard.model.GetVehicleOilCardRecordResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2022/8/4 17:24
 */
public class GetVehicleOilCardRecordMapping extends MapperMapping<GetVehicleOilCardRecordResponseModel, GetVehicleOilCardRecordResponseDto> {
    @Override
    public void configure() {
        GetVehicleOilCardRecordResponseModel source = getSource();
        GetVehicleOilCardRecordResponseDto destination = getDestination();

        if (CommonConstant.INTEGER_ONE.equals(source.getOperateType())){
            destination.setOperateTypeLabel("新增");
        }else if (CommonConstant.INTEGER_TWO.equals(source.getOperateType())){
            destination.setOperateTypeLabel("解绑");
        }else if (CommonConstant.INTEGER_THREE.equals(source.getOperateType())){
            destination.setOperateTypeLabel("绑定");
        }
    }
}
