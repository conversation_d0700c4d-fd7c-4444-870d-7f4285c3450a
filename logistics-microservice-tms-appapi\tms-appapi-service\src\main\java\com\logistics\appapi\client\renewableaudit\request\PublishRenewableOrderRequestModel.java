package com.logistics.appapi.client.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/17
 */
@Data
public class PublishRenewableOrderRequestModel {

	//发货地址
	@ApiModelProperty("发货地址code（新生客户地址code）")
	private String loadAddressCode;

	@ApiModelProperty(value = "发货省id")
	private Long loadProvinceId;

	@ApiModelProperty(value = "发货省")
	private String loadProvinceName;

	@ApiModelProperty(value = "发货市id")
	private Long loadCityId;

	@ApiModelProperty(value = "发货市")
	private String loadCityName;

	@ApiModelProperty(value = "发货区ID")
	private Long loadAreaId;

	@ApiModelProperty(value = "发货区")
	private String loadAreaName;

	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress;

	@ApiModelProperty("发货仓库")
	private String loadWarehouse;

	@ApiModelProperty(value = "发货人")
	private String consignorName;

	@ApiModelProperty(value = "发货人手机号")
	private String consignorMobile;

	@ApiModelProperty(value = "发货人所属客户账号（手机号）")
	private String customerAccount;


	//收货地址
	@ApiModelProperty("收货地址code（云仓仓库地址code）")
	private String unloadAddressCode;

	@ApiModelProperty(value = "收货省id")
	private Long unloadProvinceId;

	@ApiModelProperty(value = "收货省")
	private String unloadProvinceName;

	@ApiModelProperty(value = "收货市id")
	private Long unloadCityId;

	@ApiModelProperty(value = "收货市")
	private String unloadCityName;

	@ApiModelProperty(value = "收货区id")
	private Long unloadAreaId;

	@ApiModelProperty(value = "收货区")
	private String unloadAreaName;

	@ApiModelProperty("收货地址详细")
	private String unloadDetailAddress;

	@ApiModelProperty(value = "收货仓库")
	private String unloadWarehouse;

	@ApiModelProperty(value = "收货人")
	private String receiverName;

	@ApiModelProperty(value = "收货人手机号")
	private String receiverMobile;


	//确认单据
	@ApiModelProperty(value = "现场图片")
	private List<String> scenePictureList;

	@ApiModelProperty(value = "确认单据")
	private List<String> confirmPictureList;


	//货物信息
	@Valid
	@ApiModelProperty(value = "新生订单货物信息")
	private List<PublishRenewableOrderGoodsRequestModel> renewableOrderGoods;
}
