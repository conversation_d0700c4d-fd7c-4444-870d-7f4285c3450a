package com.logistics.appapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CodePickUpDetailResponseModel {


    @ApiModelProperty(value = "运单Id")
    private Long carrierOrderId;

    @ApiModelProperty(value = "预计提货数量")
    private Integer loadAmountExpect;

    @ApiModelProperty(value = "实际扫码数")
    private Integer scanAmountActual;

    @ApiModelProperty(value = "编码有误数")
    private Integer codeErrorCount;

    @ApiModelProperty(value = "实际提货数")
    private Integer loadAmountActual;

}
