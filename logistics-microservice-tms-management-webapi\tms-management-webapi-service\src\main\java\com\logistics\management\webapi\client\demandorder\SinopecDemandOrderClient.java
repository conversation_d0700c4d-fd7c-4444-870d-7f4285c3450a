package com.logistics.management.webapi.client.demandorder;

import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.demandorder.hystrix.SinopecDemandOrderClientHystrix;
import com.logistics.management.webapi.client.demandorder.request.*;
import com.logistics.management.webapi.client.demandorder.response.BatchPublishSinopecResponseModel;
import com.logistics.management.webapi.client.demandorder.response.PublishSinopecResponseModel;
import com.logistics.management.webapi.client.demandorder.response.SinopecReportAbnormalDetailResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2024/3/27 16:08
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = SinopecDemandOrderClientHystrix.class)
public interface SinopecDemandOrderClient {

    /**
     * 取消中石化需求单
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "取消中石化需求单")
    @PostMapping(value = "/service/demandOrder/cancelSinopecDemandOrder")
    Result<Boolean> cancelSinopecDemandOrder(@RequestBody SinopecDemandOrderCancelRequestModel requestModel);

    /**
     * 获取中石化需求发布详情
     *
     * @param requestModel 单个需求单id
     * @return 需求单发布详情
     */
    @ApiOperation(value = "获取中石化需求发布详情")
    @PostMapping(value = "/service/demandOrder/publishSinopecDetail")
    Result<PublishSinopecResponseModel> publishSinopecDetail(@RequestBody @Valid PublishSinopecDetailRequestModel requestModel);

    /**
     * 批量获取中石化需求单发布详情
     *
     * @param requestModel 多个需求单id
     * @return 需求单发布详情
     */
    @ApiOperation(value = "批量获取中石化需求单发布详情")
    @PostMapping(value = "/service/demandOrder/batchPublishSinopecDetail")
    Result<BatchPublishSinopecResponseModel> batchPublishSinopecDetail(@RequestBody @Valid BatchPublishSinopecDetailRequestModel requestModel);

    /**
     * 批量发布中石化需求单
     *
     * @param requestModel 要发布的需求单信息
     * @return 发布结果
     */
    @ApiOperation(value = "批量发布中石化需求单")
    @PostMapping(value = "/service/demandOrder/batchPublishSinopecDemandOrder")
    Result<Boolean> batchPublishSinopecDemandOrder(@RequestBody @Valid BatchPublishSinopecDemandRequestModel requestModel);

    /**
     * 单个发布中石化需求单
     *
     * @param requestModel 要发布的需求单信息
     * @return 发布结果
     */
    @ApiOperation(value = "单个发布中石化需求单")
    @PostMapping(value = "/service/demandOrder/publishSinopecDemandOrder")
    Result<Boolean> publishSinopecDemandOrder(@RequestBody @Valid PublishSinopecDemandRequestModel requestModel);


    /**
     * 中石化需求单上报异常详情查询
     *
     * @param requestModel 需求单id
     * @return 需求单详情列表
     */
    @ApiOperation(value = "中石化需求单上报异常详情查询")
    @PostMapping(value = "/service/demandOrder/sinopecReportAbnormalDetail")
    Result<SinopecReportAbnormalDetailResponseModel> sinopecReportAbnormalDetail(@RequestBody @Valid SinopecReportAbnormalDetailRequestModel requestModel);



    /**
     * 中石化需求单异常上报
     *
     * @param requestModel 异常上报信息
     * @return 操作结果
     */
    @ApiOperation(value = "中石化需求单异常上报")
    @PostMapping(value = "/service/demandOrder/saveSinopecReportAbnormal")
    Result<Boolean> saveSinopecReportAbnormal(@RequestBody @Valid SaveSinopecReportAbnormalRequestModel requestModel);

}
