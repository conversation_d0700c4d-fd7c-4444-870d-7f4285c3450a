package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DemandOrderCancelRequestModel {
    //需求单ID，逗号分隔
    private String demandId;
    private String cancelReason;
    @ApiModelProperty("取消类型：1 我司原因，2 客户原因，3 不可抗力，4 物流原因，5 平台问题，6 其他原因")
    private Integer cancelType;

    @ApiModelProperty("是否云盘系统操作取消计划：云盘操作取消，由云盘给云仓发取消mq")
    private boolean leyiOperate=false;
}
