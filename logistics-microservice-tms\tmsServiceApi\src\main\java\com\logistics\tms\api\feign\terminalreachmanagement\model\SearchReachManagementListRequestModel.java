package com.logistics.tms.api.feign.terminalreachmanagement.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SearchReachManagementListRequestModel extends AbstractPageForm<SearchReachManagementListRequestModel> {

    @ApiModelProperty(value = "勾选导出id")
    private List<Long> reachManagementIds;

    @ApiModelProperty(value = "运单号")
    private String carrierOrderCode;

    @ApiModelProperty(value = "司机")
    private String reachDriver;

    @ApiModelProperty(value = "触达地址")
    private String reachAddress;

    @ApiModelProperty(value = "联系人校验 0:无误 1:有误")
    private Integer checkReachContact;

    @ApiModelProperty(value = "联系人")
    private String contact;

    @ApiModelProperty(value = "触达起始日期")
    private String reachTimeStart;

    @ApiModelProperty(value = "触达结束日期")
    private String reachTimeEnd;

    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "操作起始日期")
    private String lastModifiedTimeStart;

    @ApiModelProperty(value = "操作结束日期")
    private String lastModifiedTimeEnd;

}
