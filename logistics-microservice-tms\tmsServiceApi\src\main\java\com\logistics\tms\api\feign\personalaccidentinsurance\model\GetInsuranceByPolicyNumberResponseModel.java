package com.logistics.tms.api.feign.personalaccidentinsurance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/5/30 17:59
 */
@Data
public class GetInsuranceByPolicyNumberResponseModel {
    @ApiModelProperty("个人意外险表id")
    private Long personalAccidentInsuranceId;
    @ApiModelProperty("保险公司id")
    private Long insuranceCompanyId;
    @ApiModelProperty("保险公司名称")
    private String insuranceCompany;
    @ApiModelProperty("保单号")
    private String policyNumber;
    @ApiModelProperty("批单号")
    private List<GetInsuranceByBatchNumberResponseModel> batchNumberList;
    @ApiModelProperty("保单总额")
    private BigDecimal grossPremium;
    @ApiModelProperty("保单人数")
    private Integer policyPersonCount;
    @ApiModelProperty("保险生效时间")
    private Date startTime;
    @ApiModelProperty("保险截止时间")
    private Date endTime;
    @ApiModelProperty("个人意外险保单单据")
    private List<PersonalAccidentInsuranceTicketsResponseModel> ticketsList;
}
