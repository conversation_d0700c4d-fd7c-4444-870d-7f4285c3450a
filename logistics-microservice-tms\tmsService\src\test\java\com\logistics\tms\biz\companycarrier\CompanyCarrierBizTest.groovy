package com.logistics.tms.biz.companycarrier

import com.logistics.tms.controller.companycarrier.request.CompanyCarrierDetailRequestModel
import com.logistics.tms.controller.companycarrier.response.CompanyCarrierDetailResponseModel
import com.logistics.tms.controller.companycarrier.request.FuzzySearchCompanyCarrierRequestModel
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel
import com.logistics.tms.controller.companycarrier.request.SaveOrModifyCompanyCarrierRequestModel
import com.logistics.tms.controller.companycarrier.request.SearchCompanyCarrierListRequestModel
import com.logistics.tms.controller.companycarrier.response.SearchCompanyCarrierListResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TCompany
import com.logistics.tms.mapper.TCompanyCarrierMapper
import com.logistics.tms.mapper.TCompanyMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class CompanyCarrierBizTest extends Specification {
    @Mock
    TCompanyCarrierMapper companyCarrierMapper
    @Mock
    TCompanyMapper companyMapper
    @Mock
    CommonBiz commonBiz
    @InjectMocks
    CompanyCarrierBiz companyCarrierBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(companyCarrierMapper.getCompanyCarrierList(any())).thenReturn([new SearchCompanyCarrierListResponseModel()])

        expect:
        companyCarrierBiz.searchList(requestModel) == expectedResult

        where:
        requestModel                               || expectedResult
        new SearchCompanyCarrierListRequestModel() || null
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(companyCarrierMapper.getCompanyCarrierDetailById(anyLong())).thenReturn(new CompanyCarrierDetailResponseModel())

        expect:
        companyCarrierBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                           || expectedResult
        new CompanyCarrierDetailRequestModel() || new CompanyCarrierDetailResponseModel()
    }

    @Unroll
    def "save Or Update where requestModel=#requestModel"() {
        given:
        when(companyMapper.getByName(anyString())).thenReturn(new TCompany(companyName: "companyName", tradingCertificateImage: "tradingCertificateImage", tradingCertificateValidityTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime(), tradingCertificateIsForever: 0, tradingCertificateIsAmend: 0))
        when(companyMapper.updateByPrimaryKeySelectiveForTime(any())).thenReturn(0)
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")

        expect:
        companyCarrierBiz.saveOrUpdate(requestModel)
        assert expectedResult == false

        where:
        requestModel                                 || expectedResult
        new SaveOrModifyCompanyCarrierRequestModel() || true
    }

    @Unroll
    def "fuzzy Query where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(companyCarrierMapper.fuzzyQueryCompanyCarrierInfo(any())).thenReturn([new FuzzySearchCompanyCarrierResponseModel()])

        expect:
        companyCarrierBiz.fuzzyQuery(requestModel) == expectedResult

        where:
        requestModel                                || expectedResult
        new FuzzySearchCompanyCarrierRequestModel() || [new FuzzySearchCompanyCarrierResponseModel()]
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme