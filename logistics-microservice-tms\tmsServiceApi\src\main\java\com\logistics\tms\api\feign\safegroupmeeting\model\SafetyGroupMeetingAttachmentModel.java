package com.logistics.tms.api.feign.safegroupmeeting.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class SafetyGroupMeetingAttachmentModel {

    @ApiModelProperty(value = "图片id")
    private Long safetyGroupMeetingAttachmentId;

    @ApiModelProperty(value = "类型：1 现场图片，2 签到图片")
    private Integer type;

    @ApiModelProperty(value = "会议图片路径")
    private String meetingImagePath;

}
