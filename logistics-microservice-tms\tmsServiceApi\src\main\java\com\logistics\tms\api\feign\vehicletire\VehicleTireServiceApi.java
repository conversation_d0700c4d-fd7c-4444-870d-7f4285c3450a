package com.logistics.tms.api.feign.vehicletire;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.vehicletire.hystrix.VehicleTireServiceApiHystrix;
import com.logistics.tms.api.feign.vehicletire.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Api(value = "API-VehicleTireServiceApiImpl-轮胎管理")
@FeignClient(name = "logistics-tms-services",fallback = VehicleTireServiceApiHystrix.class)
public interface VehicleTireServiceApi {

    @ApiOperation(value = "获取轮胎管理列表")
    @PostMapping(value = "/service/vehicleTire/searchVehicleTireList")
    Result<PageInfo<VehicleTireListResponseModel>> searchVehicleTireList(@RequestBody VehicleTireListRequestModel requestModel);

    @ApiOperation(value = "查看详情")
    @PostMapping(value = "/service/vehicleTire/getDetail")
    Result<VehicleTireDetailResponseModel> getVehicleTireDetail(@RequestBody VehicleTireIdRequestModel requestModel);

    @ApiOperation(value = "新增/修改")
    @PostMapping(value = "/service/vehicleTire/addOrModifyVehicleTire")
    Result<Boolean> addOrModifyVehicleTire(@RequestBody AddOrModifyVehicleTireRequestModel requestModel);

    @ApiOperation(value = "删除")
    @PostMapping(value = "/service/vehicleTire/delete")
    Result<Boolean> deleteVehicleTire(@RequestBody VehicleTireIdRequestModel requestModel);

    @ApiOperation(value = "导出")
    @PostMapping(value = "/service/vehicleTire/export")
    Result<List<VehicleTireListResponseModel>> export(@RequestBody VehicleTireListRequestModel requestModel);

    @ApiOperation(value = "Excel导入轮胎管理信息")
    @PostMapping(value = "/service/vehicleTire/import")
    Result<ImportVehicleTireInfoResponseModel> importExcelInfoVehicleTireInfo(@RequestBody ImportVehicleTireInfoRequestModel requestModel);

    @ApiOperation(value = "导入轮胎管理凭证")
    @PostMapping(value = "/service/vehicleTire/importCertificateInfo")
    Result<Boolean> importVehicleTireCertificateInfo(@RequestBody ImportTireCertificateRequestModel requestModel);
}
