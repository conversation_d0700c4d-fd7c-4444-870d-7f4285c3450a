package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/8/22 11:01
 */
@AllArgsConstructor
@Getter
public enum ReservationTypeEnum {

    DEFAULT(-99999, ""),
    LOAD(1, "提货"),
    UNLOAD(2, "卸货"),
    ;

    private final Integer key;
    private final String value;


    public static ReservationTypeEnum getEnum(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

}
