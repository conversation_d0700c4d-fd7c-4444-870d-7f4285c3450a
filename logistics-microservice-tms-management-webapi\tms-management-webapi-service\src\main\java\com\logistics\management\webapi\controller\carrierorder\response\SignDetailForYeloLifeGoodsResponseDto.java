package com.logistics.management.webapi.controller.carrierorder.response;

import com.logistics.management.webapi.controller.carrierorder.request.LoadGoodsForYeloLifeRequestCodeDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-29 15:30
 */
@Data
public class SignDetailForYeloLifeGoodsResponseDto {

    @ApiModelProperty("货物id")
    private String goodsId = "";

    @ApiModelProperty("货物名称")
    private String goodsName;

    @ApiModelProperty("预提数量")
    private String expectAmount = "";

    @ApiModelProperty("提货数量")
    private String loadAmount = "";

    @ApiModelProperty("卸货数量")
    private String unloadAmount = "";

    @ApiModelProperty(value = "货物的编码集合 v2.44", required = true)
    private List<SignDetailForYeloLifeGoodsCodeResponseDto> codeDtoList;

}
