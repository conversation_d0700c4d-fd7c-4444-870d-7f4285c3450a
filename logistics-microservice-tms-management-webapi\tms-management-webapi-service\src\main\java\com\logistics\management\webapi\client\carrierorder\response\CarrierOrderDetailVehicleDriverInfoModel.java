package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CarrierOrderDetailVehicleDriverInfoModel {

    @ApiModelProperty("车辆运单关联ID")
    private String vehicleHistoryId;
    @ApiModelProperty("审核状态 -1 无需审核 0 待审核 1 已审核 2 已驳回")
    private Integer auditStatus;
    @ApiModelProperty("是否有效 1 有效 0 无效")
    private Integer ifInvalid;
    @ApiModelProperty("车牌号码")
    private String vehicleNo;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机手机号")
    private String driverMobile;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("驳回理由")
    private String rejectReason;
    private Date createdTime;



}
