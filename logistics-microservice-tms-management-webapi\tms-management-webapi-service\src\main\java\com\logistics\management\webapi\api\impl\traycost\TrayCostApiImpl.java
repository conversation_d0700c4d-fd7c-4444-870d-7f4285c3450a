package com.logistics.management.webapi.api.impl.traycost;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.traycost.TrayCostApi;
import com.logistics.management.webapi.api.feign.traycost.dto.*;
import com.logistics.management.webapi.api.impl.traycost.mapping.GetTrayCostDetailMapping;
import com.logistics.management.webapi.api.impl.traycost.mapping.SearchCostRecordsListMapping;
import com.logistics.management.webapi.api.impl.traycost.mapping.SearchTrayCostListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.traycost.TrayCostServiceApi;
import com.logistics.tms.api.feign.traycost.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/20 18:42
 */
@RestController
public class TrayCostApiImpl implements TrayCostApi {
    @Autowired
    private TrayCostServiceApi trayCostServiceApi;

    /**
     * 托盘费用列表
     * @return
     */
    @Override
    public Result<List<SearchTrayCostListResponseDto>> searchTrayCostList() {
        Result<List<SearchTrayCostListResponseModel>> result = trayCostServiceApi.searchTrayCostList();
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SearchTrayCostListResponseDto.class, new SearchTrayCostListMapping()));
    }

    /**
     * 托盘费用详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<TrayCostDetailResponseDto> getDetail(@RequestBody @Valid TrayCostIdRequestDto requestDto) {
        Result<TrayCostDetailResponseModel> result = trayCostServiceApi.getDetail(MapperUtils.mapper(requestDto, TrayCostIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),TrayCostDetailResponseDto.class, new GetTrayCostDetailMapping()));
    }

    /**
     * 修改费用
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result modifyPrice(@RequestBody @Valid ModifyPriceRequestDto requestDto) {
        return trayCostServiceApi.modifyPrice(MapperUtils.mapperNoDefault(requestDto, ModifyPriceRequestModel.class));
    }

    /**
     * 费用记录列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchCostRecordsListResponseDto>> searchCostRecordsList(@RequestBody @Valid SearchCostRecordsListRequestDto requestDto) {
        Result<PageInfo<SearchCostRecordsListResponseModel>> result = trayCostServiceApi.searchCostRecordsList(MapperUtils.mapper(requestDto, SearchCostRecordsListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchCostRecordsListResponseDto> list = MapperUtils.mapper(pageInfo.getList(),SearchCostRecordsListResponseDto.class, new SearchCostRecordsListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }
}
