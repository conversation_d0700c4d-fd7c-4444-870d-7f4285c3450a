package com.logistics.tms.mapper;

import com.logistics.tms.api.feign.extvehiclesettlement.model.ExtVehicleSettlementDetailResponseModel;
import com.logistics.tms.api.feign.extvehiclesettlement.model.SearchExtVehicleSettlementListRequestModel;
import com.logistics.tms.api.feign.extvehiclesettlement.model.SearchExtVehicleSettlementListResponseModel;
import com.logistics.tms.entity.TExtVehicleSettlement;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TExtVehicleSettlementMapper extends BaseMapper<TExtVehicleSettlement> {

    List<Long> searchExtVehicleSettlementIdList(@Param("params") SearchExtVehicleSettlementListRequestModel requestModel);

    List<SearchExtVehicleSettlementListResponseModel> searchExtVehicleSettlementList(@Param("ids") String ids);

    ExtVehicleSettlementDetailResponseModel extVehicleSettlementDetail(@Param("id") Long id);

    int batchInsert(@Param("list") List<TExtVehicleSettlement> list);

    List<TExtVehicleSettlement> getByCarrierOrderIds(@Param("carrierOrderIds") String carrierOrderIds);

    TExtVehicleSettlement getByCarrierOrderId(@Param("carrierOrderId") Long carrierOrderId);
}