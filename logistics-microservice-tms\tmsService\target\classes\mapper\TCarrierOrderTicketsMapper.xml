<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderTicketsMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierOrderTickets" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="carrier_order_id" property="carrierOrderId" jdbcType="BIGINT" />
    <result column="image_type" property="imageType" jdbcType="INTEGER" />
    <result column="image_name" property="imageName" jdbcType="VARCHAR" />
    <result column="image_path" property="imagePath" jdbcType="VARCHAR" />
    <result column="upload_user_name" property="uploadUserName" jdbcType="VARCHAR" />
    <result column="upload_time" property="uploadTime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, carrier_order_id, image_type, image_name, image_path, upload_user_name, upload_time, 
    created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_carrier_order_tickets
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_carrier_order_tickets
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierOrderTickets" >
    insert into t_carrier_order_tickets (id, carrier_order_id, image_type, 
      image_name, image_path, upload_user_name, 
      upload_time, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, #{imageType,jdbcType=INTEGER}, 
      #{imageName,jdbcType=VARCHAR}, #{imagePath,jdbcType=VARCHAR}, #{uploadUserName,jdbcType=VARCHAR}, 
      #{uploadTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderTickets" >
    insert into t_carrier_order_tickets
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id,
      </if>
      <if test="imageType != null" >
        image_type,
      </if>
      <if test="imageName != null" >
        image_name,
      </if>
      <if test="imagePath != null" >
        image_path,
      </if>
      <if test="uploadUserName != null" >
        upload_user_name,
      </if>
      <if test="uploadTime != null" >
        upload_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null" >
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="imageType != null" >
        #{imageType,jdbcType=INTEGER},
      </if>
      <if test="imageName != null" >
        #{imageName,jdbcType=VARCHAR},
      </if>
      <if test="imagePath != null" >
        #{imagePath,jdbcType=VARCHAR},
      </if>
      <if test="uploadUserName != null" >
        #{uploadUserName,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null" >
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierOrderTickets" >
    update t_carrier_order_tickets
    <set >
      <if test="carrierOrderId != null" >
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="imageType != null" >
        image_type = #{imageType,jdbcType=INTEGER},
      </if>
      <if test="imageName != null" >
        image_name = #{imageName,jdbcType=VARCHAR},
      </if>
      <if test="imagePath != null" >
        image_path = #{imagePath,jdbcType=VARCHAR},
      </if>
      <if test="uploadUserName != null" >
        upload_user_name = #{uploadUserName,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null" >
        upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierOrderTickets" >
    update t_carrier_order_tickets
    set carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      image_type = #{imageType,jdbcType=INTEGER},
      image_name = #{imageName,jdbcType=VARCHAR},
      image_path = #{imagePath,jdbcType=VARCHAR},
      upload_user_name = #{uploadUserName,jdbcType=VARCHAR},
      upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>