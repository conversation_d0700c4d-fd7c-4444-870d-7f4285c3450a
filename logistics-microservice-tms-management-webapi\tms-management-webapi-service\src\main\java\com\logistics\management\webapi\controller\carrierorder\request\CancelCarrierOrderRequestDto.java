package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class CancelCarrierOrderRequestDto {

    @ApiModelProperty("运单ID")
    @NotBlank(message = "运单id不能为空")
    private String carrierOrderId;

    @ApiModelProperty("取消原因")
    @Size(min = 1,max = 300,message = "取消原因不能为空，且不超过300字")
    private String cancelReason;
}
