package com.logistics.management.webapi.controller.freightconfig.mapping;

import com.logistics.management.webapi.client.freightconfig.response.address.CarrierFreightConfigAddressDetailResponseModel;
import com.logistics.management.webapi.controller.freightconfig.response.address.CarrierFreightConfigAddressDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Optional;

public class CarrierFreightConfigAddressDetailMapping extends MapperMapping<CarrierFreightConfigAddressDetailResponseModel, CarrierFreightConfigAddressDetailResponseDto> {
    @Override
    public void configure() {
        CarrierFreightConfigAddressDetailResponseModel source = getSource();
        CarrierFreightConfigAddressDetailResponseDto destination = getDestination();

        destination.setFromAddress(Optional.ofNullable(source.getFromProvinceName()).orElse("")
                + Optional.ofNullable(source.getFromCityName()).orElse("")
                + Optional.ofNullable(source.getFromAreaName()).orElse(""));
        destination.setToAddress(Optional.ofNullable(source.getToProvinceName()).orElse("")
                + Optional.ofNullable(source.getToCityName()).orElse("")
                + Optional.ofNullable(source.getToAreaName()).orElse(""));
    }
}
