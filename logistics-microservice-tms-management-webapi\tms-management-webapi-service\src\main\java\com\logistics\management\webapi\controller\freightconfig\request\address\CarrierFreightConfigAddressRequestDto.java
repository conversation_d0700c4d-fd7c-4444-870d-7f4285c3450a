package com.logistics.management.webapi.controller.freightconfig.request.address;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CarrierFreightConfigAddressRequestDto {

    @ApiModelProperty(value = "路线计价配置ID,可批量", required = true)
    @NotEmpty(message = "路线计价配置ID不允许为空")
    private List<String> freightConfigAddressIds;

    @ApiModelProperty("禁用/启用。1：启用，0：禁用")
    @NotNull(message = "操作类型不能为空")
    private String enabled;
}
