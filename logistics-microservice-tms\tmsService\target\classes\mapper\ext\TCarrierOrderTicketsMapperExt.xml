<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderTicketsMapper" >

    <select id="getTicketsByCarrierOrderId" resultType="com.logistics.tms.controller.carrierorder.response.GetTicketsResponseModel">
        select
        id               as imageId,
        image_path       as imagePath,
        image_name       as imageName,
        upload_user_name as uploadUserName,
        upload_time      as uploadTime,
        image_type       as imageType
        from t_carrier_order_tickets
        where valid = 1
        and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
        <if test="imageType != null">
        and image_type = #{imageType,jdbcType=INTEGER}
        </if>
        order by id
    </select>
    <insert id="batchInsertTickets">
        <foreach collection="list" close="" index="index" item="item" separator=";">
            insert into t_carrier_order_tickets
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.carrierOrderId != null" >
                    carrier_order_id,
                </if>
                <if test="item.imageType != null" >
                    image_type,
                </if>
                <if test="item.imageName != null" >
                    image_name,
                </if>
                <if test="item.imagePath != null" >
                    image_path,
                </if>
                <if test="item.uploadUserName != null" >
                    upload_user_name,
                </if>
                <if test="item.uploadTime != null" >
                    upload_time,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderId != null" >
                    #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.imageType != null" >
                    #{item.imageType,jdbcType=INTEGER},
                </if>
                <if test="item.imageName != null" >
                    #{item.imageName,jdbcType=VARCHAR},
                </if>
                <if test="item.imagePath != null" >
                    #{item.imagePath,jdbcType=VARCHAR},
                </if>
                <if test="item.uploadUserName != null" >
                    #{item.uploadUserName,jdbcType=VARCHAR},
                </if>
                <if test="item.uploadTime != null" >
                    #{item.uploadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TCarrierOrderTickets" >
        <foreach collection="list" item="item" separator=";">
            update t_carrier_order_tickets
            <set >
                <if test="item.carrierOrderId != null" >
                    carrier_order_id = #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.imageType != null" >
                    image_type = #{item.imageType,jdbcType=INTEGER},
                </if>
                <if test="item.imageName != null" >
                    image_name = #{item.imageName,jdbcType=VARCHAR},
                </if>
                <if test="item.imagePath != null" >
                    image_path = #{item.imagePath,jdbcType=VARCHAR},
                </if>
                <if test="item.uploadUserName != null" >
                    upload_user_name = #{item.uploadUserName,jdbcType=VARCHAR},
                </if>
                <if test="item.uploadTime != null" >
                    upload_time = #{item.uploadTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getTicketsByCarrierOrderIdAndType" resultType="com.logistics.tms.controller.carrierorder.response.GetTicketsResponseModel">
        select
        id               as imageId,
        image_path       as imagePath,
        image_name       as imageName,
        upload_user_name as uploadUserName,
        upload_time      as uploadTime,
        image_type       as imageType
        from t_carrier_order_tickets
        where valid = 1
        and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
        and image_type = #{type,jdbcType=INTEGER}
        order by id
    </select>

    <!-- app -->
    <resultMap id="getTicketsByCarrierOrderIdByApp_map" type="com.logistics.tms.controller.carrierorderapplet.response.CarrierOrderBillResponseModel">
        <result column="image_type" property="ticketType" jdbcType="INTEGER"/>
        <collection property="ticketPath" ofType="string" javaType="list">
            <result column="image_path"/>
        </collection>
    </resultMap>
    <select id="getTicketsByCarrierOrderIdByApp" resultMap="getTicketsByCarrierOrderIdByApp_map">
        select
        image_type,
        image_path
        from t_carrier_order_tickets
        where valid = 1 and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
        and image_type != 7
    </select>
    <select id="getTicketsAmountByCarrierOrderIds" resultType="com.logistics.tms.biz.carrierorder.model.GetTicketsAmountByCarrierOrderIdsModel">
        select
        carrier_order_id as carrierOrderId,
        count(1) as amount
        from t_carrier_order_tickets
        where valid=1
        and image_type=3
        and carrier_order_id in (${carrierOrderIds})
        group by carrier_order_id
    </select>

    <select id="getTicketsByCarrierOrderIds" resultType="com.logistics.tms.controller.carrierorder.response.TicketsModel">
        select
        id               as ticketsId,
        carrier_order_id as carrierOrderId,
        image_path       as imagePath,
        image_name       as imageName,
        upload_user_name as uploadUserName,
        upload_time      as uploadTime,
        image_type       as imageType
        from t_carrier_order_tickets
        where valid = 1
        and carrier_order_id in (${carrierOrderIds})
    </select>

    <select id="getTicketsCountByCarrierOrderIdForWeb" resultType="com.logistics.tms.controller.carrierorder.response.CarrierOrderDetailTicketsModel">
        select
        ifnull(sum(if(tcot.image_type in (1, 2), 1, 0)), 0) as loadTicketsCount,
        ifnull(sum(if(tcot.image_type = 3, 1, 0)), 0)       as unLoadTicketsCount,
        ifnull(sum(if(tcot.image_type = 4, 1, 0)), 0)       as otherTicketsCount,
        ifnull(sum(if(tcot.image_type = 5, 1, 0)), 0)       as reachLoadTicketsCount,
        ifnull(sum(if(tcot.image_type = 6, 1, 0)), 0)       as reachUnloadTicketsCount
        from t_carrier_order_tickets tcot
        where tcot.valid = 1
        and tcot.carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
    </select>

    <resultMap id="getTicketsByCarrierOrderIdByWeb_map" type="com.logistics.tms.controller.carrierorder.response.CarrierOrderTicketsResponseModel">
        <result column="image_type" property="ticketType" jdbcType="INTEGER"/>
        <collection property="ticketsList" ofType="com.logistics.tms.controller.carrierorder.response.TicketsResponseModel">
            <id column="id" property="ticketsId" jdbcType="BIGINT"/>
            <result column="image_path" property="imagePath" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="getTicketsByCarrierOrderIdByWeb" resultMap="getTicketsByCarrierOrderIdByWeb_map">
        select
        id,
        image_type,
        image_path
        from t_carrier_order_tickets
        where valid = 1
        and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
        and image_type != 7
    </select>

    <select id="selectCarrierOrderIdByTicketUploadTimeAndType" resultType="java.lang.Long">
        select carrier_order_id
        from t_carrier_order_tickets
        where valid = 1
        and (upload_time >= DATE_FORMAT(#{uploadStartTime}, '%Y-%m-%d 00:00:00') and
             upload_time &lt;= DATE_FORMAT(#{uploadEndTime}, '%Y-%m-%d 23:59:59'))
        and image_type = #{type}
    </select>

    <select id="selectTicketsByCarrierOrderIdsAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_tickets
        where valid = 1
        and carrier_order_id in (${carrierOrderIds})
        and image_type = #{type}
    </select>
</mapper>