package com.logistics.appapi.client.driveraccount.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/27
 */
@Data
public class DriverAccountDetailResponseModel {

	@ApiModelProperty(value = "司机账户ID")
	private Long driverAccountId;

	@ApiModelProperty(value = "开户银行")
	private String bankAccountName;

	@ApiModelProperty(value = "银行账号")
	private String bankAccount;

	@ApiModelProperty(value = "司机id")
	private Long driverId;

	@ApiModelProperty(value = "司机姓名")
	private String driverName;

	@ApiModelProperty(value = "司机手机号")
	private String driverMobile;

	@ApiModelProperty(value = "开户支行名称")
	private String braBankName;

	@ApiModelProperty(value = "行号")
	private String bankCode;

	@ApiModelProperty(value = "银行卡图片")
	private List<String> bankAccountImages;
}
