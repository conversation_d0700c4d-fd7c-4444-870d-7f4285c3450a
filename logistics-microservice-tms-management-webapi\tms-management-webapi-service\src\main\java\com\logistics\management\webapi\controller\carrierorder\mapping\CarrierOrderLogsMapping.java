package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.controller.carrierorder.response.GetCarrierOrderLogsResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.carrierorder.response.GetCarrierOrderLogsResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @author: wjf
 * @date: 2018/11/6 10:34
 */
public class CarrierOrderLogsMapping extends MapperMapping<GetCarrierOrderLogsResponseModel,GetCarrierOrderLogsResponseDto> {
    @Override
    public void configure() {
        GetCarrierOrderLogsResponseModel model = getSource();
        GetCarrierOrderLogsResponseDto dto = getDestination();
        if (model != null){
            dto.setOperateTime(DateUtils.dateToString(model.getOperateTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
        }
    }
}
