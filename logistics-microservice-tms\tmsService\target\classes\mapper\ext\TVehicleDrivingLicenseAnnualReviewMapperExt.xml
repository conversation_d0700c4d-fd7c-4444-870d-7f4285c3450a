<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleDrivingLicenseAnnualReviewMapper">
    <select id="getDrivingLicenseAnnualReviewByVehicleIds"
           resultMap="DrivingLicenseAnnualReviewDetailMap">
      SELECT
        tqvdlar.id as drivingLicenseAnnualReviewId,
        tqvdlar.check_valid_date as checkValidDate,
        tqvdlar.remark as remark,
        tqvdlar.last_Modified_By,
        tqvdlar.last_modified_time,
         tqcp.id as fileId,
        tqcp.file_path,
        tqcp.upload_time,
        tqcp.upload_user_name
      FROM  t_vehicle_driving_license_annual_review tqvdlar
      left join t_vehicle_driving_license tvdl on tvdl.valid = 1 and tvdl.id = tqvdlar.driving_license_id
      LEFT JOIN t_certification_pictures tqcp ON tqcp.object_id = tqvdlar.id AND tqcp.object_type = 7 and tqcp.valid=1
       where tvdl.vehicle_id in (${vehicleIds}) and tqvdlar.valid = 1
      order by tqvdlar.check_valid_date desc,tqvdlar.id desc
  </select>
  <resultMap id="DrivingLicenseAnnualReviewDetailMap" type="com.logistics.tms.controller.vehicleassetmanagement.response.DrivingLicenseAnnualReviewListResponseModel">
    <id column="drivingLicenseAnnualReviewId" property="drivingLicenseAnnualReviewId" jdbcType="BIGINT"/>
    <result column="checkValidDate" property="checkValidDate" jdbcType="TIMESTAMP"/>
    <result column="last_Modified_By" property="lastModifiedBy" jdbcType="VARCHAR"/>
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
    <result column="remark" property="remark" jdbcType="VARCHAR"/>    <collection property="fileList" ofType="com.logistics.tms.controller.vehicleassetmanagement.response.CertificationPicturesResponseModel">
      <id column="fileId" property="fileId" jdbcType="BIGINT" />
      <result column="file_path" property="relativeFilepath" jdbcType="VARCHAR"/>
    <result column="upload_user_name" property="uploadUserName" jdbcType="VARCHAR"/>
    <result column="upload_time" property="uploadTime" jdbcType="TIMESTAMP"/>
    </collection>
  </resultMap>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TVehicleDrivingLicenseAnnualReview" useGeneratedKeys="true" keyProperty="id">
    <foreach collection="list" item="temp" separator=";">
       insert into t_vehicle_driving_license_annual_review
        <trim prefix="(" suffix=")" suffixOverrides="," >
          <if test="temp.id != null" >
            id,
          </if>
          <if test="temp.drivingLicenseId != null" >
            driving_license_id,
          </if>
          <if test="temp.checkValidDate != null" >
            check_valid_date,
          </if>
          <if test="temp.remark != null" >
            remark,
          </if>
          <if test="temp.createdBy != null" >
            created_by,
          </if>
          <if test="temp.createdTime != null" >
            created_time,
          </if>
          <if test="temp.lastModifiedBy != null" >
            last_modified_by,
          </if>
          <if test="temp.lastModifiedTime != null" >
            last_modified_time,
          </if>
          <if test="temp.valid != null" >
            valid,
          </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
          <if test="temp.id != null" >
            #{temp.id,jdbcType=BIGINT},
          </if>
          <if test="temp.drivingLicenseId != null" >
            #{temp.drivingLicenseId,jdbcType=BIGINT},
          </if>
          <if test="temp.checkValidDate != null" >
            #{temp.checkValidDate,jdbcType=TIMESTAMP},
          </if>
          <if test="temp.remark != null" >
            #{temp.remark,jdbcType=VARCHAR},
          </if>
          <if test="temp.createdBy != null" >
            #{temp.createdBy,jdbcType=VARCHAR},
          </if>
          <if test="temp.createdTime != null" >
            #{temp.createdTime,jdbcType=TIMESTAMP},
          </if>
          <if test="temp.lastModifiedBy != null" >
            #{temp.lastModifiedBy,jdbcType=VARCHAR},
          </if>
          <if test="temp.lastModifiedTime != null" >
            #{temp.lastModifiedTime,jdbcType=TIMESTAMP},
          </if>
          <if test="temp.valid != null" >
            #{temp.valid,jdbcType=INTEGER},
          </if>
        </trim>
    </foreach>
  </insert>

  <select id="getDueDrivingLicenseCount" resultType="java.util.HashMap">
      select
    ifnull(group_concat(if(thrityDueCount = 1, vehicle_id, null)), '') as totalIds,
      ifnull(group_concat(if(sevenDueCount = 1, vehicle_id, null)), '')  as weekIds,
      ifnull(group_concat(if(dueCount = 1, vehicle_id, null)), '')       as hasExpiredIds,
      ifnull(sum(thrityDueCount), 0)                                     as total,
      ifnull(sum(sevenDueCount), 0)                                      as week,
      ifnull(sum(dueCount), 0)                                           as hasExpired
      from (SELECT tcvr.id as vehicle_id,
            if(DATE_SUB(max(check_valid_date), INTERVAL #{remindDays,jdbcType=INTEGER} DAY) &lt;= DATE_FORMAT(now(), '%Y-%m-%d') and max(check_valid_date) >= DATE_FORMAT(now(), '%Y-%m-%d'), 1,
               0)                                                                                                                                                      as thrityDueCount,
            if(DATE_SUB(max(check_valid_date), INTERVAL 7 DAY) &lt;= DATE_FORMAT(now(), '%Y-%m-%d') and max(check_valid_date) >= DATE_FORMAT(now(), '%Y-%m-%d'), 1, 0) as sevenDueCount,
            if(max(check_valid_date) &lt; DATE_FORMAT(now(), '%Y-%m-%d'), 1, 0)                                                                                        as dueCount
            from t_vehicle_driving_license_annual_review t1
            left join t_vehicle_driving_license t2 on t1.driving_license_id = t2.id and t2.valid = 1
            left join t_vehicle_basic t3 on t3.id = t2.vehicle_id and t3.valid = 1
            left join t_carrier_vehicle_relation tcvr on tcvr.valid = 1 and tcvr.vehicle_id = t3.id and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
            where t1.valid = 1
            and t3.operating_state = 1
            and tcvr.id is not null
            group by t2.vehicle_id) tmp
  </select>


  <update id="batchUpdate">
    <foreach collection="list" item="item" separator=";">
      update t_vehicle_driving_license_annual_review
      <set >
        <if test="item.drivingLicenseId != null" >
          driving_license_id = #{item.drivingLicenseId,jdbcType=BIGINT},
        </if>
        <if test="item.checkValidDate != null" >
          check_valid_date = #{item.checkValidDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.remark != null" >
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>

  </update>
  
  <select id="countDrivingLicenseByDate" resultType="java.lang.Integer">
    select count(1)
    from t_vehicle_driving_license_annual_review
    where valid = 1
    and driving_license_id =#{drivingLicenseId,jdbcType =BIGINT}
    and check_valid_date = #{checkValidDate,jdbcType=TIMESTAMP}
  </select>
</mapper>