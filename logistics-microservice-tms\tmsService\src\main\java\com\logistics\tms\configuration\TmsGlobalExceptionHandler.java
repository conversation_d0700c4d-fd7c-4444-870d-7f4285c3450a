package com.logistics.tms.configuration;

import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BaseExceptionCodeEnum;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

@ResponseBody
@ControllerAdvice(basePackages = {"com.leyi", "com.logistics", "com.yelo"})
@Order(-1)
@Slf4j
public class TmsGlobalExceptionHandler {

    @ExceptionHandler({ClientAbortException.class})
    public Result<Void> clientAbortHandler(ClientAbortException ex) {
        log.info("输出流异常:"+ ex.getMessage());
        return new Result<>(-19999, null, ex.getMessage());
    }
}
