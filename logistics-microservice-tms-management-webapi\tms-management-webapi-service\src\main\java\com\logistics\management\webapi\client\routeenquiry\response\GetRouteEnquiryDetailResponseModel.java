package com.logistics.management.webapi.client.routeenquiry.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/9 17:16
 */
@Data
public class GetRouteEnquiryDetailResponseModel {

    /**
     * 路线询价单表id
     */
    private Long routeEnquiryId;

    /**
     * 竞价单号
     */
    private String orderCode;

    /**
     * 竞价状态：1 待业务审核，2 待车主确认，3 待结算审核，4 完成竞价
     */
    private Integer status;

    /**
     * 是否取消：0 否，1 是
     */
    private Integer ifCancel;

    /**
     * 归档：0 否，1 是
     */
    private Integer ifArchive;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 货物名称
     */
    private String goodsName;

    /**
     * 报价生效开始时间
     */
    private Date quoteStartTime;

    /**
     * 报价生效结束时间
     */
    private Date quoteEndTime;

    /**
     * 备注
     */
    private String remark;


    /**
     * 竞价详情-地址列表
     */
    private List<GetRouteEnquiryDetailAddressListResponseModel> addressList=new ArrayList<>();


    /**
     * 车主报价记录
     */
    private List<RouteEnquiryQuoteListResponseModel> quoteList=new ArrayList<>();


    /**
     * 操作记录
     */
    private List<RouteEnquiryOperateLogListResponseModel> operateLogList=new ArrayList<>();


    /**
     * 上传文件列表
     */
    private List<RouteEnquiryFileListResponseModel> fileList=new ArrayList<>();

}
