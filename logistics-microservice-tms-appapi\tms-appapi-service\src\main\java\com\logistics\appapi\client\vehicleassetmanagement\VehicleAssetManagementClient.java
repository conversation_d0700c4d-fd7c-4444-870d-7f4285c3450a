package com.logistics.appapi.client.vehicleassetmanagement;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.vehicleassetmanagement.hystrix.VehicleAssetManagementClientHystrix;
import com.logistics.appapi.client.vehicleassetmanagement.request.SearchVehicleByPropertyRequestModel;
import com.logistics.appapi.client.vehicleassetmanagement.response.SearchVehicleByPropertyResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/11 14:35
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/vehicleAssetManagement",
        fallback = VehicleAssetManagementClientHystrix.class)
public interface VehicleAssetManagementClient {

    @ApiOperation("根据车牌号模糊搜索、车辆机构查询车辆信息（分页）")
    @PostMapping({"/searchVehicleByProperty"})
    Result<PageInfo<SearchVehicleByPropertyResponseModel>> searchVehicleByProperty(@RequestBody SearchVehicleByPropertyRequestModel requestModel);

}
