package com.logistics.tms.base.enums;

public enum RenewableAuditOperateLogsEnum {

    ASSIGN_DRIVER(1001,"指派","指派%s %s"),
    UPDATE_ASSIGN_DRIVER(1002,"修改指派","修改指派%s %s"),
    CONFIRMNEWS(1003,"确认信息","合计%skg，%s元");

    private Integer key;
    private String value;
    private String format;

    RenewableAuditOperateLogsEnum(Integer key, String value, String format) {
        this.key = key;
        this.value = value;
        this.format = format;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getFormat() {
        return format;
    }
}
