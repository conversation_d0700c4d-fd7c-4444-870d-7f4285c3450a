package com.logistics.management.webapi.client.freightconfig.request.ladder;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.exception.BaseExceptionCodeEnum;
import com.yelo.tray.core.exception.BizException;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.function.BiConsumer;

public class CarrierFreightConfigLadderRequestCheck {

    public static void check(List<CarrierFreightConfigLadderRequestModel> ladderConfigList) {
        check(BigDecimal.ZERO, ladderConfigList);
    }

    public static void check(BigDecimal toAmount, List<CarrierFreightConfigLadderRequestModel> ladderConfigList) {
        // 校验是否小数位
        BiConsumer<BigDecimal, Integer> bitCheckConsumer = (v, scale) -> {
            if (v.stripTrailingZeros().scale() > scale) {
                throw new BizException(ManagementWebApiExceptionEnum.DEFEND_LADDER_PRICE);
            }
        };
        // 金额限制校验
        BiConsumer<Integer, BigDecimal> checkPriceConsumer = (priceMode, price) -> {
            if (Objects.isNull(price)) {
                throw new BizException(BaseExceptionCodeEnum.REST_METHOD_ARGUMENT_NOT_VALID_ERROR.getCode(), "阶梯价格不允许为空");
            }
            // 价格校验
            BigDecimal max;
            String errorMessage = "请正确填写阶梯价格，0<单价≤%s，允许保留2位小数";
            // 总价 0<一口价<=1,000,000(100万);
            if (CommonConstant.INTEGER_TWO.equals(priceMode)) {
                max = CommonConstant.BIG_DECIMAL_MILLION;
            }
            // 单价 0<单价<=5000;
            else {
                max = CommonConstant.BIG_DECIMAL_FIFTY_THOUSAND;
            }
            if (price.compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ONE
                    || price.compareTo(max) > CommonConstant.INTEGER_ZERO) {
                throw new BizException(BaseExceptionCodeEnum.REST_METHOD_ARGUMENT_NOT_VALID_ERROR.getCode(), String.format(errorMessage, max));
            }
        };
        // 阶梯数量校验
        for (CarrierFreightConfigLadderRequestModel ladder : ladderConfigList) {
            // 阶梯价格位数校验
            Integer scale = CommonConstant.INTEGER_ONE.equals(ladder.getLadderType()) ? CommonConstant.INTEGER_TWO :
                    CommonConstant.INTEGER_TWO.equals(ladder.getLadderUnit()) ? CommonConstant.INTEGER_THREE : CommonConstant.INTEGER_ZERO;

            // 固定价格
            if (CommonConstant.INTEGER_ZERO.equals(ladder.getLadderLevel())) {
                // 清空起始、终止参数
                ladder.setLadderFrom(BigDecimal.ZERO);
                ladder.setLadderTo(BigDecimal.ZERO);
                checkPriceConsumer.accept(ladder.getPriceMode(), ladder.getUnitPrice());
            }
            // 阶梯
            else {
                if (ladder.getLadderFrom() == null || ladder.getLadderTo() == null || ladder.getLadderType() == null) {
                    throw new BizException(ManagementWebApiExceptionEnum.DEFEND_LADDER_PRICE);
                }

                // 多级阶梯
                if (ListUtils.isNotEmpty(ladder.getLadderConfigList())) {
                    // 清空价格
                    ladder.setUnitPrice(BigDecimal.ZERO);
                    check(BigDecimal.ZERO, ladder.getLadderConfigList());
                } else {
                    checkPriceConsumer.accept(ladder.getPriceMode(), ladder.getUnitPrice());
                }
                bitCheckConsumer.accept(ladder.getLadderFrom(), scale);
                bitCheckConsumer.accept(ladder.getLadderTo(), scale);
                // 起始数量高于等于终止数量 或 起始数量没有从前一阶数量开始
                if (ladder.getLadderFrom().compareTo(ladder.getLadderTo()) > -1
                        || toAmount.compareTo(ladder.getLadderFrom()) != 0) {
                    throw new BizException(ManagementWebApiExceptionEnum.START_PRICE_HIGHER_THAN_END_PRICE);
                }
                toAmount = ladder.getLadderTo();
            }
        }
    }
}
