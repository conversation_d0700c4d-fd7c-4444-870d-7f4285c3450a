package com.logistics.appapi.controller.carrierorderotherfee.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/9/2 13:09
 */
@Data
public class CommitCarrierOrderOtherFeeItemRequestDto {
    @ApiModelProperty(value = "临时费用明细id",required = true)
    private String carrierOrderOtherFeeItemId;

    @ApiModelProperty(value = "费用名称：1 短驳费，2 保管费，3 装卸费，4 压车费，5 质量处罚费，6 其他杂费",required = true)
    @NotBlank(message = "请选择费用名称")
    @Range(min = 1, max = 6, message = "请选择费用名称")
    private String feeType;

    @ApiModelProperty(value = "调整费用符号：1 ‘+’， 2 ‘-’",required = true)
    @NotBlank(message = "调整费用符号不能为空")
    @Range(min = 1, max = 2, message = "调整费用符号不能为空")
    private String feeAmountSymbol;

    @ApiModelProperty(value = "费用金额",required = true)
    @NotBlank(message = "请维护费用金额，0<费用<=10000元")
    @DecimalMin(value = "0.01", message = "请维护费用金额，0<费用<=10000元")
    @DecimalMax(value = "10000", message = "请维护费用金额，0<费用<=10000元")
    private String feeAmount;

    /**
     * 单据 v2.43
     */
    @ApiModelProperty(value = "单据 v2.43",required = true)
    @NotEmpty(message = "请上传单据")
    private List<String> billsPicture;
}
