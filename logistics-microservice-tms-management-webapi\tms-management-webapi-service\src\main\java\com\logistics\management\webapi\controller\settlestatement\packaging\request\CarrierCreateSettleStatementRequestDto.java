package com.logistics.management.webapi.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class CarrierCreateSettleStatementRequestDto {

    @ApiModelProperty(value = "对账月份 yyyy-MM",required = true)
    @NotBlank(message = "请填写对账月份")
    private String settleStatementMonth;

    @ApiModelProperty("对账单名称")
    @Size(max = 20, message = "请正确维护对账单名称，最多20字")
    private String settleStatementName;

    @ApiModelProperty(value = "临时费用费点,0<=数值范围<=20", required = true)
    @NotBlank(message = "请填写临时费用费点")
    @Range(min = 0, max = 20, message = "请正确填写临时费用费点")
    private String otherFeeTaxPoint;

    @ApiModelProperty(value = "运费费点,0<=数值范围<=20", required = true)
    @NotBlank(message = "请填写运费费点")
    @Range(min = 0, max = 20, message = "请正确填写运费费点")
    private String freightTaxPoint;

    /*查询条件*/
    @ApiModelProperty("车主名模糊查询")
    private String companyCarrierName;

    /**
     * (3.27.0)对账状态：-3 待完结，-2 未关联
     */
    private String carrierSettleStatementStatus;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("调度单号")
    private String dispatchOrderCode;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("司机（支持模糊搜索司机姓名或手机号）")
    private String driver;

    @ApiModelProperty("报价类型：1 单价，2 一口价")
    private String carrierPriceType;

    @ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨 1.3.3")
    private String entrustType;

    @ApiModelProperty("发货地址，发货省市区+详细地址")
    private String loadAddress;

    @ApiModelProperty("收货地址，发货省市区+详细地址")
    private String unloadAddress;

    @ApiModelProperty("发货仓库")
    private String loadWarehouse;

    @ApiModelProperty("收货仓库")
    private String unloadWarehouse;

    @ApiModelProperty("提货时间起")
    private String loadTimeStart;

    @ApiModelProperty("提货时间止")
    private String loadTimeEnd;

    @ApiModelProperty("卸货时间起")
    private String unloadTimeStart;

    @ApiModelProperty("卸货时间始")
    private String unloadTimeEnd;

    @ApiModelProperty("签收时间起")
    private String signTimeStart;

    @ApiModelProperty("签收时间止")
    private String signTimeEnd;

    @ApiModelProperty("1.3.7新增；项目标签：1 石化板块，2 轮胎板块，3 涂料板块，4 其他")
    private String projectLabel;

    @ApiModelProperty("拼单助手运单号")
    private List<String> carrierOrderCodeList;

    @ApiModelProperty("拼单助手需求单号")
    private List<String> demandOrderCodeList;

    @ApiModelProperty("若有勾选导出则传此字段，运单ids  ','拼接")
    private String carrierOrderIds;
}
