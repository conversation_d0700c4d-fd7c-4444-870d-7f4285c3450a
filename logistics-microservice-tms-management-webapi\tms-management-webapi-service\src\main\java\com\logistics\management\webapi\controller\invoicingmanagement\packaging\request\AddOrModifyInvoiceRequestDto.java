package com.logistics.management.webapi.controller.invoicingmanagement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @author: wjf
 * @date: 2024/3/19 17:26
 */
@Data
public class AddOrModifyInvoiceRequestDto {
    @ApiModelProperty(value = "发票管理id")
    @NotBlank(message = "发票管理id 必填")
    private String invoicingId;

    @ApiModelProperty(value = "发票id（编辑必填）")
    private String invoiceId;

    @ApiModelProperty(value = "发票类型：1 电子发票，2 纸质发票",required = true)
    @NotBlank(message = "请选择发票类型")
    @Range(min = 1, max = 2, message = "发票类型只能只1或2")
    private String invoiceType;

    @ApiModelProperty(value = "发票图片",required = true)
    @NotBlank(message = "请选择上传发票图片")
    private String invoicePicture;

    @ApiModelProperty("发票代码")
    @Size(max = 20, message = "发票代码长度不能超过20")
    private String invoiceCode;

    @ApiModelProperty("发票号码")
    @Size(max = 20, message = "发票号码长度不能超过20")
    private String invoiceNum;

    @ApiModelProperty("开票日期")
    private String invoiceDate;

    @ApiModelProperty("发票金额")
    @DecimalMax(value = "1000000",message = "发票金额不能超过1000000")
    private String invoiceAmount;

    @ApiModelProperty("税率")
    @DecimalMax(value = "100",message = "税率不能超过100")
    private String taxRate;

    @ApiModelProperty("税额合计")
    @DecimalMax(value = "1000000",message = "税额合计不能超过1000000")
    private String totalTaxAndPrice;

}
