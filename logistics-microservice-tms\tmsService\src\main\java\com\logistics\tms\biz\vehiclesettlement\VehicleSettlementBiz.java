package com.logistics.tms.biz.vehiclesettlement;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.carrierorderotherfee.CarrierOrderOtherFeeCommonBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.controller.vehiclesettlement.request.*;
import com.logistics.tms.controller.vehiclesettlement.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2019/10/10 9:00
 */
@Service
@Slf4j
public class VehicleSettlementBiz {

    @Autowired
    private TVehicleSettlementMapper vehicleSettlementMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCarrierOrderMapper carrierOrderMapper;
    @Autowired
    private TVehicleTireMapper vehicleTireMapper;
    @Autowired
    private TOilFilledMapper oilFilledMapper;
    @Autowired
    private TGpsFeeMapper gpsFeeMapper;
    @Autowired
    private TDeductingHistoryMapper deductingHistoryMapper;
    @Autowired
    private TParkingFeeMapper parkingFeeMapper;
    @Autowired
    private TLoanRecordsMapper loanRecordsMapper;
    @Autowired
    private TInsuranceCostsMapper insuranceCostsMapper;
    @Autowired
    private TInsuranceCostsRelationMapper insuranceCostsRelationMapper;
    @Autowired
    private TCertificationPicturesMapper certificationPicturesMapper;
    @Autowired
    private TLoanSettlementRecordMapper loanSettlementRecordMapper;
    @Autowired
    private TInsuranceMapper insuranceMapper;
    @Autowired
    private TVehicleBasicMapper vehicleBasicMapper;
    @Autowired
    private TVehicleSettlementRelationMapper vehicleSettlementRelationMapper;
    @Autowired
    private TVehicleSettlementDriverRelationMapper tVehicleSettlementDriverRelationMapper;
    @Autowired
    private TStaffBasicMapper tStaffBasicMapper;
    @Autowired
    private TVehicleSettlementPaymentMapper tVehicleSettlementPaymentMapper;
    @Autowired
    private TVehicleSettlementEventsMapper tVehicleSettlementEventsMapper;
    @Autowired
    private CarrierOrderOtherFeeCommonBiz carrierOrderOtherFeeCommonBiz;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;

    /**
     * 车辆结算列表
     *
     * @param requestModel
     * @return
     */
    public List<SearchVehicleSettlementListResponseModel> searchVehicleSettlementList(SearchVehicleSettlementListRequestModel requestModel) {
        return vehicleSettlementMapper.searchVehicleSettlementList(requestModel);
    }

    /**
     * 车辆结算列表数量
     *
     * @param requestModel
     * @return
     */
    public SearchVehicleSettlementListCountResponseModel searchVehicleSettlementListCount(SearchVehicleSettlementListRequestModel requestModel) {
        return vehicleSettlementMapper.searchVehicleSettlementListCount(requestModel);
    }

    /**
     * 车辆结算详情
     *
     * @param requestModel
     * @return
     */
    public GetVehicleSettlementDetailResponseModel getVehicleSettlementDetail(VehicleSettlementDetailRequestModel requestModel) {
        TVehicleSettlement tVehicleSettlement = vehicleSettlementMapper.selectByPrimaryKey(requestModel.getVehicleSettlementId());
        if (tVehicleSettlement == null || tVehicleSettlement.getValid().equals(CommonConstant.INTEGER_ZERO)) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getOperateType())) {
            String currentMonth = DateUtils.dateToString(new Date(), CommonConstant.DATE_TO_STRING_YM_PATTERN);
            if (tVehicleSettlement.getSettlementMonth().equals(currentMonth)) {
                throw new BizException(CarrierDataExceptionEnum.CURRENT_MONTH_NOT_SETTLEMENT);
            }
            if (!VehicleSettlementStatementStatusEnum.WAIT_SETTLE_STATEMENT.getKey().equals(tVehicleSettlement.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_HAS_SETTLEMENT);
            }
            TVehicleSettlement firstNotSettlement = vehicleSettlementMapper.getFirstNotSettlementByVehicleId(tVehicleSettlement.getVehicleId());
            if (firstNotSettlement.getSettlementMonth().compareTo(tVehicleSettlement.getSettlementMonth()) < CommonConstant.INTEGER_ZERO ) {
                throw new BizException(CarrierDataExceptionEnum.FIRST_NOT_SETTLEMENT.getCode(), MessageFormat.format(CarrierDataExceptionEnum.FIRST_NOT_SETTLEMENT.getMsg(), firstNotSettlement.getSettlementMonth()));
            }
        }
        List<TVehicleSettlementRelation> relationList = vehicleSettlementRelationMapper.getByVehicleSettlementId(tVehicleSettlement.getId());
        if (ListUtils.isEmpty(relationList)) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }
        List<Long> carrierOrderIdList = new ArrayList<>();
        List<Long> vehicleTireIdList = new ArrayList<>();
        List<Long> oilFilledIdList = new ArrayList<>();
        List<Long> gpsFeeIdList = new ArrayList<>();
        List<Long> parkingFeeIdList = new ArrayList<>();
        List<Long> loanRecordsIdList = new ArrayList<>();
        List<Long> insuranceCostsIdList = new ArrayList<>();
        for (TVehicleSettlementRelation relation : relationList) {
            if (relation.getObjectType().equals(VehicleSettlementTypeEnum.GPS.getKey())) {
                gpsFeeIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.PARKING.getKey())) {
                parkingFeeIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.LOAN.getKey())) {
                loanRecordsIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.TIRE.getKey())) {
                vehicleTireIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.OIL.getKey()) ) {
                oilFilledIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.INSURANCE.getKey())) {
                insuranceCostsIdList.add(relation.getObjectId());
            }else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.CARRIER_ORDER.getKey())) {
                carrierOrderIdList.add(relation.getObjectId());
            }
        }

        GetVehicleSettlementDetailResponseModel detail = vehicleSettlementMapper.getVehicleSettlementDetail(requestModel.getVehicleSettlementId());
        List<VehicleSettlementEventModel> eventModelList=tVehicleSettlementEventsMapper.getByVehicleSettlementId(requestModel.getVehicleSettlementId());
        detail.setEventList(eventModelList);
        //查询该车当月是否有已卸货的运单
        List<GetCarrierOrderByVehicleIdResponseModel> carrierOrderList = new ArrayList<>();
        if (ListUtils.isNotEmpty(carrierOrderIdList)) {
            //查询运单信息
            carrierOrderList = carrierOrderMapper.getCarrierOrderByIdsForSettlement(StringUtils.listToString(carrierOrderIdList, ','));

            List<Long> companyCarrierIdList = carrierOrderList.stream().map(GetCarrierOrderByVehicleIdResponseModel::getCompanyCarrierId).distinct().collect(Collectors.toList());
            //查询车主信息
            Map<Long, Integer> companyLevelMap = tCompanyCarrierMapper.getByIds(StringUtils.listToString(companyCarrierIdList,',')).stream().collect(Collectors.toMap(TCompanyCarrier::getId, TCompanyCarrier::getLevel));

            //查询运单临时费用信息
            Map<Long, BigDecimal> driverOtherFeeMap = carrierOrderOtherFeeCommonBiz.getAuditFee(StringUtils.listToString(carrierOrderIdList,','));

            //赋值临时费用
            if (!MapUtils.isEmpty(driverOtherFeeMap)){
                for (GetCarrierOrderByVehicleIdResponseModel order : carrierOrderList) {
                    //赋值司机临时费用-我司时，司机才加临时费用
                    if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(companyLevelMap.get(order.getCompanyCarrierId()))) {
                        order.setDriverOtherFee(driverOtherFeeMap.get(order.getCarrierOrderId()));
                    }
                }
            }

        }
        //查询该车当月是否有已维护的轮胎费用
        List<GetVehicleTireByVehicleIdResponseModel> vehicleTireList = new ArrayList<>();
        if (ListUtils.isNotEmpty(vehicleTireIdList)) {
            vehicleTireList = vehicleTireMapper.getVehicleTireByIdsForSettlement(StringUtils.listToString(vehicleTireIdList, ','));
        }
        //查询该车当月是否有已维护的充油费用、退款
        List<GetOilFilledByVehicleIdResponseModel> oilFilledList= new ArrayList<>();
        if (ListUtils.isNotEmpty(oilFilledIdList)) {
            oilFilledList = oilFilledMapper.getOilFilledByIdsForSettlement(StringUtils.listToString(oilFilledIdList, ','));
        }
        String settlementMonth = tVehicleSettlement.getSettlementMonth();
        //查询该车当月所属周期内未还清的GPS费用
        GetGpsFeeByVehicleIdResponseModel tGpsFee = null;
        if (ListUtils.isNotEmpty(gpsFeeIdList)) {
            tGpsFee = gpsFeeMapper.getCurrentDeductingByIdForSettlement(gpsFeeIdList.get(CommonConstant.INTEGER_ZERO), settlementMonth);
            tGpsFee.setCurrentMonth(settlementMonth);
        }
        //查询该车当月所属周期内未还清的停车费用
        GetParkingFeeByVehicleIdResponseModel tParkingFee = null;
        if (ListUtils.isNotEmpty(parkingFeeIdList)) {
            tParkingFee = parkingFeeMapper.getCurrentDeductingByIdForSettlement(parkingFeeIdList.get(CommonConstant.INTEGER_ZERO), settlementMonth);
            tParkingFee.setCurrentMonth(settlementMonth);
        }
        //查询该车是否存在贷款费用
        GetLoanFeeByVehicleIdResponseModel tLoanFee = null;
        if (ListUtils.isNotEmpty(loanRecordsIdList)) {
            tLoanFee = loanRecordsMapper.getSettlementRecordsByIdForSettlement(loanRecordsIdList.get(CommonConstant.INTEGER_ZERO), settlementMonth);
            tLoanFee.setCurrentMonth(settlementMonth);
        }
        //查询该车是否有未结算的保险费用（未结算详情）或当月是否有已结算的保险费用（已结算详情）
        Long insuranceCostId = null;
        if (ListUtils.isNotEmpty(insuranceCostsIdList)){
            insuranceCostId = insuranceCostsIdList.get(CommonConstant.INTEGER_ZERO);
        }
        List<VehicleInsuranceCostResponseModel> insuranceCostList = getInsuranceInfoByVehicle(detail.getVehicleId(), insuranceCostId);

        detail.setCarrierOrderList(carrierOrderList);
        detail.setVehicleTireList(vehicleTireList);
        detail.setOilFilledList(oilFilledList);
        detail.setGpsFeeModel(tGpsFee);
        detail.setParkingFeeModel(tParkingFee);
        detail.setLoanFeeModel(tLoanFee);
        detail.setInsuranceCostList(insuranceCostList);
        return detail;
    }
    //查询车辆保险费用
    public List<VehicleInsuranceCostResponseModel> getInsuranceInfoByVehicle(Long vehicleId, Long insuranceCostId){
        List<VehicleInsuranceCostResponseModel> insuranceCostList = new ArrayList<>();
        if (insuranceCostId == null) {//未结算则查询车辆保险费用
            //查询车辆
            VehicleBasicPropertyModel vehicleBasicPropertyById = vehicleBasicMapper.getVehicleBasicPropertyById(vehicleId);
            //停运的车辆不管、只有自有车辆、一体车、牵引车才可以保险费用
            if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(vehicleBasicPropertyById.getOperatingState())
                    || !(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasicPropertyById.getVehicleProperty()) || VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasicPropertyById.getVehicleProperty()))
                    || (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasicPropertyById.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasicPropertyById.getVehicleCategory()))) {
                return insuranceCostList;
            }

            boolean ifSelectInsuranceCompulsory = true;
            boolean ifSelectInsuranceCommercial = true;
            boolean ifSelectInsuranceCargo = true;
            boolean ifSelectInsuranceCarrier = true;
            //先查询是否有结算的保险费用
            Map<Integer, TInsurance> insuranceMap = new HashMap<>();
            TInsuranceCosts tInsuranceCostLast = insuranceCostsMapper.getLastByVehicleId(vehicleBasicPropertyById.getVehicleId());
            if (tInsuranceCostLast != null) {
                Date now = new Date();
                //查询保单信息
                List<Long> insuranceIdList = new ArrayList<>();
                List<TInsuranceCostsRelation> byInsuranceCostsId = insuranceCostsRelationMapper.getByInsuranceCostsId(tInsuranceCostLast.getId());
                if (ListUtils.isNotEmpty(byInsuranceCostsId)) {
                    insuranceIdList = byInsuranceCostsId.stream().map(TInsuranceCostsRelation::getInsuranceId).collect(Collectors.toList());
                }
                if (ListUtils.isNotEmpty(insuranceIdList)) {
                    //查询对应的保单信息
                    List<TInsurance> insuranceMapperByIds = insuranceMapper.getByIds(StringUtils.listToString(insuranceIdList, ','));
                    if (ListUtils.isNotEmpty(insuranceMapperByIds)) {
                        for (TInsurance item : insuranceMapperByIds) {
                            insuranceMap.put(item.getInsuranceType(), item);
                        }
                    }
                }
                //如果保险有结算，则取上次结算剩余的
                if (tInsuranceCostLast.getCompulsoryInsuranceCost().compareTo(CommonConstant.BIG_DECIMAL_ZERO) > CommonConstant.INTEGER_ZERO) {
                    TInsurance tInsurance = insuranceMap.get(InsuranceEnum.COMPULSORY.getKey());
                    //保险不为空，并且在有效期内，才取上一笔费用
                    if (tInsurance != null && InsuranceStatusTypeEnum.NORMAL.getKey().equals(tInsurance.getStatusType()) && tInsurance.getStartTime() != null && tInsurance.getEndTime() != null && now.after(tInsurance.getStartTime()) && now.before(tInsurance.getEndTime())) {
                        VehicleInsuranceCostResponseModel vehicleInsuranceCostResponseModel = new VehicleInsuranceCostResponseModel();
                        vehicleInsuranceCostResponseModel.setInsuranceId(tInsurance.getId());
                        vehicleInsuranceCostResponseModel.setInsuranceType(InsuranceEnum.COMPULSORY.getKey());
                        vehicleInsuranceCostResponseModel.setInsuranceTypeLabel(InsuranceEnum.COMPULSORY.getValue());
                        vehicleInsuranceCostResponseModel.setPolicyNo(tInsurance.getPolicyNo());
                        vehicleInsuranceCostResponseModel.setTotalCost(tInsurance.getPremium().add(tInsurance.getPaymentOfVehicleAndVesselTax()));
                        vehicleInsuranceCostResponseModel.setRemainNotDeductionCost(tInsuranceCostLast.getCompulsoryInsuranceCost());
                        vehicleInsuranceCostResponseModel.setUnPaidCost(tInsuranceCostLast.getCompulsoryInsuranceCost());
                        insuranceCostList.add(vehicleInsuranceCostResponseModel);
                        ifSelectInsuranceCompulsory = false;
                    }
                }
                if (tInsuranceCostLast.getCommercialInsuranceCost().compareTo(CommonConstant.BIG_DECIMAL_ZERO) > CommonConstant.INTEGER_ZERO) {
                    TInsurance tInsurance = insuranceMap.get(InsuranceEnum.COMMERCIAL.getKey());
                    if (tInsurance != null && InsuranceStatusTypeEnum.NORMAL.getKey().equals(tInsurance.getStatusType()) && tInsurance.getStartTime() != null && tInsurance.getEndTime() != null && now.after(tInsurance.getStartTime()) && now.before(tInsurance.getEndTime())) {
                        VehicleInsuranceCostResponseModel vehicleInsuranceCostResponseModel = new VehicleInsuranceCostResponseModel();
                        vehicleInsuranceCostResponseModel.setInsuranceId(tInsurance.getId());
                        vehicleInsuranceCostResponseModel.setInsuranceType(InsuranceEnum.COMMERCIAL.getKey());
                        vehicleInsuranceCostResponseModel.setInsuranceTypeLabel(InsuranceEnum.COMMERCIAL.getValue());
                        vehicleInsuranceCostResponseModel.setPolicyNo(tInsurance.getPolicyNo());
                        vehicleInsuranceCostResponseModel.setTotalCost(tInsurance.getPremium());
                        vehicleInsuranceCostResponseModel.setRemainNotDeductionCost(tInsuranceCostLast.getCommercialInsuranceCost());
                        vehicleInsuranceCostResponseModel.setUnPaidCost(tInsuranceCostLast.getCommercialInsuranceCost());
                        insuranceCostList.add(vehicleInsuranceCostResponseModel);
                        ifSelectInsuranceCommercial = false;
                    }
                }
                if (tInsuranceCostLast.getCargoInsuranceCost().compareTo(CommonConstant.BIG_DECIMAL_ZERO) > CommonConstant.INTEGER_ZERO) {
                    TInsurance tInsurance = insuranceMap.get(InsuranceEnum.CARGO.getKey());
                    if (tInsurance != null && InsuranceStatusTypeEnum.NORMAL.getKey().equals(tInsurance.getStatusType()) && tInsurance.getStartTime() != null && tInsurance.getEndTime() != null && now.after(tInsurance.getStartTime()) && now.before(tInsurance.getEndTime())) {
                        VehicleInsuranceCostResponseModel vehicleInsuranceCostResponseModel = new VehicleInsuranceCostResponseModel();
                        vehicleInsuranceCostResponseModel.setInsuranceId(tInsurance.getId());
                        vehicleInsuranceCostResponseModel.setInsuranceType(InsuranceEnum.CARGO.getKey());
                        vehicleInsuranceCostResponseModel.setInsuranceTypeLabel(InsuranceEnum.CARGO.getValue());
                        vehicleInsuranceCostResponseModel.setPolicyNo(tInsurance.getPolicyNo());
                        vehicleInsuranceCostResponseModel.setTotalCost(tInsurance.getPremium());
                        vehicleInsuranceCostResponseModel.setRemainNotDeductionCost(tInsuranceCostLast.getCargoInsuranceCost());
                        vehicleInsuranceCostResponseModel.setUnPaidCost(tInsuranceCostLast.getCargoInsuranceCost());
                        insuranceCostList.add(vehicleInsuranceCostResponseModel);
                        ifSelectInsuranceCargo = false;
                    }
                }
                if (tInsuranceCostLast.getCarrierInsuranceCost().compareTo(CommonConstant.BIG_DECIMAL_ZERO) > CommonConstant.INTEGER_ZERO) {
                    TInsurance tInsurance = insuranceMap.get(InsuranceEnum.CARRIER.getKey());
                    if (tInsurance != null && InsuranceStatusTypeEnum.NORMAL.getKey().equals(tInsurance.getStatusType()) && tInsurance.getStartTime() != null && tInsurance.getEndTime() != null && now.after(tInsurance.getStartTime()) && now.before(tInsurance.getEndTime())) {
                        VehicleInsuranceCostResponseModel vehicleInsuranceCostResponseModel = new VehicleInsuranceCostResponseModel();
                        vehicleInsuranceCostResponseModel.setInsuranceId(tInsurance.getId());
                        vehicleInsuranceCostResponseModel.setInsuranceType(InsuranceEnum.CARRIER.getKey());
                        vehicleInsuranceCostResponseModel.setInsuranceTypeLabel(InsuranceEnum.CARRIER.getValue());
                        vehicleInsuranceCostResponseModel.setPolicyNo(tInsurance.getPolicyNo());
                        vehicleInsuranceCostResponseModel.setTotalCost(tInsurance.getPremium());
                        vehicleInsuranceCostResponseModel.setRemainNotDeductionCost(tInsuranceCostLast.getCarrierInsuranceCost());
                        vehicleInsuranceCostResponseModel.setUnPaidCost(tInsuranceCostLast.getCarrierInsuranceCost());
                        insuranceCostList.add(vehicleInsuranceCostResponseModel);
                        ifSelectInsuranceCarrier = false;
                    }
                }
            }
            //如果保险没有结算，则取保险上的费用
            //获取该车辆在当前周期内的保险
            Map<Integer, TInsurance> currentInsuranceMap = new HashMap<>();
            if (ifSelectInsuranceCompulsory || ifSelectInsuranceCommercial || ifSelectInsuranceCargo || ifSelectInsuranceCarrier) {
                List<TInsurance> byVehicleIdAndCurrentPeriod = insuranceMapper.getByVehicleIdAndPeriod(vehicleBasicPropertyById.getVehicleId(), new Date());
                if (ListUtils.isNotEmpty(byVehicleIdAndCurrentPeriod)) {
                    for (TInsurance item : byVehicleIdAndCurrentPeriod) {
                        currentInsuranceMap.put(item.getInsuranceType(), item);
                    }
                }
            }
            if (ifSelectInsuranceCompulsory) {
                TInsurance tInsurance = currentInsuranceMap.get(InsuranceEnum.COMPULSORY.getKey());
                if (tInsurance != null && tInsurance.getUnpaidPremium().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    VehicleInsuranceCostResponseModel vehicleInsuranceCostResponseModel = new VehicleInsuranceCostResponseModel();
                    vehicleInsuranceCostResponseModel.setInsuranceId(tInsurance.getId());
                    vehicleInsuranceCostResponseModel.setInsuranceType(InsuranceEnum.COMPULSORY.getKey());
                    vehicleInsuranceCostResponseModel.setInsuranceTypeLabel(InsuranceEnum.COMPULSORY.getValue());
                    vehicleInsuranceCostResponseModel.setPolicyNo(tInsurance.getPolicyNo());
                    vehicleInsuranceCostResponseModel.setTotalCost(tInsurance.getPremium().add(tInsurance.getPaymentOfVehicleAndVesselTax()));
                    vehicleInsuranceCostResponseModel.setRemainNotDeductionCost(tInsurance.getUnpaidPremium());
                    vehicleInsuranceCostResponseModel.setUnPaidCost(tInsurance.getUnpaidPremium());
                    insuranceCostList.add(vehicleInsuranceCostResponseModel);
                }
            }
            if (ifSelectInsuranceCommercial) {
                TInsurance tInsurance = currentInsuranceMap.get(InsuranceEnum.COMMERCIAL.getKey());
                if (tInsurance != null && tInsurance.getUnpaidPremium().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    VehicleInsuranceCostResponseModel vehicleInsuranceCostResponseModel = new VehicleInsuranceCostResponseModel();
                    vehicleInsuranceCostResponseModel.setInsuranceId(tInsurance.getId());
                    vehicleInsuranceCostResponseModel.setInsuranceType(InsuranceEnum.COMMERCIAL.getKey());
                    vehicleInsuranceCostResponseModel.setInsuranceTypeLabel(InsuranceEnum.COMMERCIAL.getValue());
                    vehicleInsuranceCostResponseModel.setPolicyNo(tInsurance.getPolicyNo());
                    vehicleInsuranceCostResponseModel.setTotalCost(tInsurance.getPremium());
                    vehicleInsuranceCostResponseModel.setRemainNotDeductionCost(tInsurance.getUnpaidPremium());
                    vehicleInsuranceCostResponseModel.setUnPaidCost(tInsurance.getUnpaidPremium());
                    insuranceCostList.add(vehicleInsuranceCostResponseModel);
                }
            }
            if (ifSelectInsuranceCargo) {
                TInsurance tInsurance = currentInsuranceMap.get(InsuranceEnum.CARGO.getKey());
                if (tInsurance != null && tInsurance.getUnpaidPremium().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    VehicleInsuranceCostResponseModel vehicleInsuranceCostResponseModel = new VehicleInsuranceCostResponseModel();
                    vehicleInsuranceCostResponseModel.setInsuranceId(tInsurance.getId());
                    vehicleInsuranceCostResponseModel.setInsuranceType(InsuranceEnum.CARGO.getKey());
                    vehicleInsuranceCostResponseModel.setInsuranceTypeLabel(InsuranceEnum.CARGO.getValue());
                    vehicleInsuranceCostResponseModel.setPolicyNo(tInsurance.getPolicyNo());
                    vehicleInsuranceCostResponseModel.setTotalCost(tInsurance.getPremium());
                    vehicleInsuranceCostResponseModel.setRemainNotDeductionCost(tInsurance.getUnpaidPremium());
                    vehicleInsuranceCostResponseModel.setUnPaidCost(tInsurance.getUnpaidPremium());
                    insuranceCostList.add(vehicleInsuranceCostResponseModel);
                }
            }
            if (ifSelectInsuranceCarrier) {
                TInsurance tInsurance = currentInsuranceMap.get(InsuranceEnum.CARRIER.getKey());
                if (tInsurance != null && tInsurance.getUnpaidPremium().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    VehicleInsuranceCostResponseModel vehicleInsuranceCostResponseModel = new VehicleInsuranceCostResponseModel();
                    vehicleInsuranceCostResponseModel.setInsuranceId(tInsurance.getId());
                    vehicleInsuranceCostResponseModel.setInsuranceType(InsuranceEnum.CARRIER.getKey());
                    vehicleInsuranceCostResponseModel.setInsuranceTypeLabel(InsuranceEnum.CARRIER.getValue());
                    vehicleInsuranceCostResponseModel.setPolicyNo(tInsurance.getPolicyNo());
                    vehicleInsuranceCostResponseModel.setTotalCost(tInsurance.getPremium());
                    vehicleInsuranceCostResponseModel.setRemainNotDeductionCost(tInsurance.getUnpaidPremium());
                    vehicleInsuranceCostResponseModel.setUnPaidCost(tInsurance.getUnpaidPremium());
                    insuranceCostList.add(vehicleInsuranceCostResponseModel);
                }
            }
        }else{//已结算则查询结算的费用
            GetInsuranceCostsByVehicleIdResponseModel tInsuranceCosts = insuranceCostsMapper.getByIdForSettlement(insuranceCostId);
            if (tInsuranceCosts != null && ListUtils.isNotEmpty(tInsuranceCosts.getInsuranceIdList())) {
                //查询对应的保单信息
                List<TInsurance> insuranceList = insuranceMapper.getByIds(StringUtils.listToString(tInsuranceCosts.getInsuranceIdList(), ','));
                if (ListUtils.isNotEmpty(insuranceList)) {
                    VehicleInsuranceCostResponseModel vehicleInsuranceCostResponseModel;
                    for (TInsurance tInsurance : insuranceList) {
                        vehicleInsuranceCostResponseModel = new VehicleInsuranceCostResponseModel();
                        vehicleInsuranceCostResponseModel.setInsuranceId(tInsurance.getId());
                        vehicleInsuranceCostResponseModel.setInsuranceType(tInsurance.getInsuranceType());
                        vehicleInsuranceCostResponseModel.setInsuranceTypeLabel(InsuranceEnum.getEnum(tInsurance.getInsuranceType()).getValue());
                        vehicleInsuranceCostResponseModel.setPolicyNo(tInsurance.getPolicyNo());
                        vehicleInsuranceCostResponseModel.setTotalCost(tInsurance.getPremium());
                        if (InsuranceEnum.COMPULSORY.getKey().equals(tInsurance.getInsuranceType())){
                            vehicleInsuranceCostResponseModel.setTotalCost(tInsurance.getPremium().add(tInsurance.getPaymentOfVehicleAndVesselTax()));//交强险费用合计
                            vehicleInsuranceCostResponseModel.setPayCost(tInsuranceCosts.getPayCompulsoryInsuranceCost());
                            vehicleInsuranceCostResponseModel.setUnPaidCost(tInsuranceCosts.getCompulsoryInsuranceCost());
                            vehicleInsuranceCostResponseModel.setRemainNotDeductionCost(tInsuranceCosts.getCompulsoryInsuranceCost().add(tInsuranceCosts.getPayCompulsoryInsuranceCost()));
                        }else if (InsuranceEnum.COMMERCIAL.getKey().equals(tInsurance.getInsuranceType())){
                            vehicleInsuranceCostResponseModel.setPayCost(tInsuranceCosts.getPayCommercialInsuranceCost());
                            vehicleInsuranceCostResponseModel.setUnPaidCost(tInsuranceCosts.getCommercialInsuranceCost());
                            vehicleInsuranceCostResponseModel.setRemainNotDeductionCost(tInsuranceCosts.getCommercialInsuranceCost().add(tInsuranceCosts.getPayCommercialInsuranceCost()));
                        }else if (InsuranceEnum.CARGO.getKey().equals(tInsurance.getInsuranceType())){
                            vehicleInsuranceCostResponseModel.setPayCost(tInsuranceCosts.getPayCargoInsuranceCost());
                            vehicleInsuranceCostResponseModel.setUnPaidCost(tInsuranceCosts.getCargoInsuranceCost());
                            vehicleInsuranceCostResponseModel.setRemainNotDeductionCost(tInsuranceCosts.getCargoInsuranceCost().add(tInsuranceCosts.getPayCargoInsuranceCost()));
                        }else if (InsuranceEnum.CARRIER.getKey().equals(tInsurance.getInsuranceType())){
                            vehicleInsuranceCostResponseModel.setPayCost(tInsuranceCosts.getPayCarrierInsuranceCost());
                            vehicleInsuranceCostResponseModel.setUnPaidCost(tInsuranceCosts.getCarrierInsuranceCost());
                            vehicleInsuranceCostResponseModel.setRemainNotDeductionCost(tInsuranceCosts.getCarrierInsuranceCost().add(tInsuranceCosts.getPayCarrierInsuranceCost()));
                        }
                        //剩余未扣减费用大于0的才显示（等于0表示此保险费用已扣完，不需要再显示）
                        if (vehicleInsuranceCostResponseModel.getRemainNotDeductionCost() != null && vehicleInsuranceCostResponseModel.getRemainNotDeductionCost().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                            insuranceCostList.add(vehicleInsuranceCostResponseModel);
                        }
                    }
                }
            }
        }
        if (ListUtils.isNotEmpty(insuranceCostList)){
            Collections.sort(insuranceCostList, (o1,o2) -> o1.getInsuranceType().compareTo(o2.getInsuranceType()));
        }
        return insuranceCostList;
    }

    /**
     * 确认结算
     *
     * @param requestModel
     */
    @Transactional
    public void confirmSettlement(ConfirmSettlementRequestModel requestModel) {
        TVehicleSettlement tVehicleSettlement = vehicleSettlementMapper.selectByPrimaryKey(requestModel.getVehicleSettlementId());
        if (tVehicleSettlement == null || tVehicleSettlement.getValid().equals(CommonConstant.INTEGER_ZERO)) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }
        if (tVehicleSettlement.getSettlementMonth().equals(DateUtils.dateToString(new Date(), CommonConstant.DATE_TO_STRING_YM_PATTERN))) {
            throw new BizException(CarrierDataExceptionEnum.CURRENT_MONTH_NOT_SETTLEMENT);
        }
        if (!VehicleSettlementStatementStatusEnum.WAIT_SETTLE_STATEMENT.getKey().equals(tVehicleSettlement.getStatus())) {
            throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_SETTLESTATEMENT);
        }
        TVehicleSettlement firstNotSettlement = vehicleSettlementMapper.getFirstNotSettlementByVehicleId(tVehicleSettlement.getVehicleId());
        if (firstNotSettlement.getSettlementMonth().compareTo(tVehicleSettlement.getSettlementMonth()) < CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.FIRST_NOT_SETTLEMENT.getCode(), MessageFormat.format(CarrierDataExceptionEnum.FIRST_NOT_SETTLEMENT.getMsg(), firstNotSettlement.getSettlementMonth()));
        }
        List<TVehicleSettlementRelation> relationList = vehicleSettlementRelationMapper.getByVehicleSettlementId(tVehicleSettlement.getId());
        if (ListUtils.isEmpty(relationList)) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }
        List<Long> vehicleTireIdList = new ArrayList<>();
        List<Long> oilFilledIdList = new ArrayList<>();
        List<Long> gpsFeeIdList = new ArrayList<>();
        List<Long> parkingFeeIdList = new ArrayList<>();
        List<Long> loanRecordsIdList = new ArrayList<>();
        for (TVehicleSettlementRelation relation : relationList) {
            if (relation.getObjectType().equals(VehicleSettlementTypeEnum.GPS.getKey())) {
                gpsFeeIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.PARKING.getKey())) {
                parkingFeeIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.LOAN.getKey())) {
                loanRecordsIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.TIRE.getKey())) {
                vehicleTireIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.OIL.getKey()) ) {
                oilFilledIdList.add(relation.getObjectId());
            }
        }
        //车辆结算
        TVehicleSettlement vehicleSettlement = new TVehicleSettlement();
        vehicleSettlement.setId(requestModel.getVehicleSettlementId());
        vehicleSettlement.setStatus(VehicleSettlementStatementStatusEnum.WAIT_SEND.getKey());
        vehicleSettlement.setActualExpensesPayable(requestModel.getActualExpensesPayable());
        vehicleSettlement.setCarrierOrderCount(requestModel.getCarrierOrderCount());
        vehicleSettlement.setCarrierFreight(requestModel.getDispatchFreightFeeTotal());
        vehicleSettlement.setTireFee(requestModel.getTireCostTotal());
        vehicleSettlement.setGpsFee(requestModel.getGpsDeductingFee());
        vehicleSettlement.setParkingFee(requestModel.getParkingDeductingFee());
        vehicleSettlement.setLoanFee(requestModel.getLoanFee());
        vehicleSettlement.setOilFilledFee(requestModel.getOilFilledFeeTotal());
        vehicleSettlement.setVehicleClaimFee(requestModel.getVehicleClaimFee());
        vehicleSettlement.setInsuranceFee(requestModel.getInsuranceFee());
        vehicleSettlement.setDeductingFee(requestModel.getDeductingFeeTotal());
        vehicleSettlement.setRemainingFee(requestModel.getRemainingDeductingFeeTotal());
        vehicleSettlement.setAccidentInsuranceFee(requestModel.getAccidentInsuranceFee());
        vehicleSettlement.setAccidentInsuranceExpenseTotal(requestModel.getAccidentInsuranceExpenseTotal());
        vehicleSettlement.setAccidentInsuranceClaimFee(requestModel.getAccidentInsuranceClaimFee());
        vehicleSettlement.setRemark(requestModel.getRemark());
        vehicleSettlement.setWithdrawRemark("");
        vehicleSettlement.setIfAdjustFee(requestModel.getIfAdjustFee());
        vehicleSettlement.setAdjustRemark(requestModel.getAdjustRemark());
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getAdjustFeeSymbol())){
            vehicleSettlement.setAdjustFee(requestModel.getAdjustFee().negate());
        }else{
            vehicleSettlement.setAdjustFee(requestModel.getAdjustFee());
        }
        commonBiz.setBaseEntityModify(vehicleSettlement, BaseContextHandler.getUserName());

        String settlementMonth = tVehicleSettlement.getSettlementMonth();
        //gps结算记录
        GetGpsFeeByVehicleIdResponseModel tGpsFee = null;
        if (ListUtils.isNotEmpty(gpsFeeIdList)) {
            tGpsFee = gpsFeeMapper.getCurrentDeductingByIdForSettlement(gpsFeeIdList.get(CommonConstant.INTEGER_ZERO), settlementMonth);
        }
        TDeductingHistory gpsDeductingHistory;
        List<TDeductingHistory> deductingHistoryList = new ArrayList<>();
        TGpsFee gpsFee = null;
        if (tGpsFee != null && !tGpsFee.getStatus().equals(SettlementStatusEnum.FINISH.getKey())) {
            gpsDeductingHistory = new TDeductingHistory();
            gpsDeductingHistory.setObjectType(DeductingHistoryObjectTypeEnum.T_GPS_FEE.getKey());
            gpsDeductingHistory.setObjectId(tGpsFee.getGpsFeeId());
            gpsDeductingHistory.setDeductingMonth(settlementMonth);
            gpsDeductingHistory.setTotalFee(tGpsFee.getServiceFee().multiply(ConverterUtils.toBigDecimal(tGpsFee.getCooperationPeriod())).setScale(2, BigDecimal.ROUND_HALF_UP));
            if (tGpsFee.getRemainingDeductingFee() != null && tGpsFee.getRemainingDeductingFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                if (requestModel.getGpsDeductingFee().compareTo(tGpsFee.getRemainingDeductingFee()) > CommonConstant.INTEGER_ZERO) {
                    throw new BizException(CarrierDataExceptionEnum.GPS_DEDUCTING_FEE_MAX);
                }
                if (tGpsFee.getFinishDate() != null && settlementMonth.equals(DateUtils.dateToString(tGpsFee.getFinishDate(), CommonConstant.DATE_TO_STRING_YM_PATTERN))) {//最后一个月全部扣完
                    gpsDeductingHistory.setDeductingFee(tGpsFee.getRemainingDeductingFee());
                    gpsDeductingHistory.setRemainingDeductingFee(CommonConstant.BIG_DECIMAL_ZERO);
                } else {
                    gpsDeductingHistory.setDeductingFee(requestModel.getGpsDeductingFee());
                    gpsDeductingHistory.setRemainingDeductingFee(tGpsFee.getRemainingDeductingFee().subtract(requestModel.getGpsDeductingFee()));
                }
            } else {
                if (tGpsFee.getFinishDate() != null && settlementMonth.equals(DateUtils.dateToString(tGpsFee.getFinishDate(), CommonConstant.DATE_TO_STRING_YM_PATTERN))) {
                    gpsDeductingHistory.setDeductingFee(gpsDeductingHistory.getTotalFee());
                    gpsDeductingHistory.setRemainingDeductingFee(CommonConstant.BIG_DECIMAL_ZERO);
                } else {
                    gpsDeductingHistory.setDeductingFee(requestModel.getGpsDeductingFee());
                    gpsDeductingHistory.setRemainingDeductingFee(gpsDeductingHistory.getTotalFee().subtract(requestModel.getGpsDeductingFee()));
                }
            }
            commonBiz.setBaseEntityAdd(gpsDeductingHistory, BaseContextHandler.getUserName());
            deductingHistoryList.add(gpsDeductingHistory);

            Integer status;
            if (gpsDeductingHistory.getRemainingDeductingFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                status = SettlementStatusEnum.PART.getKey();
            } else {
                status = SettlementStatusEnum.FINISH.getKey();
            }
            if (!status.equals(tGpsFee.getStatus())) {
                gpsFee = new TGpsFee();
                gpsFee.setId(tGpsFee.getGpsFeeId());
                gpsFee.setStatus(status);
                commonBiz.setBaseEntityModify(gpsFee, BaseContextHandler.getUserName());
            }
        }

        //停车结算记录
        GetParkingFeeByVehicleIdResponseModel tParkingFee = null;
        if (ListUtils.isNotEmpty(parkingFeeIdList)) {
            tParkingFee = parkingFeeMapper.getCurrentDeductingByIdForSettlement(parkingFeeIdList.get(CommonConstant.INTEGER_ZERO), settlementMonth);
        }
        TDeductingHistory parkingDeductingHistory;
        TParkingFee parkingFee = null;
        if (tParkingFee != null && !tParkingFee.getStatus().equals(SettlementStatusEnum.FINISH.getKey())) {
            parkingDeductingHistory = new TDeductingHistory();
            parkingDeductingHistory.setObjectType(DeductingHistoryObjectTypeEnum.T_PARKING_FEE.getKey());
            parkingDeductingHistory.setObjectId(tParkingFee.getParkingFeeId());
            parkingDeductingHistory.setDeductingMonth(settlementMonth);
            parkingDeductingHistory.setTotalFee(tParkingFee.getParkingFee().multiply(ConverterUtils.toBigDecimal(tParkingFee.getCooperationPeriod())).setScale(2, BigDecimal.ROUND_HALF_UP));
            if (tParkingFee.getRemainingDeductingFee() != null && tParkingFee.getRemainingDeductingFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                if (requestModel.getParkingDeductingFee().compareTo(tParkingFee.getRemainingDeductingFee()) > CommonConstant.INTEGER_ZERO) {
                    throw new BizException(CarrierDataExceptionEnum.PARKING_DEDUCTING_FEE_MAX);
                }
                if (tParkingFee.getFinishDate() != null && settlementMonth.equals(DateUtils.dateToString(tParkingFee.getFinishDate(), CommonConstant.DATE_TO_STRING_YM_PATTERN))) {
                    parkingDeductingHistory.setDeductingFee(tParkingFee.getRemainingDeductingFee());
                    parkingDeductingHistory.setRemainingDeductingFee(CommonConstant.BIG_DECIMAL_ZERO);
                } else {
                    parkingDeductingHistory.setDeductingFee(requestModel.getParkingDeductingFee());
                    parkingDeductingHistory.setRemainingDeductingFee(tParkingFee.getRemainingDeductingFee().subtract(requestModel.getParkingDeductingFee()));
                }
            } else {
                if (tParkingFee.getFinishDate() != null && settlementMonth.equals(DateUtils.dateToString(tParkingFee.getFinishDate(), CommonConstant.DATE_TO_STRING_YM_PATTERN))) {
                    parkingDeductingHistory.setDeductingFee(parkingDeductingHistory.getTotalFee());
                    parkingDeductingHistory.setRemainingDeductingFee(CommonConstant.BIG_DECIMAL_ZERO);
                } else {
                    parkingDeductingHistory.setDeductingFee(requestModel.getParkingDeductingFee());
                    parkingDeductingHistory.setRemainingDeductingFee(parkingDeductingHistory.getTotalFee().subtract(requestModel.getParkingDeductingFee()));
                }
            }
            commonBiz.setBaseEntityAdd(parkingDeductingHistory, BaseContextHandler.getUserName());
            deductingHistoryList.add(parkingDeductingHistory);

            Integer status;
            if (parkingDeductingHistory.getRemainingDeductingFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                status = SettlementStatusEnum.PART.getKey();
            } else {
                status = SettlementStatusEnum.FINISH.getKey();
            }
            if (!status.equals(tParkingFee.getStatus())) {
                parkingFee = new TParkingFee();
                parkingFee.setId(tParkingFee.getParkingFeeId());
                parkingFee.setStatus(status);
                commonBiz.setBaseEntityModify(parkingFee, BaseContextHandler.getUserName());
            }
        }

        //贷款结算记录
        GetLoanFeeByVehicleIdResponseModel tLoanFee = null;
        if (ListUtils.isNotEmpty(loanRecordsIdList)) {
            tLoanFee = loanRecordsMapper.getSettlementRecordsByIdForSettlement(loanRecordsIdList.get(CommonConstant.INTEGER_ZERO), settlementMonth);
        }
        TLoanSettlementRecord loanSettlementRecord = null;
        TLoanRecords loanRecords = null;
        if (tLoanFee != null && !tLoanFee.getStatus().equals(SettlementStatusEnum.FINISH.getKey())) {
            loanSettlementRecord = new TLoanSettlementRecord();
            loanSettlementRecord.setLoanRecordsId(tLoanFee.getLoanRecordsId());
            loanSettlementRecord.setDeductingMonth(settlementMonth);
            loanSettlementRecord.setSettlementDate(new Date());
            loanSettlementRecord.setTotalFee(tLoanFee.getLoanFee());
            if (tLoanFee.getRemainingDeductingFee() != null && tLoanFee.getRemainingDeductingFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                if (requestModel.getLoanFee().compareTo(tLoanFee.getRemainingDeductingFee()) > CommonConstant.INTEGER_ZERO) {
                    throw new BizException(CarrierDataExceptionEnum.LOAN_DEDUCTING_FEE_MAX);
                }
                if (tLoanFee.getLoadFinishTime() != null && settlementMonth.equals(DateUtils.dateToString(tLoanFee.getLoadFinishTime(), CommonConstant.DATE_TO_STRING_YM_PATTERN))) {
                    loanSettlementRecord.setSettlementFee(tLoanFee.getRemainingDeductingFee());
                    loanSettlementRecord.setRemainingRepaymentFee(CommonConstant.BIG_DECIMAL_ZERO);
                } else {
                    loanSettlementRecord.setSettlementFee(requestModel.getLoanFee());
                    loanSettlementRecord.setRemainingRepaymentFee(tLoanFee.getRemainingDeductingFee().subtract(requestModel.getLoanFee()));
                }
            } else {
                if (tLoanFee.getLoadFinishTime() != null && settlementMonth.equals(DateUtils.dateToString(tLoanFee.getLoadFinishTime(), CommonConstant.DATE_TO_STRING_YM_PATTERN))) {
                    loanSettlementRecord.setSettlementFee(loanSettlementRecord.getTotalFee());
                    loanSettlementRecord.setRemainingRepaymentFee(CommonConstant.BIG_DECIMAL_ZERO);
                } else {
                    loanSettlementRecord.setSettlementFee(requestModel.getLoanFee());
                    loanSettlementRecord.setRemainingRepaymentFee(loanSettlementRecord.getTotalFee().subtract(requestModel.getLoanFee()));
                }
            }
            commonBiz.setBaseEntityAdd(loanSettlementRecord, BaseContextHandler.getUserName());

            Integer status;
            if (loanSettlementRecord.getRemainingRepaymentFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                status = SettlementStatusEnum.PART.getKey();
            } else {
                status = SettlementStatusEnum.FINISH.getKey();
            }
            if (!status.equals(tLoanFee.getStatus())) {
                loanRecords = new TLoanRecords();
                loanRecords.setId(tLoanFee.getLoanRecordsId());
                loanRecords.setStatus(status);
                commonBiz.setBaseEntityModify(loanRecords, BaseContextHandler.getUserName());
            }
        }

        //保险结算
        if (ListUtils.isNotEmpty(requestModel.getInsuranceCostList())) {
            insuranceCostSettlement(tVehicleSettlement, requestModel.getInsuranceCostList(), requestModel.getVehicleClaimFee());
        }

        TCertificationPictures newPicture = null;
        if (StringUtils.isNotBlank(requestModel.getAttachment())) {
            newPicture = new TCertificationPictures();
            newPicture.setObjectType(CertificationPicturesFileTypeEnum.VEHICLE_SETTLEMENT_ATTACHMENT.getObjectType().getObjectType());
            newPicture.setObjectId(tVehicleSettlement.getId());
            newPicture.setFileType(CertificationPicturesFileTypeEnum.VEHICLE_SETTLEMENT_ATTACHMENT.getFileType());
            newPicture.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.VEHICLE_SETTLEMENT_ATTACHMENT.getKey(), "", requestModel.getAttachment(), null));
            newPicture.setUploadUserName(BaseContextHandler.getUserName());
            newPicture.setUploadTime(new Date());
            newPicture.setFileTypeName(CertificationPicturesFileTypeEnum.VEHICLE_SETTLEMENT_ATTACHMENT.getFileName());
            newPicture.setFileName(CertificationPicturesFileTypeEnum.VEHICLE_SETTLEMENT_ATTACHMENT.getFileName());
            newPicture.setSuffix(requestModel.getAttachment().substring(requestModel.getAttachment().indexOf('.')));
            commonBiz.setBaseEntityAdd(newPicture, BaseContextHandler.getUserName());
        }

        vehicleSettlementMapper.updateByPrimaryKeySelective(vehicleSettlement);
        if (gpsFee != null) {
            gpsFeeMapper.updateByPrimaryKeySelective(gpsFee);
        }
        if (parkingFee != null) {
            parkingFeeMapper.updateByPrimaryKeySelective(parkingFee);
        }
        if (ListUtils.isNotEmpty(deductingHistoryList)) {
            deductingHistoryMapper.batchInsert(deductingHistoryList);
        }
        if (loanRecords != null) {
            loanRecordsMapper.updateByPrimaryKeySelective(loanRecords);
        }
        if (loanSettlementRecord != null) {
            loanSettlementRecordMapper.insertSelective(loanSettlementRecord);
        }
        if (ListUtils.isNotEmpty(oilFilledIdList)) {
            oilFilledMapper.settlementOilFilledByIds(StringUtils.listToString(oilFilledIdList, ','), BaseContextHandler.getUserName());
        }
        if (ListUtils.isNotEmpty(vehicleTireIdList)) {
            vehicleTireMapper.settlementOilFilledByIds(StringUtils.listToString(vehicleTireIdList, ','), BaseContextHandler.getUserName());
        }
        if (newPicture != null) {
            certificationPicturesMapper.insertSelective(newPicture);
        }
        insertEvent(VehicleSettlementEventsEnum.SETTLEMENT_STATEMENT,vehicleSettlement.getId());
    }
    //保险费用结算
    public void insuranceCostSettlement(TVehicleSettlement tVehicleSettlement, List<VehicleInsuranceCostRequestModel> insuranceCostList, BigDecimal insuranceClaimsCost) {
        //查询车辆
        VehicleBasicPropertyModel vehicleBasicPropertyById = vehicleBasicMapper.getVehicleBasicPropertyById(tVehicleSettlement.getVehicleId());
        //停运的车辆不行
        if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(vehicleBasicPropertyById.getOperatingState())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_OUTAGE_ERROR);
        }
        //非自有车辆不行
        if (!(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(vehicleBasicPropertyById.getVehicleProperty()) ||
                VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(vehicleBasicPropertyById.getVehicleProperty()))) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
        }
        //不是一体车或牵引车不行
        if (!VehicleCategoryEnum.TRACTOR.getKey().equals(vehicleBasicPropertyById.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(vehicleBasicPropertyById.getVehicleCategory())) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_ERROR);
        }

        List<Long> insuranceIdList = new ArrayList<>();
        Map<Long, Integer> requestTypeMap = new HashMap<>();
        for (VehicleInsuranceCostRequestModel model : insuranceCostList) {
            insuranceIdList.add(model.getInsuranceId());
            requestTypeMap.put(model.getInsuranceId(),model.getInsuranceType());
        }
        //判断保险信息，修改保险信息
        List<TInsurance> insuranceList = insuranceMapper.getByIds(StringUtils.listToString(insuranceIdList, ','));
        if (ListUtils.isEmpty(insuranceList)){
            throw new BizException(CarrierDataExceptionEnum.INSURANCE_CHANGE);
        }
        Date now = new Date();
        Map<Long,BigDecimal> unPaidCostMap = new HashMap<>();
        for (TInsurance tmp : insuranceList) {
            //不在保障中的保险不能结算，修改了险种和车牌号的不能结算
            if (tmp.getStatusType().equals(InsuranceStatusTypeEnum.CANCEL.getKey())
                    || tmp.getStatusType().equals(InsuranceStatusTypeEnum.REFUND.getKey())
                    || tmp.getStartTime().getTime() > now.getTime()
                    || tmp.getEndTime().getTime() < now.getTime()
                    || !tmp.getInsuranceType().equals(requestTypeMap.get(tmp.getId()))
                    || !tmp.getVehicleId().equals(tVehicleSettlement.getVehicleId())) {
                throw new BizException(CarrierDataExceptionEnum.INSURANCE_CHANGE);
            }
            unPaidCostMap.put(tmp.getId(),tmp.getUnpaidPremium());
        }

        //新增保险费用
        TInsuranceCosts tInsuranceCosts = new TInsuranceCosts();
        tInsuranceCosts.setStatus(OilFilledStatusEnum.HAVE_SETTLE.getKey());
        tInsuranceCosts.setVehicleId(tVehicleSettlement.getVehicleId());
        tInsuranceCosts.setVehicleNo(tVehicleSettlement.getVehicleNo());
        tInsuranceCosts.setSettlementMonth(tVehicleSettlement.getSettlementMonth());
        tInsuranceCosts.setInsuranceClaimsCost(insuranceClaimsCost);
        tInsuranceCosts.setInsuranceClaimsTime(new Date());
        commonBiz.setBaseEntityAdd(tInsuranceCosts,BaseContextHandler.getUserName());

        BigDecimal unPaidCost;
        for (VehicleInsuranceCostRequestModel model : insuranceCostList) {
            unPaidCost = unPaidCostMap.get(model.getInsuranceId()).subtract(model.getPayCost());
            if (InsuranceEnum.COMMERCIAL.getKey().equals(model.getInsuranceType())){
                tInsuranceCosts.setCommercialInsuranceCost(unPaidCost);
                tInsuranceCosts.setPayCommercialInsuranceCost(model.getPayCost());
            }else if (InsuranceEnum.COMPULSORY.getKey().equals(model.getInsuranceType())){
                tInsuranceCosts.setCompulsoryInsuranceCost(unPaidCost);
                tInsuranceCosts.setPayCompulsoryInsuranceCost(model.getPayCost());
            }else if (InsuranceEnum.CARGO.getKey().equals(model.getInsuranceType())){
                tInsuranceCosts.setCargoInsuranceCost(unPaidCost);
                tInsuranceCosts.setPayCargoInsuranceCost(model.getPayCost());
            }else if (InsuranceEnum.CARRIER.getKey().equals(model.getInsuranceType())){
                tInsuranceCosts.setCarrierInsuranceCost(unPaidCost);
                tInsuranceCosts.setPayCarrierInsuranceCost(model.getPayCost());
            }
        }


        List<TInsurance> upInsuranceList = new ArrayList<>();
        TInsurance insurance;
        boolean flag;
        for (TInsurance tmp : insuranceList) {
            flag = false;
            insurance = new TInsurance();
            insurance.setId(tmp.getId());
            if (InsuranceEnum.COMMERCIAL.getKey().equals(tmp.getInsuranceType())) {
                if (tInsuranceCosts.getPayCommercialInsuranceCost().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
                    flag = true;
                }
                insurance.setUnpaidPremium(tmp.getUnpaidPremium().subtract(tInsuranceCosts.getPayCommercialInsuranceCost()));
            } else if (InsuranceEnum.COMPULSORY.getKey().equals(tmp.getInsuranceType())) {
                if (tInsuranceCosts.getPayCompulsoryInsuranceCost().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
                    flag = true;
                }
                insurance.setUnpaidPremium(tmp.getUnpaidPremium().subtract(tInsuranceCosts.getPayCompulsoryInsuranceCost()));
            } else if (InsuranceEnum.CARGO.getKey().equals(tmp.getInsuranceType())) {
                if (tInsuranceCosts.getPayCargoInsuranceCost().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
                    flag = true;
                }
                insurance.setUnpaidPremium(tmp.getUnpaidPremium().subtract(tInsuranceCosts.getPayCargoInsuranceCost()));
            } else if (InsuranceEnum.CARRIER.getKey().equals(tmp.getInsuranceType())) {
                if (tInsuranceCosts.getPayCarrierInsuranceCost().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
                    flag = true;
                }
                insurance.setUnpaidPremium(tmp.getUnpaidPremium().subtract(tInsuranceCosts.getPayCarrierInsuranceCost()));
            } else {
                continue;
            }
            if (insurance.getUnpaidPremium().compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO) {//扣除费用大于保险费用，则抛异常（有人操作修改了保险费用）
                throw new BizException(CarrierDataExceptionEnum.INSURANCE_CHANGE);
            }else if (insurance.getUnpaidPremium().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {//保险费用未扣完则置为部分结算状态
                insurance.setSettlementStatus(SettlementStatusEnum.PART.getKey());
            } else {
                insurance.setSettlementStatus(SettlementStatusEnum.FINISH.getKey());
            }
            commonBiz.setBaseEntityModify(insurance, BaseContextHandler.getUserName());
            if (flag) {//扣减了费用才修改
                upInsuranceList.add(insurance);
            }
        }

        //新增保险费用
        insuranceCostsMapper.insertSelective(tInsuranceCosts);

        //新增保险与保险费用关系
        TInsuranceCostsRelation tInsuranceCostsRelation;
        List<TInsuranceCostsRelation> relationList = new ArrayList<>();
        for (Long insuranceId : insuranceIdList) {
            tInsuranceCostsRelation = new TInsuranceCostsRelation();
            tInsuranceCostsRelation.setInsuranceCostsId(tInsuranceCosts.getId());
            tInsuranceCostsRelation.setInsuranceId(insuranceId);
            commonBiz.setBaseEntityAdd(tInsuranceCostsRelation,BaseContextHandler.getUserName());
            relationList.add(tInsuranceCostsRelation);
        }
        insuranceCostsRelationMapper.batchInsert(relationList);

        //新增自有车辆结算关系（保险费用关联到自有车辆结算）
        TVehicleSettlementRelation tVehicleSettlementRelation = new TVehicleSettlementRelation();
        tVehicleSettlementRelation.setVehicleSettlementId(tVehicleSettlement.getId());
        tVehicleSettlementRelation.setObjectType(VehicleSettlementTypeEnum.INSURANCE.getKey());
        tVehicleSettlementRelation.setObjectId(tInsuranceCosts.getId());
        commonBiz.setBaseEntityAdd(tVehicleSettlementRelation,BaseContextHandler.getUserName());
        vehicleSettlementRelationMapper.insertSelective(tVehicleSettlementRelation);

        //修改保险信息
        if (ListUtils.isNotEmpty(upInsuranceList)) {
            insuranceMapper.batchUpdate(upInsuranceList);
        }
    }

    /**
     * 车辆结算看板
     *
     * @param requestModel
     * @return
     */
    public List<VehicleSettlementKanBanResponseModel> vehicleSettlementKanBan(VehicleSettlementKanBanRequestModel requestModel) {
        String settlementYear = requestModel.getSettlementYear();
        List<VehicleSettlementKanBanResponseModel> vehicleSettlementList = vehicleSettlementMapper.vehicleSettlementKanBan(settlementYear);
        List<String> existMonthList = new ArrayList<>();
        for (VehicleSettlementKanBanResponseModel tmp : vehicleSettlementList) {
            tmp.setVehicleCount(tmp.getVehicleSettlementList().size());
            tmp.setWaitSettlementCount(CommonConstant.INTEGER_ZERO);
            tmp.setWaitSendCount(CommonConstant.INTEGER_ZERO);
            tmp.setWaitCommitCount(CommonConstant.INTEGER_ZERO);
            tmp.setWaitHandleCount(CommonConstant.INTEGER_ZERO);
            tmp.setPartPaySettlementCount(CommonConstant.INTEGER_ZERO);
            tmp.setWaitPaySettlementCount(CommonConstant.INTEGER_ZERO);
            tmp.setPayedSettlementCount(CommonConstant.INTEGER_ZERO);
            for(VehicleSettlementKanBanModel model:tmp.getVehicleSettlementList()){
                if(VehicleSettlementStatementStatusEnum.WAIT_SETTLE_STATEMENT.getKey().equals(model.getStatus())){
                    tmp.setWaitSettlementCount(Optional.ofNullable(tmp.getWaitSettlementCount()).orElse(CommonConstant.INTEGER_ZERO)+1);
                }else if(VehicleSettlementStatementStatusEnum.WAIT_SEND.getKey().equals(model.getStatus())){
                    tmp.setWaitSendCount(Optional.ofNullable(tmp.getWaitSendCount()).orElse(CommonConstant.INTEGER_ZERO)+1);
                }else if(VehicleSettlementStatementStatusEnum.WAIT_CONFIRM.getKey().equals(model.getStatus())){
                    tmp.setWaitCommitCount(Optional.ofNullable(tmp.getWaitCommitCount()).orElse(CommonConstant.INTEGER_ZERO)+1);
                }else if(VehicleSettlementStatementStatusEnum.WAIT_HANDLE.getKey().equals(model.getStatus())){
                    tmp.setWaitHandleCount(Optional.ofNullable(tmp.getWaitHandleCount()).orElse(CommonConstant.INTEGER_ZERO)+1);
                }else if(VehicleSettlementStatementStatusEnum.WAIT_SETTLE.getKey().equals(model.getStatus())){
                    tmp.setWaitPaySettlementCount(Optional.ofNullable(tmp.getWaitPaySettlementCount()).orElse(CommonConstant.INTEGER_ZERO)+1);
                }else if(VehicleSettlementStatementStatusEnum.PART_OF_SETTLE.getKey().equals(model.getStatus())){
                    tmp.setPartPaySettlementCount(Optional.ofNullable(tmp.getPartPaySettlementCount()).orElse(CommonConstant.INTEGER_ZERO)+1);
                }else if(VehicleSettlementStatementStatusEnum.SETTLED.getKey().equals(model.getStatus())){
                    tmp.setPayedSettlementCount(Optional.ofNullable(tmp.getPayedSettlementCount()).orElse(CommonConstant.INTEGER_ZERO)+1);
                }
            }

            if (tmp.getSettlementMonth().substring(tmp.getSettlementMonth().length() - 2).compareTo(CommonConstant.OCTOBER) < CommonConstant.INTEGER_ZERO) {
                tmp.setMonth(tmp.getSettlementMonth().substring(tmp.getSettlementMonth().length() - 1));
            } else {
                tmp.setMonth(tmp.getSettlementMonth().substring(tmp.getSettlementMonth().length() - 2));
            }

            existMonthList.add(tmp.getSettlementMonth());
        }
        String currentYear = DateUtils.dateToString(new Date(), CommonConstant.YYYY_FORMAT);
        List<String> monthList = Arrays.asList(settlementYear + "-" + CommonConstant.JANUARY, settlementYear + "-" + CommonConstant.FEBRUARY, settlementYear + "-" + CommonConstant.MARCH,
                settlementYear + "-" + CommonConstant.APRIL, settlementYear + "-" + CommonConstant.MAY, settlementYear + "-" + CommonConstant.JUNE,
                settlementYear + "-" + CommonConstant.JULY, settlementYear + "-" + CommonConstant.AUGUST, settlementYear + "-" + CommonConstant.SEPTEMBER,
                settlementYear + "-" + CommonConstant.OCTOBER, settlementYear + "-" + CommonConstant.NOVEMBER, settlementYear + "-" + CommonConstant.DECEMBER);
        if (currentYear.equals(settlementYear)) {//当前年
            String settlementMonth = DateUtils.dateToString(new Date(), CommonConstant.DATE_TO_STRING_YM_PATTERN);
            monthList = monthList.stream().filter(month -> month.compareTo(settlementMonth) <= CommonConstant.INTEGER_ZERO).collect(Collectors.toList());
        }
        monthList = new ArrayList<>(monthList);
        monthList.removeAll(existMonthList);
        if (ListUtils.isNotEmpty(monthList)) {//有月份没有结算数据，则补空数据
            VehicleSettlementKanBanResponseModel model;
            List<VehicleSettlementKanBanResponseModel> notExistVehicleSettlementList = new ArrayList<>();
            for (String month : monthList) {
                model = new VehicleSettlementKanBanResponseModel();
                model.setSettlementMonth(month);
                model.setVehicleCount(CommonConstant.INTEGER_ZERO);
                model.setWaitSettlementCount(CommonConstant.INTEGER_ZERO);
                model.setWaitSendCount(CommonConstant.INTEGER_ZERO);
                model.setWaitHandleCount(CommonConstant.INTEGER_ZERO);
                model.setWaitCommitCount(CommonConstant.INTEGER_ZERO);
                model.setWaitPaySettlementCount(CommonConstant.INTEGER_ZERO);
                model.setPartPaySettlementCount(CommonConstant.INTEGER_ZERO);
                model.setPayedSettlementCount(CommonConstant.INTEGER_ZERO);
                if (month.substring(month.length() - 2).compareTo(CommonConstant.OCTOBER) < CommonConstant.INTEGER_ZERO) {
                    model.setMonth(month.substring(month.length() - 1));
                } else {
                    model.setMonth(month.substring(month.length() - 2));
                }
                notExistVehicleSettlementList.add(model);
            }
            vehicleSettlementList.addAll(notExistVehicleSettlementList);
        }
        //排序
        Collections.sort(vehicleSettlementList, (o1, o2) -> o2.getSettlementMonth().compareTo(o1.getSettlementMonth()));
        return vehicleSettlementList;
    }

    /**
     * 车辆结算-发送司机-查询司机
     * @param requestModel
     * @return
     */
    public List<GetSettlementDriverResponseModel> getDriver(GetSettlementDriverRequestModel requestModel){
        //查询所有内部启用司机
        return tStaffBasicMapper.getDriverInfoForVehicleSettlement(requestModel.getDriverName());
    }


    /**
     * 发送司机-确认
     * @param requestModel
     */
    @Transactional
    public void confirmSendToDriver(ConfirmSendToDriverRequestModel requestModel){

        List<ConfirmSendToDriverItemModel> itemList = requestModel.getItemList();
        List<Long> ids = new ArrayList<>();
        List<Long> driverIds = new ArrayList<>();


        itemList.forEach(e->{
            if(e.getDriverId() == null || e.getVehicleSettlementId() == null) {
                throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
            }
            driverIds.add(e.getDriverId());
            ids.add(e.getVehicleSettlementId());
        });
        List<TVehicleSettlement> vehicleSettlementByIds = vehicleSettlementMapper.getVehicleSettlementByIds(StringUtils.listToString(ids, ','));
        List<TVehicleSettlement> collect = vehicleSettlementByIds.stream().filter(e -> !VehicleSettlementStatementStatusEnum.WAIT_SEND.getKey().equals(e.getStatus())).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(collect)) {
            throw new BizException(CarrierDataExceptionEnum.SEND_DRIVER_VEHICLE_NUMBER_MONTH_ERROR.getCode(), MessageFormat.format(CarrierDataExceptionEnum.SEND_DRIVER_VEHICLE_NUMBER_MONTH_ERROR.getMsg(),
                            collect.get(CommonConstant.INTEGER_ZERO).getVehicleNo(),collect.get(CommonConstant.INTEGER_ZERO).getSettlementMonth()));
        }

        List<TStaffBasic> staffByIds = tStaffBasicMapper.getStaffByIds(StringUtils.listToString(driverIds, ','));
        Map<Long, TStaffBasic> staffMap = staffByIds.stream().collect(Collectors.toMap(TStaffBasic::getId, Function.identity(), (key1, key2) -> key2));


        List<TVehicleSettlementDriverRelation> insertDriverRelationList = new ArrayList<>();
        itemList.forEach(e->{
            TVehicleSettlementDriverRelation tVehicleSettlementDriverRelation = new TVehicleSettlementDriverRelation();
            tVehicleSettlementDriverRelation.setVehicleSettlementId(e.getVehicleSettlementId());
            tVehicleSettlementDriverRelation.setDriverId(e.getDriverId());
            if (!MapUtils.isEmpty(staffMap)){
                tVehicleSettlementDriverRelation.setDriverName(staffMap.get(e.getDriverId()).getName());
                tVehicleSettlementDriverRelation.setDriverMobile(staffMap.get(e.getDriverId()).getMobile());
            }
            commonBiz.setBaseEntityAdd(tVehicleSettlementDriverRelation,BaseContextHandler.getUserName());
            insertDriverRelationList.add(tVehicleSettlementDriverRelation);
        });

        List<TVehicleSettlement> updateVehicleSettlementList = new ArrayList<>();
        ids.forEach(e->{
            TVehicleSettlement tVehicleSettlement = new TVehicleSettlement();
            tVehicleSettlement.setId(e);
            tVehicleSettlement.setStatus(VehicleSettlementStatementStatusEnum.WAIT_CONFIRM.getKey());
            commonBiz.setBaseEntityModify(tVehicleSettlement,BaseContextHandler.getUserName());
            updateVehicleSettlementList.add(tVehicleSettlement);
        });

        if (ListUtils.isNotEmpty(insertDriverRelationList)){
            tVehicleSettlementDriverRelationMapper.batchInsertSelective(insertDriverRelationList);
        }

        if (ListUtils.isNotEmpty(updateVehicleSettlementList)){
            vehicleSettlementMapper.batchUpdateSelective(updateVehicleSettlementList);
        }

    }

    /**
     * 车辆结算列表-账单记录-查看
     * @param requestModel
     * @return
     */
    public SettlementStatementRecordResponseModel settlementStatementRecord(VehicleSettlementIdRequestModel requestModel){
        TVehicleSettlement tVehicleSettlement = vehicleSettlementMapper.selectByPrimaryKey(requestModel.getVehicleSettlementId());
        if (tVehicleSettlement == null ||   IfValidEnum.INVALID.getKey().equals(tVehicleSettlement.getValid())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLE_STATEMENT_ERROR);
        }
        return vehicleSettlementMapper.getSettlementStatementRecordList(requestModel.getVehicleSettlementId());
    }

    /**
     * 定时任务：生成车辆结算数据（贷款、停车费、GPS费、保险费未结清）（每月1号凌晨1点）
     */
    @Transactional
    public void generateVehicleSettlement() {
        //获取上个月
        String lastMoth = DateUtils.dateToString(DateUtils.add(new Date(), Calendar.MONTH, -1), CommonConstant.DATE_TO_STRING_YM_PATTERN);
        //获取我司id
        Long qiyaCompanyCarrierId = commonBiz.getQiyaCompanyCarrierId();
        //查询上个月产生费用的车辆
        List<GetVehicleBySettlementMonthModel> dbCarrierOrderVehicleList = carrierOrderMapper.getVehicleBySettlementMonth(lastMoth, qiyaCompanyCarrierId);
        List<GetVehicleBySettlementMonthModel> loanVehicleList = loanRecordsMapper.getVehicleBySettlementMonth(lastMoth);
        List<GetVehicleBySettlementMonthModel> gpsVehicleList = gpsFeeMapper.getVehicleBySettlementMonth(lastMoth);
        List<GetVehicleBySettlementMonthModel> parkVehicleList = parkingFeeMapper.getVehicleBySettlementMonth(lastMoth);
        List<GetVehicleBySettlementMonthModel> oilVehicleList = oilFilledMapper.getVehicleBySettlementMonth(lastMoth);

        //查询已经关联结算费用的运单id（关联运单由调度时间变为签收时间，历史结算数据可能存在未签收但已关联的运单）
        List<Long> existCarrierOrderIdList = vehicleSettlementRelationMapper.getObjectIdByObjectType(VehicleSettlementTypeEnum.CARRIER_ORDER.getKey());

        //聚合可以关联车辆结算的运单的车辆id
        List<Long> carrierOrderVehicleIdList = new ArrayList<>();
        List<GetVehicleBySettlementMonthModel> carrierOrderVehicleList = new ArrayList<>();
        if (ListUtils.isNotEmpty(dbCarrierOrderVehicleList)) {
            for (GetVehicleBySettlementMonthModel tCarrierOrder : dbCarrierOrderVehicleList) {
                if (!existCarrierOrderIdList.contains(tCarrierOrder.getObjectId())) {
                    carrierOrderVehicleIdList.add(tCarrierOrder.getVehicleId());
                    carrierOrderVehicleList.add(tCarrierOrder);
                }
            }
        }
        //聚合可以关联车辆结算的贷款的车辆id
        List<Long> loanVehicleIdList = loanVehicleList.stream().map(GetVehicleBySettlementMonthModel::getVehicleId).collect(Collectors.toList());
        //聚合可以关联车辆结算的gps的车辆id
        List<Long> gpsVehicleIdList = gpsVehicleList.stream().map(GetVehicleBySettlementMonthModel::getVehicleId).collect(Collectors.toList());
        //聚合可以关联车辆结算的停车费用的车辆id
        List<Long> parkVehicleIdList = parkVehicleList.stream().map(GetVehicleBySettlementMonthModel::getVehicleId).collect(Collectors.toList());
        //聚合可以关联车辆结算的充油的车辆id
        List<Long> oilVehicleIdList = oilVehicleList.stream().map(GetVehicleBySettlementMonthModel::getVehicleId).collect(Collectors.toList());

        List<Long> vehicleIdList = new ArrayList<>();
        vehicleIdList.addAll(carrierOrderVehicleIdList);
        vehicleIdList.addAll(loanVehicleIdList);
        vehicleIdList.addAll(gpsVehicleIdList);
        vehicleIdList.addAll(parkVehicleIdList);
        vehicleIdList.addAll(oilVehicleIdList);
        vehicleIdList = vehicleIdList.stream().distinct().collect(Collectors.toList());

        if (ListUtils.isNotEmpty(vehicleIdList)) {//产生费用的车辆
            List<Long> existList = vehicleSettlementMapper.getVehicleByMonth(lastMoth);
            if (ListUtils.isNotEmpty(existList)) {
                vehicleIdList.removeAll(existList);
            }
            if (ListUtils.isNotEmpty(vehicleIdList)) {//需要生成车辆结算数据的车辆
                List<VehicleBasicPropertyModel> vehicleList = vehicleBasicMapper.getVehicleNoByIds(StringUtils.listToString(vehicleIdList, ','));
                TVehicleSettlement vehicleSettlement;
                List<TVehicleSettlementRelation> addRelationList = new ArrayList<>();
                for (VehicleBasicPropertyModel model : vehicleList) {
                    //生成车辆结算数据
                    vehicleSettlement = new TVehicleSettlement();
                    vehicleSettlement.setVehicleId(model.getVehicleId());
                    vehicleSettlement.setVehicleNo(model.getVehicleNo());
                    vehicleSettlement.setVehicleProperty(model.getVehicleProperty());
                    vehicleSettlement.setSettlementMonth(lastMoth);
                    commonBiz.setBaseEntityAdd(vehicleSettlement, CommonConstant.TIMING_TASK);
                    vehicleSettlementMapper.insertSelective(vehicleSettlement);

                    //生成车辆结算gps关系数据
                    createRelation(addRelationList, gpsVehicleList, model.getVehicleId(), vehicleSettlement.getId(), VehicleSettlementTypeEnum.GPS);
                    //生成车辆结算停车费用关系数据
                    createRelation(addRelationList, parkVehicleList, model.getVehicleId(), vehicleSettlement.getId(), VehicleSettlementTypeEnum.PARKING);
                    //生成车辆结算贷款关系数据
                    createRelation(addRelationList, loanVehicleList, model.getVehicleId(), vehicleSettlement.getId(), VehicleSettlementTypeEnum.LOAN);
                    //生成车辆结算充油关系数据
                    createRelation(addRelationList, oilVehicleList, model.getVehicleId(), vehicleSettlement.getId(), VehicleSettlementTypeEnum.OIL);
                    //生成车辆结算运单关系数据
                    createRelation(addRelationList, carrierOrderVehicleList, model.getVehicleId(), vehicleSettlement.getId(), VehicleSettlementTypeEnum.CARRIER_ORDER);
                }
                //关系数据落库
                if (ListUtils.isNotEmpty(addRelationList)) {
                    vehicleSettlementRelationMapper.batchInsert(addRelationList);
                }
            }
        }
    }

    //生成车辆结算关系数据
    public void createRelation(List<TVehicleSettlementRelation> addRelationList, List<GetVehicleBySettlementMonthModel> vehicleList, Long vehicleId, Long vehicleSettlementId, VehicleSettlementTypeEnum typeEnum) {
        if (ListUtils.isNotEmpty(vehicleList)) {
            for (GetVehicleBySettlementMonthModel vehicle : vehicleList) {
                if (vehicleId.equals(vehicle.getVehicleId())) {
                    TVehicleSettlementRelation vehicleSettlementRelation = new TVehicleSettlementRelation();
                    vehicleSettlementRelation.setVehicleSettlementId(vehicleSettlementId);
                    vehicleSettlementRelation.setObjectType(typeEnum.getKey());
                    vehicleSettlementRelation.setObjectId(vehicle.getObjectId());
                    commonBiz.setBaseEntityAdd(vehicleSettlementRelation, CommonConstant.TIMING_TASK);
                    addRelationList.add(vehicleSettlementRelation);
                }
            }
        }
    }

    /**
     * 定时任务：更新停车费用、gps费用的合作状态（每天凌晨1点）
     */
    @Transactional
    public void updateParkingGpsFeeCooperationStatus() {
        //gps费用
        List<TGpsFee> gpsFeeList = gpsFeeMapper.getNotTerminal();
        List<TGpsFee> upGpsList = new ArrayList<>();
        if (ListUtils.isNotEmpty(gpsFeeList)) {
            Integer cooperationStatus;
            Date now = new Date();
            TGpsFee gpsFee;
            for (TGpsFee gps : gpsFeeList) {
                cooperationStatus = getCooperationStatus(now, gps.getStartDate(), gps.getFinishDate());
                if (cooperationStatus != null && !cooperationStatus.equals(gps.getCooperationStatus())) {
                    gpsFee = new TGpsFee();
                    gpsFee.setId(gps.getId());
                    gpsFee.setCooperationStatus(cooperationStatus);
                    commonBiz.setBaseEntityModify(gpsFee, CommonConstant.TIMING_TASK);
                    upGpsList.add(gpsFee);
                }
            }
        }
        //停车费用
        List<TParkingFee> parkingFeeList = parkingFeeMapper.getNotTerminal();
        List<TParkingFee> upParkingList = new ArrayList<>();
        if (ListUtils.isNotEmpty(parkingFeeList)) {
            Integer cooperationStatus;
            Date now = new Date();
            TParkingFee parkingFee;
            for (TParkingFee park : parkingFeeList) {
                cooperationStatus = getCooperationStatus(now, park.getStartDate(), park.getFinishDate());
                if (cooperationStatus != null && !cooperationStatus.equals(park.getCooperationStatus())) {
                    parkingFee = new TParkingFee();
                    parkingFee.setId(park.getId());
                    parkingFee.setCooperationStatus(cooperationStatus);
                    commonBiz.setBaseEntityModify(parkingFee, CommonConstant.TIMING_TASK);
                    upParkingList.add(parkingFee);
                }
            }
        }
        //更新合作状态
        if (ListUtils.isNotEmpty(upGpsList)) {
            gpsFeeMapper.batchUpdate(upGpsList);
        }
        if (ListUtils.isNotEmpty(upParkingList)) {
            parkingFeeMapper.batchUpdate(upParkingList);
        }
    }

    public Integer getCooperationStatus(Date now, Date startDate, Date endDate) {
        SimpleDateFormat nowSdf = new SimpleDateFormat(DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        String nowStr = nowSdf.format(now);
        Date nowTime = null;
        try {
            nowTime = nowSdf.parse(nowStr);
        } catch (ParseException e) {
            log.info(e.getMessage(), e);
        }
        Integer cooperationStatus = null;
        if (nowTime != null) {
            if (startDate.getTime() > nowTime.getTime()) {
                cooperationStatus = CooperationStatusEnum.NO_START.getKey();
            } else if (nowTime.getTime() > endDate.getTime()) {
                cooperationStatus = CooperationStatusEnum.TERMINAL.getKey();
            } else {
                cooperationStatus = CooperationStatusEnum.VALID.getKey();
            }
        }
        return cooperationStatus;
    }

    /**
     * 无需确认/撤回 详情
     *
     * @param  requestModel
     */
    public CancelVehicleSettlementDetailResponseModel cancelVehicleSettlementDetail(CancelVehicleSettlementDetailRequestModel requestModel) {
        if(CommonConstant.INTEGER_TWO.equals(requestModel.getOperatorType())){
            TVehicleSettlement tVehicleSettlement = vehicleSettlementMapper.selectByPrimaryKey(requestModel.getVehicleSettlementId());
            if(tVehicleSettlement==null || IfValidEnum.INVALID.getKey().equals(tVehicleSettlement.getValid())){
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
            }
            if (VehicleSettlementStatementStatusEnum.WAIT_SETTLE_STATEMENT.getKey().equals(tVehicleSettlement.getStatus()) ||
                    VehicleSettlementStatementStatusEnum.WAIT_SEND.getKey().equals(tVehicleSettlement.getStatus())){
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLE_STATEMENT_ERROR);
            }
        }
        CancelVehicleSettlementDetailResponseModel responseModel=vehicleSettlementMapper.getCancelVehicleSettlementDetailById(requestModel.getVehicleSettlementId());
        if(responseModel==null ){
            responseModel=new CancelVehicleSettlementDetailResponseModel();
        }
        return responseModel;
    }

    /**
     * 无需确认/撤回
     *
     * @param  requestModel
     */
    @Transactional
    public void cancelVehicleSettlement(CancelVehicleSettlementRequestModel requestModel) {
        TVehicleSettlement dbVehicleSettlement = vehicleSettlementMapper.selectByPrimaryKey(requestModel.getVehicleSettlementId());
        if(dbVehicleSettlement==null || IfValidEnum.INVALID.getKey().equals(dbVehicleSettlement.getValid())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }
        if(CommonConstant.INTEGER_ONE.equals(requestModel.getOperatorType()) && !VehicleSettlementStatementStatusEnum.WAIT_CONFIRM.getKey().equals(dbVehicleSettlement.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_CONFIRM);
        }
        if(CommonConstant.INTEGER_TWO.equals(requestModel.getOperatorType())
                && !(VehicleSettlementStatementStatusEnum.WAIT_SEND.getKey().equals(dbVehicleSettlement.getStatus())
                        || VehicleSettlementStatementStatusEnum.WAIT_CONFIRM.getKey().equals(dbVehicleSettlement.getStatus()))){
            throw new BizException(CarrierDataExceptionEnum.ONLY_WAIT_CONFIRM);
        }
        TVehicleSettlement updateVehicleSettlement=new TVehicleSettlement();
        updateVehicleSettlement.setId(dbVehicleSettlement.getId());
        if(CommonConstant.INTEGER_ONE.equals(requestModel.getOperatorType())){
            updateVehicleSettlement.setStatus(VehicleSettlementStatementStatusEnum.WAIT_SETTLE.getKey());
        }else if(CommonConstant.INTEGER_TWO.equals(requestModel.getOperatorType())){
            if(VehicleSettlementStatementStatusEnum.WAIT_CONFIRM.getKey().equals(dbVehicleSettlement.getStatus())) {
                //待确认->待发送
                updateVehicleSettlement.setStatus(VehicleSettlementStatementStatusEnum.WAIT_SEND.getKey());
            }else if(VehicleSettlementStatementStatusEnum.WAIT_SEND.getKey().equals(dbVehicleSettlement.getStatus())){
                //待发送->待对账
                updateVehicleSettlement.setStatus(VehicleSettlementStatementStatusEnum.WAIT_SETTLE_STATEMENT.getKey());
                backToWaitSettleStatement(updateVehicleSettlement,dbVehicleSettlement.getSettlementMonth());
            }
            updateVehicleSettlement.setWithdrawRemark(requestModel.getReason());
        }
        commonBiz.setBaseEntityModify(updateVehicleSettlement,BaseContextHandler.getUserName());
        vehicleSettlementMapper.updateByPrimaryKeySelective(updateVehicleSettlement);

        TVehicleSettlementDriverRelation relation = tVehicleSettlementDriverRelationMapper.getByVehicleSettlementId(dbVehicleSettlement.getId());
        if (relation!=null){
            TVehicleSettlementDriverRelation upRelation = new TVehicleSettlementDriverRelation();
            upRelation.setId(relation.getId());
            if(CommonConstant.INTEGER_ONE.equals(requestModel.getOperatorType())){
                upRelation.setConfirmTime(new Date());
                upRelation.setStatus(CommonConstant.INTEGER_ZERO);
                upRelation.setReason(requestModel.getReason());
            }else if(CommonConstant.INTEGER_TWO.equals(requestModel.getOperatorType())){
                upRelation.setValid(IfValidEnum.INVALID.getKey());
            }
            commonBiz.setBaseEntityModify(upRelation, BaseContextHandler.getUserName());
            tVehicleSettlementDriverRelationMapper.updateByPrimaryKeySelective(upRelation);
        }
        if(CommonConstant.INTEGER_ONE.equals(requestModel.getOperatorType())) {
            insertEvent(VehicleSettlementEventsEnum.CONFIRM,dbVehicleSettlement.getId());
        }
    }
    /**
     *  账单回退至待对账状态,数据清空，相关项目的费用、状态回退
     *
     * @param  updateVehicleSettlement
     */
    private void backToWaitSettleStatement(TVehicleSettlement updateVehicleSettlement,String settlementMonth) {
        updateVehicleSettlement.setStatus(VehicleSettlementStatementStatusEnum.WAIT_SETTLE_STATEMENT.getKey());
        updateVehicleSettlement.setIfAdjustFee(CommonConstant.INTEGER_ZERO);
        updateVehicleSettlement.setActualExpensesPayable(BigDecimal.ZERO);
        updateVehicleSettlement.setCarrierOrderCount(CommonConstant.INTEGER_ZERO);
        updateVehicleSettlement.setCarrierFreight(BigDecimal.ZERO);
        updateVehicleSettlement.setTireFee(BigDecimal.ZERO);
        updateVehicleSettlement.setGpsFee(BigDecimal.ZERO);
        updateVehicleSettlement.setParkingFee(BigDecimal.ZERO);
        updateVehicleSettlement.setLoanFee(BigDecimal.ZERO);
        updateVehicleSettlement.setOilFilledFee(BigDecimal.ZERO);
        updateVehicleSettlement.setVehicleClaimFee(BigDecimal.ZERO);
        updateVehicleSettlement.setInsuranceFee(BigDecimal.ZERO);
        updateVehicleSettlement.setDeductingFee(BigDecimal.ZERO);
        updateVehicleSettlement.setRemainingFee(BigDecimal.ZERO);
        updateVehicleSettlement.setAccidentInsuranceFee(BigDecimal.ZERO);
        updateVehicleSettlement.setAccidentInsuranceExpenseTotal(BigDecimal.ZERO);
        updateVehicleSettlement.setAccidentInsuranceClaimFee(BigDecimal.ZERO);
        updateVehicleSettlement.setRemark("");
        updateVehicleSettlement.setWithdrawRemark("");
        updateVehicleSettlement.setAdjustRemark("");
        updateVehicleSettlement.setAdjustFee(BigDecimal.ZERO);


        //tire oil  gps  parking  loan   insuranceCost
        List<TVehicleSettlementRelation> relationList = vehicleSettlementRelationMapper.getByVehicleSettlementId(updateVehicleSettlement.getId());
        if (ListUtils.isEmpty(relationList)) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }
        List<Long> vehicleTireIdList = new ArrayList<>();
        List<Long> oilFilledIdList = new ArrayList<>();
        List<Long> gpsFeeIdList = new ArrayList<>();
        List<Long> parkingFeeIdList = new ArrayList<>();
        List<Long> loanRecordsIdList = new ArrayList<>();
        List<Long> insuranceCostIdList=new ArrayList<>();
        for (TVehicleSettlementRelation relation : relationList) {
            if (relation.getObjectType().equals(VehicleSettlementTypeEnum.GPS.getKey())) {
                gpsFeeIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.PARKING.getKey())) {
                parkingFeeIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.LOAN.getKey())) {
                loanRecordsIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.TIRE.getKey())) {
                vehicleTireIdList.add(relation.getObjectId());
            } else if (relation.getObjectType().equals(VehicleSettlementTypeEnum.OIL.getKey())) {
                oilFilledIdList.add(relation.getObjectId());
            } else if(relation.getObjectType().equals(VehicleSettlementTypeEnum.INSURANCE.getKey())){
                insuranceCostIdList.add(relation.getObjectId());
            }
        }
        List<TDeductingHistory> deductingHistoryObjectIdList=new ArrayList<>();
        TDeductingHistory deductingHistory=null;
        //gps回退
        TGpsFee upGpsFee=null;
        if(ListUtils.isNotEmpty(gpsFeeIdList)){
            Long gpsFeeId=gpsFeeIdList.get(CommonConstant.INTEGER_ZERO);
            TDeductingHistory tDeductingHistory = deductingHistoryMapper.getByTypeObjectIdMonth(DeductingHistoryObjectTypeEnum.T_GPS_FEE.getKey(),gpsFeeId,settlementMonth);
            if(tDeductingHistory!=null){
                deductingHistory=new TDeductingHistory();
                deductingHistory.setId(tDeductingHistory.getId());
                deductingHistory.setValid(IfValidEnum.INVALID.getKey());
                commonBiz.setBaseEntityModify(deductingHistory,BaseContextHandler.getUserName());
                deductingHistoryObjectIdList.add(deductingHistory);
                upGpsFee=new TGpsFee();
                upGpsFee.setId(tDeductingHistory.getObjectId());
                if(tDeductingHistory.getDeductingFee().add(tDeductingHistory.getRemainingDeductingFee()).compareTo(tDeductingHistory.getTotalFee())<CommonConstant.INTEGER_ZERO){
                    upGpsFee.setStatus(SettlementStatusEnum.PART.getKey());
                }else if(tDeductingHistory.getDeductingFee().add(tDeductingHistory.getRemainingDeductingFee()).compareTo(tDeductingHistory.getTotalFee())==CommonConstant.INTEGER_ZERO){
                    upGpsFee.setStatus(SettlementStatusEnum.WAIT.getKey());
                }
                commonBiz.setBaseEntityModify(upGpsFee,BaseContextHandler.getUserName());
            }
        }
        //停车费用回退
        TParkingFee upParkingFee=null;
        if(ListUtils.isNotEmpty(parkingFeeIdList)){
            Long parkingFeeId=parkingFeeIdList.get(CommonConstant.INTEGER_ZERO);
            TDeductingHistory parkingHistory=deductingHistoryMapper.getByTypeObjectIdMonth(DeductingHistoryObjectTypeEnum.T_PARKING_FEE.getKey(),parkingFeeId,settlementMonth);
            if(parkingHistory!=null){
                deductingHistory=new TDeductingHistory();
                deductingHistory.setId(parkingHistory.getId());
                deductingHistory.setValid(IfValidEnum.INVALID.getKey());
                commonBiz.setBaseEntityModify(deductingHistory,BaseContextHandler.getUserName());
                deductingHistoryObjectIdList.add(deductingHistory);

                upParkingFee=new TParkingFee();
                upParkingFee.setId(parkingHistory.getObjectId());
                if(parkingHistory.getDeductingFee().add(parkingHistory.getRemainingDeductingFee()).compareTo(parkingHistory.getTotalFee())<CommonConstant.INTEGER_ZERO){
                    upParkingFee.setStatus(SettlementStatusEnum.PART.getKey());
                }else if(parkingHistory.getDeductingFee().add(parkingHistory.getRemainingDeductingFee()).compareTo(parkingHistory.getTotalFee())==CommonConstant.INTEGER_ZERO){
                    upParkingFee.setStatus(SettlementStatusEnum.WAIT.getKey());
                }
                commonBiz.setBaseEntityModify(upParkingFee,BaseContextHandler.getUserName());
            }
        }
        //贷款还款 回退
        TLoanRecords upLoanRecords=null;
        TLoanSettlementRecord upLoanSettlementRecord=null;
        if(ListUtils.isNotEmpty(loanRecordsIdList)){
            Long loanRecordsId=loanRecordsIdList.get(CommonConstant.INTEGER_ZERO);
            TLoanSettlementRecord loanHistory=loanSettlementRecordMapper.getByLoanRecordsIdMonth(loanRecordsId,settlementMonth);
            if(loanHistory!=null){
                upLoanRecords=new TLoanRecords();
                upLoanRecords.setId(loanHistory.getLoanRecordsId());
                if(loanHistory.getSettlementFee().add(loanHistory.getRemainingRepaymentFee()).compareTo(loanHistory.getTotalFee())<CommonConstant.INTEGER_ZERO){
                    upLoanRecords.setStatus(SettlementStatusEnum.PART.getKey());
                }else if(loanHistory.getSettlementFee().add(loanHistory.getRemainingRepaymentFee()).compareTo(loanHistory.getTotalFee())==CommonConstant.INTEGER_ZERO){
                    upLoanRecords.setStatus(SettlementStatusEnum.WAIT.getKey());
                }
                commonBiz.setBaseEntityModify(upLoanRecords,BaseContextHandler.getUserName());
                upLoanSettlementRecord=new TLoanSettlementRecord();
                upLoanSettlementRecord.setId(loanHistory.getId());
                upLoanSettlementRecord.setValid(IfValidEnum.INVALID.getKey());
                commonBiz.setBaseEntityModify(upLoanSettlementRecord,BaseContextHandler.getUserName());
            }
        }
        //保险费用退还
        rollbackInsuranceFee(insuranceCostIdList);

        //账单事件清空
        tVehicleSettlementEventsMapper.rollbackToWaitSettleStatement(updateVehicleSettlement.getId(),BaseContextHandler.getUserName());

        if (ListUtils.isNotEmpty(oilFilledIdList)) {
            oilFilledMapper.rollbackSettlementOilFilledByIds(StringUtils.listToString(oilFilledIdList, ','), BaseContextHandler.getUserName());
        }
        if (ListUtils.isNotEmpty(vehicleTireIdList)) {
            vehicleTireMapper.rollbackSettlementOilFilledByIds(StringUtils.listToString(vehicleTireIdList, ','), BaseContextHandler.getUserName());
        }
        if(upGpsFee!=null){
            gpsFeeMapper.updateByPrimaryKeySelective(upGpsFee);
        }
        if(upParkingFee!=null){
            parkingFeeMapper.updateByPrimaryKeySelective(upParkingFee);
        }
        if(upLoanRecords!=null){
            loanRecordsMapper.updateByPrimaryKeySelective(upLoanRecords);
        }
        if(upLoanSettlementRecord!=null){
            loanSettlementRecordMapper.updateByPrimaryKeySelective(upLoanSettlementRecord);
        }
        if(ListUtils.isNotEmpty(deductingHistoryObjectIdList)){
            deductingHistoryMapper.batchUpdate(deductingHistoryObjectIdList);
        }
        certificationPicturesMapper.delByObjectTypeId(CertificationPicturesFileTypeEnum.VEHICLE_SETTLEMENT_ATTACHMENT.getObjectType().getObjectType(),updateVehicleSettlement.getId(),BaseContextHandler.getUserName());

    }
    /**
     * 保险费用 回退
     *
     * @param  insuranceCostIdList
   * @param  settlementMonth
     */
    private void rollbackInsuranceFee(List<Long> insuranceCostIdList) {
        if (ListUtils.isEmpty(insuranceCostIdList)) {
            return;
        }
        GetInsuranceCostsByVehicleIdResponseModel insuranceCostModel = insuranceCostsMapper.getByIdForSettlement(insuranceCostIdList.get(CommonConstant.INTEGER_ZERO));
        if(insuranceCostModel==null){
            return;
        }
        BigDecimal payCommercialInsuranceCost=Optional.ofNullable(insuranceCostModel.getPayCommercialInsuranceCost()).orElse(BigDecimal.ZERO);//已支付商业险费用
        BigDecimal payCompulsoryInsuranceCost=Optional.ofNullable(insuranceCostModel.getPayCompulsoryInsuranceCost()).orElse(BigDecimal.ZERO);//已支付交强险费用
        BigDecimal payCargoInsuranceCost =Optional.ofNullable(insuranceCostModel.getPayCargoInsuranceCost()).orElse(BigDecimal.ZERO);//已支付货物险
        BigDecimal payCarrierInsuranceCost=Optional.ofNullable(insuranceCostModel.getPayCarrierInsuranceCost()).orElse(BigDecimal.ZERO);//已支付承运人险
        List<TInsurance> insuranceList=insuranceMapper.getByIds(StringUtils.listToString(insuranceCostModel.getInsuranceIdList(),','));
        List<TInsurance> upInsuranceList=new ArrayList<>();
        TInsurance upInsurance;
        for(TInsurance insurance : insuranceList){
            upInsurance=new TInsurance();
            upInsurance.setId(insurance.getId());
            if(InsuranceEnum.COMMERCIAL.getKey().equals(insurance.getInsuranceType())
                    && payCommercialInsuranceCost.compareTo(BigDecimal.ZERO)>=CommonConstant.INTEGER_ZERO){
                upInsurance.setUnpaidPremium(insurance.getUnpaidPremium().add(payCommercialInsuranceCost).setScale(2,BigDecimal.ROUND_HALF_UP));
            }else if(InsuranceEnum.COMPULSORY.getKey().equals(insurance.getInsuranceType())
                    && payCompulsoryInsuranceCost.compareTo(BigDecimal.ZERO)>=CommonConstant.INTEGER_ZERO){
                upInsurance.setUnpaidPremium(insurance.getUnpaidPremium().add(payCompulsoryInsuranceCost).setScale(2,BigDecimal.ROUND_HALF_UP));
            }else if(InsuranceEnum.CARGO.getKey().equals(insurance.getInsuranceType())
                    && payCompulsoryInsuranceCost.compareTo(BigDecimal.ZERO)>=CommonConstant.INTEGER_ZERO){
                upInsurance.setUnpaidPremium(insurance.getUnpaidPremium().add(payCargoInsuranceCost).setScale(2,BigDecimal.ROUND_HALF_UP));
            }else if(InsuranceEnum.CARRIER.getKey().equals(insurance.getInsuranceType())
                    && payCompulsoryInsuranceCost.compareTo(BigDecimal.ZERO)>=CommonConstant.INTEGER_ZERO){
                upInsurance.setUnpaidPremium(insurance.getUnpaidPremium().add(payCarrierInsuranceCost).setScale(2,BigDecimal.ROUND_HALF_UP));
            }
            if(Optional.ofNullable(upInsurance.getUnpaidPremium()).orElse(insurance.getUnpaidPremium()).compareTo(insurance.getPremium())<CommonConstant.INTEGER_ZERO) {
                upInsurance.setSettlementStatus(SettlementStatusEnum.PART.getKey());
            }else if(Optional.ofNullable(upInsurance.getUnpaidPremium()).orElse(insurance.getUnpaidPremium()).compareTo(insurance.getPremium())==CommonConstant.INTEGER_ZERO){
                upInsurance.setSettlementStatus(SettlementStatusEnum.WAIT.getKey());
            }
            commonBiz.setBaseEntityModify(upInsurance,BaseContextHandler.getUserName());
            upInsuranceList.add(upInsurance);
        }
        if(ListUtils.isNotEmpty(upInsuranceList)) {
            insuranceMapper.batchUpdate(upInsuranceList);
        }
        if(ListUtils.isNotEmpty(insuranceCostIdList)) {
            insuranceCostsMapper.deleteInsuranceCostAndRelation(insuranceCostIdList.get(CommonConstant.INTEGER_ZERO), BaseContextHandler.getUserName());
        }

    }

    /**
     *  结清费用详情
     *
     * @param  requestModel
     */
    public SettleFreightDetailResponseModel settleFreightDetail(VehicleSettlementIdRequestModel requestModel) {
        SettleFreightDetailResponseModel responseModel= vehicleSettlementMapper.settleFreightDetailById(requestModel.getVehicleSettlementId());
        if(responseModel==null){
            return new SettleFreightDetailResponseModel();
        }
        BigDecimal actualExpensesPayable = responseModel.getActualExpensesPayable();
        BigDecimal payMoneyTotal = responseModel.getPayMoneyTotal();
        responseModel.setNotPayMoney(Optional.ofNullable(actualExpensesPayable).orElse(BigDecimal.ZERO)
                .subtract(Optional.ofNullable(payMoneyTotal).orElse(BigDecimal.ZERO)).setScale(2,BigDecimal.ROUND_HALF_UP));
        return responseModel;
    }

    /**
     * 结清运费
     *
     * @param  requestModel
     */
    @Transactional
    public void settleFreight(SettleFreightRequestModel requestModel) {
        SettleFreightDetailResponseModel responseModel= vehicleSettlementMapper.settleFreightDetailById(requestModel.getVehicleSettlementId());
        if(responseModel==null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }
        if(!(VehicleSettlementStatementStatusEnum.WAIT_SETTLE.getKey().equals(responseModel.getStatus())
                || VehicleSettlementStatementStatusEnum.PART_OF_SETTLE.getKey().equals(responseModel.getStatus()))){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLE_STATEMENT_ERROR);
        }
        TVehicleSettlement tVehicleSettlement=new TVehicleSettlement();
        if(responseModel.getActualExpensesPayable()!=null && responseModel.getActualExpensesPayable().compareTo(BigDecimal.ZERO)<=CommonConstant.INTEGER_ZERO){
            //应付运费小于等于0，直接结清
            tVehicleSettlement.setStatus(VehicleSettlementStatementStatusEnum.SETTLED.getKey());
        }else{
            if(StringUtils.isEmpty(requestModel.getPayCompany()) || requestModel.getPayCompany().length()<CommonConstant.INTEGER_ONE|| requestModel.getPayCompany().length()>CommonConstant.INTEGER_FIFTY){
                throw new BizException(CarrierDataExceptionEnum.PAY_COMPANY_ERROR);
            }
            if(StringUtils.isEmpty(requestModel.getReceivedName()) || requestModel.getReceivedName().length()<CommonConstant.INTEGER_ONE|| requestModel.getReceivedName().length()>CommonConstant.INTEGER_FIFTY){
                throw new BizException(CarrierDataExceptionEnum.RECEIVE_NAME_ERROR);
            }
            if(requestModel.getPayTime()==null){
                throw new BizException(CarrierDataExceptionEnum.PAY_TIME_ERROR);
            }
            if(requestModel.getPayFee()==null || requestModel.getPayFee().compareTo(BigDecimal.ZERO)<=CommonConstant.INTEGER_ZERO){
                throw new BizException(CarrierDataExceptionEnum.PAY_MONEY_NOT_EMPTY);
            }
            BigDecimal actualExpensesPayable = responseModel.getActualExpensesPayable();
            BigDecimal payMoneyTotal = responseModel.getPayMoneyTotal();
            BigDecimal needPayMoney=Optional.ofNullable(actualExpensesPayable).orElse(BigDecimal.ZERO)
                    .subtract(Optional.ofNullable(payMoneyTotal).orElse(BigDecimal.ZERO)).setScale(2,BigDecimal.ROUND_HALF_UP).stripTrailingZeros();
            BigDecimal notPayMoney = needPayMoney.subtract(requestModel.getPayFee()).setScale(2,BigDecimal.ROUND_HALF_UP).stripTrailingZeros();
            if(notPayMoney.compareTo(BigDecimal.ZERO)<CommonConstant.INTEGER_ZERO){
                throw new BizException(CarrierDataExceptionEnum.PAY_MONEY_ERROR);
            }else if(notPayMoney.equals(BigDecimal.ZERO)){
                tVehicleSettlement.setStatus(VehicleSettlementStatementStatusEnum.SETTLED.getKey());
            }else if(notPayMoney.compareTo(BigDecimal.ZERO)>CommonConstant.INTEGER_ZERO){
                tVehicleSettlement.setStatus(VehicleSettlementStatementStatusEnum.PART_OF_SETTLE.getKey());
            }

            TVehicleSettlementPayment tVehicleSettlementPayment=new TVehicleSettlementPayment();
            tVehicleSettlementPayment.setPayFee(requestModel.getPayFee());
            tVehicleSettlementPayment.setPayTime(requestModel.getPayTime());
            tVehicleSettlementPayment.setReceiverName(requestModel.getReceivedName());
            tVehicleSettlementPayment.setVehicleSettlementId(responseModel.getVehicleSettlementId());
            tVehicleSettlementPayment.setPayCompany(requestModel.getPayCompany());
            commonBiz.setBaseEntityAdd(tVehicleSettlementPayment,BaseContextHandler.getUserName());
            tVehicleSettlementPaymentMapper.insertSelective(tVehicleSettlementPayment);
        }
        tVehicleSettlement.setId(responseModel.getVehicleSettlementId());
        commonBiz.setBaseEntityModify(tVehicleSettlement,BaseContextHandler.getUserName());
        vehicleSettlementMapper.updateByPrimaryKeySelective(tVehicleSettlement);

        if(VehicleSettlementStatementStatusEnum.SETTLED.getKey().equals(tVehicleSettlement.getStatus())){
            insertEvent(VehicleSettlementEventsEnum.SETTLED,tVehicleSettlement.getId());
        }
    }
    /**
     * 处理详情
     *
     * @param  requestModel
     */
    public SettlementStatementHandleDetailResponseModel settlementStatementHandleDetail(VehicleSettlementIdRequestModel requestModel) {
        SettlementStatementHandleDetailResponseModel responseModel= vehicleSettlementMapper.settlementStatementHandleDetail(requestModel.getVehicleSettlementId());
        if(responseModel==null || !VehicleSettlementStatementStatusEnum.WAIT_HANDLE.getKey().equals(responseModel.getStatus())){
            return new SettlementStatementHandleDetailResponseModel();
        }
        if(responseModel.getAdjustFee()!=null){
            if(responseModel.getAdjustFee().compareTo(BigDecimal.ZERO)<CommonConstant.INTEGER_ZERO){
                responseModel.setAdjustCostSymbol(CommonConstant.INTEGER_TWO);
                responseModel.setAdjustFee(responseModel.getAdjustFee().negate());
            }else{
                responseModel.setAdjustCostSymbol(CommonConstant.INTEGER_ONE);
            }
        }
        return responseModel;
    }

    /**
     *
     * 车辆结算列表-处理
     *
     * @param  requestModel
     */
    @Transactional
    public void settlementStatementHandle(SettlementStatementHandleRequestModel requestModel) {
        SettlementStatementHandleModel model= vehicleSettlementMapper.settlementStatementHandleInfo(requestModel.getVehicleSettlementId());
        if(model==null){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }
        if(!VehicleSettlementStatementStatusEnum.WAIT_HANDLE.getKey().equals(model.getVehicleSettlementStatus())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLE_STATEMENT_ERROR);
        }
        TVehicleSettlement vehicleSettlement=new TVehicleSettlement();
        vehicleSettlement.setId(model.getVehicleSettlementId());
        TVehicleSettlementDriverRelation driverRelation=new TVehicleSettlementDriverRelation();
        driverRelation.setId(model.getVehicleSettlementDriverRelationId());
        if(CommonConstant.INTEGER_ONE.equals(requestModel.getOperatorType())) {
            vehicleSettlement.setStatus(VehicleSettlementStatementStatusEnum.WAIT_SETTLE.getKey());
            driverRelation.setConfirmTime(new Date());
            driverRelation.setReason(requestModel.getReason());
            driverRelation.setStatus(VehicleSettlementDriverStatusEnum.DO_NOT_NEED_TO_CONFIRM.getKey());
            insertEvent(VehicleSettlementEventsEnum.CONFIRM,vehicleSettlement.getId());
        }else if(CommonConstant.INTEGER_TWO.equals(requestModel.getOperatorType())){
            if(CommonConstant.INTEGER_TWO.equals(requestModel.getAdjustCostSymbol())){
                requestModel.setAdjustCost(requestModel.getAdjustCost().negate());
            }
            BigDecimal adjustFee=Optional.ofNullable(model.getAdjustFee()).orElse(BigDecimal.ZERO);
            BigDecimal finalFee = Optional.ofNullable(model.getActualExpensesPayable()).orElse(BigDecimal.ZERO).subtract(adjustFee).add(requestModel.getAdjustCost()).setScale(2,BigDecimal.ROUND_HALF_UP).stripTrailingZeros();
            vehicleSettlement.setStatus(VehicleSettlementStatementStatusEnum.WAIT_CONFIRM.getKey());
            vehicleSettlement.setIfAdjustFee(CommonConstant.INTEGER_ONE);
            vehicleSettlement.setAdjustFee(requestModel.getAdjustCost());
            vehicleSettlement.setAdjustRemark(requestModel.getAdjustRemark());
            vehicleSettlement.setActualExpensesPayable(finalFee);

            driverRelation.setStatus(VehicleSettlementDriverStatusEnum.DEFAULT.getKey());

        }else if(CommonConstant.INTEGER_THREE.equals(requestModel.getOperatorType())){
            backToWaitSettleStatement(vehicleSettlement,model.getSettlementMonth());
            driverRelation.setValid(IfValidEnum.INVALID.getKey());

        }
        commonBiz.setBaseEntityModify(vehicleSettlement,BaseContextHandler.getUserName());
        commonBiz.setBaseEntityModify(driverRelation, BaseContextHandler.getUserName());
        vehicleSettlementMapper.updateByPrimaryKeySelective(vehicleSettlement);
        tVehicleSettlementDriverRelationMapper.updateByPrimaryKeySelective(driverRelation);
    }
    /**
     * 新增事件
     *
     * @param  eventsEnum
   * @param  vehicleSettlementId
     */
    private void insertEvent(VehicleSettlementEventsEnum eventsEnum,Long vehicleSettlementId){
        Date now = new Date();
        TVehicleSettlementEvents event = new TVehicleSettlementEvents();
        event.setVehicleSettlementId(vehicleSettlementId);
        event.setEvent(eventsEnum.getKey());
        event.setEventDesc(eventsEnum.getValue());
        event.setEventTime(now);
        event.setOperateTime(now);
        event.setOperatorName(BaseContextHandler.getUserName());
        commonBiz.setBaseEntityAdd(event,BaseContextHandler.getUserName());
        tVehicleSettlementEventsMapper.insertSelective(event);
    }

    /**
     * 车辆结算-发送司机-账单列表
     *
     * @param requestModel
     * @return
     */
    public List<SendDriverSettleStatementListResponseModel> sendDriverSettleStatementList(SendDriverSettleStatementListRequestModel requestModel){
        List<SendDriverSettleStatementListResponseModel> result = vehicleSettlementMapper.sendDriverSettleStatementList(requestModel);
        if (ListUtils.isNotEmpty(result)){
            List<Long> noCarrierOrderStatement = new ArrayList<>();
            List<Long> hasCarrierOrderStatement = new ArrayList<>();
            for (SendDriverSettleStatementListResponseModel statementListResponseModel : result){
                if (statementListResponseModel.getCarrierOrderCount() == null || statementListResponseModel.getCarrierOrderCount() == 0){
                    noCarrierOrderStatement.add(statementListResponseModel.getVehicleSettlementId());
                }else {
                    hasCarrierOrderStatement.add(statementListResponseModel.getVehicleSettlementId());
                }
            }
            // 匹配司机逻辑 如果有运单的单子 随机取出一个运单司机  没有单子的取关联车辆司机
            List<GetSettlementDriverModel> settlementVehicleDriverInfoByIds = new ArrayList<>();
            if (ListUtils.isNotEmpty(hasCarrierOrderStatement)){
                settlementVehicleDriverInfoByIds = vehicleSettlementMapper.getSettlementVehicleDriverInfoByIds(StringUtils.listToString(hasCarrierOrderStatement, ','));
            }
            if(ListUtils.isNotEmpty(noCarrierOrderStatement)){
                List<GetSettlementDriverModel> settlementVehicleDriverInfoNoCarrier = vehicleSettlementMapper.getSettlementVehicleDriverInfoNoCarrier(StringUtils.listToString(noCarrierOrderStatement, ','));
                if (ListUtils.isNotEmpty(settlementVehicleDriverInfoNoCarrier)){
                    settlementVehicleDriverInfoByIds.addAll(settlementVehicleDriverInfoNoCarrier);
                }
            }

            if (ListUtils.isNotEmpty(settlementVehicleDriverInfoByIds)){
                for (SendDriverSettleStatementListResponseModel statementListResponseModel : result){
                    for (GetSettlementDriverModel getSettlementDriverModel : settlementVehicleDriverInfoByIds){
                        if (statementListResponseModel.getVehicleSettlementId().equals(getSettlementDriverModel.getVehicleSettlementId())){
                            statementListResponseModel.setDriverId(getSettlementDriverModel.getDriverId());
                            statementListResponseModel.setDriverName(getSettlementDriverModel.getDriverName());
                            statementListResponseModel.setMobile(getSettlementDriverModel.getDriverMobile());
                            break;
                        }
                    }
                }
            }

        }
        return result;
    }

    /**
     *车辆结算详情-修改关联的轮胎（待对账状态操作）
     *
     * @param  requestModel
     */
    @Transactional
    public void updateVehicleSettlementTire(UpdateVehicleSettlementTireRequestModel requestModel) {
        TVehicleSettlement vehicleSettlement = vehicleSettlementMapper.selectByPrimaryKey(requestModel.getVehicleSettlementId());
        if(vehicleSettlement==null || IfValidEnum.INVALID.getKey().equals(vehicleSettlement.getValid())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }
        if(!VehicleSettlementStatementStatusEnum.WAIT_SETTLE_STATEMENT.getKey().equals(vehicleSettlement.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLE_STATEMENT_ERROR);
        }

        List<TVehicleSettlementRelation> oldTireRelation = vehicleSettlementRelationMapper.getByTypeSettlementId(VehicleSettlementTypeEnum.TIRE.getKey(), requestModel.getVehicleSettlementId());
        if(ListUtils.isEmpty(requestModel.getSelectVehicleTireIdList())){
            if(ListUtils.isNotEmpty(oldTireRelation)){
                List<Long> collect = oldTireRelation.stream().map(TVehicleSettlementRelation::getId).collect(Collectors.toList());
                vehicleSettlementRelationMapper.delByIds(StringUtils.listToString(collect,','),BaseContextHandler.getUserName());
            }
            return ;
        }

        List<TVehicleTire> tires = vehicleTireMapper.getByIds(StringUtils.listToString(requestModel.getSelectVehicleTireIdList(), ','));
        if(ListUtils.isEmpty(tires)){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_TIRE_IS_EMPTY);
        }
        List<Long> tiresIdList=new ArrayList<>();
        tires.forEach(item-> {
            if(OilFilledStatusEnum.HAVE_SETTLE.getKey().equals(item.getSettlementStatus())){
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_HAS_SETTLEMENT);
            }
            tiresIdList.add(item.getId());
        });

        List<TVehicleSettlementRelation> newRelationList=new ArrayList<>();
        List<Long> delRelationList=null;
        TVehicleSettlementRelation newRelation;
        if(ListUtils.isEmpty(oldTireRelation)){
            for(TVehicleTire tire:tires){
                newRelation=new TVehicleSettlementRelation();
                newRelation.setVehicleSettlementId(requestModel.getVehicleSettlementId());
                newRelation.setObjectId(tire.getId());
                newRelation.setObjectType(VehicleSettlementTypeEnum.TIRE.getKey());
                commonBiz.setBaseEntityAdd(newRelation,BaseContextHandler.getUserName());
                newRelationList.add(newRelation);
            }
        }else{
            delRelationList=new ArrayList<>();
            List<Long> unionTireList=new ArrayList<>();//交集 保持不变的轮胎
            for (TVehicleSettlementRelation tVehicleSettlementRelation : oldTireRelation) {
                if(tiresIdList.contains(tVehicleSettlementRelation.getObjectId())){
                    unionTireList.add(tVehicleSettlementRelation.getObjectId());
                }else{
                    delRelationList.add(tVehicleSettlementRelation.getId());
                }
            }
            for (Long tire:tiresIdList) {
                if(!unionTireList.contains(tire)){ //需要新增的轮胎
                    newRelation=new TVehicleSettlementRelation();
                    newRelation.setVehicleSettlementId(requestModel.getVehicleSettlementId());
                    newRelation.setObjectId(tire);
                    newRelation.setObjectType(VehicleSettlementTypeEnum.TIRE.getKey());
                    commonBiz.setBaseEntityAdd(newRelation,BaseContextHandler.getUserName());
                    newRelationList.add(newRelation);
                }
            }
        }
        if(ListUtils.isNotEmpty(delRelationList)){
            vehicleSettlementRelationMapper.delByIds(StringUtils.listToString(delRelationList,','),BaseContextHandler.getUserName());
        }
        if(ListUtils.isNotEmpty(newRelationList)){
            vehicleSettlementRelationMapper.batchInsert(newRelationList);
        }
    }

    /**
     * 车辆结算详情-查询账单上该车辆未关联的轮胎费用
     *
     * @param  requestModel
     */
    public GetVehicleTireByVehicleSettlementIdResponseModel getVehicleTireByVehicleSettlementId(VehicleSettlementIdRequestModel requestModel) {
        TVehicleSettlement tVehicleSettlement = vehicleSettlementMapper.selectByPrimaryKey(requestModel.getVehicleSettlementId());
        if(tVehicleSettlement==null || IfValidEnum.INVALID.getKey().equals(tVehicleSettlement.getValid())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_SETTLEMENT_EMPTY);
        }

        List<GetVehicleTireListByVehicleSettlementIdResponseModel> vehicleTireList=vehicleSettlementRelationMapper.getVehicleTireListBySettlementId(requestModel.getVehicleSettlementId());//已关联对账单的轮胎信息
        List<Long> driverIds=null;
        List<Long> selectVehicleTireIdList=new ArrayList<>();
        if(ListUtils.isNotEmpty(vehicleTireList)) {
            driverIds=new ArrayList<>();
            for (GetVehicleTireListByVehicleSettlementIdResponseModel responseModel : vehicleTireList) {
                if(!driverIds.contains(responseModel.getStaffId())) {
                    driverIds.add(responseModel.getStaffId());
                }
                responseModel.setVehicleNo(tVehicleSettlement.getVehicleNo());
                selectVehicleTireIdList.add(responseModel.getVehicleTireId());
            }
        }
        List<GetVehicleTireListByVehicleSettlementIdResponseModel> notAssociateTireList=vehicleSettlementRelationMapper.getNotAssociateTireList(tVehicleSettlement.getVehicleId());//未关联对账单轮胎信息
        if(driverIds==null){
            driverIds=new ArrayList<>();
        }
        for (GetVehicleTireListByVehicleSettlementIdResponseModel responseModel : notAssociateTireList) {
            if(!driverIds.contains(responseModel.getStaffId())) {
                driverIds.add(responseModel.getStaffId());
            }
            responseModel.setVehicleNo(tVehicleSettlement.getVehicleNo());
        }
        if(ListUtils.isNotEmpty(driverIds)) {
            List<TStaffBasic> staffByIds = tStaffBasicMapper.getStaffByIds(StringUtils.listToString(driverIds, ','));
            Map<Long,TStaffBasic> map=new HashMap<>();
            for (TStaffBasic staff : staffByIds) {
                map.put(staff.getId(),staff);
            }
            vehicleTireList.addAll(notAssociateTireList);
            for (GetVehicleTireListByVehicleSettlementIdResponseModel responseModel : vehicleTireList) {
                TStaffBasic tStaffBasic = map.get(responseModel.getStaffId());
                if(tStaffBasic!=null){
                    responseModel.setDriverMobile(tStaffBasic.getMobile());
                    responseModel.setDriverName(tStaffBasic.getName());
                }
            }
        }
        if(ListUtils.isNotEmpty(vehicleTireList)){
            vehicleTireList.sort((x,y)->{
               int b = y.getLastModifiedTime().compareTo(x.getLastModifiedTime());
               if(b==0){
                   b= y.getVehicleTireId().compareTo(x.getVehicleTireId());
               }
               return  b;
            });
        }
        GetVehicleTireByVehicleSettlementIdResponseModel responseModel=new GetVehicleTireByVehicleSettlementIdResponseModel();
        responseModel.setSelectVehicleTireIdList(selectVehicleTireIdList);
        responseModel.setVehicleTireList(vehicleTireList);
        responseModel.setVehicleSettlementId(tVehicleSettlement.getId());
        return responseModel;

    }
}
