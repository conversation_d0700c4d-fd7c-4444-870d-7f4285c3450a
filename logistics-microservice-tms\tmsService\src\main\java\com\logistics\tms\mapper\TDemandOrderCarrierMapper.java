package com.logistics.tms.mapper;

import com.logistics.tms.entity.TDemandOrderCarrier;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Mybatis Generator on 2022/07/08
 */
@Mapper
public interface TDemandOrderCarrierMapper extends BaseMapper<TDemandOrderCarrier> {
    void batchUpdate(@Param("list") List<TDemandOrderCarrier> tDemandOrderCarrierList);

    void batchInsertSelective(@Param("list") List<TDemandOrderCarrier> tDemandOrderCarrierList);

    List<TDemandOrderCarrier> getDemandCarrierByDemandOrderIds(@Param("demandOrderIds") String demandOrderIds, @Param("companyCarrierId") Long companyCarrierId);

    List<TDemandOrderCarrier> getNewestDemandCarrierByDemandOrderIds(@Param("demandOrderIds") String demandOrderIds, @Param("companyCarrierIds") String companyCarrierIds);

}