package com.logistics.appapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/4/9 14:22
 */
@Data
public class DriverReconciliationDetailResponseDto {
    @ApiModelProperty("结算id")
    private String vehicleSettlementId="";
    @ApiModelProperty("结算状态：空 全部，2 待确认，3 待处理，4 待结清,5 部分结清，6 已结清")
    private String status="";

    //费用信息
    @ApiModelProperty("运费合计")
    private String carrierFreight="";
    @ApiModelProperty("运单数量")
    private String carrierOrderCount="";
    @ApiModelProperty("调整费用")
    private String adjustFee="";
    @ApiModelProperty("调整原因")
    private String adjustRemark="";
    @ApiModelProperty("轮胎费用")
    private String tireFee="";
    @ApiModelProperty("轮胎数量")
    private String tireCount="";
    @ApiModelProperty("充油费用")
    private String oilFilledFee="";
    @ApiModelProperty("GPS费用")
    private String gpsFee="";
    @ApiModelProperty("停车费用")
    private String parkingFee="";
    @ApiModelProperty("保险费用合计")
    private String insuranceFee="";
    @ApiModelProperty("理赔费用合计")
    private String claimFee="";
    @ApiModelProperty("贷款扣除费用")
    private String loanFee="";
    @ApiModelProperty("合计（应收费用）")
    private String actualExpensesPayable="";

    //付款信息（部分结清、已结清）
    @ApiModelProperty("已结清运费")
    private String completeSettlePayable="";
    @ApiModelProperty("未结清运费")
    private String waitSettlePayable="";
    @ApiModelProperty("账单记录数量")
    private String billingRecordsCount="";

    //异议信息（待处理）
    @ApiModelProperty("司机对账问题备注")
    private String settlementReasonRemark="";
}
