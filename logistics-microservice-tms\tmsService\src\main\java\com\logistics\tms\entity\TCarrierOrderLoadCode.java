package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
* Created by Mybatis Generator on 2025/01/03
*/
@Data
public class TCarrierOrderLoadCode extends BaseEntity {
    /**
    * 运单id
    */
    @ApiModelProperty("运单id")
    private Long carrierOrderId;

    /**
    * 编码 (state=2 该字段为空)
    */
    @ApiModelProperty("编码 (state=2 该字段为空)")
    private String productCode;

    /**
    * 云盘客户名称 (state=2 该字段为空)
    */
    @ApiModelProperty("云盘客户名称 (state=2 该字段为空)")
    private String trayCustomerCompanyName;

    /**
    * 编码状态 1正确编码 2编码无法识别
    */
    @ApiModelProperty("编码状态 1正确编码 2编码无法识别")
    private Integer state;


}