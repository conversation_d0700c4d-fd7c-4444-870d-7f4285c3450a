package com.logistics.management.webapi.api.impl.personalaccidentinsurance;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.personalaccidentinsurance.PersonalAccidentInsuranceApi;
import com.logistics.management.webapi.api.feign.personalaccidentinsurance.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.personalaccidentinsurance.mapping.GetInsuranceByPolicyNumberMapping;
import com.logistics.management.webapi.api.impl.personalaccidentinsurance.mapping.GetPolicyNoPremiumByPolicyNoMapping;
import com.logistics.management.webapi.api.impl.personalaccidentinsurance.mapping.PersonalAccidentInsuranceDetailMapping;
import com.logistics.management.webapi.api.impl.personalaccidentinsurance.mapping.SearchPersonalAccidentInsuranceListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportExcelPersonalAccidentInsurance;
import com.logistics.management.webapi.base.constant.ImportExcelHeaders;
import com.logistics.management.webapi.base.enums.InsuranceTypeEnum;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.personalaccidentinsurance.PersonalAccidentInsuranceServiceApi;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *  @author: wjf
 *  @date: 2019/5/30 17:41
 */
@Slf4j
@RestController
public class PersonalAccidentInsuranceApiImpl implements PersonalAccidentInsuranceApi {

    @Autowired
    private PersonalAccidentInsuranceServiceApi personalAccidentInsuranceServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 获取个人意外险列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<PersonalAccidentInsuranceListResponseDto>> searchPersonalAccidentInsuranceList(@RequestBody PersonalAccidentInsuranceListRequestDto requestDto) {
        Result<PageInfo<PersonalAccidentInsuranceListResponseModel>> result = personalAccidentInsuranceServiceApi.searchPersonalAccidentInsuranceList(MapperUtils.mapper(requestDto, PersonalAccidentInsuranceListRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (PersonalAccidentInsuranceListResponseModel responseModel : result.getData().getList()) {
            if(ListUtils.isNotEmpty(responseModel.getTicketsList())){
                responseModel.getTicketsList().stream().forEach(item->sourceSrcList.add(item.getFilePath()));
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(),PersonalAccidentInsuranceListResponseDto.class,new SearchPersonalAccidentInsuranceListMapping(configKeyConstant.fileAccessAddress,imageMap)));
        return Result.success(pageInfo);
    }

    /**
     * 查询个人意外险详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<PersonalAccidentInsuranceDetailResponseDto> getPersonalAccidentInsuranceDetail(@RequestBody @Valid PersonalAccidentInsuranceIdRequestDto requestDto) {
        Result<PersonalAccidentInsuranceDetailResponseModel> result = personalAccidentInsuranceServiceApi.getPersonalAccidentInsuranceDetail(MapperUtils.mapper(requestDto, PersonalAccidentInsuranceIdRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (PersonalAccidentInsuranceTicketsResponseModel model : result.getData().getTicketsList()) {
            sourceSrcList.add(model.getFilePath());
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),PersonalAccidentInsuranceDetailResponseDto.class,new PersonalAccidentInsuranceDetailMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 新增/修改个人意外险
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result addOrModifyPersonalAccidentInsurance(@RequestBody @Valid AddOrModifyPersonalAccidentInsuranceRequestDto requestDto) {
        if (requestDto.getInsuranceType().equals(InsuranceTypeEnum.BATCH.getKeyStr())){
            if (StringUtils.isBlank(requestDto.getPolicyNumberInsuranceId())){
                throw new BizException(ManagementWebApiExceptionEnum.BATCH_NUMBER_ERROR);
            }
            if (!FrequentMethodUtils.validateBatchNumber(requestDto.getBatchNumber())){
                throw new BizException(ManagementWebApiExceptionEnum.BATCH_NUMBER_ERROR);
            }
        }else{
            requestDto.setPolicyNumberInsuranceId("0");
            requestDto.setBatchNumber("");
        }
        if (ListUtils.isEmpty(requestDto.getTicketsList()) || requestDto.getTicketsList().size() > 10){
            throw new BizException(ManagementWebApiExceptionEnum.INSURANCE_TICKETS);
        }
        Result result = personalAccidentInsuranceServiceApi.addOrModifyPersonalAccidentInsurance(MapperUtils.mapperNoDefault(requestDto, AddOrModifyPersonalAccidentInsuranceRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 导出个人意外险
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportPersonalAccidentInsurance(PersonalAccidentInsuranceListRequestDto requestDto,HttpServletResponse response) {
        Result<List<PersonalAccidentInsuranceListResponseModel>> result = personalAccidentInsuranceServiceApi.exportPersonalAccidentInsurance(MapperUtils.mapper(requestDto, PersonalAccidentInsuranceListRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (PersonalAccidentInsuranceListResponseModel responseModel : result.getData()) {
            if(ListUtils.isNotEmpty(responseModel.getTicketsList())){
                responseModel.getTicketsList().stream().forEach(item->sourceSrcList.add(item.getFilePath()));
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        List<PersonalAccidentInsuranceListResponseDto> list = MapperUtils.mapper(result.getData(),PersonalAccidentInsuranceListResponseDto.class,new SearchPersonalAccidentInsuranceListMapping(configKeyConstant.fileAccessAddress,imageMap));
        String fileName = "个人意外险数据" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportExcelPersonalAccidentInsurance.getExportPersonalAccidentInsurance();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * 导入个人意外险
     * @param file
     * @param request
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ImportPersonalAccidentInsuranceResponseDto> importPersonalAccidentInsurance(@RequestParam("file")MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_PERSONAL_ACCIDENT_INSURANCE_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            log.error("导入个人意外险失败，",e);
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_PERSONAL_ACCIDENT_INSURANCE_EMPTY);
        }

        List<List<Object>> listByExcel = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportPersonalAccidentType());
        ImportPersonalAccidentInsuranceRequestModel model = new ImportPersonalAccidentInsuranceRequestModel();
        checkRepeatInsurance(listByExcel,model);
        if (ListUtils.isNotEmpty(model.getImportList())) {
            Result<ImportPersonalAccidentInsuranceResponseModel> result = personalAccidentInsuranceServiceApi.importPersonalAccidentInsurance(model);
            result.throwException();
            return Result.success(MapperUtils.mapper(result.getData(), ImportPersonalAccidentInsuranceResponseDto.class));
        }
        ImportPersonalAccidentInsuranceResponseDto responseDto = new ImportPersonalAccidentInsuranceResponseDto();
        responseDto.setNumberSuccessful(CommonConstant.ZERO);
        if (model.getErrorNumber() != null && model.getErrorNumber() > 0){
            responseDto.setNumberFailures(model.getErrorNumber().toString());
        }else {
            responseDto.setNumberFailures(CommonConstant.ZERO);
        }
        return Result.success(responseDto);
    }
    //入参校验及转换
    public void checkRepeatInsurance(List<List<Object>> listByExcel,ImportPersonalAccidentInsuranceRequestModel model){
        if (ListUtils.isEmpty(listByExcel)){
            return;
        }
        List<ImportPersonalAccidentInsuranceListRequestModel> importList = new ArrayList<>();
        ImportPersonalAccidentInsuranceListRequestModel requestModel;
        Integer errorNumber = 0;
        for (int i = 0; i < listByExcel.size(); i++){
            List<Object> objects = listByExcel.get(i);
            if (ListUtils.isNotEmpty(objects)) {
                requestModel = new ImportPersonalAccidentInsuranceListRequestModel();
                String insuranceType = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ZERO));
                if (StringUtils.isBlank(insuranceType) || (!insuranceType.equals(InsuranceTypeEnum.POLICY.getValue()) && !insuranceType.equals(InsuranceTypeEnum.BATCH.getValue()))){
                    errorNumber++;
                    continue;
                }
                if (insuranceType.equals(InsuranceTypeEnum.POLICY.getValue())){
                    requestModel.setInsuranceType(InsuranceTypeEnum.POLICY.getKey());
                }else if (insuranceType.equals(InsuranceTypeEnum.BATCH.getValue())){
                    requestModel.setInsuranceType(InsuranceTypeEnum.BATCH.getKey());
                }
                String insuranceCompany = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ONE));
                if (StringUtils.isBlank(insuranceCompany) || insuranceCompany.length() < 2 || insuranceCompany.length() > 50){
                    errorNumber++;
                    continue;
                }
                requestModel.setInsuranceCompany(insuranceCompany);
                String policyNumber = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_TWO));
                if (StringUtils.isBlank(policyNumber)){
                    errorNumber++;
                    continue;
                }else{
                    if (policyNumber.contains(".")){
                        String policy = policyNumber.substring(policyNumber.lastIndexOf('.')+1);
                        if (StringUtils.isBlank(policy) || !policy.equals("0")){
                            errorNumber++;
                            continue;
                        }
                        policyNumber = policyNumber.substring(0,policyNumber.lastIndexOf('.'));
                    }
                    if (!FrequentMethodUtils.validateBatchNumber(policyNumber)){
                        errorNumber++;
                        continue;
                    }
                }
                requestModel.setPolicyNumber(policyNumber);
                String batchNumber = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_THREE));
                if (insuranceType.equals(InsuranceTypeEnum.POLICY.getValue())){
                    requestModel.setBatchNumber("");
                }else if (insuranceType.equals(InsuranceTypeEnum.BATCH.getValue())){
                    if (StringUtils.isBlank(batchNumber)){
                        errorNumber++;
                        continue;
                    }else{
                        if (batchNumber.contains(".")){
                            String batch = batchNumber.substring(batchNumber.lastIndexOf('.')+1);
                            if (StringUtils.isBlank(batch) || !batch.equals("0")){
                                errorNumber++;
                                continue;
                            }
                            batchNumber = batchNumber.substring(0,batchNumber.lastIndexOf('.'));
                        }
                        if (!FrequentMethodUtils.validateBatchNumber(batchNumber)){
                            errorNumber++;
                            continue;
                        }
                    }
                    requestModel.setBatchNumber(batchNumber);
                }
                String grossPreminm = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_FOUR));
                if (StringUtils.isBlank(grossPreminm) || !FrequentMethodUtils.isNumberOrFloatNumberTwo(grossPreminm) || ConverterUtils.toBigDecimal(grossPreminm).compareTo(BigDecimal.ZERO) < 0 || ConverterUtils.toBigDecimal(grossPreminm).compareTo(BigDecimal.ZERO) > 10000000){
                    errorNumber++;
                    continue;
                }
                requestModel.setGrossPremium(ConverterUtils.toBigDecimal(grossPreminm));
                String policyPersonCount = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_FIVE));
                if (StringUtils.isBlank(policyPersonCount) || !FrequentMethodUtils.isNumberOrFloatNumberTwo(policyPersonCount) || ConverterUtils.toBigDecimal(policyPersonCount).compareTo(CommonConstant.BIG_DECIMAL_ONE)<0 ){
                    errorNumber++;
                    continue;
                }
                requestModel.setPolicyPersonCount(ConverterUtils.toBigDecimal(policyPersonCount).intValue());
                String startTime = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_SIX));
                String endTime = ConverterUtils.toString(objects.get(CommonConstant.INTEGER_SEVEN));
                if (StringUtils.isBlank(startTime)){
                    errorNumber++;
                    continue;
                }
                if (StringUtils.isBlank(endTime)){
                    errorNumber++;
                    continue;
                }
                try {
                    Date startDate = MapperUtils.mapperNoDefault(startTime,Date.class);
                    Date endDate = MapperUtils.mapperNoDefault(endTime,Date.class);
                    if (startDate==null||endDate==null||startDate.getTime() > endDate.getTime()){
                        errorNumber++;
                        continue;
                    }
                    requestModel.setStartTime(startDate);
                    requestModel.setEndTime(endDate);
                }catch (Exception e){
                    log.info("",e);
                    errorNumber++;
                    continue;
                }
                importList.add(requestModel);
            }
        }
        model.setErrorNumber(errorNumber);
        model.setImportList(importList);
    }

    /**
     * 根据保单号查询保单完整信息（新增个人意外险页面使用）
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<GetInsuranceByPolicyNumberResponseDto>> getInsuranceByPolicyNumber(@RequestBody @Valid GetInsuranceByPolicyNumberRequestDto requestDto) {
        Result<List<GetInsuranceByPolicyNumberResponseModel>> result = personalAccidentInsuranceServiceApi.getInsuranceByPolicyNumber(MapperUtils.mapper(requestDto,GetInsuranceByPolicyNumberRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (GetInsuranceByPolicyNumberResponseModel model : result.getData()) {
            if(ListUtils.isNotEmpty(model.getBatchNumberList())){
                for (GetInsuranceByBatchNumberResponseModel numberModel : model.getBatchNumberList()) {
                    if(ListUtils.isNotEmpty(numberModel.getTicketsList())){
                        numberModel.getTicketsList().forEach(item->sourceSrcList.add(item.getFilePath()));
                    }
                }
            }
            if(ListUtils.isNotEmpty(model.getTicketsList())){
                model.getTicketsList().forEach(item->sourceSrcList.add(item.getFilePath()));
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),GetInsuranceByPolicyNumberResponseDto.class,new GetInsuranceByPolicyNumberMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

    /**
     * 根据保单号模糊保单号
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<SearchInsuranceByPolicyNumberResponseDto>> searchInsuranceByPolicyNumber(@RequestBody @Valid SearchInsuranceByPolicyNumberRequestDto requestDto) {
        Result<List<SearchInsuranceByPolicyNumberResponseModel>> result = personalAccidentInsuranceServiceApi.searchInsuranceByPolicyNumber(MapperUtils.mapper(requestDto,SearchInsuranceByPolicyNumberRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SearchInsuranceByPolicyNumberResponseDto.class));
    }

    /**
     * 根据保单/批单号模糊查询保单号和金额（关联人数少于保单人数且保费大于0，新增个人意外险页面使用）
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<GetPolicyNoPremiumByPolicyNoResponseDto>> getPolicyNoPremiumByPolicyNo(@RequestBody @Valid SearchInsuranceByPolicyNumberRequestDto requestDto) {
        Result<List<GetPolicyNoPremiumByPolicyNoResponseModel>> result = personalAccidentInsuranceServiceApi.getPolicyNoPremiumByPolicyNo(MapperUtils.mapper(requestDto,SearchInsuranceByPolicyNumberRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (GetPolicyNoPremiumByPolicyNoResponseModel model : result.getData()) {
            if(ListUtils.isNotEmpty(model.getTicketsList())){
                model.getTicketsList().forEach(item->sourceSrcList.add(item.getFilePath()));
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),GetPolicyNoPremiumByPolicyNoResponseDto.class,new GetPolicyNoPremiumByPolicyNoMapping(configKeyConstant.fileAccessAddress,imageMap)));
    }

}
