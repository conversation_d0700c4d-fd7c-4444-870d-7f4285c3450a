package com.logistics.management.webapi.api.feign.entrustaddress;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.entrustaddress.dto.GetAddressByCompanyNameOrWarehouseRequestDto;
import com.logistics.management.webapi.api.feign.entrustaddress.dto.GetAddressByCompanyNameOrWarehouseResponseDto;
import com.logistics.management.webapi.api.feign.entrustaddress.dto.SearchEntrustAddressRequestDto;
import com.logistics.management.webapi.api.feign.entrustaddress.dto.SearchEntrustAddressResponseDto;
import com.logistics.management.webapi.api.feign.entrustaddress.hystrix.EntrustAddressApiHystrix;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2019/9/19 14:29
 */
@Api(value = "API-EntrustAddressApi-委托方地址管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = EntrustAddressApiHystrix.class)
public interface EntrustAddressApi {

    @ApiOperation(value = "发布需求单时，根据公司名或者仓库模糊搜索带出地址")
    @PostMapping(value = "/api/entrustAddress/getAddressByCompanyNameOrWarehouse")
    Result<PageInfo<GetAddressByCompanyNameOrWarehouseResponseDto>> getAddressByCompanyNameOrWarehouse(@RequestBody GetAddressByCompanyNameOrWarehouseRequestDto requestDto);

    @ApiOperation(value = "获取委托方地址列表")
    @PostMapping(value = "/api/entrustAddress/searchList")
    Result<PageInfo<SearchEntrustAddressResponseDto>> searchList(@RequestBody @Valid SearchEntrustAddressRequestDto requestDto);

    @ApiOperation(value = "导出")
    @GetMapping(value = "/api/entrustAddress/export")
    void export(SearchEntrustAddressRequestDto requestDto, HttpServletResponse response);

}
