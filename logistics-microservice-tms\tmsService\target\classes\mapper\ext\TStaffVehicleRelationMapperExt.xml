<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TStaffVehicleRelationMapper">
    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            update t_staff_vehicle_relation
            <set>
                <if test="item.companyCarrierId != null">
                    company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT},
                </if>
                <if test="item.staffId != null">
                    staff_id = #{item.staffId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleId != null">
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.trailerVehicleId != null">
                    trailer_vehicle_Id = #{item.trailerVehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="searchStaffVehicleList" resultType="com.logistics.tms.controller.staffvehiclerelation.response.SearchStaffVehicleListResponseModel">
        select
        tqsv.id                                      as staffVehicleRelationId,
        tqsv.remark                                  as remark,
        tqsv.last_modified_by                        as lastModifiedBy,
        tqsv.last_modified_Time                      as lastModifiedTime,
        tqsv.company_carrier_id                      as companyCarrierId,

        tqsb.id                                      as staffId,
        tqsb.name                                    as staffName,
        tqsb.mobile                                  as staffPhoneNumber,
        tqsb.identity_number                         as identityNumber,
        tsdc.drivers_license_no                      as driversLicenseNo,
        tsdc.occupational_requirements_credential_no as occupationalRequirementsCredentialNo,

        tqvd.vehicle_id                              as vehicleId,
        tqvd.vehicle_no                              as tractorVehicleNo,
        tqvd.brand                                   as brand,
        tqvd.model                                   as model,
        tqvd.total_weight                            as totalWeight,
        tqvd.curb_weight                             as curbWeight,
        tqvd.traction_mass_weight                    as tractionMassWeight,

        tvb.emission_standard_type                   as emissionStandardType,
        tvb.vehicle_property                         as type,

        tqvt.vehicle_category                        as vehicleCategory,
        tqvt.vehicle_type                            as vehicleType,

        tqve.vehicle_id                              as trailerVehicleId,
        tqve.vehicle_no                              as trailerVehicleNo,
        tqve.owner                                   as owner,
        tqve.approved_load_weight                    as approvedLoadWeight
        from t_staff_vehicle_relation tqsv
        left join t_staff_basic tqsb on tqsb.id = tqsv.staff_id and tqsb.valid = 1
        left join t_staff_driver_credential tsdc on tsdc.staff_id = tqsb.id and tsdc.valid = 1
        left join t_vehicle_basic tvb on tvb.id = tqsv.vehicle_id and tvb.valid = 1
        left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqsv.vehicle_id and tqvd.valid = 1
        left join t_vehicle_driving_license tqve on tqve.vehicle_id = tqsv.trailer_vehicle_Id and tqve.valid = 1
        left join t_vehicle_type tqvt on tqvt.id = tqvd.vehicle_type and tqvt.valid = 1
        where tqsv.valid = 1
        <if test="params.companyCarrierIds != null and params.companyCarrierIds != ''">
            and tqsv.company_carrier_id in (${param1.companyCarrierIds})
        </if>
        <if test="params.vehicleIds != null and params.vehicleIds != ''">
            and tqsv.vehicle_id in (${params.vehicleIds})
        </if>
        <if test="params.type != null">
            and tvb.vehicle_property = #{params.type,jdbcType = INTEGER}
        </if>
        <if test="params.vehicleCategory != null">
            and tqvt.vehicle_Category = #{params.vehicleCategory,jdbcType = INTEGER}
        </if>
        <if test="params.staffName != null and params.staffName != ''">
            and (
                        instr(tqsb.name, #{params.staffName,jdbcType = VARCHAR}) > 0
                    or instr(tqsb.mobile, #{params.staffName,jdbcType = VARCHAR}) > 0)
        </if>
        <if test="params.vehicleNo != null and params.vehicleNo != ''">
            and (
                    instr(tqvd.vehicle_no, #{params.vehicleNo,jdbcType = VARCHAR})
                    or instr(tqve.vehicle_no, #{params.vehicleNo,jdbcType = VARCHAR})
                )
        </if>
        <if test="params.staffVehicleIds != null and params.staffVehicleIds != ''">
            and tqsv.id in (${params.staffVehicleIds})
        </if>
        <if test="params.carrierVehicleIds != null and params.carrierVehicleIds != ''">
            and tqsv.vehicle_id in (${param1.carrierVehicleIds})
        </if>
        order by tqsv.last_modified_Time desc, tqsv.id desc
    </select>

    <select id="findRelationByStaffId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_staff_vehicle_relation
        where valid =1
        and staff_id = #{staffId,jdbcType = BIGINT}
    </select>

    <select id="findRelationByVehicleId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_staff_vehicle_relation
        where valid =1
        <if test="vehicleId!=null">
           and vehicle_id = #{vehicleId,jdbcType = BIGINT}
        </if>
    </select>
    <select id="findRelationByTrailerVehicleId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_staff_vehicle_relation
        where valid =1
        <if test="trailerVehicleId!=null">
            and trailer_vehicle_id = #{trailerVehicleId,jdbcType = BIGINT}
        </if>
    </select>

    <select id="findRelationList" resultType="com.logistics.tms.entity.TStaffVehicleRelation">
        select <include refid="Base_Column_List"/>
        from t_staff_vehicle_relation
        where valid =1
    </select>

    <select id="findRelationByPrimaryId" resultType="com.logistics.tms.biz.staffvehiclerelation.model.StaffVehicleRelationModel">
        select
        tqsv.id                 as vehicleRelationId,
        tqsv.staff_id           as staffId,
        tqsv.remark             as remark,
        tqsv.company_carrier_id as companyCarrierId,
        tsb.name                as staffName,
        tsb.mobile              as staffPhone,
        tvb.vehicle_property    as vehicleProperty,

        tqsv.vehicle_Id         as vehicleId,
        tvdl.vehicle_no         as vehicleNo,

        tqsv.trailer_vehicle_Id as trailerVehicleId,
        tvde.vehicle_no         as trailerVehicleNo,

        tvt.vehicle_category    as vehicleCategory
        from t_staff_vehicle_relation tqsv
        left join t_staff_basic tsb on tsb.id = tqsv.staff_id and tsb.valid = 1
        left join t_vehicle_basic tvb on tvb.id = tqsv.vehicle_id and tvb.valid = 1
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tqsv.vehicle_id and tvdl.valid = 1
        left join t_vehicle_type tvt on tvt.id = tvdl.vehicle_type and tvt.valid = 1
        left join t_vehicle_driving_license tvde on tvde.vehicle_id = tqsv.trailer_vehicle_Id and tvde.valid = 1
        where tqsv.valid = 1
        and tqsv.id = #{id,jdbcType = BIGINT}
    </select>


    <select id="findRel4VehicleSafeCheck" resultType="com.logistics.tms.controller.staffvehiclerelation.response.SafeCheckStaffRelResponseModel">
        select
        tqsv.id                 as vehicleRelationId,
        tqsv.staff_id           as staffId,
        tsb.name                as staffName,
        tsb.mobile              as staffPhone,

        tqsv.vehicle_Id         as vehicleId,
        tvdl.vehicle_no         as vehicleNo,

        tqsv.trailer_vehicle_Id as trailerVehicleId,
        tvde.vehicle_no         as trailerVehicleNo
        from t_staff_vehicle_relation tqsv
        left join t_staff_basic tsb on tsb.id = tqsv.staff_id and tsb.valid = 1
        left join t_vehicle_basic tvb on tvb.id = tqsv.vehicle_id and tvb.valid = 1 and tvb.operating_state = 1
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvb.id and tvdl.valid = 1
        left join t_vehicle_type tvt on tvt.id = tvdl.vehicle_type and tvt.valid = 1
        left join t_vehicle_driving_license tvde on tvde.vehicle_id = tqsv.trailer_vehicle_Id and tvde.valid = 1
        where tqsv.valid = 1
        and tsb.staff_property in (1,3)
        and tvb.vehicle_property in (1,3)
        and tvt.vehicle_category in (1, 3)
        <if test="vehicleNo != null and vehicleNo != ''">
            and instr(tvdl.vehicle_no, #{vehicleNo,jdbcType = VARCHAR}) > 0
        </if>
        <if test="vehicleId != null">
            and tvdl.vehicle_id = #{vehicleId,jdbcType = BIGINT}
        </if>
        order by tqsv.last_modified_time desc
    </select>

    <select id="findRelList4SafeCheck" resultType="com.logistics.tms.controller.staffvehiclerelation.response.SafeCheckStaffRelResponseModel">
        select
        tqsv.id                 as vehicleRelationId,
         tqsv.staff_id           as staffId,
         tsb.name                as staffName,
         tsb.mobile              as staffPhone,

         tqsv.vehicle_Id         as vehicleId,
         tvdl.vehicle_no         as vehicleNo,

         tqsv.trailer_vehicle_Id as trailerVehicleId,
         tvde.vehicle_no         as trailerVehicleNo,

         tvb.vehicle_property    as vehicleProperty
        from t_staff_vehicle_relation tqsv
        left join t_staff_basic tsb on tsb.id = tqsv.staff_id and tsb.valid = 1
        left join t_vehicle_basic tvb on tvb.id = tqsv.vehicle_id and tvb.valid = 1 and tvb.operating_state = 1
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvb.id and tvdl.valid = 1
        left join t_vehicle_type tvt on tvt.id = tvdl.vehicle_type and tvt.valid = 1
        left join t_vehicle_driving_license tvde on tvde.vehicle_id = tqsv.trailer_vehicle_Id and tvde.valid = 1
        where tqsv.valid = 1
        and tsb.staff_property in (1, 3)
        and tvb.vehicle_property in (1, 3)
        and tvt.vehicle_category in (1, 3)
        <if test="vehicleIds != null and vehicleIds != ''">
            and tvdl.vehicle_id in (${vehicleIds})
        </if>
        order by tqsv.last_modified_time desc
    </select>

    <select id="getDriverAndVehicleByVehicleNumber" resultType="com.logistics.tms.controller.staffvehiclerelation.response.StaffAndVehicleSearchResponseModel">
        select
        tsvr.id as vehicleStaffRelId,
        tsb.id as driverId,
        tvb.id as vehicleId,
        tvb.vehicle_property as vehicleProperty,
        tsb.name as driverName,
        tsb.mobile as driverPhone,
        tsb.identity_number as driverIdentityNumber,
        tvdl.vehicle_no as vehicleNo
        from t_staff_vehicle_relation tsvr
        left join t_staff_basic tsb on tsvr.staff_id = tsb.id and tsb.valid = 1
        left join t_vehicle_basic tvb on tsvr.vehicle_id = tvb.id and tvb.valid = 1
        left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvb.id and tvdl.valid = 1
        left join t_vehicle_type tvt on tvt.id = tvdl.vehicle_type and tvt.valid = 1
        where tsvr.valid = 1
        and tvb.operating_state = 1
        and tvt.vehicle_category in (1,3)
        <if test="params.driverName!=null and params.driverName!=''">
            and instr(tsb.name,#{params.driverName})
        </if>
        <if test="params.drivePhone!=null and params.drivePhone!=''">
            and instr(tsb.mobile,#{params.drivePhone})
        </if>
        <if test="params.vehicleNo!=null and params.vehicleNo!=''">
            and instr(tvdl.vehicle_no,#{params.vehicleNo})
        </if>
    </select>

    <select id="getDispatchVehicleListByVehicleNo" resultType="com.logistics.tms.controller.staffvehiclerelation.response.StaffAndVehicleSearchResponseModel">
        select
            tsvr.id as vehicleStaffRelId,
            tsb.id as driverId,
            tvb.id as vehicleId,
            tvb.vehicle_property as vehicleProperty,
            tsb.name as driverName,
            tsb.mobile as driverPhone,
            tsb.identity_number as driverIdentityNumber,
            tvd.vehicle_no as vehicleNo
        from
        t_vehicle_driving_license tvd
        left join t_vehicle_type tvt on tvt.id = tvd.vehicle_type and tvt.valid = 1
        left join t_vehicle_basic tvb on tvb.id = tvd.vehicle_id and tvb.valid = 1
        left join t_staff_vehicle_relation tsvr on tsvr.vehicle_id = tvd.vehicle_id and tsvr.valid = 1
        left join t_staff_basic tsb on tsb.id = tsvr.staff_id and tsb.valid = 1
        where  tvd.valid = 1
        and tvb.operating_state = 1
        and ( tvd.vehicle_type = 0 or tvt.vehicle_category in (1,3) ) -- 物流管理同步过来的车,车辆类型默认为0
        <if test="params.vehicleNo!=null and params.vehicleNo!=''">
            and instr(tvd.vehicle_no,#{params.vehicleNo})
        </if>

    </select>

    <select id="getByVehicleIdAndDriverId" resultType="com.logistics.tms.entity.TStaffVehicleRelation">
        select
        <include refid="Base_Column_List"/>
        from t_staff_vehicle_relation
        where valid = 1
        and vehicle_id = #{vehicleId,jdbcType = BIGINT}
        and staff_id = #{staffId,jdbcType=BIGINT}
        and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
    </select>

    <select id="getRelationKeyById" resultType="java.lang.Long">
        select
              tqsv.id as staffVehicleRelationId
        from t_staff_vehicle_relation tqsv
        where tqsv.valid = 1
        and tqsv.staff_id = #{staffId,jdbcType = BIGINT}
        and tqsv.vehicle_id = #{tractorVehicleId,jdbcType = BIGINT}
        <if test="trailerVehicleId!=null">
            and tqsv.trailer_Vehicle_Id = #{trailerVehicleId,jdbcType = BIGINT}
        </if>
        limit 1
    </select>

    <select id="getRelByStaffId" resultType="com.logistics.tms.biz.staffvehiclerelation.model.TStaffVehicleRelationModel">
        select
        tqsv.id as id,
        tqsv.vehicle_id as vehicleId,
        tqsv.staff_id as staffId,
        tqvt.vehicle_category as vehicleCategory,
        tsb.staff_property as vehicleProperty

        from t_staff_vehicle_relation tqsv
        left join t_staff_basic tsb on tsb.id = tqsv.staff_id and tsb.valid = 1
        left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqsv.vehicle_id and tqvd.valid = 1
        left join t_vehicle_type tqvt on tqvt.id = tqvd.vehicle_type and tqvt.valid = 1
        where tqsv.valid = 1
        and tqvt.id is not null
        <if test="staffId!=null">
            and tqsv.staff_id = #{staffId,jdbcType = BIGINT}
        </if>
    </select>

    <select id="getRelByVehicleId" resultType="com.logistics.tms.biz.staffvehiclerelation.model.TStaffVehicleRelationModel">
        select
        tqsv.id as id,
        tqsv.vehicle_id as vehicleId,
        tqsv.staff_id as staffId,
        tvb.vehicle_property as vehicleProperty,
        tqvt.vehicle_category as vehicleCategory

        from t_staff_vehicle_relation tqsv
        left join t_vehicle_basic tvb on tvb.id = tqsv.vehicle_id and tvb.valid = 1
        left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqsv.vehicle_id and tqvd.valid = 1
        left join t_vehicle_type tqvt on tqvt.id = tqvd.vehicle_type and tqvt.valid = 1
        where tqsv.valid = 1
        and tqvt.id is not null
        and tvb.id is not null
        <if test="vehicleId!=null">
            and tqsv.vehicle_id = #{vehicleId,jdbcType = BIGINT}
        </if>
    </select>
    <select id="getRelByTrailerVehicleId" resultType="com.logistics.tms.biz.staffvehiclerelation.model.TStaffVehicleRelationModel">
        select
        tqsv.id as id,
        tqsv.vehicle_id as vehicleId,
        tqsv.staff_id as staffId,
        tvb.vehicle_property as vehicleProperty,
        tqvt.vehicle_category as vehicleCategory

        from t_staff_vehicle_relation tqsv
        left join t_vehicle_basic tvb on tvb.id = tqsv.trailer_vehicle_Id and tvb.valid = 1
        left join t_vehicle_driving_license tqvd on tqvd.vehicle_id = tqsv.trailer_vehicle_Id and tqvd.valid = 1
        left join t_vehicle_type tqvt on tqvt.id = tqvd.vehicle_type and tqvt.valid = 1
        where tqsv.valid = 1
        and tqvt.id is not null
        and tvb.id is not null
        <if test="trailerVehicleId!=null">
            and tqsv.trailer_vehicle_Id = #{trailerVehicleId,jdbcType = BIGINT}
        </if>
    </select>

    <update id="updateByPrimaryKeySelectiveExt" parameterType="com.logistics.tms.entity.TStaffVehicleRelation">
        update t_staff_vehicle_relation
        <set>
            <if test="companyCarrierId != null">
                company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
            </if>
            <if test="staffId != null">
                staff_id = #{staffId,jdbcType=BIGINT},
            </if>
            <if test="vehicleId != null">
                vehicle_id = #{vehicleId,jdbcType=BIGINT},
            </if>
             trailer_vehicle_Id = #{trailerVehicleId,jdbcType=BIGINT},
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TStaffVehicleRelation">
        <foreach collection="recordList" item="item" separator=";">
            update t_staff_vehicle_relation
            <set>
                <if test="item.companyCarrierId != null">
                    company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT},
                </if>
                <if test="item.staffId != null">
                    staff_id = #{item.staffId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleId != null">
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.trailerVehicleId != null">
                    trailer_vehicle_Id = #{item.trailerVehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectRelationsByVehicleIdDriverId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_staff_vehicle_relation
        where valid = 1
        and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        <if test="driverIds != null">
            and staff_id in (${driverIds})
        </if>
        <if test="vehicleIds != null">
            and vehicle_id in (${vehicleIds})
        </if>
    </select>

    <select id="selectRelationsByStaffIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_staff_vehicle_relation
        where valid = 1
          and staff_id in (${driverIds})
    </select>

    <select id="selectRelationsByIdsCarrierId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_staff_vehicle_relation
        where valid = 1
          and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        <choose>
            <when test="ids != null and ids.size() != 0">
                and id in
                <foreach collection="ids" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>

    <select id="selectAllRelationsByVehicleIdDriverId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_staff_vehicle_relation
        where valid = 1
        <if test="driverIds != null">
            and staff_id in (${driverIds})
        </if>
        <if test="vehicleIds != null">
            and vehicle_id in (${vehicleIds})
        </if>
    </select>
</mapper>