package com.logistics.management.webapi.api.impl.driversafepromise.mapping;

import com.logistics.management.webapi.api.feign.driversafepromise.dto.SafePromiseDetailResponseDto;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.tms.api.feign.driversafepromise.model.SafePromiseDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
/**
 * @Author: sj
 * @Date: 2019/11/5 11:34
 */
public class SafePromiseDetailMapping extends MapperMapping<SafePromiseDetailResponseModel,SafePromiseDetailResponseDto> {
    private ConfigKeyConstant configKeyConstant;

    public SafePromiseDetailMapping(ConfigKeyConstant configKeyConstant){
        this.configKeyConstant = configKeyConstant;
    }

    @Override
    public void configure() {
        SafePromiseDetailResponseModel source = this.getSource();
        SafePromiseDetailResponseDto dto = this.getDestination();
        if(source!=null && source.getAttachmentUrl()!=null){
            dto.setAbsoluteAttachmentUrl(configKeyConstant.fileAccessAddress+source.getAttachmentUrl());
        }
    }
}
