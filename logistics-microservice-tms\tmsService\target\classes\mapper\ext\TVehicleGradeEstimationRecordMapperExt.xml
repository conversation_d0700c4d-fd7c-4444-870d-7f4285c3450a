<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleGradeEstimationRecordMapper" >
    <select id="getDueGradeEstimationCount" resultType="java.util.HashMap">
        select
        ifnull(group_concat(if(thrityDueCount = 1, vehicle_id, null)), '') as totalIds,
        ifnull(group_concat(if(sevenDueCount = 1, vehicle_id, null)), '')  as weekIds,
        ifnull(group_concat(if(dueCount = 1, vehicle_id, null)), '')       as hasExpiredIds,
        ifnull(sum(thrityDueCount), 0)                                     as total,
        ifnull(sum(sevenDueCount), 0)                                      as week,
        ifnull(sum(dueCount), 0)                                           as hasExpired
        from (SELECT tcvr.id as vehicle_id,
               if(DATE_SUB(max(estimation_date), INTERVAL #{remindDays,jdbcType=INTEGER} DAY) &lt;= DATE_FORMAT(now(), '%Y-%m-%d') and max(estimation_date) >= DATE_FORMAT(now(), '%Y-%m-%d'), 1,
                  0)                                                                                                                                                    as thrityDueCount,
               if(DATE_SUB(max(estimation_date), INTERVAL 7 DAY) &lt;= DATE_FORMAT(now(), '%Y-%m-%d') and max(estimation_date) >= DATE_FORMAT(now(), '%Y-%m-%d'), 1, 0) as sevenDueCount,
               if(max(estimation_date) &lt; DATE_FORMAT(now(), '%Y-%m-%d'), 1, 0)                                                                                       as dueCount
              from t_vehicle_grade_estimation_record t1
              left join t_vehicle_basic t2 on t2.id = t1.vehicle_id and t2.valid = 1
              left join t_carrier_vehicle_relation tcvr on tcvr.valid = 1 and tcvr.vehicle_id = t2.id and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
              where t1.valid = 1
              and t2.operating_state = 1
              and tcvr.id is not null
              group by vehicle_id) tmp
  </select>
  <select id="getVehicleGradeEstimationRecordByVehicleIds"
          resultMap="VehicleGradeEstimationRecordDetailMap" >
select
  tqvger.id as vehicleGradeEstimationRecordId,
  tqvger.estimation_date as estimationDate,
  tqvger.grade as grade,
  tqvger.remark as remark,
  tqvger.last_Modified_By,
  tqvger.last_modified_time,
  tqcp.id as fileId,
  tqcp.file_path,
  tqcp.upload_user_name,
  tqcp.upload_time
from t_vehicle_grade_estimation_record  tqvger
LEFT JOIN t_certification_pictures tqcp ON tqcp.object_id = tqvger.id AND tqcp.object_type = 11 and tqcp.valid=1
 where tqvger.vehicle_id in (${vehicleIds}) and tqvger.valid = 1
order by tqvger.estimation_date desc,tqvger.id desc
  </select>
  <resultMap id="VehicleGradeEstimationRecordDetailMap" type="com.logistics.tms.controller.vehicleassetmanagement.response.VehicleGradeEstimationListResponseModel">
      <id column="vehicleGradeEstimationRecordId" property="vehicleGradeEstimationId" jdbcType="BIGINT"/>
      <result column="estimationDate" property="estimationDate" jdbcType="TIMESTAMP"/>
      <result column="grade" property="grade" jdbcType="INTEGER"/>
      <result column="last_Modified_By" property="lastModifiedBy" jdbcType="VARCHAR"/>
      <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
      <result column="remark" property="remark" jdbcType="VARCHAR"/>
      <collection property="fileList"
                  ofType="com.logistics.tms.controller.vehicleassetmanagement.response.CertificationPicturesResponseModel">
          <id column="fileId" property="fileId" jdbcType="BIGINT"/>
          <result column="file_path" property="relativeFilepath" jdbcType="VARCHAR"/>
          <result column="upload_user_name" property="uploadUserName" jdbcType="VARCHAR"/>
          <result column="upload_time" property="uploadTime" jdbcType="TIMESTAMP"/>
      </collection>
  </resultMap>
    <update id="batchUpdate">
        <foreach collection="list" separator=";" item="item">
            update t_vehicle_grade_estimation_record
            <set >
                <if test="item.vehicleId != null" >
                    vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.estimationDate != null" >
                    estimation_date = #{item.estimationDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.grade != null" >
                    grade = #{item.grade,jdbcType=INTEGER},
                </if>
                <if test="item.remark!=null" >
                    remark =#{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    
    <select id="countGradeEstimationByDate" resultType="java.lang.Integer">
        select count(1)
        from t_vehicle_grade_estimation_record
        where valid =1
        and vehicle_id = #{vehicleId,jdbcType = BIGINT}
        and estimation_date = #{estmationTime,jdbcType = TIMESTAMP}
    </select>
</mapper>