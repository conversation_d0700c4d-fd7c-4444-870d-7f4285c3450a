<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TOperateLogsMapper" >

  <select id="selectLogsByCondition" resultType="com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel">
    select
    object_id as objectId,
    operate_user_name as operateUserName,
    operate_time as operateTime,
    operate_contents as operateContents,
    operate_type as operateType,
    remark as remark
    from t_operate_logs
    where valid = 1
    <if test="objectType!=null">
      and object_type = #{objectType,jdbcType=INTEGER}
    </if>
    <if test="objectId!=null">
      and object_id = #{objectId,jdbcType=BIGINT}
    </if>
    <if test="operateType!=null">
      and operate_type = #{operateType,jdbcType=INTEGER}
    </if>
    order by id desc
  </select>
  <select id="selectLogsByOperateType" resultType="com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel">
    select
    object_id as objectId,
    operate_user_name as operateUserName,
    operate_time as operateTime,
    operate_contents as operateContents,
    operate_type as operateType,
    remark as remark
    from t_operate_logs
    where valid = 1
    <if test="objectType!=null">
      and object_type = #{objectType,jdbcType=INTEGER}
    </if>
    <if test="objectId!=null">
      and object_id = #{objectId,jdbcType=BIGINT}
    </if>
    <if test="operateType!=null and operateType!=''">
      and operate_type in (${operateType})
    </if>
    order by id desc
  </select>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TOperateLogs">
    <foreach collection="list" item="item" separator=";">
      insert into t_operate_logs
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          id,
        </if>
        <if test="item.objectType != null">
          object_type,
        </if>
        <if test="item.objectId != null">
          object_id,
        </if>
        <if test="item.operateType != null">
          operate_type,
        </if>
        <if test="item.operateContents != null">
          operate_contents,
        </if>
        <if test="item.operateUserName != null">
          operate_user_name,
        </if>
        <if test="item.operateTime != null">
          operate_time,
        </if>
        <if test="item.remark != null">
          remark,
        </if>
        <if test="item.createdBy != null">
          created_by,
        </if>
        <if test="item.createdTime != null">
          created_time,
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time,
        </if>
        <if test="item.valid != null">
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.objectType != null">
          #{item.objectType,jdbcType=INTEGER},
        </if>
        <if test="item.objectId != null">
          #{item.objectId,jdbcType=BIGINT},
        </if>
        <if test="item.operateType != null">
          #{item.operateType,jdbcType=INTEGER},
        </if>
        <if test="item.operateContents != null">
          #{item.operateContents,jdbcType=VARCHAR},
        </if>
        <if test="item.operateUserName != null">
          #{item.operateUserName,jdbcType=VARCHAR},
        </if>
        <if test="item.operateTime != null">
          #{item.operateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.remark != null">
          #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null">
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>
</mapper>