package com.logistics.appapi.controller.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author：wjf
 * @date：2021/4/9 15:11
 */
@Data
public class DriverReconciliationConfirmRequestDto {
    @ApiModelProperty("结算id")
    @NotBlank(message = "id不能为空")
    private String vehicleSettlementId;
    @ApiModelProperty("确认对账：1 是，0 否")
    @NotBlank(message = "请选择确认对账")
    private String confirmReconciliationType;
    @ApiModelProperty("司机确认图片路径")
    private String commitImageUrl;
    @ApiModelProperty("司机对账问题备注")
    private String settlementReasonRemark;
}
