package com.logistics.tms.client.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/3/17 15:20
 */
@Data
public class GetVerifyCodeRequestModel {
    @ApiModelProperty("用户账号，唯一（个人类型是手机号，企业类型是公司code）")
    private String account;
    @ApiModelProperty("用户/企业名称，必须和证件上登记的名称一致")
    private String name;
    @ApiModelProperty("用户证件号，必填，必须和证件上登记的号码一致")
    private String identity;
    @ApiModelProperty("0表示身份证")
    private String identityType="0";
    @ApiModelProperty("获取验证码的手机号")
    private String mobile;
}
