package com.logistics.appapi.controller.reservationorder.mapping;

import com.logistics.appapi.base.enums.CarrierOrderStatusEnum;
import com.logistics.appapi.base.enums.EntrustTypeEnum;
import com.logistics.appapi.base.enums.ReservationTypeEnum;
import com.logistics.appapi.client.reservationorder.response.WaitReservationCarrierOrderListResponseModel;
import com.logistics.appapi.client.reservationorder.response.WaitReservationDetailResponseModel;
import com.logistics.appapi.controller.reservationorder.response.ReservationCarrierOrderListResponseDto;
import com.logistics.appapi.controller.reservationorder.response.WaitReservationDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/22 14:13
 */
public class WaitReservationDetailMapping extends MapperMapping<WaitReservationDetailResponseModel, WaitReservationDetailResponseDto> {
    @Override
    public void configure() {
        WaitReservationDetailResponseModel source = getSource();
        WaitReservationDetailResponseDto destination = getDestination();

        //预约类型
        destination.setReservationTypeLabel(ReservationTypeEnum.getEnumByKey(source.getReservationType()).getValue());

        //数据转换
        ReservationCarrierOrderListResponseDto dto;
        List<ReservationCarrierOrderListResponseDto> carrierOrderDtoList = new ArrayList<>();
        for (WaitReservationCarrierOrderListResponseModel model : source.getOrderList()) {
            dto = MapperUtils.mapper(model, ReservationCarrierOrderListResponseDto.class);

            //需求类型
            dto.setEntrustTypeLabel(EntrustTypeEnum.getEnum(model.getEntrustType()).getValue());

            //运单状态
            dto.setStatusLabel(CarrierOrderStatusEnum.getEnum(model.getStatus()).getValue());

            //运单预计数量
            dto.setExpectAmount(model.getExpectAmount().stripTrailingZeros().toPlainString());


            if (model.getExpectedTime()!=null){
                dto.setExpectedTime(DateUtils.dateToString(model.getExpectedTime(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }


            carrierOrderDtoList.add(dto);
        }
        destination.setOrderList(carrierOrderDtoList);

    }
}
