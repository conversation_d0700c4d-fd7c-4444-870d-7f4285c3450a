package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2019/6/6 9:48
 */
public enum StaffDriverCertificateEnum {

    DEFAULT(0,""),
    STAFF_IDENTITY_FRONT(1,"身份证人像面"),
    STAFF_IDENTITY_BACK(2,"身份证国徽面"),
    DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_FRONT(3,"从业资格证（卡片）正面"),
    DRIVER_OCCUPATIONAL_REQUIREMENTS_CARD_BACK(4,"从业资格证（卡片）反面"),
    DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_FRONT(5,"从业资格证（纸质）正面"),
    DRIVER_OCCUPATIONAL_REQUIREMENTS_PAPER_BACK(6,"从业资格证（纸质）反面"),
    DRIVER_LICENSE_FRONT(7,"机动车驾驶证正面"),
    ;

    private Integer key;
    private String value;

    StaffDriverCertificateEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
