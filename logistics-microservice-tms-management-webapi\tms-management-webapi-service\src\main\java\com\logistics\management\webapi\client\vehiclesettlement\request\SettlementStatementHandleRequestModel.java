package com.logistics.management.webapi.client.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author:lei.zhu
 * @date:2021/4/9 13:53
 */
@Data
public class SettlementStatementHandleRequestModel {
    @ApiModelProperty(value = "车辆运费账单id")
    private Long vehicleSettlementId;
    @ApiModelProperty(value = "操作类型 1 无需处理  2 修改调整费用  3 重新对账")
    private Integer operatorType;


    @ApiModelProperty("无需处理-理由")
    private String reason;
    @ApiModelProperty("调整费用符号：1 ‘+’， 2 ‘-’ ")
    private Integer adjustCostSymbol;
    @ApiModelProperty("调整费用")
    private BigDecimal adjustCost;
    @ApiModelProperty("调整费用-原因")
    private String adjustRemark;

}
