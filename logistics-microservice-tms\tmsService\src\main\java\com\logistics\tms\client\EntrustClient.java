//package com.logistics.tms.client;
//
//import com.logistics.entrust.api.feign.demandorder.DemandOrderServiceApi;
//import com.logistics.entrust.api.feign.demandorder.model.CancelDemandOrderFromTmsLeYiRequestModel;
//import com.logistics.entrust.api.feign.demandorder.model.CancelDemandOrderVerifyFromTmsLeYiRequestModel;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
///**
// * @author: wjf
// * @date: 2022/9/20 14:50
// */
//@Service
//public class EntrustClient {
//
//    @Autowired
//    private DemandOrderServiceApi entrustDemandOrderServiceApi;
//
//    /**
//     * 托盘取消出入库计划和取消预约需求单查询可不可以取消
//     * @param requestModel
//     */
//    public void cancelDemandOrderVerifyFromTmsLeYi(CancelDemandOrderVerifyFromTmsLeYiRequestModel requestModel){
//        entrustDemandOrderServiceApi.cancelDemandOrderVerifyFromTmsLeYi(requestModel).throwException();
//    }
//
//    /**
//     * 托盘取消出入库计划和取消预约需求单同步取消物流系统
//     * @param requestModel
//     */
//    public void cancelDemandOrderFromTmsLeYi(CancelDemandOrderFromTmsLeYiRequestModel requestModel){
//        entrustDemandOrderServiceApi.cancelDemandOrderFromTmsLeYi(requestModel).throwException();
//    }
//}
