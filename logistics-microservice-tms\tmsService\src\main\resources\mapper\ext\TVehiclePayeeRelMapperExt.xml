<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehiclePayeeRelMapper" >
  <select id="searchVehiclePayeeRelList" resultType="com.logistics.tms.api.feign.vehiclepayeerel.model.SearchVehiclePayeeRelListResponseModel">
    select
    tvpr.id as vehiclePayeeRelId,
    tvpr.remark,
    tvpr.last_modified_by as lastModifiedBy,
    tvpr.last_modified_time as lastModifiedTime,
    tvb.vehicle_property as vehicleProperty,
    tvdl.vehicle_no as vehicleNo,
    tdp.name,
    tdp.mobile,
    tdp.bank_card_no as bankCardNo,
    tdp.identity_no as identityNo,
    tb.bank_name as bankName
    from t_vehicle_payee_rel tvpr
    left join t_vehicle_basic tvb on tvb.id = tvpr.vehicle_id and tvb.valid = 1
    left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvb.id and tvdl.valid = 1
    left join t_driver_payee tdp on tdp.id = tvpr.driver_payee_id and tdp.valid = 1
    left join t_bank tb on tb.id = tdp.bank_id and tb.valid = 1
    where tvpr.valid = 1
    <if test="params.vehicleNo != null and params.vehicleNo != ''">
      and instr(tvdl.vehicle_no,#{params.vehicleNo,jdbcType=VARCHAR})
    </if>
    <if test="params.driverPayee != null and params.driverPayee != ''">
      and (instr(tdp.name,#{params.driverPayee,jdbcType=VARCHAR})
      or instr(tdp.mobile,#{params.driverPayee,jdbcType=VARCHAR})
      or instr(tdp.bank_card_no,#{params.driverPayee,jdbcType=VARCHAR})
      or instr(tb.bank_name,#{params.driverPayee,jdbcType=VARCHAR}))
    </if>
    <if test="params.lastModifiedBy != null and params.lastModifiedBy != ''">
      and instr(tvpr.last_modified_by,#{params.lastModifiedBy,jdbcType=VARCHAR})
    </if>
    <if test="params.lastModifiedTimeStart != null and params.lastModifiedTimeStart != ''">
      and tvpr.last_modified_time &gt;= DATE_FORMAT(#{params.lastModifiedTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
    </if>
    <if test="params.lastModifiedTimeEnd != null and params.lastModifiedTimeEnd != ''">
      and tvpr.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>
    order by tvpr.last_modified_time desc,tvpr.id desc
  </select>

  <select id="getVehiclePayeeRelDetail" resultType="com.logistics.tms.api.feign.vehiclepayeerel.model.VehiclePayeeRelDetailResponseModel">
    select
    tvpr.id as vehiclePayeeRelId,
    tvpr.vehicle_id as vehicleId,
    tvpr.driver_payee_id as driverPayeeId,
    tvpr.remark,
    tvdl.vehicle_no as vehicleNo,
    tdp.name,
    tdp.mobile,
    tdp.bank_card_no as bankCardNo,
    tb.bank_name as bankName
    from t_vehicle_payee_rel tvpr
    left join t_vehicle_driving_license tvdl on tvdl.vehicle_id = tvpr.vehicle_id  and tvdl.valid = 1
    left join t_driver_payee tdp on tdp.id = tvpr.driver_payee_id and tdp.valid = 1
    left join t_bank tb on tb.id = tdp.bank_id and tb.valid = 1
    where tvpr.valid = 1
    and tvpr.id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="getByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_vehicle_payee_rel
    where valid = 1
    and id in (${ids})
  </select>

  <select id="getByVehicleId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_vehicle_payee_rel
    where valid = 1
    and vehicle_id = #{vehicleId,jdbcType=BIGINT}
    limit 1
  </select>

  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TVehiclePayeeRel" >
    <foreach collection="list" item="item" separator=";">
      insert into t_vehicle_payee_rel
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.vehicleId != null" >
          vehicle_id,
        </if>
        <if test="item.driverPayeeId != null" >
          driver_payee_id,
        </if>
        <if test="item.remark != null" >
          remark,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.vehicleId != null" >
          #{item.vehicleId,jdbcType=BIGINT},
        </if>
        <if test="item.driverPayeeId != null" >
          #{item.driverPayeeId,jdbcType=BIGINT},
        </if>
        <if test="item.remark != null" >
          #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="com.logistics.tms.entity.TVehiclePayeeRel" >
    <foreach collection="list" item="item" separator=";">
      update t_vehicle_payee_rel
      <set >
        <if test="item.vehicleId != null" >
          vehicle_id = #{item.vehicleId,jdbcType=BIGINT},
        </if>
        <if test="item.driverPayeeId != null" >
          driver_payee_id = #{item.driverPayeeId,jdbcType=BIGINT},
        </if>
        <if test="item.remark != null" >
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>


  <select id="getByDriverPayeeId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from t_vehicle_payee_rel
    where valid =  1
    and driver_payee_id in (${driverPayeeIds})
  </select>
</mapper>