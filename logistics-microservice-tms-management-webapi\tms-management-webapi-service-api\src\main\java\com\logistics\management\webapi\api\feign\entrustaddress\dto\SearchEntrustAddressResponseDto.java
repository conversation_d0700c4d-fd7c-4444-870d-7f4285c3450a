package com.logistics.management.webapi.api.feign.entrustaddress.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: wjf
 * @date: 2018/12/21 15:47
 */
@Data
public class SearchEntrustAddressResponseDto implements Serializable {
    @ApiModelProperty("货主性质: 1 企业 2 个人")
    private String type="";
    @ApiModelProperty("货主性质文本")
    private String typeLabel="";
    @ApiModelProperty("地址id")
    private String entrustAddressId="";
    @ApiModelProperty("货主公司名称")
    private String companyEntrustName="";
    @ApiModelProperty("购货公司名称")
    private String companyName="";
    @ApiModelProperty("添加账号")
    private String addUserPhone="";
    @ApiModelProperty("地址")
    private String address="";
    @ApiModelProperty("仓库")
    private String warehouse="";
    @ApiModelProperty("联系人")
    private String contactName="";
    @ApiModelProperty("联系方式")
    private String contactMobile="";
    @ApiModelProperty("最后操作人")
    private String lastModifiedBy="";
    @ApiModelProperty("最后操作时间")
    private String lastModifiedTime="";
    @ApiModelProperty(value = "创建人")
    private String createdBy;
}
