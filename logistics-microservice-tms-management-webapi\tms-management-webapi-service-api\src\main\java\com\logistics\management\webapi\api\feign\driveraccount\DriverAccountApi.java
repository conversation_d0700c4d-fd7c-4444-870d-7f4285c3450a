package com.logistics.management.webapi.api.feign.driveraccount;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.driveraccount.dto.request.*;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.DriverAccountDetailResponseDto;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.DriverAccountImageResponseDto;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.DriverAccountOperateLogResponseDto;
import com.logistics.management.webapi.api.feign.driveraccount.dto.response.SearchDriverAccountResponseDto;
import com.logistics.management.webapi.api.feign.driveraccount.hystrix.DriverAccountApiHystrix;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@Api(value = "API-DriverAccountApi", tags = "司机账户管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = DriverAccountApiHystrix.class)
public interface DriverAccountApi {

    @ApiOperation(value = "新增/修改司机账户", tags = "1.2.5")
    @PostMapping("/api/driverAccount/addAccount")
    Result<Boolean> addAccount(@RequestBody @Valid DriverAccountAddRequestDto requestDto);

    @ApiOperation(value = "司机账户列表", tags = "1.2.5")
    @PostMapping("/api/driverAccount/searchList")
    Result<PageInfo<SearchDriverAccountResponseDto>> searchList(@RequestBody SearchDriverAccountRequestDto requestDto);

    @ApiOperation(value = "司机账户详情", tags = "1.2.5")
    @PostMapping("/api/driverAccount/getDetail")
    Result<DriverAccountDetailResponseDto> getDetail(@RequestBody DriverAccountDetailRequestDto requestDto);

    @ApiOperation(value = "查询司机账户收款证件", tags = "1.2.5")
    @PostMapping("/api/driverAccount/getAccountImageList")
    Result<DriverAccountImageResponseDto> getAccountImageList(@RequestBody @Valid DriverAccountImageRequestDto requestDto);

    @ApiOperation(value = "操作日志列表", tags = "1.2.5")
    @PostMapping("/api/driverAccount/getOperateLogList")
    Result<List<DriverAccountOperateLogResponseDto>> getOperateLogList(@RequestBody @Valid DriverAccountOperateLogRequestDto requestDto);

}
