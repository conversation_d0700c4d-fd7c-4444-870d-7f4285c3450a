<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleSourceMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleSource" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR" />
    <result column="approved_load_weight" property="approvedLoadWeight" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="contact_name" property="contactName" jdbcType="VARCHAR" />
    <result column="contact_mobile" property="contactMobile" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, vehicle_no, approved_load_weight, type, contact_name, contact_mobile, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_source
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_vehicle_source
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleSource" >
    insert into t_vehicle_source (id, vehicle_no, approved_load_weight, 
      type, contact_name, contact_mobile, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{vehicleNo,jdbcType=VARCHAR}, #{approvedLoadWeight,jdbcType=INTEGER}, 
      #{type,jdbcType=INTEGER}, #{contactName,jdbcType=VARCHAR}, #{contactMobile,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleSource" >
    insert into t_vehicle_source
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="vehicleNo != null" >
        vehicle_no,
      </if>
      <if test="approvedLoadWeight != null" >
        approved_load_weight,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="contactName != null" >
        contact_name,
      </if>
      <if test="contactMobile != null" >
        contact_mobile,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null" >
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="approvedLoadWeight != null" >
        #{approvedLoadWeight,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="contactName != null" >
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactMobile != null" >
        #{contactMobile,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleSource" >
    update t_vehicle_source
    <set >
      <if test="vehicleNo != null" >
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="approvedLoadWeight != null" >
        approved_load_weight = #{approvedLoadWeight,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="contactName != null" >
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactMobile != null" >
        contact_mobile = #{contactMobile,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleSource" >
    update t_vehicle_source
    set vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      approved_load_weight = #{approvedLoadWeight,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_mobile = #{contactMobile,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>