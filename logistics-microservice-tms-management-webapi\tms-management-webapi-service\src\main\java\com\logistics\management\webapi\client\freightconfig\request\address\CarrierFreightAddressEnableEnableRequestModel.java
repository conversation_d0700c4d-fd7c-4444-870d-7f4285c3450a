package com.logistics.management.webapi.client.freightconfig.request.address;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightAddressEnableEnableRequestModel extends CarrierFreightAddressEnableRequestModel {

    @ApiModelProperty(value = "启用禁用; 1 启用 0 禁用")
    private Integer enable;
}
