package com.logistics.tms.api.feign.entrustsettlement;

import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.entrustsettlement.hystrix.EntrustSettlementServiceApiHystrix;
import com.logistics.tms.api.feign.entrustsettlement.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/11 18:58
 */
@Api(value = "API-EntrustSettlementServiceApi-委托方运费结算管理")
@FeignClient(name = "logistics-tms-services", fallback = EntrustSettlementServiceApiHystrix.class)
public interface EntrustSettlementServiceApi {

    @ApiOperation(value = "查询委托方结算列表")
    @PostMapping(value = "/service/settlement/entrust/entrustSettlementList")
    Result<EntrustSettlementListResponseModel> entrustSettlementList(@RequestBody EntrustSettlementListRequestModel requestModel);

    @ApiOperation(value = "导出委托方结算列表")
    @PostMapping(value = "/service/settlement/entrust/exportEntrustSettlementList")
    Result<List<EntrustSettlementRowModel>> exportEntrustSettlementList(@RequestBody EntrustSettlementListRequestModel requestModel);

    @ApiOperation(value = "修改费用")
    @PostMapping(value = "/service/settlement/entrust/modifyCost")
    Result modifyCost(@RequestBody ModifyCostRequestModel requestModel);

    @ApiOperation(value = "结算详情，确认收款，回退界面")
    @PostMapping(value = "/service/settlement/entrust/getSettlementDetail")
    Result<GetSettlementDetailResponseModel> getSettlementDetail(@RequestBody GetSettlementDetailRequestModel requestModel);

    @ApiOperation(value = "结算详情，修改费用")
    @PostMapping(value = "/service/settlement/entrust/getDetail")
    Result<GetDetailResponseModel> getDetail(@RequestBody GetDetailRequestModel requestModel);

    @ApiOperation(value = "已收款")
    @PostMapping(value = "/service/settlement/entrust/receiveMoney")
    Result receiveMoney(@RequestBody GetSettlementDetailRequestModel requestModel);

    @ApiOperation(value = "退款")
    @PostMapping(value = "/service/settlement/entrust/refund")
    Result refund(@RequestBody RefundRequestModel requestModel);
}
