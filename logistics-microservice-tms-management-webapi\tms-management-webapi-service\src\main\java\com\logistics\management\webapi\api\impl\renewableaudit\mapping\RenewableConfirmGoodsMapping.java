package com.logistics.management.webapi.api.impl.renewableaudit.mapping;

import com.logistics.management.webapi.api.feign.renewableaudit.dto.RenewableConfirmGoodsResponseDto;
import com.logistics.management.webapi.api.feign.renewableaudit.dto.RenewableGoodsResponseDto;
import com.logistics.management.webapi.api.feign.renewableaudit.dto.RenewablePhotosResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CertificationPicturesFileTypeEnum;
import com.logistics.management.webapi.base.enums.RenewableGoodsUnitEnum;
import com.logistics.management.webapi.base.enums.RenewableSourceTypeEnum;
import com.logistics.tms.api.feign.renewableaudit.model.RenewableAuditGoodsResponseModel;
import com.logistics.tms.api.feign.renewableaudit.model.RenewableConfirmGoodsResponseModel;
import com.logistics.tms.api.feign.renewableaudit.model.RenewableTicketsUrlResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class RenewableConfirmGoodsMapping extends MapperMapping<RenewableConfirmGoodsResponseModel, RenewableConfirmGoodsResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;

    public RenewableConfirmGoodsMapping(String imagePrefix, Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap = imageMap;
    }

    @Override
    public void configure() {
        RenewableConfirmGoodsResponseModel source = getSource();
        RenewableConfirmGoodsResponseDto destination = getDestination();
        destination.setStaffName(source.getStaffName()+" "+source.getVehicleNo());
        List<RenewableAuditGoodsResponseModel> confirmGoodsList = source.getConfirmGoodsList();
        //货物信息转换
        if(ListUtils.isNotEmpty(confirmGoodsList)){
            List<RenewableGoodsResponseDto> confirmGoods = new ArrayList<>();
            confirmGoodsList.forEach(goods->{
                RenewableGoodsResponseDto renewableGoodsResponseDto = new RenewableGoodsResponseDto();
                renewableGoodsResponseDto.setSkuCode(goods.getSkuCode());
                renewableGoodsResponseDto.setGoodsName(goods.getGoodsName());
                //如果是客户下单
                if(RenewableSourceTypeEnum.YELOLIFE_SYNC.getKey().equals(goods.getGoodsSourceType())){
                    if (goods.getGoodsUnit().equals(RenewableGoodsUnitEnum.PIECE.getKey())) {
                        renewableGoodsResponseDto.setGoodsAmount(goods.getGoodsAmount().stripTrailingZeros().toPlainString()+RenewableGoodsUnitEnum.PIECE.getValue());
                        renewableGoodsResponseDto.setVerifiedGoodsAmount(CommonConstant.BLANK_TEXT);
                        renewableGoodsResponseDto.setGoodsPrice(CommonConstant.BLANK_TEXT);
                    } else if (goods.getGoodsUnit().equals(RenewableGoodsUnitEnum.KILOGRAM.getKey())) {
                        renewableGoodsResponseDto.setGoodsAmount(goods.getGoodsAmount().stripTrailingZeros().toPlainString()+RenewableGoodsUnitEnum.KILOGRAM.getValue());
                        renewableGoodsResponseDto.setVerifiedGoodsAmount(goods.getGoodsAmount().stripTrailingZeros().toPlainString());
                        renewableGoodsResponseDto.setGoodsPrice(ConverterUtils.toString(goods.getGoodsPrice()));
                    }
                //如果是司机确认
                }else if(RenewableSourceTypeEnum.DRIVER_CONFIRM.getKey().equals(goods.getGoodsSourceType())){
                    renewableGoodsResponseDto.setVerifiedGoodsAmount(goods.getGoodsAmount().stripTrailingZeros().toPlainString());
                    renewableGoodsResponseDto.setGoodsPrice(ConverterUtils.toString(goods.getGoodsPrice()));
                }
                confirmGoods.add(renewableGoodsResponseDto);
            });
            destination.setConfirmGoodsList(confirmGoods);
        }
        //图片信息转换
        if(ListUtils.isNotEmpty(source.getPhotosList())){
            List<RenewableTicketsUrlResponseModel> ticketsUrlResponseModels = source.getPhotosList();
            ticketsUrlResponseModels.forEach(tickets->{
                List<RenewablePhotosResponseDto> list = new ArrayList<>();
                RenewablePhotosResponseDto renewablePhotosResponseDto = new RenewablePhotosResponseDto();
                renewablePhotosResponseDto.setFilePath(imagePrefix + imageMap.get(tickets.getFilePath()));
                list.add(renewablePhotosResponseDto);
                if(CertificationPicturesFileTypeEnum.T_RENEWABLE_AUDIT_SCENE_PICTURE_FILE.getFileType().equals(tickets.getFileType())){
                    destination.setScenePhotosList(list);
                }else if(CertificationPicturesFileTypeEnum.T_RENEWABLE_AUDIT_CONFIRM_PICTURE_FILE.getFileType().equals(tickets.getFileType())){
                    destination.setOrderPhotosList(list);
                }
            });
        }
    }
}
