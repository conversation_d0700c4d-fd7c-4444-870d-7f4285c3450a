package com.logistics.management.webapi.controller.biddingorder.mapping;

import com.logistics.management.webapi.client.biddingorder.response.SearchBiddingOrderListResponseModel;
import com.logistics.management.webapi.controller.biddingorder.response.SearchBiddingOrderListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * <AUTHOR>
 * @date 2024/05/11
 */
public class SearchBiddingOrderListMapping extends MapperMapping<SearchBiddingOrderListResponseModel, SearchBiddingOrderListResponseDto> {
    @Override
    public void configure() {
        SearchBiddingOrderListResponseDto destination = getDestination();
        SearchBiddingOrderListResponseModel source = getSource();
        if(source!=null){
            destination.setVehicleLength(null!=source.getVehicleLength()?source.getVehicleLength().stripTrailingZeros().toPlainString():"");
            destination.setGoodsCount(null!=source.getGoodsCount()?source.getGoodsCount().toPlainString():"");
        }
    }
}
