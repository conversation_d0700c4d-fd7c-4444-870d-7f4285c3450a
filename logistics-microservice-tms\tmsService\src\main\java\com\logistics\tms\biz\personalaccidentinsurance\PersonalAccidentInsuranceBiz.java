package com.logistics.tms.biz.personalaccidentinsurance;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TCertificationPictures;
import com.logistics.tms.entity.TInsuranceCompany;
import com.logistics.tms.entity.TPersonalAccidentInsurance;
import com.logistics.tms.mapper.TCertificationPicturesMapper;
import com.logistics.tms.mapper.TInsuranceCompanyMapper;
import com.logistics.tms.mapper.TPersonalAccidentInsuranceMapper;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2019/5/30 14:11
 */
@Service
public class PersonalAccidentInsuranceBiz {

    @Autowired
    private TPersonalAccidentInsuranceMapper personalAccidentInsuranceMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCertificationPicturesMapper certificationPicturesMapper;
    @Autowired
    private TInsuranceCompanyMapper insuranceCompanyMapper;

    /**
     * 获取个人意外险列表
     * @param requestModel
     * @return
     */
    public PageInfo<PersonalAccidentInsuranceListResponseModel> searchPersonalAccidentInsuranceList(PersonalAccidentInsuranceListRequestModel requestModel) {
        List<Long> idList = personalAccidentInsuranceMapper.searchPersonalAccidentInsuranceIds(requestModel);
        PageInfo pageInfo = new PageInfo(idList);
        pageInfo.setList(new ArrayList());
        if (ListUtils.isNotEmpty(idList)){
            List<PersonalAccidentInsuranceListResponseModel> insuranceList = personalAccidentInsuranceMapper.searchPersonalAccidentInsuranceList(StringUtils.listToString(idList,','));
            if (ListUtils.isEmpty(insuranceList)){
                insuranceList = new ArrayList<>();
            }
            pageInfo.setList(insuranceList);
        }
        return pageInfo;
    }

    /**
     * 查询个人意外险详情
     * @param requestModel
     * @return
     */
    public PersonalAccidentInsuranceDetailResponseModel getPersonalAccidentInsuranceDetail(PersonalAccidentInsuranceIdRequestModel requestModel) {
        return personalAccidentInsuranceMapper.getPersonalAccidentInsuranceDetail(requestModel.getPersonalAccidentInsuranceId());
    }

    /**
     * 新增/修改个人意外险
     * @param requestModel
     */
    @Transactional
    public void addOrModifyPersonalAccidentInsurance(AddOrModifyPersonalAccidentInsuranceRequestModel requestModel) {
        if (requestModel.getStartTime().getTime() > requestModel.getEndTime().getTime()){
            throw new BizException(CarrierDataExceptionEnum.START_TIME_DA_YU_END_TIME);
        }
        TPersonalAccidentInsurance personalAccidentInsurance = new TPersonalAccidentInsurance();
        personalAccidentInsurance.setInsuranceCompanyId(requestModel.getInsuranceCompanyId());
        if(CommonConstant.INTEGER_TWO.equals(requestModel.getInsuranceType())&&(requestModel.getPersonalAccidentInsuranceId() == null && requestModel.getPersonalAccidentInsuranceId() <= CommonConstant.LONG_ZERO)){
            throw new BizException(CarrierDataExceptionEnum.INSURANCE_NOT_EXIST);
        }else{
            personalAccidentInsurance.setPersonalAccidentInsuranceId(requestModel.getPolicyNumberInsuranceId());
        }
        personalAccidentInsurance.setType(requestModel.getInsuranceType());
        personalAccidentInsurance.setPolicyNumber(requestModel.getPolicyNumber());
        personalAccidentInsurance.setBatchNumber(requestModel.getBatchNumber());
        personalAccidentInsurance.setGrossPremium(requestModel.getGrossPremium());
        personalAccidentInsurance.setPolicyPersonCount(requestModel.getPolicyPersonCount());
        personalAccidentInsurance.setStartTime(requestModel.getStartTime());
        personalAccidentInsurance.setEndTime(requestModel.getEndTime());
        String userName = BaseContextHandler.getUserName();
        Date now = new Date();
        Long personalAccidentInsuranceId = requestModel.getPersonalAccidentInsuranceId();
        TPersonalAccidentInsurance tqPersonalAccidentInsurance = personalAccidentInsuranceMapper.getByTypePolicyNumber(requestModel.getInsuranceType(),requestModel.getPolicyNumber(),requestModel.getBatchNumber());
        if (requestModel.getPersonalAccidentInsuranceId() != null && requestModel.getPersonalAccidentInsuranceId() > CommonConstant.LONG_ZERO){//修改
            if (tqPersonalAccidentInsurance != null && !tqPersonalAccidentInsurance.getId().equals(requestModel.getPersonalAccidentInsuranceId())){
                throw new BizException(CarrierDataExceptionEnum.PERSONAL_ACCIDENT_INSURANCE_EXIST);
            }
            if (tqPersonalAccidentInsurance == null){
                tqPersonalAccidentInsurance = personalAccidentInsuranceMapper.selectByPrimaryKey(requestModel.getPersonalAccidentInsuranceId());
            }
            personalAccidentInsurance.setId(requestModel.getPersonalAccidentInsuranceId());
            commonBiz.setBaseEntityModify(personalAccidentInsurance,userName);

            List<TPersonalAccidentInsurance> upInsuranceList = new ArrayList<>();
            upInsuranceList.add(personalAccidentInsurance);

            if (requestModel.getInsuranceType().equals(InsuranceTypeEnum.POLICY.getKey()) && (!requestModel.getPolicyNumber().equals(tqPersonalAccidentInsurance.getPolicyNumber()) || !requestModel.getInsuranceCompanyId().equals(tqPersonalAccidentInsurance.getInsuranceCompanyId()))) {
                List<TPersonalAccidentInsurance> batchInsuranceList = personalAccidentInsuranceMapper.getBatchInsuranceById(requestModel.getPersonalAccidentInsuranceId());
                if (ListUtils.isNotEmpty(batchInsuranceList)){
                    for (TPersonalAccidentInsurance batch:batchInsuranceList) {
                        personalAccidentInsurance = new TPersonalAccidentInsurance();
                        personalAccidentInsurance.setId(batch.getId());
                        personalAccidentInsurance.setInsuranceCompanyId(requestModel.getInsuranceCompanyId());
                        personalAccidentInsurance.setPolicyNumber(requestModel.getPolicyNumber());
                        commonBiz.setBaseEntityModify(personalAccidentInsurance,userName);
                        upInsuranceList.add(personalAccidentInsurance);
                    }
                }
            }
            personalAccidentInsuranceMapper.batchUpdate(upInsuranceList);

            //删除或修改保单票据
            List<TCertificationPictures> ticketList = certificationPicturesMapper.getByObjectIdType(requestModel.getPersonalAccidentInsuranceId(), CertificationPicturesObjectTypeEnum.T_PERSONAL_ACCIDENT_INSURANCE.getObjectType(),CertificationPicturesFileTypeEnum.PERSONAL_ACCIDENT_INSURANCE.getFileType());
            List<String> ticketPathList = ticketList.stream().map(TCertificationPictures::getFilePath).collect(Collectors.toList());
            List<Long> ticketIdList = ticketList.stream().map(TCertificationPictures::getId).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(requestModel.getTicketsList())){
                List<PersonalAccidentInsuranceTicketsRequestModel> ticketsListUpdate = requestModel.getTicketsList().stream().filter(item -> item.getTicketId() != null && item.getTicketId() > CommonConstant.LONG_ZERO).collect(Collectors.toList());
                List<PersonalAccidentInsuranceTicketsRequestModel> ticketsListRequest = requestModel.getTicketsList().stream().filter(item -> item.getTicketId() == null || item.getTicketId().equals(CommonConstant.LONG_ZERO)).collect(Collectors.toList());
                requestModel.setTicketsList(ticketsListRequest);
                TCertificationPictures certificationPictures;
                List<TCertificationPictures> upList = new ArrayList<>();
                List<Long> idList = new ArrayList<>();
                if (ListUtils.isNotEmpty(ticketsListUpdate)){
                    for (PersonalAccidentInsuranceTicketsRequestModel ticket:ticketsListUpdate) {
                        if (!ticketPathList.contains(ticket.getFilePath())){
                            certificationPictures = new TCertificationPictures();
                            certificationPictures.setId(ticket.getTicketId());
                            certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.PERSONAL_ACCIDENT_INSURANCE.getKey(),"",ticket.getFilePath(),null));
                            commonBiz.setBaseEntityModify(certificationPictures,userName);
                            upList.add(certificationPictures);
                        }
                        idList.add(ticket.getTicketId());
                    }
                    if (ListUtils.isNotEmpty(idList)){
                        ticketIdList.removeAll(idList);
                        if (ListUtils.isNotEmpty(ticketIdList)){
                            for (Long id:ticketIdList) {
                                certificationPictures = new TCertificationPictures();
                                certificationPictures.setId(id);
                                certificationPictures.setValid(CommonConstant.INTEGER_ZERO);
                                commonBiz.setBaseEntityModify(certificationPictures,userName);
                                upList.add(certificationPictures);
                            }
                        }
                    }
                }else{
                    for (TCertificationPictures ticket:ticketList) {
                        certificationPictures = new TCertificationPictures();
                        certificationPictures.setId(ticket.getId());
                        certificationPictures.setValid(CommonConstant.INTEGER_ZERO);
                        commonBiz.setBaseEntityModify(certificationPictures,userName);
                        upList.add(certificationPictures);
                    }
                }
                if (ListUtils.isNotEmpty(upList)){
                    certificationPicturesMapper.batchUpdate(upList);
                }
            }
        }else{//新增
            if (tqPersonalAccidentInsurance != null){
                throw new BizException(CarrierDataExceptionEnum.PERSONAL_ACCIDENT_INSURANCE_EXIST);
            }
            personalAccidentInsurance.setSource(CommonConstant.INTEGER_ONE);
            personalAccidentInsurance.setAddUserId(BaseContextHandler.getUserId());
            personalAccidentInsurance.setAddUserName(userName);
            commonBiz.setBaseEntityAdd(personalAccidentInsurance,userName);
            personalAccidentInsuranceMapper.insertSelective(personalAccidentInsurance);
            personalAccidentInsuranceId = personalAccidentInsurance.getId();
        }
        //新增保单票据
        if (ListUtils.isNotEmpty(requestModel.getTicketsList())){
            TCertificationPictures certificationPictures;
            List<TCertificationPictures> addList = new ArrayList<>();
            for (PersonalAccidentInsuranceTicketsRequestModel ticket:requestModel.getTicketsList()) {
                certificationPictures = new TCertificationPictures();
                certificationPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_PERSONAL_ACCIDENT_INSURANCE.getObjectType());
                certificationPictures.setObjectId(personalAccidentInsuranceId);
                certificationPictures.setFileType(CertificationPicturesFileTypeEnum.PERSONAL_ACCIDENT_INSURANCE.getFileType());
                certificationPictures.setFileTypeName(CertificationPicturesFileTypeEnum.PERSONAL_ACCIDENT_INSURANCE.getFileName());
                certificationPictures.setFileName(CertificationPicturesFileTypeEnum.PERSONAL_ACCIDENT_INSURANCE.getFileName());
                certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.PERSONAL_ACCIDENT_INSURANCE.getKey(),"",ticket.getFilePath(),null));
                certificationPictures.setUploadUserName(userName);
                certificationPictures.setUploadTime(now);
                certificationPictures.setSuffix(ticket.getFilePath().substring(ticket.getFilePath().lastIndexOf('.')));
                commonBiz.setBaseEntityAdd(certificationPictures,userName);
                addList.add(certificationPictures);
            }
            if (ListUtils.isNotEmpty(addList)){
                certificationPicturesMapper.batchInsert(addList);
            }
        }
    }

    /**
     * 根据保单号查询保单完整信息（新增个人意外险页面使用）
     * @param requestModel
     * @return
     */
    public List<GetInsuranceByPolicyNumberResponseModel> getInsuranceByPolicyNumber(GetInsuranceByPolicyNumberRequestModel requestModel){
        List<GetInsuranceByPolicyNumberResponseModel> list = personalAccidentInsuranceMapper.getInsuranceByPolicyNumber(requestModel.getInsuranceType(),requestModel.getPolicyNumber());
        if (ListUtils.isEmpty(list)){
            list = new ArrayList<>();
        }
        return list;
    }

    /**
     * 根据保单号模糊保单号
     * @param requestModel
     * @return
     */
    public List<SearchInsuranceByPolicyNumberResponseModel> searchInsuranceByPolicyNumber(SearchInsuranceByPolicyNumberRequestModel requestModel){
        List<SearchInsuranceByPolicyNumberResponseModel> list = personalAccidentInsuranceMapper.searchInsuranceByPolicyNumber(requestModel.getPolicyNumber());
        if (ListUtils.isEmpty(list)){
            list = new ArrayList<>();
        }
        return list;
    }

    /**
     * 根据保单/批单号模糊查询保单号和金额（关联人数少于保单人数且保费大于0，新增个人意外险页面使用）
     * @param requestModel
     * @return
     */
    public List<GetPolicyNoPremiumByPolicyNoResponseModel> getPolicyNoPremiumByPolicyNo(SearchInsuranceByPolicyNumberRequestModel requestModel) {
        List<GetPolicyNoPremiumByPolicyNoResponseModel> list = personalAccidentInsuranceMapper.getPolicyNoPremiumByPolicyNo(requestModel.getPolicyNumber());
        if (ListUtils.isEmpty(list)){
            list = new ArrayList<>();
        }
        return list;
    }

    /**
     * 导入个人意外险
     * @param requestModel
     * @return
     */
    @Transactional
    public ImportPersonalAccidentInsuranceResponseModel importPersonalAccidentInsurance(ImportPersonalAccidentInsuranceRequestModel requestModel){
        ImportPersonalAccidentInsuranceResponseModel responseModel = new ImportPersonalAccidentInsuranceResponseModel();
        Integer numberFailures = requestModel.getErrorNumber();
        Integer numberSuccessful = 0;
        String userName = BaseContextHandler.getUserName();
        List<ImportPersonalAccidentInsuranceListRequestModel> policyList = requestModel.getImportList().stream().filter(item -> item.getInsuranceType().equals(InsuranceTypeEnum.POLICY.getKey())).collect(Collectors.toList());
        List<ImportPersonalAccidentInsuranceListRequestModel> batchList = requestModel.getImportList().stream().filter(item -> item.getInsuranceType().equals(InsuranceTypeEnum.BATCH.getKey())).collect(Collectors.toList());
        List<ImportPersonalAccidentInsuranceListRequestModel> importList;
        if (ListUtils.isNotEmpty(policyList)){
            policyList.addAll(batchList);
            importList = policyList;
        }else{
            importList = batchList;
        }
        TPersonalAccidentInsurance tqPersonalAccidentInsurance;
        TPersonalAccidentInsurance personalAccidentInsurance;
        TInsuranceCompany tqInsuranceCompany;
        List<TPersonalAccidentInsurance> upList = new ArrayList<>();
        for (ImportPersonalAccidentInsuranceListRequestModel model:importList) {
            tqInsuranceCompany = insuranceCompanyMapper.findInsuranceCompanyByName(model.getInsuranceCompany());
            if (tqInsuranceCompany == null){
                numberFailures++;
                continue;
            }
            personalAccidentInsurance = new TPersonalAccidentInsurance();
            personalAccidentInsurance.setInsuranceCompanyId(tqInsuranceCompany.getId());
            if (model.getInsuranceType().equals(InsuranceTypeEnum.BATCH.getKey())){
                tqPersonalAccidentInsurance = personalAccidentInsuranceMapper.getByTypePolicyNumber(InsuranceTypeEnum.POLICY.getKey(),model.getPolicyNumber(),"");
                if (tqPersonalAccidentInsurance == null){
                    numberFailures++;
                    continue;
                }
                tqInsuranceCompany = insuranceCompanyMapper.selectByPrimaryKey(tqPersonalAccidentInsurance.getInsuranceCompanyId());
                if (tqInsuranceCompany == null || !tqInsuranceCompany.getId().equals(personalAccidentInsurance.getInsuranceCompanyId())){
                    numberFailures++;
                    continue;
                }
                if (!model.getInsuranceCompany().equals(tqInsuranceCompany.getCompanyName())){//如果批单保险公司名称与保单不一致，则插入批单
                    numberFailures++;
                    continue;
                }
                personalAccidentInsurance.setPersonalAccidentInsuranceId(tqPersonalAccidentInsurance.getId());
            }
            personalAccidentInsurance.setType(model.getInsuranceType());
            personalAccidentInsurance.setPolicyNumber(model.getPolicyNumber());
            personalAccidentInsurance.setBatchNumber(model.getBatchNumber());
            personalAccidentInsurance.setGrossPremium(model.getGrossPremium());
            personalAccidentInsurance.setPolicyPersonCount(model.getPolicyPersonCount());
            personalAccidentInsurance.setStartTime(model.getStartTime());
            personalAccidentInsurance.setEndTime(model.getEndTime());
            personalAccidentInsurance.setSource(CommonConstant.INTEGER_TWO);
            tqPersonalAccidentInsurance = personalAccidentInsuranceMapper.getByTypePolicyNumber(model.getInsuranceType(),model.getPolicyNumber(),model.getBatchNumber());
            if (tqPersonalAccidentInsurance != null){
                personalAccidentInsurance.setId(tqPersonalAccidentInsurance.getId());
                commonBiz.setBaseEntityModify(personalAccidentInsurance,userName);
                upList.add(personalAccidentInsurance);
                if (model.getInsuranceType().equals(InsuranceTypeEnum.POLICY.getKey()) && !tqInsuranceCompany.getId().equals(tqPersonalAccidentInsurance.getInsuranceCompanyId())) {
                    List<TPersonalAccidentInsurance> batchInsuranceList = personalAccidentInsuranceMapper.getBatchInsuranceById(tqPersonalAccidentInsurance.getId());
                    if (ListUtils.isNotEmpty(batchInsuranceList)){
                        for (TPersonalAccidentInsurance batch:batchInsuranceList) {
                            personalAccidentInsurance = new TPersonalAccidentInsurance();
                            personalAccidentInsurance.setId(batch.getId());
                            personalAccidentInsurance.setInsuranceCompanyId(tqInsuranceCompany.getId());
                            commonBiz.setBaseEntityModify(personalAccidentInsurance,userName);
                            upList.add(personalAccidentInsurance);
                        }
                    }
                }
            }else {
                personalAccidentInsurance.setAddUserId(BaseContextHandler.getUserId());
                personalAccidentInsurance.setAddUserName(userName);
                commonBiz.setBaseEntityAdd(personalAccidentInsurance,userName);
                personalAccidentInsuranceMapper.insertSelective(personalAccidentInsurance);
            }
            numberSuccessful++;
        }
        if (ListUtils.isNotEmpty(upList)){
            personalAccidentInsuranceMapper.batchUpdate(upList);
        }
        responseModel.setNumberFailures(numberFailures);
        responseModel.setNumberSuccessful(numberSuccessful);
        return responseModel;
    }
}
