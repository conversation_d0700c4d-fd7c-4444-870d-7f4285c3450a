package com.logistics.tms.client.feign.basicdata.ocr.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class OCRCustomizationIdentifyResponseDto {

    @JsonProperty(value = "filename")
    private String fileName;

    @JsonProperty(value = "iocrdata")
    private List<IOCRIdentifyDataResponseModel> iOcrData;

    @Data
    public static class IOCRIdentifyDataResponseModel {

        @JsonProperty(value = "word_name")
        private String wordName;
        private String word;
    }
}
