package com.logistics.appapi.controller.attendance;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.client.attendance.AttendanceClient;
import com.logistics.appapi.client.attendance.request.*;
import com.logistics.appapi.client.attendance.response.AttendanceClockDetailResponseModel;
import com.logistics.appapi.client.attendance.response.AttendanceHistoryListResponseModel;
import com.logistics.appapi.client.attendance.response.QueryPathByLonAndLatResponseModel;
import com.logistics.appapi.client.attendance.response.UpdateAttendanceHistoryDetailResponseModel;
import com.logistics.appapi.controller.attendance.mappping.AttendanceClockDetailMapping;
import com.logistics.appapi.controller.attendance.mappping.AttendanceHistoryListMapping;
import com.logistics.appapi.controller.attendance.mappping.UpdateAttendanceHistoryDetailMapping;
import com.logistics.appapi.controller.attendance.request.*;
import com.logistics.appapi.controller.attendance.response.AttendanceClockDetailResponseDto;
import com.logistics.appapi.controller.attendance.response.AttendanceHistoryListResponseDto;
import com.logistics.appapi.controller.attendance.response.QueryPathByLonAndLatResponseDto;
import com.logistics.appapi.controller.attendance.response.UpdateAttendanceHistoryDetailResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2024/3/7 11:03
 */
@Api(value = "考勤中心", tags = "考勤中心")
@Slf4j
@RestController
@RequestMapping(value = "/api/driverApplet/attendance")
public class AttendanceController {

    @Resource
    private AttendanceClient attendanceClient;

    /**
     * 考勤打卡
     * @param requestDto
     * @return boolean
     */
    @ApiOperation(value = "考勤打卡", tags = "1.0.6")
    @PostMapping(value = "/attendanceClock")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE, excludeParams = "punchLocation")
    public Result<Boolean> attendanceClock(@RequestBody @Valid AttendanceClockRequestDto requestDto) {
        return attendanceClient.attendanceClock(MapperUtils.mapper(requestDto, AttendanceClockRequestModel.class));
    }

    /**
     * 根据登陆人获取打卡状态
     * @return AttendanceClockDetailResponseDto
     */
    @ApiOperation(value = "根据登陆人获取打卡状态", tags = "1.0.6")
    @PostMapping(value = "/attendanceClockDetail")
    public Result<AttendanceClockDetailResponseDto> attendanceClockDetail() {
        Result<AttendanceClockDetailResponseModel> responseModelResult = attendanceClient.attendanceClockDetail();
        responseModelResult.throwException();
        AttendanceClockDetailResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), AttendanceClockDetailResponseDto.class,
                new AttendanceClockDetailMapping());
        return Result.success(responseDto);
    }

    /**
     * 根据经纬度获取地址
     * @param requestDto
     * @return QueryPathByLonAndLatResponseDto
     */
    @ApiOperation(value = "根据经纬度获取地址", tags = "1.0.6")
    @PostMapping(value = "/queryPathByLonAndLat")
    public Result<QueryPathByLonAndLatResponseDto> queryPathByLonAndLat(@RequestBody @Valid QueryPathByLonAndLatRequestDto requestDto) {
        QueryPathByLonAndLatRequestModel requestModel = MapperUtils.mapper(requestDto, QueryPathByLonAndLatRequestModel.class);
        Result<QueryPathByLonAndLatResponseModel> responseModelResult = attendanceClient.queryPathByLonAndLat(requestModel);
        responseModelResult.throwException();
        QueryPathByLonAndLatResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), QueryPathByLonAndLatResponseDto.class);
        return Result.success(responseDto);
    }

    /**
     * 查询员工考勤历史（以月份为维度）
     * @param requestDto
     * @return AttendanceHistoryListResponseDto
     */
    @ApiOperation(value = "考勤历史列表", tags = "1.0.6")
    @PostMapping(value = "/attendanceHistoryList")
    public Result<AttendanceHistoryListResponseDto> attendanceHistoryList(@RequestBody @Valid AttendanceHistoryListRequestDto requestDto) {
        AttendanceHistoryListRequestModel requestModel = MapperUtils.mapper(requestDto, AttendanceHistoryListRequestModel.class);
        Result<AttendanceHistoryListResponseModel> responseModelResult = attendanceClient.attendanceHistoryList(requestModel);
        responseModelResult.throwException();
        AttendanceHistoryListResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), AttendanceHistoryListResponseDto.class,
                new AttendanceHistoryListMapping());
        return Result.success(responseDto);
    }

    /**
     * 修改考勤
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "修改考勤打卡", tags = "1.0.6")
    @PostMapping(value = "/updateAttendanceHistory")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> updateAttendanceHistory(@RequestBody @Valid UpdateAttendanceHistoryRequestDto requestDto) {
        UpdateAttendanceHistoryRequestModel requestModel = MapperUtils.mapper(requestDto, UpdateAttendanceHistoryRequestModel.class);
        return attendanceClient.updateAttendanceHistory(requestModel);
    }

    /**
     * 变更考勤详情
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "修改考勤打卡详情查询", tags = "1.0.6")
    @PostMapping(value = "/updateAttendanceHistoryDetail")
    public Result<UpdateAttendanceHistoryDetailResponseDto> updateAttendanceHistoryDetail(@RequestBody @Valid UpdateAttendanceHistoryDetailRequestDto requestDto) {
        UpdateAttendanceHistoryDetailRequestModel requestModel = MapperUtils.mapper(requestDto, UpdateAttendanceHistoryDetailRequestModel.class);
        Result<UpdateAttendanceHistoryDetailResponseModel> responseModelResult = attendanceClient.updateAttendanceHistoryDetail(requestModel);
        responseModelResult.throwException();
        UpdateAttendanceHistoryDetailResponseDto responseDto = MapperUtils.mapper(responseModelResult.getData(), UpdateAttendanceHistoryDetailResponseDto.class,
                new UpdateAttendanceHistoryDetailMapping());
        return Result.success(responseDto);
    }

    /**
     * 撤销考勤申请
     * @param requestDto
     * @return
     */
    @ApiOperation(value = "修改考勤打卡撤销", tags = "1.0.6")
    @PostMapping(value = "/cancelUpdateAttendanceHistory")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> cancelUpdateAttendanceHistory(@RequestBody @Valid CancelUpdateAttendanceHistoryRequestDto requestDto) {
        CancelUpdateAttendanceHistoryRequestModel requestModel = MapperUtils.mapper(requestDto, CancelUpdateAttendanceHistoryRequestModel.class);
        return attendanceClient.cancelUpdateAttendanceHistory(requestModel);
    }
}
