package com.logistics.management.webapi.api.feign.driversafemeeting.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/11/4 9:43
 */
@Data
public class DriverSafeMeetingDetailRequestDto extends AbstractPageForm<DriverSafeMeetingDetailRequestDto> {
    @ApiModelProperty(value = "学习状态：空 全部，0未学习，1已学习")
    private String status;
    @ApiModelProperty(value = "学习月份")
    private String periodStart;
    private String periodEnd;
    @ApiModelProperty("驾驶员姓名")
    private String staffName;
    @ApiModelProperty("驾驶员手机号")
    private String staffMobile;
    @ApiModelProperty("安全例会关系ids（多选导出使用）")
    private String safeMeetingRelationIds;
    @ApiModelProperty(value = "安全例会id")
    private String driverSafeMeetingId;
    @ApiModelProperty("人员机构 1 自主 2外部 3 自营")
    private String staffProperty;
}
