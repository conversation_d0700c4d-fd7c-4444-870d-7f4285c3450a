package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class GetPublishDemandOrderDetailResponseModel {

    private Long demandOrderId;
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty(value="发货公司id")
    private Long companyEntrustId;
    @ApiModelProperty(value="发货公司名称")
    private String companyEntrustName;
    @ApiModelProperty(value = "发货地址")
    private Long loadProvinceId;
    private String loadProvinceName;
    @ApiModelProperty(value = "发货地址")
    private Long loadCityId;
    private String loadCityName;
    @ApiModelProperty(value = "发货地址")
    private Long loadAreaId;
    private String loadAreaName;
    private String loadWarehouse;
    private String loadDetailAddress;
    private String loadContactName;
    private String loadContactMobile;
    @ApiModelProperty("期望提货时间")
    private Date expectedLoadTime;
    @ApiModelProperty(value = "收货地址")
    private Long unloadProvinceId;
    private String unloadProvinceName;
    @ApiModelProperty(value = "收货地址")
    private Long unloadCityId;
    private String unloadCityName;
    @ApiModelProperty(value = "收货地址")
    private Long unloadAreaId;
    private String unloadAreaName;
    private String unloadWarehouse;
    private String unloadDetailAddress;
    private String unloadContactName;
    @ApiModelProperty(value = "收货地址联系方式")
    private String unloadContactMobile;
    @ApiModelProperty("期望卸货时间")
    private Date expectedUnloadTime;
    @ApiModelProperty(value = "货主结算费用类型：0 空 1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer contractPriceType;
    @ApiModelProperty(value = "合同价：0 空")
    private BigDecimal contractPrice;

    @ApiModelProperty(value = "预计货主结算费用类型：0 空 1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer exceptContractPriceType;
    @ApiModelProperty(value = "预计合同价：0 空")
    private BigDecimal exceptContractPrice;

    @ApiModelProperty(value = "承运商公司id：0 空")
    private Long companyCarrierId;
    @ApiModelProperty(value = "承运商公司名称")
    private String companyCarrierName;
    @ApiModelProperty(value = "承运商公司类型")
    private Integer companyCarrierType;
    @ApiModelProperty(value = "车主账号id：0 空")
    private Long carrierContactId;
    @ApiModelProperty(value = "承运商公司联系人名称")
    private String companyCarrierContactName;
    @ApiModelProperty(value = "承运商公司联系方式")
    private String companyCarrierContactPhone;
    @ApiModelProperty(value = "车主价格：0 空，1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer carrierPriceType;
    @ApiModelProperty(value = "车主价格：0 空")
    private BigDecimal carrierPrice;

    @ApiModelProperty("是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;

    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;
    @ApiModelProperty("委托单货物信息")
    private List<DemandOrderGoodsBaseInfoResponseModel> demandOrderGoodsList;
}
