package com.logistics.management.webapi.controller.invoicingmanagement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/20 9:13
 */
@Data
public class TraditionGetAddSettleStatementListResponseDto {
    /**
     * 对账单id
     */
    @ApiModelProperty("对账单id")
    private String settleStatementId = "";

    /**
     * 对账月份
     */
    @ApiModelProperty("对账月份")
    private String settleStatementMonth = "";

    /**
     * 对账单号
     */
    @ApiModelProperty("对账单号")
    private String settleStatementCode = "";

    /**
     * 对账费用
     */
    @ApiModelProperty("对账费用")
    private String reconciliationFee = "";

    /**
     * 对账单名称
     */
    @ApiModelProperty("对账单名称")
    private String settleStatementName = "";
}
