package com.logistics.management.webapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/10/14 17:09
 */
@Data
public class GetLoanFeeByVehicleIdResponseModel {
    private Long loanRecordsId;
    private Integer status;
    private BigDecimal loanFee;
    @ApiModelProperty("扣减月份")
    private String deductingMonth;
    @ApiModelProperty("贷款当月扣减费用")
    private BigDecimal deductingFee;
    @ApiModelProperty("贷款剩余未扣减费用")
    private BigDecimal remainingDeductingFee;
    @ApiModelProperty("贷款结束时间（用于判断是否是最后一个月）")
    private Date loadFinishTime;
    @ApiModelProperty("贷款总期数")
    private Integer loanPeriods;
    @ApiModelProperty("贷款查询月份")
    private String currentMonth;
}
