package com.logistics.tms.client.feign.tray.order.customerinorder.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GetCustomerInOrderStateResponseModel {

    @ApiModelProperty(value = "运单号")
    private String carrierOrderCode;

    @ApiModelProperty(value = "状态: -1.已取消 1.待入库 2.部分入库 3.已入库 4.取消待审核 5.异常回退待审核 6.异常回退")
    private Integer state;
}
