package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

public class ExportExcelEntrustAddress {
    private ExportExcelEntrustAddress() {
    }
    private static final Map<String, String> EXPORT_ENTRUST_ADDRESS;
    static{
        EXPORT_ENTRUST_ADDRESS = new LinkedHashMap<>();
        EXPORT_ENTRUST_ADDRESS.put("货主性质","typeLabel");
        EXPORT_ENTRUST_ADDRESS.put("货主公司", "companyEntrustName");
        EXPORT_ENTRUST_ADDRESS.put("发货仓库","warehouse");
        EXPORT_ENTRUST_ADDRESS.put("发货地址", "address");
        EXPORT_ENTRUST_ADDRESS.put("联系人", "contactName");
        EXPORT_ENTRUST_ADDRESS.put("联系方式", "contactMobile");
        EXPORT_ENTRUST_ADDRESS.put("创建人","createdBy");
        EXPORT_ENTRUST_ADDRESS.put("操作人", "lastModifiedBy");
        EXPORT_ENTRUST_ADDRESS.put("操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getExportEntrustAddress() {
        return EXPORT_ENTRUST_ADDRESS;
    }
}
