package com.logistics.tms.biz.settlestatement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/20 14:37
 */
@Data
public class CarrierSettleStatementOrderDetailModel {
    @ApiModelProperty("对账单表Id")
    private Long settleStatementItemId;

    @ApiModelProperty("运单id")
    private Long carrierOrderId;

    @ApiModelProperty("车主id")
    private Long companyCarrierId;

    @ApiModelProperty(value = "车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;

    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;

    @ApiModelProperty("车主联系人")
    private String carrierContactName;

    @ApiModelProperty("车主联系人电话")
    private String carrierContactMobile;
}
