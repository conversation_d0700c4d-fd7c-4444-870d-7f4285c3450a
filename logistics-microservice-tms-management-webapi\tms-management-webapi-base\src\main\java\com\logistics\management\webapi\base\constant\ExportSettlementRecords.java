package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/10/8 19:43
 */
public class ExportSettlementRecords {
    private ExportSettlementRecords() {

    }

    private static final Map<String, String> LOAN_SETTLEMENT_RECORD_INFO;

    static {
        LOAN_SETTLEMENT_RECORD_INFO = new LinkedHashMap<>();
        LOAN_SETTLEMENT_RECORD_INFO.put("结算日期", "settlementDate");
        LOAN_SETTLEMENT_RECORD_INFO.put("结算金额", "settlementFee");
        LOAN_SETTLEMENT_RECORD_INFO.put("剩余还款金额", "remainingRepaymentFee");
        LOAN_SETTLEMENT_RECORD_INFO.put("总贷款金额", "totalFee");
        LOAN_SETTLEMENT_RECORD_INFO.put("操作人", "lastModifiedBy");
        LOAN_SETTLEMENT_RECORD_INFO.put("操作时间", "createdTime");
    }

    public static Map<String, String> getExportSettlementRecordsMap() {
        return LOAN_SETTLEMENT_RECORD_INFO;
    }

}
