package com.logistics.tms.base.enums;

/**
 * 合同状态枚举
 * @Author: sj
 * @Date: 2019/4/8 10:27
 */
public enum ContractOrderStatusEnum {
    WAIT_EXECUTE(1, "待执行"),
    EXECUTING(2, "执行中"),
    TERMINATION(3, "已终止"),
    CANCEL(4, "已作废"),
    ;

    private Integer key;
    private String value;

    ContractOrderStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
