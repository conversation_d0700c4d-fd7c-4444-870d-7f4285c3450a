package com.logistics.tms.controller.driversafepromise.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/11/4 10:10
 */
@Data
public class SearchSafePromiseListResponseModel {
    @ApiModelProperty("承诺书ID")
    private Long safePromiseId;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("周期")
    private String period;
    @ApiModelProperty("经办人")
    private String agent;
    @ApiModelProperty("已签订人数")
    private Integer hasSignCount;
    @ApiModelProperty("未签订人数")
    private Integer notSignCount;
    @ApiModelProperty("上传时间")
    private Date uploadTime;

}
