<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSysConfigMapper" >

  <select id="selectOnByGroupCodeAndKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_sys_config
    where valid = 1
    and group_code = #{groupCode}
    and config_key = #{configKey}
  </select>

  <select id="selectAllByGroupCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_sys_config
    where valid = 1
    and group_code = #{groupCode}
    order by last_modified_time desc, id desc
  </select>

  <select id="selectInGroupCodeAndKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_sys_config
    where valid = 1
    and (group_code, config_key) IN
    <foreach collection="sysConfigParams" item="item" open="(" separator="," close=")">
      (#{item.groupCode}, #{item.configKey})
    </foreach>
    order by last_modified_time desc, id desc
  </select>

  <update id="updateByGroupAndKey" parameterType="com.logistics.tms.entity.TSysConfig">
    update t_sys_config
    <set >
      <if test="groupDesc != null" >
        group_desc = #{groupDesc,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null" >
        config_value = #{configValue},
      </if>
      <if test="configDesc != null" >
        config_desc = #{configDesc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where valid = 1
    and group_code = #{groupCode}
    and config_key = #{configKey}
  </update>
</mapper>