package com.logistics.tms.controller.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DrivingLicenseAnnualReviewListResponseModel  {
    @ApiModelProperty("行驶证年审记录Id")
    private Long drivingLicenseAnnualReviewId;
    @ApiModelProperty("行驶证检查有效期")
    private Date checkValidDate;
    @ApiModelProperty("凭证列表")
    private List<CertificationPicturesResponseModel> fileList;
    @ApiModelProperty("最后操作人")
    private String lastModifiedBy;
    @ApiModelProperty("最后操作时间")
    private Date lastModifiedTime;
    @ApiModelProperty("备注")
    private String remark;
}
