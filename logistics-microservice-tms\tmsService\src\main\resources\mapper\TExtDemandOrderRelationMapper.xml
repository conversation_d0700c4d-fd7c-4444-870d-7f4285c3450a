<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TExtDemandOrderRelationMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TExtDemandOrderRelation" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="carrier_order_id" property="carrierOrderId" jdbcType="BIGINT" />
    <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR" />
    <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT" />
    <result column="demand_order_code" property="demandOrderCode" jdbcType="VARCHAR" />
    <result column="ext_demand_order_id" property="extDemandOrderId" jdbcType="BIGINT" />
    <result column="ext_demand_order_code" property="extDemandOrderCode" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, carrier_order_id, carrier_order_code, demand_order_id, demand_order_code, ext_demand_order_id, 
    ext_demand_order_code, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_ext_demand_order_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_ext_demand_order_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TExtDemandOrderRelation" >
    insert into t_ext_demand_order_relation (id, carrier_order_id, carrier_order_code, 
      demand_order_id, demand_order_code, ext_demand_order_id, 
      ext_demand_order_code, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, #{carrierOrderCode,jdbcType=VARCHAR}, 
      #{demandOrderId,jdbcType=BIGINT}, #{demandOrderCode,jdbcType=VARCHAR}, #{extDemandOrderId,jdbcType=BIGINT}, 
      #{extDemandOrderCode,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TExtDemandOrderRelation" >
    insert into t_ext_demand_order_relation
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="carrierOrderId != null" >
        carrier_order_id,
      </if>
      <if test="carrierOrderCode != null" >
        carrier_order_code,
      </if>
      <if test="demandOrderId != null" >
        demand_order_id,
      </if>
      <if test="demandOrderCode != null" >
        demand_order_code,
      </if>
      <if test="extDemandOrderId != null" >
        ext_demand_order_id,
      </if>
      <if test="extDemandOrderCode != null" >
        ext_demand_order_code,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null" >
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null" >
        #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="demandOrderId != null" >
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null" >
        #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="extDemandOrderId != null" >
        #{extDemandOrderId,jdbcType=BIGINT},
      </if>
      <if test="extDemandOrderCode != null" >
        #{extDemandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TExtDemandOrderRelation" >
    update t_ext_demand_order_relation
    <set >
      <if test="carrierOrderId != null" >
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null" >
        carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="demandOrderId != null" >
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null" >
        demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="extDemandOrderId != null" >
        ext_demand_order_id = #{extDemandOrderId,jdbcType=BIGINT},
      </if>
      <if test="extDemandOrderCode != null" >
        ext_demand_order_code = #{extDemandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TExtDemandOrderRelation" >
    update t_ext_demand_order_relation
    set carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      ext_demand_order_id = #{extDemandOrderId,jdbcType=BIGINT},
      ext_demand_order_code = #{extDemandOrderCode,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCarrierOrderCode" resultMap="BaseResultMap" parameterType="String" >
    select
    <include refid="Base_Column_List" />
    from t_ext_demand_order_relation
    where carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR}
    and valid = 1
  </select>

  <select id="selectByExtDemandOrderId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from t_ext_demand_order_relation
    where ext_demand_order_id = #{extDemandOrderId,jdbcType=BIGINT}
    and valid = 1
    limit 1
  </select>

  <select id="selectByExtCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_ext_demand_order_relation
    where ext_demand_order_code = #{extCode,jdbcType=VARCHAR}
      and valid = 1
  </select>
</mapper>