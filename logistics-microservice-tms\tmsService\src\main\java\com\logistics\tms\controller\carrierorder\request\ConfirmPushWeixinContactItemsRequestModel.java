package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ConfirmPushWeixinContactItemsRequestModel {

    @ApiModelProperty("id")
    private Long carrierOrderWxId;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("角色")
    private Integer role;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("是否推送")
    private Integer ifPush;
}
