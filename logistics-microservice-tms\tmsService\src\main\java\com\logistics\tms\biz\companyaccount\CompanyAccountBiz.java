package com.logistics.tms.biz.companyaccount;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.controller.companyaccount.request.CompanyAccountAddRequestModel;
import com.logistics.tms.controller.companyaccount.request.CompanyAccountEnabledRequestModel;
import com.logistics.tms.controller.companyaccount.request.CompanyAccountImageRequestModel;
import com.logistics.tms.controller.companyaccount.request.SearchCompanyAccountRequestModel;
import com.logistics.tms.controller.companyaccount.response.CompanyAccountImageResponseModel;
import com.logistics.tms.controller.companyaccount.response.SearchCompanyAccountResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TCertificationPictures;
import com.logistics.tms.entity.TCompanyAccount;
import com.logistics.tms.mapper.TCertificationPicturesMapper;
import com.logistics.tms.mapper.TCompanyAccountMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/6
 */
@Slf4j
@Service
public class CompanyAccountBiz {

	@Autowired
	private TCompanyAccountMapper tCompanyAccountMapper;
	@Autowired
	private TCertificationPicturesMapper tCertificationPicturesMapper;
	@Autowired
	private CommonBiz commonBiz;

	/**
	 * 公司账户列表
	 *
	 * @param requestModel 筛选条件
	 * @return 公司账户列表
	 */
	public PageInfo<SearchCompanyAccountResponseModel> searchList(SearchCompanyAccountRequestModel requestModel) {
		requestModel.enablePaging();
		List<SearchCompanyAccountResponseModel> responseModelList = tCompanyAccountMapper.searchList(requestModel);
		if (ListUtils.isNotEmpty(responseModelList)) {
			List<Long> picOutIds = new ArrayList<>();
			//查询银行账户图片
			for (SearchCompanyAccountResponseModel searchCompanyAccountResponseModel : responseModelList) {
				picOutIds.add(searchCompanyAccountResponseModel.getCompanyAccountId());
			}
			List<TCertificationPictures> tCertificationPicturesList = tCertificationPicturesMapper.getImageByIdsAndType(StringUtils.join(picOutIds, ','), CertificationPicturesObjectTypeEnum.T_COMPANY_ACCOUNT_BANK_CARD_PIC.getObjectType());
			if (ListUtils.isNotEmpty(tCertificationPicturesList)) {
				//分组图片
				Map<Long, List<TCertificationPictures>> certificationPicturesMap = tCertificationPicturesList.stream().collect(Collectors.groupingBy(TCertificationPictures::getObjectId));
				for (SearchCompanyAccountResponseModel searchCompanyAccountResponseModel : responseModelList) {
					List<TCertificationPictures> tCertificationPicturesListScope = certificationPicturesMap.get(searchCompanyAccountResponseModel.getCompanyAccountId());
					if (ListUtils.isNotEmpty(tCertificationPicturesListScope)) {
						//银行卡图片数量
						searchCompanyAccountResponseModel.setBankImageNumber(tCertificationPicturesListScope.size());
					}
				}
			}
		}
		return new PageInfo<>(responseModelList);
	}

	/**
	 * 查看银行账户图片
	 *
	 * @param requestModel 公司银行账户id
	 * @return 银行账户图片
	 */
	public CompanyAccountImageResponseModel getAccountImageList(CompanyAccountImageRequestModel requestModel) {
		//查询公司账户
		TCompanyAccount tCompanyAccount = tCompanyAccountMapper.selectByPrimaryKey(requestModel.getCompanyAccountId());
		if (tCompanyAccount == null || IfValidEnum.INVALID.getKey().equals(tCompanyAccount.getValid())) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_ACCOUNT_NOT_EXIST);
		}
		CompanyAccountImageResponseModel responseModel = new CompanyAccountImageResponseModel();
		//查询图片
		List<TCertificationPictures> tCertificationPicturesList = tCertificationPicturesMapper.getImageByIdsAndType(ConverterUtils.toString(requestModel.getCompanyAccountId()), CertificationPicturesObjectTypeEnum.T_COMPANY_ACCOUNT_BANK_CARD_PIC.getObjectType());

		//拼接数据
		if (ListUtils.isNotEmpty(tCertificationPicturesList)) {
			List<String> imagePathList = new ArrayList<>();
			for (TCertificationPictures tCertificationPictures : tCertificationPicturesList) {
				imagePathList.add(tCertificationPictures.getFilePath());
			}
			responseModel.setImagePaths(imagePathList);
		}
		return responseModel;
	}

	/**
	 * 新增公司账户
	 *
	 * @param requestModel 公司账户信息
	 */
	@Transactional
	public void addAccount(CompanyAccountAddRequestModel requestModel) {
		//根据账户号查询公司账户
		TCompanyAccount dbCompanyAccount = tCompanyAccountMapper.selectByBankAccount(requestModel.getBankAccount());
		if (dbCompanyAccount != null) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_ACCOUNT_IS_EXIST);
		}

		//插入公司账户信息
		TCompanyAccount tCompanyAccount = MapperUtils.mapperNoDefault(requestModel, TCompanyAccount.class);
		tCompanyAccount.setEnabled(CommonConstant.INTEGER_ZERO);
		commonBiz.setBaseEntityAdd(tCompanyAccount, BaseContextHandler.getUserName());
		tCompanyAccountMapper.insertSelectiveEncrypt(tCompanyAccount);

		//账户图片
		List<String> bankAccountImage = requestModel.getBankAccountImage();
		if (ListUtils.isNotEmpty(bankAccountImage)) {
			TCertificationPictures addCertificationPictures;
			List<TCertificationPictures> addCertificationPicturesList = new ArrayList<>();
			for (String picPath : bankAccountImage) {
				addCertificationPictures = new TCertificationPictures();
				addCertificationPictures.setObjectId(tCompanyAccount.getId());
				addCertificationPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_COMPANY_ACCOUNT_BANK_CARD_PIC.getObjectType());
				addCertificationPictures.setFileType(CertificationPicturesFileTypeEnum.T_COMPANY_ACCOUNT_BANK_CARD_PIC_FILE.getFileType());
				addCertificationPictures.setFileTypeName(CertificationPicturesFileTypeEnum.T_COMPANY_ACCOUNT_BANK_CARD_PIC_FILE.getFileName());
				addCertificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_ACCOUNT_BANK_CARD_PIC.getKey(), "", picPath, null));
				addCertificationPictures.setUploadUserName(BaseContextHandler.getUserName());
				addCertificationPictures.setUploadTime(new Date());
				addCertificationPictures.setSuffix(addCertificationPictures.getFilePath().substring(addCertificationPictures.getFilePath().lastIndexOf('.')));
				commonBiz.setBaseEntityAdd(addCertificationPictures, BaseContextHandler.getUserName());
				addCertificationPicturesList.add(addCertificationPictures);
			}
			//插入图片
			tCertificationPicturesMapper.batchInsert(addCertificationPicturesList);
		}
	}

	/**
	 * 公司账户启用/禁用
	 *
	 * @param requestModel 启用/禁用
	 */
	@Transactional
	public void enabled(CompanyAccountEnabledRequestModel requestModel) {
		//查询公司账户
		TCompanyAccount tCompanyAccount = tCompanyAccountMapper.selectByPrimaryKey(requestModel.getCompanyAccountId());
		if (tCompanyAccount == null || IfValidEnum.INVALID.getKey().equals(tCompanyAccount.getValid())) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_ACCOUNT_NOT_EXIST);
		}

		if (CommonConstant.INTEGER_ONE.equals(requestModel.getEnabled())) {
			/*启用*/

			//禁用状态才能启用
			if (!CommonConstant.INTEGER_ZERO.equals(tCompanyAccount.getEnabled())) {
				throw new BizException(CarrierDataExceptionEnum.COMPANY_ACCOUNT_STATUS_ERROR);
			}
		} else {
			/*禁用*/

			//启用状态才能启用
			if (!CommonConstant.INTEGER_ONE.equals(tCompanyAccount.getEnabled())) {
				throw new BizException(CarrierDataExceptionEnum.COMPANY_ACCOUNT_STATUS_ERROR);
			}
		}

		//更新启用禁用状态
		TCompanyAccount tCompanyAccountUp = new TCompanyAccount();
		tCompanyAccountUp.setId(requestModel.getCompanyAccountId());
		tCompanyAccountUp.setEnabled(requestModel.getEnabled());
		commonBiz.setBaseEntityModify(tCompanyAccountUp, BaseContextHandler.getUserName());
		tCompanyAccountMapper.updateByPrimaryKeySelective(tCompanyAccountUp);
	}
}
