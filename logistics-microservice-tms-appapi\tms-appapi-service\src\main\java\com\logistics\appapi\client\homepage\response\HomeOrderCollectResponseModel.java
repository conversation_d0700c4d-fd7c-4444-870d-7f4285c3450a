package com.logistics.appapi.client.homepage.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class HomeOrderCollectResponseModel {

	@ApiModelProperty("待确认乐橘新生订单")
	private Integer waitConfirmedCount;

	@ApiModelProperty("待提货运单")
	private Integer waitLoadCount;

	@ApiModelProperty("工单数量")
	private Integer workOrderCount;
}
