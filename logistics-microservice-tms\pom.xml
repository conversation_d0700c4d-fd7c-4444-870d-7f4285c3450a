<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.logistics</groupId>
    <artifactId>microService-tms</artifactId>
    <version>3.31.0-RELEASE</version>

    <packaging>pom</packaging>
    <name>logistics-microservice-tms</name>
    <description>TMS服务</description>

    <modules>
        <module>tmsBase</module>
        <module>tmsService</module>
        <module>tmsServiceApi</module>
    </modules>

    <parent>
        <groupId>com.yelo</groupId>
        <artifactId>yelo-starter-parent</artifactId>
        <version>********-RELEASE</version>
    </parent>

    <properties>
        <auth.service.version>2.0.5-RELEASE</auth.service.version>
        <yelo.basicdata.api.version>2.0.2-RELEASE</yelo.basicdata.api.version>
        <logistics.entrust.service.api>3.0.0-RELEASE</logistics.entrust.service.api>
        <logistics.carrier.service.api>3.0.0-RELEASE</logistics.carrier.service.api>
        <yelolife.basicData.service.api>2.0.0-RELEASE</yelolife.basicData.service.api>
        <yelolife.order.service.api>2.0.0-RELEASE</yelolife.order.service.api>

        <projects.version>3.31.0-RELEASE</projects.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--服务依赖-->
            <dependency>
                <groupId>com.leyi.tray</groupId>
                <artifactId>microService-auth-service-client</artifactId>
                <version>${auth.service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yelo</groupId>
                <artifactId>basicDataServiceApi</artifactId>
                <version>${yelo.basicdata.api.version}</version>
            </dependency>
            <!--网络货运 entrust api-->
            <dependency>
                <groupId>com.logistics</groupId>
                <artifactId>entrustServiceApi</artifactId>
                <version>${logistics.entrust.service.api}</version>
            </dependency>
            <!--网络货运 carrier api-->
            <dependency>
                <groupId>com.logistics</groupId>
                <artifactId>carrierServiceApi</artifactId>
                <version>${logistics.carrier.service.api}</version>
            </dependency>
            <!--新生基础数据服务依赖-->
            <dependency>
                <groupId>com.yelolife</groupId>
                <artifactId>basicDataServiceApi</artifactId>
                <version>${yelolife.basicData.service.api}</version>
            </dependency>
            <!--新生订单服务依赖-->
            <dependency>
                <groupId>com.yelolife</groupId>
                <artifactId>orderServiceApi</artifactId>
                <version>${yelolife.order.service.api}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
