package com.logistics.tms.controller.dispatchorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/1/22 11:21
 */
@Data
public class CarrierOrderGoodsResponseModel {
    private Long carrierOrderId;
    private Long demandOrderId;
    private String demandOrderCode;
    private String customerOrderCode;
    private Integer goodsUnit;
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    private Long carrierOrderGoodsId;
    private String goodsName;
    private Integer length;
    private Integer width;
    private Integer height;
    private String goodsSize;
    private BigDecimal expectAmount;
    @ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;
    @ApiModelProperty("委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生")
    private Integer source;

    @ApiModelProperty("车主公司名")
    private String companyCarrierName;

    @ApiModelProperty("车主类型: 1:企业 2:个人")
    private Integer companyCarrierType;

    @ApiModelProperty("车主联系人姓名")
    private String carrierContactName;

    @ApiModelProperty("车主联系人手机号")
    private String carrierContactPhone;
}
