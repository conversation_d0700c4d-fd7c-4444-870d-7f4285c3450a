<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TTrayCostMapper" >
    <select id="searchTrayCostList" resultType="com.logistics.tms.api.feign.traycost.model.SearchTrayCostListResponseModel">
        select
        id as trayCostId,
        entrust_type as entrustType,
        goods_unit as goodsUnit,
        unit_price as unitPrice
        from t_tray_cost
        where valid = 1
        and (end_time is null or (start_time &lt;= now() and end_time >= now()))
        group by entrust_type,goods_unit
        order by
        case
        when entrust_type = 9 then 1
        when entrust_type = 3 then 2
        when entrust_type = 2 then 3
        when entrust_type = 1 then 4
        when entrust_type = 4 then 5
        when entrust_type = 7 then 6
        when entrust_type = 10 then 7
        when entrust_type = 11 then 8
        when entrust_type = 12 then 9
        end asc,
        case when goods_unit = 3 then 1
        when goods_unit = 2 then 2
        when goods_unit = 4 then 3
        end asc
    </select>

    <select id="getTopByEntrustTypeGoodsUnit" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tray_cost
        where valid = 1
        and entrust_type = #{entrustType,jdbcType=INTEGER}
        and goods_unit = #{goodsUnit,jdbcType=INTEGER}
        and start_time > now()
    </select>

    <select id="searchCostRecordsList" resultType="com.logistics.tms.api.feign.traycost.model.SearchCostRecordsListResponseModel">
        select
        entrust_type as entrustType,
        goods_unit as goodsUnit,
        unit_price as unitPrice,
        start_time as startTime,
        last_modified_by as lastModifiedBy,
        last_modified_time as lastModifiedTime
        from t_tray_cost
        where valid = 1
        and entrust_type = #{prams.entrustType,jdbcType=INTEGER}
        and goods_unit = #{prams.goodsUnit,jdbcType=INTEGER}
        order by last_modified_time desc, start_time desc
    </select>

    <select id="getTopByEntrustTypesAndGoodsUnits" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tray_cost
        where valid = 1
        and entrust_type in (${entrustTypes})
        and goods_unit in (${goodsUnits})
        and start_time &lt;= now()
    </select>
</mapper>