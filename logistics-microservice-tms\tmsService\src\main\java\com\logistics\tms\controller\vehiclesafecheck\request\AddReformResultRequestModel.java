package com.logistics.tms.controller.vehiclesafecheck.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 整改结果
 * @Author: sj
 * @Date: 2019/11/6 13:15
 */
@Data
public class AddReformResultRequestModel {
    @ApiModelProperty("车辆安全检查ID")
    private Long safeCheckVehicleId;
    @ApiModelProperty("整改结果")
    private String reformResult;
    @ApiModelProperty("整改结果图片列表")
    private List<String> resultFileList;
}
