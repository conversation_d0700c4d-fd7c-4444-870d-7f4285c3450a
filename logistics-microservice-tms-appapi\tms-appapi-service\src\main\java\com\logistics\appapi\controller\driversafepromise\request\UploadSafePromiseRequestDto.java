package com.logistics.appapi.controller.driversafepromise.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: sj
 * @Date: 2019/11/18 15:28
 */
@Data
public class UploadSafePromiseRequestDto {
    @ApiModelProperty("签订承诺书关系ID")
    @NotBlank(message = "ID为空")
    private String relationId;
    @ApiModelProperty("手持承诺书图片地址")
    @NotBlank(message = "手持承诺书图片不能为空")
    private String handPromiseUrl;
    @ApiModelProperty("签字责任书图片地址")
    @NotBlank(message = "签字责任书图片不能为空")
    private String signResponsibilityUrl;
}
