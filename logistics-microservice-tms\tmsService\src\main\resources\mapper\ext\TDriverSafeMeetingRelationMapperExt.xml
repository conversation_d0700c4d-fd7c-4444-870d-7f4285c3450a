<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDriverSafeMeetingRelationMapper" >
  <insert id="batchInsert" parameterType="com.logistics.tms.entity.TDriverSafeMeetingRelation" >
    <foreach collection="list" item="item" separator=";">
      insert into t_driver_safe_meeting_relation
      <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          id,
        </if>
        <if test="item.safeMeetingId != null" >
          safe_meeting_id,
        </if>
        <if test="item.staffId != null" >
          staff_id,
        </if>
        <if test="item.staffName != null" >
          staff_name,
        </if>
        <if test="item.staffMobile != null" >
          staff_mobile,
        </if>
        <if test="item.staffProperty != null">
          staff_property,
        </if>
        <if test="item.status != null" >
          status,
        </if>
        <if test="item.studyTime != null" >
          study_time,
        </if>
        <if test="item.signTime != null" >
          sign_time,
        </if>
        <if test="item.signLocateAddress != null">
          sign_locate_address,
        </if>
        <if test="item.staffDriverImageUrl != null" >
          staff_driver_image_url,
        </if>
        <if test="item.signImageUrl != null" >
          sign_image_url,
        </if>
        <if test="item.createdBy != null" >
          created_by,
        </if>
        <if test="item.createdTime != null" >
          created_time,
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by,
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time,
        </if>
        <if test="item.valid != null" >
          valid,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="item.id != null" >
          #{item.id,jdbcType=BIGINT},
        </if>
        <if test="item.safeMeetingId != null" >
          #{item.safeMeetingId,jdbcType=BIGINT},
        </if>
        <if test="item.staffId != null" >
          #{item.staffId,jdbcType=BIGINT},
        </if>
        <if test="item.staffName != null" >
          #{item.staffName,jdbcType=VARCHAR},
        </if>
        <if test="item.staffMobile != null" >
          #{item.staffMobile,jdbcType=VARCHAR},
        </if>
        <if test="item.staffProperty != null">
          #{item.staffProperty,jdbcType=INTEGER},
        </if>
        <if test="item.status != null" >
          #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.studyTime != null" >
          #{item.studyTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.signTime != null" >
          #{item.signTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.signLocateAddress != null">
          #{item.signLocateAddress,jdbcType=VARCHAR},
        </if>
        <if test="item.staffDriverImageUrl != null" >
          #{item.staffDriverImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.signImageUrl != null" >
          #{item.signImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          #{item.valid,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <select id="driverSafeMeetingDetailList" resultType="com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingDetailResponseModel">
    select
    tdsmr.id as safeMeetingRelationId,
    tdsmr.safe_meeting_id as safeMeetingId,
    tdsmr.status,
    if(tdsmr.status = 1, tdsmr.staff_name, tsb.name) as staffName,
    if(tdsmr.status = 1, tdsmr.staff_mobile, tsb.mobile) as staffMobile,
    tdsmr.study_time as studyTime,
    tdsmr.sign_time as signTime,
    tdsmr.staff_driver_image_url as staffDriverImageUrl,
    tdsmr.sign_image_url as signImageUrl,
    tdsmr.staff_property as staffProperty,

    tdsm.period,
    tdsm.title
    from t_driver_safe_meeting_relation tdsmr
    left join t_driver_safe_meeting tdsm on tdsm.id = tdsmr.safe_meeting_id and tdsm.valid = 1
    left join t_staff_basic tsb on tsb.id = tdsmr.staff_id and tsb.valid = 1
    where tdsmr.valid = 1
    <if test="params.status != null">
      and tdsmr.status = #{params.status,jdbcType=INTEGER}
    </if>
    <if test="params.staffName != null and params.staffName != ''">
      and if(tdsmr.status=1,instr(tdsmr.staff_name,#{params.staffName,jdbcType=VARCHAR}),instr(tsb.name,#{params.staffName,jdbcType=VARCHAR}))
    </if>
    <if test="params.staffMobile != null and params.staffMobile != ''">
      and if(tdsmr.status=1,instr(tdsmr.staff_mobile,#{params.staffMobile,jdbcType=VARCHAR}),instr(tsb.mobile,#{params.staffMobile,jdbcType=VARCHAR}))
    </if>
    <if test="params.periodStart != null and params.periodStart != ''">
      and tdsm.period >= #{params.periodStart,jdbcType=VARCHAR}
    </if>
    <if test="params.periodEnd != null and params.periodEnd != ''">
      and tdsm.period &lt;= #{params.periodEnd,jdbcType=VARCHAR}
    </if>
    <if test="params.safeMeetingRelationIds != null and params.safeMeetingRelationIds != ''">
      and tdsmr.id in (${params.safeMeetingRelationIds})
    </if>
    <if test="params.driverSafeMeetingId != null">
      and tdsm.id  = #{params.driverSafeMeetingId,jdbcType=BIGINT}
    </if>
    <if test="params.staffProperty != null">
      and tdsmr.staff_property = #{params.staffProperty,jdbcType=BIGINT}
    </if>
    order by tdsm.period desc,tdsmr.study_time desc,tdsmr.id desc
  </select>

  <select id="driverSafeMeetingListCount" resultType="com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingListCountResponseModel">
    select
    ifnull(count(tdsmr.id),0) as allCount,
    ifnull(sum(if(tdsmr.status = 1, 1, 0)),0) as learnCount,
    ifnull(sum(if(tdsmr.status = 0, 1, 0)),0) as notLearnCount
    from t_driver_safe_meeting_relation tdsmr
    left join t_driver_safe_meeting tdsm on tdsm.id = tdsmr.safe_meeting_id and tdsm.valid = 1
    left join t_staff_basic tsb on tsb.id = tdsmr.staff_id and tsb.valid = 1
    where tdsmr.valid = 1
    <if test="params.staffName != null and params.staffName != ''">
      and if(tdsmr.status=1,instr(tdsmr.staff_name,#{params.staffName,jdbcType=VARCHAR}),instr(tsb.name,#{params.staffName,jdbcType=VARCHAR}))
    </if>
    <if test="params.staffMobile != null and params.staffMobile != ''">
      and if(tdsmr.status=1,instr(tdsmr.staff_mobile,#{params.staffMobile,jdbcType=VARCHAR}),instr(tsb.mobile,#{params.staffMobile,jdbcType=VARCHAR}))
    </if>
    <if test="params.periodStart != null and params.periodStart != ''">
      and tdsm.period >= #{params.periodStart,jdbcType=VARCHAR}
    </if>
    <if test="params.periodEnd != null and params.periodEnd != ''">
      and tdsm.period &lt;= #{params.periodEnd,jdbcType=VARCHAR}
    </if>
    <if test="params.driverSafeMeetingId != null">
      and tdsm.id  = #{params.driverSafeMeetingId,jdbcType=BIGINT}
    </if>
    <if test="params.staffProperty != null">
      and tdsmr.staff_property = #{params.staffProperty,jdbcType=BIGINT}
    </if>
  </select>

  <select id="appletSafeMeetingList" resultType="com.logistics.tms.controller.driversafemeeting.response.AppletSafeMeetingListResponseModel">
    select
    tdsmr.id as safeMeetingRelationId,
    tdsmr.status,
    tdsm.period,
    tdsm.title,
    tdsm.created_by as createdBy,
    tdsm.created_time as createdTime,
    tdsm.type
    from t_driver_safe_meeting_relation tdsmr
    left join t_driver_safe_meeting tdsm on tdsm.id = tdsmr.safe_meeting_id and tdsm.valid = 1
    where tdsmr.valid = 1 and tdsmr.staff_id = #{driverId,jdbcType=BIGINT}
    <if test="params.status != null">
      and tdsmr.status = #{params.status,jdbcType=INTEGER}
    </if>
    order by tdsm.period desc,tdsm.created_time desc
  </select>

  <select id="appletSafeMeetingListCount" resultType="com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingListCountResponseModel">
    select
    ifnull(count(tdsmr.id),0) as allCount,
    ifnull(sum(if(tdsmr.status = 1, 1, 0)),0) as learnCount,
    ifnull(sum(if(tdsmr.status = 0, 1, 0)),0) as notLearnCount
    from t_driver_safe_meeting_relation tdsmr
    where tdsmr.valid = 1 and tdsmr.staff_id = #{driverId,jdbcType=BIGINT}
  </select>

  <select id="appletSafeMeetingDetail" resultType="com.logistics.tms.controller.driversafemeeting.response.AppletSafeMeetingDetailResponseModel">
    select
    tdsmr.id as safeMeetingRelationId,
    tdsmr.status,
    tdsmr.staff_driver_image_url as staffDriverImageUrl,
    tdsmr.sign_image_url as signImageUrl,
    tdsm.period,
    tdsm.title,
    tdsm.content,
    tdsm.created_by as createdBy,
    tdsm.created_time as createdTime
    from t_driver_safe_meeting_relation tdsmr
    left join t_driver_safe_meeting tdsm on tdsm.id = tdsmr.safe_meeting_id and tdsm.valid = 1
    where tdsmr.valid = 1 and tdsmr.staff_id = #{driverId,jdbcType=BIGINT}
    and tdsmr.id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getBySafeMeetingId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_driver_safe_meeting_relation
    where valid = 1
    and safe_meeting_id = #{safeMeetingId,jdbcType=BIGINT}
  </select>

  <update id="batchUpdateForStudySignTimeNull" parameterType="com.logistics.tms.entity.TDriverSafeMeetingRelation">
    <foreach collection="list" item="item" separator=";">
      update t_driver_safe_meeting_relation
      <set>
        <if test="item.safeMeetingId != null">
          safe_meeting_id = #{item.safeMeetingId,jdbcType=BIGINT},
        </if>
        <if test="item.staffId != null">
          staff_id = #{item.staffId,jdbcType=BIGINT},
        </if>
        <if test="item.staffName != null">
          staff_name = #{item.staffName,jdbcType=VARCHAR},
        </if>
        <if test="item.staffMobile != null">
          staff_mobile = #{item.staffMobile,jdbcType=VARCHAR},
        </if>
        <if test="item.staffProperty != null">
          staff_property = #{item.staffProperty,jdbcType=INTEGER},
        </if>
        <if test="item.status != null">
          status = #{item.status,jdbcType=INTEGER},
        </if>
          study_time = #{item.studyTime,jdbcType=TIMESTAMP},
          sign_time = #{item.signTime,jdbcType=TIMESTAMP},
        <if test="item.signLocateAddress != null">
          sign_locate_address = #{item.signLocateAddress,jdbcType=VARCHAR},
        </if>
        <if test="item.staffDriverImageUrl != null">
          staff_driver_image_url = #{item.staffDriverImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.signImageUrl != null">
          sign_image_url = #{item.signImageUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null">
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null">
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null">
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="getDriverSafeMeetingKanBanItem" resultType="com.logistics.tms.controller.driversafemeeting.response.DriverSafeMeetingKanBanItemResponseModel">
    select
    tdsmr.safe_meeting_id as safeMeetingId,
    tdsmr.staff_id as staffId,
    tdsmr.status,
    tdsmr.staff_property as staffProperty
    from t_driver_safe_meeting_relation tdsmr
    where tdsmr.valid = 1
    and tdsmr.safe_meeting_id in (${safeMeetingIds})
  </select>
</mapper>