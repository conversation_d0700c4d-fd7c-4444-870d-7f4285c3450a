package com.logistics.management.webapi.api.impl.renewableaudit;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.renewableaudit.RenewableAuditApi;
import com.logistics.management.webapi.api.feign.renewableaudit.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.renewableaudit.mapping.*;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportExcelRenewableAudit;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.tms.api.feign.renewableaudit.RenewableAuditServiceApi;
import com.logistics.tms.api.feign.renewableaudit.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Slf4j
public class RenewableAuditApiImpl implements RenewableAuditApi {

    @Autowired
    private RenewableAuditServiceApi renewableAuditServiceApi;
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 新生审核列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<RenewableAuditResponseDto>> renewableAuditList(@RequestBody RenewableAuditRequestDto requestDto) {
        Result<PageInfo<RenewableAuditResponseModel>> pageInfoResult = renewableAuditServiceApi.renewableAuditList(MapperUtils.mapperNoDefault(requestDto, RenewableAuditRequestModel.class));
        pageInfoResult.throwException();
        PageInfo pageInfo = pageInfoResult.getData();
        List<RenewableAuditResponseDto> renewableAuditResponseDtoList = MapperUtils.mapper(pageInfo.getList(), RenewableAuditResponseDto.class,new RenewableAuditMapping());
        pageInfo.setList(renewableAuditResponseDtoList);
        return Result.success(pageInfo);
    }

    /**
     * 新生审核列表导出
     *
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportRenewableAuditList(RenewableAuditExportRequestDto requestDto, HttpServletResponse response) {
        Result<List<RenewableAuditResponseModel>> pageInfoResult = renewableAuditServiceApi.exportRenewableAuditList(MapperUtils.mapper(requestDto, RenewableAuditRequestModel.class));
        pageInfoResult.throwException();
        List<RenewableAuditResponseDto> list = MapperUtils.mapper(pageInfoResult.getData(), RenewableAuditResponseDto.class, new ExportRenewableAuditMapping());
        String fileName = "新生订单审核" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String, String> exportMap = ExportExcelRenewableAudit.getExcelRenewableOrder();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * 新生审核列表查看详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<RenewableAuditDetailResponseDto> renewableAuditDetail(@RequestBody @Valid RenewableAuditDetailRequestDto requestDto) {
        Result<RenewableAuditDetailResponseModel> modelResult = renewableAuditServiceApi.getRenewableAuditDetail(MapperUtils.mapper(requestDto, RenewableAuditDetailRequestModel.class));
        modelResult.throwException();
        RenewableAuditDetailResponseDto renewableAuditDetailResponseDto = MapperUtils.mapper(modelResult.getData(), RenewableAuditDetailResponseDto.class, new RenewableAuditDetailMapping());
        return Result.success(renewableAuditDetailResponseDto);
    }

    /**
     * 指派司机-详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<RenewableAuditAssignDriverResponseDto>> assignDriverList(@RequestBody @Valid RenewableAuditAssignRequestDto requestDto) {
        Result<List<RenewableAssignDriverDetailResponseModel>> assignDriverResult = renewableAuditServiceApi.getAssignDriverDetail(MapperUtils.mapper(requestDto, RenewableAssignDriverDetailRequestModel.class));
        assignDriverResult.throwException();
        List<RenewableAuditAssignDriverResponseDto> assignDriverResponseDtos = MapperUtils.mapper(assignDriverResult.getData(), RenewableAuditAssignDriverResponseDto.class, new RenewableAuditAssignDriverMapping());
        return Result.success(assignDriverResponseDtos);
    }

    /**
     * 指派司机-确认
     * @param requestDto
     * @return
     */
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @Override
    public Result<Boolean> assignDriver(@RequestBody @Valid RenewableAuditAssignDriverRequestDto requestDto) {
        Result<Boolean> result = renewableAuditServiceApi.assignDriver(MapperUtils.mapper(requestDto, RenewableAssignDriverRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 确认信息-详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<RenewableConfirmGoodsResponseDto> confirmNewsDetail(@RequestBody @Valid RenewableAuditDetailRequestDto requestDto) {
        Result<RenewableConfirmGoodsResponseModel> confirmGoodsResult = renewableAuditServiceApi.confirmNewsDetail(MapperUtils.mapper(requestDto, RenewableAuditDetailRequestModel.class));
        confirmGoodsResult.throwException();
        List<String> sourceSrcList = new ArrayList<>();
        RenewableConfirmGoodsResponseModel data = confirmGoodsResult.getData();
        if (data != null && ListUtils.isNotEmpty(data.getPhotosList())) {
            List<RenewableTicketsUrlResponseModel> model = confirmGoodsResult.getData().getPhotosList();
            List<String> collect = model.stream().map(RenewableTicketsUrlResponseModel::getFilePath).collect(Collectors.toList());
            sourceSrcList.addAll(collect);
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(confirmGoodsResult.getData(), RenewableConfirmGoodsResponseDto.class, new RenewableConfirmGoodsMapping(configKeyConstant.fileAccessAddress, imageMap)));
    }

    /**
     * 确认信息-确认
     * @param requestDto
     * @return
     */
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @Override
    public Result<Boolean> confirmNews(@RequestBody @Valid RenewableConfirmGoodsRequestDto requestDto) {
        // skuCode唯一性
        List<RenewableGoodsRequestDto> confirmGoodsList = requestDto.getConfirmGoodsList();
        List<String> skuCodeList = confirmGoodsList.stream().map(RenewableGoodsRequestDto::getSkuCode)
                .distinct().collect(Collectors.toList());
        if (confirmGoodsList.size() != skuCodeList.size()) {
            throw new BizException(ManagementWebApiExceptionEnum.SKU_CODE_NOT_UNIQUE);
        }
        Result<Boolean> result = renewableAuditServiceApi.confirmNews(MapperUtils.mapperNoDefault(requestDto, RenewableConfirmGoodsRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 修改指派-提交
     * @param requestDto
     * @return
     */
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @Override
    public Result<Boolean> updateAssignDriver(@RequestBody @Valid RenewableAuditAssignDriverRequestDto requestDto) {
        return renewableAuditServiceApi.updateAssignDriver(MapperUtils.mapper(requestDto, RenewableAssignDriverRequestModel.class));
    }

    /**
     * 操作日志
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<GetRenewableOperateLogsResponseDto>> getRenewableOrderLogs(@RequestBody @Valid RenewableAuditDetailRequestDto requestDto) {
        Result<List<RenewableAuditOperateLogsResponseModel>> logsResult = renewableAuditServiceApi.getRenewableAuditLogs(MapperUtils.mapper(requestDto, RenewableAuditDetailRequestModel.class));
        logsResult.throwException();
        List<GetRenewableOperateLogsResponseDto> logsResponseDtos = MapperUtils.mapper(logsResult.getData(), GetRenewableOperateLogsResponseDto.class);
        return Result.success(logsResponseDtos);
    }

    /**
     * 查询票据
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<RenewableTicketsResponseDto>> getRenewableTickets(@RequestBody @Valid RenewableAuditDetailRequestDto requestDto) {
        Result<List<RenewableAuditTicketsResponseModel>> ticketsResult = renewableAuditServiceApi.getRenewableAuditTickets(MapperUtils.mapper(requestDto, RenewableAuditDetailRequestModel.class));
        ticketsResult.throwException();
        List<String> sourceSrcList = new ArrayList<>();
        if(ListUtils.isNotEmpty(ticketsResult.getData())){
            for (RenewableAuditTicketsResponseModel datum : ticketsResult.getData()) {
                sourceSrcList.add(datum.getFilePath());
            }
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        List<RenewableTicketsResponseDto> mapper = MapperUtils.mapper(ticketsResult.getData(), RenewableTicketsResponseDto.class, new GetRenewableTicketsMapping(configKeyConstant.fileAccessAddress, imageMap));
        return Result.success(mapper);
    }

    /**
     * 新生审核Tab统计
     * @param requestDto
     * @return
     */
    @Override
    public Result<RenewableAuditListStatisticsResponseDto> getRenewableAuditListStatistics(@RequestBody RenewableAuditRequestDto requestDto) {
        Result<RenewableAuditListStatisticsResponseModel> renewableAuditListStatisticsResult = renewableAuditServiceApi.getRenewableAuditListStatistics(MapperUtils.mapper(requestDto, RenewableAuditRequestModel.class));
        return Result.success(MapperUtils.mapper(renewableAuditListStatisticsResult.getData(),RenewableAuditListStatisticsResponseDto.class));
    }

    /**
     * 查询sku下拉列表
     *
     * @param requestDto sku名模糊查询
     * @return sku列表
     */
    @Override
    public Result<List<SearchLifeSkuResponseDto>> searchLifeSku(@RequestBody SearchLifeSkuRequestDto requestDto) {
        Result<List<SearchLifeSkuResponseModel>> result = renewableAuditServiceApi.searchLifeSku(MapperUtils.mapper(requestDto, SearchLifeSkuRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SearchLifeSkuResponseDto.class));
    }

    /**
     * 确认信息-查询收货仓库
     *
     * @param requestDto 查询条件
     * @return 收货仓库信息
     */
    @Override
    public Result<List<SearchWarehouseResponseDto>> searchWarehouse(SearchWarehouseRequestDto requestDto) {
        SearchWarehouseRequestModel mapper = MapperUtils.mapper(requestDto, SearchWarehouseRequestModel.class);
        mapper.setSearchType(CommonConstant.TWO);
        Result<List<SearchWarehouseResponseModel>> result = renewableAuditServiceApi.searchWarehouse(mapper);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SearchWarehouseResponseDto.class));
    }
}
