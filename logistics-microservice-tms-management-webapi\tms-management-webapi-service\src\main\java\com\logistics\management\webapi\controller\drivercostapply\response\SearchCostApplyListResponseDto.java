package com.logistics.management.webapi.controller.drivercostapply.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/8/3 13:43
 */
@Data
@ExcelIgnoreUnannotated
public class SearchCostApplyListResponseDto {

    @ApiModelProperty("司机费用申请表id")
    private String driverCostApplyId="";

    @ApiModelProperty("(3.17.0)审核状态：-1 待业务审核，0 待财务审核，1 已审核，2 已驳回，3 已撤销，4 已红冲")
    private String auditStatus="";

    @ApiModelProperty("审核状态")
    @ExcelProperty(value = "审核状态")
    private String auditStatusLabel="";

    @ApiModelProperty("单号; 1.3.6 新增")
    @ExcelProperty(value = "单号")
    private String costApplyCode = "";

    @ApiModelProperty("司机")
    @ExcelProperty(value = "司机")
    private String staffName="";

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private String staffProperty="";

    @ApiModelProperty("人员机构")
    @ExcelProperty(value = "司机机构")
    private String staffPropertyLabel="";

    @ApiModelProperty("车牌号")
    @ExcelProperty(value = "车牌号")
    private String vehicleNo="";

    @ApiModelProperty("(3.17.0)费用类型：100 住宿费，101 装卸费，102 其他费用，103 加班费，104 劳保费，105 电话费，106 交通费， 107 打印费，108 叉车费，109 盖雨布， 110破包赔偿，111 交通罚款；200 维修费，201 车辆保养费，202 过路过桥费，203 停车费，204 尿素费，205 加油费；300 核酸检测费，301 医疗防护用品；400 扣款")
    private String costType="";

    @ApiModelProperty("费用类型")
    @ExcelProperty(value = "费用类型")
    private String costTypeLabel="";

    @ApiModelProperty("申请费用")
    @ExcelProperty(value = "申请费用（元）")
    private String applyCost="";

    @ApiModelProperty("发生时间")
    @ExcelProperty(value = "发生时间")
    private String occurrenceTime="";

    @ApiModelProperty("申请时间")
    @ExcelProperty(value = "申请时间")
    private String applyTime="";

    @ApiModelProperty("关联油卡")
    @ExcelProperty(value = "关联油卡")
    private String associatedOilCard="";

    @ApiModelProperty("备用金单号")
    @ExcelProperty(value = "备用金单号")
    private String reserveCode="";

    @ApiModelProperty("垫付费用（元）; 1.3.6 新增")
    @ExcelProperty(value = "垫付费用（元）")
    private String advanceCosts = "";

    @ApiModelProperty("发票: 0 无票, 1 有票; 1.3.6 新增")
    private String invoice = "";

    @ApiModelProperty("发票文本; 1.3.6 新增")
    @ExcelProperty(value = "发票")
    private String invoiceLabel = "";

    @ApiModelProperty("审核时间")
    @ExcelProperty(value = "审核时间")
    private String auditTime="";
    
    @ApiModelProperty("审核人")
    @ExcelProperty(value = "审核人")
    private String auditorName="";

    @ApiModelProperty("司机-导出用")
    private String exportStaffName="";
}
