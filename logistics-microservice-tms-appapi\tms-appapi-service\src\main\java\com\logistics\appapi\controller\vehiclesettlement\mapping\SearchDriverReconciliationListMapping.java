package com.logistics.appapi.controller.vehiclesettlement.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.VehicleSettlementStatementStatusEnum;
import com.logistics.appapi.client.vehiclesettlement.response.SearchDriverReconciliationListResponseModel;
import com.logistics.appapi.controller.vehiclesettlement.response.SearchDriverReconciliationListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * @author：wjf
 * @date：2021/4/12 13:49
 */
public class SearchDriverReconciliationListMapping extends MapperMapping<SearchDriverReconciliationListResponseModel,SearchDriverReconciliationListResponseDto> {
    @Override
    public void configure() {
        SearchDriverReconciliationListResponseModel source = getSource();
        SearchDriverReconciliationListResponseDto destination = getDestination();

        destination.setStatusLabel(VehicleSettlementStatementStatusEnum.getEnum(source.getStatus()).getValue());
        destination.setSettlementYear(source.getSettlementMonth().substring(0,4));
        if (source.getSettlementMonth().substring(source.getSettlementMonth().length() - 2).compareTo(CommonConstant.TEN) < CommonConstant.INTEGER_ZERO) {
            destination.setSettlementMonth(source.getSettlementMonth().substring(source.getSettlementMonth().length() - 1));
        } else {
            destination.setSettlementMonth(source.getSettlementMonth().substring(source.getSettlementMonth().length() - 2));
        }
        BigDecimal adjustFee=Optional.ofNullable(source.getAdjustFee()).orElse(BigDecimal.ZERO);
        if(adjustFee.compareTo(BigDecimal.ZERO)>CommonConstant.INTEGER_ZERO){
            destination.setAdjustFee(CommonConstant.PLUS+ adjustFee.stripTrailingZeros().setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString());
        }
    }
}
