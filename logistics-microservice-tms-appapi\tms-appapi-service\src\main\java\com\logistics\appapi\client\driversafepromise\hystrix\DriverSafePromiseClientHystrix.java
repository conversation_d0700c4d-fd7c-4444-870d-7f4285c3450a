package com.logistics.appapi.client.driversafepromise.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.driversafepromise.DriverSafePromiseClient;
import com.logistics.appapi.client.driversafepromise.request.SafePromiseAppletDetailRequestModel;
import com.logistics.appapi.client.driversafepromise.request.SearchSafePromiseAppletListRequestModel;
import com.logistics.appapi.client.driversafepromise.request.UploadSafePromiseRequestModel;
import com.logistics.appapi.client.driversafepromise.response.SafePromiseAppletDetailResponseModel;
import com.logistics.appapi.client.driversafepromise.response.SearchSafePromiseAppletListResponseModel;
import com.logistics.appapi.client.driversafepromise.response.SummarySignSafePromiseAppletResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/14 14:09
 */
@Component
public class DriverSafePromiseClientHystrix implements DriverSafePromiseClient {
    @Override
    public Result<PageInfo<SearchSafePromiseAppletListResponseModel>> searchAppletList(SearchSafePromiseAppletListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SummarySignSafePromiseAppletResponseModel> searchAppletSummary(SearchSafePromiseAppletListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SafePromiseAppletDetailResponseModel> getAppletDetail(SafePromiseAppletDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> uploadSafePromise(UploadSafePromiseRequestModel requestModel) {
        return Result.timeout();
    }
}
