package com.logistics.tms.client.feign.yelolife.basicdata;

import com.logistics.tms.client.feign.yelolife.basicdata.hystrix.YeloLifeBasicDataServiceApiHystrix;
import com.yelo.tray.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "yelolife-basicdata-services", fallback = YeloLifeBasicDataServiceApiHystrix.class)
public interface YeloLifeBasicDataServiceApi {

    /**
     * 校验新生编码
     *
     * @param goodsCodes
     * @return 正确的code集合
     */
    @PostMapping("/service/life/verifyGoodsCodeList")
    Result<List<String>> verifyGoodsCodeList(@RequestBody List<String> goodsCodes);

}
