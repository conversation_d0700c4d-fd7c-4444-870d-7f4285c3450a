package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class CopyCarrierRecycleOrderByDispatchVehicleMessage {

    @ApiModelProperty("需求单号集合")
    private List<String> demandOrderCodeList;

    @ApiModelProperty("key:需求单号  value:调度数量")
    private Map<String, Integer> shipmentCountMap;

    @ApiModelProperty("运单集合")
    private List<CopyCarrierRecycleOrderByDispatchVehicleModel> items;

    @ApiModelProperty("预计提货时间")
    private Date expectArrivalTime;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("司机名称")
    private String driverName;

    @ApiModelProperty("司机手机号码")
    private String driverMobile;

    @ApiModelProperty("身份证号")
    private String driverIdentity;

    private Long userId;

    @ApiModelProperty("调度人")
    private String userName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("调度单号")
    private String dispatchOrderCode;

    @ApiModelProperty("调度时间")
    private Date dispatchTime;

    @ApiModelProperty("key：需求单号 value：需求单状态")
    private Map<String, Integer> demandOrderStatusMap;
}
