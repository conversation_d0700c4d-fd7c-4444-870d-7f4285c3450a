package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/9/21 16:25
 */
@Data
public class SyncSignDemandOrderToGroundPushModel {

    @ApiModelProperty("签收的需求单信息")
    private List<SyncSignDemandOrderListToGroundPushModel> signDemandOrderList;

    @ApiModelProperty("签收人")
    private String signUser;

    @ApiModelProperty("签收时间")
    private Date signTime;

    @ApiModelProperty("操作人")
    private String operator;
}
