package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2020/2/29 11:22
 */
public enum CarrierContactTypeEnum {
    TOURISTS(0, "游客"),
    ORDINARY_ACCOUNT(1, "普通账号"),
    ADMINISTRATOR(2, "管理员"),
    ;
    private Integer key;
    private String value;

    CarrierContactTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
