package com.logistics.management.webapi.base.utils;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.yelo.tools.utils.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * liang current user system login name
 * 2018/10/24 current system date
 */
public class FrequentMethodUtils {
    private FrequentMethodUtils() {
    }

    // 判断车牌号是否正确
    public static boolean validateVehicleFormat(String vehicleNo) {
        if (StringUtils.isNotBlank(vehicleNo)) {
            String regex = "[冀豫云辽黑湘皖鲁苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼渝京津沪新军空海北沈兰济南广成使领]{1}[A-Z]{1}[A-Z0-9]{4,5}[A-Z0-9挂学警港澳]{1}";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(vehicleNo);
            if (matcher.find()) {
                return true;
            }
        }
        return false;
    }

    // 判断手机号格式是否正确
    public static boolean validateTelFormat(String telephone) {
        if (StringUtils.isNotBlank(telephone)) {
            Pattern pattern = Pattern.compile("^\\d{11}$");
            Matcher matcher = pattern.matcher(telephone);
            if (matcher.find()) {
                return true;
            }
        }
        return false;
    }

    // 判断保单号合法性
    public static boolean validateBatchNumber(String batchNumber) {
        if (StringUtils.isNotBlank(batchNumber)) {
            Pattern pattern = Pattern.compile("^[A-Z0-9]{1,50}$");
            Matcher matcher = pattern.matcher(batchNumber);
            if (matcher.find()) {
                return true;
            }
        }
        return false;
    }

    //金额
    public static boolean isNumberOrFloatNumberTwo(String str) {
        if (StringUtils.isNotBlank(str)) {
            Pattern pattern = Pattern.compile("(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)");
            Matcher matcher = pattern.matcher(str);
            if (matcher.find()) {
                return true;
            }
        }
        return false;
    }

    public static boolean isNumberOrFloatNumberThree(String str) {
        String regex = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,3})$)";
        return match(regex, str);
    }

    //判断数字正整数
    public static boolean isPositiveInteger(String var){
        if(StringUtils.isNotBlank(var)){
            Pattern pattern = Pattern.compile("^[+]{0,1}(\\d+)$");
            Matcher matcher = pattern.matcher(var);
            if (matcher.find() && Integer.valueOf(var)>=1 && Integer.valueOf(var)<=365 ) {
                    return true;
            }
        }
        return false;
    }

    //整数数量校验（0<amount<=5000）
    public static boolean integerNum(String amount){
        if(StringUtils.isBlank(amount)){
            return false;
        }
        Pattern pattern = Pattern.compile("^[1-9]\\d{0,2}|[1-4]\\d{3}|5000$");
        Matcher matcher = pattern.matcher(amount);
        return matcher.find();
    }

    // 判断机动车驾驶证号是否正确
    public static boolean validateDriversLicenseNo(String driversLicenseNo) {
        if (StringUtils.isNotBlank(driversLicenseNo)) {
            Pattern pattern = Pattern.compile("^\\d{1,50}$");
            Matcher matcher = pattern.matcher(driversLicenseNo);
            if (matcher.find()) {
                return true;
            }
        }
        return false;
    }

    // 判断准驾车型合法性
    public static boolean validatePermittedType(String permittedType) {
        if (StringUtils.isNotBlank(permittedType)) {
            Pattern pattern = Pattern.compile("^[A-Z0-9,，]{2,50}$");
            Matcher matcher = pattern.matcher(permittedType);
            if (matcher.find()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 匹配正则
     * @param data 匹配数据
     * @param reg 正则表达式
     * @return
     */
    public static boolean checkReg(String data,String reg){
        if (StringUtils.isNotBlank(data)) {
            Pattern pattern = Pattern.compile(reg);
            Matcher matcher = pattern.matcher(data);
            if (matcher.find()) {
                return true;
            }
        }
        return false;
    }

    public static final boolean match(String regex, String str) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        return matcher.find();
    }

    /**
     * 源字符串除去保留的部分其余都用'*'替换
     * @param source 源数据
     * @param typeEnum 加密类型
     * @return
     */
    public static String encryptionData(String source,EncodeTypeEnum typeEnum){
        if(StringUtils.isBlank(source) || typeEnum == null){
            return "";
        }

        int startLength = typeEnum.getStartLength();
        int endLength = typeEnum.getEndLength();

        int encodeLength = source.length() - startLength - endLength;
        if (encodeLength <= 0){
            return source;
        }

        StringBuffer encodeStr = new StringBuffer();
        for (int i = 1; i <= encodeLength ;i++) {
            encodeStr.append("*");
        }

        String startStr = source.substring(CommonConstant.INTEGER_ZERO, startLength);
        String endStr = source.substring(source.length()-endLength);
        return startStr + encodeStr + endStr;
    }
}
