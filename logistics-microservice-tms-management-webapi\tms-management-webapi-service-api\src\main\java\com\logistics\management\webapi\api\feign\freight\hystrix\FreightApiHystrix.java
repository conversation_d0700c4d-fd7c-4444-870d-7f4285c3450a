package com.logistics.management.webapi.api.feign.freight.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.freight.FreightApi;
import com.logistics.management.webapi.api.feign.freight.dto.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/12/24 12:00
 */
@Component
public class FreightApiHystrix implements FreightApi {
    @Override
    public Result<PageInfo<SearchFreightListResponseDto>> searchList(SearchFreightListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addFreight(AddFreightRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> enableFreight(EnableFreightRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchFreightAddressResponseDto>> searchFreightAddressList(SearchFreightAddressRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addFreightAddressRule(AddOrModifyFreightAddressRuleRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<FreightAddressRuleDetailResponseDto> getFreightRuleDetail(FreightAddressRuleDetailRequestDto responseDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> deleteFreightAddressRule(DeleteFreightAddressRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifyFreightPrice(ModifyFreightPriceRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<List<FreightLogsResponseDto>> freightLogs(FreightLogsRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportFreightAddress(SearchFreightAddressRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }

    @Override
    public Result<DriverFreightByDemandOrderIdsAndVehicleResponseDto> getDriverFreight(DriverFreightByDemandOrderIdsAndVehicleRequestDto requestDto) {
        return Result.timeout();
    }


    @Override
    public Result<GetPriceByAddressAndAmountResponseDto> getPriceByAddressAndAmount(GetPriceByAddressAndAmountRequestDto requestDto) {
        return Result.timeout();
    }
}
