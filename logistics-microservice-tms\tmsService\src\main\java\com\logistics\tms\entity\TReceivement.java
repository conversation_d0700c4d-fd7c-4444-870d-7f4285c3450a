package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TReceivement extends BaseEntity {
    /**
    * 需求单ID
    */
    @ApiModelProperty("需求单ID")
    private Long carrierOrderId;

    /**
    * 报价类型 1 单价 2 整车价
    */
    @ApiModelProperty("报价类型 1 单价 2 整车价")
    private Integer priceType;

    /**
    * 结算数量
    */
    @ApiModelProperty("结算数量")
    private BigDecimal settlementAmount;

    /**
    * 结算费用合计
    */
    @ApiModelProperty("结算费用合计")
    private BigDecimal settlementCostTotal;
}