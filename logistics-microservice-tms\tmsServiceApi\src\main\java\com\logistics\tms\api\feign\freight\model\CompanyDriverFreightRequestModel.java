package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sj
 * @Date: 2019/12/31 18:34
 */
@Data
public class CompanyDriverFreightRequestModel {
    @ApiModelProperty("车主公司ID")
    private Long companyCarrierId;
    @ApiModelProperty("省")
    private Long fromProvinceId;
    @ApiModelProperty("市")
    private Long fromCityId;
    @ApiModelProperty("区")
    private String fromAreaId;
    @ApiModelProperty("省")
    private Long toProvinceId;
    @ApiModelProperty("市")
    private Long toCityId;
    @ApiModelProperty("区")
    private String toAreaId;
    @ApiModelProperty("装货点")
    private Integer loadAmount;
    @ApiModelProperty("卸货点")
    private Integer unloadAmount;
    @ApiModelProperty("吨位")
    private BigDecimal totalAmount;
    @ApiModelProperty("费用类型: 1 单价 2 一口价")
    private Integer freightType;
}
