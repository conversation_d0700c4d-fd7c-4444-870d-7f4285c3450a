package com.logistics.tms.controller.shippingorder.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/6 10:15
 */
@Data
public class GetShippingOrderDetailResponseModel {

    //基础信息
    /**
     * 运输单id
     */
    private Long shippingOrderId;

    /**
     * 运输单号
     */
    private String shippingOrderCode;

    /**
     * 状态：0 待审核，1 已审核，2 已驳回
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 创建人
     */
    private String createdBy;


    //零担拼单详情
    /**
     * 货物单位：1 件，2 吨，3 方，4 块
     */
    private Integer goodsUnit;
    /**
     * 拼单列表
     */
    private List<GetShippingOrderDetailOrderListResponseModel> orderList=new ArrayList<>();
    /**
     * 装卸数-装
     */
    private Integer loadPointAmount;

    /**
     * 装卸数-卸
     */
    private Integer unloadPointAmount;

    /**
     * 车长
     */
    private BigDecimal vehicleLength;

    /**
     * 整车费用
     */
    private BigDecimal carrierFreight;

    /**
     * 串点费用
     */
    private BigDecimal crossPointFee;


    //业务审核
    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核备注
     */
    private String auditRemark;


    /**
     * 操作日志
     */
    private List<GetShippingOrderOperateLogListResponseModel> operateLogList=new ArrayList<>();


    /**
     * 车主
     */
    private String companyCarrierName="";
    /**
     * 发货省市区
     */
    private String loadAddress="";

    /**
     * 收货省市区
     */
    private String unloadAddress="";
    /**
     * 装卸数
     */
    private String pointAmount;
    /**
     * 预提数
     */
    private BigDecimal expectAmount;

}
