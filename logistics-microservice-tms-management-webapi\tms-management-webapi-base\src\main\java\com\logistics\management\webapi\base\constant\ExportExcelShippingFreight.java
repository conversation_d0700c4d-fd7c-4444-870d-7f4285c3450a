package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/5/31 13:55
 */
public class ExportExcelShippingFreight {
    private ExportExcelShippingFreight() {
    }

    private static final Map<String, String> SHIPPING_FREIGHT_MAP;

    static {
        SHIPPING_FREIGHT_MAP = new LinkedHashMap<>();
        SHIPPING_FREIGHT_MAP.put("运价规则名称", "carrierPriceRuleName");
        SHIPPING_FREIGHT_MAP.put("适用业务类型", "entrustTypeLabel");
        SHIPPING_FREIGHT_MAP.put("承运商数量", "companyCarrierCount");
        SHIPPING_FREIGHT_MAP.put("备注", "remark");
        SHIPPING_FREIGHT_MAP.put("创建人", "createdBy");
        SHIPPING_FREIGHT_MAP.put("创建时间", "createdTime");
        SHIPPING_FREIGHT_MAP.put("操作人", "lastModifiedBy");
        SHIPPING_FREIGHT_MAP.put("操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getDateShippingFreightMap() {
        return SHIPPING_FREIGHT_MAP;
    }
}
