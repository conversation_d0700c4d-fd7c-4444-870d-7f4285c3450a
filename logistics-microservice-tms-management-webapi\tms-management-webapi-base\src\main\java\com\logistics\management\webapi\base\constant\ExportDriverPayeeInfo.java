package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

public class ExportDriverPayeeInfo {
    private ExportDriverPayeeInfo() {
    }

    private static final Map<String, String> DRIVER_PAYEE_INFO;

    static {
        DRIVER_PAYEE_INFO = new LinkedHashMap<>();
        DRIVER_PAYEE_INFO.put("收款人姓名", "name");
        DRIVER_PAYEE_INFO.put("联系方式", "mobile");
        DRIVER_PAYEE_INFO.put("身份证号", "identityNo");
        DRIVER_PAYEE_INFO.put("银行卡号", "bankCardNo");
        DRIVER_PAYEE_INFO.put("银行名称", "bankName");
        DRIVER_PAYEE_INFO.put("备注", "remark");
    }

    public static Map<String, String> getDriverPayeeInfo() {
        return DRIVER_PAYEE_INFO;
    }
}
