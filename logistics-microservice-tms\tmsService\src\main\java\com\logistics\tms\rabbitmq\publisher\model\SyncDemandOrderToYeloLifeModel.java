package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/20 13:06
 */
@Data
public class SyncDemandOrderToYeloLifeModel<T> {

    /**
     * 同步类型 {@link SyncTypeEnum#key}
     */
    private Integer type;

    @ApiModelProperty("内容")
    private T msgData;

    /**
     * 新生需求单节点操作枚举
     */
    @Getter
    @AllArgsConstructor
    public enum SyncTypeEnum {

        /**
         * 取消需求单节点：内容{@link DemandOrderToYeloLifeModel}
         */
        CANCEL(1, "取消需求单"),

        /**
         * 需求单手动完成调度节点：内容{@link List<DemandOrderCompleteDispatchToYeloLifeModel>}
         */
        COMPLETE_DISPATCH(2, "完成调度"),

        /**
         * 运单签收触发需求单签收节点：内容{@link DemandOrderToYeloLifeModel}
         */
        SIGN_DISPATCH(4,"需求单签收"),

        ;
        private final Integer key;
        private final String value;

    }

}
