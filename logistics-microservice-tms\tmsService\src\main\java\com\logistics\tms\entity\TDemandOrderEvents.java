package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/08/01
*/
@Data
public class TDemandOrderEvents extends BaseEntity {
    /**
    * 需求单ID
    */
    @ApiModelProperty("需求单ID")
    private Long demandOrderId;

    /**
    * 车主ID
    */
    @ApiModelProperty("车主ID")
    private Long companyCarrierId;

    /**
    * 事件 10 承接订单 20 调度车辆 30 已取消 40 已完成
    */
    @ApiModelProperty("事件 10 承接订单 20 调度车辆 30 已取消 40 已完成")
    private Integer event;

    /**
    * 事件描述
    */
    @ApiModelProperty("事件描述")
    private String eventDesc;

    /**
    * 事件时间
    */
    @ApiModelProperty("事件时间")
    private Date eventTime;

    /**
    * 操作人名称
    */
    @ApiModelProperty("操作人名称")
    private String operatorName;

    /**
    * 操作时间
    */
    @ApiModelProperty("操作时间")
    private Date operateTime;
}