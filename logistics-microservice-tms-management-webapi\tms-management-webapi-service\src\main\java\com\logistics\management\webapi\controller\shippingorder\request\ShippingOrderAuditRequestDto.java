package com.logistics.management.webapi.controller.shippingorder.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * @author: wjf
 * @date: 2024/8/5 10:28
 */
@Data
public class ShippingOrderAuditRequestDto {

    /**
     * 运输单id
     */
    @NotBlank(message = "id不能为空")
    private String shippingOrderId;

    /**
     * 操作类型：1 通过，2 驳回
     */
    @NotBlank(message = "操作类型不能为空")
    @Pattern(regexp = "^[12]$", message = "操作类型错误")
    private String operateType;

    /**
     * 备注
     */
    @Size(max = 200, message = "备注最多200字")
    private String remark;
}
