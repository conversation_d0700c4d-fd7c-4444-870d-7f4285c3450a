package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleRoadTransportCertificateAnnualReview extends BaseEntity {
    /**
    * 道路运输证ID
    */
    @ApiModelProperty("道路运输证ID")
    private Long roadTransportCetificationId;

    /**
    * 检查有效期
    */
    @ApiModelProperty("检查有效期")
    private Date checkValidDate;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}