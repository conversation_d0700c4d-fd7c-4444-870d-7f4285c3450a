package com.logistics.appapi.client.staff;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.staff.hystrix.StaffClientHystrix;
import com.logistics.appapi.client.staff.response.GetStaffDetailByAccountIdResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @author: wjf
 * @date: 2024/3/7 17:05
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/staffmanagement",
        fallback = StaffClientHystrix.class)
public interface StaffClient {

    @ApiModelProperty(value = "根据accountId查询司机信息")
    @PostMapping(value = "/getStaffDetailByAccountId")
    Result<GetStaffDetailByAccountIdResponseModel> getStaffDetailByAccountId();
}
