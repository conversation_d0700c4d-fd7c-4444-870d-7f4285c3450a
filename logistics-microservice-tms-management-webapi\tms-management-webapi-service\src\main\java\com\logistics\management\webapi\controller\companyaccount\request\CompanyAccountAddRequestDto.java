package com.logistics.management.webapi.controller.companyaccount.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class CompanyAccountAddRequestDto {

    @ApiModelProperty(value = "银行账号,9-30位", required = true)
    @NotBlank(message = "请填写9-30位银行账号")
    @Length(min = 9, max = 30, message = "请填写9-30位银行账号")
    private String bankAccount;

    @ApiModelProperty(value = "开户银行名称,4-20位", required = true)
    @NotBlank(message = "请填写4-20位银行名称")
    @Length(min = 4, max = 20, message = "请填写4-20位银行名称")
    private String bankAccountName;

    @ApiModelProperty(value = "开户支行名称, 非必填 填写时检验长度 0<=长度<=20 ")
    @Length(max = 20, message = "最多填写20个字符")
    private String braBankName;

    @ApiModelProperty(value = "行号,4-30个数字", required = true)
    @NotBlank(message = "请填写4-30位银行账号")
    @Length(min = 4, max = 30, message = "请填写4-30位银行账号")
    private String bankCode;

    @ApiModelProperty(value = "银行卡图片,1-2张图片", required = true)
    @NotEmpty(message = "请上传1-2张银行卡图片")
    @Size(max = 2, message = "最多上传2张银行卡图片")
    private List<@NotBlank(message = "图片路径不允许为空") String> bankAccountImage;

    @ApiModelProperty(value = "使用范围, 非必填 0-100")
    @Length(max = 100, message = "最多填写100字符")
    private String remark;
}
