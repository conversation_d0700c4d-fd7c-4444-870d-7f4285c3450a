package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TFreightAddressRuleMarkup;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TFreightAddressRuleMarkupMapper extends BaseMapper<TFreightAddressRuleMarkup>{
    int batchInsert(@Param("list") List<TFreightAddressRuleMarkup> list);

    List<TFreightAddressRuleMarkup> getByFreightAddressRuleIds(@Param("freightAddressRuleIds")String freightAddressRuleIds);

    int batchUpdate(@Param("list") List<TFreightAddressRuleMarkup> list);

    TFreightAddressRuleMarkup getByRuleIdLoadAmount(@Param("ruleId")Long ruleId, @Param("loadAmount")Integer loadAmount, @Param("unloadAmount")Integer unloadAmount);
}