package com.logistics.management.webapi.client.biddingorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class BiddingOrderQuoteDetailResponseModel {
    /**
     * 报价单id
     */
    private Long biddingOrderQuoteId;

    /**
     * 车主
     */
    @ApiModelProperty("车主")
    private String carrierContactName;

    /**
     * 报价金额
     */
    @ApiModelProperty("报价金额")
    private BigDecimal quotePrice;

    /**
     * 报价单价
     */
    @ApiModelProperty("报价金额")
    private BigDecimal unitPrice;

    /**
     * 报价金额类型：1 单价，2 一口价
     */
    @ApiModelProperty("报价金额类型：1 单价，2 一口价")
    private Integer quotePriceType;

    /**
     * 车长id
     */
    @ApiModelProperty("车长id")
    private Long vehicleLengthId;

    /**
     * 车长
     */
    @ApiModelProperty("车长")
    private BigDecimal vehicleLength;

    /**
     * 需求单列表
     */
    @ApiModelProperty("需求单列表")
    private List<BiddingQuoteDemandModel> demandDtoList = new ArrayList<>();

}
