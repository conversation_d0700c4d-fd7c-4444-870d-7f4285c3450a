package com.logistics.tms.rabbitmq.publisher.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.concurrent.TimeUnit;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class DelayMsg {

    /**
     *消息Id
     */
    private String msgId;

    /**
     *业务类型
     */
    private String delayMsgType;

    /**
     *业务数据
     */
    private Object object;

    /**
     *延时时间
     */
    private Long time;

    /**
     * 延时的单位
     */
    private TimeUnit timeUnit;

    public DelayMsg(String msgId, String delayMsgType, String object) {
        this.msgId = msgId;
        this.delayMsgType = delayMsgType;
        this.object = object;
    }
}
