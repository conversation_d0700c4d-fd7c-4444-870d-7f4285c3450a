package com.logistics.management.webapi.api.impl.workordercenter.mapping;

import com.logistics.management.webapi.api.feign.workordercenter.dto.response.WorkOrderDetailResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.WorkOrderStatusEnum;
import com.logistics.tms.api.feign.workordercenter.enums.WorkOrderAnomalyTypeOneEnum;
import com.logistics.tms.api.feign.workordercenter.enums.WorkOrderAnomalyTypeTwoEnum;
import com.logistics.tms.api.feign.workordercenter.model.response.WorkOrderDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/17
 */
public class WorkOrderDetailMapping extends MapperMapping<WorkOrderDetailResponseModel, WorkOrderDetailResponseDto> {

	private final Map<String, String> picMap;
	private final ConfigKeyConstant configKeyConstant;

	public WorkOrderDetailMapping(ConfigKeyConstant configKeyConstant, Map<String, String> picMap) {
		this.picMap = picMap;
		this.configKeyConstant = configKeyConstant;
	}

	@Override
	public void configure() {
		WorkOrderDetailResponseModel source = getSource();
		WorkOrderDetailResponseDto destination = getDestination();

		//状态
		destination.setStatusLabel(WorkOrderStatusEnum.getEnumByKey(source.getStatus()).getValue());

		String reportAddress;
		if (CommonConstant.INTEGER_ONE.equals(source.getIsArriveScene())) {
			reportAddress = (StringUtils.isNotBlank(source.getAddressHead()) ? ("【" + source.getAddressHead() + "】") : "") + Optional.ofNullable(source.getAddressDetail()).orElse("");
		} else {
			reportAddress = "未到现场";
		}

		//现场情况
		destination.setReportAddress(reportAddress);

		//司机
		destination.setDriver(Optional.ofNullable(source.getDriverName()).orElse("") + " " + Optional.ofNullable(source.getDriverPhone()).orElse(""));

		//问题类型
		destination.setAbnormalProblem(WorkOrderAnomalyTypeOneEnum.getEnumByKey(source.getAnomalyTypeOne()).getValue() + "/" + WorkOrderAnomalyTypeTwoEnum.getEnumByKey(source.getAnomalyTypeTwo()).getValue());

		//现场证明
		if (StringUtils.isNotBlank(source.getArriveScenePicture())) {
			destination.setArriveScenePictureSrc(configKeyConstant.fileAccessAddress + picMap.get(source.getArriveScenePicture()));
		}
	}
}
