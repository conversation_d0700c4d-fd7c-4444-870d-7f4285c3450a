package com.logistics.tms.biz.attendancerecord.handle;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.AttendancePunchTypeEnum;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.StaffPropertyEnum;
import com.logistics.tms.biz.attendancerecord.model.AttendanceClockModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TAttendanceRecord;
import com.logistics.tms.mapper.TAttendanceRecordMapper;
import com.logistics.tms.mapper.TStaffBasicMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ObjectUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Component
@RequiredArgsConstructor
public class OffDutyPunchHandle implements AttendancePunchHandle {

    private final CommonBiz commonBiz;
    private final TAttendanceRecordMapper attendanceRecordMapper;
    private final TStaffBasicMapper staffBasicMapper;

    /**
     * 下班打卡 filter
     *
     * @return
     */
    @Override
    public boolean filter(Integer dutyPunchType) {
        return AttendancePunchTypeEnum.OFF_DUTY_CLOCK_IN.getKey().equals(dutyPunchType);
    }

    /**
     * 下班打卡 handel
     *
     * @param boModel
     * @return true 成功 false 失败
     */
    @Override
    public boolean handle(AttendanceClockModel boModel) {

        staffBasicMapper.getStaffByIds(boModel.getStaffId().toString())
                .stream()
                // 过滤出自营自主员工
                .filter(f -> {
                    return StaffPropertyEnum.OWN_STAFF.getKey().equals(f.getStaffProperty()) ||
                            StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(f.getStaffProperty());
                })
                .findFirst().orElseThrow(() -> new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST));

        // 判断是否已完成打卡
        if (AttendancePunchTypeEnum.OFF_DUTY_CLOCK_IN.getKey().equals(boModel.getToDayPunchType())) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_CLOCK_COMPLETE);
        }
        if (ObjectUtils.isEmpty(boModel.getRecodeId())) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_RECORD_NOT_EXISTS);
        }

        // 计算工时
        Date offDutyPunchTime = new Date();
        Date onDutyPunchTime = boModel.getOnDutyPunchTime();
        BigDecimal manHour = calculatingManHour(onDutyPunchTime, offDutyPunchTime);

        // 构建下班打卡Model
        TAttendanceRecord record = new TAttendanceRecord();
        record.setId(boModel.getRecodeId());
        record.setManHour(manHour);
        record.setOffDutyPunchPic(boModel.getDutyPunchPic());
        record.setOffDutyPunchLocation(boModel.getDutyPunchLocation());
        record.setOffDutyPunchTime(offDutyPunchTime);
        commonBiz.setBaseEntityModify(record, BaseContextHandler.getUserName());
        return attendanceRecordMapper.updateByPrimaryKeySelective(record) > CommonConstant.INTEGER_ZERO;
    }
}
