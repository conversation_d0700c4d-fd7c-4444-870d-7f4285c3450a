package com.logistics.management.webapi.client.invoicingmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2024/3/19 17:26
 */
@Data
public class AddOrModifyInvoiceRequestModel {
    @ApiModelProperty(value = "发票管理id")
    private Long invoicingId;

    @ApiModelProperty("业务类型：1 包装业务，2 自营业务")
    private Integer businessType;

    @ApiModelProperty(value = "发票id（编辑必填）")
    private Long invoiceId;

    @ApiModelProperty(value = "发票类型：1 电子发票，2 纸质发票")
    private Integer invoiceType;

    @ApiModelProperty(value = "发票图片")
    private String invoicePicture;

    @ApiModelProperty("发票代码")
    private String invoiceCode;

    @ApiModelProperty("发票号码")
    private String invoiceNum;

    @ApiModelProperty("开票日期")
    private Date invoiceDate;

    @ApiModelProperty("发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("税额合计")
    private BigDecimal totalTaxAndPrice;

}
