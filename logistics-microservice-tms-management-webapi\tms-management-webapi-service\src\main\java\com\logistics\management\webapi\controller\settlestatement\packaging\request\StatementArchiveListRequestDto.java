package com.logistics.management.webapi.controller.settlestatement.packaging.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/9
 */
@Data
public class StatementArchiveListRequestDto extends AbstractPageForm<StatementArchiveListRequestDto> {

	@ApiModelProperty(value = "对账单id", required = true)
	@NotBlank(message = "id不能为空")
	private String settleStatementId;
}
