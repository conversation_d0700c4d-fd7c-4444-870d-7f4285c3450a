package com.logistics.management.webapi.api.feign.dateremind.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * @Author: sj
 * @Date: 2019/5/31 8:57
 */
@Data
public class DateRemindDetailResponseDto implements Serializable {
    @ApiModelProperty("日期提醒ID")
    private String dateRemindId;
    @ApiModelProperty("日期提醒名称")
    private String dateName = "";
    @ApiModelProperty("是否提醒：0 否，1 是")
    private String ifRemind = "";
    @ApiModelProperty("提醒天数")
    private String remindDays = "";
    @ApiModelProperty("添加人ID")
    private String addUserId;
    @ApiModelProperty("添加人")
    private String addUserName = "";
    @ApiModelProperty("数据来源:1新增 2导入")
    private String source = "";
    @ApiModelProperty("创建人")
    private String createdBy = "";
    @ApiModelProperty("创建时间")
    private String createdTime = "";
    @ApiModelProperty("最后修改人")
    private String lastModifiedBy = "";
    @ApiModelProperty("最后修改日期")
    private String lastModifiedTime = "";
    @ApiModelProperty("1.有效,0.无效")
    private String valid;
    @ApiModelProperty("备注")
    private String remark = "";
}
