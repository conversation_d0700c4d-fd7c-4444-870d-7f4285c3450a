package com.logistics.tms.controller.demandorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.demandorder.DemandOrderBiz;
import com.logistics.tms.controller.demandorder.request.*;
import com.logistics.tms.controller.demandorder.response.*;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/9/11 19:46
 */
@RestController
@RequestMapping(value = "/service/demandOrder")
public class DemandOrderController {

    @Resource
    private DemandOrderBiz demandOrderBiz;

    /**
     * 委托发布
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "委托发布")
    @PostMapping(value = "/saveDemandOrder")
    public Result<Boolean> saveDemandOrder(@RequestBody SaveDemandOrderRequestModel requestModel) {
        demandOrderBiz.saveDemandOrder(requestModel);
        return Result.success(true);
    }

    /**
     * 获取需求单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取需求单列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<DemandOrderResponseModel>> searchList(@RequestBody DemandOrderSearchRequestModel requestModel) {
        return Result.success(demandOrderBiz.searchListManage(requestModel, false));
    }

    /**
     * 获取需求单列表统计
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取需求单列表统计")
    @PostMapping(value = "/searchListStatistics")
    public Result<SearchListStatisticsResponseModel> searchListStatistics(@RequestBody DemandOrderSearchRequestModel requestModel) {
        return Result.success(demandOrderBiz.searchListStatistics(requestModel));
    }

    /**
     * 取消需求单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "取消需求单")
    @PostMapping(value = "/cancelDemandOrder")
    public Result<Boolean> cancelDemandOrder(@RequestBody DemandOrderCancelRequestModel requestModel) {
        demandOrderBiz.cancelDemandOrder(requestModel);
        return Result.success(true);
    }

    /**
     * 获取需求单详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取需求单详情")
    @PostMapping(value = "/getDetail")
    public Result<DemandOrderDetailResponseModel> getDetail(@RequestBody DemandOrderDetailRequestModel requestModel) {
        return Result.success(demandOrderBiz.getDemandOrderDetail(requestModel));
    }

    /**
     * 需求单日志
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "需求单日志")
    @PostMapping(value = "/getDemandOrderLogs")
    public Result<List<GetDemandOrderLogsResponseModel>> getDemandOrderLogs(@RequestBody DemandOrderDetailRequestModel requestModel) {
        return Result.success(demandOrderBiz.getDemandOrderLogs(requestModel.getDemandId()));
    }

    /**
     * 导出需求单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出需求单")
    @PostMapping(value = "/exportDemandOrder")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<DemandOrderResponseModel>> exportDemandOrder(@RequestBody DemandOrderSearchRequestModel requestModel) {
        return Result.success(demandOrderBiz.searchListManage(requestModel, true).getList());
    }

    /**
     * 复制发布详情接口
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "复制发布详情接口")
    @PostMapping(value = "/getPublishDemandOrderDetail")
    public Result<GetPublishDemandOrderDetailResponseModel> getPublishDemandOrderDetail(@RequestBody DemandOrderIdRequestModel requestModel) {
        return Result.success(demandOrderBiz.getPublishDemandOrderDetail(requestModel));
    }

    /**
     * 确认同步网络货运平台
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "确认同步网络货运平台")
    @PostMapping(value = "/confirmSynNetworkFreight")
    public Result<Boolean> confirmSynNetworkFreight(@RequestBody DemandOrderIdListRequestModel requestModel) {
        demandOrderBiz.confirmSynNetworkFreight(requestModel);
        return Result.success(true);
    }

    /**
     * 托盘取消出入库计划和取消预约需求单查询可不可以取消（托盘调用）
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "托盘取消出入库计划和取消预约需求单查询可不可以取消（托盘调用）")
    @PostMapping(value = "/cancelDemandOrderVerifyFromLeYi")
    public Result<Boolean> cancelDemandOrderVerifyFromLeYi(@RequestBody CancelDemandOrderVerifyFromLeYiRequestModel requestModel) {
        demandOrderBiz.cancelDemandOrderVerifyFromLeYi(requestModel);
        return Result.success(true);
    }

    /**
     * 托盘取消出入库计划和取消预约需求单同步取消物流系统（托盘调用）
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "托盘取消出入库计划和取消预约需求单同步取消物流系统（托盘调用）")
    @PostMapping(value = "/cancelDemandOrderFromLeYi")
    public Result<Boolean> cancelDemandOrderFromLeYi(@RequestBody CancelDemandOrderFromLeYiRequestModel requestModel) {
        demandOrderBiz.cancelDemandOrderFromLeYi(requestModel);
        return Result.success(true);
    }

    /**
     * 修改车主详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "需求单修改车主详情查询v1.1.9")
    @PostMapping(value = "/modifyCarrierDetail")
    public Result<ModifyCarrierDetailResponseModel> modifyCarrierDetail(@RequestBody ModifyCarrierDetailRequestModel requestModel) {
        return Result.success(demandOrderBiz.modifyCarrierDetail(requestModel));
    }


    /**
     * 修改车主
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "修改车主")
    @PostMapping(value = "/modifyCarrier")
    public Result<Boolean> modifyCarrier(@RequestBody ModifyCarrierRequestModel requestModel) {
        demandOrderBiz.modifyCarrier(requestModel);
        return Result.success(true);
    }
}
