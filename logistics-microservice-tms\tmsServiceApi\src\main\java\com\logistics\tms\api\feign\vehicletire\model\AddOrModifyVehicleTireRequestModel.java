package com.logistics.tms.api.feign.vehicletire.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
public class AddOrModifyVehicleTireRequestModel {
    @ApiModelProperty("轮胎管理信息Id")
    private Long vehicleTireId;
    @ApiModelProperty("车辆Id")
    private Long vehicleId;
    @ApiModelProperty("司机Id")
    private Long staffId;
    @ApiModelProperty("更换日期")
    private Date replaceDate;
    @ApiModelProperty("轮胎企业")
    private String tireCompany;
    @ApiModelProperty("轮胎牌号列表")
    private List<VehicleTireNoListRequestModel> vehicleTireNoList;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("凭证列表")
    private List<CertificationPicturesInfoRequestModel> fileList;
}
