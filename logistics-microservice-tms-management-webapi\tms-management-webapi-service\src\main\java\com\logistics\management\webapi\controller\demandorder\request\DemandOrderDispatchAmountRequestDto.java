package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


@Data
public class DemandOrderDispatchAmountRequestDto {
    @ApiModelProperty("需求单货物id")
    @NotBlank(message = "需求单货物id不能为空")
    private String demandOrderGoodsId;
    @ApiModelProperty("需求单id")
    @NotBlank(message = "需求单id不能为空")
    private String demandOrderId;
    @ApiModelProperty("预提件数")
    @NotBlank(message = "预提件数不能为空")
    private String loadAmount;
}
