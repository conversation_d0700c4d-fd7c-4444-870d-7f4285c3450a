package com.logistics.management.webapi.client.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CarrierOrderDetailBasicInfoModel {

    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    @ApiModelProperty("提货人")
    private String consignorName;
    @ApiModelProperty("提货人")
    private String consignorMobile;
    @ApiModelProperty("卸货人")
    private String receiverName;
    @ApiModelProperty("卸货人")
    private String receiverMobile;
    @ApiModelProperty("实际提货时间")
    private Date loadTime;
    @ApiModelProperty("实际卸货时间")
    private Date unloadTime;


}
