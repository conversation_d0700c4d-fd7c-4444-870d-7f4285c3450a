package com.logistics.management.webapi.client.region.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/6/5 14:31
 */
@Data
public class RegionCompanyResponseModel {
    @ApiModelProperty("车主公司ID")
    private Long companyId;

    @ApiModelProperty("车主类型 1公司 2 个人")
    private Integer companyType;

    @ApiModelProperty("车主公司名称")
    private String companyName;

    @ApiModelProperty("车主联系人姓名")
    private String contactName;

    @ApiModelProperty("车主联系人手机号")
    private String contactPhone;
}
