package com.logistics.tms.api.feign.mailinginfo;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.mailinginfo.hystrix.MailingInfoServiceApiHystrix;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoDetailRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoListRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.request.MailingInfoModifyRequestModel;
import com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoDetailResponseModel;
import com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "API-MailingInfoServiceApi-邮寄信息配置", tags = "邮寄信息配置")
@FeignClient(name = "logistics-tms-services", fallback = MailingInfoServiceApiHystrix.class)
public interface MailingInfoServiceApi {

    @ApiOperation(value = "邮寄信息列表", tags = "1.2.6")
    @PostMapping(value = "/service/mailingInfo/mailingInfoList")
    Result<PageInfo<MailingInfoListResponseModel>> mailingInfoList(@RequestBody MailingInfoListRequestModel requestModel);

    @ApiOperation(value = "邮寄信息详情", tags = "1.2.6")
    @PostMapping(value = "/service/mailingInfo/mailingInfoDetail")
    Result<MailingInfoDetailResponseModel> mailingInfoDetail(@RequestBody MailingInfoDetailRequestModel requestModel);

    @ApiOperation(value = "邮寄信息编辑", tags = "1.2.6")
    @PostMapping(value = "/service/mailingInfo/mailingInfoModify")
    Result<Boolean> mailingInfoModify(@RequestBody MailingInfoModifyRequestModel requestModel);
}
