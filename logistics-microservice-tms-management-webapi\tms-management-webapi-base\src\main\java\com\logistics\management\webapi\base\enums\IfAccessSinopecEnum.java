package com.logistics.management.webapi.base.enums;

/**
 * 是否入网中石化
 * @Author: sj
 * @Date: 2019/6/10 17:37
 */
public enum IfAccessSinopecEnum {
    NULL(-1,""),
    NOT(0,"否"),
    YES(1,"是"),
    ;

    private Integer key;
    private String value;

    IfAccessSinopecEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static IfAccessSinopecEnum getEnum(Integer key) {
        for (IfAccessSinopecEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return NULL;
    }

    public static IfAccessSinopecEnum getEnumByValue(String value) {
        for (IfAccessSinopecEnum t : values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return null;
    }
}
