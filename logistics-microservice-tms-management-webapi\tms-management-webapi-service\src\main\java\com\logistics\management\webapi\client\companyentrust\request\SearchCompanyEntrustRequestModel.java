package com.logistics.management.webapi.client.companyentrust.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/9/27 14:24
 */
@Data
public class SearchCompanyEntrustRequestModel extends AbstractPageForm<SearchCompanyEntrustRequestModel> {
    private String companyEntrustName;
    @ApiModelProperty("证件补充状态全部为空，待补充 1 齐全2")
    private String certificateSupplement;
    private String createdTimeStart;
    private String createdTimeEnd;
    private String lastModifiedBy;
    private String lastModifiedTimeStart;
    private String lastModifiedTimeEnd;
    @ApiModelProperty("货主类型：1 公司 2 个人")
    private Integer type;
    @ApiModelProperty("来源：1后台添加 2 web注册")
    private Integer source;
}
