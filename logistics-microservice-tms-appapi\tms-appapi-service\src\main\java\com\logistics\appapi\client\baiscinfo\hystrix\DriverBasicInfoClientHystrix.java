package com.logistics.appapi.client.baiscinfo.hystrix;

import com.logistics.appapi.client.baiscinfo.DriverBasicInfoClient;
import com.logistics.appapi.client.baiscinfo.request.*;
import com.logistics.appapi.client.baiscinfo.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/11 9:07
 */
@Component
public class DriverBasicInfoClientHystrix implements DriverBasicInfoClient {
    @Override
    public Result<DriverBasicInfoResponseModel> driverBasicInfo() {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> driverBasicInfoSubmit(DriverBasicInfoSubmitRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> getVerifyCode(GetPersonAuthVerifyCodeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<VerifyPersonTwoElementsResponseModel> verifyPersonTwoElements(VerifyPersonTwoElementsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<VerifyPersonThreeElementsResponseModel> verifyPersonThreeElements(VerifyPersonThreeElementsRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<FaceRecognitionResponseModel> faceRecognition(FaceRecognitionRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<FaceRecognitionResultResponseModel> faceRecognitionResult(FaceRecognitionResultRequestModel requestModel) {
        return Result.timeout();
    }
}
