<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TRouteEnquiryAddressMapper" >
    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TRouteEnquiryAddress" >
        <foreach collection="recordList" item="item" separator=";">
            insert into t_route_enquiry_address
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.routeEnquiryId != null" >
                    route_enquiry_id,
                </if>
                <if test="item.fromProvinceId != null" >
                    from_province_id,
                </if>
                <if test="item.fromProvinceName != null" >
                    from_province_name,
                </if>
                <if test="item.fromCityId != null" >
                    from_city_id,
                </if>
                <if test="item.fromCityName != null" >
                    from_city_name,
                </if>
                <if test="item.fromAreaId != null" >
                    from_area_id,
                </if>
                <if test="item.fromAreaName != null" >
                    from_area_name,
                </if>
                <if test="item.fromWarehouse != null" >
                    from_warehouse,
                </if>
                <if test="item.toProvinceId != null" >
                    to_province_id,
                </if>
                <if test="item.toProvinceName != null" >
                    to_province_name,
                </if>
                <if test="item.toCityId != null" >
                    to_city_id,
                </if>
                <if test="item.toCityName != null" >
                    to_city_name,
                </if>
                <if test="item.toAreaId != null" >
                    to_area_id,
                </if>
                <if test="item.toAreaName != null" >
                    to_area_name,
                </if>
                <if test="item.distance != null" >
                    distance,
                </if>
                <if test="item.quotePriceType != null" >
                    quote_price_type,
                </if>
                <if test="item.quotePrice != null" >
                    quote_price,
                </if>
                <if test="item.quoteRemark != null" >
                    quote_remark,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.routeEnquiryId != null" >
                    #{item.routeEnquiryId,jdbcType=BIGINT},
                </if>
                <if test="item.fromProvinceId != null" >
                    #{item.fromProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.fromProvinceName != null" >
                    #{item.fromProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.fromCityId != null" >
                    #{item.fromCityId,jdbcType=BIGINT},
                </if>
                <if test="item.fromCityName != null" >
                    #{item.fromCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.fromAreaId != null" >
                    #{item.fromAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.fromAreaName != null" >
                    #{item.fromAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.fromWarehouse != null" >
                    #{item.fromWarehouse,jdbcType=VARCHAR},
                </if>
                <if test="item.toProvinceId != null" >
                    #{item.toProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.toProvinceName != null" >
                    #{item.toProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.toCityId != null" >
                    #{item.toCityId,jdbcType=BIGINT},
                </if>
                <if test="item.toCityName != null" >
                    #{item.toCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.toAreaId != null" >
                    #{item.toAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.toAreaName != null" >
                    #{item.toAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.distance != null" >
                    #{item.distance,jdbcType=DECIMAL},
                </if>
                <if test="item.quotePriceType != null" >
                    #{item.quotePriceType,jdbcType=INTEGER},
                </if>
                <if test="item.quotePrice != null" >
                    #{item.quotePrice,jdbcType=DECIMAL},
                </if>
                <if test="item.quoteRemark != null" >
                    #{item.quoteRemark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TRouteEnquiryAddress" >
        <foreach collection="recordList" item="item" separator=";">
            update t_route_enquiry_address
            <set >
                <if test="item.routeEnquiryId != null" >
                    route_enquiry_id = #{item.routeEnquiryId,jdbcType=BIGINT},
                </if>
                <if test="item.fromProvinceId != null" >
                    from_province_id = #{item.fromProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.fromProvinceName != null" >
                    from_province_name = #{item.fromProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.fromCityId != null" >
                    from_city_id = #{item.fromCityId,jdbcType=BIGINT},
                </if>
                <if test="item.fromCityName != null" >
                    from_city_name = #{item.fromCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.fromAreaId != null" >
                    from_area_id = #{item.fromAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.fromAreaName != null" >
                    from_area_name = #{item.fromAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.fromWarehouse != null" >
                    from_warehouse = #{item.fromWarehouse,jdbcType=VARCHAR},
                </if>
                <if test="item.toProvinceId != null" >
                    to_province_id = #{item.toProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.toProvinceName != null" >
                    to_province_name = #{item.toProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.toCityId != null" >
                    to_city_id = #{item.toCityId,jdbcType=BIGINT},
                </if>
                <if test="item.toCityName != null" >
                    to_city_name = #{item.toCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.toAreaId != null" >
                    to_area_id = #{item.toAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.toAreaName != null" >
                    to_area_name = #{item.toAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.distance != null" >
                    distance = #{item.distance,jdbcType=DECIMAL},
                </if>
                <if test="item.quotePriceType != null" >
                    quote_price_type = #{item.quotePriceType,jdbcType=INTEGER},
                </if>
                <if test="item.quotePrice != null" >
                    quote_price = #{item.quotePrice,jdbcType=DECIMAL},
                </if>
                <if test="item.quoteRemark != null" >
                    quote_remark = #{item.quoteRemark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getAddressCount" resultType="com.logistics.tms.biz.routeenquiry.model.RouteEnquiryAddressCountModel">
        select
        route_enquiry_id as routeEnquiryId,
        sum(1) as addressCount
        from t_route_enquiry_address
        where route_enquiry_id in
        <foreach collection="routeEnquiryIdList" item="routeEnquiryId" open="(" close=")" separator=",">
            #{routeEnquiryId,jdbcType=BIGINT}
        </foreach>
        and valid = 1
        group by route_enquiry_id
    </select>

    <select id="getAddressByRouteEnquiryId" resultType="com.logistics.tms.controller.routeenquiry.response.GetRouteEnquiryDetailAddressListResponseModel">
        select
        id as routeEnquiryAddressId,
        from_province_name as fromProvinceName,
        from_city_name as fromCityName,
        from_area_name as fromAreaName,
        from_warehouse as fromWarehouse,
        to_province_name as toProvinceName,
        to_city_name as toCityName,
        to_area_name as toAreaName,
        distance,
        quote_price_type as quotePriceType,
        quote_price as quotePrice,
        quote_remark as quoteRemark
        from t_route_enquiry_address
        where route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT}
        and valid = 1
        order by id desc
    </select>

    <select id="getAddressForWebByRouteEnquiryId" resultType="com.logistics.tms.controller.routeenquiry.response.GetRouteEnquiryDetailAddressListResponseModel">
        select
        trea.id as routeEnquiryAddressId,
        trea.from_province_name as fromProvinceName,
        trea.from_city_name as fromCityName,
        trea.from_area_name as fromAreaName,
        trea.from_warehouse as fromWarehouse,
        trea.to_province_name as toProvinceName,
        trea.to_city_name as toCityName,
        trea.to_area_name as toAreaName,

        treaq.distance,
        treaq.quote_price_type as quotePriceType,
        treaq.quote_price as quotePrice,
        treaq.quote_remark as quoteRemark
        from t_route_enquiry_address trea
        left join t_route_enquiry_address_quote treaq on trea.id = treaq.route_enquiry_address_id
            and treaq.route_enquiry_company_id = #{routeEnquiryCompanyId,jdbcType=BIGINT} and treaq.valid = 1
        where trea.route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT}
        and trea.valid = 1
        order by trea.id desc
    </select>

    <select id="searchSummaryList" resultType="com.logistics.tms.controller.routeenquiry.response.SearchRouteEnquirySummaryListResponseModel">
        select
        trea.id as routeEnquiryAddressId,
        trea.from_province_name as fromProvinceName,
        trea.from_city_name as fromCityName,
        trea.from_area_name as fromAreaName,
        trea.from_warehouse as fromWarehouse,
        trea.to_province_name as toProvinceName,
        trea.to_city_name as toCityName,
        trea.to_area_name as toAreaName,
        trea.distance,
        trea.quote_price_type as quotePriceType,
        trea.quote_price as quotePrice,
        trea.created_by as createdBy,
        trea.created_time as createdTime,

        tre.order_code as orderCode,
        tre.goods_name as goodsName,
        tre.quote_start_time as quoteStartTime,
        tre.quote_end_time as quoteEndTime,
        tre.contract_code as contractCode,
        tre.remark,

        trec.company_carrier_type as companyCarrierType,
        trec.company_carrier_name as companyCarrierName,
        trec.carrier_contact_name as carrierContactName,
        AES_DECRYPT(UNHEX(trec.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactPhone
        from t_route_enquiry_address trea
        left join t_route_enquiry tre on tre.id = trea.route_enquiry_id and tre.valid = 1
        left join t_route_enquiry_company trec on tre.id = trec.route_enquiry_id and trec.quote_status = 1 and trec.valid = 1
        where tre.status = 4
        <if test="params.orderCode != null and params.orderCode != ''">
            and instr(tre.order_code, #{params.orderCode,jdbcType=VARCHAR})
        </if>
        <if test="params.loadAddress != null and params.loadAddress != ''">
            and instr(CONCAT(trea.from_province_name,trea.from_city_name,trea.from_area_name,trea.from_warehouse),#{params.loadAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.unloadAddress != null and params.unloadAddress != ''">
            and instr(CONCAT(trea.to_province_name,trea.to_city_name,trea.to_area_name),#{params.unloadAddress,jdbcType=VARCHAR})
        </if>
        <if test="params.companyCarrierName != null and params.companyCarrierName !=''">
            and ((trec.company_carrier_type = 1 and instr(trec.company_carrier_name,#{params.companyCarrierName,jdbcType=VARCHAR}))
            or (trec.company_carrier_type = 2 and (instr(trec.carrier_contact_name,#{params.companyCarrierName,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(trec.carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') ,#{params.companyCarrierName,jdbcType=VARCHAR}))))
        </if>
        <if test="params.createdBy != null and params.createdBy != ''">
            and instr(trea.created_by, #{params.createdBy,jdbcType=VARCHAR})
        </if>
        <if test="params.createdTimeStart != null and params.createdTimeStart != ''">
            and trea.created_time &gt;= DATE_FORMAT(#{params.createdTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="params.createdTimeEnd != null and params.createdTimeEnd != ''">
            and trea.created_time &lt;= DATE_FORMAT(#{params.createdTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        order by trea.id desc
    </select>

    <select id="getByRouteEnquiryId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_route_enquiry_address
        where valid = 1
        and route_enquiry_id = #{routeEnquiryId,jdbcType=BIGINT}
    </select>
</mapper>