package com.logistics.management.webapi.api.feign.loanrecord.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/9/30 9:37
 */
@Data
public class LoanRecordListRequestDto extends AbstractPageForm<LoanRecordListRequestDto>{
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机信息:手机号或者姓名")
    private String driverInfo;
    @ApiModelProperty("贷款状态：0 待结算，1 部分结算，2 已结算")
    private String status;
    @ApiModelProperty("贷款ID拼接,譬如：'123,456,789'")
    private String loanRecordIds;


}
