package com.logistics.management.webapi.controller.oilfilled.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OilFilledListResponseDto {

    @ApiModelProperty(value = "充油记录ID")
    private String oilFilledId = "";
    @ApiModelProperty(value = "结算状态0 待结算，1 已结算")
    private String status = "";
    @ApiModelProperty(value = "状态文字")
    private String statusLabel;
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("车辆机构：1 自主 2 外部 3 自营")
    private String vehicleProperty = "";
    @ApiModelProperty("车辆机构展示文本")
    private String vehiclePropertyLabel = "";
    @ApiModelProperty(value = "司机姓名")
    private String name = "";
    @ApiModelProperty(value = "司机手机号")
    private String mobile = "";
    @ApiModelProperty(value = "充油金额")
    private String oilFilledFee = "";
    @ApiModelProperty(value = "费用来源：10 充油，20 退款")
    private String source = "";
    private String sourceDesc = "";
    @ApiModelProperty(value = "充值时间")
    private String oilFilledDate = "";
    @ApiModelProperty(value = "充油方式：1 充油卡，2 加油车")
    private String oilFilledType = "";
    @ApiModelProperty(value = "充油方式文字")
    private String oilFilledTypeLabel = "";
    @ApiModelProperty(value = "升数")
    private String liter = "";
    @ApiModelProperty(value = "充值积分")
    private String topUpIntegral = "";
    @ApiModelProperty(value = "奖励积分")
    private String rewardIntegral = "";
    @ApiModelProperty(value = "副卡卡号")
    private String subCardNumber = "";
    @ApiModelProperty(value = "副卡所属人")
    private String subCardOwner = "";
    @ApiModelProperty(value = "备注")
    private String remark = "";
    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy = "";
    @ApiModelProperty(value = "操作时间")
    private String lastModifiedTime = "";
    @ApiModelProperty(value = "退款原因类型：0 其他，10 丢失副卡，20 车辆过户，30 车辆报废，40 充油充错")
    private String refundReasonType = "";
    private String refundReasonTypeDesc = "";
    @ApiModelProperty(value = "退款原因描述")
    private String refundReason = "";

}
