package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TCustomerAccountRelation extends BaseEntity {
    /**
    * 用户角色：1 tms司机
    */
    @ApiModelProperty("用户角色：1 tms司机")
    private Integer userRole;

    /**
    * 账号id
    */
    @ApiModelProperty("账号id")
    private Long accountId;

    /**
    * 联系人id(user_role=1，就是人员表id)
    */
    @ApiModelProperty("联系人id(user_role=1，就是人员表id)")
    private Long userId;

    /**
    * 账号是否关闭：0 否，1 是
    */
    @ApiModelProperty("账号是否关闭：0 否，1 是")
    private Integer ifClose;

    /**
    * 启用 1 禁用 0
    */
    @ApiModelProperty("启用 1 禁用 0")
    private Integer enabled;
}