package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TVehicleSafeCheck;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TVehicleSafeCheckMapper extends BaseMapper<TVehicleSafeCheck> {

    int getCountBySpecifyPeriod(@Param("period") String period);

    TVehicleSafeCheck getBySpecifyPeriod(@Param("period") String period);
}