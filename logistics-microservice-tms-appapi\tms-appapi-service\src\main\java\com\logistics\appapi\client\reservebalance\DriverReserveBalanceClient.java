package com.logistics.appapi.client.reservebalance;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.reservebalance.hystrix.DriverReserveBalanceClientHystrix;
import com.logistics.appapi.client.reservebalance.response.ReserveBalanceInfoResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@Api(value = "司机备用金余额台账")
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = DriverReserveBalanceClientHystrix.class,
        path = "/service/DriverReserveBalance")
public interface DriverReserveBalanceClient {

    @ApiOperation(value = "当前备用金信息查询")
    @PostMapping(value = "/applet/reserveBalanceInfo")
    Result<ReserveBalanceInfoResponseModel> reserveBalanceInfo();
}
