package com.logistics.tms.base.enums;

public enum FreightRuleCalcRuleEnum {
    NULL(-99,""),
    BY_PACKAGE(1,"(元/件)"),
    BY_WEIGHT(2,"(元/吨)"),;
    private Integer key;
    private String value;

    FreightRuleCalcRuleEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static FreightRuleCalcRuleEnum getEnum(Integer key) {
        for (FreightRuleCalcRuleEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return NULL;
    }
}
