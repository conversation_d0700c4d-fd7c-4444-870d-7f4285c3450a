package com.logistics.tms.biz.loanrecord;

import com.github.pagehelper.PageInfo;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel;
import com.logistics.tms.api.feign.loanrecord.model.*;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.api.feign.violationregulation.model.UpdateCertificationPicturesModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/9/30 10:17
 */
@Service
public class LoanRecordBiz {
    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TVehicleBasicMapper tVehicleBasicMapper;
    @Autowired
    private TStaffBasicMapper tStaffBasicMapper;
    @Autowired
    private TLoanRecordsMapper tLoanRecordsMapper;
    @Autowired
    private TCertificationPicturesMapper tCertificationPicturesMapper;
    @Autowired
    private TLoanSettlementRecordMapper tLoanSettlementRecordMapper;
    @Autowired
    private TOperateLogsMapper tOperateLogsMapper;
    @Autowired
    private TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper;

    /**
     * 贷款记录列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<LoanRecordListResponseModel> searchList(LoanRecordListRequestModel requestModel) {
        requestModel.enablePaging();
        List<LoanRecordListResponseModel> loadRecordList = tLoanRecordsMapper.getLoanRecordList(requestModel);
        return new PageInfo<>(loadRecordList == null ? new ArrayList<>() : loadRecordList);
    }

    /**
     * 列表汇总
     *
     * @param requestModel
     * @return
     */
    public SummaryLoanRecordResponseModel getSummary(LoanRecordListRequestModel requestModel) {
        return tLoanRecordsMapper.getSummaryLoanRecordInfo(requestModel);
    }

    /**
     * 修改/保存 贷款记录
     *
     * @param requestModel
     */
    @Transactional
    public void saveOrUpdate(SaveOrUpdateLoanRecordRequestModel requestModel) {
        TLoanRecords tLoanRecords = new TLoanRecords();
        MapperUtils.mapper(requestModel, tLoanRecords);

        VehicleBasicPropertyModel dbVehicleBasic = tVehicleBasicMapper.getVehicleBasicPropertyById(requestModel.getVehicleId());
        if (requestModel.getVehicleId() == null || dbVehicleBasic == null) {
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_IS_EMPTY);
        }

        TStaffBasic dbStaffBasic = tStaffBasicMapper.selectByPrimaryKey(requestModel.getStaffId());
        if (requestModel.getStaffId() == null || dbStaffBasic == null || CommonConstant.INTEGER_ZERO.equals(dbStaffBasic.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_VEHICLE_RELATION_EMPTY);
        }

        //总价
        BigDecimal calculationCarPrice = requestModel.getPurchaseTax().add(requestModel.getInsurancePremium()).add(requestModel.getNakedCarPrice());
        if (calculationCarPrice.compareTo(requestModel.getCarPrice()) != CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.CALCULATION_CAR_PRICE_MISTAKE);
        }
        //贷款总费用
        BigDecimal calculationLoanFee = calculationCarPrice.subtract(requestModel.getDriverExpense());
        if (calculationLoanFee.compareTo(requestModel.getLoanFee()) != CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.CALCULATION_LOAN_FEE_MISTAKE);
        }
        Date currentNow = DateUtils.stringToDate(DateUtils.dateToString(new Date(), DateUtils.DATE_TO_STRING_SHORT_PATTERN), DateUtils.DATE_TO_STRING_SHORT_PATTERN);

        Date startDate = tLoanRecords.getLoanStartTime();
        Date endDate = DateUtils.add(startDate, Calendar.MONTH, requestModel.getLoanPeriods());
        tLoanRecords.setLoadFinishTime(endDate);
        tLoanRecords.setVehicleNo(dbVehicleBasic.getVehicleNo());
        tLoanRecords.setName(dbStaffBasic.getName());
        tLoanRecords.setMobile(dbStaffBasic.getMobile());

        if (requestModel.getLoanRecordId() == null || requestModel.getLoanRecordId() <= CommonConstant.LONG_ZERO) {//新增
            TLoanRecords dbOneLoanRecords = tLoanRecordsMapper.getLoanRecordsByVehicleId(requestModel.getVehicleId());
            if (dbOneLoanRecords != null) {
                throw new BizException(CarrierDataExceptionEnum.LOAN_RECORDS_HAS_EXIST);
            }

            //是否内部车辆
            if (!(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(dbVehicleBasic.getVehicleProperty()) ||
                    VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(dbVehicleBasic.getVehicleProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
            }
            //是否运营中
            if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(dbVehicleBasic.getOperatingState())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_OUTAGE_ERROR);
            }
            //是否为牵引车或者一体车
            if (!VehicleCategoryEnum.TRACTOR.getKey().equals(dbVehicleBasic.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(dbVehicleBasic.getVehicleCategory())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_ERROR);
            }
            //是否内部司机
            if (!(StaffPropertyEnum.OWN_STAFF.getKey().equals(dbStaffBasic.getStaffProperty()) ||
                    StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(dbStaffBasic.getStaffProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_PROPERTY_ERROR);
            }
            //截止时间是否小于当前时间
            if (endDate.before(currentNow)) {
                throw new BizException(CarrierDataExceptionEnum.LOAN_RECORDS_START_DATE_ERROR);
            }

            tLoanRecords.setStatus(SettlementStatusEnum.WAIT.getKey());
            commonBiz.setBaseEntityAdd(tLoanRecords, BaseContextHandler.getUserName());
            tLoanRecordsMapper.insertSelective(tLoanRecords);
            tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(tLoanRecords.getId(), OperateLogsOperateTypeEnum.LOAN_RECORDS_ADD, requestModel.getRemark(), BaseContextHandler.getUserName()));
            requestModel.setLoanRecordId(tLoanRecords.getId());

        } else {//修改

            TLoanRecords dbTwoLoanRecords = tLoanRecordsMapper.selectByPrimaryKey(requestModel.getLoanRecordId());
            if (dbTwoLoanRecords == null) {
                throw new BizException(CarrierDataExceptionEnum.LOAN_RECORDS_NOT_EXIST);
            }
            TLoanRecords dbThrLoanRecords = tLoanRecordsMapper.getLoanRecordsByVehicleId(requestModel.getVehicleId());
            if (dbThrLoanRecords != null && !requestModel.getLoanRecordId().equals(dbThrLoanRecords.getId())) {
                throw new BizException(CarrierDataExceptionEnum.LOAN_RECORDS_HAS_EXIST);
            }
            if (!SettlementStatusEnum.WAIT.getKey().equals(dbTwoLoanRecords.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.SETTLEMENT_STATUS_ERROR_NOT_UPDATE);
            }

            //修改了车辆则判断车辆是否符合条件
            if (!dbTwoLoanRecords.getVehicleId().equals(requestModel.getVehicleId())) {
                //是否内部车辆
                if (!(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(dbVehicleBasic.getVehicleProperty()) ||
                        VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(dbVehicleBasic.getVehicleProperty()))) {
                    throw new BizException(CarrierDataExceptionEnum.VEHICLE_PROPERTY_ERROR);
                }
                //是否运营中
                if (!VehicleOutageStatus.IN_OPERATION.getKey().equals(dbVehicleBasic.getOperatingState())) {
                    throw new BizException(CarrierDataExceptionEnum.VEHICLE_OUTAGE_ERROR);
                }
                //是否牵引车或者一体车
                if (!VehicleCategoryEnum.TRACTOR.getKey().equals(dbVehicleBasic.getVehicleCategory()) && !VehicleCategoryEnum.WHOLE.getKey().equals(dbVehicleBasic.getVehicleCategory())) {
                    throw new BizException(CarrierDataExceptionEnum.VEHICLE_CATEGORY_ERROR);
                }
            }
            //修改了司机则判断司机是否符合条件
            if (!dbTwoLoanRecords.getStaffId().equals(requestModel.getStaffId()) &&
                    !(StaffPropertyEnum.OWN_STAFF.getKey().equals(dbStaffBasic.getStaffProperty()) ||
                            StaffPropertyEnum.AFFILIATION_STAFF.getKey().equals(dbStaffBasic.getStaffProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_PROPERTY_ERROR);
            }

            List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.LOAN.getKey(), requestModel.getLoanRecordId());
            if (ListUtils.isEmpty(tVehicleSettlementRelationList) && endDate.before(currentNow)) {
                throw new BizException(CarrierDataExceptionEnum.LOAN_RECORDS_START_DATE_ERROR);
            }
            //未产生账单月修，改截至时间不能小于当前时间; 已产生账单月数据可修改字段：生产地、生产厂商、出产日期、司机、备注
            if (ListUtils.isNotEmpty(tVehicleSettlementRelationList)) {
                this.setOnlyRead(tLoanRecords);
            }
            tLoanRecords.setId(dbTwoLoanRecords.getId());
            commonBiz.setBaseEntityModify(tLoanRecords, BaseContextHandler.getUserName());
            tLoanRecordsMapper.updateByPrimaryKeySelective(tLoanRecords);
            tOperateLogsMapper.insertSelective(commonBiz.addOperateLogs(tLoanRecords.getId(), OperateLogsOperateTypeEnum.LOAN_RECORDS_UPDATE, requestModel.getRemark(), BaseContextHandler.getUserName()));
            requestModel.setLoanRecordId(tLoanRecords.getId());

            List<TCertificationPictures> certificationList = tCertificationPicturesMapper.getByObjectIdType(requestModel.getLoanRecordId(), CertificationPicturesObjectTypeEnum.T_LOAN_RECORDS.getObjectType(), CertificationPicturesFileTypeEnum.LOAN_RECORDS_FILE.getFileType());
            if (ListUtils.isNotEmpty(certificationList)) {
                UpdateCertificationPicturesModel updateModel = new UpdateCertificationPicturesModel();
                updateModel.setObjectId(dbTwoLoanRecords.getId());
                updateModel.setObjectType(CertificationPicturesObjectTypeEnum.T_LOAN_RECORDS.getObjectType());
                updateModel.setFileType(CertificationPicturesFileTypeEnum.LOAN_RECORDS_FILE.getFileType());
                updateModel.setFilePaths(LocalStringUtil.listTostring(requestModel.getFileList(), ','));
                tCertificationPicturesMapper.updateFilePath(updateModel);
            }

            if (ListUtils.isNotEmpty(requestModel.getFileList())) {
                List<String> batchInsertList = new ArrayList<>();
                for (String filePath : requestModel.getFileList()) {
                    if (!filePath.contains(configKeyConstant.loanRecordImageCatalog)) {
                        batchInsertList.add(filePath);
                    }
                }
                requestModel.setFileList(batchInsertList);
            }

        }

        //批量插入图片信息
        if (ListUtils.isNotEmpty(requestModel.getFileList())) {
            List<TCertificationPictures> batchInsertList = new ArrayList<>();
            TCertificationPictures pageCertificationPictures;
            for (String picturesPath : requestModel.getFileList()) {
                pageCertificationPictures = new TCertificationPictures();
                pageCertificationPictures.setObjectId(requestModel.getLoanRecordId());
                pageCertificationPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_LOAN_RECORDS.getObjectType());
                pageCertificationPictures.setFileType(CertificationPicturesFileTypeEnum.LOAN_RECORDS_FILE.getFileType());
                pageCertificationPictures.setFileTypeName(CertificationPicturesFileTypeEnum.LOAN_RECORDS_FILE.getFileName());
                pageCertificationPictures.setUploadTime(new Date());
                pageCertificationPictures.setUploadUserName(BaseContextHandler.getUserName());
                pageCertificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.LOAN_RECORDS_FILE_TYPE.getKey(), requestModel.getVehicleNo(), picturesPath, null));
                pageCertificationPictures.setSuffix(pageCertificationPictures.getFilePath().substring(pageCertificationPictures.getFilePath().lastIndexOf('.')));
                commonBiz.setBaseEntityAdd(pageCertificationPictures, BaseContextHandler.getUserName());
                batchInsertList.add(pageCertificationPictures);
            }
            tCertificationPicturesMapper.batchInsert(batchInsertList);
        }
    }

    /**
     * 设置不可修改字段
     *
     * @param tLoanRecords
     */
    public void setOnlyRead(TLoanRecords tLoanRecords) {
        if (tLoanRecords == null) {
            return;
        }
        tLoanRecords.setStatus(null);
        tLoanRecords.setVehicleId(null);
        tLoanRecords.setVehicleNo(null);
        tLoanRecords.setBrand(null);
        tLoanRecords.setModel(null);
        tLoanRecords.setVehicleIdentificationNumber(null);
        tLoanRecords.setEngineNumber(null);
        tLoanRecords.setBodyColor(null);
        tLoanRecords.setStaffId(null);
        tLoanRecords.setMobile(null);
        tLoanRecords.setIdentityNumber(null);
        tLoanRecords.setNakedCarPrice(null);
        tLoanRecords.setInsurancePremium(null);
        tLoanRecords.setPurchaseTax(null);
        tLoanRecords.setCarPrice(null);
        tLoanRecords.setDriverExpense(null);
        tLoanRecords.setLoanFee(null);
        tLoanRecords.setLoanPeriods(null);
        tLoanRecords.setLoanStartTime(null);
        tLoanRecords.setLoadFinishTime(null);
        tLoanRecords.setLoanRate(null);
        tLoanRecords.setLoanCommission(null);
        tLoanRecords.setLoanInterest(null);
    }

    /**
     * 详情
     *
     * @param requestModel
     * @return
     */
    public LoanRecordDetailResponseModel getDetail(LoanRecordDetailRequestModel requestModel) {
        TLoanRecords dbLoanRecords = tLoanRecordsMapper.selectByPrimaryKey(requestModel.getLoanRecordId());
        if (dbLoanRecords == null) {
            throw new BizException(CarrierDataExceptionEnum.LOAN_RECORDS_NOT_EXIST);
        }

        LoanRecordDetailResponseModel responseModel = MapperUtils.mapper(dbLoanRecords, LoanRecordDetailResponseModel.class);
        responseModel.setLoanRecordId(dbLoanRecords.getId());

        List<TCertificationPictures> dbFileList = tCertificationPicturesMapper.getByObjectIdType(dbLoanRecords.getId(), CertificationPicturesObjectTypeEnum.T_LOAN_RECORDS.getObjectType(), CertificationPicturesFileTypeEnum.LOAN_RECORDS_FILE.getFileType());
        if (ListUtils.isNotEmpty(dbFileList)) {
            List<LoanRecordFileResponseModel> fileList = new ArrayList<>();
            LoanRecordFileResponseModel fileModel;
            for (TCertificationPictures tempFile : dbFileList) {
                fileModel = new LoanRecordFileResponseModel();
                fileModel.setFileId(tempFile.getId());
                fileModel.setRelativeFilepath(tempFile.getFilePath());
                fileList.add(fileModel);
            }
            responseModel.setFileList(fileList);
        }

        //查询该贷款费用是否已经关联了结算数据
        List<TVehicleSettlementRelation> tVehicleSettlementRelationList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.LOAN.getKey(), dbLoanRecords.getId());
        if (ListUtils.isNotEmpty(tVehicleSettlementRelationList)) {
            responseModel.setIfSettlement(CommonConstant.INTEGER_ONE);
        }

        return responseModel;
    }


    /**
     * 结算记录
     *
     * @param requestModel
     * @return
     */
    public ExportSettlementRecordResponseModel getSettlementRecordList(LoanSettlementRecordRequestModel requestModel) {
        ExportSettlementRecordResponseModel responseModel = new ExportSettlementRecordResponseModel();
        List<LoanSettlementRecordResponseModel> responseModelList = new ArrayList<>();

        List<TLoanSettlementRecord> loanSettlementRecordList = tLoanSettlementRecordMapper.getSettlementRecordList(requestModel.getLoanRecordId());
        if (ListUtils.isNotEmpty(loanSettlementRecordList)) {
            LoanSettlementRecordResponseModel loanSettlementRecordModel;
            for (TLoanSettlementRecord tempSettlementRecord : loanSettlementRecordList) {
                loanSettlementRecordModel = new LoanSettlementRecordResponseModel();
                loanSettlementRecordModel.setLoanSettlementRecordId(tempSettlementRecord.getId());
                loanSettlementRecordModel.setLoanRecordsId(tempSettlementRecord.getLoanRecordsId());
                loanSettlementRecordModel.setSettlementDate(tempSettlementRecord.getSettlementDate());
                loanSettlementRecordModel.setSettlementFee(tempSettlementRecord.getSettlementFee());
                loanSettlementRecordModel.setRemainingRepaymentFee(tempSettlementRecord.getRemainingRepaymentFee());
                loanSettlementRecordModel.setTotalFee(tempSettlementRecord.getTotalFee());
                loanSettlementRecordModel.setLastModifiedBy(tempSettlementRecord.getLastModifiedBy());
                loanSettlementRecordModel.setCreatedTime(tempSettlementRecord.getCreatedTime());
                responseModelList.add(loanSettlementRecordModel);
            }
            responseModel.setSettlementRecordList(responseModelList);

            TLoanRecords dbLoanRecords = tLoanRecordsMapper.selectByPrimaryKey(loanSettlementRecordList.get(CommonConstant.INTEGER_ZERO).getLoanRecordsId());
            responseModel.setVehicleNo(dbLoanRecords.getVehicleNo());
        }

        return responseModel;
    }

    /**
     * 操作记录
     *
     * @param requestModel
     * @return
     */
    public ExportOperationRecordResponseModel getOperationRecords(LoanOperationRecordRequestModel requestModel) {
        ExportOperationRecordResponseModel responseModel = new ExportOperationRecordResponseModel();
        List<LoanOperationRecordResponseModel> responseModelList = new ArrayList<>();

        List<ViewLogResponseModel> operateLogList = tOperateLogsMapper.selectLogsByCondition(OperateLogsObjectTypeEnum.LOAN_RECORDS.getKey(), requestModel.getLoanRecordId(), null);
        if (ListUtils.isNotEmpty(operateLogList)) {
            LoanOperationRecordResponseModel operationRecordResponseModel;
            for (ViewLogResponseModel logResponseModel : operateLogList) {
                operationRecordResponseModel = new LoanOperationRecordResponseModel();
                operationRecordResponseModel.setOperateTime(logResponseModel.getOperateTime());
                operationRecordResponseModel.setOperateType(logResponseModel.getOperateType());
                operationRecordResponseModel.setOperateUserName(logResponseModel.getOperateUserName());
                responseModelList.add(operationRecordResponseModel);
            }
            responseModel.setOperationRecordList(responseModelList);

            TLoanRecords dbLoanRecords = tLoanRecordsMapper.selectByPrimaryKey(operateLogList.get(CommonConstant.INTEGER_ZERO).getObjectId());
            responseModel.setVehicleNo(dbLoanRecords.getVehicleNo());
        }

        return responseModel;
    }
}
