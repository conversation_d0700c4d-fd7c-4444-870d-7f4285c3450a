package com.logistics.management.webapi.controller.freightconfig;

import com.github.pagehelper.PageInfo;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelAssociateCarrier;
import com.logistics.management.webapi.base.constant.ExportExcelShippingFreight;
import com.logistics.management.webapi.client.shipingfreight.ShippingFreightClient;
import com.logistics.management.webapi.client.shipingfreight.request.*;
import com.logistics.management.webapi.client.shipingfreight.response.AssociateCarrierListRespModel;
import com.logistics.management.webapi.client.shipingfreight.response.ListShippingFreightListRespModel;

import com.logistics.management.webapi.controller.freightconfig.request.shipping.*;
import com.logistics.management.webapi.controller.freightconfig.response.shipping.AssociateCarrierListRespDto;
import com.logistics.management.webapi.controller.freightconfig.response.shipping.ListShippingFreightListRespDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tray.core.base.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;


/**
 * 零担运价管理
 */
@RestController
@RequestMapping(value = "/api/freight/shipping")
public class ShippingFreightController {

    @Resource
    private ShippingFreightClient shippingFreightClient;



    /**
     * 零担运价管理列表  v2.42
     * @return
     */
    @PostMapping(value = "/getList")
    public Result<PageInfo<ListShippingFreightListRespDto>> getList(@RequestBody ListShippingFreightListReqDto reqDto) {

        Result<PageInfo<ListShippingFreightListRespModel>> pageInfoResult = shippingFreightClient.getList(MapperUtils.mapper(reqDto, ListShippingFreightListReqModel.class));
        pageInfoResult.throwException();
        PageInfo data = pageInfoResult.getData();
        List<ListShippingFreightListRespDto> dtoList = MapperUtils.mapper(pageInfoResult.getData().getList(), ListShippingFreightListRespDto.class);
        data.setList(dtoList);
        return Result.success(data);
    }


    /**
     * 零担运价管理列表导出  v2.42
     * @return
     */
    @GetMapping(value = "/exportList")
    void exportList(HttpServletResponse response) {
        ListShippingFreightListReqModel requestModel = new ListShippingFreightListReqModel();;
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<ListShippingFreightListRespModel>> pageInfoResult = shippingFreightClient.getList(requestModel);
        pageInfoResult.throwException();
        List<ListShippingFreightListRespDto> dtoList = MapperUtils.mapper(pageInfoResult.getData().getList(), ListShippingFreightListRespDto.class);
        String fileName = "零担运价管理列表";
        Map<String,String> exportMap = ExportExcelShippingFreight.getDateShippingFreightMap();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }

    /**
     * 零担运价管理新增  v2.42
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/add")
    public Result<Boolean> add(@RequestBody @Valid AddShippingFreightReqDto requestDto) {
        Result<Boolean> result = shippingFreightClient.add(MapperUtils.mapper(requestDto, AddShippingFreightReqModel.class));
        result.throwException();
        return result;
    }




    /**
     * 零担运价管理关联承运商  v2.42
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/associateCarrier")
    public Result<Boolean> associateCarrier(@RequestBody @Valid AssociateCarrierReqDto requestDto) {
        Result<Boolean> result = shippingFreightClient.associateCarrier(MapperUtils.mapper(requestDto, AssociateCarrierReqModel.class));
        result.throwException();
        return result;
    }



    /**
     * 关联承运商列表  v2.42
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/associateCarrierList")
    public Result<PageInfo<AssociateCarrierListRespDto>> associateCarrierList(@RequestBody @Valid ShippingFreightIdReqDto requestDto) {
        Result<PageInfo<AssociateCarrierListRespModel>> pageInfoResult = shippingFreightClient.associateCarrierList(MapperUtils.mapper(requestDto, ShippingFreightIdReqModel.class));
        pageInfoResult.throwException();
        PageInfo data = pageInfoResult.getData();
        List<AssociateCarrierListRespDto> dtoList = MapperUtils.mapper(pageInfoResult.getData().getList(), AssociateCarrierListRespDto.class);
        data.setList(dtoList);
        return Result.success(data);
    }


    /**
     * 关联承运商列表导出  v2.42
     * @param requestDto
     * @return
     */
    @GetMapping(value = "/exportAssociateCarrierList")
    void exportAssociateCarrierList(ShippingFreightIdReqDto requestDto,HttpServletResponse response) {
        requestDto.setPageNum(CommonConstant.INTEGER_ZERO);
        requestDto.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<AssociateCarrierListRespModel>> pageInfoResult = shippingFreightClient.associateCarrierList(MapperUtils.mapper(requestDto, ShippingFreightIdReqModel.class));
        pageInfoResult.throwException();
        PageInfo data = pageInfoResult.getData();
        List<AssociateCarrierListRespDto> dtoList = MapperUtils.mapper(data.getList(), AssociateCarrierListRespDto.class);
        String fileName = "关联承运商列表";
        Map<String,String> exportMap = ExportExcelAssociateCarrier.getDateAssociateCarrierMap();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }

    /**
     * 关联承运商删除  v2.42
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/associateCarrierDelete")
    public Result<Boolean> associateCarrierDelete(@RequestBody @Valid AssociateCarrierDeleteReqDto requestDto) {
        Result<Boolean> result = shippingFreightClient.associateCarrierDelete(MapperUtils.mapper(requestDto, AssociateCarrierDeleteReqModel.class));
        result.throwException();
        return result;
    }

}
