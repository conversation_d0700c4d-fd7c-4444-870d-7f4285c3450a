package com.logistics.appapi.controller.website.entrust;

import cn.dev33.satoken.annotation.SaIgnore;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.client.thirdparty.basicdata.BasicServiceClient;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.request.CheckSensitiveWordRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.response.CheckSensitiveWordResponseModel;
import com.logistics.appapi.client.website.entrust.EntrustSourceClient;
import com.logistics.appapi.client.website.entrust.request.AddEntrustSourceRequestModel;
import com.logistics.appapi.controller.website.entrust.request.AddEntrustSourceRequestDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.ExtFrequency;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.annocation.LimitedExtFrequency;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2024/3/15 9:51
 */
@Api(value = "云途官网-货主")
@RestController
public class EntrustSourceController {

    @Resource
    private EntrustSourceClient entrustSourceClient;
    @Resource
    private BasicServiceClient basicServiceClient;

    /**
     * 成为货主
     * @param requestDto
     * @return
     */
    @ApiOperation("成为货主")
    @PostMapping("/api/entrustSource/addEntrustSource")
    @SaIgnore
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    @LimitedExtFrequency(limiteds = {@ExtFrequency(params = "sourceIp", count = 100, type = ExtFrequency.TYPE_DAY_DAWN, message = "非法访问,访问超过限定次数!")})
    public Result addEntrustSource(@RequestBody @Valid AddEntrustSourceRequestDto requestDto) {
        StringBuilder sb = new StringBuilder();
        sb = sb.append(requestDto.getCompanyName()).append("&").append(requestDto.getContactName()).append("&").append(requestDto.getRemark());
        CheckSensitiveWordRequestModel checkSensitiveWordRequestModel = new CheckSensitiveWordRequestModel();
        checkSensitiveWordRequestModel.setContent(sb.toString());
        CheckSensitiveWordResponseModel sensitiveWordModel = basicServiceClient.checkSensitiveWord(checkSensitiveWordRequestModel);
        if (sensitiveWordModel == null || CommonConstant.INTEGER_ONE.equals(sensitiveWordModel.getIfSensitive())) {
            throw new BizException(AppApiExceptionEnum.NOT_ALLOW_SUBMIT_SENSITIVE_WORD);
        }
        return entrustSourceClient.addEntrustSource(MapperUtils.mapper(requestDto, AddEntrustSourceRequestModel.class));
    }
}
