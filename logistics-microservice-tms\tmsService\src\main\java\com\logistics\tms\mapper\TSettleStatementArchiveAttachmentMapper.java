package com.logistics.tms.mapper;

import com.logistics.tms.entity.TSettleStatementArchiveAttachment;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2022/11/10
*/
@Mapper
public interface TSettleStatementArchiveAttachmentMapper extends BaseMapper<TSettleStatementArchiveAttachment> {

    int batchInsertSelective(@Param("list") List<TSettleStatementArchiveAttachment> list);

    List<String> archiveTicketPathByItemId(@Param("itemId")Long itemId);

    void delBySettleStatementItemId(@Param("itemId") Long itemId);
}