package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class WebDemandOrderResponseModel {
    private Long demandId;
    private Integer status;
    private Integer entrustStatus;
    private Integer ifCancel;
    @ApiModelProperty("是否放空：0 否，1 是")
    private Integer ifEmpty;
    private Integer ifRollback;//是否回退
    private String demandOrderCode;
    private String customerOrderCode;
    private String publishName;
    private Date publishTime;
    private Integer dispatchVehicleCount = 0;
    private Integer entrustType;
    private Integer goodsUnit;
    private String remark;
    private String userName;
    private Long loadProvinceId;
    private String loadProvinceName;
    private Long loadCityId;
    private String loadCityName;
    private Long loadAreaId;
    private String loadAreaName;
    private String loadDetailAddress;
    private String loadWarehouse;
    private String consignorName;
    private String consignorMobile;
    private Date expectedLoadTime;
    private Long unloadProvinceId;
    private String unloadProvinceName;
    private Long unloadCityId;
    private String unloadCityName;
    private Long unloadAreaId;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;
    private String receiverName;
    private String receiverMobile;
    private Date expectedUnloadTime;
    private BigDecimal goodsAmount;
    private BigDecimal arrangedAmount;
    private BigDecimal notArrangedAmount;
    private BigDecimal backAmount;
    private Long customerEntrustId;
    private Long companyEntrustId;
    private Long companyCarrierId;
    private Long carrierContactId;
    private String cancelReason;
    private String rollbackRemark;
    private Integer source;


    private String companyEntrustName;


    //更换车主比较取消使用
    private String modifyCarrierReason;
    private Long myCompanyCarrierId;

    @ApiModelProperty("回收任务类型：1 日常回收，2 加急或节假日回收")
    private Integer recycleTaskType;

    private List<DemandOrderGoodsResponseModel> goodsResponseModels;

    private Long evaluateId;
    private Long evaluateScore;

    @ApiModelProperty("中石化运单号")
    private String sinopecOrderNo;

    @ApiModelProperty("中石化下单类型：20 拉取，21 推送")
    private Integer orderType;

    @ApiModelProperty("是否有异常工单: 0否 1是")
    private Integer whetherWorkOrder;

    @ApiModelProperty("是否是额外补的需求单0：否1：是 v2.6.8")
    private Integer ifExtDemandOrder;
}

