package com.logistics.management.webapi.client.shipingfreight.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.shipingfreight.ShippingFreightClient;
import com.logistics.management.webapi.client.shipingfreight.request.*;
import com.logistics.management.webapi.client.shipingfreight.response.AssociateCarrierListRespModel;
import com.logistics.management.webapi.client.shipingfreight.response.ListShippingFreightListRespModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/6 17:40
 */
@Component
public class ShippingFreightClientHystrix implements ShippingFreightClient {

    @Override
    public Result<PageInfo<ListShippingFreightListRespModel>> getList(ListShippingFreightListReqModel reqModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> add(AddShippingFreightReqModel reqModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> associateCarrier(AssociateCarrierReqModel reqModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<AssociateCarrierListRespModel>> associateCarrierList(ShippingFreightIdReqModel reqModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> associateCarrierDelete(AssociateCarrierDeleteReqModel reqModel) {
        return Result.timeout();
    }
}
