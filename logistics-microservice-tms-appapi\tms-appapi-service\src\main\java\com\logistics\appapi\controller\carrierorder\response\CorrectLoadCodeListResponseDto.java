package com.logistics.appapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CorrectLoadCodeListResponseDto {

    @ApiModelProperty(value = "运单提货编码id")
    private String carrierOrderLoadCodeId;

    @ApiModelProperty(value = "托盘客户name")
    private String trayCustomerCompanyName;

    @ApiModelProperty(value = "托盘编码")
    private String productCode;

}
