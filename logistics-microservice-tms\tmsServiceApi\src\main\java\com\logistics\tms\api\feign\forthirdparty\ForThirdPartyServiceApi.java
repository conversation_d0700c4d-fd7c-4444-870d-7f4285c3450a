package com.logistics.tms.api.feign.forthirdparty;

import com.logistics.tms.api.feign.forthirdparty.hystrix.ForThirdPartyServiceApiHystrix;
import com.logistics.tms.api.feign.forthirdparty.model.request.WorkOrderProcessSyncRequestModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "API-ForThirdPartyApi", tags = "供第三方调用API")
@FeignClient(name = "logistics-tms-services", fallback = ForThirdPartyServiceApiHystrix.class)
public interface ForThirdPartyServiceApi {

    @ApiOperation(value = "工单处理流程同步 - 智慧运营(任务中心)调用")
    @PostMapping("/service/workOrderCenter/syncWorkOrderProcess")
    Result<Boolean> syncWorkOrderProcess(@RequestBody WorkOrderProcessSyncRequestModel requestModel);
}
