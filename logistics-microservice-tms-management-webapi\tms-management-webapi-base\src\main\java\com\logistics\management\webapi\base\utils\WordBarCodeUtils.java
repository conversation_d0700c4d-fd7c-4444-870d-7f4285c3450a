package com.logistics.management.webapi.base.utils;

import com.yelo.tools.utils.WorderToNewWordUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.TargetMode;
import org.apache.poi.xwpf.usermodel.*;

import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * word替换内容和图片工具类
 *
 * <AUTHOR>
 * @date ：Created in 2022/10/31
 */
public class WordBarCodeUtils {

    public static boolean fillWordTemplateValues(InputStream resourceAsStream, String outputUrl,
                                                 Map<String, String> textMap, Map<String, InputStream> picMap, List<String[]> tableList, List<Map<String, List<String[]>>> tableInsertItems) {
        //模板转换默认成功
        boolean changeFlag = true;
        try {
            //获取docx解析对象
            XWPFDocument document = new XWPFDocument(resourceAsStream);
            //修改页眉
            changeHeaderText(document, textMap);
            //解析替换文本段落对象
            WorderToNewWordUtils.changeText(document, textMap);
            //解析替换表格对象
            WorderToNewWordUtils.changeTable(document, textMap, tableList);
            //插入新的行
            insertRowByIndex(document, tableInsertItems);
            //替换word图片 根据图片rId
            changePic(document, picMap);
            File file = new File(outputUrl);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            //生成新的word
            OutputStream outputStream = new FileOutputStream(outputUrl);
            document.write(outputStream);
            outputStream.close();
        } catch (IOException e) {
            changeFlag = false;
        }
        return changeFlag;
    }

    /**
     * 替换页眉
     *
     * @param document docx解析对象
     * @param textMap  需要替换的信息集合
     */
    private static void changeHeaderText(XWPFDocument document, Map<String, String> textMap) {
        //获取段落集合
        List<XWPFHeader> headers = document.getHeaderList();

        for (XWPFHeader header : headers) {
            //判断此段落时候需要进行替换
            List<XWPFParagraph> paragraphs = header.getParagraphs();
            changeHeaderBodyText(paragraphs, textMap);
        }
    }

    //替换页眉和段落文本
    private static void changeHeaderBodyText(List<XWPFParagraph> paragraphs, Map<String, String> textMap) {
        for (XWPFParagraph paragraph : paragraphs) {
            //判断此段落时候需要进行替换
            String text = paragraph.getText();
            if (WorderToNewWordUtils.checkText(text)) {
                List<XWPFRun> runs = paragraph.getRuns();
                for (XWPFRun run : runs) {
                    //替换模板原来位置
                    run.setText(WorderToNewWordUtils.changeValue(run.toString(), textMap), 0);
                }
            }
        }
    }

    /**
     * 替换word图片
     *
     * @param document word对象
     * @param picMap   rid-新图片输入流  map
     */
    public static void changePic(XWPFDocument document, Map<String, InputStream> picMap) {
        if (picMap != null && !picMap.isEmpty()) {
            Set<Map.Entry<String, InputStream>> entries = picMap.entrySet();
            for (Map.Entry<String, InputStream> entry : entries) {
                replaceImg(document, entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * 用新图片替换word里面已经存在的图片(.docx哥特式)
     *
     * @param document word对象
     * @param newPicIn 图片输入流
     * @param rid      原图片rid, 把.docx文件改名为zip 找到document.xml,找到要替换的图片的rid
     */
    public static void replaceImg(XWPFDocument document, String rid, InputStream newPicIn) {
        try {
            document.getPackagePart().removeRelationship(rid);//删除原有印章
            String ind = document.addPictureData(newPicIn, XWPFDocument.PICTURE_TYPE_PNG);//在word插入一张没有关系的图片数据
            //将新插入的图片关系替换到模板图片上，替换的只是图片数据，大小位置还是原来模板的
            document.getPackagePart().addRelationship(document.getPackagePart().getRelationship(ind).getTargetURI(), TargetMode.INTERNAL, XWPFRelation.IMAGES.getRelation(), rid);
        } catch (InvalidFormatException e) {
            e.printStackTrace();
        }
    }

    /**
     * insertRow 在word表格中指定位置插入一行，并将某一行的样式复制到新增行
     *
     * @param copyRowIndex 需要复制的行位置
     * @param newRowIndex  需要新增一行的位置
     */
    public static XWPFTableRow insertRow(XWPFTable table, int copyRowIndex, int newRowIndex) {
        // 在表格中指定的位置新增一行
        XWPFTableRow targetRow = table.insertNewTableRow(newRowIndex);
        // 获取需要复制行对象
        XWPFTableRow copyRow = table.getRow(copyRowIndex);
        //复制行对象
        targetRow.getCtRow().setTrPr(copyRow.getCtRow().getTrPr());
        //或许需要复制的行的列
        List<XWPFTableCell> copyCells = copyRow.getTableCells();
        //复制列对象
        XWPFTableCell targetCell;
        for (XWPFTableCell copyCell : copyCells) {
            targetCell = targetRow.addNewTableCell();
            targetCell.getCTTc().setTcPr(copyCell.getCTTc().getTcPr());
            if (copyCell.getParagraphs() != null && !copyCell.getParagraphs().isEmpty()) {
                targetCell.getParagraphs().get(0).getCTP().setPPr(copyCell.getParagraphs().get(0).getCTP().getPPr());
                if (copyCell.getParagraphs().get(0).getRuns() != null && !copyCell.getParagraphs().get(0).getRuns().isEmpty()) {
                    XWPFRun cellR = targetCell.getParagraphs().get(0).createRun();
                    cellR.setBold(copyCell.getParagraphs().get(0).getRuns().get(0).isBold());
                }
            }
        }
        return targetRow;
    }

    /**
     * 插入新的行
     *
     * @param tableInsertItems 行内容
     */
    public static void insertRowByIndex(XWPFDocument document, List<Map<String, List<String[]>>> tableInsertItems) {
        if (tableInsertItems != null && !tableInsertItems.isEmpty()) {
            for (Map<String, List<String[]>> tableInsertItem : tableInsertItems) {
                //遍历关键字
                Set<String> keyWords = tableInsertItem.keySet();
                for (String keyWord : keyWords) {
                    List<String[]> cellIndexValueList = tableInsertItem.get(keyWord);
                    if (cellIndexValueList == null || cellIndexValueList.isEmpty()) {
                        return;
                    }

                    //查找目标行
                    int targetRowIndex = 0;
                    XWPFTable targetTable = null;
                    List<XWPFTable> tables = document.getTables();
                    for (XWPFTable table : tables) {
                        List<XWPFTableRow> rows = table.getRows();
                        for (int i = 0; i < rows.size(); i++) {
                            if (keyWord.equals(rows.get(i).getCell(0).getText())) {
                                targetRowIndex = i;
                                targetTable = table;
                                break;
                            }
                        }

                        if (targetRowIndex > 0) {
                            break;
                        }
                    }

                    if (targetRowIndex > 0) {
                        int copyRowIndex = targetRowIndex + 1;
                        int newRowIndex = targetRowIndex + 1;
                        //第一行数据填充到被复制的行
                        XWPFTableRow row = targetTable.getRow(copyRowIndex);
                        String[] cellIdxValue = cellIndexValueList.get(0);
                        insertRowValue(cellIdxValue, row);

                        //新增其他行并填充数据
                        for (int i = 1; i < cellIndexValueList.size(); i++) {
                            XWPFTableRow newRow = insertRow(targetTable, copyRowIndex, newRowIndex + i);
                            String[] rowValues = cellIndexValueList.get(i);
                            insertRowValue(rowValues, newRow);
                        }
                    }
                }
            }
        }
    }

    /**
     * 插入table每行的值
     *
     * @param cellIdxValue cell数据
     * @param row          row对象
     */
    private static void insertRowValue(String[] cellIdxValue, XWPFTableRow row) {
        for (int i = 0; i < cellIdxValue.length; i++) {
            String value = cellIdxValue[i];
            if (StringUtils.isNotBlank(value)) {
                row.getCell(i).setText(value);
            }
        }
    }
}
