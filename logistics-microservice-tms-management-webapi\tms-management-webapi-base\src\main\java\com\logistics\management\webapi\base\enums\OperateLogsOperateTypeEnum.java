/**
 * Created by yun<PERSON>zhou on 2017/12/12.
 */
package com.logistics.management.webapi.base.enums;

public enum OperateLogsOperateTypeEnum {

    AUDIT_THROUGH(1, "审核通过"),
    AUDIT_REJECT(2, "审核驳回"),
    UPDATE(3,"修改"),
    INVALID(4,"作废"),
    ;

    private Integer key;
    private String value;

    OperateLogsOperateTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static OperateLogsOperateTypeEnum getEnum(Integer key) {
        for (OperateLogsOperateTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
