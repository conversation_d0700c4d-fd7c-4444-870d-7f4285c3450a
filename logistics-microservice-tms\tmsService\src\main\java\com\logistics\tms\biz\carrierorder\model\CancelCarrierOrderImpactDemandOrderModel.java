package com.logistics.tms.biz.carrierorder.model;

import com.logistics.tms.controller.demandorder.response.CancelCarrierOrderOrderRelModel;
import com.logistics.tms.rabbitmq.publisher.model.CarrierOrderCancelToYeloLifeModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * liang current user system login name
 * 2018/10/10 current system date
 */
@Data
public class CancelCarrierOrderImpactDemandOrderModel {
    private Map<Long, BigDecimal> carrierOrderCountMap;
    private Map<Long, BigDecimal> carrierGoodCountMap;
    private List<CancelCarrierOrderOrderRelModel> carrierOrderOrderRelModels;
    private List<CarrierOrderSynchronizeModel> synchronizeModels;

    @ApiModelProperty("取消运单同步新生List")
    private List<CarrierOrderCancelToYeloLifeModel> carrierOrderForLifeList;
}
