package com.logistics.tms.controller.carriervehiclerel;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.carriervehiclerel.CarrierVehicleBiz;
import com.logistics.tms.controller.carriervehiclerel.request.SaveOrModifyCarrierVehicleRequestModel;
import com.logistics.tms.controller.carriervehiclerel.request.SearchCarrierVehicleListRequestModel;
import com.logistics.tms.controller.carriervehiclerel.request.VehicleAssetDetailRequestModel;
import com.logistics.tms.controller.carriervehiclerel.response.GetCarrierVehicleDetailResponseModel;
import com.logistics.tms.controller.carriervehiclerel.response.SearchCarrierVehicleListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: sj
 * @Date: 2019/10/14 14:19
 */
@Api(value = "API-CarrierVehicleServiceApi")
@RestController
@RequestMapping("/service/carrierVehicle")
public class CarrierVehicleController {

    @Autowired
    private CarrierVehicleBiz carrierVehicleBiz;

    /**
     * 列表(前台)
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "列表(前台)")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchCarrierVehicleListResponseModel>> searchList(@RequestBody SearchCarrierVehicleListRequestModel requestModel) {
        return Result.success(carrierVehicleBiz.searchList(requestModel));
    }

    /**
     * 新增/车辆信息(前台)
     *
     * @param requestModel 车辆信息
     * @return 操作结果
     */
    @ApiOperation(value = "新增车辆信息(前台)")
    @PostMapping(value = "/saveOrModify")
    public Result<Boolean> saveOrModify(@RequestBody SaveOrModifyCarrierVehicleRequestModel requestModel) {
        carrierVehicleBiz.saveOrModify(requestModel);
        return Result.success(true);
    }

    /**
     * 车主车辆详情(前台)
     *
     * @param requestModel 车辆id
     * @return 车主车辆详情
     */
    @ApiOperation(value = "车主车辆详情(前台)")
    @PostMapping(value = "/getDetailList")
    public Result<GetCarrierVehicleDetailResponseModel> getDetailList(@RequestBody VehicleAssetDetailRequestModel requestModel) {
        return Result.success(carrierVehicleBiz.getCarrierVehicleDetail(requestModel));
    }
}
