package com.logistics.management.webapi.controller.settlestatement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/25 13:35
 */
@Data
public class TraditionSettleStatementApplyInvoicingRequestDto {
    @ApiModelProperty(value = "对账单id", required = true)
    @NotEmpty(message = "对账单id不能为空")
    @Size(max = 50, message = "请选择对账单，最多50条")
    private List<String> settleStatementIdList;

    @ApiModelProperty(value = "业务名称",required = true)
    @NotBlank(message = "业务名称不能为空")
    @Size(min = 1, max = 50, message = "业务名称不能为空")
    private String businessName;

    @ApiModelProperty(value = "开票月份",required = true)
    @NotBlank(message = "开票月份不能为空")
    private String invoicingMonth;

    @ApiModelProperty("备注")
    @Size(max = 300, message = "备注不能超过300字")
    private String remark;
}
