package com.logistics.tms.biz.carrierorder.model;

import com.logistics.tms.controller.demandorder.response.CancelCarrierOrderOrderRelModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2023/6/9 11:04
 */
@Data
public class ExternalCancelCarrierOrderImpactDemandOrderModel {

    //需求单id-》退回到需求单已安排的数量
    private Map<Long, BigDecimal> arrangedAmountMap;

    //需求单id-》退回到需求单未安排的数量
    private Map<Long, BigDecimal> notArrangedAmountMap;

    //需求单货物id-》退回到需求单货物已安排的数量
    private Map<Long, BigDecimal> goodsArrangedAmountMap;

    //需求单货物id-》退回到需求单货物未安排的数量
    private Map<Long, BigDecimal> goodsNotArrangedAmountMap;

    //取消运单-同步云盘运单信息
    private List<CancelCarrierOrderOrderRelModel> carrierOrderOrderRelModels;

    //取消运单-同步云盘运单S单信息
    private List<CarrierOrderSynchronizeModel> synchronizeModels;
}
