package com.logistics.tms.biz.driverpayee;


import com.github.pagehelper.PageInfo;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import com.logistics.tms.api.feign.common.SrcUrlModel;
import com.logistics.tms.api.feign.common.model.CertificatePictureModel;
import com.logistics.tms.api.feign.driverpayee.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.RegExpValidatorUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class DriverPayeeBiz {

    @Autowired
    private TDriverPayeeMapper tqDriverPayeeMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TCertificationPicturesMapper tqCertificationPicturesMapper;
    @Autowired
    private TVehiclePayeeRelMapper tqVehiclePayeeRelMapper;
    @Autowired
    private TOperateLogsMapper tOperateLogsMapper;
    @Autowired
    private TBankMapper tqBankMapper;

    /**
     * 司机收款人账户列表
     * @param requestModel
     * @return
     */
    public PageInfo<DriverPayeeListResponseModel> driverPayeeList(DriverPayeeListRequestModel requestModel) {
        requestModel.enablePaging();
        List<Long> driverPayeeIds = tqDriverPayeeMapper.selectDriverPayeeListIds(requestModel);
        PageInfo pageInfo = new PageInfo(driverPayeeIds);
        if(ListUtils.isNotEmpty(driverPayeeIds)){
            pageInfo.setList(tqDriverPayeeMapper.selectDriverPayeeListByIds(StringUtils.listToString(driverPayeeIds,',')));
        }
        return pageInfo;
    }

    /**
     * 导出司机收款人账户列表
     * @param requestModel
     * @return
     */
    public List<ExportDriverPayeeListResponseModel> exportDriverPayeeList(DriverPayeeListRequestModel requestModel) {
        return tqDriverPayeeMapper.exportDriverPayeeList(requestModel);
    }

    /**
     * 司机收款人账户新增/修改
     * @param requestModel
     */
    @Transactional
    public void addOrModifyDriverPayee(AddOrModifyDriverPayeeRequestModel requestModel) {
        if(requestModel.getDriverPayeeId()==null){
            addDriverPayee(requestModel);
        }else{
            modifyDriverPayee(requestModel);
        }
    }
    //新增
    private void addDriverPayee(AddOrModifyDriverPayeeRequestModel requestModel){
        TDriverPayee tqDriverPayee = tqDriverPayeeMapper.getByIdentity(requestModel.getIdentityNo());
        if(tqDriverPayee!=null){
            throw new BizException(CarrierDataExceptionEnum.DATA_EXIST);
        }
        TDriverPayee newDriverPayee = new TDriverPayee();
        newDriverPayee.setName(requestModel.getName());
        newDriverPayee.setIdentityNo(requestModel.getIdentityNo());
        newDriverPayee.setMobile(requestModel.getMobile());
        newDriverPayee.setBankCardNo(requestModel.getBankCardNo());
        newDriverPayee.setBankId(requestModel.getBankId());
        newDriverPayee.setAuditStatus(AuditStatusEnum.WAIT_AUDIT.getKey());
        newDriverPayee.setRemark(requestModel.getRemark());
        commonBiz.setBaseEntityAdd(newDriverPayee, BaseContextHandler.getUserName());
        tqDriverPayeeMapper.insertSelective(newDriverPayee);
        tOperateLogsMapper.insertSelective(commonBiz.insertLogs(OperateLogsOperateTypeEnum.ADD_DRIVER_PAYEE,newDriverPayee.getId(),""));
        addDriverPayeeIdentityImages(newDriverPayee.getId(),CertificationPicturesFileTypeEnum.DRIVER_PAYEE_ID_FRONT_IMAGE_FILE,requestModel.getIdentityFront());
        addDriverPayeeIdentityImages(newDriverPayee.getId(),CertificationPicturesFileTypeEnum.DRIVER_PAYEE_ID_BACK_IMAGE_FILE,requestModel.getIdentityBack());
        if(ListUtils.isNotEmpty(requestModel.getImageList())){
            List<TCertificationPictures> addPictures = new ArrayList<>();
            for(String relPath : requestModel.getImageList()){
                TCertificationPictures newPicture = new TCertificationPictures();
                newPicture.setObjectType(CertificationPicturesFileTypeEnum.DRIVER_PAYEE_IMAGE_FILE.getObjectType().getObjectType());
                newPicture.setObjectId(newDriverPayee.getId());
                newPicture.setFileType(CertificationPicturesFileTypeEnum.DRIVER_PAYEE_IMAGE_FILE.getFileType());
                newPicture.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_PAYEE.getKey(),"",relPath,null));
                newPicture.setUploadUserName(BaseContextHandler.getUserName());
                newPicture.setUploadTime(newDriverPayee.getCreatedTime());
                newPicture.setFileTypeName(CertificationPicturesFileTypeEnum.DRIVER_PAYEE_IMAGE_FILE.getFileName());
                newPicture.setFileName(CertificationPicturesFileTypeEnum.DRIVER_PAYEE_IMAGE_FILE.getFileName());
                newPicture.setSuffix(relPath.substring(relPath.indexOf('.')));
                commonBiz.setBaseEntityAdd(newPicture,BaseContextHandler.getUserName());
                addPictures.add(newPicture);
            }
            tqCertificationPicturesMapper.batchInsert(addPictures);
        }
    }
    //新增图片
    private void addDriverPayeeIdentityImages(Long driverPayeeId,CertificationPicturesFileTypeEnum typeEnum,String tempPath){
        if(StringUtils.isBlank(tempPath)){
            return;
        }
        TCertificationPictures newPicture = new TCertificationPictures();
        newPicture.setObjectType(typeEnum.getObjectType().getObjectType());
        newPicture.setObjectId(driverPayeeId);
        newPicture.setFileType(typeEnum.getFileType());
        newPicture.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_PAYEE.getKey(),"",tempPath,null));
        newPicture.setUploadUserName(BaseContextHandler.getUserName());
        newPicture.setUploadTime(new Date());
        newPicture.setFileTypeName(typeEnum.getFileName());
        newPicture.setFileName(typeEnum.getFileName());
        newPicture.setSuffix(tempPath.substring(tempPath.indexOf('.')));
        commonBiz.setBaseEntityAdd(newPicture,BaseContextHandler.getUserName());
        tqCertificationPicturesMapper.insertSelective(newPicture);
    }
    //修改图片
    private void modifyDriverPayeeIdentityImages(Long driverPayeeId,CertificationPicturesFileTypeEnum typeEnum,String tempPath){

        List<TCertificationPictures> pictures = tqCertificationPicturesMapper.getByObjectIdType(driverPayeeId,typeEnum.getObjectType().getObjectType(),typeEnum.getFileType());
        if(pictures.size()>1){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_PAYEE_IDENTITY_IMAGE);
        }
        if(StringUtils.isNotBlank(tempPath)) {
            if (ListUtils.isEmpty(pictures)) {
                TCertificationPictures newPicture = new TCertificationPictures();
                newPicture.setObjectType(typeEnum.getObjectType().getObjectType());
                newPicture.setObjectId(driverPayeeId);
                newPicture.setFileType(typeEnum.getFileType());
                newPicture.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_PAYEE.getKey(), "", tempPath, null));
                newPicture.setUploadUserName(BaseContextHandler.getUserName());
                newPicture.setUploadTime(new Date());
                newPicture.setFileTypeName(typeEnum.getFileName());
                newPicture.setFileName(typeEnum.getFileName());
                newPicture.setSuffix(tempPath.substring(tempPath.indexOf('.')));
                commonBiz.setBaseEntityAdd(newPicture, BaseContextHandler.getUserName());
                tqCertificationPicturesMapper.insertSelective(newPicture);
            } else {
                TCertificationPictures oldPicture = pictures.get(0);
                if (!oldPicture.getFilePath().equals(tempPath)) {
                    TCertificationPictures newPicture = new TCertificationPictures();
                    newPicture.setId(oldPicture.getId());
                    newPicture.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_PAYEE.getKey(), "", tempPath, null));
                    newPicture.setUploadUserName(BaseContextHandler.getUserName());
                    newPicture.setUploadTime(new Date());
                    newPicture.setSuffix(tempPath.substring(tempPath.indexOf('.')));
                    commonBiz.setBaseEntityModify(newPicture, BaseContextHandler.getUserName());
                    tqCertificationPicturesMapper.updateByPrimaryKeySelective(newPicture);
                }
            }
        }else if(ListUtils.isNotEmpty(pictures)){
            TCertificationPictures oldPicture = pictures.get(0);
            TCertificationPictures upPicture = new TCertificationPictures();
            upPicture.setId(oldPicture.getId());
            upPicture.setValid(CommonConstant.INTEGER_ZERO);
            commonBiz.setBaseEntityModify(upPicture, BaseContextHandler.getUserName());
            tqCertificationPicturesMapper.updateByPrimaryKeySelective(upPicture);
        }
    }

    //修改
    private void modifyDriverPayee(AddOrModifyDriverPayeeRequestModel requestModel){
        TDriverPayee tqDriverPayee = tqDriverPayeeMapper.selectByPrimaryKey(requestModel.getDriverPayeeId());
        if(tqDriverPayee==null|| CommonConstant.INTEGER_ZERO.equals(tqDriverPayee.getValid())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_PAYEE_NOT_EXIST);
        }
        if(!(AuditStatusEnum.WAIT_AUDIT.getKey().equals(tqDriverPayee.getAuditStatus())
                || AuditStatusEnum.AUDIT_REJECT.getKey().equals(tqDriverPayee.getAuditStatus())
                || AuditStatusEnum.AUDIT_THROUGH.getKey().equals(tqDriverPayee.getAuditStatus()))){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_PAYEE_AUDIT_STATUS_CANT_MODIFY);
        }
        TDriverPayee driverPayeeWithSameIdentity = tqDriverPayeeMapper.getByIdentity(requestModel.getIdentityNo());
        if(driverPayeeWithSameIdentity != null && !driverPayeeWithSameIdentity.getId().equals(tqDriverPayee.getId())){
            throw new BizException(CarrierDataExceptionEnum.DRIVER_PAYEE_IDENTITY_EXIST);
        }
        TDriverPayee upDriverPayee = new TDriverPayee();
        upDriverPayee.setId(tqDriverPayee.getId());
        upDriverPayee.setRemark(requestModel.getRemark());
        upDriverPayee.setMobile(requestModel.getMobile());
        upDriverPayee.setBankId(requestModel.getBankId());
        upDriverPayee.setBankCardNo(requestModel.getBankCardNo());
        upDriverPayee.setIdentityNo(requestModel.getIdentityNo());
        upDriverPayee.setName(requestModel.getName());
        if(AuditStatusEnum.AUDIT_REJECT.getKey().equals(tqDriverPayee.getAuditStatus())){
            upDriverPayee.setAuditStatus(AuditStatusEnum.WAIT_AUDIT.getKey());
        }
        commonBiz.setBaseEntityModify(upDriverPayee,BaseContextHandler.getUserName());
        tqDriverPayeeMapper.updateByPrimaryKeySelective(upDriverPayee);
        tOperateLogsMapper.insertSelective(commonBiz.insertLogs(OperateLogsOperateTypeEnum.UPDATE_DRIVER_PAYEE,upDriverPayee.getId(),""));

        if(ListUtils.isEmpty(requestModel.getImageList())){
            requestModel.setImageList(new ArrayList<>());
        }
        modifyDriverPayeeIdentityImages(upDriverPayee.getId(),CertificationPicturesFileTypeEnum.DRIVER_PAYEE_ID_FRONT_IMAGE_FILE,requestModel.getIdentityFront());
        modifyDriverPayeeIdentityImages(upDriverPayee.getId(),CertificationPicturesFileTypeEnum.DRIVER_PAYEE_ID_BACK_IMAGE_FILE,requestModel.getIdentityBack());

        List<TCertificationPictures> images = tqCertificationPicturesMapper.getByObjectIdType(upDriverPayee.getId(),CertificationPicturesFileTypeEnum.DRIVER_PAYEE_IMAGE_FILE.getObjectType().getObjectType(),CertificationPicturesFileTypeEnum.DRIVER_PAYEE_IMAGE_FILE.getFileType());
        Map<String,Long>  pathIdMap = new HashMap<>();
        Map<String,String> reqPathMap = new HashMap<>();
        requestModel.getImageList().stream().forEach(t->reqPathMap.putIfAbsent(t,t));
        images.stream().forEach(t->pathIdMap.putIfAbsent(t.getFilePath(),t.getId()));
        List<TCertificationPictures> addImages = new ArrayList<>();
        List<TCertificationPictures> delImages = new ArrayList<>();


        for(String tempPath : requestModel.getImageList()){
            if(pathIdMap.get(tempPath) == null){
                TCertificationPictures newPicture = new TCertificationPictures();
                newPicture.setObjectType(CertificationPicturesFileTypeEnum.DRIVER_PAYEE_IMAGE_FILE.getObjectType().getObjectType());
                newPicture.setObjectId(upDriverPayee.getId());
                newPicture.setFileType(CertificationPicturesFileTypeEnum.DRIVER_PAYEE_IMAGE_FILE.getFileType());
                newPicture.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_PAYEE.getKey(),"",tempPath,null));
                newPicture.setUploadUserName(BaseContextHandler.getUserName());
                newPicture.setUploadTime(new Date());
                newPicture.setFileTypeName(CertificationPicturesFileTypeEnum.DRIVER_PAYEE_IMAGE_FILE.getFileName());
                newPicture.setFileName(CertificationPicturesFileTypeEnum.DRIVER_PAYEE_IMAGE_FILE.getFileName());
                newPicture.setSuffix(tempPath.substring(tempPath.indexOf('.')));
                commonBiz.setBaseEntityAdd(newPicture,BaseContextHandler.getUserName());
                addImages.add(newPicture);
            }
        }
        for(TCertificationPictures tmpPic :images ){
            if(reqPathMap.get(tmpPic.getFilePath()) == null){
                TCertificationPictures delImage = new TCertificationPictures();
                delImage.setValid(CommonConstant.INTEGER_ZERO);
                delImage.setId(tmpPic.getId());
                commonBiz.setBaseEntityModify(delImage,BaseContextHandler.getUserName());
                delImages.add(delImage);
            }
        }
        if(ListUtils.isNotEmpty(addImages)){
            tqCertificationPicturesMapper.batchInsert(addImages);
        }
        if(ListUtils.isNotEmpty(delImages)){
            tqCertificationPicturesMapper.batchUpdate(delImages);
        }
    }

    /**
     * 司机收款人账户详情
     * @param requestModel
     * @return
     */
    public DriverPayeeDetailResponseModel driverPayeeDetail(DriverPayeeDetailRequestModel requestModel) {
        DriverPayeeDetailResponseModel driverPayeeDetailResponseModel = tqDriverPayeeMapper.getDriverPayeeDetail(requestModel.getDriverPayeeId());
        if(ListUtils.isNotEmpty(driverPayeeDetailResponseModel.getImageList())){
            List<CertificatePictureModel> imageList = driverPayeeDetailResponseModel.getImageList();
            for(Iterator<CertificatePictureModel> ite = imageList.iterator(); ite.hasNext();){
                CertificatePictureModel next = ite.next();
                if(CertificationPicturesFileTypeEnum.DRIVER_PAYEE_ID_FRONT_IMAGE_FILE.getFileType().equals(next.getFileType())){
                    driverPayeeDetailResponseModel.setIdentityFront(next.getFilePath());
                    ite.remove();
                }else if(CertificationPicturesFileTypeEnum.DRIVER_PAYEE_ID_BACK_IMAGE_FILE.getFileType().equals(next.getFileType())){
                    driverPayeeDetailResponseModel.setIdentityBack(next.getFilePath());
                    ite.remove();
                }
            }
        }
        return driverPayeeDetailResponseModel;
    }

    /**
     * 司机收款人审核驳回作废
     * @param requestModel
     */
    @Transactional
    public void auditOrReject(AuditRejectDriverPayeeRequestModel requestModel) {
        if(StringUtils.isBlank(requestModel.getDriverPayeeIds())){
            return;
        }
        if(CommonConstant.INTEGER_ONE.equals(requestModel.getOperateType()) || CommonConstant.INTEGER_TWO.equals(requestModel.getOperateType())){
            auditOrRejectDriverPayee(requestModel);
        }else if(CommonConstant.INT_THREE.equals(requestModel.getOperateType())){
            invalidDriverPayee(requestModel);
        }
    }
    //审核/驳回
    private void auditOrRejectDriverPayee(AuditRejectDriverPayeeRequestModel requestModel){
        List<TDriverPayee> tqDriverPayeeList = tqDriverPayeeMapper.selectDriverPayeeByIds(requestModel.getDriverPayeeIds());
        List<TOperateLogs> tOperateLogs = new ArrayList<>();
        if(ListUtils.isNotEmpty(tqDriverPayeeList)){
            if(CommonConstant.INTEGER_ONE.equals(requestModel.getOperateType())
                    && tqDriverPayeeList.stream().anyMatch(tmp->!AuditStatusEnum.WAIT_AUDIT.getKey().equals(tmp.getAuditStatus())&&!AuditStatusEnum.AUDIT_REJECT.getKey().equals(tmp.getAuditStatus()))){
                throw new BizException(CarrierDataExceptionEnum.DRIVER_PAYEE_CANT_AUDIT);
            }else if(CommonConstant.INTEGER_TWO.equals(requestModel.getOperateType())
                    &&tqDriverPayeeList.stream().anyMatch(tmp->!AuditStatusEnum.WAIT_AUDIT.getKey().equals(tmp.getAuditStatus()))){
                throw new BizException(CarrierDataExceptionEnum.DRIVER_PAYEE_CANT_REJECT);
            }

            List<TDriverPayee> upList = new ArrayList<>();
            TDriverPayee upDriverPayee;
            for(TDriverPayee tmp: tqDriverPayeeList){
                upDriverPayee = new TDriverPayee();
                upDriverPayee.setId(tmp.getId());
                upDriverPayee.setAuditorId(BaseContextHandler.getUserId());
                upDriverPayee.setAuditorName(BaseContextHandler.getUserName());
                upDriverPayee.setAuditTime(new Date());
                commonBiz.setBaseEntityModify(upDriverPayee,BaseContextHandler.getUserName());

                if(CommonConstant.INTEGER_ONE.equals(requestModel.getOperateType())){
                    upDriverPayee.setAuditStatus(AuditStatusEnum.AUDIT_THROUGH.getKey());
                    tOperateLogs.add(commonBiz.insertLogs(OperateLogsOperateTypeEnum.AUDIT_THROUGH_DRIVER_PAYEE,tmp.getId(),null));

                }else if(CommonConstant.INTEGER_TWO.equals(requestModel.getOperateType())){
                    upDriverPayee.setAuditStatus(AuditStatusEnum.AUDIT_REJECT.getKey());
                    tOperateLogs.add(commonBiz.insertLogs(OperateLogsOperateTypeEnum.REJECT_DRIVER_PAYEE,tmp.getId(),null));

                }

                upList.add(upDriverPayee);
            }
            if(ListUtils.isNotEmpty(upList)){
                tqDriverPayeeMapper.batchUpdate(upList);
            }
            if(ListUtils.isNotEmpty(tOperateLogs)){
                tOperateLogsMapper.batchInsert(tOperateLogs);
            }
        }

    }
    //作废
    private void invalidDriverPayee(AuditRejectDriverPayeeRequestModel requestModel){
        List<TDriverPayee> tqDriverPayeeList = tqDriverPayeeMapper.selectDriverPayeeByIds(requestModel.getDriverPayeeIds());
        List<TOperateLogs> tOperateLogs = new ArrayList<>();

        if(ListUtils.isNotEmpty(tqDriverPayeeList)) {
            if (tqDriverPayeeList.stream().anyMatch(tmp -> !AuditStatusEnum.AUDIT_THROUGH.getKey().equals(tmp.getAuditStatus()))) {
                throw new BizException(CarrierDataExceptionEnum.DRIVER_PAYEE_NOT_AUDIT);
            }
            List<TVehiclePayeeRel> byDriverPayeeId = tqVehiclePayeeRelMapper.getByDriverPayeeId(StringUtils.listToString(tqDriverPayeeList.stream().map(TDriverPayee::getId).collect(Collectors.toList()),','));
            if(ListUtils.isNotEmpty(byDriverPayeeId)){
                throw new BizException(CarrierDataExceptionEnum.DRIVER_PAYEE_RELATED.getKey(),CarrierDataExceptionEnum.DRIVER_PAYEE_RELATED.getMsg().replace("X",byDriverPayeeId.size()+""));
            }
            List<TDriverPayee> upList = new ArrayList<>();
            for(TDriverPayee tmp: tqDriverPayeeList){
                TDriverPayee upDriverPayee = new TDriverPayee();
                upDriverPayee.setId(tmp.getId());
                commonBiz.setBaseEntityModify(upDriverPayee,BaseContextHandler.getUserName());
                if(CommonConstant.INTEGER_THREE.equals(requestModel.getOperateType())){
                    upDriverPayee.setAuditStatus(AuditStatusEnum.INVALID.getKey());
                    tOperateLogs.add(commonBiz.insertLogs(OperateLogsOperateTypeEnum.INVALID_DRIVER_PAYEE,tmp.getId(),null));
                }
                upDriverPayee.setAuditorId(BaseContextHandler.getUserId());
                upDriverPayee.setAuditorName(BaseContextHandler.getUserName());
                upDriverPayee.setAuditTime(new Date());
                upList.add(upDriverPayee);
            }
            if(ListUtils.isNotEmpty(upList)){
                tqDriverPayeeMapper.batchUpdate(upList);
            }
            if(ListUtils.isNotEmpty(tOperateLogs)){
                tOperateLogsMapper.batchInsert(tOperateLogs);
            }
        }
    }

    /**
     * 导入
     * @param importDriverPayeeRequestModel
     * @return
     */
    @Transactional
    public ImportDriverPayeeResponseModel importDriverPayee(ImportDriverPayeeRequestModel importDriverPayeeRequestModel) {
        ImportDriverPayeeResponseModel responseModel = new ImportDriverPayeeResponseModel();
        responseModel.setNumberFailures(importDriverPayeeRequestModel.getNumberFailure());
        List<ImportDriverPayeeListModel> importList = importDriverPayeeRequestModel.getImportList();
        if(ListUtils.isNotEmpty(importList)){
            for(ImportDriverPayeeListModel tmpImportModel : importList){
                TBank tqBank = tqBankMapper.findBankByName(tmpImportModel.getBankName());
                if(tqBank==null){
                    responseModel.failPlus();
                    continue;
                }
                TDriverPayee tqDriverPayee = tqDriverPayeeMapper.getByIdentity(tmpImportModel.getIdentityNo());
                TDriverPayee newDriverPayee = new TDriverPayee();
                newDriverPayee.setName(tmpImportModel.getName());
                newDriverPayee.setIdentityNo(tmpImportModel.getIdentityNo());
                newDriverPayee.setMobile(tmpImportModel.getMobile());
                newDriverPayee.setBankCardNo(tmpImportModel.getBankCardNo());
                newDriverPayee.setBankId(tqBank.getId());
                newDriverPayee.setRemark(tmpImportModel.getRemark());
                if(tqDriverPayee==null){
                    newDriverPayee.setAuditStatus(AuditStatusEnum.WAIT_AUDIT.getKey());
                    commonBiz.setBaseEntityAdd(newDriverPayee, BaseContextHandler.getUserName());
                    tqDriverPayeeMapper.insertSelective(newDriverPayee);
                    tOperateLogsMapper.insertSelective(commonBiz.insertLogs(OperateLogsOperateTypeEnum.IMPORT_ADD_DRIVER_PAYEE,newDriverPayee.getId(),""));
                }else{
                    newDriverPayee.setId(tqDriverPayee.getId());
                    commonBiz.setBaseEntityModify(newDriverPayee, BaseContextHandler.getUserName());
                    tqDriverPayeeMapper.updateByPrimaryKeySelective(newDriverPayee);
                    tOperateLogsMapper.insertSelective(commonBiz.insertLogs(OperateLogsOperateTypeEnum.IMPORT_MODIFY_DRIVER_PAYEE,newDriverPayee.getId(),""));
                }
                responseModel.successPlus();
            }
        }
        return responseModel;
    }

    /**
     * 导入证件信息
     * @param srcUrlModel
     */
    @Transactional
    public void importDriverPayeeCertificate(SrcUrlModel srcUrlModel) {

        if(StringUtils.isBlank(srcUrlModel.getFileName())||StringUtils.isBlank(srcUrlModel.getRelativePath())){
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        CertificationPicturesFileTypeEnum  typeEnum = null;
        if( RegExpValidatorUtil.match("^([\\u4e00-\\u9fa5A-Za-z])+1$",srcUrlModel.getFileName())){
            typeEnum = CertificationPicturesFileTypeEnum.DRIVER_PAYEE_ID_FRONT_IMAGE_FILE;
        }else if(RegExpValidatorUtil.match("^([\\u4e00-\\u9fa5A-Za-z])+2$",srcUrlModel.getFileName())){
            typeEnum = CertificationPicturesFileTypeEnum.DRIVER_PAYEE_ID_BACK_IMAGE_FILE;
        }else if(RegExpValidatorUtil.match("^([\\u4e00-\\u9fa5A-Za-z])+[3456]{1}$",srcUrlModel.getFileName())){
            typeEnum = CertificationPicturesFileTypeEnum.DRIVER_PAYEE_IMAGE_FILE;
        }

        if(typeEnum==null){
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        String userName = srcUrlModel.getFileName().substring(0,srcUrlModel.getFileName().length()-1);
        if(StringUtils.isBlank(userName)){
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        List<TDriverPayee> tqDriverPayeeList = tqDriverPayeeMapper.findByName(userName);
        if(ListUtils.isEmpty(tqDriverPayeeList)||tqDriverPayeeList.size()>1){
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        TDriverPayee tqDriverPayee = tqDriverPayeeList.get(0);
        if(tqDriverPayee==null){
            throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
        }
        Long objectId = tqDriverPayee.getId();
        List<TCertificationPictures> staffDriverTicketsList = tqCertificationPicturesMapper.getByObjectIdType(objectId,typeEnum.getObjectType().getObjectType(),typeEnum.getFileType());
        if(typeEnum == CertificationPicturesFileTypeEnum.DRIVER_PAYEE_ID_FRONT_IMAGE_FILE||typeEnum == CertificationPicturesFileTypeEnum.DRIVER_PAYEE_ID_BACK_IMAGE_FILE){
            if(staffDriverTicketsList.size()>CommonConstant.INTEGER_ONE){
                throw new BizException(CarrierDataExceptionEnum.IMPORT_ERROR);
            }
            if(ListUtils.isNotEmpty(staffDriverTicketsList)){
                TCertificationPictures tqCertificationPictures = staffDriverTicketsList.get(CommonConstant.INTEGER_ZERO);
                modifyDriverPayCertificate(typeEnum,objectId,tqCertificationPictures.getId(),srcUrlModel.getRelativePath());
                tOperateLogsMapper.insertSelective(commonBiz.insertLogs(OperateLogsOperateTypeEnum.IMPORT_MODIFY_DRIVER_PAYEE_CERTIFICATE,objectId,""));
            }else{
                addDriverPayCertificate(typeEnum,objectId,srcUrlModel.getRelativePath());
                tOperateLogsMapper.insertSelective(commonBiz.insertLogs(OperateLogsOperateTypeEnum.IMPORT_MODIFY_DRIVER_PAYEE_CERTIFICATE,objectId,""));
            }
        }else {
            if(staffDriverTicketsList.size()>=4){
                deleteEarlyPics(staffDriverTicketsList);
            }
            addDriverPayCertificate(typeEnum,objectId,srcUrlModel.getRelativePath());
            tOperateLogsMapper.insertSelective(commonBiz.insertLogs(OperateLogsOperateTypeEnum.IMPORT_MODIFY_DRIVER_PAYEE_CERTIFICATE,objectId,""));
        }
    }
    //删除图片
    private void deleteEarlyPics(List<TCertificationPictures> tqCertificationPictures){
        List<TCertificationPictures> deleteList = new ArrayList<>();
        for(int i = 0;i<tqCertificationPictures.size()-3;i++){
            TCertificationPictures tqCertificationPictures1 = new TCertificationPictures();
            tqCertificationPictures1.setId(tqCertificationPictures.get(i).getId());
            tqCertificationPictures1.setValid(CommonConstant.INTEGER_ZERO);
            commonBiz.setBaseEntityModify(tqCertificationPictures1,BaseContextHandler.getUserName());
            deleteList.add(tqCertificationPictures1);
        }
        if(ListUtils.isNotEmpty(deleteList)){
            tqCertificationPicturesMapper.batchUpdate(deleteList);
        }
    }
    //修改图片
    private void modifyDriverPayCertificate(CertificationPicturesFileTypeEnum  typeEnum,Long objectId,Long certiFIcatePicID,String relativePath){
        TCertificationPictures upPicture = new TCertificationPictures();
        upPicture.setId(certiFIcatePicID);
        upPicture.setFileType(typeEnum.getFileType());
        upPicture.setFileTypeName(typeEnum.getFileName());
        upPicture.setFileName(typeEnum.getFileName());
        upPicture.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_PAYEE.getKey(),"",relativePath,null));
        upPicture.setObjectType(typeEnum.getObjectType().getObjectType());
        upPicture.setObjectId(objectId);
        upPicture.setUploadUserName(BaseContextHandler.getUserName());
        upPicture.setUploadTime(new Date());
        commonBiz.setBaseEntityModify(upPicture,BaseContextHandler.getUserName());
        tqCertificationPicturesMapper.updateByPrimaryKeySelective(upPicture);
    }
    //新增图片
    private void addDriverPayCertificate(CertificationPicturesFileTypeEnum  typeEnum,Long objectId,String relativePath){
        TCertificationPictures newPicture = new TCertificationPictures();
        newPicture.setFileType(typeEnum.getFileType());
        newPicture.setFileTypeName(typeEnum.getFileName());
        newPicture.setFileName(typeEnum.getFileName());
        newPicture.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.DRIVER_PAYEE.getKey(),"",relativePath,null));
        newPicture.setObjectType(typeEnum.getObjectType().getObjectType());
        newPicture.setObjectId(objectId);
        newPicture.setUploadUserName(BaseContextHandler.getUserName());
        newPicture.setUploadTime(new Date());
        commonBiz.setBaseEntityAdd(newPicture,BaseContextHandler.getUserName());
        tqCertificationPicturesMapper.insertSelective(newPicture);
    }

    /**
     * 查看日志
     * @param requestModel
     * @return
     */
    public List<ViewLogResponseModel> driverPayeeLogs(DriverPayeeDetailRequestModel requestModel) {
        return tOperateLogsMapper.selectLogsByCondition(OperateLogsObjectTypeEnum.DRIVER_PAYEE.getKey(),requestModel.getDriverPayeeId(),null);
    }

    /**
     * 查询已审核的收款账户信息
     * @param requestDto
     * @return
     */
    public List<SearchDriverPayeesResponseModel> searchDriverPayees(SearchDriverPayeesRequestModel requestDto) {
        return tqDriverPayeeMapper.searchAuditedDriverPayees(requestDto.getDriverPayee());
    }

}
