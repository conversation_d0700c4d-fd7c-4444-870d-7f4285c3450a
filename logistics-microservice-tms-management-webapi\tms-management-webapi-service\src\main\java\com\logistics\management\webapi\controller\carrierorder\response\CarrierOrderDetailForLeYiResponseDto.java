package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CarrierOrderDetailForLeYiResponseDto {

    @ApiModelProperty("运单ID")
    private String carrierOrderId= "";

    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("客户单号")
    private String customerOrderCode = "";

    @ApiModelProperty("运单状态：10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消 2已放空")
    private String status= "";

    private String statusLabel= "";

    @ApiModelProperty("客户公司")
    private String entrustCompany= "";

    @ApiModelProperty("需求单号")
    private String demandOrderCode= "";

    @ApiModelProperty("调度")
    private String dispatchUserName= "";

    @ApiModelProperty("运单生成时间")
    private String dispatchTime= "";

    @ApiModelProperty("运单基本信息")
    private CarrierOrderDetailBasicInfoForLeYiDto carrierOrderDetailBasicInfo;

    @ApiModelProperty("运单货物信息")
    private List<CarrierOrderDetailGoodsInfoDto> carrierOrderDetailGoodsInfo;

    @ApiModelProperty("运单运费信息")
    private CarrierOrderDetailFreightFeeInfoDto carrierOrderDetailFreightFeeInfo;

    @ApiModelProperty("运单车辆司机信息")
    private List<CarrierOrderDetailVehicleDriverInfoDto> carrierOrderDetailVehicleDriverInfo;

    @ApiModelProperty("下单备注")
    private String remark = "";

    @ApiModelProperty("调度备注")
    private String dispatchRemark = "";

    @ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private String entrustSettlementTonnage = "";

    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private String carrierSettlementTonnage = "";

    @ApiModelProperty("单位")
    private String goodsUnit = "";

    @ApiModelProperty("是否我司 1:我司,2:其他车主")
    private String isOurCompany = "";

    @ApiModelProperty("车主对账单状态, -2:未关联对账")
    private String carrierSettleStatementStatus = "";

    @ApiModelProperty("1.3.7新增；项目标签")
    private String projectLabel="";
}
