package com.logistics.appapi.controller.vehiclesafecheck.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 待整改 - 提交
 * @Author: sj
 * @Date: 2019/11/6 13:15
 */
@Data
public class AddWaitReformRequestDto {
    @NotBlank(message = "车辆安全检查ID为空")
    @ApiModelProperty("车辆安全检查ID")
    private String safeCheckVehicleId;
    @ApiModelProperty("整改图片列表")
    private List<String> fileList;
}
