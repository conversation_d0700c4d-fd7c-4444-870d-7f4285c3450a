package com.logistics.management.webapi.api.feign.demandorderobjection.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/10/18 11:21
 */
@Data
public class SearchDemandOrderObjectionResponseDto {
    @ApiModelProperty("需求单id")
    private String demandId= "";
    @ApiModelProperty("需求单状态：1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收 1取消 2放空 3回退")
    private String status= "";
    private String statusDesc = "";
    @ApiModelProperty("需求单号")
    private String demandOrderCode= "";
    @ApiModelProperty("上报时间")
    private String reportTime="";
    @ApiModelProperty("上报人")
    private String reportContactName="";
    @ApiModelProperty("客户名称")
    private String customerName="";
    @ApiModelProperty("问题类型")
    private String objectionTypeDesc="";
    @ApiModelProperty("问题描述")
    private String objectionReason="";
    @ApiModelProperty("委托类型")
    private String entrustType="";
    @ApiModelProperty("地区（提货大区）")
    private String loadRegionName;

    @ApiModelProperty("委托单来源 1乐医托盘委托单 2委托方发单 3中石化系统 4扬巴邮件")
    private String demandOrderSource="";
}
