package com.logistics.appapi.client.driveraccount.hystrix;

import com.logistics.appapi.client.driveraccount.DriverAccountClient;
import com.logistics.appapi.client.driveraccount.request.AddBankCardAppletRequestModel;
import com.logistics.appapi.client.driveraccount.request.DriverAccountDetailRequestModel;
import com.logistics.appapi.client.driveraccount.response.BankCardInfoResponseModel;
import com.logistics.appapi.client.driveraccount.response.DriverAccountDetailResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/11 9:37
 */
@Component
public class DriverAccountClientHystrix implements DriverAccountClient {
    @Override
    public Result<Boolean> addBankCardForApplet(AddBankCardAppletRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<BankCardInfoResponseModel> currBankCardInfo() {
        return Result.timeout();
    }

    @Override
    public Result<DriverAccountDetailResponseModel> getDetail(DriverAccountDetailRequestModel requestModel) {
        return Result.timeout();
    }
}
