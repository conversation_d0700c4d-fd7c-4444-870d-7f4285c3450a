package com.logistics.tms.api.feign.contractorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.contractorder.hystrix.ContractOrderServiceApiHystrix;
import com.logistics.tms.api.feign.contractorder.model.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Api(value = "API-ContractOrderServiceApi-合同管理")
@FeignClient(name = "logistics-tms-services", fallback = ContractOrderServiceApiHystrix.class)
public interface ContractOrderServiceApi {
    
    @ApiOperation(value = "获取合同列表")
    @PostMapping(value = "/service/contractOrder/searchList")
    Result<PageInfo<ContractOrderSearchResponseModel>> searchContractOrderList(@RequestBody ContractOrderSearchRequestModel requestModel);

    @ApiOperation(value = "获取合同详情")
    @PostMapping(value = "/service/contractOrder/getDetail")
    Result<ContractOrderDetailResponseModel> getDetail(@RequestBody ContractOrderDetailRequestModel requestModel);

    @ApiOperation(value = "新增修改合同信息")
    @PostMapping(value = "/service/contractOrder/saveContract")
    Result saveContract(@RequestBody AddOrModifyContractOrderRequestModel requestModel);

    @ApiOperation(value = "作废或终止合同")
    @PostMapping(value = "/service/contractOrder/terminateOrCancelContract")
    Result terminateOrCancelContract(@RequestBody TerminateOrCancelContractRequestModel requestModel);

    @ApiOperation(value = "导出合同")
    @PostMapping(value = "/service/contractOrder/exportContractOrder")
    Result<List<ContractOrderSearchResponseModel>> exportContractOrder(@RequestBody ContractOrderSearchRequestModel requestModel);
}
