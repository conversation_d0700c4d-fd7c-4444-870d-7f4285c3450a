package com.logistics.management.webapi.controller.biddingorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BiddingOrderQuoteListResponseDto {
    /**
     * 报价单id
     */
    @ApiModelProperty("报价单id")
    private String biddingOrderQuoteId="";

    /**
     * 车主
     */
    @ApiModelProperty("车主")
    private String carrierContactName="";

    /**
     * 车主 报价状态 0 未选择，1 已选择，2 已取消
     */
    private String quoteStatus="";

    /**
     * 车主 报价状态
     */
    private String quoteStatusLabel="";

    /**
     * 报价金额
     */
    @ApiModelProperty("报价金额")
    private String quotePrice="";

    /**
     * 车长
     */
    @ApiModelProperty("车长")
    private String vehicleLength="";

    /**
     * 报价时间
     */
    @ApiModelProperty("报价时间")
    private String quoteTime="";

}
