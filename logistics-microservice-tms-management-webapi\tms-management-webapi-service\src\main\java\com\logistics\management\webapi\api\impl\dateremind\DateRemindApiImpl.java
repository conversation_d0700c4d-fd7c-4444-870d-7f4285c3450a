package com.logistics.management.webapi.api.impl.dateremind;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import com.logistics.management.webapi.api.feign.dateremind.DateRemindApi;
import com.logistics.management.webapi.api.feign.dateremind.dto.*;
import com.logistics.management.webapi.api.impl.dateremind.mapping.DateRemindListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelDateRemind;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.tms.api.feign.dateremind.DateRemindServiceApi;
import com.logistics.tms.api.feign.dateremind.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tools.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.logistics.management.webapi.base.utils.FrequentMethodUtils.isPositiveInteger;

/**
 * @Author: sj
 * @Date: 2019/5/31 8:51
 */
@RestController
public class DateRemindApiImpl implements DateRemindApi {
    @Autowired
    private DateRemindServiceApi dateRemindServiceApi;

    /**
     * 日期提醒列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<DateRemindListResponseDto>> searchDateRemindList(@RequestBody DateRemindListRequestDto requestDto) {
        Result<PageInfo<DateRemindListResponseModel>> pageInfoModel = dateRemindServiceApi.searchDateRemindList(MapperUtils.mapper(requestDto, DateRemindListRequestModel.class));
        pageInfoModel.throwException();
        PageInfo pageInfoDto = pageInfoModel.getData();
        if(pageInfoModel.getData()!=null&& ListUtils.isNotEmpty(pageInfoModel.getData().getList())){
           PageInfo<DateRemindListResponseModel> pageInfoData = pageInfoModel.getData();
           List<DateRemindListResponseDto> dateRemindDtoList = MapperUtils.mapper(pageInfoData.getList(),DateRemindListResponseDto.class,new DateRemindListMapping());
           pageInfoDto.setList(dateRemindDtoList);
        }
        return Result.success(pageInfoDto);
    }

    /**
     * 日期提醒详情
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<DateRemindDetailResponseDto> dateRemindDetail(@RequestBody @Valid DateRemindDetailRequestDto requestDto) {
        Result<DateRemindDetailResponseModel> result = dateRemindServiceApi.dateRemindDetail(MapperUtils.mapper(requestDto, DateRemindDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),DateRemindDetailResponseDto.class));
    }

    /**
     * 日期提醒保存/修改
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveOrModifyDateRemind(@RequestBody @Valid SaveOrModifyDateRemindRequestDto requestDto) {
        if(CommonConstant.ONE.equals(requestDto.getIfRemind()) && !isPositiveInteger(requestDto.getRemindDays())){
            throw new BizException(ManagementWebApiExceptionEnum.REMINDER_DAYS_ERROR);
        }
        Result<?> result = dateRemindServiceApi.saveOrModifyDateRemind(MapperUtils.mapper(requestDto, SaveOrModifyDateRemindRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 日期统一提醒
     * @param remindRequestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> unifiedDateRemind(@RequestBody @Valid UnifiedDateRemindRequestDto remindRequestDto) {
        Result<?>  result = dateRemindServiceApi.unifiedDateRemind(MapperUtils.mapper(remindRequestDto,UnifiedDateRemindRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void export(DateRemindListRequestDto requestDto, HttpServletResponse response) {
        Result<List<DateRemindListResponseModel>> result = dateRemindServiceApi.export(new DateRemindListRequestModel());
        result.throwException();
        String fileName = "日期提醒基础数据" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        List<DateRemindListResponseDto>  resultList  = MapperUtils.mapper(result.getData(),DateRemindListResponseDto.class,new DateRemindListMapping());
        Map<String, String> exportTypeMap = ExportExcelDateRemind.getDateRemindMap();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return resultList;
            }
        });
    }
}
