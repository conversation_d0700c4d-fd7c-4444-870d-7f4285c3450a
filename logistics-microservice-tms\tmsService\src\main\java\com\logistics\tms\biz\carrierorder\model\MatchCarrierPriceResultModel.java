package com.logistics.tms.biz.carrierorder.model;

import com.logistics.tms.biz.carrierfreight.model.CarrierFreightCarrierModel;
import com.logistics.tms.entity.TCarrierFreightConfigScheme;
import com.logistics.tms.entity.TRouteDistanceConfig;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
public class MatchCarrierPriceResultModel {

    //车主id-委托类型map
    private Map<Long, Map<Integer, CarrierFreightCarrierModel>> entrustCarrierFreightMap;

    //系统配置的距离集合
    private List<TRouteDistanceConfig> tRouteDistanceConfigs;

    //车主运价id-方案map
    private Map<Long, List<TCarrierFreightConfigScheme>> carrierFreightConfigSchemeMap;
}
