package com.logistics.management.webapi.client.routeconfig.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.routeconfig.RouteDistanceConfigListClient;
import com.logistics.management.webapi.client.routeconfig.request.*;
import com.logistics.management.webapi.client.routeconfig.response.RouteDistanceConfigDetailResponseModel;
import com.logistics.management.webapi.client.routeconfig.response.RouteDistanceConfigRecommendResponseModel;
import com.logistics.management.webapi.client.routeconfig.response.RouteDistanceConfigResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @Author: xuanjia.liang
 * @Date: 2023/6/30 11:23
 */
@Component
public class RouteDistanceConfigListHystrix implements RouteDistanceConfigListClient {


    @Override
    public Result<PageInfo<RouteDistanceConfigResponseModel>> searchList(RouteDistanceConfigListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<RouteDistanceConfigDetailResponseModel> detail(RouteDistanceConfigRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> add(RouteDistanceConfigAddRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> edit(RouteDistanceConfigEditRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delete(RouteDistanceConfigRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<RouteDistanceConfigRecommendResponseModel> recommend(RouteDistanceConfigRecommendRequestModel requestModel) {
        return Result.timeout();
    }
}
