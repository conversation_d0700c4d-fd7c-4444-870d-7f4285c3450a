package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum WorkReportSourceEnum {
    DEFAULT(-1, ""),
    BACK_STAGE(1, "后台"),
    WEB(2, "前台"),
    APPLET(3,"小程序"),
    ;

    private final Integer key;

    private final String value;

    public static WorkReportSourceEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
