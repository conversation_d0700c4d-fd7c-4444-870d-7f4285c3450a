package com.logistics.management.webapi.api.feign.freight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: sj
 * @Date: 2019/12/24 13:04
 */
@Data
public class AddFreightRequestDto {
    @NotBlank(message ="角色不能为空")
    @ApiModelProperty("角色: 1 货主 2 车主")
    private String roleType;
    @ApiModelProperty("业务表公司Id")
    @NotBlank(message = "请选择公司")
    private String companyId;
}
