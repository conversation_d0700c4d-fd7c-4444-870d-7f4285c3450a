package com.logistics.management.webapi.controller.reserveapply.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ReserveApplyListRequestDto extends AbstractPageForm<ReserveApplyListRequestDto> {

	@ApiModelProperty("状态：-1 已撤销，0 待业务审核，1 待财务审核，2 已驳回，3 待打款，4 已打款")
	private String status;

	@ApiModelProperty("申请单号")
	private String applyCode;

	@ApiModelProperty("司机")
	private String driver;

	@ApiModelProperty("机构:1:自主 2:外部 3:自营")
	private String staffProperty;

	@ApiModelProperty("申请时间起")
	private String applyTimeStart;

	@ApiModelProperty("申请时间起")
	private String applyTimeEnd;

	@ApiModelProperty("财务审核时间起")
	private String financialAuditTimeStart;

	@ApiModelProperty("审核时间止")
	private String financialAuditTimeEnd;

	@ApiModelProperty("打款时间起")
	private String payTimeStart;

	@ApiModelProperty("打款时间止")
	private String payTimeEnd;

	@ApiModelProperty("备用金类型: 1 充值 2 垫付; 1.3.6 新增")
	private String type;
}
