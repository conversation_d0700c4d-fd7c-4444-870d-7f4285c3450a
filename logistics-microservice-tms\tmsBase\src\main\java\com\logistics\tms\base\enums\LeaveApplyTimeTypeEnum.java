/**
 * Created by yun<PERSON>zhou on 2017/12/12.
 */
package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalTime;

@Getter
@AllArgsConstructor
public enum LeaveApplyTimeTypeEnum {
    DEFAULT(-1, ""),
    MORNING(1, "上午"),
    AFTERNOON(2, "下午"),
    ;

    private Integer key;
    private String value;

    public static LocalTime getLocalTime(Integer key) {
        return LeaveApplyTimeTypeEnum.MORNING.getKey().equals(key) ?
                LocalTime.MIDNIGHT : LocalTime.NOON;
    }

}
