package com.logistics.management.webapi.api.feign.demandorderobjection.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/10/18 11:21
 */
@Data
public class SearchDemandOrderObjectionRequestDto extends AbstractPageForm<SearchDemandOrderObjectionRequestDto> {
    @ApiModelProperty("需求单状态：空 全部 500待发布 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收 1取消 2放空 3回退")
    private String demandStatus;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("下单时间开始")
    private String publishTimeStart;
    @ApiModelProperty("下单时间结束")
    private String publishTimeEnd;
    @ApiModelProperty("地区（提货大区）")
    private String loadRegionName;
    @ApiModelProperty("上报时间开始")
    private String reportTimeStart;
    @ApiModelProperty("上报时间结束")
    private String reportTimeEnd;
}
