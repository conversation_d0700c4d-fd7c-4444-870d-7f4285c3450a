package com.logistics.appapi.client.thirdparty.basicdata.file.hystrix;

import com.logistics.appapi.client.thirdparty.basicdata.file.FileServiceApi;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.BatchGetOSSFileUrlRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.GetFileByFilePathRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.GetOSSUrlRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.request.PdfWriteToImgRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/1/22 15:16
 */
@Component
public class FileServiceApiHystrix implements FileServiceApi {
    @Override
    public Result<FileUploadForAIResponseModel> uploadOSSFileForAI(MultipartFile file, String picType, String idCardSide) {
        return Result.timeout();
    }

    @Override
    public Result<UploadFileOSSResponseModel> uploadMultiPartFileOSS(MultipartFile file) {
        return Result.timeout();
    }

    @Override
    public Result<PdfWriteToImgResponseModel> pdfWriteToImgOSS(PdfWriteToImgRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetOSSUrlResponseModel> getOSSFileUrl(GetOSSUrlRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetOSSUrlResponseModel>> batchGetOSSFileUrl(BatchGetOSSFileUrlRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetFileByteOSSResponseModel> getFileByteOSS(GetFileByFilePathRequestModel requestModel) {
        return Result.timeout();
    }
}
