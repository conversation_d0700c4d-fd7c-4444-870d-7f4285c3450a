package com.logistics.tms.client;

import com.logistics.tms.client.feign.warehouse.stock.WarehouseStockServiceApi;
import com.logistics.tms.client.feign.warehouse.stock.reponse.CheckChangeUnloadWarehouseResponseModel;
import com.logistics.tms.client.feign.warehouse.stock.reponse.GetCompanyNameByCodeRespModel;
import com.logistics.tms.client.feign.warehouse.stock.reponse.GetSkipTokenResponseModel;
import com.logistics.tms.client.feign.warehouse.stock.reponse.ListWarehouseByNameResponseModel;
import com.logistics.tms.client.feign.warehouse.stock.request.CheckChangeUnloadWarehouseRequestModel;
import com.logistics.tms.client.feign.warehouse.stock.request.GetCompanyNameByCodeReqModel;
import com.logistics.tms.client.feign.warehouse.stock.request.GetSkipTokenRequestModel;
import com.logistics.tms.client.feign.warehouse.stock.request.ListWarehouseByNameRequestModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2022/10/28 16:39
 */
@Service
public class WarehouseStockClient {

    @Resource
    private WarehouseStockServiceApi warehouseStockServiceApi;

    /**
     * 模糊查询云仓仓库信息
     *
     * @param carrierOrderCodes 运单号
     * @return
     */
    public List<CheckChangeUnloadWarehouseResponseModel> checkChangeUnloadWarehouse(List<String> carrierOrderCodes) {
        CheckChangeUnloadWarehouseRequestModel checkChangeUnloadWarehouseRequestModel = new CheckChangeUnloadWarehouseRequestModel();
        checkChangeUnloadWarehouseRequestModel.setCarrierOrderCodes(carrierOrderCodes);
        Result<List<CheckChangeUnloadWarehouseResponseModel>> result = warehouseStockServiceApi.checkChangeUnloadWarehouse(checkChangeUnloadWarehouseRequestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 模糊查询云仓仓库信息
     *
     * @param warehouseKey 模糊查询关键字
     * @return
     */
    public List<ListWarehouseByNameResponseModel> listWarehouseByName(String warehouseKey) {
        ListWarehouseByNameRequestModel listWarehouseByNameRequestModel = new ListWarehouseByNameRequestModel();
        listWarehouseByNameRequestModel.setWarehouseKey(warehouseKey);
        Result<List<ListWarehouseByNameResponseModel>> result = warehouseStockServiceApi.listWarehouseByName(listWarehouseByNameRequestModel);
        result.throwException();
        return result.getData();
    }

    /**
     * 获取云仓小程序跳转令牌
     */
    public String getSkipToken(String mobile) {
        GetSkipTokenRequestModel requestModel = new GetSkipTokenRequestModel();
        requestModel.setMobilePhone(mobile);
        Result<GetSkipTokenResponseModel> result = warehouseStockServiceApi.getSkipToken(requestModel);
        result.throwException();
        return result.getData().getToken();
    }


    /**
     * 通过托盘编码查询云仓出库计划上托盘来源公司
     *
     * @return
     */
    public GetCompanyNameByCodeRespModel getCompanyNameByCode(String code) {
        GetCompanyNameByCodeReqModel getCompanyNameByCodeReqModel = new GetCompanyNameByCodeReqModel();
        getCompanyNameByCodeReqModel.setCode(code);
        Result<GetCompanyNameByCodeRespModel> result = warehouseStockServiceApi.getCompanyNameByCode(getCompanyNameByCodeReqModel);
        result.throwException();
        return result.getData();
    }



}
