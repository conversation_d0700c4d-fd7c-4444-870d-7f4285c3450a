package com.logistics.appapi.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class SubmitRenewableOrderRequestDto {

	@ApiModelProperty(value = "乐橘新生订单审核表id",required = true)
	@NotBlank(message = "新生订单id不能为空")
	private String renewableAuditId;

	@ApiModelProperty(value = "收货地址code（云仓仓库地址code）", required = true)
	@NotBlank(message = "收货地址code不能为空")
	private String unloadAddressCode;

	@ApiModelProperty(value = "收货省id",required = true)
	@NotBlank(message = "收货省不能为空")
	private String unloadProvinceId;

	@ApiModelProperty(value = "收货省",required = true)
	@NotBlank(message = "收货省不能为空")
	private String unloadProvinceName;

	@ApiModelProperty(value = "收货市id",required = true)
	@NotBlank(message = "收货市不能为空")
	private String unloadCityId;

	@ApiModelProperty(value = "收货市",required = true)
	@NotBlank(message = "收货市不能为空")
	private String unloadCityName;

	@ApiModelProperty(value = "收货区id",required = true)
	@NotBlank(message = "收货区不能为空")
	private String unloadAreaId;

	@ApiModelProperty(value = "收货区",required = true)
	@NotBlank(message = "收货区不能为空")
	private String unloadAreaName;

	@ApiModelProperty("收货地址详细")
	private String unloadDetailAddress;

	@ApiModelProperty(value = "收货仓库",required = true)
	@NotBlank(message = "收货仓库不能为空")
	private String unloadWarehouse;

	@ApiModelProperty(value = "收货人",required = true)
	@NotBlank(message = "收货人不能为空")
	private String receiverName;

	@ApiModelProperty(value = "收货人手机号",required = true)
	@NotBlank(message = "收货人手机号不能为空")
	private String receiverMobile;

	@ApiModelProperty(value = "卸货经度。有就传，没有就不传")
	private String unloadLongitude;

	@ApiModelProperty(value = "卸货纬度。有就传，没有就不传")
	private String unloadLatitude;
}
