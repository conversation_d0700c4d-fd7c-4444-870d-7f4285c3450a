package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WorkGroupAmountTypeEnum {

    DEFAULT(WorkGroupOrderTypeEnum.DEFAULT,0, ""),

    ENTRUST_AMOUNT(WorkGroupOrderTypeEnum.DEMAND_ORDER,101, "委托数"),
    ARRANGED_AMOUNT(WorkGroupOrderTypeEnum.DEMAND_ORDER,102, "已安排"),
    NOT_ARRANGED_AMOUNT(WorkGroupOrderTypeEnum.DEMAND_ORDER,103, "未安排"),

    EXPECT_AMOUNT(WorkGroupOrderTypeEnum.CARRIER_ORDER,201, "预提数"),
    LOAD_AMOUNT(WorkGroupOrderTypeEnum.CARRIER_ORDER,202, "实提数"),
    DIFFERENCE_AMOUNT(WorkGroupOrderTypeEnum.CARRIER_ORDER,203, "差异数(预提-实提)"),
    ;

    private final WorkGroupOrderTypeEnum orderTypeEnum;
    private final Integer key;
    private final String value;

    public static WorkGroupAmountTypeEnum getEnum(Integer key) {
        for (WorkGroupAmountTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }

    public static WorkGroupAmountTypeEnum getEnumByKeyStr(String key) {
        for (WorkGroupAmountTypeEnum t : values()) {
            if (t.getKey().toString().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }
}
