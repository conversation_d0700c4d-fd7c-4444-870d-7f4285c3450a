package com.logistics.management.webapi.client.thirdparty.basicdata.ocr.hystrix;

import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.OCRClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.response.OcrMultipleInvoiceResponseModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.ocr.response.OcrPictureRequestModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Component
public class OCRClientHystrix implements OCRClient {

    @Override
    public Result<OcrMultipleInvoiceResponseModel> multipleInvoice(MultipartFile file, String type) {
        return Result.timeout();
    }

    @Override
    public Result<String> qRCodeIO(OcrPictureRequestModel requestModel) {
        return Result.timeout();
    }
}
