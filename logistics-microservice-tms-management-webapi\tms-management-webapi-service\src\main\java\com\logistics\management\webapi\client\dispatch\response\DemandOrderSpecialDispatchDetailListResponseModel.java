package com.logistics.management.webapi.client.dispatch.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/8/7 9:14
 */
@Data
public class DemandOrderSpecialDispatchDetailListResponseModel {

    /**
     * 需求单货物id
     */
    private Long demandOrderGoodsId;

    /**
     * 需求单id
     */
    private Long demandOrderId;

    /**
     * 需求单号
     */
    private String demandOrderCode;

    /**
     * 客户单号
     */
    private String customerOrderCode;

    /**
     * 品名
     */
    private String goodsName;

    /**
     * 规格
     */
    private String goodsSize;
    private Integer length;
    private Integer width;
    private Integer height;

    /**
     * 委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生
     */
    private Integer source;

    /**
     * 需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨；100 新生回收，101 新生销售
     */
    private Integer entrustType;

    /**
     * 待调度数
     */
    private BigDecimal notArrangedAmount;

    /**
     * 发货地址
     */
    private String loadProvinceName;
    private String loadCityName;
    private String loadAreaName;
    private String loadWarehouse;
    private String loadDetailAddress;

    /**
     * 收货地址
     */
    private String unloadWarehouse;
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 到下个点位距离
     */
    private BigDecimal nextPointDistance;
    
}
