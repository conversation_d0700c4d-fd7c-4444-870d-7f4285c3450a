package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RealNameAuthenticationStatusEnum {

    DEFAULT(-1, ""),
    NOT_REAL_NAME(0, "待实名"),
    WAIT_REAL_NAME(1, "实名中"),
    REAL_NAME(2, "已实名");

    private final Integer key;

    private final String value;

    public static RealNameAuthenticationStatusEnum getEnum(Integer key) {
        for (RealNameAuthenticationStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
