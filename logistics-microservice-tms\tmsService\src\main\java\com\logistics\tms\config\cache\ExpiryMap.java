package com.logistics.tms.config.cache;

import com.yelo.tools.utils.ListUtils;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @author：wjf
 * @date：2021/5/6 10:23
 */
public class ExpiryMap<K, V> extends ConcurrentHashMap<K, V> {

    private ConcurrentHashMap<K, Long> expiryMap = new ConcurrentHashMap<>();

    /**
     * @param expiryTime 键值对有效期 秒
     * @return
     */
    public V put(K key, V value, long expiryTime) {
        if (!expiryMap.containsKey(key)) {
            expiryMap.put(key, System.currentTimeMillis() + expiryTime);
        }
        return super.put(key, value);
    }

    public V get(Object key) {
        if (key == null) {
            return null;
        }
        return super.get(key);
    }

    /**
     * 1、删除过期的数据
     * 2、删除刚启用的仓库缓存数据
     */
    public void removeExpiry(List<String> removeKeyList){
        Long currentTime = System.currentTimeMillis();
        for (Entry<K, Long> entry : expiryMap.entrySet()) {
            if (currentTime > entry.getValue() || (ListUtils.isNotEmpty(removeKeyList) && removeKeyList.contains(entry.getKey().toString()))){
                super.remove(entry.getKey());
                expiryMap.remove(entry.getKey());
            }
        }
    }
}
