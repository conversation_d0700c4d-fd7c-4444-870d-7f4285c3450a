package com.logistics.management.webapi.api.impl.carrierfreight;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.carrierfreight.CarrierFreightApi;
import com.logistics.management.webapi.api.feign.carrierfreight.dto.CarrierFreightAddRequestDto;
import com.logistics.management.webapi.api.feign.carrierfreight.dto.CarrierFreightEnableRequestDto;
import com.logistics.management.webapi.api.feign.carrierfreight.dto.SearchCarrierFreightRequestDto;
import com.logistics.management.webapi.api.feign.carrierfreight.dto.SearchCarrierFreightResponseDto;
import com.logistics.management.webapi.api.impl.carrierfreight.mapping.CarrierFreightSelectListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.carrierfreight.CarrierFreightServiceApi;
import com.logistics.tms.api.feign.carrierfreight.model.CarrierFreightAddRequestModel;
import com.logistics.tms.api.feign.carrierfreight.model.CarrierFreightEnableRequestModel;
import com.logistics.tms.api.feign.carrierfreight.model.SearchCarrierFreightRequestModel;
import com.logistics.tms.api.feign.carrierfreight.model.SearchCarrierFreightResponseModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 车主运价
 *
 * <AUTHOR>
 * @date 2022/9/1 14:35
 */
@Slf4j
@RestController
public class CarrierFreightApiImpl implements CarrierFreightApi {

    @Autowired
    private CarrierFreightServiceApi carrierFreightServiceApi;

    /**
     * 添加车主运价
     *
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addCarrierFreight(@RequestBody CarrierFreightAddRequestDto requestDto) {
        Result<Boolean> result = carrierFreightServiceApi.addCarrierFreight(MapperUtils.mapper(requestDto, CarrierFreightAddRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 车主运价禁用启用
     *
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> enable(@RequestBody CarrierFreightEnableRequestDto requestDto) {
        Result<Boolean> result = carrierFreightServiceApi.enable(MapperUtils.mapper(requestDto, CarrierFreightEnableRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 查询车主运价列表
     *
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchCarrierFreightResponseDto>> searchList(@RequestBody SearchCarrierFreightRequestDto requestDto) {
        Result<PageInfo<SearchCarrierFreightResponseModel>> result = carrierFreightServiceApi.searchList(MapperUtils.mapper(requestDto, SearchCarrierFreightRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchCarrierFreightResponseDto> carrierFreightList = MapperUtils.mapper(pageInfo.getList(),
                SearchCarrierFreightResponseDto.class,
                new CarrierFreightSelectListMapping());
        pageInfo.setList(carrierFreightList);
        return Result.success(pageInfo);
    }
}
