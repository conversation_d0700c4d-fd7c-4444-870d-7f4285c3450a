package com.logistics.tms.controller.carrierdriverrel.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2022/7/18
 */
@Data
public class DriveDetailResponseModel {

	@ApiModelProperty("司机id")
	private Long carrierDriverId;

	@ApiModelProperty("姓名")
	private String staffDriverName;

	@ApiModelProperty("性别")
	private Integer staffDriverGender;

	@ApiModelProperty("年龄")
	private Integer staffDriverAge;

	@ApiModelProperty("手机号")
	private String staffDriverMobile;

	@ApiModelProperty("身份证号")
	private String identityNumber;

	@ApiModelProperty("新增时间")
	private Date createdTime;

	@ApiModelProperty("新增人")
	private String createdByName;
}
