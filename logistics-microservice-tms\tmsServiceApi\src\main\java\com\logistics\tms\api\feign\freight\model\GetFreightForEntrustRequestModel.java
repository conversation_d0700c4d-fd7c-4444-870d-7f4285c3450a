package com.logistics.tms.api.feign.freight.model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * @Author: sj
 * @Date: 2020/1/2 13:52
 */
@Data
public class GetFreightForEntrustRequestModel {
    @ApiModelProperty("委托方公司ID")
    private Long companyEntrustId;
    @ApiModelProperty("仓库ID")
    private Long warehouseId;
    @ApiModelProperty("发货省ID")
    private Long fromProvinceId;
    @ApiModelProperty("发货市ID")
    private Long fromCityId;
    @ApiModelProperty("发货区ID")
    private Long fromAreaId;
    @ApiModelProperty("收货省ID")
    private Long toProvinceId;
    @ApiModelProperty("收货市ID")
    private Long toCityId;
    @ApiModelProperty("收货区ID")
    private Long toAreaId;
    @ApiModelProperty("货物数量")
    private BigDecimal goodsAmount;
    @ApiModelProperty("计价类型 1 基价 2 一日游")
    private Integer calcType;
    @ApiModelProperty("费用类型 1 单价 2 一口价")
    private Integer freightType;
}
