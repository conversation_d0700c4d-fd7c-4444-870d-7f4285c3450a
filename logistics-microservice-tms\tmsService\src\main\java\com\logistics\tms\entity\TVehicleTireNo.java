package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleTireNo extends BaseEntity {
    /**
    * 轮胎外键
    */
    @ApiModelProperty("轮胎外键")
    private Long tireId;

    /**
    * 轮胎牌号
    */
    @ApiModelProperty("轮胎牌号")
    private String tireBrand;

    /**
    * 数量
    */
    @ApiModelProperty("数量")
    private Integer amount;

    /**
    * 单价
    */
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;
}