package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AuditOrRejectVehicleRequestDto {

    @ApiModelProperty("运单id")
    @NotBlank(message = "运单id不能为空")
    private String carrierOrderId;
    @ApiModelProperty("车辆历史纪录ID")
    @NotBlank(message = "车辆历史纪录id不能为空")
    private String vehicleHistoryId;
    @ApiModelProperty("操作类型 1 审核 2 驳回")
    @NotBlank(message = "操作类型不能为空（1 审核 2 驳回）")
    private String type;
    @ApiModelProperty("驳回原因")
    private String rejectReason;

}
