package com.logistics.tms.mapper;

import com.logistics.tms.controller.vehiclesafecheck.request.AppletSafeCheckListRequestModel;
import com.logistics.tms.controller.vehiclesafecheck.request.SafeCheckBoardRequestModel;
import com.logistics.tms.controller.vehiclesafecheck.request.SearchSafeCheckListRequestModel;
import com.logistics.tms.controller.vehiclesafecheck.response.*;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TVehicleSafeCheckVehicle;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import java.util.Date;
import java.util.List;

@Mapper
public interface TVehicleSafeCheckVehicleMapper extends BaseMapper<TVehicleSafeCheckVehicle> {

    List<SearchSafeCheckListResponseModel> searchList(@Param("params") SearchSafeCheckListRequestModel requestModel);

    SummarySafeCheckResponseModel getListSummary(@Param("params") SearchSafeCheckListRequestModel requestModel);

    SafeCheckDetailResponseModel getDetail(@Param("safeCheckVehicleId") Long safeCheckVehicleId, @Param("objectType") Integer objectType);

    int delSafeCheck(@Param("safeCheckVehicleId") Long safeCheckVehicleId, @Param("modifiedBy") String modifiedBy, @Param("modifiedTime") Date modifiedTime);

    List<SafeCheckBoardResponseModel> getSafeCheckBoardInfo(@Param("params") SafeCheckBoardRequestModel requestModel);

    int getUnFinishCheckCount(@Param("vehicleId") Long vehicleId);

    List<TVehicleSafeCheckVehicle> getListByCheckId(@Param("safeCheckId") Long safeCheckId);

    /* 小程序 */
    List<AppletSafeCheckListResponseModel> searchAppletList(@Param("params") AppletSafeCheckListRequestModel responseModel);

    AppletSafeCheckSummaryResponseModel getAppletSummary(@Param("params") AppletSafeCheckListRequestModel responseModel);
}



