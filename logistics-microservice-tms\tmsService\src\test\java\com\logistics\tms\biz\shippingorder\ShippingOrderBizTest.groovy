package com.logistics.tms.biz.shippingorder

import com.logistics.tms.controller.shippingorder.request.GetShippingOrderDetailRequestModel
import com.logistics.tms.controller.shippingorder.request.SearchShippingOrderListRequestModel
import org.springframework.boot.test.context.SpringBootTest
import spock.lang.Specification

import javax.annotation.Resource


@SpringBootTest
class ShippingOrderBizTest extends Specification {

    @Resource
    ShippingOrderCommonBiz shippingOrderCommonBiz
    @Resource
    ShippingOrderBiz shippingOrderBiz

    def "searchList"() {
        given:
        def model = new SearchShippingOrderListRequestModel()
        expect:
        def action = shippingOrderBiz.searchList(model)
        println action
    }

    def "getDetail"() {
        given:
        def model = new GetShippingOrderDetailRequestModel(shippingOrderId: 1L)
        expect:
        def action = shippingOrderBiz.getDetail(model)
        println action
    }

    def "checkAndGenerateShippingFeeFromCarrierOrderAction"() {
        given:
        List<Long> ids = [21404]
        expect:
        def action = shippingOrderCommonBiz.checkAndGenerateShippingFeeFromCarrierOrderAction(ids)
        println action
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme