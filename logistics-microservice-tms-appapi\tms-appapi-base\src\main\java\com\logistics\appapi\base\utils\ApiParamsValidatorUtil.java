package com.logistics.appapi.base.utils;


import com.logistics.appapi.base.constant.CommonConstant;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;

/**
 * 参数校验工具类
 *
 * <AUTHOR>
 * @date ：Created in 2022/7/12
 */
public class ApiParamsValidatorUtil {

	/**
	 * 验证单价：0<=价格<=100000
	 *
	 * @param amount 浮点数量
	 * @return 验证结果: true为验证通过,false为验证不通过
	 */
	public static boolean verifyFloatAmount(String amount) {
		if (StringUtils.isBlank(amount)){
			return true;
		}
		boolean result = true;
		try {
			BigDecimal bigDecimal = new BigDecimal(amount);
			if (bigDecimal.compareTo(CommonConstant.BIG_DECIMAL_ZERO) < CommonConstant.INTEGER_ZERO ||
					bigDecimal.compareTo(CommonConstant.BIG_DECIMAL_HUNDRED_THOUSAND) > CommonConstant.INTEGER_ZERO) {
				result = false;
			}
		} catch (Exception e) {
			result = false;
		}
		return result;
	}
}
