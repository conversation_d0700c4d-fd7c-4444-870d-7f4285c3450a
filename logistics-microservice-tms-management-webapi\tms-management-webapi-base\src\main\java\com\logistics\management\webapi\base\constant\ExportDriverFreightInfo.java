package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

public class ExportDriverFreightInfo {
    private ExportDriverFreightInfo() {
    }
    private static final Map<String,String> DRIVER_FREIGHT_INFO;
    static{
        DRIVER_FREIGHT_INFO =new LinkedHashMap<>();
        DRIVER_FREIGHT_INFO.put("状态", "statusDesc");
        DRIVER_FREIGHT_INFO.put("运单号", "carrierOrderCode");
        DRIVER_FREIGHT_INFO.put("需求单", "demandOrderCode");
        DRIVER_FREIGHT_INFO.put("车辆机构","vehiclePropertyDesc");
        DRIVER_FREIGHT_INFO.put("车牌号", "vehicleNo");
        DRIVER_FREIGHT_INFO.put("司机", "driver");
        DRIVER_FREIGHT_INFO.put("司机费用合计（元）", "driverFreightTotal");
        DRIVER_FREIGHT_INFO.put("司机运费（元）", "driverFreight");
        DRIVER_FREIGHT_INFO.put("司机调整费（元）", "adjustFee");
        DRIVER_FREIGHT_INFO.put("司机多装多卸费（元）", "markupFee");
        DRIVER_FREIGHT_INFO.put("实际结算数据", "unloadAmount");
        DRIVER_FREIGHT_INFO.put("实际结算数单位", "goodsUnitDesc");
        DRIVER_FREIGHT_INFO.put("实际签收时间", "signTime");
        DRIVER_FREIGHT_INFO.put("发货地", "loadAddress");
        DRIVER_FREIGHT_INFO.put("收货地", "unloadAddress");
        DRIVER_FREIGHT_INFO.put("品名", "goodsName");
        DRIVER_FREIGHT_INFO.put("规格", "size");
        DRIVER_FREIGHT_INFO.put("调度", "dispatchUserName");
        DRIVER_FREIGHT_INFO.put("车主", "companyCarrierName");
        DRIVER_FREIGHT_INFO.put("货主", "companyEntrustName");
        DRIVER_FREIGHT_INFO.put("运单生成时间", "dispatchTime");

    }

    public static Map<String, String> getDriverFreightInfo() {
        return DRIVER_FREIGHT_INFO;
    }
}

