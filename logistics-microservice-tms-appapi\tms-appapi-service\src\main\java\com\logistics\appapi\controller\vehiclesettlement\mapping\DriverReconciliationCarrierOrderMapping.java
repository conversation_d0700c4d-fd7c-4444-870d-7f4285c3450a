package com.logistics.appapi.controller.vehiclesettlement.mapping;

import com.logistics.appapi.base.enums.GoodsUnitEnum;
import com.logistics.appapi.base.enums.PriceTypeEnum;
import com.logistics.appapi.client.vehiclesettlement.response.ReconciliationCarrierOrderDetailResponseModel;
import com.logistics.appapi.client.vehiclesettlement.response.ReconciliationCarrierOrderListResponseModel;
import com.logistics.appapi.controller.vehiclesettlement.response.ReconciliationCarrierOrderDetailResponseDto;
import com.logistics.appapi.controller.vehiclesettlement.response.ReconciliationCarrierOrderListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @author：wjf
 * @date：2021/4/13 14:40
 */
public class DriverReconciliationCarrierOrderMapping extends MapperMapping<ReconciliationCarrierOrderDetailResponseModel,ReconciliationCarrierOrderDetailResponseDto> {
    @Override
    public void configure() {
        ReconciliationCarrierOrderDetailResponseModel source = getSource();
        ReconciliationCarrierOrderDetailResponseDto destination = getDestination();

        if (ListUtils.isNotEmpty(source.getCarrierOrderList())){
            List<ReconciliationCarrierOrderListResponseDto> carrierOrderList = new ArrayList<>();
            ReconciliationCarrierOrderListResponseDto dto;
            BigDecimal amount;
            for (ReconciliationCarrierOrderListResponseModel model : source.getCarrierOrderList()) {
                //转换运单信息
                dto = MapperUtils.mapper(model, ReconciliationCarrierOrderListResponseDto.class);
                //司机实际数量
                amount = model.getUnloadAmount();
                dto.setAmount(amount.stripTrailingZeros().toPlainString()+ GoodsUnitEnum.getEnum(model.getGoodsUnit()).getUnit());

                //计算司机费用
                BigDecimal dispatchFreightFee = model.getDispatchFreightFee();
                if (model.getDispatchFreightFeeType().equals(PriceTypeEnum.UNIT_PRICE.getKey())) {
                    dispatchFreightFee = dispatchFreightFee.multiply(amount).setScale(2, BigDecimal.ROUND_HALF_UP);
                }
                //司机费用加上调整费用、多装多装费用
                dispatchFreightFee = dispatchFreightFee.add(model.getAdjustFee()).add(model.getMarkupFee());
                //如果存在临时费用，则加上临时费用
                if (model.getDriverOtherFee() != null){
                    dispatchFreightFee = dispatchFreightFee.add(model.getDriverOtherFee());
                }
                dto.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee));
                //处理发卸地址信息
                dto.setLoadAddress((StringUtils.isNotEmpty(model.getLoadWarehouse())?"【" + model.getLoadWarehouse() + "】":"") + " "+model.getLoadProvinceName()+model.getLoadCityName()+model.getLoadAreaName() + model.getLoadDetailAddress());
                dto.setUnloadAddress((StringUtils.isNotEmpty(model.getUnloadWarehouse())?"【" + model.getUnloadWarehouse() + "】":"")  + " "+model.getUnloadProvinceName()+model.getUnloadCityName()+model.getUnloadAreaName()+ model.getUnloadDetailAddress());
                carrierOrderList.add(dto);
            }
            destination.setCarrierOrderList(carrierOrderList);
        }
    }
}
