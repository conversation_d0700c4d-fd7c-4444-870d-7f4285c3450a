package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleGps extends BaseEntity {
    /**
    * 车辆ID
    */
    @ApiModelProperty("车辆ID")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 司机ID
    */
    @ApiModelProperty("司机ID")
    private Long driverId;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String driverName;

    /**
    * 司机手机号
    */
    @ApiModelProperty("司机手机号")
    private String driverMobile;

    /**
    * 司机身份证号吗
    */
    @ApiModelProperty("司机身份证号吗")
    private String driverIdentity;

    /**
    * 车辆承运单状态 0 无运单 1 有运单 ??可能不准确
    */
    @ApiModelProperty("车辆承运单状态 0 无运单 1 有运单 ??可能不准确")
    private Integer carrierOrderStatus;

    /**
    * 车辆状态 0 停车 1 行驶中
    */
    @ApiModelProperty("车辆状态 0 停车 1 行驶中")
    private Integer vehicleStatus;

    /**
    * 数据上传时间
    */
    @ApiModelProperty("数据上传时间")
    private Date uploadTime;

    /**
    * 当前位置
    */
    @ApiModelProperty("当前位置")
    private String currentLocation;

    /**
    * 最新一笔调度单ID
    */
    @ApiModelProperty("最新一笔调度单ID")
    private Long dispatchOrderId;

    /**
    * 最新一笔调度单号
    */
    @ApiModelProperty("最新一笔调度单号")
    private String dispatchOrderCode;
}