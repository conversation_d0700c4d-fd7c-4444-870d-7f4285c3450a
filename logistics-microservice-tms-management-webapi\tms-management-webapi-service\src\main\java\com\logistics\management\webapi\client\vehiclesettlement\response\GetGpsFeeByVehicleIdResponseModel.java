package com.logistics.management.webapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/10/14 16:06
 */
@Data
public class GetGpsFeeByVehicleIdResponseModel {
    private Long gpsFeeId;
    private Integer status;
    private BigDecimal serviceFee;
    private Integer cooperationPeriod;
    @ApiModelProperty("gps结算月份")
    private String deductingMonth;
    @ApiModelProperty("gps当月扣减费用")
    private BigDecimal deductingFee;
    @ApiModelProperty("gps剩余未扣减费用")
    private BigDecimal remainingDeductingFee;
    @ApiModelProperty("终止时间（用于判断是否是合作的最后一个月）")
    private Date finishDate;
    @ApiModelProperty("gps查询月份")
    private String currentMonth;
}
