<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TReserveBalanceMapper" >

  <select id="statisticsReserve" resultType="com.logistics.tms.biz.reservebalance.model.StatisticsReserveModel">
      SELECT IFNULL(SUM(trb.`balance_amount`), 0)      totalRemainingAmount,
             IFNULL(SUM(trb.`recharge_amount`), 0)     totalRechargeAmount,
             IFNULL(SUM(trb.`verification_amount`), 0) totalVerificationAmount,
             (SELECT IFNULL(SUM(tdca.`apply_cost`), 0)
              FROM `t_reserve_balance` trb
                       INNER JOIN t_driver_cost_apply tdca ON tdca.staff_id = trb.driver_id
                  AND tdca.valid = 1
                  AND tdca.audit_status IN (0, 2)
                  AND staff_property = 1
              WHERE trb.valid = 1) totalAwaitVerificationAmount
      FROM `t_reserve_balance` trb
      WHERE trb.valid = 1;
    </select>

    <select id="selectSearchList" resultType="com.logistics.tms.controller.reservebalance.response.DriverReserveBalanceListResponseModel">
        SELECT
        tsb.id driverId,
        tsb.staff_property driverProperty,
        tsb.name driverName,
        tsb.mobile driverMobile,
        AES_DECRYPT(UNHEX(tda.bank_account), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') bankAccount,
        trb.id reserveBalanceId,
        trb.balance_amount balanceAmount,
        trb.verification_amount verificationAmount
        FROM t_reserve_balance trb
        INNER JOIN t_driver_account tda ON tda.driver_id = trb.driver_id
            AND tda.valid = 1
        INNER JOIN t_staff_basic tsb ON tsb.id = trb.driver_id
            AND tsb.valid = 1
            <if test="param1.driverName != null and param1.driverName != ''">
                AND (INSTR(tsb.name, #{params.driverName})
                OR INSTR(tsb.mobile, #{params.driverName}))
            </if>
            <if test="params.staffProperty != null">
                AND tsb.staff_property = #{params.staffProperty}
            </if>
        WHERE trb.valid = 1
        ORDER BY
        <trim>
            <if test="params.balanceSort != null">
                <choose>
                    <when test="param1.balanceSort == 1">
                        trb.balance_amount DESC,
                    </when>
                    <otherwise>
                        trb.balance_amount ASC,
                    </otherwise>
                </choose>
            </if>
            <if test="params.verificationSort != null">
                <choose>
                    <when test="params.verificationSort == 1">
                        trb.verification_amount DESC,
                    </when>
                    <otherwise>
                        trb.verification_amount ASC,
                    </otherwise>
                </choose>
            </if>
        </trim>
        trb.last_modified_time DESC
    </select>

    <select id="selectDriverNameById" resultType="java.lang.String">
        SELECT tsb.name
        FROM t_reserve_balance trb
        LEFT JOIN t_staff_basic tsb ON tsb.id = trb.driver_id
        WHERE trb.id = #{id}
    </select>

    <select id="selectOneByDriverId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_reserve_balance
        WHERE valid = 1
        AND driver_id = #{driverId}
    </select>


    <select id="selectBalanceByDriverIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_reserve_balance
        WHERE valid = 1
        AND driver_id in
        <foreach collection="driverIds" item="driverId" open="(" separator="," close=")">
            #{driverId,jdbcType=BIGINT}
        </foreach>
    </select>

    <update id="updateBalanceById">
        UPDATE t_reserve_balance
        <set>
            <if test="updateModel.amount != null">
                <choose>
                    <when test="updateModel.isRecharge">
                        balance_amount = balance_amount + #{updateModel.amount},
                        recharge_amount = recharge_amount + #{updateModel.amount},
                    </when>
                    <otherwise>
                        balance_amount = balance_amount - #{updateModel.amount},
                        verification_amount = verification_amount + #{updateModel.amount},
                    </otherwise>
                </choose>
            </if>
            <if test="updateModel.lastModifiedBy != null and updateModel.lastModifiedBy != ''">
                last_modified_by = #{updateModel.lastModifiedBy},
            </if>
            <if test="updateModel.lastModifiedTime != null">
                last_modified_time = #{updateModel.lastModifiedTime},
            </if>
        </set>
        WHERE valid = 1
        AND id = #{updateModel.id}
    </update>

    <insert id="createBalance" parameterType="com.logistics.tms.entity.TReserveBalance" useGeneratedKeys="true" keyProperty="id">
        insert into t_reserve_balance
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="driverId != null" >
                driver_id,
            </if>
            <if test="balanceAmount != null" >
                balance_amount,
            </if>
            <if test="rechargeAmount != null" >
                recharge_amount,
            </if>
            <if test="verificationAmount != null" >
                verification_amount,
            </if>
            <if test="createdBy != null" >
                created_by,
            </if>
            <if test="createdTime != null" >
                created_time,
            </if>
            <if test="lastModifiedBy != null" >
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null" >
                last_modified_time,
            </if>
            <if test="valid != null" >
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="driverId != null" >
                #{driverId,jdbcType=BIGINT},
            </if>
            <if test="balanceAmount != null" >
                #{balanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="rechargeAmount != null" >
                #{rechargeAmount,jdbcType=DECIMAL},
            </if>
            <if test="verificationAmount != null" >
                #{verificationAmount,jdbcType=DECIMAL},
            </if>
            <if test="createdBy != null" >
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null" >
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null" >
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null" >
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
</mapper>