package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/12/16
*/
@Data
public class TCarrierOrderLocation extends BaseEntity {
    /**
    * 运单id
    */
    @ApiModelProperty("运单id")
    private Long carrierOrderId;

    /**
    * 定位类型：0 卸货，1 提货
    */
    @ApiModelProperty("定位类型：0 卸货，1 提货")
    private Integer locationType;

    /**
    * 定位位置
    */
    @ApiModelProperty("定位位置")
    private String location;

    /**
    * 经度
    */
    @ApiModelProperty("经度")
    private String longitude;

    /**
    * 纬度
    */
    @ApiModelProperty("纬度")
    private String latitude;
}