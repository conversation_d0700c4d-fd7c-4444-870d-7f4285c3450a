package com.logistics.tms.controller.website.demand;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.website.demand.DemandSourceBiz;
import com.logistics.tms.controller.website.demand.request.AddDemandSourceRequestModel;
import com.logistics.tms.controller.website.demand.request.DemandSourceListRequestModel;
import com.logistics.tms.controller.website.demand.response.DemandSourceListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: wjf
 * @date: 2024/3/15 9:58
 */
@Api(value = "云途官网-需求单")
@RestController
public class DemandSourceController {

    @Resource
    private DemandSourceBiz demandSourceBiz;

    /**
     * 货源列表接口
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "货源列表接口")
    @PostMapping(value = "/service/demandSource/searchList")
    public Result<PageInfo<DemandSourceListResponseModel>> searchList(@RequestBody DemandSourceListRequestModel requestModel) {
        return Result.success(demandSourceBiz.searchList(requestModel));
    }

    /**
     * 发布货源
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "发布货源")
    @PostMapping(value = "/service/demandSource/add")
    public Result<Boolean> add(@RequestBody AddDemandSourceRequestModel requestModel) {
        demandSourceBiz.add(requestModel);
        return Result.success(true);
    }
}
