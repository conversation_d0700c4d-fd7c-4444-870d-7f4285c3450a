package com.logistics.management.webapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class LoadGoodsForYeloLifeRequestCodeDto {

    @ApiModelProperty(value = "code", required = true)
    private String yeloCode;

    @ApiModelProperty(value = "重量 对应实提实卸实签", required = true)
    private String weight;

    @ApiModelProperty(value = "单位 1.kg", required = true)
    private String unit;
}
