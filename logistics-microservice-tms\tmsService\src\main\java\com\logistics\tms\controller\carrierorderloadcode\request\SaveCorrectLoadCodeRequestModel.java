package com.logistics.tms.controller.carrierorderloadcode.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SaveCorrectLoadCodeRequestModel {

    @ApiModelProperty(value = "产品编码", required = true)
    private String productCode;

    @ApiModelProperty(value = "运单Id", required = true)
    private Long carrierOrderId;


}
