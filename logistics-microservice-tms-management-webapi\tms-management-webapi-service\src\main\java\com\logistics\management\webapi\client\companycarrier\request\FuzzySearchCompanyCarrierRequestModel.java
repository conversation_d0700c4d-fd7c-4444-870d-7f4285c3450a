package com.logistics.management.webapi.client.companycarrier.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/9/27 17:55
 */
@Data
public class FuzzySearchCompanyCarrierRequestModel {

    @ApiModelProperty("公司名字")
    private String companyName;

    @ApiModelProperty("车主类型 1公司 2 个人")
    private Integer companyType;

    @ApiModelProperty(value = "公司级别 1 云途 2 二级承运商")
    private Integer level;

    /**
     * 是否加入黑名单：0 否，1 是
     */
    @ApiModelProperty("是否加入黑名单：0 否，1 是")
    private Integer ifAddBlacklist;
}
