<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TReservationOrderItemMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TReservationOrderItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="reservation_order_id" jdbcType="BIGINT" property="reservationOrderId" />
    <result column="carrier_order_id" jdbcType="BIGINT" property="carrierOrderId" />
    <result column="carrier_order_code" jdbcType="VARCHAR" property="carrierOrderCode" />
    <result column="expect_amount" jdbcType="DECIMAL" property="expectAmount" />
    <result column="demand_order_entrust_type" jdbcType="INTEGER" property="demandOrderEntrustType" />
    <result column="expected_time" jdbcType="TIMESTAMP" property="expectedTime" />
    <result column="enabled" jdbcType="INTEGER" property="enabled" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, reservation_order_id, carrier_order_id, carrier_order_code, expect_amount, demand_order_entrust_type, 
    expected_time, enabled, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_reservation_order_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_reservation_order_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TReservationOrderItem">
    insert into t_reservation_order_item (id, reservation_order_id, carrier_order_id, 
      carrier_order_code, expect_amount, demand_order_entrust_type, 
      expected_time, enabled, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{reservationOrderId,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, 
      #{carrierOrderCode,jdbcType=VARCHAR}, #{expectAmount,jdbcType=DECIMAL}, #{demandOrderEntrustType,jdbcType=INTEGER}, 
      #{expectedTime,jdbcType=TIMESTAMP}, #{enabled,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TReservationOrderItem">
    insert into t_reservation_order_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reservationOrderId != null">
        reservation_order_id,
      </if>
      <if test="carrierOrderId != null">
        carrier_order_id,
      </if>
      <if test="carrierOrderCode != null">
        carrier_order_code,
      </if>
      <if test="expectAmount != null">
        expect_amount,
      </if>
      <if test="demandOrderEntrustType != null">
        demand_order_entrust_type,
      </if>
      <if test="expectedTime != null">
        expected_time,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reservationOrderId != null">
        #{reservationOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null">
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null">
        #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="expectAmount != null">
        #{expectAmount,jdbcType=DECIMAL},
      </if>
      <if test="demandOrderEntrustType != null">
        #{demandOrderEntrustType,jdbcType=INTEGER},
      </if>
      <if test="expectedTime != null">
        #{expectedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TReservationOrderItem">
    update t_reservation_order_item
    <set>
      <if test="reservationOrderId != null">
        reservation_order_id = #{reservationOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null">
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderCode != null">
        carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="expectAmount != null">
        expect_amount = #{expectAmount,jdbcType=DECIMAL},
      </if>
      <if test="demandOrderEntrustType != null">
        demand_order_entrust_type = #{demandOrderEntrustType,jdbcType=INTEGER},
      </if>
      <if test="expectedTime != null">
        expected_time = #{expectedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TReservationOrderItem">
    update t_reservation_order_item
    set reservation_order_id = #{reservationOrderId,jdbcType=BIGINT},
      carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      carrier_order_code = #{carrierOrderCode,jdbcType=VARCHAR},
      expect_amount = #{expectAmount,jdbcType=DECIMAL},
      demand_order_entrust_type = #{demandOrderEntrustType,jdbcType=INTEGER},
      expected_time = #{expectedTime,jdbcType=TIMESTAMP},
      enabled = #{enabled,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>