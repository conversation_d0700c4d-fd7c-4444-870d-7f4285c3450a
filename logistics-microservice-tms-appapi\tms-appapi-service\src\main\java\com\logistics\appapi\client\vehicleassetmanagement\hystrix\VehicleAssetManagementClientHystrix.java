package com.logistics.appapi.client.vehicleassetmanagement.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.vehicleassetmanagement.VehicleAssetManagementClient;
import com.logistics.appapi.client.vehicleassetmanagement.request.SearchVehicleByPropertyRequestModel;
import com.logistics.appapi.client.vehicleassetmanagement.response.SearchVehicleByPropertyResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/11 14:36
 */
@Component
public class VehicleAssetManagementClientHystrix implements VehicleAssetManagementClient {
    @Override
    public Result<PageInfo<SearchVehicleByPropertyResponseModel>> searchVehicleByProperty(SearchVehicleByPropertyRequestModel requestModel) {
        return Result.timeout();
    }
}
