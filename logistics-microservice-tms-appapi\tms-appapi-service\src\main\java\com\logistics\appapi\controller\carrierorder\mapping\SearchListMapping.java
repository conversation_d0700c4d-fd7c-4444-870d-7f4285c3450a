package com.logistics.appapi.controller.carrierorder.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.*;
import com.logistics.appapi.client.carrierorder.response.CarrierOrderDetailGoodsInfoAppModel;
import com.logistics.appapi.client.carrierorder.response.SearchCarrierOrderListAppResponseModel;
import com.logistics.appapi.controller.carrierorder.response.SearchCarrierOrderListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.util.Calendar;

/**
 * @author: wjf
 * @date: 2018/10/19 12:46
 */
public class SearchListMapping extends MapperMapping<SearchCarrierOrderListAppResponseModel,SearchCarrierOrderListResponseDto> {
    @Override
    public void configure() {
        SearchCarrierOrderListAppResponseModel model = getSource();
        SearchCarrierOrderListResponseDto dto = getDestination();
        if (null != model){
            //时间的转换
            Calendar todayC = Calendar.getInstance();
            todayC.set(Calendar.HOUR_OF_DAY,0);
            todayC.set(Calendar.MINUTE,0);
            todayC.set(Calendar.SECOND,0);
            todayC.set(Calendar.MILLISECOND,0);
            Calendar tomorrowC = Calendar.getInstance();
            tomorrowC.add(Calendar.DAY_OF_MONTH,1);
            tomorrowC.set(Calendar.HOUR_OF_DAY,0);
            tomorrowC.set(Calendar.MINUTE,0);
            tomorrowC.set(Calendar.SECOND,0);
            tomorrowC.set(Calendar.MILLISECOND,0);
            Calendar tomorrowAfterC = Calendar.getInstance();
            tomorrowAfterC.add(Calendar.DAY_OF_MONTH,2);
            tomorrowAfterC.set(Calendar.HOUR_OF_DAY,0);
            tomorrowAfterC.set(Calendar.MINUTE,0);
            tomorrowAfterC.set(Calendar.SECOND,0);
            tomorrowAfterC.set(Calendar.MILLISECOND,0);
            Calendar outC = Calendar.getInstance();
            outC.add(Calendar.DAY_OF_MONTH,3);
            outC.set(Calendar.HOUR_OF_DAY,0);
            outC.set(Calendar.MINUTE,0);
            outC.set(Calendar.SECOND,0);
            outC.set(Calendar.MILLISECOND,0);
            if (model.getExpectArrivalTime() != null){
                Calendar expectArrivalTimeC = Calendar.getInstance();
                expectArrivalTimeC.setTime(model.getExpectArrivalTime());
                if (expectArrivalTimeC.compareTo(todayC) >= 0 && expectArrivalTimeC.compareTo(tomorrowC) < 0) {
                    dto.setExpectArrivalTime("今天装");
                } else if (expectArrivalTimeC.compareTo(tomorrowC) >= 0 && expectArrivalTimeC.compareTo(tomorrowAfterC) < 0) {
                    dto.setExpectArrivalTime("明天装");
                } else if (expectArrivalTimeC.compareTo(tomorrowAfterC) >= 0 && expectArrivalTimeC.compareTo(outC) < 0) {
                    dto.setExpectArrivalTime("后天装");
                } else {
                    dto.setExpectArrivalTime(DateUtils.dateToString(model.getExpectArrivalTime(),"MM/dd")+"装");
                }
            }else if (model.getExpectedUnloadTime() != null){
                Calendar expectedUnloadTimeC = Calendar.getInstance();
                expectedUnloadTimeC.setTime(model.getExpectedUnloadTime());
                if (expectedUnloadTimeC.compareTo(todayC) >= 0 && expectedUnloadTimeC.compareTo(tomorrowC) < 0) {
                    dto.setExpectArrivalTime("今天卸");
                } else if (expectedUnloadTimeC.compareTo(tomorrowC) >= 0 && expectedUnloadTimeC.compareTo(tomorrowAfterC) < 0) {
                    dto.setExpectArrivalTime("明天卸");
                } else if (expectedUnloadTimeC.compareTo(tomorrowAfterC) >= 0 && expectedUnloadTimeC.compareTo(outC) < 0) {
                    dto.setExpectArrivalTime("后天卸");
                } else {
                    dto.setExpectArrivalTime(DateUtils.dateToString(model.getExpectedUnloadTime(),"MM/dd")+"卸");
                }
            }else {
                dto.setExpectArrivalTime("");
            }
            //货物信息的转换
            if (ListUtils.isNotEmpty(model.getGoodList())){
                String unit = GoodsUnitEnum.getEnum(model.getGoodsUnit()).getUnit();
                BigDecimal totalAmount = new BigDecimal(0);
                BigDecimal expectAmount = new BigDecimal(0);
                BigDecimal loadAmount = new BigDecimal(0);
                BigDecimal unloadAmount = new BigDecimal(0);
                BigDecimal signAmount = new BigDecimal(0);
                BigDecimal expectCapacity = new BigDecimal(0);
                BigDecimal totalVolume = new BigDecimal(0);
                BigDecimal loadCapacity = new BigDecimal(0);
                BigDecimal unloadCapacity = new BigDecimal(0);
                BigDecimal signAmountCapacity = new BigDecimal(0);
                for (CarrierOrderDetailGoodsInfoAppModel good:model.getGoodList()) {
                    expectAmount = expectAmount.add(good.getExpectAmount());
                    loadAmount = loadAmount.add(good.getLoadAmount());
                    unloadAmount = unloadAmount.add(good.getUnloadAmount());
                    signAmount = signAmount.add(good.getSignAmount());
                    BigDecimal capacity = ConverterUtils.toBigDecimal(good.getLength()).multiply(ConverterUtils.toBigDecimal(good.getWidth())).multiply(ConverterUtils.toBigDecimal(good.getHeight()));
                    expectCapacity = expectCapacity.add(capacity.multiply(ConverterUtils.toBigDecimal(good.getExpectAmount())).divide(CommonConstant.CUBIC_MILLIMETER_TO_CUBIC_METER,3,BigDecimal.ROUND_HALF_UP));
                    loadCapacity = loadCapacity.add(capacity.multiply(ConverterUtils.toBigDecimal(good.getLoadAmount())).divide(CommonConstant.CUBIC_MILLIMETER_TO_CUBIC_METER,3,BigDecimal.ROUND_HALF_UP));
                    unloadCapacity = unloadCapacity.add(capacity.multiply(ConverterUtils.toBigDecimal(good.getUnloadAmount())).divide(CommonConstant.CUBIC_MILLIMETER_TO_CUBIC_METER,3,BigDecimal.ROUND_HALF_UP));
                    signAmountCapacity = signAmountCapacity.add(capacity.multiply(ConverterUtils.toBigDecimal(good.getSignAmount())).divide(CommonConstant.CUBIC_MILLIMETER_TO_CUBIC_METER,3,BigDecimal.ROUND_HALF_UP));
                }
                if(ListUtils.isNotEmpty(model.getGoodList())){
                    dto.setGoodsName(model.getGoodList().get(0).getGoodsName());
                }
                if (model.getStatus().equals(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey()) || model.getStatus().equals(CarrierOrderStatusEnum.WAIT_LOAD.getKey())){
                    totalAmount = expectAmount;
                    totalVolume = expectCapacity;
                }else if (model.getStatus().equals(CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey()) || model.getStatus().equals(CarrierOrderStatusEnum.WAIT_UNLOAD.getKey())){
                    totalAmount = loadAmount;
                    totalVolume = loadCapacity;
                }else if (model.getStatus().equals(CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey())){
                    totalAmount = unloadAmount;
                    totalVolume = unloadCapacity;
                }else if (model.getStatus().equals(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey())){
                    totalAmount = signAmount;
                    totalVolume = signAmountCapacity;
                }
                dto.setAmount(totalAmount.stripTrailingZeros().toPlainString()+unit);
                if (model.getGoodsUnit().equals(GoodsUnitEnum.BY_VOLUME.getKey())) {
                    dto.setCapacity(totalVolume.stripTrailingZeros().toPlainString()+ CommonConstant.SQUARE);
                }
            }
            if (CommonConstant.INTEGER_ONE.equals(model.getIfCancel())){
                dto.setStatus(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getKey().toString());
                dto.setStatusLabel(CarrierOrderStatusEnum.CANCEL_CARRIERORDER_STATUS.getValue());
            }else if (CommonConstant.INTEGER_ONE.equals(model.getIfEmpty())){
                dto.setStatus(CarrierOrderStatusEnum.EMPTY.getKey().toString());
                dto.setStatusLabel(CarrierOrderStatusEnum.EMPTY.getValue());
            }else{
                dto.setStatusLabel(CarrierOrderStatusEnum.getEnum(model.getStatus()).getValue());
            }

            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(model.getDemandOrderSource())) {
                //云盘运单
                dto.setCustomerOrderCode("");
            }else if (DemandOrderSourceEnum.SINOPEC.getKey().equals(model.getDemandOrderSource())) {
                //中石化推送的单子
                if (DemandOrderOrderTypeEnum.PUSH.getKey().equals(model.getOrderType())) {
                    dto.setCustomerOrderCode(model.getSinopecOrderNo());
                }
            }else if (DemandOrderSourceEnum.YELO_LIFE.getKey().equals(model.getDemandOrderSource())) {
                //新生单子
                if (CompanyTypeEnum.PERSONAL.getKey().equals(model.getBusinessType())){
                    dto.setCustomerName(model.getCustomerUserName() + " " + model.getCustomerUserMobile());
                }
            }
        }
    }
}
