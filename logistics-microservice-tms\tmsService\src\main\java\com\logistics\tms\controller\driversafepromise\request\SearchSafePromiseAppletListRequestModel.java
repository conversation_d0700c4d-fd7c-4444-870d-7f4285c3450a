package com.logistics.tms.controller.driversafepromise.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/11/18 15:05
 */
@Data
public class SearchSafePromiseAppletListRequestModel extends AbstractPageForm<SearchSafePromiseAppletListRequestModel> {
    @ApiModelProperty("签订状态: 0待签订、1已签订")
    private Integer status;

    @ApiModelProperty(value = "人员ID")
    private Long staffId;
}
