<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDriverPayeeMapper" >
  <select id="getByNameMobileIdentity" resultMap="BaseResultMap">
    select
    tdp.*
    from t_driver_payee tdp
    left join t_bank tb on tb.id = tdp.bank_id and tb.valid = 1
    where tdp.valid = 1
    and tdp.name = #{name,jdbcType=VARCHAR}
    and tdp.bank_card_no = #{bankCardNo,jdbcType=VARCHAR}
    and tdp.identity_no = #{identityNo,jdbcType=VARCHAR}
    and tb.bank_name = #{bankName,jdbcType=VARCHAR}
  </select>

  <select id="selectDriverPayeeListIds" resultType="java.lang.Long">

    select
    DISTINCT  tdp.id
    from t_driver_payee tdp
    left join t_bank tqb on tdp.bank_id = tqb.id and tqb.valid = 1
    where tdp.valid = 1
    <if test="param.auditStatus!=null">
      and tdp.audit_status = #{param.auditStatus,jdbcType=INTEGER}
    </if>
    <if test="param.driverPayee!=null and param.driverPayee!=''">
      and (instr(tdp.name,#{param.driverPayee,jdbcType=VARCHAR}) or instr(tdp.mobile,#{param.driverPayee,jdbcType=VARCHAR}))
    </if>
    <if test="param.bankCardNo!=null and param.bankCardNo!=''">
      and instr(tdp.bank_card_no,#{param.bankCardNo,jdbcType=VARCHAR})
    </if>
    <if test="param.lastModifiedBy!=null and param.lastModifiedBy!=''">
      and instr(tdp.last_modified_by,#{param.lastModifiedBy,jdbcType=VARCHAR})
    </if>
    <if test="param.lastModifiedTimeFrom !=null and param.lastModifiedTimeFrom != ''">
      and tdp.last_modified_time &gt;= DATE_FORMAT(#{param.lastModifiedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
    </if>
    <if test="param.lastModifiedTimeTo !=null and param.lastModifiedTimeTo != ''">
      and tdp.last_modified_time &lt;= DATE_FORMAT(#{param.lastModifiedTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
    </if>
      order by tdp.last_modified_time desc,tdp.id desc

  </select>

  <resultMap id="selectDriverPayeeListByIdsMap" type="com.logistics.tms.api.feign.driverpayee.model.DriverPayeeListResponseModel">
    <id column="driverPayeeId" property="driverPayeeId" jdbcType="BIGINT"/>
    <result column="auditStatus" property="auditStatus" jdbcType="INTEGER"/>
    <result column="name" property="name" jdbcType="VARCHAR"/>
    <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
    <result column="identityNo" property="identityNo" jdbcType="VARCHAR"/>
    <result column="bankCardNo" property="bankCardNo" jdbcType="VARCHAR"/>
    <result column="remark" property="remark" jdbcType="VARCHAR"/>
    <result column="bankName" property="bankName" jdbcType="VARCHAR"/>
    <result column="lastModifiedBy" property="lastModifiedBy" jdbcType="VARCHAR"/>
    <result column="lastModifiedTime" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
    <collection property="imageList" ofType="com.logistics.tms.api.feign.common.model.CertificatePictureModel">
      <result column="objectId" property="objectId" jdbcType="BIGINT"/>
      <result column="fileTypeName" property="fileTypeName" jdbcType="VARCHAR"/>
      <result column="filePath" property="filePath" jdbcType="VARCHAR"/>
      <result column="uploadUserName" property="uploadUserName" jdbcType="VARCHAR"/>
      <result column="uploadTime" property="uploadTime" jdbcType="VARCHAR"/>
    </collection>
  </resultMap>

  <select id="selectDriverPayeeListByIds" resultMap="selectDriverPayeeListByIdsMap">
    select
    tdp.id as driverPayeeId,
    tdp.audit_status as auditStatus,
    tdp.name,
    tdp.mobile,
    tdp.identity_no as identityNo,
    tdp.bank_card_no as bankCardNo,
    tdp.remark,
    tqb.bank_name as bankName,
    tdp.last_modified_by as lastModifiedBy,
    tdp.last_modified_time as lastModifiedTime,
    tqp.id as objectId,
    tqp.file_type_name as fileTypeName,
    tqp.file_path as filePath,
    tqp.upload_user_name as uploadUserName,
    tqp.upload_time as uploadTime
    from t_driver_payee tdp
    left join t_bank tqb on tdp.bank_id = tqb.id and tqb.valid = 1
    left join t_certification_pictures tqp on tdp.id = tqp.object_id and object_type = 17 and tqp.valid = 1
    where tdp.id in (${ids})
    order by tdp.last_modified_time desc,tdp.id desc
  </select>
  
  <resultMap id="getDriverPayeeDetailMap"  type="com.logistics.tms.api.feign.driverpayee.model.DriverPayeeDetailResponseModel">
    <id column="driverPayeeId" property="driverPayeeId" jdbcType="BIGINT"/>
    <result column="name" property="name" jdbcType="VARCHAR"/>
    <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
    <result column="identityNo" property="identityNo" jdbcType="VARCHAR"/>
    <result column="bankCardNo" property="bankCardNo" jdbcType="VARCHAR"/>
    <result column="remark" property="remark" jdbcType="VARCHAR"/>
    <result column="bankId" property="bankId" jdbcType="BIGINT"/>
    <result column="bankName" property="bankName" jdbcType="VARCHAR"/>
    <collection property="imageList" ofType="com.logistics.tms.api.feign.common.model.CertificatePictureModel">
      <result column="objectId" property="objectId" jdbcType="BIGINT"/>
      <result column="fileTypeName" property="fileTypeName" jdbcType="VARCHAR"/>
      <result column="filePath" property="filePath" jdbcType="VARCHAR"/>
      <result column="fileType" property="fileType" jdbcType="INTEGER"/>
      <result column="uploadUserName" property="uploadUserName" jdbcType="VARCHAR"/>
      <result column="uploadTime" property="uploadTime" jdbcType="VARCHAR"/>
    </collection>
  </resultMap>


  <select id="getDriverPayeeDetail" resultMap="getDriverPayeeDetailMap">
     select
    tdp.id as driverPayeeId,
    tdp.audit_status as auditStatus,
    tdp.name,
    tdp.mobile,
    tdp.identity_no as identityNo,
    tdp.bank_card_no as bankCardNo,
    tdp.remark,
    tdp.bank_id as bankId,
    tqb.bank_name as bankName,
    tqp.id as objectId,
    tqp.file_type_name as fileTypeName,
    tqp.file_type as fileType,
    tqp.file_path as filePath,
    tqp.upload_user_name as uploadUserName,
    tqp.upload_time as uploadTime
    from t_driver_payee tdp
    left join t_bank tqb on tdp.bank_id = tqb.id and tqb.valid = 1
    left join t_certification_pictures tqp on tdp.id = tqp.object_id and object_type = 17 and tqp.valid = 1
    where tdp.id = #{driverPayeeId,jdbcType=BIGINT}
  </select>

  <select id="getByIdentity" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_driver_payee
    where valid = 1 and identity_no = #{identityNo,jdbcType=VARCHAR} and audit_status >=0
    limit 1
  </select>

  <select id="selectDriverPayeeByIds" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    from t_driver_payee
    where valid = 1 and id in (${driverPayeeIds})
  </select>

  <update id="batchUpdate" >
    <foreach collection="list" item="item" separator=";">
      update t_driver_payee
      <set >
        <if test="item.name != null" >
          name = #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.mobile != null" >
          mobile = #{item.mobile,jdbcType=VARCHAR},
        </if>
        <if test="item.identityNo != null" >
          identity_no = #{item.identityNo,jdbcType=VARCHAR},
        </if>
        <if test="item.bankId != null" >
          bank_id = #{item.bankId,jdbcType=BIGINT},
        </if>
        <if test="item.bankCardNo != null" >
          bank_card_no = #{item.bankCardNo,jdbcType=VARCHAR},
        </if>
        <if test="item.auditStatus != null" >
          audit_status = #{item.auditStatus,jdbcType=INTEGER},
        </if>
        <if test="item.auditTime != null" >
          audit_time = #{item.auditTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.auditorId != null" >
          auditor_id = #{item.auditorId,jdbcType=BIGINT},
        </if>
        <if test="item.auditorName != null" >
          auditor_name = #{item.auditorName,jdbcType=VARCHAR},
        </if>
        <if test="item.auditReason != null" >
          audit_reason = #{item.auditReason,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null" >
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdTime != null" >
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.lastModifiedBy != null" >
          last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.lastModifiedTime != null" >
          last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.valid != null" >
          valid = #{item.valid,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="findByName" resultMap="BaseResultMap">

    select <include refid="Base_Column_List"/>
    from t_driver_payee
    where valid = 1
    and name = #{name,jdbcType=VARCHAR}
    and audit_status != -2
  </select>

    <select id="exportDriverPayeeList" resultType="com.logistics.tms.api.feign.driverpayee.model.ExportDriverPayeeListResponseModel">

        select
        tdp.name,
        tdp.mobile,
        tdp.identity_no as identityNo,
        tdp.bank_card_no as bankCardNo,
        tdp.remark,
        tqb.bank_name as bankName
        from t_driver_payee tdp
        left join t_bank tqb on tdp.bank_id = tqb.id and tqb.valid = 1
        where tdp.valid = 1
        <if test="param.auditStatus!=null">
            and tdp.audit_status = #{param.auditStatus,jdbcType=INTEGER}
        </if>
        <if test="param.driverPayee!=null and param.driverPayee!=''">
            and (instr(tdp.name,#{param.driverPayee,jdbcType=VARCHAR}) or instr(tdp.mobile,#{param.driverPayee,jdbcType=VARCHAR}))
        </if>
        <if test="param.bankCardNo!=null and param.bankCardNo!=''">
            and instr(tdp.bank_card_no,#{param.bankCardNo,jdbcType=VARCHAR})
        </if>
        <if test="param.lastModifiedBy!=null and param.lastModifiedBy!=''">
            and instr(tdp.last_modified_by,#{param.lastModifiedBy,jdbcType=VARCHAR})
        </if>
        <if test="param.lastModifiedTimeFrom !=null and param.lastModifiedTimeFrom != ''">
            and tdp.last_modified_time &gt;= DATE_FORMAT(#{param.lastModifiedTimeFrom,jdbcType=VARCHAR},'%Y-%m-%d 00:00:00')
        </if>
        <if test="param.lastModifiedTimeTo !=null and param.lastModifiedTimeTo != ''">
            and tdp.last_modified_time &lt;= DATE_FORMAT(#{param.lastModifiedTimeTo,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        order by tdp.last_modified_time desc,tdp.id desc
    </select>

  <select id="searchAuditedDriverPayees" resultType="com.logistics.tms.api.feign.driverpayee.model.SearchDriverPayeesResponseModel">
    select
    tdp.id as driverPayeeId,
    tdp.name,
    tdp.mobile,
    tdp.bank_card_no as bankCardNo,
    tqb.bank_name as bankName
    from t_driver_payee tdp
    left join t_bank tqb on tdp.bank_id = tqb.id and tqb.valid = 1
    where tdp.valid = 1 and tdp.audit_status = 1
    <if test="driverPayee!=null and driverPayee!=''">
      and ( instr(name,#{driverPayee,jdbcType=VARCHAR})
      OR instr(mobile,#{driverPayee,jdbcType=VARCHAR})
      OR instr(bank_card_no,#{driverPayee,jdbcType=VARCHAR})
      OR instr(bank_name,#{driverPayee,jdbcType=VARCHAR}))
    </if>
    order by tdp.created_time desc,tdp.id desc
  </select>
</mapper>