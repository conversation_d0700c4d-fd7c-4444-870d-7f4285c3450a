package com.logistics.appapi.controller.leave.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 请假记录item
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Data
public class LeaveApplyListItem {

	@ApiModelProperty("请假申请ID")
	private String leaveApplyId = "";

	@ApiModelProperty("请假申请审核状态,审核状态: 0 待审核，1 已审核，2 已驳回，3 已撤销")
	private String leaveAuditStatus = "";

	@ApiModelProperty("请假申请审核状态文本")
	private String leaveAuditStatusLabel = "";

	@ApiModelProperty("申请人")
	private String leaveApplyStaff = "";

	@ApiModelProperty("请假类型, 1 事假")
	private String leaveType = "";

	@ApiModelProperty("请假类型文本")
	private String leaveTypeLabel = "";

	@ApiModelProperty("请假申请开始时间 (年-月-日 上午/下午)")
	private String leaveStartTime = "";

	@ApiModelProperty("请假申请结束时间 (年-月-日 上午/下午)")
	private String leaveEndTime = "";
}
