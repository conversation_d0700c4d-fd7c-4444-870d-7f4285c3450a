package com.logistics.tms.controller.reservationorder.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/19 09:48
 */
@Data
public class WaitReservationDetailResponseModel {

    /**
     * 预约类型：1 提货，2 卸货
     */
    private Integer reservationType;

    /**
     * 发货地/收货地
     */
    private String address;

    /**
     * 仓库名称
     */
    private String warehouse;

    /**
     * 运单列表
     */
    private List<WaitReservationCarrierOrderListResponseModel> orderList=new ArrayList<>();

    /**
     * 预计里程数（公里）
     */
    private BigDecimal expectMileage = new BigDecimal("0.00");

}
