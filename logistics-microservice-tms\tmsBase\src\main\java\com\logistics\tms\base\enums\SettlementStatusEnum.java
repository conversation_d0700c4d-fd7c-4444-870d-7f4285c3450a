package com.logistics.tms.base.enums;

/**
 * @Author: sj
 * @Date: 2019/10/8 15:21
 */
public enum SettlementStatusEnum {
    WAIT(0, "待结算"),
    PART(1, "部分结算"),
    FINISH(2,"已结算"),
    ;
    private Integer key;
    private String value;

    SettlementStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
