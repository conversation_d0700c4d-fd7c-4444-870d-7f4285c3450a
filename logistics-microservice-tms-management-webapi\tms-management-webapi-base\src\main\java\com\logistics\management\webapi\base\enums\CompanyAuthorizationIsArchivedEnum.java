package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CompanyAuthorizationIsArchivedEnum {

    DEFAULT(-1, ""),
    NOT_ARCHIVED(0, "否"),
    ARCHIVED(1, "是"),
    ;

    private Integer key;
    private String value;

    public static CompanyAuthorizationIsArchivedEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
