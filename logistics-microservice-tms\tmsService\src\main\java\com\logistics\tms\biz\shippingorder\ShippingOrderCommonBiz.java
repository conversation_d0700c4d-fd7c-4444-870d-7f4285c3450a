package com.logistics.tms.biz.shippingorder;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandpayment.DemandPaymentBiz;
import com.logistics.tms.biz.shippingorder.model.ShippingOrderItemSqlConditionModel;
import com.logistics.tms.controller.shippingorder.request.GetShippingOrderDetailRequestModel;
import com.logistics.tms.controller.shippingorder.request.SearchShippingOrderListRequestModel;
import com.logistics.tms.controller.shippingorder.request.ShippingOrderAuditRequestModel;
import com.logistics.tms.controller.shippingorder.request.ShippingOrderReQuoteRequestModel;
import com.logistics.tms.controller.shippingorder.response.GetShippingOrderDetailResponseModel;
import com.logistics.tms.controller.shippingorder.response.GetShippingOrderRoutePlanResponseModel;
import com.logistics.tms.controller.shippingorder.response.SearchShippingOrderListResponseModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2024/8/6 14:39
 */
@Service
public class ShippingOrderCommonBiz {

    @Resource
    private TShippingOrderMapper tShippingOrderMapper;
    @Resource
    private TShippingOrderItemMapper tShippingOrderItemMapper;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Resource
    private TPaymentMapper tPaymentMapper;
    @Resource
    private DemandPaymentBiz demandPaymentBiz;



    /**
     * 校验并且计算零担价格 运单操作节点
     */
    @Transactional
    public void checkAndGenerateShippingFeeFromCarrierOrderAction(List<Long> carrierOrderIds) {
        if (CollectionUtil.isEmpty(carrierOrderIds)) {
            return;
        }
        //查询入参运单id对应的零担运输单明细
        ShippingOrderItemSqlConditionModel sqlConditionModel = new ShippingOrderItemSqlConditionModel();
        sqlConditionModel.setCarrierOrderIds(carrierOrderIds);
        List<TShippingOrderItem> tShippingOrderItems = tShippingOrderItemMapper.selectByCondition(sqlConditionModel);
        if (CollectionUtil.isEmpty(tShippingOrderItems)) {
            return;
        }
        List<Long> shippingOrderIds = tShippingOrderItems.stream().map(TShippingOrderItem::getShippingOrderId).distinct().collect(Collectors.toList());
        this.checkAndGenerateShippingFee(shippingOrderIds, carrierOrderIds);

    }

    /**
     * 校验并且计算零担价格
     */
    @Transactional
    public void checkAndGenerateShippingFee(List<Long> shippingOrderIds, List<Long> excludeCheckCarrierOrderIds) {
        if (CollectionUtil.isEmpty(shippingOrderIds)) {
            return;
        }
        //查询零担运输单
        List<TShippingOrder> tShippingOrders = tShippingOrderMapper.selectBatchIds(shippingOrderIds);
        if (CollectionUtil.isEmpty(tShippingOrders)) {
            throw new BizException(CarrierDataExceptionEnum.SHIPPING_ORDER_NOT_EXIST);
        }
        //过滤已审核的
        tShippingOrders = tShippingOrders.stream().filter(tShippingOrder -> ShippingOrderStatusEnum.AUDIT_THROUGH.getKey().equals(tShippingOrder.getStatus())).collect(Collectors.toList());

        //查询零担运输单明细
        ShippingOrderItemSqlConditionModel selectAllSqlConditionModel = new ShippingOrderItemSqlConditionModel();
        selectAllSqlConditionModel.setShippingOrderIds(shippingOrderIds);
        List<TShippingOrderItem> allShippingOrderItems = tShippingOrderItemMapper.selectByCondition(selectAllSqlConditionModel);
        if (CollectionUtil.isEmpty(allShippingOrderItems)) {
            throw new BizException(CarrierDataExceptionEnum.SHIPPING_ORDER_ITEM_NOT_EXIST);
        }
        List<Long> allCarrierOrderIds = allShippingOrderItems.stream().map(TShippingOrderItem::getCarrierOrderId).collect(Collectors.toList());
//        List<Long> allDemandOrderIds = allShippingOrderItems.stream().map(TShippingOrderItem::getDemandOrderId).distinct().collect(Collectors.toList());

        //查询对应全部的运单
        List<TCarrierOrder> tCarrierOrders = tCarrierOrderMapper.selectCarrierOrdersByIds(StringUtils.listToString(allCarrierOrderIds, ','));
        if (CollectionUtil.isEmpty(tCarrierOrders)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //查询 运单应付承运方费用结算表
        List<TPayment> tPayments = tPaymentMapper.getByCarrierOrderIds(StringUtils.listToString(allCarrierOrderIds, ','));

        //更新运单零担费用
        List<Long> effectedDemandOrderIds = this.setCarrierOrderShippingFee(tShippingOrders, allShippingOrderItems, tCarrierOrders, tPayments, excludeCheckCarrierOrderIds);

        //重新计算需求单的结算费用
        demandPaymentBiz.refreshDemandCarrierPriceForShippingOrder(effectedDemandOrderIds);
    }

    /**
     * 更新运单零担费用
     */
    private List<Long> setCarrierOrderShippingFee(List<TShippingOrder> tShippingOrders, List<TShippingOrderItem> allShippingOrderItems, List<TCarrierOrder> tCarrierOrders,
                                            List<TPayment> tPayments, List<Long> excludeCheckCarrierOrderIds) {

        //把入参集合变成map映射
        Map<Long, List<TShippingOrderItem>> allShippingOrderItemsGroupMap = allShippingOrderItems.stream().collect(Collectors.groupingBy(TShippingOrderItem::getShippingOrderId));
        Map<Long, TCarrierOrder> tCarrierOrdersMap = tCarrierOrders.stream().collect(Collectors.toMap(TCarrierOrder::getId, Function.identity()));
        Map<Long, TPayment> tPaymentsMap = tPayments.stream().collect(Collectors.toMap(TPayment::getCarrierOrderId, Function.identity()));


        List<TCarrierOrder> updateCarrierOrders = new ArrayList<>();
        List<TPayment> updatePayments = new ArrayList<>();
        Set<Long> effectedDemandOrderIds = new LinkedHashSet<>();

        for (TShippingOrder tShippingOrder : tShippingOrders) {

            List<TShippingOrderItem> tShippingOrderItems = allShippingOrderItemsGroupMap.getOrDefault(tShippingOrder.getId(), new ArrayList<>());
            if (CollectionUtil.isEmpty(tShippingOrderItems)) {
                continue;
            }

            //对应下带运单
            List<TCarrierOrder> carrierOrders = tShippingOrderItems.stream().map(tShippingOrderItem -> tCarrierOrdersMap.get(tShippingOrderItem.getCarrierOrderId())).collect(Collectors.toList());

            //判断对应运单是否都是 (已签收/放空/取消) 不是就continue (排除需要过滤校验的运单)
            boolean allMatchStateFinal = carrierOrders.stream()
                    .filter(carrierOrder -> CollectionUtil.isEmpty(excludeCheckCarrierOrderIds) || !excludeCheckCarrierOrderIds.contains(carrierOrder.getId()))
                    .allMatch(carrierOrder -> CommonConstant.INTEGER_ONE.equals(carrierOrder.getIfEmpty()) || CommonConstant.INTEGER_ONE.equals(carrierOrder.getIfCancel()) || CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(carrierOrder.getStatus()));
            if (!allMatchStateFinal) {
                continue;
            }

            //取 预提数量(全部) 和 签收数量(全部) 的最大值  用于后面计算
            BigDecimal totalExpectAmount = tShippingOrder.getExpectAmount();
            BigDecimal totalSignAmount = carrierOrders.stream().map(TCarrierOrder::getSignAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal maxTotalAmount = totalExpectAmount.max(totalSignAmount);

            //整车费用+串点费用 = 合计车主费用
            BigDecimal totalCarrierAmount = tShippingOrder.getCarrierFreight().add(tShippingOrder.getCrossPointFee());

            //签收数量>=预提数量，运单/实际车主费用=（合计车主费用/（零担运输单签收数量数量合计））*运单签收数量；
            //签收数量<预提数量，运单/实际车主费用=（合计车主费用/（零担运输单预提数量数量合计））*运单签收数量
            for (TShippingOrderItem tShippingOrderItem : tShippingOrderItems) {
                //运单
                TCarrierOrder tCarrierOrder = tCarrierOrdersMap.get(tShippingOrderItem.getCarrierOrderId());
                if (tCarrierOrder == null) {
                    throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
                }
                if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfCancel()) || CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())) {
                    //如果取消/放空了 就不更新费用了 只更新状态为未关联
                    TCarrierOrder updateCarrierOrder = new TCarrierOrder();
                    updateCarrierOrder.setId(tCarrierOrder.getId());
                    updateCarrierOrder.setCarrierSettleStatementStatus(CarrierSettleStatementStatusEnum.NOT_RELATED.getKey());
                    commonBiz.setBaseEntityModify(updateCarrierOrder, BaseContextHandler.getUserName());
                    updateCarrierOrders.add(updateCarrierOrder);
                    continue;
                }
                //运单应付承运方费用结算
                TPayment tPayment = tPaymentsMap.get(tShippingOrderItem.getCarrierOrderId());

                //计算费用
                BigDecimal realFee = totalCarrierAmount.multiply(tCarrierOrder.getSignAmount()).divide(maxTotalAmount, 2, RoundingMode.HALF_UP);

                //更新运单的车主费用
                TCarrierOrder updateCarrierOrder = new TCarrierOrder();
                updateCarrierOrder.setId(tCarrierOrder.getId());
                updateCarrierOrder.setCarrierPrice(realFee);
                updateCarrierOrder.setCarrierPriceType(CarrierPriceTypeEnum.FIXED_PRICE.getKey());
                //改成-2未关联  未关联的状态可以被选中生成对账
                updateCarrierOrder.setCarrierSettleStatementStatus(CarrierSettleStatementStatusEnum.NOT_RELATED.getKey());
                commonBiz.setBaseEntityModify(updateCarrierOrder, BaseContextHandler.getUserName());
                updateCarrierOrders.add(updateCarrierOrder);

                //更新运单结算费用
                if (tPayment != null) {
                    TPayment updatePayment = new TPayment();
                    updatePayment.setId(tPayment.getId());
                    updatePayment.setSettlementCostTotal(realFee);
                    updatePayment.setPriceType(PriceTypeEnum.FIXED_PRICE.getKey());
                    commonBiz.setBaseEntityModify(updatePayment, BaseContextHandler.getUserName());
                    updatePayments.add(updatePayment);
                }

                //获取更新运单费用影响到的需求单id 用于后续重新计算需求单的费用
                effectedDemandOrderIds.add(tCarrierOrder.getDemandOrderId());
            }

        }

        if (CollectionUtil.isNotEmpty(updateCarrierOrders)) {
            tCarrierOrderMapper.batchUpdateCarrierOrders(updateCarrierOrders);
        }
        if (CollectionUtil.isNotEmpty(updatePayments)) {
            tPaymentMapper.batchUpdate(updatePayments);
        }

        //返回更新运单费用影响到的需求单id
        return new ArrayList<>(effectedDemandOrderIds);
    }

}
