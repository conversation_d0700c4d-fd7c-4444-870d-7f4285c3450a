package com.logistics.tms.biz.freight;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.driverpayee.model.ViewLogResponseModel;
import com.logistics.tms.api.feign.freight.model.*;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.demandorder.response.GetDemandOrderInfoByIdsModel;
import com.logistics.tms.controller.demandorder.response.WebDriverAndVehicleResponseModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sj
 * @Date: 2019/12/24 16:50
 */
@Service
public class FreightBiz {
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TFreightMapper tFreightMapper;
    @Autowired
    private TFreightAddressMapper tFreightAddressMapper;
    @Autowired
    private TFreightAddressRuleMapper tFreightAddressRuleMapper;
    @Autowired
    private TFreightAddressRuleMarkupMapper tFreightAddressRuleMarkupMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TCompanyEntrustMapper tCompanyEntrustMapper;
    @Autowired
    private TOperateLogsMapper tOperateLogsMapper;
    @Autowired
    private TDemandOrderMapper tDemandOrderMapper;
    @Autowired
    private TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper;
    @Autowired
    private TWarehouseAddressMapper tWarehouseAddressMapper;

    /**
     * 运价主表列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchFreightListResponseModel> searchList(SearchFreightListRequestModel requestModel){
        List<SearchFreightListResponseModel> searchFreightList = new ArrayList<>();
        if(CommonConstant.INTEGER_ONE.equals(requestModel.getRoleType())){
            requestModel.enablePaging();
            searchFreightList = tFreightMapper.searchEntrustFreightList(requestModel);
        }
        if(CommonConstant.INTEGER_TWO.equals(requestModel.getRoleType())){
            requestModel.enablePaging();
            searchFreightList = tFreightMapper.searchCarrierFreightList(requestModel);
        }
        return new PageInfo(ListUtils.isEmpty(searchFreightList) ? new ArrayList<>() : searchFreightList);
    }

    /**
     * 运价公司信息
     * @return
     */
    public FreightCompanyInfoResponseModel getFreightCompanyInfo(FreightCompanyInfoRequestModel requestModel){
        if(CommonConstant.INTEGER_ONE.equals(requestModel.getRoleType())){
            return tFreightMapper.getEntrustFreightCompanyInfo(requestModel.getFreightId());
        }else if(CommonConstant.INTEGER_TWO.equals(requestModel.getRoleType())){
            return tFreightMapper.getCarrierFreightCompanyInfo(requestModel.getFreightId());
        }else{
            return new FreightCompanyInfoResponseModel();
        }
    }


    /**
     * 添加运价
     * @param requestModel
     */
    @Transactional
    public void addFreight(AddFreightRequestModel requestModel){
        if(CommonConstant.INTEGER_TWO.equals(requestModel.getRoleType())){
            TCompanyCarrier dbTCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(requestModel.getCompanyId());
            if(dbTCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(dbTCompanyCarrier.getValid())){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
            }
        }else{
            TCompanyEntrust dbTCompanyEntrust = tCompanyEntrustMapper.selectByPrimaryKey(requestModel.getCompanyId());
            if(dbTCompanyEntrust == null || IfValidEnum.INVALID.getKey().equals(dbTCompanyEntrust.getValid())){
                throw new BizException(CarrierDataExceptionEnum.COMPANY_ENTRUST_EMPTY);
            }
        }
        TFreight dbTFreight = tFreightMapper.getByCompanyIdAndRoleType(requestModel.getCompanyId(),requestModel.getRoleType());
        if(dbTFreight != null){
            throw new BizException(CarrierDataExceptionEnum.FREIGHT_RATE_ALREADY_EXISTS_IN_THE_COMPANY);
        }
        TFreight tFreight = new TFreight();
        tFreight.setObjectId(requestModel.getCompanyId());
        tFreight.setObjectType(requestModel.getRoleType());
        commonBiz.setBaseEntityAdd(tFreight, BaseContextHandler.getUserName());
        tFreightMapper.insertSelective(tFreight);
    }

    /**
     * 启用/禁用
     * @param requestModel
     */
    @Transactional
    public void enableFreight(EnableFreightRequestModel requestModel) {
        TFreight dbTFreight = tFreightMapper.selectByPrimaryKey(requestModel.getFreightId());
        if(dbTFreight == null || IfValidEnum.INVALID.getKey().equals(dbTFreight.getValid())){
            throw new BizException(CarrierDataExceptionEnum.COMPANY_FREIGHT_NOT_EXISTS);
        }
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getEnabled()) && CommonConstant.INTEGER_ONE.equals(dbTFreight.getEnabled()) ) {
            throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_ALREADY_ENABLED);
        }
        if (CommonConstant.INTEGER_ZERO.equals(requestModel.getEnabled()) && CommonConstant.INTEGER_ZERO.equals(dbTFreight.getEnabled())) {
            throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_ALREADY_DISABLED);
        }
        TFreight upTFreight = new TFreight();
        upTFreight.setId(dbTFreight.getId());
        upTFreight.setEnabled(requestModel.getEnabled());
        commonBiz.setBaseEntityModify(upTFreight, BaseContextHandler.getUserName());
        tFreightMapper.updateByPrimaryKeySelective(upTFreight);
    }

    /**
     * 运价地址列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchFreightAddressResponseModel> searchFreightAddressList(SearchFreightAddressRequestModel requestModel) {
        requestModel.enablePaging();
        List<SearchFreightAddressResponseModel> freightAddressList =  tFreightAddressMapper.searchFreightAddressList(requestModel);
        return new PageInfo<>(ListUtils.isNotEmpty(freightAddressList) ? freightAddressList : new ArrayList<>());
    }

    /**
     * 运价地址规则
     * @param requestModel
     */
    @Transactional
    public void addFreightAddressRule(AddOrModifyFreightAddressRuleRequestModel requestModel) {
        if(requestModel.getFreightAddressId() == null){
            //运价地址
            List<TFreightAddress> dbTFreightAddress = tFreightAddressMapper.getListByCondition(this.getAddressRuleListConditionModel(requestModel));
            if(ListUtils.isNotEmpty(dbTFreightAddress)){
                throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_EXIST);
            }
            TFreightAddress newTFreightAddress = this.getTFreightAddressModel(requestModel);
            newTFreightAddress.setId(requestModel.getFreightAddressId());
            commonBiz.setBaseEntityAdd(newTFreightAddress,BaseContextHandler.getUserName());
            tFreightAddressMapper.insertSelective(newTFreightAddress);

            //运价地址规则
            List<FreightAddressRuleModel> pageAddressRuleList = requestModel.getFreightAddressRuleList();
            if(ListUtils.isEmpty(pageAddressRuleList)){
                throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_PERIODS_NULL);
            }
            if(pageAddressRuleList.size() > CommonConstant.INTEGER_TEN){
                throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_PERIODS_MAX);
            }

            BigDecimal ruleAmountTo = null;
            Map<Integer,Long> ruleIndexMap = new HashMap<>();
            Collections.sort(pageAddressRuleList,(o1,o2)-> o1.getRuleIndex().compareTo(o2.getRuleIndex()));
            for (FreightAddressRuleModel tempModel : pageAddressRuleList) {
                if(tempModel.getAmountTo().compareTo(tempModel.getAmountFrom()) < CommonConstant.INTEGER_ZERO){
                  throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_PERIODS_PRICE_ERROR);
                }

                if(ruleAmountTo != null && (tempModel.getAmountFrom().compareTo(ruleAmountTo) != CommonConstant.INTEGER_ZERO)){
                    throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_PERIODS_SAME_AS_PREVIOUS_PRICE);
                }
                TFreightAddressRule newTFreightAddressRule = new TFreightAddressRule();
                newTFreightAddressRule.setFreightAddressId(newTFreightAddress.getId());
                newTFreightAddressRule.setFromSymbol(tempModel.getFromSymbol());
                newTFreightAddressRule.setAmountFrom(tempModel.getAmountFrom());
                newTFreightAddressRule.setToSymbol(tempModel.getToSymbol());
                newTFreightAddressRule.setAmountTo(tempModel.getAmountTo());
                newTFreightAddressRule.setRuleIndex(tempModel.getRuleIndex());
                newTFreightAddressRule.setFreightFee(tempModel.getFreightFee());
                newTFreightAddressRule.setFreightType(tempModel.getFreightType());
                commonBiz.setBaseEntityAdd(newTFreightAddressRule,BaseContextHandler.getUserName());
                tFreightAddressRuleMapper.insertSelective(newTFreightAddressRule);

                ruleIndexMap.put(tempModel.getRuleIndex(),newTFreightAddressRule.getId());
                ruleAmountTo = tempModel.getAmountTo();
            }

            //车主多装多卸价
            if(CommonConstant.INTEGER_TWO.equals(requestModel.getRoleType()) && ListUtils.isNotEmpty(requestModel.getFreightAddressRuleMarkupList())){
                if(requestModel.getFreightAddressRuleMarkupList().size() > CommonConstant.INTEGER_THIRTY){
                    throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_PERIODS_MARKUP_MAX);
                }

                //相同阶梯阶梯+装数+卸数 唯一校验
                Map<String,String> checkRepeatRuleMarkupMap = new HashMap<>();
                requestModel.getFreightAddressRuleMarkupList().stream().forEach(tempModel->{
                    String dataSplicing = ConverterUtils.toString(tempModel.getMarkIndex()) + ConverterUtils.toString(tempModel.getLoadAmount())+ConverterUtils.toString(tempModel.getUnloadAmount());
                    checkRepeatRuleMarkupMap.put(dataSplicing,"");
                });
                if(checkRepeatRuleMarkupMap.size() != requestModel.getFreightAddressRuleMarkupList().size()){
                    throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_PERIODS_MARKUP_REPEAT);
                }

                List<TFreightAddressRuleMarkup> newTFreightAddressRuleMarkupList = new ArrayList<>();
                TFreightAddressRuleMarkup newTFreightAddressRuleMarkup;
                for (FreightAddressRuleMarkupModel tempModel : requestModel.getFreightAddressRuleMarkupList()) {
                    newTFreightAddressRuleMarkup = new TFreightAddressRuleMarkup();
                    newTFreightAddressRuleMarkup.setFreightAddressRuleId(ruleIndexMap.get(tempModel.getRuleIndex()));
                    newTFreightAddressRuleMarkup.setMarkIndex(tempModel.getMarkIndex());
                    newTFreightAddressRuleMarkup.setLoadAmount(tempModel.getLoadAmount());
                    newTFreightAddressRuleMarkup.setUnloadAmount(tempModel.getUnloadAmount());
                    newTFreightAddressRuleMarkup.setMarkupFreightFee(tempModel.getMarkupFreightFee());
                    commonBiz.setBaseEntityAdd(newTFreightAddressRuleMarkup,BaseContextHandler.getUserName());
                    newTFreightAddressRuleMarkupList.add(newTFreightAddressRuleMarkup);
                }
                if(ListUtils.isNotEmpty(newTFreightAddressRuleMarkupList)){
                    tFreightAddressRuleMarkupMapper.batchInsert(newTFreightAddressRuleMarkupList);
                }
            }
            this.insertLogs(OperateLogsOperateTypeEnum.FREIGHT_RULE_ADD,newTFreightAddress.getId(),"");
        }else{
            //运价地址
            List<TFreightAddress> dbTFreightAddress = tFreightAddressMapper.getListByCondition(this.getAddressRuleListConditionModel(requestModel));
            if(ListUtils.isNotEmpty(dbTFreightAddress) && !dbTFreightAddress.get(CommonConstant.INTEGER_ZERO).getId().equals(requestModel.getFreightAddressId())){
               throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_EXIST);
            }
            TFreightAddress upTFreightAddress = this.getTFreightAddressModel(requestModel);
            commonBiz.setBaseEntityModify(upTFreightAddress,BaseContextHandler.getUserName());
            tFreightAddressMapper.updateByPrimaryKeySelective(upTFreightAddress);

            //运价规则
            List<FreightAddressRuleModel> pageAddressRuleList = requestModel.getFreightAddressRuleList();
            if(ListUtils.isEmpty(pageAddressRuleList)){
                throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_PERIODS_NULL);
            }
            if(pageAddressRuleList.size() > CommonConstant.INTEGER_TEN){
                throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_PERIODS_MAX);
            }

            Collections.sort(pageAddressRuleList,(o1,o2)-> o1.getRuleIndex().compareTo(o2.getRuleIndex()));
            Map<Long,FreightAddressRuleModel> pageFreightAddressRuleMap = new HashMap<>();
            BigDecimal ruleAmountTo = null;
            for (FreightAddressRuleModel tempModel : requestModel.getFreightAddressRuleList()) {
                if(tempModel.getAmountTo().compareTo(tempModel.getAmountFrom()) < CommonConstant.INTEGER_ZERO){
                    throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_PERIODS_PRICE_ERROR);
                }
                if(ruleAmountTo != null && (tempModel.getAmountFrom().compareTo(ruleAmountTo) != CommonConstant.INTEGER_ZERO)){
                    throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_PERIODS_SAME_AS_PREVIOUS_PRICE);
                }
                if(tempModel.getFreightAddressRuleId() != null){
                    pageFreightAddressRuleMap.put(tempModel.getFreightAddressRuleId(),tempModel);
                }
                ruleAmountTo = tempModel.getAmountTo();
            }

            Map<Integer,Long> ruleIndexMap = new HashMap<>();
            List<TFreightAddressRule> tFreightAddressRuleList = tFreightAddressRuleMapper.getListByFreightAddressIds(ConverterUtils.toString(requestModel.getFreightAddressId()));
            for (FreightAddressRuleModel tempModel : pageAddressRuleList) {
                if(tempModel.getFreightAddressRuleId() == null){
                    TFreightAddressRule newTFreightAddressRule = new TFreightAddressRule();
                    newTFreightAddressRule.setFreightAddressId(upTFreightAddress.getId());
                    newTFreightAddressRule.setFromSymbol(tempModel.getFromSymbol());
                    newTFreightAddressRule.setAmountFrom(tempModel.getAmountFrom());
                    newTFreightAddressRule.setToSymbol(tempModel.getToSymbol());
                    newTFreightAddressRule.setAmountTo(tempModel.getAmountTo());
                    newTFreightAddressRule.setRuleIndex(tempModel.getRuleIndex());
                    newTFreightAddressRule.setFreightFee(tempModel.getFreightFee());
                    newTFreightAddressRule.setFreightType(tempModel.getFreightType());
                    commonBiz.setBaseEntityAdd(newTFreightAddressRule,BaseContextHandler.getUserName());
                    tFreightAddressRuleMapper.insertSelective(newTFreightAddressRule);
                    ruleIndexMap.put(tempModel.getRuleIndex(),newTFreightAddressRule.getId());
                }
            }

            List<TFreightAddressRule> upTFreightAddressRuleList = new ArrayList<>();
            TFreightAddressRule upTFreightAddressRule;
            for (TFreightAddressRule tempEntity : tFreightAddressRuleList) {
                FreightAddressRuleModel tempModel = pageFreightAddressRuleMap.get(tempEntity.getId());
                upTFreightAddressRule = new TFreightAddressRule();
                if(tempModel != null){
                    upTFreightAddressRule.setRuleIndex(tempModel.getRuleIndex());
                    upTFreightAddressRule.setAmountFrom(tempModel.getAmountFrom());
                    upTFreightAddressRule.setFromSymbol(tempModel.getFromSymbol());
                    upTFreightAddressRule.setAmountTo(tempModel.getAmountTo());
                    upTFreightAddressRule.setToSymbol(tempModel.getToSymbol());
                    upTFreightAddressRule.setFreightFee(tempModel.getFreightFee());
                    upTFreightAddressRule.setFreightType(tempModel.getFreightType());
                    ruleIndexMap.put(tempModel.getRuleIndex(),tempEntity.getId());
                }else{
                    upTFreightAddressRule.setValid(IfValidEnum.INVALID.getKey());
                }
                upTFreightAddressRule.setId(tempEntity.getId());
                commonBiz.setBaseEntityModify(upTFreightAddressRule,BaseContextHandler.getUserName());
                upTFreightAddressRuleList.add(upTFreightAddressRule);
            }
            if(ListUtils.isNotEmpty(upTFreightAddressRuleList)){
                tFreightAddressRuleMapper.batchUpdate(upTFreightAddressRuleList);
            }

            //车主多装多卸修改
            if(CommonConstant.INTEGER_TWO.equals(requestModel.getRoleType())){
                List<FreightAddressRuleMarkupModel> freightAddressRuleMarkupList = requestModel.getFreightAddressRuleMarkupList();
                if(freightAddressRuleMarkupList.size() > CommonConstant.INTEGER_THIRTY){
                    throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_PERIODS_MARKUP_MAX);
                }

                Map<String,String> checkRepeatRuleMarkupMap = new HashMap<>();
                freightAddressRuleMarkupList.stream().forEach(tempModel->{
                    String dataSplicing = ConverterUtils.toString(tempModel.getMarkIndex()) + ConverterUtils.toString(tempModel.getLoadAmount())+ConverterUtils.toString(tempModel.getUnloadAmount());
                    checkRepeatRuleMarkupMap.put(dataSplicing,"");
                });
                if(checkRepeatRuleMarkupMap.size() != freightAddressRuleMarkupList.size()){
                    throw new BizException(EntrustDataExceptionEnum.FREIGHT_RULE_PERIODS_MARKUP_REPEAT);
                }

                List<FreightAddressRuleMarkupModel> newAddressRuleMarkupModelList = new ArrayList<>();
                Map<Long,FreightAddressRuleMarkupModel> pageFreightAddressRuleMarkupMap = new HashMap<>();
                for (FreightAddressRuleMarkupModel tempModel : freightAddressRuleMarkupList) {
                    if(tempModel.getFreightAddressRuleMarkupId() != null){
                        pageFreightAddressRuleMarkupMap.put(tempModel.getFreightAddressRuleMarkupId(),tempModel);
                    }else{
                        newAddressRuleMarkupModelList.add(tempModel);
                    }
                }

                List<Long> freightAddressRuleIdList = tFreightAddressRuleList.stream().map(TFreightAddressRule::getId).distinct().collect(Collectors.toList());
                List<TFreightAddressRuleMarkup> dbAddressRuleMarkupList = tFreightAddressRuleMarkupMapper.getByFreightAddressRuleIds(StringUtils.listToString(freightAddressRuleIdList,','));
                if(ListUtils.isNotEmpty(newAddressRuleMarkupModelList)){
                    List<TFreightAddressRuleMarkup> newTFreightAddressRuleMarkupList = new ArrayList<>();
                    TFreightAddressRuleMarkup newTFreightAddressRuleMarkup;
                    for (FreightAddressRuleMarkupModel tempModel : newAddressRuleMarkupModelList) {
                        newTFreightAddressRuleMarkup = new TFreightAddressRuleMarkup();
                        newTFreightAddressRuleMarkup.setFreightAddressRuleId(ruleIndexMap.get(tempModel.getRuleIndex()));
                        newTFreightAddressRuleMarkup.setMarkIndex(tempModel.getMarkIndex());
                        newTFreightAddressRuleMarkup.setLoadAmount(tempModel.getLoadAmount());
                        newTFreightAddressRuleMarkup.setUnloadAmount(tempModel.getUnloadAmount());
                        newTFreightAddressRuleMarkup.setMarkupFreightFee(tempModel.getMarkupFreightFee());
                        commonBiz.setBaseEntityAdd(newTFreightAddressRuleMarkup,BaseContextHandler.getUserName());
                        newTFreightAddressRuleMarkupList.add(newTFreightAddressRuleMarkup);
                    }

                    if(ListUtils.isNotEmpty(newTFreightAddressRuleMarkupList)){
                        tFreightAddressRuleMarkupMapper.batchInsert(newTFreightAddressRuleMarkupList);
                    }
                }

                List<TFreightAddressRuleMarkup> upTFreightAddressRuleMarkupList = new ArrayList<>();
                TFreightAddressRuleMarkup upTFreightAddressRuleMarkup;
                if(ListUtils.isNotEmpty(dbAddressRuleMarkupList)){
                    for (TFreightAddressRuleMarkup tempModel : dbAddressRuleMarkupList) {
                        FreightAddressRuleMarkupModel addressRuleMarkupModel = pageFreightAddressRuleMarkupMap.get(tempModel.getId());
                        upTFreightAddressRuleMarkup = new TFreightAddressRuleMarkup();
                        upTFreightAddressRuleMarkup.setId(tempModel.getId());
                        commonBiz.setBaseEntityModify(upTFreightAddressRuleMarkup,BaseContextHandler.getUserName());
                        if(addressRuleMarkupModel != null){
                            upTFreightAddressRuleMarkup.setFreightAddressRuleId(ruleIndexMap.get(addressRuleMarkupModel.getRuleIndex()));
                            upTFreightAddressRuleMarkup.setMarkIndex(addressRuleMarkupModel.getMarkIndex());
                            upTFreightAddressRuleMarkup.setLoadAmount(addressRuleMarkupModel.getLoadAmount());
                            upTFreightAddressRuleMarkup.setUnloadAmount(addressRuleMarkupModel.getUnloadAmount());
                            upTFreightAddressRuleMarkup.setMarkupFreightFee(addressRuleMarkupModel.getMarkupFreightFee());
                        }else{
                            upTFreightAddressRuleMarkup.setValid(IfValidEnum.INVALID.getKey());
                        }
                        upTFreightAddressRuleMarkupList.add(upTFreightAddressRuleMarkup);
                    }
                    tFreightAddressRuleMarkupMapper.batchUpdate(upTFreightAddressRuleMarkupList);
                }
            }

            this.insertLogs(OperateLogsOperateTypeEnum.FREIGHT_RULE_MODIFY,upTFreightAddress.getId(),"");
        }
    }

    /**
     * @param requestModel
     * @return GetAddressRuleListConditionModel 查询条件
     */
    private GetAddressRuleListConditionModel getAddressRuleListConditionModel(AddOrModifyFreightAddressRuleRequestModel requestModel){
        GetAddressRuleListConditionModel conditionModel = new GetAddressRuleListConditionModel();
        if(requestModel == null){ return conditionModel; }
        conditionModel.setCalcType(requestModel.getCalcType());
        conditionModel.setFreightId(requestModel.getFreightId());
        conditionModel.setWarehouseId(Optional.ofNullable(requestModel.getWarehouseId()).orElse(CommonConstant.LONG_ZERO));
        conditionModel.setFromAreaId(requestModel.getFromAreaId());
        conditionModel.setFromCityId(requestModel.getFromCityId());
        conditionModel.setFromProvinceId(requestModel.getFromProvinceId());
        conditionModel.setToAreaId(requestModel.getToAreaId());
        conditionModel.setToCityId(requestModel.getToCityId());
        conditionModel.setToProvinceId(requestModel.getToProvinceId());
        return conditionModel;
    }

    /**
     * @param requestModel
     * @return TFreightAddress 新增/修改实体
     */
    private TFreightAddress getTFreightAddressModel(AddOrModifyFreightAddressRuleRequestModel requestModel){
        TFreightAddress tFreightAddress = new TFreightAddress();
        tFreightAddress.setId(requestModel.getFreightAddressId());
        tFreightAddress.setFreightId(requestModel.getFreightId());
        tFreightAddress.setWarehouseId(requestModel.getWarehouseId() == null ? CommonConstant.LONG_ZERO : requestModel.getWarehouseId() );
        tFreightAddress.setCalcType(requestModel.getCalcType());
        tFreightAddress.setFromAreaId(requestModel.getFromAreaId());
        tFreightAddress.setFromAreaName(requestModel.getFromAreaName());
        tFreightAddress.setFromCityId(requestModel.getFromCityId());
        tFreightAddress.setFromCityName(requestModel.getFromCityName());
        tFreightAddress.setFromProvinceId(requestModel.getFromProvinceId());
        tFreightAddress.setFromProvinceName(requestModel.getFromProvinceName());
        tFreightAddress.setToAreaId(requestModel.getToAreaId());
        tFreightAddress.setToAreaName(requestModel.getToAreaName());
        tFreightAddress.setToCityId(requestModel.getToCityId());
        tFreightAddress.setToCityName(requestModel.getToCityName());
        tFreightAddress.setToProvinceId(requestModel.getToProvinceId());
        tFreightAddress.setToProvinceName(requestModel.getToProvinceName());
        return tFreightAddress;
    }

    /**
     * 运价规则详情
     * @param requestModel
     * @return
     */
    public FreightAddressRuleDetailResponseModel getFreightRuleDetail(FreightAddressRuleDetailRequestModel requestModel) {
        FreightAddressRuleDetailResponseModel responseModel = tFreightAddressMapper.getFreightAddressRuleDetail(requestModel.getFreightAddressId());
        return responseModel == null ? new FreightAddressRuleDetailResponseModel() : responseModel;
    }

    /**
     * 删除运价规则
     * @param requestModel
     */
    @Transactional
    public void deleteFreightAddressRule(DeleteFreightAddressRequestModel requestModel) {
        tFreightAddressMapper.deleteFreightAddress(requestModel.getFreightAddressIds(),BaseContextHandler.getUserName(),new Date());
    }

    /**
     * 统一加价/减价
     * @param requestModel
     */
    @Transactional
    public void modifyFreightPrice(ModifyFreightPriceRequestModel requestModel) {
        if(FreightCalcTypeEnum.getEnum(requestModel.getCalcType()) == null
                || FreightUnitTypeEnum.getEnum(requestModel.getUnitType()) == null
                || requestModel.getFreightAddressIds() == null){
            throw new BizException(EntrustDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        List<TFreightAddressRule> ruleList = tFreightAddressRuleMapper.getListByFreightAddressIds(requestModel.getFreightAddressIds());
        FreightCalcTypeEnum freightCalcTypeEnum = FreightCalcTypeEnum.getEnum(requestModel.getCalcType());
        List<String> addressIdList = Arrays.asList(requestModel.getFreightAddressIds().split(","));

        String logTxtSuffix = "";
        if(CommonConstant.INTEGER_ONE.equals(requestModel.getUnitType())){
            logTxtSuffix = ConverterUtils.toString(requestModel.getAmount()) + "%";
        }else{
            logTxtSuffix = ConverterUtils.toString(requestModel.getAmount()) + "元";
        }

        //日志信息
        Date now = new Date();
        String userName = BaseContextHandler.getUserName();
        TOperateLogs tOperateLogs;
        List<TOperateLogs> batchList = new ArrayList<>();
        if(ListUtils.isNotEmpty(addressIdList)){
            for (String addressId : addressIdList) {
                tOperateLogs = new TOperateLogs();
                tOperateLogs.setObjectId(ConverterUtils.toLong(addressId));
                tOperateLogs.setObjectType(OperateLogsObjectTypeEnum.FREIGHT.getKey());
                tOperateLogs.setOperateContents(freightCalcTypeEnum.getValue() + ":" + logTxtSuffix);
                tOperateLogs.setOperateTime(now);
                tOperateLogs.setOperateUserName(userName);
                batchList.add(tOperateLogs);
            }
            tOperateLogsMapper.batchInsert(batchList);
        }

        //统一加价减价
        if(ListUtils.isNotEmpty(ruleList)){
            List<TFreightAddressRule> upRuleList = new ArrayList<>();
            TFreightAddressRule upRule;
            BigDecimal freightFee = BigDecimal.ZERO;
            for (TFreightAddressRule temp : ruleList) {
                if (FreightCalcTypeEnum.ADD_PRICE.equals(freightCalcTypeEnum)) {
                    if (FreightUnitTypeEnum.PERCENT.getKey().equals(requestModel.getUnitType())) {
                        freightFee = temp.getFreightFee().add(temp.getFreightFee().multiply(requestModel.getAmount()).divide(CommonConstant.ONE_HUNDRED)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    } else if (FreightUnitTypeEnum.YUAN.getKey().equals(requestModel.getUnitType())){
                        freightFee = temp.getFreightFee().add(requestModel.getAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                }else if(FreightCalcTypeEnum.REDUCE_PRICE.equals(freightCalcTypeEnum)){
                    if (FreightUnitTypeEnum.PERCENT.getKey().equals(requestModel.getUnitType())){
                        freightFee = temp.getFreightFee().subtract(temp.getFreightFee().multiply(requestModel.getAmount()).divide(CommonConstant.ONE_HUNDRED)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    } else if (FreightUnitTypeEnum.YUAN.getKey().equals(requestModel.getUnitType())) {//元
                        freightFee = temp.getFreightFee().subtract(requestModel.getAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                }
                upRule = new TFreightAddressRule();
                upRule.setId(temp.getId());
                upRule.setFreightFee(freightFee);
                upRule.setLastModifiedTime(now);
                upRule.setLastModifiedBy(userName);
                upRuleList.add(upRule);

                if(BigDecimal.ZERO.compareTo(freightFee) > CommonConstant.INTEGER_ZERO){
                    throw new BizException(EntrustDataExceptionEnum.PRICE_ADJUSTMENT_ERROR);
                }
            }
            tFreightAddressRuleMapper.batchUpdate(upRuleList);
        }

    }

    /**
     * 操作日志
     * @param requestModel
     * @return
     */
    public List<FreightLogsResponseModel> freightLogs(FreightLogsRequestModel requestModel) {
        List<FreightLogsResponseModel> responseModelList = new ArrayList<>();
        List<ViewLogResponseModel> logsList = tOperateLogsMapper.selectLogsByCondition(OperateLogsObjectTypeEnum.FREIGHT.getKey(),requestModel.getFreightAddressId(),null);
        FreightLogsResponseModel model;
        if(ListUtils.isNotEmpty(logsList)){
            for (ViewLogResponseModel tempModel : logsList) {
                model = new FreightLogsResponseModel();
                model.setOperateContents(tempModel.getOperateContents());
                model.setOperateTime(tempModel.getOperateTime());
                model.setOperateUserName(tempModel.getOperateUserName());
                responseModelList.add(model);
            }
        }
        return responseModelList;
    }

    private void insertLogs(OperateLogsOperateTypeEnum type, Long objectId, String operateContents) {
        TOperateLogs newLog = new TOperateLogs();
        newLog.setObjectId(objectId);
        newLog.setObjectType(type.getObjectType().getKey());
        newLog.setOperateType(type.getOperateType());
        newLog.setOperateContents(StringUtils.isBlank(operateContents) ? type.getOperateContents() : operateContents);
        newLog.setOperateTime(new Date());
        newLog.setOperateUserName(BaseContextHandler.getUserName());
        commonBiz.setBaseEntityAdd(newLog, BaseContextHandler.getUserName());
        tOperateLogsMapper.insertSelective(newLog);
    }


    /**
     * 查询司机运价信息
     * @param requestModel
     * @return
     */
    public DriverFreightByDemandOrderIdsAndVehicleResponseModel getDriverFreight(DriverFreightByDemandOrderIdsAndVehicleRequestModel requestModel){
        if(ListUtils.isEmpty(requestModel.getDemandOrderList())){
            throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_IDS_EMPTY);
        }

        List<Long> demandOrderIdLList = requestModel.getDemandOrderList().stream().map(DemandOrderIdAndExpectAmountRequestModel::getDemandOrderId).distinct().collect(Collectors.toList());
        List<GetDemandOrderInfoByIdsModel> demandOrderInfoList = tDemandOrderMapper.getDemandOrderInfoByIds(StringUtils.listToString(demandOrderIdLList,','));
        if(ListUtils.isEmpty(demandOrderInfoList) || demandOrderIdLList.size() != demandOrderInfoList.size()){
            throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_IDS_EMPTY);
        }
        GetDemandOrderInfoByIdsModel demandOrderInfo = demandOrderInfoList.get(CommonConstant.INTEGER_ZERO);
        Long companyCarrierId = demandOrderInfo.getCompanyCarrierId();
        FreightRuleCalcRuleEnum  calcRuleEnum = FreightRuleCalcRuleEnum.getEnum(demandOrderInfo.getGoodsUnit());
        if(calcRuleEnum == null || !calcRuleEnum.getKey().equals(FreightRuleCalcRuleEnum.BY_WEIGHT.getKey())){
            return new DriverFreightByDemandOrderIdsAndVehicleResponseModel();
        }

        List<WebDriverAndVehicleResponseModel> carrierVehicle = tCarrierVehicleRelationMapper.searchRelByVehicleNoAndCarrierId(companyCarrierId, requestModel.getVehicleNo());
        if(ListUtils.isEmpty(carrierVehicle)){
            return new DriverFreightByDemandOrderIdsAndVehicleResponseModel();
        }

        TFreight dbTFreight = tFreightMapper.getByCompanyIdAndRoleType(companyCarrierId,CommonConstant.INTEGER_TWO);
        if(dbTFreight == null
                || IfValidEnum.INVALID.getKey().equals(dbTFreight.getValid())
                || dbTFreight.getEnabled().equals(CommonConstant.INTEGER_ZERO)){
            return new DriverFreightByDemandOrderIdsAndVehicleResponseModel();
        }

        BigDecimal totalExpectAmount = BigDecimal.ZERO;
        for (DemandOrderIdAndExpectAmountRequestModel tempModel : requestModel.getDemandOrderList()) {
            totalExpectAmount = totalExpectAmount.add(tempModel.getExpectAmount());
        }

        List<CompanyDriverFreightResponseModel> retList;
        List<CompanyDriverFreightRequestModel> requestModelList = new ArrayList<>();
        if (demandOrderInfoList.size() == CommonConstant.INTEGER_ONE){//单个需求单调度，以提货地址为主匹配最精确的运价
            GetDemandOrderInfoByIdsModel tempModel = demandOrderInfoList.get(CommonConstant.INTEGER_ZERO);
            String fromAreaId = Optional.ofNullable(tempModel.getLoadAreaId()).orElse(CommonConstant.LONG_ZERO).toString();
            String toAreaId = Optional.ofNullable(tempModel.getUnloadAreaId()).orElse(CommonConstant.LONG_ZERO).toString();
            CompanyDriverFreightRequestModel model = new CompanyDriverFreightRequestModel();
            model.setCompanyCarrierId(Optional.ofNullable(companyCarrierId).orElse(CommonConstant.LONG_ZERO));
            model.setFromProvinceId(Optional.ofNullable(tempModel.getLoadProvinceId()).orElse(CommonConstant.LONG_ZERO));
            model.setFromCityId(Optional.ofNullable(tempModel.getLoadCityId()).orElse(CommonConstant.LONG_ZERO));
            model.setFromAreaId(fromAreaId);
            model.setToProvinceId(Optional.ofNullable(tempModel.getUnloadProvinceId()).orElse(CommonConstant.LONG_ZERO));
            model.setToCityId(Optional.ofNullable(tempModel.getUnloadCityId()).orElse(CommonConstant.LONG_ZERO));
            model.setToAreaId(toAreaId);
            model.setTotalAmount(totalExpectAmount);
            model.setLoadAmount(requestModel.getLoadCount());
            model.setUnloadAmount(requestModel.getUnloadCount());
            requestModelList.add(model);

            //省市区（提货地）+省市区和省市全部区（卸货地）
            retList = tFreightMapper.getDriverFreightInfo(requestModelList);//省市区+省市区
            if (ListUtils.isEmpty(retList)){
                requestModelList = new ArrayList<>();
                model.setToAreaId(CommonConstant.ZERO);
                requestModelList.add(model);
                retList = tFreightMapper.getDriverFreightInfo(requestModelList);//省市区+省市全部区
            }
            //省市全部区（提货地）+省市区和省市全部区（卸货地）
            if(ListUtils.isEmpty(retList)){
                requestModelList = new ArrayList<>();
                model.setToAreaId(toAreaId);
                model.setFromAreaId(CommonConstant.ZERO);
                requestModelList.add(model);
                retList = tFreightMapper.getDriverFreightInfo(requestModelList);//省市全部区+省市区
                if(ListUtils.isEmpty(retList)){
                    requestModelList = new ArrayList<>();
                    model.setToAreaId(CommonConstant.ZERO);
                    requestModelList.add(model);
                    retList = tFreightMapper.getDriverFreightInfo(requestModelList);//省市全部区+省市全部区
                }
            }
        }else{//多个需求单调度，匹配最高的运价
            CompanyDriverFreightRequestModel model;
            String fromAreaId;
            String toAreaId;
            for (GetDemandOrderInfoByIdsModel tempModel : demandOrderInfoList) {
                fromAreaId = Optional.ofNullable(tempModel.getLoadAreaId()).orElse(CommonConstant.LONG_ZERO).toString();
                toAreaId = Optional.ofNullable(tempModel.getUnloadAreaId()).orElse(CommonConstant.LONG_ZERO).toString();
                model = new CompanyDriverFreightRequestModel();
                model.setCompanyCarrierId(Optional.ofNullable(companyCarrierId).orElse(CommonConstant.LONG_ZERO));
                model.setFromProvinceId(Optional.ofNullable(tempModel.getLoadProvinceId()).orElse(CommonConstant.LONG_ZERO));
                model.setFromCityId(Optional.ofNullable(tempModel.getLoadCityId()).orElse(CommonConstant.LONG_ZERO));
                model.setFromAreaId(fromAreaId+","+CommonConstant.INTEGER_ZERO);
                model.setToProvinceId(Optional.ofNullable(tempModel.getUnloadProvinceId()).orElse(CommonConstant.LONG_ZERO));
                model.setToCityId(Optional.ofNullable(tempModel.getUnloadCityId()).orElse(CommonConstant.LONG_ZERO));
                model.setToAreaId(toAreaId+","+CommonConstant.INTEGER_ZERO);
                model.setTotalAmount(totalExpectAmount);
                model.setLoadAmount(requestModel.getLoadCount());
                model.setUnloadAmount(requestModel.getUnloadCount());
                requestModelList.add(model);
            }
            //主公司+发货地+卸货地+预提 - 带出司机运费
            retList = tFreightMapper.getDriverFreightInfo(requestModelList);
        }

        if(ListUtils.isEmpty(retList)){
            return new DriverFreightByDemandOrderIdsAndVehicleResponseModel();
        }
        for (CompanyDriverFreightResponseModel tempModel : retList) {
            BigDecimal freightFee;
            if(FreightTypeEnum.UNIT_PRICE.getKey().equals(tempModel.getFreightType())){
                freightFee = tempModel.getFreightFee().multiply(totalExpectAmount);
            }else{
                freightFee = tempModel.getFreightFee();
            }
            tempModel.setTotalFee(freightFee);
        }
        Collections.sort(retList,(s,d)-> d.getTotalFee().compareTo(s.getTotalFee()));
        CompanyDriverFreightResponseModel maxFeeModel = retList.get(CommonConstant.INTEGER_ZERO);

        //根据查询出的最高运价的地址规则查询多装多卸价
        BigDecimal markUpFee = BigDecimal.ZERO;
        TFreightAddressRuleMarkup tFreightAddressRuleMarkup = tFreightAddressRuleMarkupMapper.getByRuleIdLoadAmount(maxFeeModel.getFreightAddressRuleId(), requestModel.getLoadCount(), requestModel.getUnloadCount());
        if (tFreightAddressRuleMarkup != null){
            markUpFee = tFreightAddressRuleMarkup.getMarkupFreightFee();
        }

        DriverFreightByDemandOrderIdsAndVehicleResponseModel responseModel = new DriverFreightByDemandOrderIdsAndVehicleResponseModel();
        responseModel.setPriceType(maxFeeModel.getFreightType());
        responseModel.setPrice(maxFeeModel.getFreightFee());
        responseModel.setMultipleMarkupPrice(markUpFee);
        return responseModel;
    }

    /**
     * 委托发布匹配货主运价
     * @param requestModel
     * @return
     */
    public GetPriceByAddressAndAmountResponseModel getEntrustFreightInfo(GetPriceByAddressAndAmountRequestModel requestModel){
        TFreight dbTFreight = tFreightMapper.getByCompanyIdAndRoleType(requestModel.getCompanyEntrustId(),CommonConstant.INTEGER_ONE);
        if(dbTFreight == null || dbTFreight.getEnabled().equals(CommonConstant.INTEGER_ZERO) ){
            return new GetPriceByAddressAndAmountResponseModel();
        }
        if(!FreightRuleCalcRuleEnum.BY_WEIGHT.equals(FreightRuleCalcRuleEnum.getEnum(requestModel.getGoodsUnit()))){
            return new GetPriceByAddressAndAmountResponseModel();
        }

        //匹配卸货仓库详细地址是否包含一日游价格
        Integer calcType = CommonConstant.INTEGER_ONE;
        if(StringUtils.isNotBlank(requestModel.getToWarehouseDetail()) && requestModel.getToWarehouseDetail().contains(CommonConstant.ONE_DAY_TOUR)){
            calcType = CommonConstant.INTEGER_TWO;
        }

        GetFreightForEntrustRequestModel model = new GetFreightForEntrustRequestModel();
        model.setCompanyEntrustId(Optional.ofNullable(requestModel.getCompanyEntrustId()).orElse(CommonConstant.LONG_ZERO));
        model.setCalcType(Optional.ofNullable(calcType).orElse(CommonConstant.INTEGER_ZERO));
        model.setGoodsAmount(Optional.ofNullable(requestModel.getGoodsAmount()).orElse(BigDecimal.ZERO));
        model.setFromAreaId(Optional.ofNullable(requestModel.getFromAreaId()).orElse(CommonConstant.LONG_ZERO));
        model.setFromCityId(Optional.ofNullable(requestModel.getFromCityId()).orElse(CommonConstant.LONG_ZERO));
        model.setFromProvinceId(Optional.ofNullable(requestModel.getFromProvinceId()).orElse(CommonConstant.LONG_ZERO));
        model.setToAreaId(Optional.ofNullable(requestModel.getToAreaId()).orElse(CommonConstant.LONG_ZERO));
        model.setToCityId(Optional.ofNullable(requestModel.getToCityId()).orElse(CommonConstant.LONG_ZERO));
        model.setToProvinceId(Optional.ofNullable(requestModel.getToProvinceId()).orElse(CommonConstant.LONG_ZERO));
        GetFreightForEntrustResponseModel dbModel = null;
        //有仓库
        if (StringUtils.isNotBlank(requestModel.getFromWarehouse())){
            TWarehouseAddress dbTWarehouseAddress = tWarehouseAddressMapper.getWarehouseByName(model.getCompanyEntrustId(), requestModel.getFromWarehouse());
            if(dbTWarehouseAddress != null){
                model.setWarehouseId(dbTWarehouseAddress.getId());
            }
            //省市区仓库（提货地）+省市区和省市全部区（卸货地）
            dbModel = tFreightMapper.getEntrustFreightInfo(model);//省市区仓库+省市区
            if(dbModel == null){
                model.setToAreaId(CommonConstant.LONG_ZERO);
                dbModel = tFreightMapper.getEntrustFreightInfo(model);//省市区仓库+省市全部区
            }
        }
        //无仓库
        //省市区（提货地）+省市区和省市全部区（卸货地）
        if(dbModel == null){
            model.setWarehouseId(null);
            model.setToAreaId(Optional.ofNullable(requestModel.getToAreaId()).orElse(CommonConstant.LONG_ZERO));
            dbModel = tFreightMapper.getEntrustFreightInfo(model);//省市区+省市区
            if(dbModel == null){
                model.setToAreaId(CommonConstant.LONG_ZERO);
                dbModel = tFreightMapper.getEntrustFreightInfo(model);//省市区+省市全部区
            }
        }
        //省市全部区（提货地）+省市区和省市全部区（卸货地）
        if(dbModel == null){
            model.setToAreaId(Optional.ofNullable(requestModel.getToAreaId()).orElse(CommonConstant.LONG_ZERO));
            model.setFromAreaId(CommonConstant.LONG_ZERO);
            dbModel = tFreightMapper.getEntrustFreightInfo(model);//省市全部区+省市区
            if(dbModel == null){
                model.setToAreaId(CommonConstant.LONG_ZERO);
                dbModel = tFreightMapper.getEntrustFreightInfo(model);//省市全部区+省市全部区
            }
        }
        if (dbModel == null){
            return new GetPriceByAddressAndAmountResponseModel();
        }

        GetPriceByAddressAndAmountResponseModel retModel = new GetPriceByAddressAndAmountResponseModel();
        retModel.setFreightFee(Optional.ofNullable(dbModel.getFreightFee()).orElse(BigDecimal.ZERO));
        retModel.setFreightType(dbModel.getFreightType());
        return retModel;
    }
}
