package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/2
 */
@Data
public class EditCarrierOrderCostRequestModel {

	@ApiModelProperty(value = "运单ID")
	private Long carrierOrderId;

	@ApiModelProperty(value = "费用类型 1:货主费用 2:车主费用")
	private Integer costType;

	@ApiModelProperty(value = "价格类型 1 单价 2 一口价")
	private Integer priceType;

	@ApiModelProperty(value = "价格")
	private BigDecimal price;
}
