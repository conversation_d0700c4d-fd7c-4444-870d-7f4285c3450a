package com.logistics.appapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/10/17
 */
@Data
public class QueryIdByCarrierOrderCodeRequestDto {

	@ApiModelProperty(value = "运单号", required = true)
	@NotBlank(message = "缺少运单号")
	private String carrierOrderCode;
}
