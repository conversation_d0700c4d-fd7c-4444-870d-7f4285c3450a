package com.logistics.management.webapi.controller.carrierorder.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新生运单列表
 *
 * <AUTHOR>
 * @date 2022/8/16 15:05
 */
@Data
@ExcelIgnoreUnannotated
public class SearchCarrierOrderListForYeloLifeResponseDto {

    @ApiModelProperty("运单ID")
    private String carrierOrderId = "";

    @ApiModelProperty("10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消")
    private String status = "";
    @ExcelProperty("状态")
    @ApiModelProperty("运单状态描述")
    private String statusDesc = "";

    @ExcelProperty("运单号")
    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("需求单ID")
    private String demandOrderId = "";
    @ExcelProperty("需求单号")
    @ApiModelProperty("需求单号")
    private String demandOrderCode = "";

    /**
     * (3.26.0)出库状态：0 待出库，2 已出库
     */
    @ApiModelProperty("出库状态：0 待出库，2 已出库")
    private String outStatus = "";

    /**
     * (3.26.0)出库状态
     */
    @ExcelProperty("出库状态")
    @ApiModelProperty("出库状态展示文本")
    private String outStatusLabel = "";

    /**
     * 需求类型 100 新生回收，101新生销售    3.23.0
     */
    private String entrustType = "";
    /**
     * 需求类型
     */
    @ExcelProperty("需求类型")
    private String entrustTypeDesc = "";

    @ExcelProperty("客户单号")
    @ApiModelProperty("客户单号")
    private String customerOrderCode = "";

    @ApiModelProperty("客户")
    private String customName = "";
    @ExcelProperty("客户")
    private String exportCustomerName = "";

    @ExcelProperty("车主")
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName= "";

    @ExcelProperty("车牌号")
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";

    @ApiModelProperty("司机")
    private String driver = "";
    @ExcelProperty("司机")
    private String exportDriver = "";

    @ExcelProperty("发货地址")
    @ApiModelProperty("发货地址")
    private String loadAddress = "";//拼接后的数据，列表展示

    @ExcelProperty("收货仓库")
    @ApiModelProperty("收货仓库")
    private String unloadWareHouse = "";

    @ExcelProperty("收货地址")
    @ApiModelProperty("收货地址")
    private String unloadAddress = "";//拼接后的数据，列表展示

    @ExcelProperty("备注")
    @ApiModelProperty("备注")
    private String remark = "";

    @ExcelProperty("品名")
    @ApiModelProperty("品名")
    private String goodsName = "";

    @ExcelProperty("单位")
    @ApiModelProperty("货物单位")
    private String goodsUnit="";

    @ExcelProperty("预提")
    @ApiModelProperty("预计承运数量")
    private String expectAmount = "";

    @ExcelProperty("实提")
    @ApiModelProperty("实际提货数量")
    private String loadAmount = "";

    @ExcelProperty("实卸")
    @ApiModelProperty("实际卸货数量")
    private String unloadAmount = "";

    @ExcelProperty("签收")
    @ApiModelProperty("签收数量")
    private String signAmount = "";

    @ExcelProperty("回单数")
    @ApiModelProperty("回单数")
    private String carrierOrderTicketsAmount = "";

    @ExcelProperty("运单生成时间")
    @ApiModelProperty("运单生成时间")
    private String dispatchTime = "";

    @ExcelProperty("调度人")
    @ApiModelProperty("调度人")
    private String dispatchUserName = "";

    @ExcelProperty("来源")
    @ApiModelProperty("客户订单来源")
    private String customerOrderSource = "";

    @ApiModelProperty("是否按编码回收 0:否 1:是   v2.44")
    private String ifRecycleByCode;

}
