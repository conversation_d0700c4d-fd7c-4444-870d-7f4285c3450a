package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TWarehouseAddress extends BaseEntity {
    /**
    * 委托方公司id
    */
    @ApiModelProperty("委托方公司id")
    private Long companyEntrustId;

    /**
    * 委托方公司名称
    */
    @ApiModelProperty("委托方公司名称")
    private String companyEntrustName;

    /**
    * 仓库名
    */
    @ApiModelProperty("仓库名")
    private String warehouse;

    /**
    * 省份ID
    */
    @ApiModelProperty("省份ID")
    private Long provinceId;

    /**
    * 省份名字
    */
    @ApiModelProperty("省份名字")
    private String provinceName;

    /**
    * 城市ID
    */
    @ApiModelProperty("城市ID")
    private Long cityId;

    /**
    * 城市名字
    */
    @ApiModelProperty("城市名字")
    private String cityName;

    /**
    * 县区id
    */
    @ApiModelProperty("县区id")
    private Long areaId;

    /**
    * 县区名字
    */
    @ApiModelProperty("县区名字")
    private String areaName;

    /**
    * 0禁用 1启用
    */
    @ApiModelProperty("0禁用 1启用")
    private Integer enabled;

    /**
    * 禁用启用时间
    */
    @ApiModelProperty("禁用启用时间")
    private Date enabledTime;
}