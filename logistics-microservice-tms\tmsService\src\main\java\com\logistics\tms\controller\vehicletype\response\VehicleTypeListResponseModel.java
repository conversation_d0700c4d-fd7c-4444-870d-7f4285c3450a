package com.logistics.tms.controller.vehicletype.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class VehicleTypeListResponseModel {
    @ApiModelProperty("序号")
    private Long vehicleTypeId;
    @ApiModelProperty("车辆状态 0禁用 1启用")
    private Integer enabled;
    @ApiModelProperty("车辆类型")
    private String vehicleType;
    @ApiModelProperty("车辆类别：1 牵引车 2 挂车 3 一体车")
    private Integer vehicleCategory;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("添加人")
    private String addUserName;
    @ApiModelProperty("操作人")
    private String lastModifiedBy;
    @ApiModelProperty("最新操作时间")
    private Date lastModifiedTime;
}
