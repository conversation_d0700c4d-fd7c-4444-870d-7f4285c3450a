package com.logistics.tms.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
@Data
public class RenewableConfirmGoodsRequestModel {

    @ApiModelProperty(value = "新生订单审核列表id")
    private Long renewableOrderId;

    @ApiModelProperty(value = "收货地址code（云仓仓库地址code）", required = true)
    private String unloadAddressCode;

    @ApiModelProperty(value = "收货省id")
    private Long unloadProvinceId;

    @ApiModelProperty(value = "收货省")
    private String unloadProvinceName;

    @ApiModelProperty(value = "收货市id")
    private Long unloadCityId;

    @ApiModelProperty(value = "收货市")
    private String unloadCityName;

    @ApiModelProperty(value = "收货区id")
    private Long unloadAreaId;

    @ApiModelProperty(value = "收货区")
    private String unloadAreaName;

    @ApiModelProperty("收货地址详细")
    private String unloadDetailAddress;

    @ApiModelProperty(value = "收货仓库")
    private String unloadWarehouse;

    @ApiModelProperty(value = "收货人")
    private String receiverName;

    @ApiModelProperty(value = "收货人手机号")
    private String receiverMobile;

    @ApiModelProperty(value = "确认货物信息")
    private List<RenewableGoodsRequestModel> confirmGoodsList;

    @ApiModelProperty(value = "现场图片")
    private List<String> scenePhotosList;

    @ApiModelProperty(value = "确认单据")
    private List<String> orderPhotosList;
}
