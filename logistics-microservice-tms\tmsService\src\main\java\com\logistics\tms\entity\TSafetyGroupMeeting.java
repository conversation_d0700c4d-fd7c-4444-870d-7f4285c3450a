package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TSafetyGroupMeeting extends BaseEntity {
    /**
    * 会议年份
    */
    @ApiModelProperty("会议年份")
    private String meetingYear;

    /**
    * 会议季度
    */
    @ApiModelProperty("会议季度")
    private Integer meetingSeason;

    /**
    * 会议标题
    */
    @ApiModelProperty("会议标题")
    private String meetingTitle;

    /**
    * 会议时间
    */
    @ApiModelProperty("会议时间")
    private Date meetingTime;

    /**
    * 会议地点
    */
    @ApiModelProperty("会议地点")
    private String meetingPlace;

    /**
    * 主持人
    */
    @ApiModelProperty("主持人")
    private String anchor;

    /**
    * 记录人
    */
    @ApiModelProperty("记录人")
    private String recordPerson;

    /**
    * 会议内容
    */
    @ApiModelProperty("会议内容")
    private String content;
}