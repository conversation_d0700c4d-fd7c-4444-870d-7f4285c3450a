package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RenewableDemandOrderDetailResponseDto {

    @ApiModelProperty("需求单id")
    private String demandId="";

    @ApiModelProperty("需求单号")
    private String demandOrderCode = "";

    @ApiModelProperty("客户单号")
    private String customerOrderCode = "";

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("下单时间")
    private String publishTime = "";

    @ApiModelProperty("下单人")
    private String publishName = "";

    @ApiModelProperty("需求单状态")
    private String status = "";

    @ApiModelProperty("需求单展示文本")
    private String statusLabel = "";

    @ApiModelProperty("期望提货时间")
    private String expectedLoadTime = "";

    @ApiModelProperty("客户")
    private String customerName = "";

    @ApiModelProperty("车主公司名称")
    private String companyCarrierName= "";

    @ApiModelProperty("发货地址")
    private String loadDetailAddress = "";

    @ApiModelProperty("发货人")
    private String consignorName = "";

    @ApiModelProperty("收货地址")
    private String unloadDetailAddress = "";

    @ApiModelProperty("收货人")
    private String receiverName = "";

    @ApiModelProperty("预计货主价格类型 1 单价(元/吨，元/件)，2 一口价(元)")
    private String expectContractPriceType = "";

    @ApiModelProperty("预计货主价格")
    private String expectContractPrice = "";

    @ApiModelProperty("预计货主费用合计")
    private String contractPriceTotal = "";

    @ApiModelProperty("实际货主价格类型 1 单价(元/吨，元/件)，2 一口价(元)")
    private String contractPriceType = "";

    @ApiModelProperty("实际货主价格")
    private String contractPrice = "";

    @ApiModelProperty("实际货主费用合计")
    private String actualContractPriceTotal = "";

    @ApiModelProperty("备注")
    private String remark = "";

    @ApiModelProperty("单位")
    private String goodsUnit = "";

    @ApiModelProperty("货物信息")
    private List<RenewableDemandOrderGoodsResponseDto> goodsResponseModel;

    @ApiModelProperty("运单信息")
    private List<RenewableDemandOrderCarrierResponseDto> carrierResponseModel;
}
