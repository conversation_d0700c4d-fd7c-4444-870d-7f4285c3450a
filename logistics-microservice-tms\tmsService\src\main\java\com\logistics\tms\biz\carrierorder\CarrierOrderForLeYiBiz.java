package com.logistics.tms.biz.carrierorder;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.base.utils.StripTrailingZerosUtils;
import com.logistics.tms.biz.carrierfreight.model.AddressAreaExistRequestModel;
import com.logistics.tms.biz.carrierfreight.model.CarrierFreightCarrierModel;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.biz.carrierorder.model.*;
import com.logistics.tms.biz.carrierorderloadcode.model.CarrierOrderLoadCodeListSqlConditionModel;
import com.logistics.tms.biz.carrierorderticketsaudit.model.SearchCarrierOrderIdsForLeYiReceiptAuditBoModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.DemandOrderBiz;
import com.logistics.tms.biz.extvehiclesettlement.ExtVehicleSettlementBiz;
import com.logistics.tms.biz.reservationorder.ReservationOrderCommonBiz;
import com.logistics.tms.biz.shippingorder.ShippingOrderCommonBiz;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupEventModel;
import com.logistics.tms.biz.workordercenter.WorkOrderBiz;
import com.logistics.tms.client.TrayOrderServiceClient;
import com.logistics.tms.client.WarehouseStockClient;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.LeyiCommonAddressServiceApi;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.request.BatchQueryCustomerInfoRequest;
import com.logistics.tms.client.feign.tray.basicdata.commonaddress.response.BatchQueryCustomerInfoResponse;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCarrierOrderQRCodeResponseModel;
import com.logistics.tms.client.feign.tray.order.customerinorder.model.response.GetCustomerInOrderStateResponseModel;
import com.logistics.tms.client.feign.warehouse.stock.reponse.CheckChangeUnloadWarehouseResponseModel;
import com.logistics.tms.client.model.RecyclePublishUpdateDemandRequestModel;
import com.logistics.tms.controller.carrierorder.request.*;
import com.logistics.tms.controller.carrierorder.response.*;
import com.logistics.tms.controller.carrierorderapplet.request.CarrierOrderIdRequestModel;
import com.logistics.tms.controller.carrierorderapplet.request.VerifyEnablePickUpMoreReqModel;
import com.logistics.tms.controller.demandorder.request.UpdateDemandOrderStatusByIdsRequestModel;
import com.logistics.tms.controller.demandorder.response.CancelCarrierOrderOrderRelModel;
import com.logistics.tms.controller.demandorder.response.CreateSettlementForEntrustCorrectConsumerModel;
import com.logistics.tms.controller.demandorder.response.GetDemandOrderInfoByIdsModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.consumer.model.CompleteWarehouseModel;
import com.logistics.tms.rabbitmq.consumer.model.SyncAbnormalAmountMessage;
import com.logistics.tms.rabbitmq.consumer.model.SyncStockInfoMessage;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: wjf
 * @date: 2021/9/18 16:27
 */
@Slf4j
@Service
public class CarrierOrderForLeYiBiz {
    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private TCarrierOrderTicketsMapper tCarrierOrderTicketsMapper;
    @Autowired
    private TCarrierOrderVehicleHistoryMapper tCarrierOrderVehicleHistoryMapper;
    @Autowired
    private TDemandOrderMapper tDemandOrderMapper;
    @Autowired
    private TCarrierOrderCorrectMapper tCarrierOrderCorrectMapper;
    @Autowired
    private TCarrierOrderAddressMapper tCarrierOrderAddressMapper;
    @Autowired
    private TCarrierOrderOperateLogsMapper tCarrierOrderOperateLogsMapper;
    @Autowired
    private TCarrierOrderEventsMapper tCarrierOrderEventsMapper;
    @Autowired
    private TCarrierOrderOrderRelMapper tCarrierOrderOrderRelMapper;
    @Autowired
    private TCarrierOrderGoodsMapper tCarrierOrderGoodsMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private TPaymentMapper tPaymentMapper;
    @Autowired
    private TReceivementMapper tReceivementMapper;
    @Autowired
    private ExtVehicleSettlementBiz extVehicleSettlementBiz;
    @Autowired
    private DemandOrderBiz demandOrderBiz;
    @Autowired
    private RabbitMqPublishBiz rabbitMqPublishBiz;
    @Autowired
    private CarrierOrderCommonBiz carrierOrderCommonBiz;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TCarrierOrderOtherFeeItemMapper tCarrierOrderOtherFeeItemMapper;
    @Autowired
    private TDemandReceivementMapper tDemandReceivementMapper;
    @Autowired
    private TExtVehicleSettlementMapper tExtVehicleSettlementMapper;
    @Autowired
    private TVehicleSettlementRelationMapper tVehicleSettlementRelationMapper;
    @Autowired
    private TDemandPaymentMapper tDemandPaymentMapper;
    @Autowired
    private TCarrierOrderLocationMapper tCarrierOrderLocationMapper;
    @Autowired
    private TReachManagementMapper tReachManagementMapper;
    @Autowired
    private TSettleStatementItemMapper tSettleStatementItemMapper;
    @Autowired
    private WorkOrderBiz workOrderBiz;
    @Autowired
    private TrayOrderServiceClient trayOrderServiceClient;
    @Resource
    private WarehouseStockClient warehouseStockClient;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private ShippingOrderCommonBiz shippingOrderCommonBiz;
    @Resource
    private ReservationOrderCommonBiz reservationOrderCommonBiz;
    @Resource
    private LeyiCommonAddressServiceApi leyiCommonAddressServiceApi;

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private TCarrierOrderLoadCodeMapper tCarrierOrderLoadCodeMapper;
    @Resource
    private TExtDemandOrderRelationMapper tExtDemandOrderRelationMapper;



    /**
     * 查询云盘运单列表
     *
     * @param requestModel
     */
    public PageInfo<SearchCarrierOrderListForLeYiResponseModel> searchCarrierOrderListForLeYi(SearchCarrierOrderListForLeYiRequestModel requestModel) {
        //如果运单生成时间选项为空的话，默认为当前-90
        if (ListUtils.isEmpty(requestModel.getDemandOrderCodeList()) && ListUtils.isEmpty(requestModel.getCarrierOrderCodeList())) {
            //非批量查询情况下
            if (StringUtils.isBlank(requestModel.getDispatchTimeFrom()) && StringUtils.isBlank(requestModel.getDispatchTimeTo())) {
                //选择待到达提货地、待提货、待到达卸货地、待卸货状态筛选时，运单生成时间允许清空
                List<Integer> statusList = Arrays.asList(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey(), CarrierOrderStatusEnum.WAIT_LOAD.getKey(),
                        CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey(), CarrierOrderStatusEnum.WAIT_UNLOAD.getKey());
                Integer status = null;
                if (ListUtils.isNotEmpty(requestModel.getStatusList())){
                    status = requestModel.getStatusList().stream().filter(item -> !statusList.contains(item)).findFirst().orElse(null);
                }
                if (ListUtils.isEmpty(requestModel.getStatusList()) || status != null) {
                    requestModel.setDispatchTimeFrom(DateUtils.dateToString(new Date(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
                    Calendar now = Calendar.getInstance();
                    now.add(Calendar.DAY_OF_MONTH, -90);
                    requestModel.setDispatchTimeTo(DateUtils.dateToString(now.getTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
                }
            }
        } else {
            //以批量查询条件为准,清掉默认的时间条件
            requestModel.setDispatchTimeFrom("");
            requestModel.setDispatchTimeTo("");
        }

        //查询条件含有司机车辆信息
        List<Long> vehicleCarrierOrderIdList;
        Map<Long, TCarrierOrderVehicleHistory> carrierOrderVehicleHistoryMap = new HashMap<>();
        if (StringUtils.isBlank(requestModel.getCarrierOrderIds())
                && (StringUtils.isNotBlank(requestModel.getVehicleNo())
                || StringUtils.isNotBlank(requestModel.getDriver())
                || StringUtils.isNotBlank(requestModel.getExpectArrivalTimeFrom())
                || StringUtils.isNotBlank(requestModel.getExpectArrivalTimeTo())
                || StringUtils.isNotBlank(requestModel.getExpectLoadTimeFrom())
                || StringUtils.isNotBlank(requestModel.getExpectLoadTimeTo()))){
            //查询地址信息
            SearchCarrierOrderVehicleHistoryModel vehicleHistoryModel = MapperUtils.mapper(requestModel, SearchCarrierOrderVehicleHistoryModel.class);
            List<TCarrierOrderVehicleHistory> vehicleHistoryList = tCarrierOrderVehicleHistoryMapper.searchListByCarrierOrderParams(vehicleHistoryModel);
            //不存在，直接返回
            if (ListUtils.isEmpty(vehicleHistoryList)) {
                return new PageInfo<>(new ArrayList<>());
            }
            //存在，数据聚合处理
            vehicleCarrierOrderIdList = new ArrayList<>();
            for (TCarrierOrderVehicleHistory vehicleHistory : vehicleHistoryList) {
                carrierOrderVehicleHistoryMap.put(vehicleHistory.getCarrierOrderId(), vehicleHistory);
                if (!vehicleCarrierOrderIdList.contains(vehicleHistory.getCarrierOrderId())) {
                    vehicleCarrierOrderIdList.add(vehicleHistory.getCarrierOrderId());
                }
            }
            requestModel.setCarrierOrderIdList(vehicleCarrierOrderIdList);
        }

        //设置要排除的运单号
        requestModel.setExcludeCarrierOrderIdList(workOrderBiz.getNeedExcludedOrderIds(WorkOrderTypeEnum.CARRIER_ORDER_TYPE, CommonConstant.INTEGER_ONE, null, null));

        //分页查询获取运单Id
        requestModel.enablePaging();
        List<Long> carrierOrderIdList = tCarrierOrderMapper.searchCarrierOrderIdsForLeYiManagement(requestModel);
        PageInfo carrierOrderList = new PageInfo(carrierOrderIdList);

        if (ListUtils.isNotEmpty(carrierOrderIdList)) {
            List<SearchCarrierOrderListForLeYiResponseModel> searchCarrierOrderListResponseModels =
                    searchCarrierOrderListForLeYiByIds(carrierOrderIdList, carrierOrderVehicleHistoryMap);
            carrierOrderList.setList(searchCarrierOrderListResponseModels);
        }
        return carrierOrderList;
    }

    public List<SearchCarrierOrderListForLeYiResponseModel> searchCarrierOrderListForLeYiByIds(List<Long> carrierOrderIdList,
                                                                                               Map<Long, TCarrierOrderVehicleHistory> carrierOrderVehicleHistoryMap) {
        String carrierOrderIds = StringUtils.listToString(carrierOrderIdList, ',');
        //根据运单id查询运单信息
        List<SearchCarrierOrderListForLeYiResponseModel> searchCarrierOrderListResponseModels = tCarrierOrderMapper.searchCarrierOrderForLeYiManagement(carrierOrderIds);

        //获取车主id
        List<Long> companyCarrierIdList = new ArrayList<>();
        for (SearchCarrierOrderListForLeYiResponseModel responseModel : searchCarrierOrderListResponseModels) {
            if (!companyCarrierIdList.contains(responseModel.getCompanyCarrierId())){
                companyCarrierIdList.add(responseModel.getCompanyCarrierId());
            }
        }

        //根据运单id查询票据信息
        List<GetTicketsAmountByCarrierOrderIdsModel> ticketsAmountList=tCarrierOrderTicketsMapper.getTicketsAmountByCarrierOrderIds(carrierOrderIds);
        Map<Long, Integer> carrierOrderTicketsAmountMap = ticketsAmountList.stream().collect(
                Collectors.toMap(GetTicketsAmountByCarrierOrderIdsModel::getCarrierOrderId, GetTicketsAmountByCarrierOrderIdsModel::getAmount));

        //不存在车辆相关筛选条件，则根据运单id查询运单司机车辆信息
        if (MapUtils.isEmpty(carrierOrderVehicleHistoryMap)) {
            List<TCarrierOrderVehicleHistory> carrierOrderVehicleHistoryList = tCarrierOrderVehicleHistoryMapper.getValidByCarrierOrderIds(carrierOrderIds);
            carrierOrderVehicleHistoryList.forEach(item -> carrierOrderVehicleHistoryMap.put(item.getCarrierOrderId(), item));
        }

        //根据运单id查询车主结算信息
        List<TPayment> tPaymentList = tPaymentMapper.getByCarrierOrderIds(carrierOrderIds);
        Map<Long, TPayment> paymentMap = new HashMap<>();
        tPaymentList.forEach(item -> paymentMap.put(item.getCarrierOrderId(), item));

        //查询运单其他费用
        List<CarrierOrderOtherFeeItemModel> carrierOrderOtherFeeList = tCarrierOrderOtherFeeItemMapper.selectOtherFeeItemByCarrierIds(carrierOrderIds);
        Map<Long, List<CarrierOrderOtherFeeItemModel>> carrierOrderOtherFeeMap = carrierOrderOtherFeeList.stream().collect(Collectors.groupingBy(CarrierOrderOtherFeeItemModel::getCarrierOrderId));

        //查询运单定位信息
        List<TCarrierOrderLocation> tCarrierOrderLocationList = tCarrierOrderLocationMapper.getByCarrierOrderIds(carrierOrderIds);
        Map<Long, List<TCarrierOrderLocation>> carrierOrderLocationMap = tCarrierOrderLocationList.stream().collect(Collectors.groupingBy(TCarrierOrderLocation::getCarrierOrderId));

        //拼接数据
        TCarrierOrderVehicleHistory tCarrierOrderVehicleHistory;
        List<CarrierOrderOtherFeeItemModel> carrierOrderOtherFeeItemModels;
        List<TCarrierOrderLocation> carrierOrderLocations;
        TPayment tPayment;
        for (SearchCarrierOrderListForLeYiResponseModel responseModel : searchCarrierOrderListResponseModels) {
            //票据数量
            responseModel.setCarrierOrderTicketsAmount(Optional.ofNullable(carrierOrderTicketsAmountMap.get(responseModel.getCarrierOrderId())).orElse(CommonConstant.INTEGER_ZERO));

            //车辆司机信息
            tCarrierOrderVehicleHistory = carrierOrderVehicleHistoryMap.get(responseModel.getCarrierOrderId());
            if (tCarrierOrderVehicleHistory != null) {
                responseModel.setVehicleNo(tCarrierOrderVehicleHistory.getVehicleNo());
                responseModel.setDriverName(tCarrierOrderVehicleHistory.getDriverName());
                responseModel.setDriverMobile(tCarrierOrderVehicleHistory.getDriverMobile());
                responseModel.setDriverIdentity(tCarrierOrderVehicleHistory.getDriverIdentity());
                responseModel.setExpectArrivalTime(tCarrierOrderVehicleHistory.getExpectArrivalTime());
                responseModel.setExpectLoadTime(tCarrierOrderVehicleHistory.getExpectLoadTime());
            }

            //结算信息
            tPayment = paymentMap.get(responseModel.getCarrierOrderId());
            if (tPayment != null) {
                responseModel.setCarrierPaymentPriceType(tPayment.getPriceType());
                responseModel.setCarrierSettlementAmount(tPayment.getSettlementAmount());
                responseModel.setCarrierSettlementCostTotal(tPayment.getSettlementCostTotal());
            }

            //运单临时费用
            carrierOrderOtherFeeItemModels = carrierOrderOtherFeeMap.get(responseModel.getCarrierOrderId());
            if (ListUtils.isNotEmpty(carrierOrderOtherFeeItemModels)) {
                responseModel.setOtherFee(carrierOrderOtherFeeItemModels);
            }

            //运单定位信息
            carrierOrderLocations = carrierOrderLocationMap.get(responseModel.getCarrierOrderId());
            if (ListUtils.isNotEmpty(carrierOrderLocations)){
                for (TCarrierOrderLocation orderLocation : carrierOrderLocations) {
                    if (CarrierOrderLocationTypeEnum.LOAD.getKey().equals(orderLocation.getLocationType())){
                        responseModel.setLoadLocation(orderLocation.getLocation());
                    }else if (CarrierOrderLocationTypeEnum.UNLOAD.getKey().equals(orderLocation.getLocationType())){
                        responseModel.setUnloadLocation(orderLocation.getLocation());
                    }
                }
            }
            responseModel.setEnableExtCarrierOrder(CommonConstant.ZERO);
            // 回收入
            if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(responseModel.getDemandOrderEntrustType())){
                //运单状态：≠已取消/已放空；
                Integer ifCancel = responseModel.getIfCancel();
                Integer ifEmpty = responseModel.getIfEmpty();
                if (!CommonConstant.INTEGER_ONE.equals(ifCancel) && !CommonConstant.INTEGER_ONE.equals(ifEmpty)){
                    // 非补单
                    if (!responseModel.getIfExtCarrierOrder().equals(CommonConstant.INTEGER_ONE)){
                        //查询是否货物是共享托盘
                        List<TCarrierOrderGoods> tCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderId(responseModel.getCarrierOrderId());
                        if (ListUtils.isNotEmpty(tCarrierOrderGoods)){
                            responseModel.setEnableExtCarrierOrder(CommonConstant.ONE);
                        }
                    }
                }
            }
        }
        return searchCarrierOrderListResponseModels;
    }

    /**
     * 云盘运单详情页
     *
     * @param  requestModel
     */
    public CarrierOrderDetailForLeYiResponseModel carrierOrderDetailForLeYi(CarrierOrderDetailRequestModel requestModel) {
        if (requestModel == null) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        //查询运单详情
        CarrierOrderDetailForLeYiResponseModel responseModel = tCarrierOrderMapper.getCarrierOrderDetailForLeYiManagementById(requestModel.getCarrierOrderId());
        if (responseModel == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //查询车主结算费用
        TPayment tPayment = tPaymentMapper.getByCarrierOrderId(responseModel.getCarrierOrderId());
        if (tPayment != null){
            responseModel.getCarrierOrderDetailFreightFeeInfo().setCarrierPriceType(tPayment.getPriceType());
            responseModel.getCarrierOrderDetailFreightFeeInfo().setCarrierSettlementCostTotal(tPayment.getSettlementCostTotal());
        }

        //查询货主结算费用
        TReceivement tReceivement = tReceivementMapper.getByCarrierOrderId(responseModel.getCarrierOrderId());
        if (tReceivement != null){
            responseModel.getCarrierOrderDetailFreightFeeInfo().setEntrustPriceType(tReceivement.getPriceType());
            responseModel.getCarrierOrderDetailFreightFeeInfo().setEntrustSettlementCostTotal(tReceivement.getSettlementCostTotal());
        }

        //查询车主信息
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(responseModel.getCompanyCarrierId());
        if (tCompanyCarrier != null && tCompanyCarrier.getValid().equals(IfValidEnum.VALID.getKey())){
            responseModel.setLevel(tCompanyCarrier.getLevel());
        }

        //运单临时费用
        BigDecimal otherFee = CommonConstant.BIG_DECIMAL_ZERO;
        List<CarrierOrderOtherFeeItemModel> carrierOrderOtherFeeList = tCarrierOrderOtherFeeItemMapper.selectOtherFeeItemByCarrierIds(LocalStringUtil.listTostring(Collections.singletonList(responseModel.getCarrierOrderId()), ','));
        if (ListUtils.isNotEmpty(carrierOrderOtherFeeList)) {
            for (CarrierOrderOtherFeeItemModel carrierOrderOtherFeeItemModel : carrierOrderOtherFeeList) {
                otherFee = otherFee.add(carrierOrderOtherFeeItemModel.getFeeAmount());
            }
        }
        responseModel.getCarrierOrderDetailFreightFeeInfo().setOtherFee(otherFee);

        //查询运单定位信息
        List<TCarrierOrderLocation> tCarrierOrderLocationList = tCarrierOrderLocationMapper.getByCarrierOrderIds(responseModel.getCarrierOrderId().toString());
        if (ListUtils.isNotEmpty(tCarrierOrderLocationList)){
            for (TCarrierOrderLocation orderLocation : tCarrierOrderLocationList) {
                if (CarrierOrderLocationTypeEnum.LOAD.getKey().equals(orderLocation.getLocationType())){
                    responseModel.getCarrierOrderDetailBasicInfo().setLoadLocation(orderLocation.getLocation());
                }else if (CarrierOrderLocationTypeEnum.UNLOAD.getKey().equals(orderLocation.getLocationType())){
                    responseModel.getCarrierOrderDetailBasicInfo().setUnloadLocation(orderLocation.getLocation());
                }
            }
        }

        //通过运单id获取触达信息
        TReachManagement tReachManagement = tReachManagementMapper.selectByCarrierOrderId(requestModel.getCarrierOrderId());
        responseModel.setReachManagementInfoModel(MapperUtils.mapper(tReachManagement, ReachManagementInfoModel.class));

        //车辆司机排序
        responseModel.getCarrierOrderDetailVehicleDriverInfo().sort((o1, o2) -> o2.getCreatedTime().compareTo(o1.getCreatedTime()));
        return responseModel;
    }

    /**
     * 云盘运单修改地址详情（回收入库、调拨、退货调拨类型&签收前&待入库，才能修改卸货地址）
     *
     * @param requestModel 要修改地址的运单id
     * @return 要修改地址的运单id
     */
    public List<UpdateUnloadAddressDetailResponseModel> updateCarrierOrderUnloadAddressDetail(CarrierOrderUpUnloadAddrRequestModel requestModel) {

        String carrierOrderIdsStr = StringUtils.join(requestModel.getCarrierOrderId(), ',');
        List<UpdateUnloadAddressDetailResponseModel> detailList = tCarrierOrderMapper.updateCarrierOrderUnloadAddressDetail(carrierOrderIdsStr);
        if (ListUtils.isEmpty(detailList)) {
            throw new BizException(CarrierDataExceptionEnum.UPDATE_UNLOAD_ADDRESS_ERROR);
        }

        //调云仓接口查询运单是否可以修改
        List<String> carrierOrderCodes = detailList.stream().map(UpdateUnloadAddressDetailResponseModel::getCarrierOrderCode).collect(Collectors.toList());
        List<CheckChangeUnloadWarehouseResponseModel> warehouseOrderList = warehouseStockClient.checkChangeUnloadWarehouse(carrierOrderCodes);
        if (ListUtils.isEmpty(warehouseOrderList)){
            throw new BizException(CarrierDataExceptionEnum.UPDATE_UNLOAD_ADDRESS_ERROR);
        }
        Map<String, String> warehouseOrderMap = new HashMap<>();
        warehouseOrderList.forEach(item -> warehouseOrderMap.put(item.getCarrierOrderCode(), item.getIfChangeUploadWarehouse()));

        //此注释代码，待对接云仓驳回操作后放开
        //查询云仓入库产生的运单纠错信息
//        Map<Long, TCarrierOrderCorrect> carrierOrderCorrectMap = tCarrierOrderCorrectMapper.getByCarrierOrderIds(carrierOrderIdsStr).stream().collect(Collectors.toMap(TCarrierOrderCorrect::getCarrierOrderId, item -> item));

        //校验运单状态
        for (UpdateUnloadAddressDetailResponseModel carrierOrderDetail : detailList) {
            //非云盘单子不能操作
            if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(carrierOrderDetail.getDemandOrderSource())) {
                throw new BizException(CarrierDataExceptionEnum.UPDATE_UNLOAD_ADDRESS_ERROR);
            }
            //仅回收入库、调拨、退货调拨类型
            if (!EntrustTypeEnum.RECYCLE_IN.getKey().equals(carrierOrderDetail.getEntrustType())
                    && !EntrustTypeEnum.TRANSFERS.getKey().equals(carrierOrderDetail.getEntrustType())
                    && !EntrustTypeEnum.RETURN_GOODS_TRANSFERS.getKey().equals(carrierOrderDetail.getEntrustType())) {
                throw new BizException(CarrierDataExceptionEnum.UPDATE_UNLOAD_ADDRESS_ERROR);
            }

            //云仓是否入库
//            TCarrierOrderCorrect tCarrierOrderCorrect = carrierOrderCorrectMap.get(carrierOrderDetail.getCarrierOrderId());
//            if (tCarrierOrderCorrect != null) {
//                throw new BizException(CarrierDataExceptionEnum.UPDATE_UNLOAD_ADDRESS_ERROR_FOR_STOCK_IN);
//            }

            //判断云仓是否可以修改
            if (warehouseOrderMap.get(carrierOrderDetail.getCarrierOrderCode()) == null){
                throw new BizException(CarrierDataExceptionEnum.UPDATE_UNLOAD_ADDRESS_ERROR);
            }
            if (CommonConstant.TWO.equals(warehouseOrderMap.get(carrierOrderDetail.getCarrierOrderCode()))){
                throw new BizException(CarrierDataExceptionEnum.UPDATE_UNLOAD_ADDRESS_ERROR);
            }else if (CommonConstant.THREE.equals(warehouseOrderMap.get(carrierOrderDetail.getCarrierOrderCode()))){
                throw new BizException(CarrierDataExceptionEnum.UPDATE_UNLOAD_ADDRESS_ERROR_FOR_STOCK_IN);
            }
        }

        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(carrierOrderIdsStr);
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }
        return detailList;
    }

    /**
     * 云盘运单确认修改地址
     * @param requestModel
     */
    @Transactional
    public void updateCarrierOrderUnloadAddressConfirm(UpdateCarrierOrderUnloadAddressConfirmRequestModel requestModel) {
        CarrierOrderUpUnloadAddrRequestModel detailRequestModel = new CarrierOrderUpUnloadAddrRequestModel();
        detailRequestModel.setCarrierOrderId(requestModel.getCarrierOrderIds());
        List<UpdateUnloadAddressDetailResponseModel> detailList = this.updateCarrierOrderUnloadAddressDetail(detailRequestModel);

        //运单地址信息
        TCarrierOrderAddress carrierOrderAddressUp;
        List<TCarrierOrderAddress> carrierOrderAddressUpList = new ArrayList<>();

        //运单操作日志信息
        List<TCarrierOrderOperateLogs> carrierOrderOperateLogsList = new ArrayList<>();

        //同步云盘云仓mq信息
        List<String> synWarehouseCarrierOrderCodeList = new ArrayList<>();
        UpdateCarrierOrderUnloadAddressSynLeYiModel updateCarrierOrderUnloadAddressSynLeYiModel;
        List<UpdateCarrierOrderUnloadAddressSynLeYiModel> updateCarrierOrderUnloadAddressSynToLeYiModelList = new ArrayList<>();
        for (UpdateUnloadAddressDetailResponseModel detail : detailList) {
            //运单地址信息
            carrierOrderAddressUp = new TCarrierOrderAddress();
            carrierOrderAddressUp.setId(detail.getCarrierOrderAddressId());
            carrierOrderAddressUp.setUnloadProvinceId(requestModel.getUnloadProvinceId());
            carrierOrderAddressUp.setUnloadProvinceName(requestModel.getUnloadProvinceName());
            carrierOrderAddressUp.setUnloadCityId(requestModel.getUnloadCityId());
            carrierOrderAddressUp.setUnloadCityName(requestModel.getUnloadCityName());
            carrierOrderAddressUp.setUnloadAreaId(requestModel.getUnloadAreaId());
            carrierOrderAddressUp.setUnloadAreaName(requestModel.getUnloadAreaName());
            carrierOrderAddressUp.setUnloadDetailAddress(requestModel.getUnloadDetailAddress());
            carrierOrderAddressUp.setUnloadWarehouse(requestModel.getUnloadWarehouse());
            carrierOrderAddressUp.setReceiverName(requestModel.getReceiverName());
            carrierOrderAddressUp.setReceiverMobile(requestModel.getReceiverMobile());
            carrierOrderAddressUp.setUnloadAddressUpdateType(requestModel.getUnloadAddressUpdateType());
            commonBiz.setBaseEntityModify(carrierOrderAddressUp, BaseContextHandler.getUserName());
            carrierOrderAddressUpList.add(carrierOrderAddressUp);

            //记录操作日志
            String remark = (StringUtils.isBlank(detail.getUnloadWarehouse()) ? "" : "【" + detail.getUnloadWarehouse() + "】") +
                    detail.getUnloadProvinceName() +
                    detail.getUnloadCityName() +
                    detail.getUnloadAreaName() +
                    (StringUtils.isBlank(detail.getUnloadDetailAddress()) ? "" : detail.getUnloadDetailAddress());
            carrierOrderOperateLogsList.add(carrierOrderCommonBiz.getCarrierOrderOperateLogs(detail.getCarrierOrderId(), CarrierOrderOperateLogsTypeEnum.UPDATE_UNLOAD_ADDRESS, BaseContextHandler.getUserName(), remark));

            //同步云仓
            synWarehouseCarrierOrderCodeList.add(detail.getCarrierOrderCode());

            //同步云盘（回收入库类型）
            if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(detail.getEntrustType())){
                updateCarrierOrderUnloadAddressSynLeYiModel = new UpdateCarrierOrderUnloadAddressSynLeYiModel();
                updateCarrierOrderUnloadAddressSynLeYiModel.setCarrierOrderCode(detail.getCarrierOrderCode());
                updateCarrierOrderUnloadAddressSynLeYiModel.setWarehouseId(requestModel.getWarehouseId());
                updateCarrierOrderUnloadAddressSynLeYiModel.setUserName(BaseContextHandler.getUserName());
                updateCarrierOrderUnloadAddressSynToLeYiModelList.add(updateCarrierOrderUnloadAddressSynLeYiModel);
            }
        }

        //更新地址表
        if (ListUtils.isNotEmpty(carrierOrderAddressUpList)) {
            tCarrierOrderAddressMapper.batchUpdate(carrierOrderAddressUpList);
        }

        //新增运单操作日志表
        if (ListUtils.isNotEmpty(carrierOrderOperateLogsList)) {
            tCarrierOrderOperateLogsMapper.batchInsertSelective(carrierOrderOperateLogsList);
        }

        //预约单解除关联运单
        reservationOrderCommonBiz.disassociateCarrierOrder(requestModel.getCarrierOrderIds());

        //异步修改卸货地址经纬度
        AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.updateCarrierOrderAddressLonAndLat(carrierOrderAddressUpList, true));

        //将修改后的地址同步给云仓
        if (ListUtils.isNotEmpty(synWarehouseCarrierOrderCodeList)) {
            UpdateCarrierOrderUnloadAddressSynWarehouseModel synWarehouseModel = new UpdateCarrierOrderUnloadAddressSynWarehouseModel();
            synWarehouseModel.setCarrierOrderCodes(synWarehouseCarrierOrderCodeList);
            synWarehouseModel.setWarehouseId(requestModel.getWarehouseId());
            synWarehouseModel.setUserName(BaseContextHandler.getUserName());
            rabbitMqPublishBiz.updateCarrierOrderUnloadAddressSynToWarehouse(synWarehouseModel);
        }
        //将修改后的地址同步给云盘（回收入库类型）
        if (ListUtils.isNotEmpty(updateCarrierOrderUnloadAddressSynToLeYiModelList)) {
            for (UpdateCarrierOrderUnloadAddressSynLeYiModel carrierOrderUnloadAddressSynToLeYiModel : updateCarrierOrderUnloadAddressSynToLeYiModelList) {
                rabbitMqPublishBiz.updateCarrierOrderUnloadAddressSynToLeYi(carrierOrderUnloadAddressSynToLeYiModel);
            }
        }
    }

    /**
     * 云盘运单纠错详情
     *
     * @param requestModel 运单id
     * @return 纠错详情信息
     */
    public CarrierOrderCorrectDetailResponseModel carrierOrderCorrectDetail(CarrierOrderCorrectDetailRequestModel requestModel) {
        CarrierOrderCorrectDetailResponseModel carrierOrderDetail = tCarrierOrderMapper.carrierOrderCorrectDetail(requestModel.getCarrierOrderId());
        if (CommonConstant.ONE.equals(requestModel.getOperateType()) && !CorrectStatusEnum.WAIT_CORRECT.getKey().equals(carrierOrderDetail.getCorrectStatus())) {
            throw new BizException(CarrierDataExceptionEnum.CORRECT_STATUS_ERROR);
        }
        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(requestModel.getCarrierOrderId().toString());
        if (!workOrderMap.isEmpty()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }
        //查询运单其他费用
        List<CarrierOrderOtherFeeItemModel> carrierOrderOtherFeeList = tCarrierOrderOtherFeeItemMapper.selectOtherFeeItemByCarrierIds(ConverterUtils.toString(requestModel.getCarrierOrderId()));

        //查询入库单
        List<GetTicketsResponseModel> carrierOrderTicketsList = tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(requestModel.getCarrierOrderId(), CarrierOrderTicketsTypeEnum.CARRIER_ORDER_STOCK_IN_TICKETS.getKey());
        List<String> stockInTicketsList = carrierOrderTicketsList.stream().map(GetTicketsResponseModel::getImagePath).collect(Collectors.toList());
        carrierOrderDetail.setStockInTicketsList(stockInTicketsList);
        //查询签收单/回单
        List<GetTicketsResponseModel> carrierOrderSignTicketsList = tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(requestModel.getCarrierOrderId(), CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey());
        List<String> signTicketsList = carrierOrderSignTicketsList.stream().map(GetTicketsResponseModel::getImagePath).collect(Collectors.toList());
        carrierOrderDetail.setSignTicketsList(signTicketsList);
        //查询外部车辆结算信息
        TExtVehicleSettlement tExtVehicleSettlement = tExtVehicleSettlementMapper.getByCarrierOrderId(carrierOrderDetail.getCarrierOrderId());
        if (tExtVehicleSettlement != null) {
            carrierOrderDetail.setExtVehiclePayStatus(tExtVehicleSettlement.getStatus());
        }
        //运单是否关联对账单,关联对账单了就用对账单的结算数量
        if (carrierOrderDetail.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
            TSettleStatementItem tSettleStatementItem = tSettleStatementItemMapper.selectValidByCarrierOrderId(carrierOrderDetail.getCarrierOrderId());
            if (tSettleStatementItem != null) {
                carrierOrderDetail.setCarrierSettlementAmount(tSettleStatementItem.getSettlementAmount());
            }
        }
        //是否关联司机对账
        carrierOrderDetail.setRelDriverSettlement(carrierOrderCommonBiz.existSettlementData(ConverterUtils.toString(carrierOrderDetail.getCarrierOrderId())));
        //查询运单车主信息
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(carrierOrderDetail.getCompanyCarrierId());
        if (tCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(tCompanyCarrier.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        carrierOrderDetail.setIsOurCompany(tCompanyCarrier.getLevel());

        //匹配车主价格,二次纠错不需要
        if (carrierOrderDetail.getAbnormalAmount() == null || carrierOrderDetail.getAbnormalAmount().compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO) {

            //车主实际结算数量
            BigDecimal carrierSettlementAmount = BigDecimal.ZERO;
            if (SettlementTonnageEnum.LOAD.getKey().equals(carrierOrderDetail.getCarrierSettlement())) {
                carrierSettlementAmount = carrierOrderDetail.getLoadAmount();
            } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(carrierOrderDetail.getCarrierSettlement())) {
                carrierSettlementAmount = carrierOrderDetail.getStockInCount();
            } else if (SettlementTonnageEnum.SIGN.getKey().equals(carrierOrderDetail.getCarrierSettlement())) {
                carrierSettlementAmount = carrierOrderDetail.getStockInCount();
            } else if (SettlementTonnageEnum.EXPECT.getKey().equals(carrierOrderDetail.getCarrierSettlement())) {
                carrierSettlementAmount = carrierOrderDetail.getExpectAmount();
            }

            //结算数量大于0
            if (carrierSettlementAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                AddressAreaExistRequestModel areaModel = new AddressAreaExistRequestModel();
                areaModel.setFromAreaId(carrierOrderDetail.getLoadAreaId());
                areaModel.setToAreaId(carrierOrderDetail.getUnloadAreaId());
                //车主运价匹配需要用到的信息
                MatchCarrierPriceResultModel matchCarrierPriceStepOneModel = carrierOrderCommonBiz.matchCarrierPriceStepOneResult(Collections.singletonList(carrierOrderDetail.getCompanyCarrierId()), Collections.singletonList(areaModel));

                if (matchCarrierPriceStepOneModel != null &&
                        matchCarrierPriceStepOneModel.getEntrustCarrierFreightMap().get(carrierOrderDetail.getCompanyCarrierId()) != null
                        && matchCarrierPriceStepOneModel.getEntrustCarrierFreightMap().get(carrierOrderDetail.getCompanyCarrierId()).get(carrierOrderDetail.getEntrustType()) != null) {

                    Map<Integer, CarrierFreightCarrierModel> entrustFreightMap = matchCarrierPriceStepOneModel.getEntrustCarrierFreightMap().get(carrierOrderDetail.getCompanyCarrierId());
                    CarrierFreightCarrierModel carrierFreightCarrierModel = entrustFreightMap.get(carrierOrderDetail.getEntrustType());
                    GetCarrierPriceModel getCarrierPriceModel = new GetCarrierPriceModel();
                    getCarrierPriceModel.setGoodsAmount(carrierSettlementAmount);
                    getCarrierPriceModel.setGoodsUnit(carrierOrderDetail.getGoodsUnit());
                    getCarrierPriceModel.setEntrustType(carrierOrderDetail.getEntrustType());
                    getCarrierPriceModel.setLoadAreaId(carrierOrderDetail.getLoadAreaId());
                    getCarrierPriceModel.setUnloadAreaId(carrierOrderDetail.getUnloadAreaId());
                    getCarrierPriceModel.setExpectMileage(carrierOrderDetail.getExpectMileage());
                    carrierOrderCommonBiz.matchCarrierPrice(matchCarrierPriceStepOneModel.getTRouteDistanceConfigs(), matchCarrierPriceStepOneModel.getCarrierFreightConfigSchemeMap(), getCarrierPriceModel, carrierFreightCarrierModel);

                    //设置车主价格
                    if (getCarrierPriceModel.getCarrierPrice() != null
                            && getCarrierPriceModel.getCarrierPrice().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                        carrierOrderDetail.setCarrierFreightType(getCarrierPriceModel.getCarrierPriceType());
                        carrierOrderDetail.setCarrierFreight(getCarrierPriceModel.getCarrierPrice());
                    }
                }
            }
        }
        //其他车主才需要展示
        if (IsOurCompanyEnum.OTHER_COMPANY.getKey().equals(tCompanyCarrier.getLevel())) {
            //临时费用
            BigDecimal otherFee = BigDecimal.ZERO;
            for (CarrierOrderOtherFeeItemModel otherFeeItemModel : carrierOrderOtherFeeList) {
                otherFee = otherFee.add(otherFeeItemModel.getFeeAmount());
            }
            carrierOrderDetail.setOtherFee(otherFee);
        }
        return carrierOrderDetail;
    }

    /**
     * 云盘运单确认纠错
     *
     * @param requestModel
     */
    @Transactional
    public void carrierOrderCorrectConfirm(CarrierOrderCorrectConfirmRequestModel requestModel) {
        TCarrierOrderCorrect tCarrierOrderCorrect = tCarrierOrderCorrectMapper.getByCarrierOrderId(requestModel.getCarrierOrderId());
        if (tCarrierOrderCorrect == null || tCarrierOrderCorrect.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CORRECT_INFO_EMPTY);
        }
        if (!CorrectStatusEnum.WAIT_CORRECT.getKey().equals(tCarrierOrderCorrect.getCorrectStatus())) {
            throw new BizException(CarrierDataExceptionEnum.CORRECT_STATUS_ERROR);
        }

        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (tCarrierOrder == null || tCarrierOrder.getValid().equals(IfValidEnum.INVALID.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //非云盘单子不能操作
        if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(tCarrierOrder.getDemandOrderSource())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //是否第二次纠错
        boolean secondCorrect = false;
        BigDecimal stockInCount = tCarrierOrderCorrect.getStockInCount();
        //有云仓异常数, 入库数-异常数
        if (tCarrierOrder.getAbnormalAmount() != null && tCarrierOrder.getAbnormalAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
            stockInCount = stockInCount.subtract(tCarrierOrder.getAbnormalAmount());
            secondCorrect = true;
        }

        //是否关联结算信息, 对账单,外部车辆结算,自主运费结算
        boolean noRelSettleStatement = true;
        if (tCarrierOrder.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
            noRelSettleStatement = false;
        } else if (extVehicleSettlementBiz.checkAlreadyPaid(tCarrierOrder.getId())) {
            noRelSettleStatement = false;
        } else if (carrierOrderCommonBiz.existSettlementData(ConverterUtils.toString(tCarrierOrder.getId()))) {
            noRelSettleStatement = false;
        }

        //未关联结算时时校验回单必填
        if (noRelSettleStatement) {
            if (ListUtils.isEmpty(requestModel.getSignTicketsList()) || requestModel.getSignTicketsList().size() > CommonConstant.INTEGER_TWO) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CORRECT_TICKETS_IS_NULL);
            }
        }

        //关联结算后后不允许修改费用
        if (!noRelSettleStatement) {
            if (requestModel.getCarrierFreightType() != null && requestModel.getCarrierFreight() != null
                    && (!tCarrierOrder.getCarrierPriceType().equals(requestModel.getCarrierFreightType())
                    || tCarrierOrder.getCarrierPrice().compareTo(requestModel.getCarrierFreight()) != CommonConstant.INTEGER_ZERO)) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CORRECT_CARRIER_PRICE_NO_CHANGE);
            }
        }

        //实提数量不能小于实卸数量（实卸数量=入库数量）
        if (requestModel.getLoadAmount().compareTo(stockInCount) < CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CORRECT_AMOUNT_ERROR);
        }

        //回收入库类型
        if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
            //云仓异常数不为空或者不为0时
            if (tCarrierOrder.getAbnormalAmount() != null && tCarrierOrder.getAbnormalAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                //实际实提数不能大于原实提数
                if (requestModel.getLoadAmount().compareTo(tCarrierOrder.getLoadAmount()) > CommonConstant.INTEGER_ZERO) {
                    throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CORRECT_LOAD_ERROR);
                }
            }
        } else if (EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
            //运单为回收出库时，实际实提数=实际卸货数，不可修改
            if (requestModel.getLoadAmount().compareTo(stockInCount) != CommonConstant.INTEGER_ZERO) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CORRECT_LOAD_EQUAL_UNLOAD);
            }
        }

        //计算运单差异数
        BigDecimal differenceAmount = requestModel.getLoadAmount().subtract(tCarrierOrder.getExpectAmount());
        //提错托盘、遗失托盘数量判断
        if (requestModel.getLoadErrorAmount() != null && requestModel.getLoadErrorAmount().compareTo(differenceAmount) > CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.LOAD_ERROR_AMOUNT_ERROR);
        }
        if (requestModel.getLoseErrorAmount() != null && requestModel.getLoseErrorAmount().compareTo(differenceAmount) > CommonConstant.INTEGER_ZERO) {
            throw new BizException(CarrierDataExceptionEnum.LOSE_ERROR_AMOUNT_ERROR);
        }
        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(requestModel.getCarrierOrderId().toString());
        if (!workOrderMap.isEmpty()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }
        Date now = new Date();

        //更新纠错信息
        TCarrierOrderCorrect carrierOrderCorrect = new TCarrierOrderCorrect();
        carrierOrderCorrect.setId(tCarrierOrderCorrect.getId());
        carrierOrderCorrect.setCorrectStatus(CorrectStatusEnum.CORRECT.getKey());
        carrierOrderCorrect.setCorrectType(requestModel.getCorrectType());
        carrierOrderCorrect.setCorrectUser(BaseContextHandler.getUserName());
        carrierOrderCorrect.setCorrectTime(now);
        carrierOrderCorrect.setLoadErrorAmount(requestModel.getLoadErrorAmount());
        carrierOrderCorrect.setLoseErrorAmount(requestModel.getLoseErrorAmount());
        commonBiz.setBaseEntityModify(carrierOrderCorrect, BaseContextHandler.getUserName());
        tCarrierOrderCorrectMapper.updateByPrimaryKeySelective(carrierOrderCorrect);

        BigDecimal loadAmount = Optional.ofNullable(tCarrierOrder.getLoadAmount()).orElse(BigDecimal.ZERO);
        //回退数 纠错后实提-纠错前实提
        BigDecimal rollbackCount = requestModel.getLoadAmount().subtract(loadAmount);
        //记录操作日志
        TCarrierOrderOperateLogs carrierOrderOperateLogs =
                carrierOrderCommonBiz.getCarrierOrderOperateLogs(tCarrierOrderCorrect.getCarrierOrderId(), CarrierOrderOperateLogsTypeEnum.CORRECT, BaseContextHandler.getUserName(), CarrierOrderOperateLogsTypeEnum.CORRECT.format(tCarrierOrder.getCarrierOrderCode(), loadAmount.stripTrailingZeros().toPlainString(), rollbackCount.stripTrailingZeros().toPlainString()));
        tCarrierOrderOperateLogsMapper.insertSelective(carrierOrderOperateLogs);

        //更新运单上纠错状态
        TCarrierOrder upCarrierOrder = new TCarrierOrder();
        upCarrierOrder.setId(tCarrierOrder.getId());
        upCarrierOrder.setCorrectStatus(CorrectStatusEnum.CORRECT.getKey());
        //第二次纠错,把第一次纠错的实提实卸赋值给预计提卸
        if (secondCorrect) {
            upCarrierOrder.setLoadAmountExpect(tCarrierOrder.getLoadAmount());
            upCarrierOrder.setUnloadAmountExpect(tCarrierOrder.getUnloadAmount());
        }
        commonBiz.setBaseEntityModify(upCarrierOrder, BaseContextHandler.getUserName());
        tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(upCarrierOrder);

        //未关联结算时才能修改回单信息
        List<String> responseTicketsList = new ArrayList<>();
        if (noRelSettleStatement) {
            //未关联对账单,更新回单
            responseTicketsList = this.updateSignTicketsForCorrect(tCarrierOrder.getId(), tCarrierOrder.getCarrierOrderCode(), requestModel.getSignTicketsList());
        }

        Integer ifSolveError = CommonConstant.INTEGER_ONE;//是否纠错（控制云仓是否回退库存，第2次纠错不用回退）
        /*
        是否重新生成结算数据(第二次纠错实提实卸变更了,所以需要重新生成)
        1.需求单已经签收生成了结算数据
        2.没有关联车主对账单
        */
        boolean rebuildCarrierSettle = false;
        TDemandOrder tDemandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(tCarrierOrder.getDemandOrderId());
        //第二次纠错并且未关联结算才重新生成结算数据
        RLock lock = null;
        if (secondCorrect) {
            //第2次纠错不用回退
            ifSolveError = CommonConstant.INTEGER_TWO;

            //需求单已经签收
            if (tCarrierOrder.getCarrierSettleStatementStatus() <= CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()
                    && DemandOrderStatusEnum.SIGN_DISPATCH.getKey().equals(tDemandOrder.getEntrustStatus())) {
                rebuildCarrierSettle = true;
            }
            if (CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfExtDemandOrder())) {
                lock = redissonClient.getLock("DEMAND_LOCK_" + tDemandOrder.getDemandOrderCode());
                try {
                    boolean lockFlag = lock.tryLock(0, 1, TimeUnit.MINUTES);
                    if (!lockFlag) {
                        throw new BizException("系统繁忙，请稍后重试");
                    }
                } catch (InterruptedException e) {
                    throw new BizException("系统繁忙，请稍后重试");
                }
                trayOrderServiceClient.tmsLockReplenishDemand(tDemandOrder.getDemandOrderCode());
            }

        }

        //未卸货，则跨节点自动签收；卸货了，则直接签收
        this.correctAutoSign(tCarrierOrderCorrect, now, requestModel, BaseContextHandler.getUserName(), ifSolveError, responseTicketsList);

        //二次纠错时，重新生成需求单运单结算信息
        if (secondCorrect && DemandOrderStatusEnum.SIGN_DISPATCH.getKey().equals(tDemandOrder.getEntrustStatus())) {
            CreateSettlementForEntrustCorrectConsumerModel createSettlementForEntrustConsumerModel = new CreateSettlementForEntrustCorrectConsumerModel();
            createSettlementForEntrustConsumerModel.setIfUpdateCarrierSettlement(CommonConstant.ZERO);
            if (rebuildCarrierSettle) {
                createSettlementForEntrustConsumerModel.setIfUpdateCarrierSettlement(CommonConstant.ONE);
            }
            createSettlementForEntrustConsumerModel.setDemandOrderId(tDemandOrder.getId());
            demandOrderBiz.createSettlementCostForCorrect(createSettlementForEntrustConsumerModel, tCarrierOrder.getId());
        }
        if (null != lock) {
            try {
                lock.unlock();
            } catch (Exception e) {
                log.info("需求单：{}解锁失败",tCarrierOrder.getDemandOrderCode());
            }

        }

    }

    //纠错-更新回单
    public List<String> updateSignTicketsForCorrect(Long carrierOrderId, String carrierOrderCode, List<String> requestList){
        List<String> responseTicketsList = new ArrayList<>();
        List<String> addTicketsList = new ArrayList<>();
        List<GetTicketsResponseModel> carrierOrderSignTicketsList = tCarrierOrderTicketsMapper.getTicketsByCarrierOrderIdAndType(carrierOrderId, CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey());
        if (ListUtils.isNotEmpty(carrierOrderSignTicketsList)) {
            List<String> existTicketsList = new ArrayList<>();
            Map<String, Long> pathIdMap = new HashMap<>();
            for (GetTicketsResponseModel model : carrierOrderSignTicketsList) {
                existTicketsList.add(model.getImagePath());
                pathIdMap.put(model.getImagePath(), model.getImageId());
            }

            List<String> samePathList = new ArrayList<>();
            for (String path : requestList) {
                if (!existTicketsList.contains(path)){
                    addTicketsList.add(path);
                }else{
                    samePathList.add(path);
                    responseTicketsList.add(path);
                }
            }
            if (ListUtils.isNotEmpty(samePathList)){
                existTicketsList.removeAll(samePathList);
            }
            //去掉相同回单，还有则删除
            if (ListUtils.isNotEmpty(existTicketsList)){
                TCarrierOrderTickets delTickets;
                List<TCarrierOrderTickets> delTicketsList = new ArrayList<>();
                for (String path : existTicketsList) {
                    delTickets = new TCarrierOrderTickets();
                    delTickets.setId(pathIdMap.get(path));
                    delTickets.setValid(IfValidEnum.INVALID.getKey());
                    commonBiz.setBaseEntityModify(delTickets,BaseContextHandler.getUserName());
                    delTicketsList.add(delTickets);
                }
                if (ListUtils.isNotEmpty(delTicketsList)){
                    tCarrierOrderTicketsMapper.batchUpdate(delTicketsList);
                }
            }
        }else{
            addTicketsList = requestList;
        }
        //新增回单
        if (ListUtils.isNotEmpty(addTicketsList)){
            Date now = new Date();
            TCarrierOrderTickets carrierOrderTickets;
            List<TCarrierOrderTickets> ticketsList = new ArrayList<>();
            for (String path : addTicketsList) {
                carrierOrderTickets = new TCarrierOrderTickets();
                carrierOrderTickets.setCarrierOrderId(carrierOrderId);
                carrierOrderTickets.setImageType(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey());
                carrierOrderTickets.setImageName(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getValue());
                String imagePath = commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey(), carrierOrderCode, path, null);
                responseTicketsList.add(imagePath);
                carrierOrderTickets.setImagePath(imagePath);
                carrierOrderTickets.setUploadUserName(BaseContextHandler.getUserName());
                carrierOrderTickets.setUploadTime(now);
                commonBiz.setBaseEntityAdd(carrierOrderTickets,BaseContextHandler.getUserName());
                ticketsList.add(carrierOrderTickets);
            }
            if (ListUtils.isNotEmpty(ticketsList)){
                tCarrierOrderTicketsMapper.batchInsertTickets(ticketsList);
            }
        }
        return responseTicketsList;
    }

    //纠错/无需纠错自动签收（未卸货则跨节点签收）
    public void correctAutoSign(TCarrierOrderCorrect tCarrierOrderCorrect,
                                Date now,
                                CarrierOrderCorrectConfirmRequestModel requestModel,
                                String userName,
                                Integer ifSolveError,
                                List<String> responseTicketsList) {
        CarrierOrderInfoForAutoModel carrierOrderModel = tCarrierOrderMapper.getCarrierOrderInfoForAuto(tCarrierOrderCorrect.getCarrierOrderId());
        //查询运单地址信息
        TCarrierOrderAddress carrierOrderAddress = tCarrierOrderAddressMapper.getByCarrierOrderId(carrierOrderModel.getCarrierOrderId());

        //纠错前运单提货数
        BigDecimal originalLoadAmount = carrierOrderModel.getLoadAmount();
        BigDecimal stockInCount = tCarrierOrderCorrect.getStockInCount();
        //第二次纠错
        boolean secondCorrect = false;
        //有云仓异常数, 入库数-异常数
        if (carrierOrderModel.getAbnormalAmount() != null && carrierOrderModel.getAbnormalAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
            stockInCount = stockInCount.subtract(carrierOrderModel.getAbnormalAmount());
            secondCorrect = true;
        }
        Integer carrierOrderStatus = carrierOrderModel.getStatus();
        Integer settlementTonnage = carrierOrderModel.getSettlementTonnage();

        BigDecimal entrustAmount = BigDecimal.ZERO;
        if (settlementTonnage.equals(EntrustSettlementTonnageEnum.EXPECT_AMOUNT.getKey())) {
            entrustAmount = carrierOrderModel.getExpectAmount();
        } else if (settlementTonnage.equals(EntrustSettlementTonnageEnum.LOAD_AMOUNT.getKey())) {
            if (requestModel != null) {//纠错
                entrustAmount = requestModel.getLoadAmount();
            } else {
                entrustAmount = carrierOrderModel.getLoadAmount();
            }
        } else if (settlementTonnage.equals(EntrustSettlementTonnageEnum.UNLOAD_AMOUNT.getKey())) {
            entrustAmount = stockInCount;
        } else if (settlementTonnage.equals(EntrustSettlementTonnageEnum.SIGN_AMOUNT.getKey())) {
            entrustAmount = stockInCount;
        }

        //匹配货主费用
        Map<String, BigDecimal> entrustPriceMap = carrierOrderCommonBiz.getEntrustPrice(Collections.singletonList(carrierOrderModel.getEntrustType()), Collections.singletonList(carrierOrderModel.getGoodsUnit()));
        BigDecimal entrustFreight = entrustPriceMap.get(carrierOrderModel.getEntrustType().toString() + carrierOrderModel.getGoodsUnit().toString());

        //查询计费距离配置
        AddressAreaExistRequestModel areaModel = new AddressAreaExistRequestModel();
        areaModel.setFromAreaId(carrierOrderAddress.getLoadAreaId());
        areaModel.setToAreaId(carrierOrderAddress.getUnloadAreaId());
        Map<String, BigDecimal> configDistanceMap = carrierOrderCommonBiz.getConfigDistanceMap(Collections.singletonList(areaModel));

        //运单上无车主费用-匹配车主价格
        Integer carrierPriceType = null;
        BigDecimal carrierPrice = null;
        if (carrierOrderModel.getCarrierPriceType() == null || CommonConstant.INTEGER_ZERO.equals(carrierOrderModel.getCarrierPriceType())) {
            //车主实际结算数量
            BigDecimal carrierSettlementAmount = BigDecimal.ZERO;
            if (SettlementTonnageEnum.LOAD.getKey().equals(carrierOrderModel.getCarrierSettlement())) {
                carrierSettlementAmount = carrierOrderModel.getLoadAmount();
            } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(carrierOrderModel.getCarrierSettlement())) {
                carrierSettlementAmount = stockInCount;
            } else if (SettlementTonnageEnum.SIGN.getKey().equals(carrierOrderModel.getCarrierSettlement())) {
                carrierSettlementAmount = stockInCount;
            } else if (SettlementTonnageEnum.EXPECT.getKey().equals(carrierOrderModel.getCarrierSettlement())) {
                carrierSettlementAmount = carrierOrderModel.getExpectAmount();
            }

            //结算数量大于0
            if (carrierSettlementAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                AddressAreaExistRequestModel areaMatchModel = new AddressAreaExistRequestModel();
                areaMatchModel.setFromAreaId(carrierOrderAddress.getLoadAreaId());
                areaMatchModel.setToAreaId(carrierOrderAddress.getUnloadAreaId());
                //车主运价匹配需要用到的信息
                MatchCarrierPriceResultModel matchCarrierPriceStepOneModel = carrierOrderCommonBiz.matchCarrierPriceStepOneResult(Collections.singletonList(carrierOrderModel.getCompanyCarrierId()), Collections.singletonList(areaMatchModel));

                if (matchCarrierPriceStepOneModel != null &&
                        matchCarrierPriceStepOneModel.getEntrustCarrierFreightMap().get(carrierOrderModel.getCompanyCarrierId()) != null
                        && matchCarrierPriceStepOneModel.getEntrustCarrierFreightMap().get(carrierOrderModel.getCompanyCarrierId()).get(carrierOrderModel.getEntrustType()) != null) {

                    Map<Integer, CarrierFreightCarrierModel> entrustFreightMap = matchCarrierPriceStepOneModel.getEntrustCarrierFreightMap().get(carrierOrderModel.getCompanyCarrierId());
                    CarrierFreightCarrierModel carrierFreightCarrierModel = entrustFreightMap.get(carrierOrderModel.getEntrustType());
                    GetCarrierPriceModel getCarrierPriceModel = new GetCarrierPriceModel();
                    getCarrierPriceModel.setGoodsAmount(carrierSettlementAmount);
                    getCarrierPriceModel.setGoodsUnit(carrierOrderModel.getGoodsUnit());
                    getCarrierPriceModel.setEntrustType(carrierOrderModel.getEntrustType());
                    getCarrierPriceModel.setLoadAreaId(carrierOrderAddress.getLoadAreaId());
                    getCarrierPriceModel.setUnloadAreaId(carrierOrderAddress.getUnloadAreaId());
                    getCarrierPriceModel.setExpectMileage(carrierOrderModel.getExpectMileage());
                    carrierOrderCommonBiz.matchCarrierPrice(matchCarrierPriceStepOneModel.getTRouteDistanceConfigs(), matchCarrierPriceStepOneModel.getCarrierFreightConfigSchemeMap(), getCarrierPriceModel, carrierFreightCarrierModel);
                    //设置车主价格
                    if (getCarrierPriceModel.getCarrierPrice() != null
                            && getCarrierPriceModel.getCarrierPrice().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                        carrierPriceType = getCarrierPriceModel.getCarrierPriceType();
                        carrierPrice = getCarrierPriceModel.getCarrierPrice();
                    }
                }
            }
        }

        //更新运单
        TCarrierOrder carrierOrder = new TCarrierOrder();
        carrierOrder.setId(carrierOrderModel.getCarrierOrderId());
        carrierOrder.setStatus(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey());
        carrierOrder.setStatusUpdateTime(now);
        carrierOrder.setUnloadAmount(stockInCount);
        carrierOrder.setUnloadTime(now);
        if (carrierOrderStatus < CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()) {
            carrierOrder.setUnloadAmountExpect(stockInCount);
        }
        carrierOrder.setSignTime(now);
        carrierOrder.setSignAmount(stockInCount);

        //货主费用
        if (entrustFreight != null) {
            carrierOrder.setExpectEntrustFreightType(FreightTypeEnum.UNIT_PRICE.getKey());
            carrierOrder.setExpectEntrustFreight(entrustFreight);
            carrierOrder.setEntrustFreightType(FreightTypeEnum.UNIT_PRICE.getKey());
            carrierOrder.setEntrustFreight(entrustFreight);
            carrierOrder.setSignFreightFee(entrustFreight.multiply(entrustAmount).setScale(2, RoundingMode.HALF_UP));
            //司机运费=货主运费
            if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(carrierOrderModel.getIsOurCompany())) {
                carrierOrder.setDispatchFreightFeeType(FreightTypeEnum.UNIT_PRICE.getKey());
                carrierOrder.setDispatchFreightFee(entrustFreight);
            }
        }

        if (requestModel != null) {//纠错
            carrierOrder.setLoadAmount(requestModel.getLoadAmount());
            //更新车主价格
            carrierOrder.setCarrierPriceType(requestModel.getCarrierFreightType());
            carrierOrder.setCarrierPrice(requestModel.getCarrierFreight());

            carrierOrderModel.setLoadAmount(carrierOrder.getLoadAmount());
        } else {//无需纠错
            //提货数量
            carrierOrder.setLoadAmount(carrierOrderModel.getLoadAmount());
            if (!BargainingModeEnum.LESS_THAN_TRUCKLOAD_PRICING.getKey().equals(carrierOrderModel.getBargainingMode())) {
                //车主价格
                carrierOrder.setCarrierPriceType(carrierPriceType);
                carrierOrder.setCarrierPrice(carrierPrice);
            }
        }
        if (!secondCorrect) {
            //第一次纠错更新计费里程配置
            carrierOrder.setConfigDistance(configDistanceMap.get(carrierOrderAddress.getLoadAreaId() + CommonConstant.COMMA + carrierOrderAddress.getUnloadAreaId()));
        }
        commonBiz.setBaseEntityModify(carrierOrder, userName);

        //更新货物
        TCarrierOrderGoods upGoods;
        List<TCarrierOrderGoods> batchUpdateCarrierGoods = new ArrayList<>();
        if (requestModel != null) {//纠错
            BigDecimal loadAmount = carrierOrder.getLoadAmount();
            BigDecimal unloadAmount = carrierOrder.getUnloadAmount();
            for (CarrierOrderGoodsForAutoModel goods : carrierOrderModel.getGoodsList()) {
                upGoods = new TCarrierOrderGoods();
                upGoods.setId(goods.getGoodsId());

                if (carrierOrderModel.getGoodsList().size() > CommonConstant.INTEGER_ONE) {
                    if (goods.getLoadAmount().compareTo(loadAmount) < CommonConstant.INTEGER_ZERO){
                        upGoods.setLoadAmount(goods.getLoadAmount());
                        loadAmount = loadAmount.subtract(goods.getLoadAmount());
                    }else{
                        upGoods.setLoadAmount(loadAmount);
                    }

                    if (carrierOrderStatus < CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()){//未卸货
                        if (unloadAmount.compareTo(upGoods.getLoadAmount()) <= CommonConstant.INTEGER_ZERO){
                            upGoods.setUnloadAmount(unloadAmount);
                            unloadAmount = BigDecimal.ZERO;
                        }else {
                            upGoods.setUnloadAmount(upGoods.getLoadAmount());
                            unloadAmount = unloadAmount.subtract(upGoods.getLoadAmount());
                        }
                    }else {
                        if (goods.getUnloadAmount().compareTo(unloadAmount) < CommonConstant.INTEGER_ZERO) {
                            upGoods.setUnloadAmount(goods.getUnloadAmount());
                            unloadAmount = unloadAmount.subtract(goods.getUnloadAmount());
                        } else {
                            upGoods.setUnloadAmount(unloadAmount);
                        }
                    }
                }else{
                    upGoods.setLoadAmount(loadAmount);
                    upGoods.setUnloadAmount(unloadAmount);
                }

                upGoods.setSignAmount(upGoods.getUnloadAmount());
                commonBiz.setBaseEntityModify(upGoods, userName);
                batchUpdateCarrierGoods.add(upGoods);
            }
        }else{//无需纠错
            for (CarrierOrderGoodsForAutoModel goods : carrierOrderModel.getGoodsList()) {
                upGoods = new TCarrierOrderGoods();
                upGoods.setId(goods.getGoodsId());
                upGoods.setUnloadAmount(goods.getLoadAmount());
                upGoods.setSignAmount(goods.getLoadAmount());
                commonBiz.setBaseEntityModify(upGoods, userName);
                batchUpdateCarrierGoods.add(upGoods);
            }
        }

        //因为可以多次纠错,第一次纠错触发签收之后 后续的不记录签收日志
        if (!CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(carrierOrderModel.getStatus())) {
            //运单可以多次纠错,已经签收后不用重复记录日志
            String unit = GoodsUnitEnum.getEnum(carrierOrderModel.getGoodsUnit()).getUnit();
            //生成运单事件
            TCarrierOrderEvents carrierOrderEvents = carrierOrderCommonBiz.getCarrierOrderEvent(carrierOrderModel.getCarrierOrderId(), CarrierOrderEventsTypeEnum.SIGN_IN, userName, CarrierOrderEventsTypeEnum.SIGN_IN.format(StripTrailingZerosUtils.stripTrailingZerosToString(carrierOrder.getSignAmount()) + unit));

            //生成操作日志
            TCarrierOrderOperateLogs carrierOrderOperateLogs = carrierOrderCommonBiz.getCarrierOrderOperateLogs(carrierOrderModel.getCarrierOrderId(), CarrierOrderOperateLogsTypeEnum.SIGN_IN, userName, CarrierOrderOperateLogsTypeEnum.SIGN_IN.format(StripTrailingZerosUtils.stripTrailingZerosToString(carrierOrder.getSignAmount()) + unit, carrierOrder.getSignFreightFee() == null ? CommonConstant.ZERO : ConverterUtils.toString(carrierOrder.getSignFreightFee())));
            tCarrierOrderEventsMapper.insertSelective(carrierOrderEvents);
            tCarrierOrderOperateLogsMapper.insertSelective(carrierOrderOperateLogs);
        }
        tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(carrierOrder);
        tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(batchUpdateCarrierGoods);

        //如果需求单下所有运单（除已取消的）均为已签收状态并且需求单未签收，则判断是否将需求单置为已签收状态
        TDemandOrder tDemandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(carrierOrderModel.getDemandOrderId());
        if (tDemandOrder == null || IfValidEnum.INVALID.getKey().equals(tDemandOrder.getValid())) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        List<TCarrierOrder> dbCarrierOrderList = tCarrierOrderMapper.getNotCancelByDemandOrderId(carrierOrderModel.getDemandOrderId());
        TCarrierOrder tCarrierOrder = dbCarrierOrderList.stream().filter(item -> !CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(item.getStatus())).findFirst().orElse(null);
        //运单纠错后差异数 实提-预计数量
        BigDecimal currDifferenceAmount;
        if (carrierOrderModel.getAbnormalAmount() != null && carrierOrderModel.getAbnormalAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
            //第二次纠错,纠错后实提减去纠错前实提
            currDifferenceAmount = carrierOrderModel.getLoadAmount().subtract(originalLoadAmount);
        }else {
            //第一次纠错,纠错后实提减去预计数量
            currDifferenceAmount = carrierOrderModel.getLoadAmount().subtract(carrierOrderModel.getExpectAmount());
        }

        DemandBackCountMessage demandBackCountMessage = null;
        RecyclePublishUpdateDemandRequestModel recyclePublishUpdateDemandRequestModel = null;
        //需求单下运单都已签收
        if (tCarrierOrder == null) {
            if (tDemandOrder.getEntrustStatus() < DemandOrderStatusEnum.SIGN_DISPATCH.getKey()) {
                //需求单未签收
                //修改需求单状态、差异数
                UpdateDemandOrderStatusByIdsRequestModel updateDemandOrderStatusByIdsRequestModel = new UpdateDemandOrderStatusByIdsRequestModel();
                updateDemandOrderStatusByIdsRequestModel.setDemandOrderIdList(Collections.singletonList(carrierOrderModel.getDemandOrderId()));
                updateDemandOrderStatusByIdsRequestModel.setEntrustStatus(DemandOrderStatusEnum.SIGN_DISPATCH.getKey());
                updateDemandOrderStatusByIdsRequestModel.setOperatorName(userName);
                updateDemandOrderStatusByIdsRequestModel.setDifferenceAmount(currDifferenceAmount);
                demandOrderBiz.updateDemandOrderStatusByIds(updateDemandOrderStatusByIdsRequestModel);
            } else if (currDifferenceAmount.compareTo(BigDecimal.ZERO) != CommonConstant.INTEGER_ZERO){
                //需求单已签收
                //差异数不为0的情况下
                TDemandOrder upDemandOrder = new TDemandOrder();
                upDemandOrder.setId(tDemandOrder.getId());
                if (tDemandOrder.getDifferenceAmount() == null) {
                    upDemandOrder.setDifferenceAmount(currDifferenceAmount);
                } else {
                    upDemandOrder.setDifferenceAmount(tDemandOrder.getDifferenceAmount().add(currDifferenceAmount));
                }
                commonBiz.setBaseEntityModify(upDemandOrder, userName);
                tDemandOrderMapper.updateByPrimaryKeySelectiveEncrypt(upDemandOrder);

                if (currDifferenceAmount.compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO) {
                    //需求单已经签收了,再次回退差异数
                    demandBackCountMessage = new DemandBackCountMessage();
                    demandBackCountMessage.setDemandBackCountModelList(
                            Collections.singletonList(new DemandBackCountModel()
                                    .setDemandCode(tDemandOrder.getDemandOrderCode())
                                    .setCompleteBackAmount(-currDifferenceAmount.intValue())));
                    demandBackCountMessage.setUserName(BaseContextHandler.getUserName());

                    //打标的回收入库类型且是自动发布，有回退数量需同步给智慧运营系统
                    if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(tDemandOrder.getEntrustType())
                            && StringUtils.isNotBlank(tDemandOrder.getFixedDemand())
                            && CommonConstant.INTEGER_ONE.equals(tDemandOrder.getAutoPublish())) {

                        recyclePublishUpdateDemandRequestModel = new RecyclePublishUpdateDemandRequestModel();
                        recyclePublishUpdateDemandRequestModel.setConfigCode(tDemandOrder.getFixedDemand());
                        recyclePublishUpdateDemandRequestModel.setDemandOrderNo(tDemandOrder.getDemandOrderCode());
                        recyclePublishUpdateDemandRequestModel.setBackspaceNum(currDifferenceAmount.negate());
                    }
                }
            }
        }
        //需求单下运单存在未签收的
        //运单存在差异数
        else if (currDifferenceAmount.compareTo(BigDecimal.ZERO) != CommonConstant.INTEGER_ZERO) {
            //差异数不为0的情况下
            TDemandOrder upDemandOrder = new TDemandOrder();
            upDemandOrder.setId(tDemandOrder.getId());
            if (tDemandOrder.getDifferenceAmount() == null) {
                upDemandOrder.setDifferenceAmount(currDifferenceAmount);
            } else {
                upDemandOrder.setDifferenceAmount(tDemandOrder.getDifferenceAmount().add(currDifferenceAmount));
            }
            commonBiz.setBaseEntityModify(upDemandOrder, userName);
            tDemandOrderMapper.updateByPrimaryKeySelectiveEncrypt(upDemandOrder);
        }


//        //石化板块回收出同步云仓
//        if (EntrustTypeEnum.RECYCLE_OUT.getKey().equals(carrierOrderModel.getDemandOrderEntrustType()) && carrierOrderStatus < CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey() ){
//            //纠错不考虑
//            CarrierOrderLoadCodeListSqlConditionModel conditionModel = new CarrierOrderLoadCodeListSqlConditionModel();
//            List<Long> carrierOrderIds = new ArrayList();
//            carrierOrderIds.add(carrierOrderModel.getCarrierOrderId());
//            conditionModel.setCarrierOrderIds(carrierOrderIds);
//            List<CarrierOrderLoadProductCodeModel> carrierOrderLoadProductCodeModels = tCarrierOrderLoadCodeMapper.listCarrierOrderProductCode(conditionModel);
//            List<SyncRecycleOutProductCodeToWarehouseModelItem> itemList = new ArrayList<>();
//            SyncRecycleOutProductCodeToWarehouseModel  syncRecycleOutProductCodeToWarehouseModel = new SyncRecycleOutProductCodeToWarehouseModel();
//            if (ListUtils.isNotEmpty(carrierOrderLoadProductCodeModels)){
//                Map<String, List<CarrierOrderLoadProductCodeModel>> carrierOrderMap = carrierOrderLoadProductCodeModels.stream().collect(Collectors.groupingBy(CarrierOrderLoadProductCodeModel::getCarrierOrderCode));
//                carrierOrderMap.forEach((k,v)->{
//                    if (ListUtils.isNotEmpty(v)){
//                        SyncRecycleOutProductCodeToWarehouseModelItem codeToWarehouseModel = new SyncRecycleOutProductCodeToWarehouseModelItem();
//                        codeToWarehouseModel.setCarrierOrderCode(k);
//                        //差值初始化(可能扫码与实提不一致)
//                        Integer billCount = v.get(0).getLoadAmount().intValue()-v.size();
//                        List<String> codes = new ArrayList<>();
//                        for (CarrierOrderLoadProductCodeModel carrierOrderLoadProductCodeModel : v ) {
//                            if (CarrierOrderLoadCodeStateEnum.CORRECT_CODE.getKey().equals(carrierOrderLoadProductCodeModel.getState()) && StringUtils.isNotEmpty(carrierOrderLoadProductCodeModel.getProductCode())) {
//                                codes.add(carrierOrderLoadProductCodeModel.getProductCode());
//                            } else if (CarrierOrderLoadCodeStateEnum.CAN_NOT_IDENTIFY_CODE.getKey().equals(carrierOrderLoadProductCodeModel.getState())) {
//                                billCount = billCount+1;
//
//                            }
//                        }
//                        codeToWarehouseModel.setBillCount(billCount);
//                        codeToWarehouseModel.setProductCode(codes);
//                        itemList.add(codeToWarehouseModel);
//                    }
//                });
//                if (ListUtils.isNotEmpty(itemList)){
//                    syncRecycleOutProductCodeToWarehouseModel.setSyncRecycleOutProductCodeToWarehouseModelItems(itemList);
//                }
//            }else {
//                //全算票据
//                syncRecycleOutProductCodeToWarehouseModel.setSyncRecycleOutProductCodeToWarehouseModelItems(itemList);
//                SyncRecycleOutProductCodeToWarehouseModelItem codeToWarehouseModel = new SyncRecycleOutProductCodeToWarehouseModelItem();
//                codeToWarehouseModel.setCarrierOrderCode(carrierOrderModel.getCarrierOrderCode());
//                codeToWarehouseModel.setBillCount(carrierOrderModel.getLoadAmount().intValue());
//                itemList.add(codeToWarehouseModel);
//
//            }
//            syncRecycleOutProductCodeToWarehouseModel.setUserName(userName);
//            rabbitMqPublishBiz.syncRecycleOutProductCodeToWarehouse(syncRecycleOutProductCodeToWarehouseModel);
//        }
        

        //同步托盘
        carrierOrder.setCarrierOrderCode(carrierOrderModel.getCarrierOrderCode());
        carrierOrder.setDemandOrderEntrustType(carrierOrderModel.getEntrustType());
        this.signCarrierOrderSyn(carrierOrder, carrierOrderStatus, userName, ifSolveError, responseTicketsList);

        //没有预计里程数,更新预计里程数
        if (!(carrierOrderModel.getExpectMileage() != null
                && carrierOrderModel.getExpectMileage().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO)) {
            AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.carrierOrderMileage(Collections.singletonList(carrierOrder.getId()), null));
        }

        //生成车辆结算数据（外部车辆）
        List<Long> carrierOrderIdList = new ArrayList<>();
        carrierOrderIdList.add(carrierOrder.getId());
        extVehicleSettlementBiz.createExtVehicleSettlement(carrierOrderIdList, false, true);

        //过滤零担运单 校验生成零担费用
        if (!CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(carrierOrderModel.getStatus()) && BargainingModeEnum.LESS_THAN_TRUCKLOAD_PRICING.getKey().equals(carrierOrderModel.getBargainingMode())) {
            shippingOrderCommonBiz.checkAndGenerateShippingFeeFromCarrierOrderAction(Collections.singletonList(carrierOrderModel.getCarrierOrderId()));
        }

        if (demandBackCountMessage != null) {
            log.info("需求单签收同步云盘差异数");
            rabbitMqPublishBiz.syncBackAmountToLeYi(demandBackCountMessage);
        }

        if (recyclePublishUpdateDemandRequestModel != null) {
            commonBiz.synRecyclePublishUpdateDemand(Collections.singletonList(recyclePublishUpdateDemandRequestModel));
        }
    }

    //S单同步托盘
    private void signCarrierOrderSyn(TCarrierOrder synCarrierOrder,
                                     Integer carrierOrderStatus,
                                     String userName,
                                     Integer ifSolveError,
                                     List<String> billPath) {
        List<TCarrierOrderOrderRel> dbCarrierOrderRels = tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(ConverterUtils.toString(synCarrierOrder.getId()));
        List<TCarrierOrderGoods> carrierOrderGoodsList = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(ConverterUtils.toString(synCarrierOrder.getId()));

        //更新S单数据
        List<TCarrierOrderOrderRel> upCarrierOrderRels = new ArrayList<>();
        List<OrderAmountModel> upOrderLoadAmountModels = new ArrayList<>();
        List<OrderAmountModel> upOrderUnLoadAmountModels = new ArrayList<>();
        List<OrderAmountModel> upOrderSignAmountModels = new ArrayList<>();
        if (ListUtils.isNotEmpty(dbCarrierOrderRels)) {
            TCarrierOrderOrderRel upOrderRel;
            OrderAmountModel orderLoadAmountModel;
            OrderAmountModel orderUnloadAmountModel;
            OrderAmountModel orderSignAmountModel;
            BigDecimal loadAmount = synCarrierOrder.getLoadAmount();
            BigDecimal unloadAmount = synCarrierOrder.getUnloadAmount();
            for (TCarrierOrderOrderRel tmpOrderRel : dbCarrierOrderRels) {
                upOrderRel = new TCarrierOrderOrderRel();
                upOrderRel.setId(tmpOrderRel.getId());

                if (dbCarrierOrderRels.size() > CommonConstant.INTEGER_ONE) {
                    if (tmpOrderRel.getLoadAmount().compareTo(loadAmount) < CommonConstant.INTEGER_ZERO) {
                        upOrderRel.setLoadAmount(tmpOrderRel.getLoadAmount());
                        loadAmount = loadAmount.subtract(tmpOrderRel.getLoadAmount());
                    } else {
                        upOrderRel.setLoadAmount(loadAmount);
                    }

                    if (carrierOrderStatus < CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()) {//未卸货
                        if (unloadAmount.compareTo(upOrderRel.getLoadAmount()) <= CommonConstant.INTEGER_ZERO) {
                            upOrderRel.setUnloadAmount(unloadAmount);
                            unloadAmount = BigDecimal.ZERO;
                        } else {
                            upOrderRel.setUnloadAmount(upOrderRel.getLoadAmount());
                            unloadAmount = unloadAmount.subtract(upOrderRel.getLoadAmount());
                        }
                    } else {
                        if (tmpOrderRel.getUnloadAmount().compareTo(unloadAmount) < CommonConstant.INTEGER_ZERO) {
                            upOrderRel.setUnloadAmount(tmpOrderRel.getUnloadAmount());
                            unloadAmount = unloadAmount.subtract(tmpOrderRel.getUnloadAmount());
                        } else {
                            upOrderRel.setUnloadAmount(unloadAmount);
                        }
                    }
                }else{
                    upOrderRel.setLoadAmount(loadAmount);
                    upOrderRel.setUnloadAmount(unloadAmount);
                }

                upOrderRel.setSignAmount(upOrderRel.getUnloadAmount());
                commonBiz.setBaseEntityModify(upOrderRel, userName);
                upCarrierOrderRels.add(upOrderRel);

                orderLoadAmountModel = new OrderAmountModel();
                orderLoadAmountModel.setAmount(upOrderRel.getLoadAmount());
                orderLoadAmountModel.setOrderId(tmpOrderRel.getOrderId());
                orderLoadAmountModel.setOrderCode(tmpOrderRel.getOrderCode());
                upOrderLoadAmountModels.add(orderLoadAmountModel);

                orderUnloadAmountModel = new OrderAmountModel();
                orderUnloadAmountModel.setAmount(upOrderRel.getUnloadAmount());
                orderUnloadAmountModel.setOrderId(tmpOrderRel.getOrderId());
                orderUnloadAmountModel.setOrderCode(tmpOrderRel.getOrderCode());
                upOrderUnLoadAmountModels.add(orderUnloadAmountModel);

                orderSignAmountModel = new OrderAmountModel();
                orderSignAmountModel.setAmount(upOrderRel.getSignAmount());
                orderSignAmountModel.setOrderId(tmpOrderRel.getOrderId());
                orderSignAmountModel.setOrderCode(tmpOrderRel.getOrderCode());
                upOrderSignAmountModels.add(orderSignAmountModel);
            }
        }

        //同步云仓、云盘信息
        List<CarrierOrderSynchronizeModel> synchronizeModels = new ArrayList<>();
        CarrierOrderSynchronizeModel syncModel = new CarrierOrderSynchronizeModel();
        //纠错签收
        syncModel.setType(synCarrierOrder.getDemandOrderEntrustType());
        syncModel.setCarrierOrderCode(synCarrierOrder.getCarrierOrderCode());
        syncModel.setStatus(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey());
        syncModel.setReceiptTime(synCarrierOrder.getSignTime());
        syncModel.setLoadingCount(synCarrierOrder.getLoadAmount());
        syncModel.setUnloadingCount(synCarrierOrder.getUnloadAmount());
        syncModel.setUnloadTime(synCarrierOrder.getUnloadTime());
        syncModel.setUserName(userName);
        syncModel.setOrderLoadAmount(upOrderLoadAmountModels);
        syncModel.setOrderUnloadAmount(upOrderUnLoadAmountModels);
        syncModel.setOrderSignAmount(upOrderSignAmountModels);
        if (ListUtils.isNotEmpty(billPath)) {
            syncModel.setBillPath(billPath);
        }
        syncModel.setIfSolveError(ifSolveError);

        //运单sku数据
        TypeAndCountModel typeAndCountModel;
        List<TypeAndCountModel> typeAndCountModels = new ArrayList<>();
        for (TCarrierOrderGoods goods : carrierOrderGoodsList) {
            typeAndCountModel = new TypeAndCountModel();
            typeAndCountModel.setProductTypeCode(goods.getSkuCode());
            typeAndCountModel.setCategoryName(goods.getCategoryName());
            typeAndCountModel.setHeight(ConverterUtils.toString(goods.getHeight()));
            typeAndCountModel.setWidth(ConverterUtils.toString(goods.getWidth()));
            typeAndCountModel.setLength(ConverterUtils.toString(goods.getLength()));
            typeAndCountModel.setSortName(goods.getGoodsName());
            typeAndCountModel.setLoadingCount(goods.getLoadAmount().intValue());
            typeAndCountModel.setUnloadingCount(goods.getUnloadAmount().intValue());

            typeAndCountModels.add(typeAndCountModel);
        }
        syncModel.setTypeAndCountModel(typeAndCountModels);

        synchronizeModels.add(syncModel);

        if (ListUtils.isNotEmpty(upCarrierOrderRels)) {
            tCarrierOrderOrderRelMapper.batchUpdateSelective(upCarrierOrderRels);
        }

        //同步云仓消息
        if (ListUtils.isNotEmpty(synchronizeModels)) {
            log.info("运单纠错自动签收同步消息到云仓" + synchronizeModels);
            log.info("运单纠错自动签收同步消息到云盘" + synchronizeModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToWarehouse(carrierOrderSynchronizeLeyiModel);//云仓
            rabbitMqPublishBiz.carrierOrderSynchronizeToLeyi(carrierOrderSynchronizeLeyiModel);//云盘
        }
    }

    /**
     * 托盘同步入库数量
     *
     * @param  message
     */
    @Transactional
    public void syncStockInfoFromLeyiStockIn(SyncStockInfoMessage message) {
        String carrierOrderCode = message.getCarrierOrderCode();
        TCarrierOrder carrierOrder = tCarrierOrderMapper.getByCode(carrierOrderCode);
        if(carrierOrder==null || IfValidEnum.INVALID.getKey().equals(carrierOrder.getValid())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //非云盘单子不能操作
        if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(carrierOrder.getDemandOrderSource())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        if(CommonConstant.INTEGER_ONE.equals(carrierOrder.getIfCancel())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
        }
        //运单放空不能操作
        if (CommonConstant.INTEGER_ONE.equals(carrierOrder.getIfEmpty())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
        }
        if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(carrierOrder.getStatus())){
            return;
        }
        if(message.getStockInCount()==null || message.getStockInCount().compareTo(BigDecimal.ZERO)<CommonConstant.INTEGER_ZERO){
            throw new BizException(CarrierDataExceptionEnum.STOCK_IN_AMOUNT_ERROR);
        }

        Date now = new Date();
        List<TCarrierOrderTickets> list = new ArrayList<>();
        TCarrierOrderCorrect dbCorrect = tCarrierOrderCorrectMapper.getByCarrierOrderId(carrierOrder.getId());

        TCarrierOrderCorrect addOrUpCorrect = new TCarrierOrderCorrect();
        addOrUpCorrect.setCarrierOrderId(carrierOrder.getId());
        addOrUpCorrect.setStockInCount(message.getStockInCount());
        addOrUpCorrect.setStockInState(message.getStockInState());
        addOrUpCorrect.setStockInRemark(message.getStockInRemark());

        if(StockInStateEnum.STOCK_IN.getKey().equals(message.getStockInState())){
            if (CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey().equals(carrierOrder.getStatus())){
                if (message.getStockInCount().compareTo(carrierOrder.getLoadAmount())==CommonConstant.INTEGER_ZERO && message.getStockInCount().compareTo(carrierOrder.getUnloadAmount())==CommonConstant.INTEGER_ZERO){
                    addOrUpCorrect.setCorrectStatus(CorrectStatusEnum.DISPENSE_WITH_CORRECT.getKey());
                }else{
                    addOrUpCorrect.setCorrectStatus(CorrectStatusEnum.WAIT_CORRECT.getKey());
                }
            }else{
                if (message.getStockInCount().compareTo(carrierOrder.getLoadAmount())==CommonConstant.INTEGER_ZERO){
                    addOrUpCorrect.setCorrectStatus(CorrectStatusEnum.DISPENSE_WITH_CORRECT.getKey());
                }else{
                    addOrUpCorrect.setCorrectStatus(CorrectStatusEnum.WAIT_CORRECT.getKey());
                }
            }

            //入库单
            if (ListUtils.isNotEmpty(message.getStockInImagePathList())) {
                TCarrierOrderTickets tmp;
                for (String tmpUrl : message.getStockInImagePathList()) {
                    tmp = new TCarrierOrderTickets();
                    tmp.setCarrierOrderId(carrierOrder.getId());
                    tmp.setImageName(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_STOCK_IN_TICKETS.getValue());
                    tmp.setImagePath(tmpUrl);
                    tmp.setImageType(CarrierOrderTicketsTypeEnum.CARRIER_ORDER_STOCK_IN_TICKETS.getKey());
                    tmp.setUploadTime(now);
                    tmp.setUploadUserName(message.getOperatorName());
                    commonBiz.setBaseEntityAdd(tmp, message.getOperatorName());
                    list.add(tmp);
                }
            }
        }

        Integer correctStatus = addOrUpCorrect.getCorrectStatus();
        if(dbCorrect != null && IfValidEnum.VALID.getKey().equals(dbCorrect.getValid())){
            addOrUpCorrect.setId(dbCorrect.getId());
            commonBiz.setBaseEntityModify(addOrUpCorrect,message.getOperatorName());
            tCarrierOrderCorrectMapper.updateByPrimaryKeySelective(addOrUpCorrect);
        }else{
            commonBiz.setBaseEntityAdd(addOrUpCorrect,message.getOperatorName());
            tCarrierOrderCorrectMapper.insertSelective(addOrUpCorrect);

            if (correctStatus == null){
                correctStatus = CorrectStatusEnum.DEFAULT.getKey();
            }
        }

        if(ListUtils.isNotEmpty(list)){
            tCarrierOrderTicketsMapper.batchInsertTickets(list);
        }

        //更新运单上纠错状态
        TCarrierOrder upCarrierOrder = new TCarrierOrder();
        upCarrierOrder.setId(carrierOrder.getId());
        upCarrierOrder.setCorrectStatus(correctStatus);
        commonBiz.setBaseEntityModify(upCarrierOrder, message.getOperatorName());
        tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(upCarrierOrder);

        //无需纠错，则自动签收
        if (CorrectStatusEnum.DISPENSE_WITH_CORRECT.getKey().equals(addOrUpCorrect.getCorrectStatus())) {
            correctAutoSign(addOrUpCorrect,now,null,message.getOperatorName(), CommonConstant.INTEGER_TWO, null);
        }
        // 待纠错发布运单待纠错节点事件
        else if (CorrectStatusEnum.WAIT_CORRECT.getKey().equals(addOrUpCorrect.getCorrectStatus())) {
            // 发布智能推送运单待纠错节点事件
            applicationContext.publishEvent(new WorkGroupEventModel()
                    .setWorkGroupPushBoModels(carrierOrder.getId(),
                            carrierOrder.getDemandOrderSource(),
                            carrierOrder.getDemandOrderEntrustType(),
                            carrierOrder.getProjectLabel(),
                            WorkGroupOrderTypeEnum.CARRIER_ORDER,
                            WorkGroupOrderNodeEnum.CARRIER_ORDER_CORRECTION));
        }
    }

    /**
     * 云盘待审核车辆数
     *
     * @param  requestModel
     */
    public WaitAuditVehicleInfoResponseModel getWaitAuditVehicleCountInfoForLeYi(WaitAuditVehicleInfoRequestModel requestModel) {
        //如果下单开始时间跟下单结束时间为空的话，默认为当前-90
        if (StringUtils.isBlank(requestModel.getDemandCreatedTimeFrom()) || StringUtils.isBlank(requestModel.getDemandCreatedTimeTo())) {
            requestModel.setDemandCreatedTimeTo(DateUtils.dateToString(new Date(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            Calendar now = Calendar.getInstance();
            now.add(Calendar.DAY_OF_MONTH, -90);
            requestModel.setDemandCreatedTimeFrom(DateUtils.dateToString(now.getTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        WaitAuditVehicleInfoResponseModel responseModel = new WaitAuditVehicleInfoResponseModel();
        responseModel.setWaitAuditVehicleCount(tCarrierOrderMapper.getWaitAuditVehicleCountForLeYi(requestModel));
        return responseModel;
    }

    /**
     * 运单放空详情
     * @param requestModel
     * @return
     */
    public CarrierOrderEmptyDetailResponseModel carrierOrderEmptyDetail(CarrierOrderDetailRequestModel requestModel) {
        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (tCarrierOrder == null || tCarrierOrder.getValid().equals(IfValidEnum.INVALID.getKey())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //非云盘单子不能操作
        if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(tCarrierOrder.getDemandOrderSource())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //运单取消不能操作
        if(CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfCancel())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EMPTY_STATUS_ERROR);
        }
        //运单放空不能操作
        if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EMPTY_STATUS_ERROR);
        }
        //提货后不允许放空
        if(CarrierOrderStatusEnum.WAIT_LOAD.getKey()<tCarrierOrder.getStatus()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EMPTY_STATUS_ERROR);
        }
        //已出库不能放空
        if (!CarrierOrderOutStatusEnum.WAIT_OUT.getKey().equals(tCarrierOrder.getOutStatus())) {
            throw new BizException(CarrierDataExceptionEnum.NOT_ALLOWED_CANCEL);
        }

        //转换返回结果
        CarrierOrderEmptyDetailResponseModel responseModel = MapperUtils.mapper(tCarrierOrder, CarrierOrderEmptyDetailResponseModel.class);
        responseModel.setCarrierOrderId(tCarrierOrder.getId());
        return responseModel;
    }

    /**
     * 确认放空
     *
     * @param  requestModel
     */
    @Transactional
    public void confirmEmpty(CarrierOrderEmptyRequestModel requestModel) {
        TCarrierOrder carrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if(carrierOrder==null || IfValidEnum.INVALID.getKey().equals(carrierOrder.getValid())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //非云盘单子不能操作
        if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(carrierOrder.getDemandOrderSource())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //运单取消不能操作
        if(CommonConstant.INTEGER_ONE.equals(carrierOrder.getIfCancel())){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EMPTY_STATUS_ERROR);
        }
        //运单放空不能操作
        if (CommonConstant.INTEGER_ONE.equals(carrierOrder.getIfEmpty())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EMPTY_STATUS_ERROR);
        }
        //提货后不允许放空
        if(CarrierOrderStatusEnum.WAIT_LOAD.getKey()<carrierOrder.getStatus()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EMPTY_STATUS_ERROR);
        }
        //已出库不能放空
        if (!CarrierOrderOutStatusEnum.WAIT_OUT.getKey().equals(carrierOrder.getOutStatus())) {
            throw new BizException(CarrierDataExceptionEnum.NOT_ALLOWED_CANCEL);
        }
        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(carrierOrder.getId().toString());
        if (!workOrderMap.isEmpty()){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        //更新运单
        TCarrierOrder upCarrierOrder=new TCarrierOrder();
        upCarrierOrder.setId(carrierOrder.getId());
        upCarrierOrder.setIfEmpty(CommonConstant.INTEGER_ONE);
        upCarrierOrder.setEmptyTime(new Date());

        //计算货主费用
        if (ContractPriceTypeEnum.UNIT_PRICE.getKey().equals(carrierOrder.getEntrustFreightType())){
            upCarrierOrder.setSignFreightFee(carrierOrder.getEntrustFreight().multiply(carrierOrder.getExpectAmount()).setScale(2, BigDecimal.ROUND_HALF_UP));
        }else if (ContractPriceTypeEnum.FIXED_PRICE.getKey().equals(carrierOrder.getEntrustFreightType())){
            upCarrierOrder.setSignFreightFee(carrierOrder.getEntrustFreight());
        }
        commonBiz.setBaseEntityModify(upCarrierOrder,BaseContextHandler.getUserName());

        //运单日志
        TCarrierOrderOperateLogs carrierOrderOperateLogs = carrierOrderCommonBiz.getCarrierOrderOperateLogs(carrierOrder.getId(), CarrierOrderOperateLogsTypeEnum.CARRIER_ORDER_EMPTY,
                BaseContextHandler.getUserName(), "【" + CarrierOrderObjectTypeEnum.getEnum(requestModel.getObjectionType()).getValue() + "】" + requestModel.getObjectionReason());
        //运单事件
        TCarrierOrderEvents carrierOrderEvents = new TCarrierOrderEvents();
        carrierOrderEvents.setCarrierOrderId(carrierOrder.getId());
        carrierOrderEvents.setEvent(CarrierOrderEventsTypeEnum.EMPTY.getKey());
        carrierOrderEvents.setEventDesc(CarrierOrderEventsTypeEnum.EMPTY.getValue());
        carrierOrderEvents.setRemark("【" + CarrierOrderObjectTypeEnum.getEnum(requestModel.getObjectionType()).getValue() + "】" + requestModel.getObjectionReason());
        carrierOrderEvents.setEventTime(new Date());
        carrierOrderEvents.setOperatorName(BaseContextHandler.getUserName());
        carrierOrderEvents.setOperateTime(new Date());
        commonBiz.setBaseEntityAdd(carrierOrderEvents, BaseContextHandler.getUserName());

        tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(upCarrierOrder);
        tCarrierOrderOperateLogsMapper.insertSelective(carrierOrderOperateLogs);
        tCarrierOrderEventsMapper.insertSelective(carrierOrderEvents);
        //外部车辆结算
        List<Long> carrierOrderIdList = new ArrayList<>();
        carrierOrderIdList.add(carrierOrder.getId());
        extVehicleSettlementBiz.createExtVehicleSettlement(carrierOrderIdList, true, false);
        //以下为云盘逻辑
        //查询s单数据信息
        CancelCarrierOrderImpactDemandOrderModel orderResponseModels = new CancelCarrierOrderImpactDemandOrderModel();
        List<CarrierOrderSynchronizeModel> synchronizeModels = new ArrayList<>();
        Map<Long, BigDecimal> carrierOrderCountMap = new HashMap<>();
        Map<Long, BigDecimal> carrierGoodCountMap = new HashMap<>();

        List<TCarrierOrderOrderRel> carrierOrderOrderRels = tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(carrierOrder.getId().toString());
        if (ListUtils.isNotEmpty(carrierOrderOrderRels)) {
            List<CancelCarrierOrderOrderRelModel> relModels = new ArrayList<>();
            for (TCarrierOrderOrderRel rel : carrierOrderOrderRels) {
                CancelCarrierOrderOrderRelModel relModel = new CancelCarrierOrderOrderRelModel();
                relModel.setDemandOrderOrderId(rel.getDemandOrderOrderId());
                relModel.setExpectAmount(rel.getExpectAmount());
                relModel.setOrderId(rel.getOrderId());
                relModel.setOrderCode(rel.getOrderCode());
                relModel.setDemandOrdeId(carrierOrder.getDemandOrderId());
                relModels.add(relModel);
            }
            orderResponseModels.setCarrierOrderOrderRelModels(relModels);
        }
        List<CarrierOrderListBeforeSignUpResponseModel> carrierOrderList = tCarrierOrderMapper.selectCarrierOrderSignDetail(carrierOrder.getId().toString(), null);
        if (ListUtils.isEmpty(carrierOrderList)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        CarrierOrderListBeforeSignUpResponseModel carrierOrderDetail=carrierOrderList.get(CommonConstant.INTEGER_ZERO);
        //运单预提数量
        carrierOrderCountMap.put(carrierOrderDetail.getDemandOrderId(), carrierOrderDetail.getExpectAmount());
        //运单货物预提
        for (CarrierOrderListBeforeSignUpGoodsModel good : carrierOrderDetail.getGoodsInfo()) {
            carrierGoodCountMap.put(good.getDemandOrderGoodsId(), good.getExpectAmount());
        }

        CarrierOrderSynchronizeModel syncModel = new CarrierOrderSynchronizeModel();
        syncModel.setType(carrierOrderDetail.getEntrustType());
        syncModel.setDemandOrderId(carrierOrderDetail.getDemandOrderId());//临时字段
        syncModel.setCarrierOrderCode(carrierOrderDetail.getCarrierOrderCode());
        syncModel.setIfInvalid(CommonConstant.INTEGER_ONE);
        syncModel.setUserName(BaseContextHandler.getUserName());
        syncModel.setCancelReason("【"+CarrierOrderObjectTypeEnum.getEnum(requestModel.getObjectionType()).getValue()+"】"+requestModel.getObjectionReason());
        synchronizeModels.add(syncModel);

        orderResponseModels.setCarrierGoodCountMap(carrierGoodCountMap);
        orderResponseModels.setCarrierOrderCountMap(carrierOrderCountMap);
        if (ListUtils.isNotEmpty(synchronizeModels)) {
            orderResponseModels.setSynchronizeModels(synchronizeModels);
        }

        //过滤零担运单 校验生成零担费用
        if (BargainingModeEnum.LESS_THAN_TRUCKLOAD_PRICING.getKey().equals(carrierOrder.getBargainingMode())) {
            shippingOrderCommonBiz.checkAndGenerateShippingFeeFromCarrierOrderAction(Collections.singletonList(carrierOrder.getId()));
        }
        //运单放空，影响需求单和托盘
        demandOrderBiz.cancelCarrierOrderUpdateDemandOrder(orderResponseModels);

        //预约单解除关联运单
        reservationOrderCommonBiz.disassociateCarrierOrder(new ArrayList<>(){{add(requestModel.getCarrierOrderId());}});



    }

    /**
     * 复制运单信息
     *
     * @param requestModel
     * @return
     */
    public CopyCarrierOrderResponseModel copyCarrierOrder(CarrierOrderIdRequestModel requestModel) {
        TCarrierOrder dbCarrierOrder = tCarrierOrderMapper.selectByPrimaryKey(requestModel.getCarrierOrderId());
        if (dbCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(dbCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        // 查询发货地址
        TCarrierOrderAddress dbCarrierOrderAddress = tCarrierOrderAddressMapper.getByCarrierOrderId(dbCarrierOrder.getId());
        // 查询车牌号
        TCarrierOrderVehicleHistory dbCarrierOrderVehicleHistory = tCarrierOrderVehicleHistoryMapper.getValidTopByCarrierOrderId(dbCarrierOrder.getId());
        // 查询商品信息
        List<String> goodsNameList = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderId(dbCarrierOrder.getId())
                .stream()
                .map(TCarrierOrderGoods::getGoodsName)
                .collect(Collectors.toList());

        //赋值
        CopyCarrierOrderResponseModel responseModel = new CopyCarrierOrderResponseModel();
        responseModel.setCarrierOrderCode(dbCarrierOrder.getCarrierOrderCode());
        responseModel.setDemandOrderCode(dbCarrierOrder.getDemandOrderCode());
        responseModel.setExpectAmount(dbCarrierOrder.getExpectAmount());
        //货物
        responseModel.setGoodsNameList(goodsNameList);
        //地址
        if (dbCarrierOrderAddress != null) {
            MapperUtils.mapper(dbCarrierOrderAddress, responseModel);
        }
        //车辆司机
        if (dbCarrierOrderVehicleHistory != null) {
            MapperUtils.mapperNoDefault(dbCarrierOrderVehicleHistory, responseModel);
        }
        return responseModel;
    }

    /**
     * 云盘物流看板-平均物流费用
     * @param requestModel
     * @return
     */
    public List<LogisticsCostStatisticsResponseModel> logisticsCostStatistics(LogisticsCostStatisticsRequestModel requestModel) {
        List<LogisticsCostStatisticsResponseModel> responseModelList = new ArrayList<>();
        //计算2个筛选参数有几个月，结果需展示多少条数据
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstant.DATE_TO_STRING_YM_PATTERN);
        Calendar startC = Calendar.getInstance();
        int num = 0;//数据条数
        try {
            Date sd = sdf.parse(requestModel.getYearMonthStart());
            Date ed = sdf.parse(requestModel.getYearMonthEnd());
            num = (ed.getYear() - sd.getYear()) * CommonConstant.INTEGER_TWELVE + (ed.getMonth() - sd.getMonth());
            startC.setTime(sdf.parse(requestModel.getYearMonthStart()));
        } catch (ParseException p) {
            log.info(p.getMessage(),p);
        }
        //年月份对应的空数据
        Map<String, LogisticsCostStatisticsResponseModel> monthEmptyMap = new HashMap<>();
        LogisticsCostStatisticsResponseModel responseModel;
        String yearMonthEmpty;
        List<String> allYearMonthList = new ArrayList<>();//所有年月
        for (int i = 0; i <= num; i++) {
            yearMonthEmpty = sdf.format(startC.getTime());

            responseModel = new LogisticsCostStatisticsResponseModel();
            responseModel.setYearMonth(yearMonthEmpty);
            monthEmptyMap.put(yearMonthEmpty, responseModel);

            allYearMonthList.add(yearMonthEmpty);

            startC.add(Calendar.MONTH, CommonConstant.INTEGER_ONE);
        }

        //查询发货、回收入库、回收出库、调拨、退货仓库配送、退货调拨类型的签收运单
        List<LogisticsCostStatisticsModel> carrierOrderList = tCarrierOrderMapper.logisticsCostStatistics(requestModel.getYearMonthStart(), requestModel.getYearMonthEnd());
        //无数据，则返所有年月的空数据
        if (ListUtils.isEmpty(carrierOrderList)){
            responseModelList = new ArrayList<>(monthEmptyMap.values());
        }else {
            //计算发货、回收、调拨类型的费用
            BigDecimal deliverCount;//发货、退货仓库配送
            BigDecimal deliverSignAmount;//发货、退货仓库配送签收数量
            BigDecimal recycleCount;//回收入库、回收出库
            BigDecimal recycleSignAmount;//回收入库、回收出库签收数量
            BigDecimal transfersCount;//调拨、退货调拨
            BigDecimal transfersSignAmount;//调拨、退货调拨签收数量
            BigDecimal cost;
            List<String> existMonthList = new ArrayList<>();
            for (LogisticsCostStatisticsModel monthModel : carrierOrderList) {
                deliverCount = BigDecimal.ZERO;
                deliverSignAmount = BigDecimal.ZERO;
                recycleCount = BigDecimal.ZERO;
                recycleSignAmount = BigDecimal.ZERO;
                transfersCount = BigDecimal.ZERO;
                transfersSignAmount = BigDecimal.ZERO;
                for (LogisticsCostStatisticsOrderModel orderModel : monthModel.getOrderList()) {
                    cost = (orderModel.getDispatchFreightFee().add(orderModel.getAdjustFee()).add(orderModel.getMarkupFee()));
                    if (EntrustTypeEnum.DELIVER.getKey().equals(orderModel.getEntrustType()) || EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey().equals(orderModel.getEntrustType())) {//发货、退货仓库配送
                        deliverCount = deliverCount.add(cost);
                        deliverSignAmount = deliverSignAmount.add(orderModel.getSignAmount());
                    } else if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(orderModel.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(orderModel.getEntrustType())) {//回收入库、回收出库
                        recycleCount = recycleCount.add(cost);
                        recycleSignAmount = recycleSignAmount.add(orderModel.getSignAmount());
                    } else {//调拨、退货调拨
                        transfersCount = transfersCount.add(cost);
                        transfersSignAmount = transfersSignAmount.add(orderModel.getSignAmount());
                    }
                }
                responseModel = new LogisticsCostStatisticsResponseModel();
                responseModel.setYearMonth(monthModel.getYearMonth());
                if (deliverSignAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    responseModel.setDeliverCount(deliverCount.divide(deliverSignAmount, 2, BigDecimal.ROUND_HALF_UP));
                }
                if (recycleSignAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    responseModel.setRecycleCount(recycleCount.divide(recycleSignAmount, 2, BigDecimal.ROUND_HALF_UP));
                }
                if (transfersSignAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    responseModel.setTransfersCount(transfersCount.divide(transfersSignAmount, 2, BigDecimal.ROUND_HALF_UP));
                }
                responseModelList.add(responseModel);

                existMonthList.add(monthModel.getYearMonth());
            }
            //所有年月去除存在的年月
            allYearMonthList.removeAll(existMonthList);
            if (ListUtils.isNotEmpty(allYearMonthList)) {
                //没有的年月则给控制
                for (String month : allYearMonthList) {
                    responseModelList.add(monthEmptyMap.get(month));
                }
            }
        }
        //按年月从小到大排序
        Collections.sort(responseModelList,(o1, o2) -> o1.getYearMonth().compareTo(o2.getYearMonth()));

        return responseModelList;
    }

    /**
     * 云盘物流看板-平均提货时效
     * @param requestModel
     * @return
     */
    public List<LogisticsLoadValidityStatisticsResponseModel> logisticsLoadValidityStatistics(LogisticsLoadValidityStatisticsRequestModel requestModel) {
        List<LogisticsLoadValidityStatisticsResponseModel> responseModelList = new ArrayList<>();
        //计算2个筛选参数有几个月，结果需展示多少条数据
        SimpleDateFormat sdf = new SimpleDateFormat(CommonConstant.DATE_TO_STRING_YM_PATTERN);
        Calendar startC = Calendar.getInstance();
        int num = 0;//数据条数
        try {
            Date sd = sdf.parse(requestModel.getYearMonthStart());
            Date ed = sdf.parse(requestModel.getYearMonthEnd());
            num = (ed.getYear() - sd.getYear()) * CommonConstant.INTEGER_TWELVE + (ed.getMonth() - sd.getMonth());
            startC.setTime(sdf.parse(requestModel.getYearMonthStart()));
        } catch (ParseException p) {
            log.info(p.getMessage(),p);
        }
        //年月份对应的空数据
        Map<String, LogisticsLoadValidityStatisticsResponseModel> monthEmptyMap = new HashMap<>();
        LogisticsLoadValidityStatisticsResponseModel responseModel;
        String yearMonthEmpty;
        List<String> allYearMonthList = new ArrayList<>();//所有年月
        for (int i = 0; i <= num; i++) {
            yearMonthEmpty = sdf.format(startC.getTime());

            responseModel = new LogisticsLoadValidityStatisticsResponseModel();
            responseModel.setYearMonth(yearMonthEmpty);
            monthEmptyMap.put(yearMonthEmpty, responseModel);

            allYearMonthList.add(yearMonthEmpty);

            startC.add(Calendar.MONTH, CommonConstant.INTEGER_ONE);
        }

        //查询发货、回收入库、回收出库、调拨、退货仓库配送、退货调拨类型的提货运单
        List<LogisticsLoadValidityStatisticsModel> carrierOrderList = tCarrierOrderMapper.logisticsLoadValidityStatistics(requestModel.getYearMonthStart(), requestModel.getYearMonthEnd());
        //无数据，则返所有年月的空数据
        if (ListUtils.isEmpty(carrierOrderList)){
            responseModelList = new ArrayList<>(monthEmptyMap.values());
        }else {
            //计算发货、回收、调拨类型的平均提货时效
            BigDecimal deliverCount;//发货、退货仓库配送
            BigDecimal deliverCarrierCount;//发货、退货仓库配送运单数量
            BigDecimal recycleCount;//回收入库、回收出库
            BigDecimal recycleCarrierCount;//回收入库、回收出库运单数量
            BigDecimal transfersCount;//调拨、退货调拨
            BigDecimal transfersCarrierCount;//调拨、退货调拨运单数量
            List<String> existMonthList = new ArrayList<>();
            for (LogisticsLoadValidityStatisticsModel monthModel : carrierOrderList) {
                deliverCount = BigDecimal.ZERO;
                deliverCarrierCount = BigDecimal.ZERO;
                recycleCount = BigDecimal.ZERO;
                recycleCarrierCount = BigDecimal.ZERO;
                transfersCount = BigDecimal.ZERO;
                transfersCarrierCount = BigDecimal.ZERO;
                for (LogisticsLoadValidityStatisticsOrderModel orderModel : monthModel.getOrderList()) {
                    if (EntrustTypeEnum.DELIVER.getKey().equals(orderModel.getEntrustType()) || EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey().equals(orderModel.getEntrustType())) {//发货、退货仓库配送
                        deliverCount = deliverCount.add(orderModel.getLoadValidity());
                        deliverCarrierCount = deliverCarrierCount.add(orderModel.getCarrierOrderCount());
                    } else if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(orderModel.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(orderModel.getEntrustType())) {//回收入库、回收出库
                        recycleCount = recycleCount.add(orderModel.getLoadValidity());
                        recycleCarrierCount = recycleCarrierCount.add(orderModel.getCarrierOrderCount());
                    } else {//调拨、退货调拨
                        transfersCount = transfersCount.add(orderModel.getLoadValidity());
                        transfersCarrierCount = transfersCarrierCount.add(orderModel.getCarrierOrderCount());
                    }
                }
                responseModel = new LogisticsLoadValidityStatisticsResponseModel();
                responseModel.setYearMonth(monthModel.getYearMonth());
                if (deliverCarrierCount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    responseModel.setDeliverCount(deliverCount.divide(deliverCarrierCount, 2, BigDecimal.ROUND_HALF_UP));
                }
                if (recycleCarrierCount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    responseModel.setRecycleCount(recycleCount.divide(recycleCarrierCount, 2, BigDecimal.ROUND_HALF_UP));
                }
                if (transfersCarrierCount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                    responseModel.setTransfersCount(transfersCount.divide(transfersCarrierCount, 2, BigDecimal.ROUND_HALF_UP));
                }
                responseModelList.add(responseModel);

                existMonthList.add(monthModel.getYearMonth());
            }
            //所有年月去除存在的年月
            allYearMonthList.removeAll(existMonthList);
            if (ListUtils.isNotEmpty(allYearMonthList)) {
                //没有的年月则给控制
                for (String month : allYearMonthList) {
                    responseModelList.add(monthEmptyMap.get(month));
                }
            }
        }
        //按年月从小到大排序
        Collections.sort(responseModelList,(o1, o2) -> o1.getYearMonth().compareTo(o2.getYearMonth()));

        return responseModelList;
    }

    /**
     * 云仓发起异常-再次进入待纠错
     *
     * @param abnormalAmountMessage 云仓异常数信息
     */
    @Transactional
    public void syncAbnormalAmount(SyncAbnormalAmountMessage abnormalAmountMessage) {

        //判断云仓异常数量是否合规
        if (abnormalAmountMessage.getAbnormalAmount() == null || abnormalAmountMessage.getAbnormalAmount().compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO) {
            log.error("云仓发起异常-再次进入待纠错 error --> 异常数不合符。运单号："+abnormalAmountMessage.getCarrierOrderCode());
            return;
        }

        //运单是否存在
        TCarrierOrder carrierOrder = tCarrierOrderMapper.getByCode(abnormalAmountMessage.getCarrierOrderCode());
        if (carrierOrder == null || IfValidEnum.INVALID.getKey().equals(carrierOrder.getValid())) {
            log.error("云仓发起异常-再次进入待纠错 error --> 运单不存在。运单号："+abnormalAmountMessage.getCarrierOrderCode());
            return;
        }

        //非云盘单子不能操作
        if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(carrierOrder.getDemandOrderSource())) {
            log.error("云仓发起异常-再次进入待纠错 error --> 运单不存在。运单号："+abnormalAmountMessage.getCarrierOrderCode());
            return;
        }

        //是否被签收
        if (!CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(carrierOrder.getStatus())) {
            log.error("云仓发起异常-再次进入待纠错 error --> 运单未签收。运单号："+abnormalAmountMessage.getCarrierOrderCode());
            return;
        }

        //一个运单只允许发起一次异常,判断当前运单是否已经有有效的云仓异常数
        if (carrierOrder.getAbnormalAmount() != null && carrierOrder.getAbnormalAmount().compareTo(BigDecimal.ZERO) == CommonConstant.INTEGER_ONE) {
            log.error("云仓发起异常-再次进入待纠错 error --> 已发起过异常。运单号："+abnormalAmountMessage.getCarrierOrderCode());
            return;
        }

        //是否为回收入库类型
        if (!EntrustTypeEnum.RECYCLE_IN.getKey().equals(carrierOrder.getDemandOrderEntrustType())) {
            log.error("云仓发起异常-再次进入待纠错 error --> 非回收入库类型。运单号："+abnormalAmountMessage.getCarrierOrderCode());
            return;
        }

        //查询需求单
        TDemandOrder demandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(carrierOrder.getDemandOrderId());
        if (demandOrder == null || IfValidEnum.INVALID.getKey().equals(demandOrder.getValid())) {
            log.error("云仓发起异常-再次进入待纠错 error --> 需求单不存在。运单号："+abnormalAmountMessage.getCarrierOrderCode());
            return;
        }

        //查询纠错信息
        TCarrierOrderCorrect carrierOrderCorrect = tCarrierOrderCorrectMapper.getByCarrierOrderId(carrierOrder.getId());
        if (carrierOrderCorrect == null || IfValidEnum.INVALID.getKey().equals(carrierOrderCorrect.getValid())) {
            log.error("云仓发起异常-再次进入待纠错 error --> 运单纠错信息不存在。运单号："+abnormalAmountMessage.getCarrierOrderCode());
            return;
        }

        String operatorName = abnormalAmountMessage.getOperatorName();

        //更新运单信息
        TCarrierOrder tCarrierOrder = new TCarrierOrder();
        tCarrierOrder.setId(carrierOrder.getId());
        tCarrierOrder.setAbnormalAmount(abnormalAmountMessage.getAbnormalAmount());
        tCarrierOrder.setCorrectStatus(CorrectStatusEnum.WAIT_CORRECT.getKey());
        commonBiz.setBaseEntityModify(tCarrierOrder, operatorName);

        //更新纠错状态
        TCarrierOrderCorrect tCarrierOrderCorrect = new TCarrierOrderCorrect();
        tCarrierOrderCorrect.setId(carrierOrderCorrect.getId());
        tCarrierOrderCorrect.setCorrectStatus(CorrectStatusEnum.WAIT_CORRECT.getKey());
        commonBiz.setBaseEntityModify(tCarrierOrderCorrect, operatorName);

        //更新需求单信息,累加云仓异常数
        TDemandOrder tDemandOrder = new TDemandOrder();
        tDemandOrder.setId(demandOrder.getId());
        tDemandOrder.setAbnormalAmount(abnormalAmountMessage.getAbnormalAmount().add(demandOrder.getAbnormalAmount()));
        commonBiz.setBaseEntityModify(tDemandOrder, operatorName);

        //记录log
        TCarrierOrderOperateLogs tCarrierOrderOperateLogs = new TCarrierOrderOperateLogs();
        tCarrierOrderOperateLogs.setCarrierOrderId(carrierOrder.getId());
        tCarrierOrderOperateLogs.setOperationType(CarrierOrderOperateLogsTypeEnum.EXCEPTION_FALLBACK.getKey());
        tCarrierOrderOperateLogs.setOperationContent(CarrierOrderOperateLogsTypeEnum.EXCEPTION_FALLBACK.getValue());
        tCarrierOrderOperateLogs.setRemark(String.format(CarrierOrderOperateLogsTypeEnum.EXCEPTION_FALLBACK.getFormat(), carrierOrder.getCarrierOrderCode(), abnormalAmountMessage.getAbnormalAmount().stripTrailingZeros().toPlainString()));
        tCarrierOrderOperateLogs.setOperateTime(new Date());
        tCarrierOrderOperateLogs.setOperatorName(operatorName);
        commonBiz.setBaseEntityAdd(tCarrierOrderOperateLogs, operatorName);

        //db持久化
        tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(tCarrierOrder);
        tCarrierOrderCorrectMapper.updateByPrimaryKeySelective(tCarrierOrderCorrect);
        tDemandOrderMapper.updateByPrimaryKeySelectiveEncrypt(tDemandOrder);
        tCarrierOrderOperateLogsMapper.insertSelective(tCarrierOrderOperateLogs);

        // 发布智能推送运单待纠错节点事件
        applicationContext.publishEvent(new WorkGroupEventModel()
                .setWorkGroupPushBoModels(carrierOrder.getId(),
                        carrierOrder.getDemandOrderSource(),
                        carrierOrder.getDemandOrderEntrustType(),
                        carrierOrder.getProjectLabel(),
                        WorkGroupOrderTypeEnum.CARRIER_ORDER,
                        WorkGroupOrderNodeEnum.CARRIER_ORDER_CORRECTION));
    }

    /**
     * 云盘运单获取客户单号详情
     *
     * @param requestModel 运单号
     * @return 客户单号详情
     */
    public List<CarrierOrderOrdersResponseModel> getCarrierOrderOrders(CarrierOrderIdRequestModel requestModel) {

        if (requestModel == null || requestModel.getCarrierOrderId() == null) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }

        TCarrierOrder carrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (carrierOrder == null || IfValidEnum.INVALID.getKey().equals(carrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        return tCarrierOrderOrderRelMapper.getCarrierOrderOrdersByCarrierCode(requestModel.getCarrierOrderId());
    }

    /**
     * 云盘物流看板-待提货、待纠错
     *
     * @param
     */
    public WaitCorrectStatisticsResponseModel waitCorrectStatistics() {
        //云盘物流看板-待提货、待纠错集合
        WaitCorrectStatisticsResponseModel waitCorrectStatisticsResponseModel = new WaitCorrectStatisticsResponseModel();
        //云盘物流看板-待纠错
        List<WaitCorrectStatisticsModel> waitCorrectList = tCarrierOrderMapper.waitCorrectStatistics();
        //云盘物流看板-待提货
        List<WaitLoadStatisticsModel> waitLoadList =tCarrierOrderMapper.waitLoadStatistics();

        if (ListUtils.isNotEmpty(waitCorrectList)){
            //提取市id
            List<Long> loadCityIdList = new ArrayList<>();
            for (WaitCorrectStatisticsModel model : waitCorrectList) {
                loadCityIdList.add(model.getLoadCityId());
            }
            //查询每个市存在大区负责人的最新单子地址id
            List<Long> addressIdList = tCarrierOrderAddressMapper.waitCorrectStatisticsAddress(loadCityIdList);
            if (ListUtils.isNotEmpty(addressIdList)) {
                List<TCarrierOrderAddress> tCarrierOrderAddressList = tCarrierOrderAddressMapper.getByIds(StringUtils.listToString(addressIdList, ','));

                Map<Long, String> loadRegionContactNameMap = new HashMap<>();
                tCarrierOrderAddressList.forEach(item -> loadRegionContactNameMap.put(item.getLoadCityId(), item.getLoadRegionContactName()));

                //拼接数据
                for (WaitCorrectStatisticsModel model : waitCorrectList) {
                    model.setLoadRegionContactName(loadRegionContactNameMap.get(model.getLoadCityId()));
                }
            }
        }
        //云盘物流看板-待提货、待纠错数据统计
        waitCorrectStatisticsResponseModel.setWaitLoadList(waitLoadList);
        waitCorrectStatisticsResponseModel.setWaitCorrectList(waitCorrectList);

        return waitCorrectStatisticsResponseModel;
    }

    /**
     * 接收云仓手动完成入库mq，修改运单出库状态为已出库
     * @param model
     */
    @Transactional
    public void synCompleteWarehouseStatus(CompleteWarehouseModel model){
        if (model == null || ListUtils.isEmpty(model.getCarrierOrderCodeList())){
            return;
        }

        //查询运单信息
        List<String> carrierOrderCodeList = model.getCarrierOrderCodeList();
        List<TCarrierOrder> dbCarrierOrderList = tCarrierOrderMapper.getCarrierOrderByCodes(carrierOrderCodeList);
        if (ListUtils.isEmpty(dbCarrierOrderList)){
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //请求参数的数量与库里运单数量不一致，则某些运单不存在，不能更新
        if (dbCarrierOrderList.size() != carrierOrderCodeList.size()){
            List<String> dbCarrierOrderCodeList = dbCarrierOrderList.stream().map(TCarrierOrder::getCarrierOrderCode).collect(Collectors.toList());
            carrierOrderCodeList.removeAll(dbCarrierOrderCodeList);
            log.info("接收云仓手动完成入库mq，运单号不存在："+carrierOrderCodeList.toString());
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //更新运单出库状态为已出库
        TCarrierOrder carrierOrder;
        List<TCarrierOrder> upList = new ArrayList<>();
        for (TCarrierOrder tCarrierOrder : dbCarrierOrderList) {
            carrierOrder = new TCarrierOrder();
            carrierOrder.setId(tCarrierOrder.getId());
            carrierOrder.setOutStatus(CarrierOrderOutStatusEnum.FINISH_OUT.getKey());
            carrierOrder.setLastModifiedBy(model.getOperateUserName());
            carrierOrder.setLastModifiedTime(model.getCurrentDateTime());
            upList.add(carrierOrder);
        }
        tCarrierOrderMapper.batchUpdateCarrierOrders(upList);
    }

    /**
     * 运单详情编辑费用-详情查询
     *
     * @param requestModel 运单id,费用类型
     * @return 费用信息
     */
    public EditCarrierOrderCostDetailResponseModel editCarrierOrderCostDetail(EditCarrierOrderCostDetailRequestModel requestModel) {
        //查询运单信息
        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //签收后 或 放空才能编辑费用
        if (!CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(tCarrierOrder.getStatus())
                && CommonConstant.INTEGER_ZERO.equals(tCarrierOrder.getIfEmpty())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_ONLY_ALREADY_SIGN_UP);
        }

        EditCarrierOrderCostDetailResponseModel responseModel = new EditCarrierOrderCostDetailResponseModel();
        responseModel.setCarrierOrderId(tCarrierOrder.getId());
        //判断哪种费用
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getCostType())) {
            //查询运单货主结算
            TReceivement tReceivement = tReceivementMapper.getByCarrierOrderId(tCarrierOrder.getId());
            if (tReceivement != null) {
                //货主费用
                responseModel.setPriceType(tReceivement.getPriceType());
                responseModel.setGoodsAmount(tReceivement.getSettlementAmount());
                if (FreightTypeEnum.UNIT_PRICE.getKey().equals(tReceivement.getPriceType())) {
                    //单价
                    BigDecimal settlementAmount = tReceivement.getSettlementAmount();
                    if (settlementAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                        responseModel.setCarrierFreight(tReceivement.getSettlementCostTotal().divide(settlementAmount, 2, RoundingMode.HALF_UP));
                    } else {
                        responseModel.setCarrierFreight(BigDecimal.ZERO);
                    }
                } else {
                    responseModel.setCarrierFreight(tReceivement.getSettlementCostTotal());
                }
            } else {
                //货主费用
                responseModel.setPriceType(tCarrierOrder.getEntrustFreightType());
                responseModel.setGoodsAmount(getGoodsAmount(tCarrierOrder.getSettlementTonnage(), tCarrierOrder));
                responseModel.setCarrierFreight(tCarrierOrder.getEntrustFreight());
            }
        } else if (CommonConstant.INTEGER_TWO.equals(requestModel.getCostType())) {
            //运单已关联对账不可修改
            if (tCarrierOrder.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
                throw new BizException(CarrierDataExceptionEnum.ALREADY_RELEVANCY_RECONCILIATION);
            }
            //查询运单车主结算
            TPayment tPayment = tPaymentMapper.getByCarrierOrderId(tCarrierOrder.getId());
            if (tPayment != null) {
                //车主费用
                responseModel.setPriceType(tPayment.getPriceType());
                responseModel.setGoodsAmount(tPayment.getSettlementAmount());
                if (FreightTypeEnum.UNIT_PRICE.getKey().equals(tPayment.getPriceType())) {
                    //单价
                    BigDecimal settlementAmount = tPayment.getSettlementAmount();
                    if (settlementAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                        responseModel.setCarrierFreight(tPayment.getSettlementCostTotal().divide(settlementAmount, 2, RoundingMode.HALF_UP));
                    } else {
                        responseModel.setCarrierFreight(BigDecimal.ZERO);
                    }
                } else {
                    responseModel.setCarrierFreight(tPayment.getSettlementCostTotal());
                }
            } else {
                //车主费用
                responseModel.setPriceType(tCarrierOrder.getCarrierPriceType());
                responseModel.setGoodsAmount(getGoodsAmount(tCarrierOrder.getCarrierSettlement(), tCarrierOrder));
                responseModel.setCarrierFreight(tCarrierOrder.getCarrierPrice());
            }
        }

        responseModel.setGoodsUnit(tCarrierOrder.getGoodsUnit());
        return responseModel;
    }

    /**
     * 根据不同结算方式获取货物数量
     *
     * @param settlementTonnage 结算方式
     * @param tCarrierOrder     运单信息
     * @return 货物数量
     */
    public BigDecimal getGoodsAmount(Integer settlementTonnage, TCarrierOrder tCarrierOrder) {
        BigDecimal resultAmount = BigDecimal.ZERO;
        if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())){
            resultAmount = tCarrierOrder.getExpectAmount();
        }else{
            if (SettlementTonnageEnum.LOAD.getKey().equals(settlementTonnage)) {
                resultAmount = tCarrierOrder.getLoadAmount();
            } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(settlementTonnage)) {
                resultAmount = tCarrierOrder.getUnloadAmount();
            } else if (SettlementTonnageEnum.SIGN.getKey().equals(settlementTonnage)) {
                resultAmount = tCarrierOrder.getSignAmount();
            } else if (SettlementTonnageEnum.EXPECT.getKey().equals(settlementTonnage)) {
                resultAmount = tCarrierOrder.getExpectAmount();
            }
        }
        return resultAmount;
    }

    /**
     * 运单详情编辑费用
     *
     * @param requestModel 费用信息
     */
    @Transactional
    public void editCarrierOrderCost(EditCarrierOrderCostRequestModel requestModel) {
        //查询运单信息
        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //分布式锁,根据需求单维度加锁
        String redisKey = CommonConstant.EDIT_CARRIER_ORDER_PRICE_KEY + tCarrierOrder.getDemandOrderCode();
        String distributedLock = commonBiz.getDistributedLock(redisKey);
        try {
            //签收后或放空才能编辑费用
            if (!CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(tCarrierOrder.getStatus()) &&
                    CommonConstant.INTEGER_ZERO.equals(tCarrierOrder.getIfEmpty())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_OPERATION_ERROR);
            }
            //查询需求单
            TDemandOrder tDemandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(tCarrierOrder.getDemandOrderId());
            if (tDemandOrder == null || IfValidEnum.INVALID.getKey().equals(tDemandOrder.getValid())) {
                throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_NOT_EXIST);
            }

            //需求单
            TDemandOrder upDemandOrder = new TDemandOrder();
            upDemandOrder.setId(tDemandOrder.getId());

            //运单
            TCarrierOrder upCarrierOrder = new TCarrierOrder();
            upCarrierOrder.setId(tCarrierOrder.getId());

            //运单结算数据
            TReceivement dbReceivement = tReceivementMapper.getByCarrierOrderId(requestModel.getCarrierOrderId());
            TPayment dbPayment = tPaymentMapper.getByCarrierOrderId(requestModel.getCarrierOrderId());

            //运单车主结算
            TPayment upPayment = null;
            //运单货主结算
            TReceivement upReceivement = null;
            //需求单车主结算
            TDemandPayment upDemandPayment = null;
            //需求单货主结算
            TDemandReceivement upDemandReceivement = null;
            //外部司机结算
            TExtVehicleSettlement upExtVehicleSettlement = null;

            //查询当前需求单下所有运单
            List<TCarrierOrder> notCancelCarrierOrder = tCarrierOrderMapper.getNotCancelEmptyByDemandOrderId(tDemandOrder.getId());
            List<Long> demandOrderCarrierOrderIdsList = notCancelCarrierOrder.stream().map(TCarrierOrder::getId).collect(Collectors.toList());

            boolean demandOrderEntrustIsFixedPrice = false;//需求单是否一口价(需求单下面如果有一个运单是一口价那么需求单也是一口价)
            boolean demandOrderCarrierIsFixedPrice = false;//同上
            //计算需求单下排除当前修改的运单的费用总和(编辑运单费用为一口价时,需求单也为一口价)
            BigDecimal demandOrderExpectEntrustPrice = BigDecimal.ZERO;
            BigDecimal demandOrderEntrustPrice = BigDecimal.ZERO;
            BigDecimal demandOrderCarrierPrice = BigDecimal.ZERO;
            for (TCarrierOrder carrierOrder : notCancelCarrierOrder) {
                //排除当前运单
                if (!tCarrierOrder.getId().equals(carrierOrder.getId())) {
                    //获取货主价格类型,有结算数据优先使用结算数据的价格类型
                    int entrustFreightType = dbReceivement == null ? carrierOrder.getEntrustFreightType() : dbReceivement.getPriceType();
                    //获取车主价格类型,有结算数据优先使用结算数据的价格类型
                    int carrierPriceType = dbPayment == null ? carrierOrder.getCarrierPriceType() : dbPayment.getPriceType();

                    //货主价格类型
                    if (FreightTypeEnum.FIXED_PRICE.getKey().equals(entrustFreightType)) {//一口价
                        demandOrderEntrustIsFixedPrice = true;

                        demandOrderEntrustPrice = demandOrderEntrustPrice.add(carrierOrder.getEntrustFreight());

                        demandOrderExpectEntrustPrice = demandOrderExpectEntrustPrice.add(carrierOrder.getExpectEntrustFreight());
                    } else {//单价
                        //获取货主结算吨位及费用合计
                        BigDecimal settlementGoodsAmount = getGoodsAmount(tCarrierOrder.getSettlementTonnage(), carrierOrder);
                        demandOrderEntrustPrice = carrierOrder.getEntrustFreight().multiply(settlementGoodsAmount);

                        //获取货主预计结算费用
                        demandOrderExpectEntrustPrice = demandOrderExpectEntrustPrice.add(carrierOrder.getEntrustFreight().multiply(carrierOrder.getExpectAmount()));
                    }
                    //车主价格类型
                    if (FreightTypeEnum.FIXED_PRICE.getKey().equals(carrierPriceType)) {//一口价
                        demandOrderCarrierIsFixedPrice = true;

                        demandOrderCarrierPrice = demandOrderCarrierPrice.add(carrierOrder.getCarrierPrice());
                    } else {//单价
                        //获取车主结算吨位及费用合计
                        BigDecimal carrierSettlementGoodsAmount = getGoodsAmount(tCarrierOrder.getCarrierSettlement(), carrierOrder);
                        demandOrderCarrierPrice = carrierOrder.getCarrierPrice().multiply(carrierSettlementGoodsAmount);
                    }
                }
            }

            //需求单是否已经触发签收或已放空
            boolean isSing = DemandOrderStatusEnum.SIGN_DISPATCH.getKey().equals(tDemandOrder.getEntrustStatus()) || CommonConstant.INTEGER_ONE.equals(tDemandOrder.getIfEmpty());

            //判断哪种费用
            if (CommonConstant.INTEGER_ONE.equals(requestModel.getCostType())) {
                //修改货主费用

                //是否更新司机费用
                boolean isUpdateDispatchFreight = true;

                //查询运单车主信息
                TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(tCarrierOrder.getCompanyCarrierId());
                if (tCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(tCompanyCarrier.getValid())) {
                    throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
                }
                //其他车主不更新司机费用
                if (IsOurCompanyEnum.OTHER_COMPANY.getKey().equals(tCompanyCarrier.getLevel())) {
                    isUpdateDispatchFreight = false;
                }

                //查询货主结算费用
                List<TDemandReceivement> demandReceivementList = tDemandReceivementMapper.getByDemandOrderIds(ConverterUtils.toString(tDemandOrder.getId()));
                if (ListUtils.isNotEmpty(demandReceivementList)) {
                    TDemandReceivement tDemandReceivement = demandReceivementList.get(CommonConstant.INTEGER_ZERO);
                    //货主已付款无法编辑
                    if (EntrustSettlementStatusEnum.PAYMENT.getKey().equals(tDemandReceivement.getStatus())) {
                        throw new BizException(CarrierDataExceptionEnum.ENTRUST_SETTLEMENT_PAY);
                    }
                }

                //查询运单车辆信息
                DriverAndVehicleResponseModel carrierOrderVehicle = tCarrierOrderVehicleHistoryMapper.getDriverVehicleInfoByCarrierOrderId(tCarrierOrder.getId());
                if (carrierOrderVehicle == null) {
                    throw new BizException(CarrierDataExceptionEnum.VEHICLE_NUMBER_NOT_EXIST);
                }

                //需求单下排除当前运单的所有结算费用
                BigDecimal entrustSettlementPriceTotal = BigDecimal.ZERO;
                //查询需求单下所有结算数据
                List<TReceivement> tReceivementList = tReceivementMapper.getByCarrierOrderIds(LocalStringUtil.listTostring(demandOrderCarrierOrderIdsList, ','));
                for (TReceivement tReceivement : tReceivementList) {
                    //排除当前运单
                    if (!tReceivement.getCarrierOrderId().equals(requestModel.getCarrierOrderId())) {
                        entrustSettlementPriceTotal = entrustSettlementPriceTotal.add(tReceivement.getSettlementCostTotal());
                    }
                }

                //判断车辆机构是外部车辆还是自有车辆
                if (VehiclePropertyEnum.EXTERNAL_VEHICLE.getKey().equals(carrierOrderVehicle.getVehicleProperty())) {
                    //查询外部司机结算列表
                    List<TExtVehicleSettlement> exVehicleSettlementList = tExtVehicleSettlementMapper.getByCarrierOrderIds(LocalStringUtil.listTostring(Collections.singletonList(tCarrierOrder.getId()), ','));
                    if (ListUtils.isNotEmpty(exVehicleSettlementList)) {
                        //是否未付款
                        TExtVehicleSettlement tExtVehicleSettlement = exVehicleSettlementList.get(CommonConstant.INTEGER_ZERO);
                        if (VehicleSettlementStatusEnum.NOT_PAYMENT.getKey().equals(tExtVehicleSettlement.getStatus())) {
                            //更新费用
                            upExtVehicleSettlement = new TExtVehicleSettlement();
                            if (FreightTypeEnum.FIXED_PRICE.getKey().equals(requestModel.getPriceType())) {
                                upExtVehicleSettlement.setDriverTotalFee(requestModel.getPrice());
                            } else {
                                upExtVehicleSettlement.setDriverTotalFee(tExtVehicleSettlement.getSettlementAmount().multiply(requestModel.getPrice()));
                            }
                        } else {
                            isUpdateDispatchFreight = false;
                        }
                    }
                } else {
                    //查询自有车辆结算
                    List<TVehicleSettlementRelation> vehicleSettlementList = tVehicleSettlementRelationMapper.getByObjectIdAndType(VehicleSettlementTypeEnum.CARRIER_ORDER.getKey(), tCarrierOrder.getId());
                    if (ListUtils.isNotEmpty(vehicleSettlementList)) {
                        isUpdateDispatchFreight = false;
                    }
                }

                TDemandReceivement tDemandReceivement = tDemandReceivementMapper.getByDemandOrderId(tDemandOrder.getId());
                upReceivement = new TReceivement();
                upDemandReceivement = new TDemandReceivement();
                //如果是单价直接修改,如果是一口价则需求单也是一口价
                if (FreightTypeEnum.FIXED_PRICE.getKey().equals(requestModel.getPriceType())) {
                    //一口价

                    //修改货主费用
                    upCarrierOrder.setEntrustFreightType(requestModel.getPriceType());
                    upCarrierOrder.setEntrustFreight(requestModel.getPrice());
                    upCarrierOrder.setSignFreightFee(requestModel.getPrice());
                    upCarrierOrder.setExpectEntrustFreightType(requestModel.getPriceType());
                    upCarrierOrder.setExpectEntrustFreight(requestModel.getPrice());

                    //修改需求单货主价格
                    upDemandOrder.setContractPriceType(requestModel.getPriceType());
                    upDemandOrder.setContractPrice(demandOrderEntrustPrice.add(requestModel.getPrice()));
                    upDemandOrder.setExpectContractPriceType(requestModel.getPriceType());
                    upDemandOrder.setExpectContractPrice(demandOrderExpectEntrustPrice.add(requestModel.getPrice()));

                    //已结算
                    if (isSing && dbReceivement != null) {
                        //更新运单货主结算信息
                        upReceivement.setId(dbReceivement.getId());
                        upReceivement.setPriceType(requestModel.getPriceType());
                        upReceivement.setSettlementCostTotal(requestModel.getPrice());

                        //更新需求单货主结算信息
                        upDemandReceivement.setId(tDemandReceivement.getId());
                        upDemandReceivement.setPriceType(requestModel.getPriceType());
                        upDemandReceivement.setSettlementCostTotal(entrustSettlementPriceTotal.add(requestModel.getPrice()));
                    }
                } else {
                    //单价

                    BigDecimal signFreightFee = getGoodsAmount(tCarrierOrder.getSettlementTonnage(), tCarrierOrder).multiply(requestModel.getPrice()).setScale(2, RoundingMode.HALF_UP);

                    //修改货主费用
                    upCarrierOrder.setEntrustFreightType(requestModel.getPriceType());
                    upCarrierOrder.setEntrustFreight(requestModel.getPrice());
                    upCarrierOrder.setSignFreightFee(signFreightFee);
                    upCarrierOrder.setExpectEntrustFreightType(requestModel.getPriceType());
                    upCarrierOrder.setExpectEntrustFreight(requestModel.getPrice());

                    //需求单货主费用
                    if (demandOrderEntrustIsFixedPrice) {
                        upDemandOrder.setContractPriceType(FreightTypeEnum.FIXED_PRICE.getKey());
                        upDemandOrder.setContractPrice(demandOrderEntrustPrice.add(signFreightFee));
                        upDemandOrder.setExpectContractPriceType(FreightTypeEnum.FIXED_PRICE.getKey());
                        upDemandOrder.setExpectContractPrice(demandOrderExpectEntrustPrice.add(signFreightFee));
                    } else {
                        upDemandOrder.setContractPriceType(requestModel.getPriceType());
                        upDemandOrder.setContractPrice(requestModel.getPrice());
                        upDemandOrder.setExpectContractPriceType(requestModel.getPriceType());
                        upDemandOrder.setExpectContractPrice(requestModel.getPrice());
                    }

                    //已结算
                    if (isSing && dbReceivement != null) {

                        //更新运单货主结算信息
                        upReceivement.setId(dbReceivement.getId());
                        upReceivement.setPriceType(requestModel.getPriceType());
                        upReceivement.setSettlementCostTotal(signFreightFee);

                        //更新需求单货主结算信息
                        upDemandReceivement.setId(tDemandReceivement.getId());
                        upDemandReceivement.setSettlementCostTotal(entrustSettlementPriceTotal.add(signFreightFee));
                        if (demandOrderEntrustIsFixedPrice) {
                            upDemandReceivement.setPriceType(FreightTypeEnum.FIXED_PRICE.getKey());
                        } else {
                            upDemandReceivement.setPriceType(requestModel.getPriceType());
                        }
                    }
                }
                //未生成结算时修改货主费用时修改司机费用
                if (isUpdateDispatchFreight) {
                    upCarrierOrder.setDispatchFreightFeeType(requestModel.getPriceType());
                    upCarrierOrder.setDispatchFreightFee(requestModel.getPrice());
                }
            } else if (CommonConstant.INTEGER_TWO.equals(requestModel.getCostType())) {
                //修改车主费用

                //运单已关联对账不可修改
                if (tCarrierOrder.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
                    throw new BizException(CarrierDataExceptionEnum.ALREADY_RELEVANCY_RECONCILIATION);
                }

                //需求单下排除当前运单的所有结算费用
                BigDecimal carrierSettlementPriceTotal = BigDecimal.ZERO;
                //查询需求单下所有结算数据
                List<TPayment> tPaymentList = tPaymentMapper.getByCarrierOrderIds(LocalStringUtil.listTostring(demandOrderCarrierOrderIdsList, ','));
                for (TPayment tPayment : tPaymentList) {
                    //排除当前运单
                    if (!tPayment.getCarrierOrderId().equals(requestModel.getCarrierOrderId())) {
                        carrierSettlementPriceTotal = carrierSettlementPriceTotal.add(tPayment.getSettlementCostTotal());
                    }
                }

                TDemandPayment tDemandPayment = tDemandPaymentMapper.getByDemandOrderId(tDemandOrder.getId());
                upPayment = new TPayment();
                upDemandPayment = new TDemandPayment();

                //如果是单价直接修改,如果是一口价则需求单也是一口价
                if (FreightTypeEnum.FIXED_PRICE.getKey().equals(requestModel.getPriceType())) {
                    //修改运单车主费用
                    upCarrierOrder.setCarrierPriceType(requestModel.getPriceType());
                    //当前需求单下的运单费用总和加上当前运单的一口价
                    upCarrierOrder.setCarrierPrice(requestModel.getPrice());

                    //更新需求单车主费用
                    upDemandOrder.setCarrierPriceType(requestModel.getPriceType());
                    //当前需求单下的运单费用总和加上当前运单的一口价
                    upDemandOrder.setCarrierPrice(requestModel.getPrice().add(demandOrderCarrierPrice));

                    //已结算
                    if (isSing && dbPayment != null) {
                        //更新运单货主结算信息
                        upPayment.setId(dbPayment.getId());
                        upPayment.setPriceType(requestModel.getPriceType());
                        upPayment.setSettlementCostTotal(requestModel.getPrice());

                        //更新需求单货主结算信息
                        upDemandPayment.setId(tDemandPayment.getId());
                        upDemandPayment.setPriceType(requestModel.getPriceType());
                        upDemandPayment.setSettlementCostTotal(carrierSettlementPriceTotal.add(requestModel.getPrice()));
                    }
                } else {
                    //单价

                    BigDecimal signFreightFee = getGoodsAmount(tCarrierOrder.getCarrierSettlement(), tCarrierOrder).multiply(requestModel.getPrice()).setScale(2, RoundingMode.HALF_UP);

                    //运单车主费用
                    upCarrierOrder.setCarrierPriceType(requestModel.getPriceType());
                    upCarrierOrder.setCarrierPrice(requestModel.getPrice());

                    //需求单车主费用
                    if (demandOrderCarrierIsFixedPrice) {
                        upDemandOrder.setCarrierPriceType(FreightTypeEnum.FIXED_PRICE.getKey());
                        upDemandOrder.setCarrierPrice(demandOrderCarrierPrice.add(signFreightFee));
                    } else {
                        upDemandOrder.setCarrierPriceType(requestModel.getPriceType());
                        upDemandOrder.setCarrierPrice(requestModel.getPrice());
                    }

                    //已结算
                    if (isSing && dbPayment != null) {
                        BigDecimal paymentPrice = dbPayment.getSettlementAmount().multiply(requestModel.getPrice());
                        //更新运单货主结算信息
                        upPayment.setId(dbPayment.getId());
                        upPayment.setPriceType(requestModel.getPriceType());
                        upPayment.setSettlementCostTotal(paymentPrice);

                        //更新需求单货主结算信息
                        upDemandPayment.setId(tDemandPayment.getId());
                        upDemandPayment.setSettlementCostTotal(carrierSettlementPriceTotal.add(paymentPrice));
                        if (demandOrderCarrierIsFixedPrice) {
                            upDemandPayment.setPriceType(FreightTypeEnum.FIXED_PRICE.getKey());
                        } else {
                            upDemandPayment.setPriceType(requestModel.getPriceType());
                        }
                    }
                }
            }

            //更新运单
            commonBiz.setBaseEntityModify(upCarrierOrder, BaseContextHandler.getUserName());
            tCarrierOrderMapper.updateByPrimaryKeySelectiveEncrypt(upCarrierOrder);

            //更新需求单
            commonBiz.setBaseEntityModify(upDemandOrder, BaseContextHandler.getUserName());
            tDemandOrderMapper.updateByPrimaryKeySelectiveEncrypt(upDemandOrder);

            //更新外部司机结算表费用
            if (upExtVehicleSettlement != null) {
                commonBiz.setBaseEntityModify(upExtVehicleSettlement, BaseContextHandler.getUserName());
                tExtVehicleSettlementMapper.updateByPrimaryKeySelective(upExtVehicleSettlement);
            }

            //更新结算表
            if (upPayment != null) {
                commonBiz.setBaseEntityModify(upPayment, BaseContextHandler.getUserName());
                tPaymentMapper.updateByPrimaryKeySelective(upPayment);
            }

            if (upReceivement != null) {
                commonBiz.setBaseEntityModify(upReceivement, BaseContextHandler.getUserName());
                tReceivementMapper.updateByPrimaryKeySelective(upReceivement);
            }

            if (upDemandPayment != null) {
                commonBiz.setBaseEntityModify(upDemandPayment, BaseContextHandler.getUserName());
                tDemandPaymentMapper.updateByPrimaryKeySelective(upDemandPayment);
            }

            if (upDemandReceivement != null) {
                commonBiz.setBaseEntityModify(upDemandReceivement, BaseContextHandler.getUserName());
                tDemandReceivementMapper.updateByPrimaryKeySelective(upDemandReceivement);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            commonBiz.removeDistributedLock(redisKey, distributedLock);
        }
    }

    /**
     * 云盘运单签收详情
     *
     * @param requestModel 运单id
     * @return 运单签收详情
     */
    public List<CarrierOrderListBeforeSignUpForLeyiResponseModel> carrierOrderListBeforeSignUpForLeyi(CarrierOrderListBeforeSignUpRequestModel requestModel) {
        if (requestModel == null || ListUtils.isEmpty(requestModel.getCarrierOrderIds())) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }
        if (requestModel.getCarrierOrderIds().size() > 30) {
            throw new BizException(CarrierDataExceptionEnum.PLEASE_MAX_THIRTY_SIGN);
        }
        List<Long> carrierOrderIdList = requestModel.getCarrierOrderIds();
        //判断车辆是否处于未审核状态
        carrierOrderCommonBiz.checkIfCarrierOrdersStatusCanChange(carrierOrderIdList, CarrierDataExceptionEnum.VEHICLE_NEED_AUDIT_TIP1);

        String carrierOrderIds = StringUtils.listToString(carrierOrderIdList, ',');
        List<CarrierOrderListBeforeSignUpForLeyiResponseModel> carrierOrderList = tCarrierOrderMapper.selectCarrierOrderSignDetailForLeyi(carrierOrderIds);

        //查询运单纠错信息
        List<TCarrierOrderCorrect> tCarrierOrderCorrectList = tCarrierOrderCorrectMapper.getByCarrierOrderIds(carrierOrderIds);
        Map<Long, TCarrierOrderCorrect> carrierOrderCorrectMap = new HashMap<>();
        tCarrierOrderCorrectList.forEach(item -> carrierOrderCorrectMap.put(item.getCarrierOrderId(), item));

        //发货-供应商直配-退货仓库配送 判断客户是否入库，未入库不允许签收，提示“客户未入库”；
        List<Integer> checkEntrustTypeList = Arrays.asList(EntrustTypeEnum.DELIVER.getKey(), EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey(), EntrustTypeEnum.RETURN_GOODS_DISTRIBUTION.getKey());
        List<String> checkCarrierOrderCodeList = new ArrayList<>();

        //发货卸货区id
        AddressAreaExistRequestModel areaModel;
        List<AddressAreaExistRequestModel> areaList = new ArrayList<>();

        TCarrierOrderCorrect tCarrierOrderCorrect;
        List<Integer> goodsUnitList = new ArrayList<>();
        List<Integer> entrustTypeList = new ArrayList<>();
        List<Long> companyCarrierIdList = new ArrayList<>();
        List<Long> matchCarrierPriceCompanyCarrierIdList = new ArrayList<>();
        for (CarrierOrderListBeforeSignUpForLeyiResponseModel carrierOrderDetailModel : carrierOrderList) {
            //云盘的运单才能操作
            if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(carrierOrderDetailModel.getDemandOrderSource())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
            }
            if (!CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey().equals(carrierOrderDetailModel.getStatus())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
            }
            //运单取消不能操作
            if (CommonConstant.INTEGER_ONE.equals(carrierOrderDetailModel.getIfCancel())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
            }
            //运单取消不能操作
            if (CommonConstant.INTEGER_ONE.equals(carrierOrderDetailModel.getIfExtCarrierOrder())) {
                throw new BizException(EntrustDataExceptionEnum.SYSTEM_NOT_SUPPORT);
            }
            //运单放空不能操作
            if (CommonConstant.INTEGER_ONE.equals(carrierOrderDetailModel.getIfEmpty())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
            }
            if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(carrierOrderDetailModel.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(carrierOrderDetailModel.getEntrustType())) {
                tCarrierOrderCorrect = carrierOrderCorrectMap.get(carrierOrderDetailModel.getCarrierOrderId());
                if (tCarrierOrderCorrect != null) {
                    throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN_RECYCLE);
                } else {
                    throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_STATUS_NOT_ALLOW_SIGN);
                }
            }

            if (checkEntrustTypeList.contains(carrierOrderDetailModel.getEntrustType())) {
                checkCarrierOrderCodeList.add(carrierOrderDetailModel.getCarrierOrderCode());
            }

            //货物单位
            if (!goodsUnitList.contains(carrierOrderDetailModel.getGoodsUnit())) {
                goodsUnitList.add(carrierOrderDetailModel.getGoodsUnit());
            }
            //委托类型
            if (!entrustTypeList.contains(carrierOrderDetailModel.getEntrustType())) {
                entrustTypeList.add(carrierOrderDetailModel.getEntrustType());
            }
            //车主id
            if (!companyCarrierIdList.contains(carrierOrderDetailModel.getCompanyCarrierId())) {
                companyCarrierIdList.add(carrierOrderDetailModel.getCompanyCarrierId());
            }

            //非零担定价且不存在车主费用，则匹配车主运价
            if (!BargainingModeEnum.LESS_THAN_TRUCKLOAD_PRICING.getKey().equals(carrierOrderDetailModel.getBargainingMode())
                    && (carrierOrderDetailModel.getCarrierPriceType() == null || CommonConstant.INTEGER_ZERO.equals(carrierOrderDetailModel.getCarrierPriceType()))) {
                if (!matchCarrierPriceCompanyCarrierIdList.contains(carrierOrderDetailModel.getCompanyCarrierId())) {
                    matchCarrierPriceCompanyCarrierIdList.add(carrierOrderDetailModel.getCompanyCarrierId());
                }

                areaModel = new AddressAreaExistRequestModel();
                areaModel.setFromAreaId(carrierOrderDetailModel.getLoadAreaId());
                areaModel.setToAreaId(carrierOrderDetailModel.getUnloadAreaId());
                areaList.add(areaModel);
            }
        }
        //不同单位的不能一起操作
        if (ListUtils.isNotEmpty(goodsUnitList) && goodsUnitList.size() > CommonConstant.INTEGER_ONE) {
            throw new BizException(CarrierDataExceptionEnum.GOODS_UNIT_DIFFERENT);
        }
        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(carrierOrderIds);
        if (!workOrderMap.isEmpty()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        //发货-供应商直配-退货仓库配送 判断客户是否入库，未入库不允许签收，提示“客户未入库”；
        if (ListUtils.isNotEmpty(checkCarrierOrderCodeList)) {
            checkCarrierOrderInStock(checkCarrierOrderCodeList, entrustTypeList);
        }

        //查询车主信息
        Map<Long, Integer> companyLevelMap = tCompanyCarrierMapper.getByIds(StringUtils.listToString(companyCarrierIdList, ',')).stream().collect(Collectors.toMap(TCompanyCarrier::getId, TCompanyCarrier::getLevel));
        //匹配货主费用
        Map<String, BigDecimal> entrustPriceMap = carrierOrderCommonBiz.getEntrustPrice(entrustTypeList, goodsUnitList);

        //车主运价匹配需要用到的信息
        MatchCarrierPriceResultModel matchCarrierPriceStepOneModel = null;
        if (requestModel.isShowDetail() && ListUtils.isNotEmpty(matchCarrierPriceCompanyCarrierIdList)) {
            //因为签收接口调用了详情接口做判断,但是签收不需要匹配车主价格
            matchCarrierPriceStepOneModel = carrierOrderCommonBiz.matchCarrierPriceStepOneResult(matchCarrierPriceCompanyCarrierIdList, areaList);
        }

        //运单临时费用
        Map<Long, List<CarrierOrderOtherFeeItemModel>> carrierOrderOtherFeeMap = tCarrierOrderOtherFeeItemMapper.selectOtherFeeItemByCarrierIds(carrierOrderIds).stream().collect(Collectors.groupingBy(CarrierOrderOtherFeeItemModel::getCarrierOrderId));

        //组装信息
        GetCarrierPriceModel getCarrierPriceModel;
        BigDecimal entrustFreight;
        List<CarrierOrderOtherFeeItemModel> carrierOrderOtherFeeItems;
        for (CarrierOrderListBeforeSignUpForLeyiResponseModel signDetails : carrierOrderList) {
            //是否是我司
            signDetails.setIsOurCompany(companyLevelMap.get(signDetails.getCompanyCarrierId()));

            //货主费用
            entrustFreight = entrustPriceMap.get(signDetails.getEntrustType().toString()+signDetails.getGoodsUnit().toString());
            if (entrustFreight != null && entrustFreight.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                signDetails.setEntrustFreightType(ContractPriceTypeEnum.UNIT_PRICE.getKey());
                signDetails.setEntrustFreight(entrustFreight);
            }else{
                signDetails.setEntrustFreightType(ContractPriceTypeEnum.DEFAULT.getKey());
                signDetails.setEntrustFreight(BigDecimal.ZERO);
            }

            //车主实际结算数量
            BigDecimal carrierSettlementAmount = BigDecimal.ZERO;
            if (SettlementTonnageEnum.LOAD.getKey().equals(signDetails.getCarrierSettlement())) {
                carrierSettlementAmount = signDetails.getLoadAmount();
            } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(signDetails.getCarrierSettlement())) {
                carrierSettlementAmount = signDetails.getUnloadAmount();
            } else if (SettlementTonnageEnum.SIGN.getKey().equals(signDetails.getCarrierSettlement())) {
                carrierSettlementAmount = signDetails.getUnloadAmount();
            } else if (SettlementTonnageEnum.EXPECT.getKey().equals(signDetails.getCarrierSettlement())) {
                carrierSettlementAmount = signDetails.getExpectAmount();
            }

            //非零担定价，结算数量大于0，且不存在车主费用
            if (!BargainingModeEnum.LESS_THAN_TRUCKLOAD_PRICING.getKey().equals(signDetails.getBargainingMode())
                    && carrierSettlementAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO
                    && (signDetails.getCarrierPriceType() == null || CommonConstant.INTEGER_ZERO.equals(signDetails.getCarrierPriceType()))
                    && matchCarrierPriceStepOneModel != null
                    && matchCarrierPriceStepOneModel.getEntrustCarrierFreightMap().get(signDetails.getCompanyCarrierId()) != null
                    && matchCarrierPriceStepOneModel.getEntrustCarrierFreightMap().get(signDetails.getCompanyCarrierId()).get(signDetails.getEntrustType()) != null) {

                getCarrierPriceModel = MapperUtils.mapper(signDetails, GetCarrierPriceModel.class);
                getCarrierPriceModel.setGoodsAmount(carrierSettlementAmount);
                //能获取到说明配置了车主价格
                Map<Integer, CarrierFreightCarrierModel> entrustFreightMap = matchCarrierPriceStepOneModel.getEntrustCarrierFreightMap().get(signDetails.getCompanyCarrierId());
                CarrierFreightCarrierModel carrierFreightCarrierModel = entrustFreightMap.get(signDetails.getEntrustType());
                carrierOrderCommonBiz.matchCarrierPrice(matchCarrierPriceStepOneModel.getTRouteDistanceConfigs(), matchCarrierPriceStepOneModel.getCarrierFreightConfigSchemeMap(), getCarrierPriceModel, carrierFreightCarrierModel);
                //设置车主价格
                signDetails.setCarrierPriceType(getCarrierPriceModel.getCarrierPriceType());
                signDetails.setCarrierPrice(getCarrierPriceModel.getCarrierPrice());
            }

            //临时费用
            BigDecimal otherFee = CommonConstant.BIG_DECIMAL_ZERO;
            carrierOrderOtherFeeItems = carrierOrderOtherFeeMap.get(signDetails.getCarrierOrderId());
            if (ListUtils.isNotEmpty(carrierOrderOtherFeeItems)) {
                for (CarrierOrderOtherFeeItemModel carrierOrderOtherFeeItem : carrierOrderOtherFeeItems) {
                    otherFee = otherFee.add(carrierOrderOtherFeeItem.getFeeAmount());
                }
            }
            signDetails.setOtherFee(otherFee);
        }
        return carrierOrderList;
    }


    /**
     * 发货-供应商直配-退货仓库配送,判断运单是否入库
     *
     * @param checkCarrierOrderCodeList 运单号集合
     */
    private void checkCarrierOrderInStock(List<String> checkCarrierOrderCodeList, List<Integer> entrustTypeList) {
        List<GetCustomerInOrderStateResponseModel> carrierOrderInStockInfos = trayOrderServiceClient.getCustomerStateByCarrierOrderCodes(checkCarrierOrderCodeList);
        if (ListUtils.isEmpty(carrierOrderInStockInfos)
                && !entrustTypeList.contains(EntrustTypeEnum.DELIVER.getKey())
                && !entrustTypeList.contains(EntrustTypeEnum.SUPPLIER_DIRECT_DISTRIBUTION.getKey())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NO_IN_STOCK);
        }
        List<Integer> stockInStateList = Arrays.asList(StockInStateEnum.STOCK_IN.getKey(), StockInStateEnum.ABNORMAL_ROLLBACK_WAIT_AUDIT.getKey(), StockInStateEnum.ABNORMAL_ROLLBACK.getKey());
        for (GetCustomerInOrderStateResponseModel carrierOrderInStockInfo : carrierOrderInStockInfos) {
            if (!stockInStateList.contains(carrierOrderInStockInfo.getState())) {
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NO_IN_STOCK);
            }
        }
    }

    /**
     * 云盘运单签收
     *
     * @param requestModel 签收详情
     */
    public void carrierOrderSignUpForLeyi(CarrierOrderConfirmSignUpForLeyiRequestModel requestModel) {
        if (requestModel == null || ListUtils.isEmpty(requestModel.getSignList())) {
            throw new BizException(CarrierDataExceptionEnum.REQUEST_PARAM_ERROR);
        }
        if (requestModel.getSignList().size() > 30) {
            throw new BizException(CarrierDataExceptionEnum.PLEASE_MAX_THIRTY_SIGN);
        }

        Date now = new Date();
        TCarrierOrder upCarrierOrder;
        List<TCarrierOrder> batchUpdateCarrierOrders = new ArrayList<>();
        TCarrierOrderGoods upGoods;
        List<TCarrierOrderGoods> batchUpdateCarrierGoods = new ArrayList<>();
        TCarrierOrderEvents carrierOrderEvents;
        List<TCarrierOrderEvents> insertEvents = new ArrayList<>();
        TCarrierOrderOperateLogs carrierOrderOperateLogs;
        List<TCarrierOrderOperateLogs> insertLogs = new ArrayList<>();
        String unit;
        //签收时间
        Date signTime = requestModel.getSignTime() == null ? now : requestModel.getSignTime();
        //签收备注
        String signRemark = "";
        if (signTime != null) {
            signRemark = "，修改签收时间为" + DateUtils.dateToString(signTime, "yyyy年MM月dd日");
        }

        //查询运单签收详情
        List<Long> carrierOrderIds = requestModel.getSignList().stream().map(CarrierOrderSignUpForLeyiRequestModel::getCarrierOrderId).collect(Collectors.toList());
        CarrierOrderListBeforeSignUpRequestModel carrierOrderDetailRequest = new CarrierOrderListBeforeSignUpRequestModel();
        carrierOrderDetailRequest.setCarrierOrderIds(carrierOrderIds);
        List<CarrierOrderListBeforeSignUpForLeyiResponseModel> carrierOrderSignList = carrierOrderListBeforeSignUpForLeyi(carrierOrderDetailRequest);
        Map<Long, CarrierOrderListBeforeSignUpForLeyiResponseModel> carrierOrderSignMap = carrierOrderSignList.stream().collect(Collectors.toMap(CarrierOrderListBeforeSignUpForLeyiResponseModel::getCarrierOrderId, item -> item));
        //查询运单货物
        Map<Long, List<TCarrierOrderGoods>> carrierOrderGoodsSignMap = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderIds(LocalStringUtil.listTostring(carrierOrderIds, ',')).stream().collect(Collectors.groupingBy(TCarrierOrderGoods::getCarrierOrderId));

        List<Long> updateCarrierOrderMileageIdList = new ArrayList<>();
        List<TCarrierOrder> synCarrierOrderList = new ArrayList<>();//更新的托盘运单
        List<Long> synCarrierOrderIdList = new ArrayList<>();//更新的托盘运单id
        List<Long> synCarrierOrderIdRecycleList = new ArrayList<>();//回收类型的的托盘运单id
        Map<Long, Integer> carrierOrderEntrustTypeMap = new HashMap<>();
        List<Long> upExtDriverFeeCarrierOrderIdList = new ArrayList<>();
        Map<Long, BigDecimal> upExtDriverFeeMap = new HashMap<>();//我司时司机费用：运单id-》司机费用

        //查询计费距离配置
        List<AddressAreaExistRequestModel> areaList = carrierOrderSignList.stream().map(carrierOrderInfo -> {
            AddressAreaExistRequestModel model = new AddressAreaExistRequestModel();
            model.setFromAreaId(carrierOrderInfo.getLoadAreaId());
            model.setToAreaId(carrierOrderInfo.getUnloadAreaId());
            return model;
        }).collect(Collectors.toList());
        Map<String, BigDecimal> configDistanceMap = carrierOrderCommonBiz.getConfigDistanceMap(areaList);

        for (CarrierOrderSignUpForLeyiRequestModel carrierOrderSignInfo : requestModel.getSignList()) {

            CarrierOrderListBeforeSignUpForLeyiResponseModel carrierOrderSignModel = carrierOrderSignMap.get(carrierOrderSignInfo.getCarrierOrderId());
            if (carrierOrderSignModel != null) {
                //没有预计里程数,更新预计里程数
                if (!(carrierOrderSignModel.getExpectMileage() != null
                        && carrierOrderSignModel.getExpectMileage().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO)) {
                    updateCarrierOrderMileageIdList.add(carrierOrderSignInfo.getCarrierOrderId());
                }

                //其他车主时校验金额
                if (IsOurCompanyEnum.OTHER_COMPANY.getKey().equals(carrierOrderSignModel.getIsOurCompany())) {
                    if (carrierOrderSignInfo.getSignCarrierFreight().compareTo(BigDecimal.ZERO) <= CommonConstant.INTEGER_ZERO ||
                            carrierOrderSignInfo.getSignCarrierFreight().compareTo(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_THOUSAND) > CommonConstant.INTEGER_ZERO) {
                        throw new BizException(CarrierDataExceptionEnum.SIGN_CARRIER_PRICE_ERROR);
                    }
                }

                List<TCarrierOrderGoods> tCarrierOrderGoods = carrierOrderGoodsSignMap.get(carrierOrderSignInfo.getCarrierOrderId());
                //更新货物
                for (TCarrierOrderGoods goods : tCarrierOrderGoods) {
                    upGoods = new TCarrierOrderGoods();
                    upGoods.setId(goods.getId());
                    upGoods.setSignAmount(goods.getUnloadAmount());
                    commonBiz.setBaseEntityModify(upGoods, BaseContextHandler.getUserName());
                    batchUpdateCarrierGoods.add(upGoods);
                }

                //货主费用
                BigDecimal entrustFee = carrierOrderSignInfo.getActualEntrustFee();

                //更新运单
                upCarrierOrder = new TCarrierOrder();
                upCarrierOrder.setId(carrierOrderSignInfo.getCarrierOrderId());
                upCarrierOrder.setStatus(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey());
                upCarrierOrder.setStatusUpdateTime(now);
                upCarrierOrder.setExpectEntrustFreightType(FreightTypeEnum.FIXED_PRICE.getKey());
                upCarrierOrder.setExpectEntrustFreight(entrustFee);
                upCarrierOrder.setEntrustFreightType(FreightTypeEnum.FIXED_PRICE.getKey());
                upCarrierOrder.setEntrustFreight(entrustFee);
                upCarrierOrder.setSignFreightFee(entrustFee);
                upCarrierOrder.setCarrierPriceType(FreightTypeEnum.FIXED_PRICE.getKey());
                upCarrierOrder.setCarrierPrice(carrierOrderSignInfo.getSignCarrierFreight());
                //我司时，司机费用=货主费用
                if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(carrierOrderSignModel.getIsOurCompany())) {
                    upCarrierOrder.setDispatchFreightFeeType(FreightTypeEnum.FIXED_PRICE.getKey());
                    upCarrierOrder.setDispatchFreightFee(entrustFee);

                    upExtDriverFeeCarrierOrderIdList.add(upCarrierOrder.getId());
                    upExtDriverFeeMap.put(upCarrierOrder.getId(), upCarrierOrder.getDispatchFreightFee());
                }
                //回收入库、回收出库、预约类型
                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(carrierOrderSignModel.getEntrustType())
                        || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(carrierOrderSignModel.getEntrustType())
                        || EntrustTypeEnum.BOOKING.getKey().equals(carrierOrderSignModel.getEntrustType())) {
                    synCarrierOrderIdRecycleList.add(carrierOrderSignInfo.getCarrierOrderId());
                }
                upCarrierOrder.setSignTime(signTime);
                upCarrierOrder.setSignAmount(carrierOrderSignModel.getUnloadAmount());
                upCarrierOrder.setConfigDistance(configDistanceMap.get(carrierOrderSignModel.getLoadAreaId() + CommonConstant.COMMA + carrierOrderSignModel.getUnloadAreaId()));
                commonBiz.setBaseEntityModify(upCarrierOrder, BaseContextHandler.getUserName());
                batchUpdateCarrierOrders.add(upCarrierOrder);

                unit = GoodsUnitEnum.getEnum(carrierOrderSignModel.getGoodsUnit()).getUnit();
                //生成运单事件
                carrierOrderEvents = new TCarrierOrderEvents();
                carrierOrderEvents.setCarrierOrderId(carrierOrderSignInfo.getCarrierOrderId());
                carrierOrderEvents.setEvent(CarrierOrderEventsTypeEnum.SIGN_IN.getKey());
                carrierOrderEvents.setEventDesc(CarrierOrderEventsTypeEnum.SIGN_IN.getValue());
                carrierOrderEvents.setRemark(CarrierOrderEventsTypeEnum.SIGN_IN.format(StripTrailingZerosUtils.stripTrailingZerosToString(upCarrierOrder.getSignAmount()) + unit));
                carrierOrderEvents.setEventTime(now);
                carrierOrderEvents.setOperatorName(BaseContextHandler.getUserName());
                carrierOrderEvents.setOperateTime(now);
                commonBiz.setBaseEntityAdd(carrierOrderEvents, BaseContextHandler.getUserName());
                insertEvents.add(carrierOrderEvents);

                //生成操作日志
                carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
                carrierOrderOperateLogs.setCarrierOrderId(carrierOrderSignInfo.getCarrierOrderId());
                carrierOrderOperateLogs.setOperationType(CarrierOrderOperateLogsTypeEnum.SIGN_IN.getKey());
                carrierOrderOperateLogs.setOperationContent(CarrierOrderOperateLogsTypeEnum.SIGN_IN.getValue());
                carrierOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
                carrierOrderOperateLogs.setOperateTime(now);
                carrierOrderOperateLogs.setRemark(CarrierOrderOperateLogsTypeEnum.SIGN_IN.format(StripTrailingZerosUtils.stripTrailingZerosToString(upCarrierOrder.getSignAmount()) + unit, ConverterUtils.toString(carrierOrderSignInfo.getActualEntrustFee())) + signRemark);
                commonBiz.setBaseEntityAdd(carrierOrderOperateLogs, BaseContextHandler.getUserName());
                insertLogs.add(carrierOrderOperateLogs);

                upCarrierOrder.setCarrierOrderCode(carrierOrderSignModel.getCarrierOrderCode());
                synCarrierOrderList.add(upCarrierOrder);
                synCarrierOrderIdList.add(upCarrierOrder.getId());
                carrierOrderEntrustTypeMap.put(carrierOrderSignInfo.getCarrierOrderId(), carrierOrderSignModel.getEntrustType());
            }
        }
        if (ListUtils.isNotEmpty(batchUpdateCarrierOrders)) {
            tCarrierOrderMapper.batchUpdateCarrierOrders(batchUpdateCarrierOrders);
        }
        if (ListUtils.isNotEmpty(batchUpdateCarrierGoods)) {
            tCarrierOrderGoodsMapper.batchUpdateCarrierOrderGoods(batchUpdateCarrierGoods);
        }
        if (ListUtils.isNotEmpty(insertEvents)) {
            tCarrierOrderEventsMapper.batchInsertSelective(insertEvents);
        }
        if (ListUtils.isNotEmpty(insertLogs)) {
            tCarrierOrderOperateLogsMapper.batchInsertSelective(insertLogs);
        }
        //更新外部车辆结算司机费用
        if (ListUtils.isNotEmpty(upExtDriverFeeCarrierOrderIdList)){
            extVehicleSettlementBiz.updateDriverFee(upExtDriverFeeCarrierOrderIdList, upExtDriverFeeMap);
        }

        //根据运单所属需求单Id查询需求单状态
        List<Long> demandOrderIdList = carrierOrderSignList.stream().map(CarrierOrderListBeforeSignUpForLeyiResponseModel::getDemandOrderId).distinct().collect(Collectors.toList());
        List<GetDemandOrderInfoByIdsModel> demandOrderInfoList = tDemandOrderMapper.getDemandOrderInfoByIds(StringUtils.listToString(demandOrderIdList, ','));
        List<Long> demandOrderIds = new ArrayList<>();
        //需求单货物数量map
        demandOrderInfoList.forEach(demandOrder -> {
            if (demandOrder.getEntrustStatus() < DemandOrderStatusEnum.SIGN_DISPATCH.getKey()) {
                demandOrderIds.add(demandOrder.getDemandOrderId());
            }
        });
        if (ListUtils.isNotEmpty(demandOrderIds)) {
            //如果需求单下所有运单（除已取消的）均为已签收状态，则判断是否将需求单置为已签收状态
            List<DemandCarrierOrderRecursiveModel> demandCarrierOrderList = tCarrierOrderMapper.getNotCancelByDemandOrderIds(StringUtils.listToString(demandOrderIds, ','));
            List<Long> demandIds = new ArrayList<>();
            for (DemandCarrierOrderRecursiveModel demandOrderInfo : demandCarrierOrderList) {
                DemandCarrierOrderModel demandCarrierOrderModel = demandOrderInfo.getCarrierOrderList().stream().filter(carrierOrder -> !CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(carrierOrder.getStatus())).findFirst().orElse(null);
                if (demandCarrierOrderModel == null) {
                    demandIds.add(demandOrderInfo.getDemandOrderId());
                }
            }
            if (ListUtils.isNotEmpty(demandIds)) {
                //修改需求单状态
                UpdateDemandOrderStatusByIdsRequestModel updateDemandOrderStatusByIdsRequestModel = new UpdateDemandOrderStatusByIdsRequestModel();
                updateDemandOrderStatusByIdsRequestModel.setDemandOrderIdList(demandIds);
                updateDemandOrderStatusByIdsRequestModel.setEntrustStatus(DemandOrderStatusEnum.SIGN_DISPATCH.getKey());
                updateDemandOrderStatusByIdsRequestModel.setOperatorName(BaseContextHandler.getUserName());
                demandOrderBiz.updateDemandOrderStatusByIds(updateDemandOrderStatusByIdsRequestModel);
            }
        }

        //过滤零担运单 校验生成零担费用
        List<Long> lessThanTruckLoadCarrierOrderIds = carrierOrderSignList.stream().filter(carrierModel -> BargainingModeEnum.LESS_THAN_TRUCKLOAD_PRICING.getKey().equals(carrierModel.getBargainingMode()))
                .map(CarrierOrderListBeforeSignUpForLeyiResponseModel::getCarrierOrderId).collect(Collectors.toList());
        shippingOrderCommonBiz.checkAndGenerateShippingFeeFromCarrierOrderAction(lessThanTruckLoadCarrierOrderIds);

        if (ListUtils.isNotEmpty(updateCarrierOrderMileageIdList)) {
            AsyncProcessQueue.execute(() -> carrierOrderCommonBiz.carrierOrderMileage(updateCarrierOrderMileageIdList, null));
        }

        //同步云盘消息
        if (ListUtils.isNotEmpty(synCarrierOrderList)) {
            signCarrierOrderSyn(synCarrierOrderList, synCarrierOrderIdList, synCarrierOrderIdRecycleList, carrierOrderEntrustTypeMap);
        }
    }

    /**
     * 运单签收-同步托盘
     *
     * @param synCarrierOrderList          更新的云盘运单
     * @param synCarrierOrderIdList        更新的云盘运单id
     * @param synCarrierOrderIdRecycleList 回收类型云盘运单id
     */
    public void signCarrierOrderSyn(List<TCarrierOrder> synCarrierOrderList, List<Long> synCarrierOrderIdList, List<Long> synCarrierOrderIdRecycleList, Map<Long, Integer> carrierOrderEntrustTypeMap) {
        List<TCarrierOrderOrderRel> oldCarrierOrderRels;
        TCarrierOrderOrderRel upOrderRel;
        List<TCarrierOrderOrderRel> upCarrierOrderRels = new ArrayList<>();
        CarrierOrderSynchronizeModel syncModel;
        List<CarrierOrderSynchronizeModel> synchronizeModels = new ArrayList<>();
        List<CarrierOrderSynchronizeModel> synchronizeToLeYiModels = new ArrayList<>();
        OrderAmountModel orderSignAmountModel;
        List<OrderAmountModel> upOrderSignAmountModels;

        //查询销售单数据,同步托盘
        Map<Long, List<TCarrierOrderOrderRel>> dbCarrierOrderRelMap = new HashMap<>();
        List<TCarrierOrderOrderRel> dbCarrierOrderRels = tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(StringUtils.listToString(synCarrierOrderIdList, ','));
        if (ListUtils.isNotEmpty(dbCarrierOrderRels)) {
            dbCarrierOrderRelMap = dbCarrierOrderRels.stream().collect(Collectors.groupingBy(TCarrierOrderOrderRel::getCarrierOrderId, Collectors.toList()));
        }

        for (TCarrierOrder tmp : synCarrierOrderList) {
            //更新销售单数据
            upOrderSignAmountModels = new ArrayList<>();
            oldCarrierOrderRels = dbCarrierOrderRelMap.get(tmp.getId());
            if (ListUtils.isNotEmpty(oldCarrierOrderRels)) {
                BigDecimal restSignAmount = tmp.getSignAmount();
                for (TCarrierOrderOrderRel tmpOrderRel : oldCarrierOrderRels) {
                    upOrderRel = new TCarrierOrderOrderRel();
                    upOrderRel.setId(tmpOrderRel.getId());
                    //签收件数
                    if (tmpOrderRel.getExpectAmount().compareTo(restSignAmount) <= 0) {
                        upOrderRel.setSignAmount(tmpOrderRel.getExpectAmount());
                        restSignAmount = restSignAmount.subtract(upOrderRel.getSignAmount());
                    } else {
                        upOrderRel.setSignAmount(restSignAmount);
                        restSignAmount = BigDecimal.ZERO;
                    }
                    commonBiz.setBaseEntityModify(upOrderRel, BaseContextHandler.getUserName());
                    upCarrierOrderRels.add(upOrderRel);

                    orderSignAmountModel = new OrderAmountModel();
                    orderSignAmountModel.setAmount(upOrderRel.getSignAmount());
                    orderSignAmountModel.setOrderId(tmpOrderRel.getOrderId());
                    orderSignAmountModel.setOrderCode(tmpOrderRel.getOrderCode());
                    upOrderSignAmountModels.add(orderSignAmountModel);
                }
            }

            syncModel = new CarrierOrderSynchronizeModel();
            syncModel.setType(carrierOrderEntrustTypeMap.get(tmp.getId()));
            syncModel.setCarrierOrderCode(tmp.getCarrierOrderCode());
            syncModel.setStatus(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey());
            syncModel.setReceiptTime(tmp.getSignTime());
            syncModel.setUserName(BaseContextHandler.getUserName());
            syncModel.setOrderSignAmount(upOrderSignAmountModels);
            synchronizeModels.add(syncModel);

            //云盘回收类型
            if (synCarrierOrderIdRecycleList.contains(tmp.getId())) {
                synchronizeToLeYiModels.add(syncModel);
            }
        }
        if (ListUtils.isNotEmpty(upCarrierOrderRels)) {
            tCarrierOrderOrderRelMapper.batchUpdateSelective(upCarrierOrderRels);
        }

        //同步云仓
        if (ListUtils.isNotEmpty(synchronizeModels)) {
            log.info("运单签收同步消息到云仓" + synchronizeModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToWarehouse(carrierOrderSynchronizeLeyiModel);
        }

        //同步云盘
        if (ListUtils.isNotEmpty(synchronizeToLeYiModels)) {
            log.info("运单签收同步消息到云盘" + synchronizeToLeYiModels);
            CarrierOrderSynchronizeLeyiModel carrierOrderSynchronizeLeyiModel = new CarrierOrderSynchronizeLeyiModel();
            carrierOrderSynchronizeLeyiModel.setCarrierOrderSynchronizeModels(synchronizeToLeYiModels);
            rabbitMqPublishBiz.carrierOrderSynchronizeToLeyi(carrierOrderSynchronizeLeyiModel);
        }
    }

    /**
     * 根据车主id查询有效的云盘运单（排除已取消、已放空）
     * @param requestModel
     * @return
     */
    public PageInfo<GetValidCarrierOrderResponseModel> getValidCarrierOrder(GetValidCarrierOrderRequestModel requestModel) {
        //前台
        if (CommonConstant.TWO.equals(requestModel.getRequestSource())) {
            //查询登录人所属车主id
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || companyCarrierId.equals(CommonConstant.LONG_ZERO)) {
                return new PageInfo<>(new ArrayList<>());
            }
            requestModel.setCompanyCarrierId(companyCarrierId);
        }else{//后台
            //我司
            if (requestModel.getCompanyCarrierId() == null){
                Long companyCarrierId = commonBiz.getQiyaCompanyCarrierId();
                if (companyCarrierId == null || companyCarrierId.equals(CommonConstant.LONG_ZERO)) {
                    return new PageInfo<>(new ArrayList<>());
                }
                requestModel.setCompanyCarrierId(companyCarrierId);
            }
        }

        //分页查询
        requestModel.enablePaging();
        return new PageInfo<>(tCarrierOrderMapper.getValidCarrierOrder(requestModel));
    }

    /**
     * 搜索回单审核运单 ID 列表
     * @param queryBoModel 请求 Bo Model
     * @return ID 列表
     */
    public List<Long> getReceiptAuditCarrierOrderIds(SearchCarrierOrderIdsForLeYiReceiptAuditBoModel queryBoModel) {

        List<Long> carrierOrderIdList;

        // 根据票据筛选运单Id
        if (Objects.nonNull(queryBoModel.getTicketUploadStartDate()) &&
                Objects.nonNull(queryBoModel.getTicketUploadEndDate())) {
            carrierOrderIdList = tCarrierOrderTicketsMapper.selectCarrierOrderIdByTicketUploadTimeAndType(
                    queryBoModel.getTicketUploadStartDate(),
                    queryBoModel.getTicketUploadEndDate(),
                    CarrierOrderTicketsTypeEnum.CARRIER_ORDER_SIGN_TICKETS.getKey());
            if (ListUtils.isEmpty(carrierOrderIdList)) {
                return carrierOrderIdList;
            }
            queryBoModel.setCarrierOrderIdList(carrierOrderIdList);
        }

        // 筛选运单Id
        carrierOrderIdList = tCarrierOrderMapper.searchCarrierOrderIdsForLeYiReceiptAudit(queryBoModel);
        return carrierOrderIdList;
    }

    /**
     * 根据ID查询运单详情
     *
     * @param carrierOrderIds 运单Id列表
     * @return 运单详情列表
     */
    public List<SearchCarrierOrderListForLeYiResponseModel> getReceiptAuditCarrierOrderInfoList(List<Long> carrierOrderIds) {
        return tCarrierOrderMapper.searchCarrierOrderForLeYiReceiptAudit(carrierOrderIds);
    }

    /**
     * 云仓,云盘取消  发货-回收出库 的运单
     *
     * @param requestModel 运单号
     */
    @Transactional
    public void externalCancelCarrierOrder(ExternalCancelCarrierOrderRequestModel requestModel) {
        if (StringUtils.isNotBlank(requestModel.getOperatorName())) {
            BaseContextHandler.setUserName(requestModel.getOperatorName());
        }

        //判断运单是否可以取消
        CanCancelCarrierOrderModel canCancelCarrierOrderModel = checkCanCancel(requestModel);

        String carrierOrderIds = ConverterUtils.toString(canCancelCarrierOrderModel.getCarrierOrderId());

        //查询运单及货物信息
        List<CarrierOrderListBeforeSignUpResponseModel> carrierOrderList = tCarrierOrderMapper.selectCarrierOrderSignDetail(carrierOrderIds, null);
        if (ListUtils.isEmpty(carrierOrderList)) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        String cancelReason = requestModel.getCancelReason();
        Date date = new Date();
        List<TCarrierOrder> upOrderList = new ArrayList<>();
        List<TCarrierOrderOperateLogs> operateLogsList = new ArrayList<>();
        List<TCarrierOrderEvents> eventsList = new ArrayList<>();
        List<CarrierOrderSynchronizeModel> synchronizeModels = new ArrayList<>();

        //需求单数量map
        Map<Long, BigDecimal> arrangedAmountMap = new HashMap<>();//需求单id-》退回到需求单已安排的数量
        Map<Long, BigDecimal> notArrangedAmountMap = new HashMap<>();//需求单id-》退回到需求单未安排的数量
        //需求单货物数量map
        Map<Long, BigDecimal> goodsArrangedAmountMap = new HashMap<>();//需求单货物id-》退回到需求单货物已安排的数量
        Map<Long, BigDecimal> goodsNotArrangedAmountMap = new HashMap<>();//需求单货物id-》退回到需求单货物未安排的数量

        //运单id-》运单信息
        Map<Long, CarrierOrderListBeforeSignUpResponseModel> orderMap = new HashMap<>();

        ExternalCancelCarrierOrderImpactDemandOrderModel orderResponseModels = new ExternalCancelCarrierOrderImpactDemandOrderModel();
        if (ListUtils.isNotEmpty(carrierOrderList)) {
            for (CarrierOrderListBeforeSignUpResponseModel carrierOrderInfo : carrierOrderList) {
                //退回需求单的已安排数
                arrangedAmountMap.put(carrierOrderInfo.getDemandOrderId(), carrierOrderInfo.getExpectAmount());

                //发货类型，退回需求单的未安排数
                //	实提数>=预提数：未安排数=预提数；
                //	实提<预提数：未安排数=实提数；
                //回收出库类型，退回需求单的未安排数=预计数量
                BigDecimal notArrangedAmount = notArrangedAmountMap.get(carrierOrderInfo.getDemandOrderId());
                //运单取消要回退到需求单的数量
                BigDecimal notArrangedAmountBack = carrierOrderInfo.getExpectAmount();
                //已提货且是发货类型
                if (carrierOrderInfo.getStatus() > CarrierOrderStatusEnum.WAIT_LOAD.getKey() && EntrustTypeEnum.DELIVER.getKey().equals(carrierOrderInfo.getEntrustType())) {
                    //少提，则取提货数量
                    if (carrierOrderInfo.getExpectAmount().compareTo(carrierOrderInfo.getLoadAmount()) > CommonConstant.INTEGER_ZERO) {
                        notArrangedAmountBack = carrierOrderInfo.getLoadAmount();
                    }
                }
                if (notArrangedAmount != null) {
                    notArrangedAmountMap.put(carrierOrderInfo.getDemandOrderId(), notArrangedAmount.add(notArrangedAmountBack));
                } else {
                    notArrangedAmountMap.put(carrierOrderInfo.getDemandOrderId(), notArrangedAmountBack);
                }

                //更新运单
                TCarrierOrder upOrder = new TCarrierOrder();
                upOrder.setId(carrierOrderInfo.getCarrierOrderId());
                upOrder.setCancelReason(cancelReason);
                upOrder.setCancelOperatorName(BaseContextHandler.getUserName());
                upOrder.setCancelTime(date);
                upOrder.setIfCancel(CommonConstant.INTEGER_ONE);
                commonBiz.setBaseEntityModify(upOrder, BaseContextHandler.getUserName());
                upOrderList.add(upOrder);

                //日志
                TCarrierOrderOperateLogs carrierOrderOperateLogs = new TCarrierOrderOperateLogs();
                carrierOrderOperateLogs.setCarrierOrderId(carrierOrderInfo.getCarrierOrderId());
                carrierOrderOperateLogs.setOperationType(CarrierOrderOperateLogsTypeEnum.CANCEL_CARRIER_ORDER.getKey());
                carrierOrderOperateLogs.setOperationContent(CarrierOrderOperateLogsTypeEnum.CANCEL_CARRIER_ORDER.getValue());
                carrierOrderOperateLogs.setRemark(cancelReason);
                carrierOrderOperateLogs.setOperatorName(BaseContextHandler.getUserName());
                carrierOrderOperateLogs.setOperateTime(date);
                commonBiz.setBaseEntityAdd(carrierOrderOperateLogs, BaseContextHandler.getUserName());
                operateLogsList.add(carrierOrderOperateLogs);

                //事件
                TCarrierOrderEvents newEvent = new TCarrierOrderEvents();
                newEvent.setEvent(CarrierOrderEventsTypeEnum.HAS_CANCELED.getKey());
                newEvent.setEventDesc(CarrierOrderEventsTypeEnum.HAS_CANCELED.getValue());
                newEvent.setRemark(CarrierOrderEventsTypeEnum.HAS_CANCELED.getFormat());
                newEvent.setEventTime(date);
                newEvent.setCarrierOrderId(carrierOrderInfo.getCarrierOrderId());
                newEvent.setOperateTime(date);
                newEvent.setOperatorName(BaseContextHandler.getUserName());
                commonBiz.setBaseEntityAdd(newEvent, BaseContextHandler.getUserName());
                eventsList.add(newEvent);

                //托盘的单子
                CarrierOrderSynchronizeModel syncModel = new CarrierOrderSynchronizeModel();
                syncModel.setType(carrierOrderInfo.getEntrustType());
                syncModel.setCarrierOrderCode(carrierOrderInfo.getCarrierOrderCode());
                syncModel.setDemandOrderId(carrierOrderInfo.getDemandOrderId());//借用的临时字段
                syncModel.setIfInvalid(CommonConstant.INTEGER_ONE);
                syncModel.setUserName(BaseContextHandler.getUserName());
                syncModel.setCancelReason(cancelReason);
                synchronizeModels.add(syncModel);

                List<CarrierOrderListBeforeSignUpGoodsModel> goodsInfo = carrierOrderInfo.getGoodsInfo();
                for (CarrierOrderListBeforeSignUpGoodsModel good : goodsInfo) {
                    //退回需求单货物的已安排数
                    goodsArrangedAmountMap.put(good.getDemandOrderGoodsId(), good.getExpectAmount());

                    //退回需求单货物的未安排数
                    //	实提数>=预提数：未安排数=预提数；
                    //	实提<预提数：未安排数=实提数；
                    BigDecimal goodsNotArrangedAmount = goodsNotArrangedAmountMap.get(good.getDemandOrderGoodsId());
                    //运单取消要回退到需求单货物的数量
                    BigDecimal goodsNotArrangedAmountBack = good.getExpectAmount();
                    //已提货且是发货类型
                    if (carrierOrderInfo.getStatus() > CarrierOrderStatusEnum.WAIT_LOAD.getKey() && EntrustTypeEnum.DELIVER.getKey().equals(carrierOrderInfo.getEntrustType())) {
                        //少提，则取提货数量
                        if (good.getExpectAmount().compareTo(good.getLoadAmount()) > CommonConstant.INTEGER_ZERO) {
                            goodsNotArrangedAmountBack = good.getLoadAmount();
                        }
                    }
                    if (goodsNotArrangedAmount != null) {
                        goodsNotArrangedAmountMap.put(good.getDemandOrderGoodsId(), goodsNotArrangedAmount.add(goodsNotArrangedAmountBack));
                    } else {
                        goodsNotArrangedAmountMap.put(good.getDemandOrderGoodsId(), goodsNotArrangedAmountBack);
                    }
                }
                orderMap.put(carrierOrderInfo.getCarrierOrderId(), carrierOrderInfo);
            }
        }

        //查询s单数据信息
        List<TCarrierOrderOrderRel> carrierOrderOrderRels = tCarrierOrderOrderRelMapper.selectCarrierOrderRelsByCarrierOrderIds(carrierOrderIds);
        if (ListUtils.isNotEmpty(carrierOrderOrderRels)) {
            List<CancelCarrierOrderOrderRelModel> relModels = new ArrayList<>();
            for (TCarrierOrderOrderRel rel : carrierOrderOrderRels) {
                CarrierOrderListBeforeSignUpResponseModel code = orderMap.get(rel.getCarrierOrderId());
                if (code != null) {
                    CancelCarrierOrderOrderRelModel relModel = new CancelCarrierOrderOrderRelModel();
                    relModel.setDemandOrderOrderId(rel.getDemandOrderOrderId());
                    relModel.setExpectAmount(rel.getExpectAmount());
                    if (code.getStatus() > CarrierOrderStatusEnum.WAIT_LOAD.getKey() && EntrustTypeEnum.DELIVER.getKey().equals(code.getEntrustType())){
                        if (code.getExpectAmount().compareTo(code.getLoadAmount()) > CommonConstant.INTEGER_ZERO) {
                            relModel.setExpectAmount(rel.getLoadAmount());
                        }
                    }
                    relModel.setOrderId(rel.getOrderId());
                    relModel.setOrderCode(rel.getOrderCode());
                    relModel.setDemandOrdeId(code.getDemandOrderId());
                    relModels.add(relModel);
                }
            }
            orderResponseModels.setCarrierOrderOrderRelModels(relModels);
        }

        orderResponseModels.setArrangedAmountMap(arrangedAmountMap);
        orderResponseModels.setNotArrangedAmountMap(notArrangedAmountMap);
        orderResponseModels.setGoodsArrangedAmountMap(goodsArrangedAmountMap);
        orderResponseModels.setGoodsNotArrangedAmountMap(goodsNotArrangedAmountMap);

        if (ListUtils.isNotEmpty(synchronizeModels)) {
            orderResponseModels.setSynchronizeModels(synchronizeModels);
        }

        //更新数据库
        if (ListUtils.isNotEmpty(upOrderList)) {
            tCarrierOrderMapper.batchUpdateCarrierOrders(upOrderList);
        }
        if (ListUtils.isNotEmpty(eventsList)) {
            tCarrierOrderEventsMapper.batchInsertSelective(eventsList);
        }
        if (ListUtils.isNotEmpty(operateLogsList)) {
            tCarrierOrderOperateLogsMapper.batchInsertSelective(operateLogsList);
        }

        //过滤零担运单 校验生成零担费用
        List<Long> lessThanTruckLoadCarrierOrderIds = carrierOrderList.stream().filter(carrierModel -> BargainingModeEnum.LESS_THAN_TRUCKLOAD_PRICING.getKey().equals(carrierModel.getBargainingMode()))
                .map(CarrierOrderListBeforeSignUpResponseModel::getCarrierOrderId).collect(Collectors.toList());
        shippingOrderCommonBiz.checkAndGenerateShippingFeeFromCarrierOrderAction(lessThanTruckLoadCarrierOrderIds);


        demandOrderBiz.externalCancelCarrierOrderUpdateDemandOrder(orderResponseModels);

        if (canCancelCarrierOrderModel.getExtVehicleSettlementId() != null) {
            //删除外部车辆结算数据
            TExtVehicleSettlement tExtVehicleSettlement = new TExtVehicleSettlement();
            tExtVehicleSettlement.setId(canCancelCarrierOrderModel.getExtVehicleSettlementId());
            tExtVehicleSettlement.setValid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(tExtVehicleSettlement, BaseContextHandler.getUserName());
            tExtVehicleSettlementMapper.batchUpdateSelective(Collections.singletonList(tExtVehicleSettlement));
        }



    }

    /**
     * 校验运单是否可以取消
     *
     * @param requestModel 运单号
     * @return model
     */
    private CanCancelCarrierOrderModel checkCanCancel(ExternalCancelCarrierOrderRequestModel requestModel) {
        //根据运单号查询运单
        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.getByCode(requestModel.getCarrierOrderCode());
        if (tCarrierOrder == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //非云盘单子不能操作
        if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(tCarrierOrder.getDemandOrderSource())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        //运单取消不能操作
        if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfCancel())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_CANCEL);
        }

        //运单放空不能操作
        if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_IS_EMPTY);
        }

        //发货-回收出库的才能操作
        if (!EntrustTypeEnum.DELIVER.getKey().equals(tCarrierOrder.getDemandOrderEntrustType()) &&
                !EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())) {
            throw new BizException(CarrierDataExceptionEnum.OUTER_CANCEL_ORDER_ENTRUST_TYPE_ERROR);
        }

        //未签收才能取消
        if (CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey().equals(tCarrierOrder.getStatus())) {
            throw new BizException(CarrierDataExceptionEnum.OUTER_CANCEL_ORDER_ALREADY_SIGN_UP);
        }

        //运单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkCarrierExceptionExist(ConverterUtils.toString(tCarrierOrder.getId()));
        if (!workOrderMap.isEmpty()) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_EXCEPTION);
        }

        //外部车辆对账信息id
        Long extVehicleSettlementId = null;

        //判断取消的前提条件
        //	运单属于我司-自主自营车辆：未关联司机对账单；
        //	运单属于我司-外部车辆：外部车辆结算管理中，运单未支付状态；
        //	运单属于其他车主：未关联车主对账单；
        boolean canCancelFlag = true;
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(tCarrierOrder.getCompanyCarrierId());
        if (tCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(tCompanyCarrier.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
        }
        if (IsOurCompanyEnum.OUR_COMPANY.getKey().equals(tCompanyCarrier.getLevel())) {
            //我司
            //是否关联司机对账
            boolean relDriverSettlement = carrierOrderCommonBiz.existSettlementData(ConverterUtils.toString(tCarrierOrder.getId()));
            if (relDriverSettlement) {
                canCancelFlag = false;
            }
        }

        //外部车辆结算信息是否支付
        TExtVehicleSettlement dbExtVehicleSettlement = tExtVehicleSettlementMapper.getByCarrierOrderId(tCarrierOrder.getId());
        if (dbExtVehicleSettlement != null) {
            if (VehicleSettlementStatusEnum.PAYMENT.getKey().equals(dbExtVehicleSettlement.getStatus())) {
                //有外部车辆结算信息并且已经支付
                canCancelFlag = false;
            } else {
                //有外部车辆结算信息但是未支付
                extVehicleSettlementId = dbExtVehicleSettlement.getId();
            }
        }

        //如果关联对账单不可取消
        if (tCarrierOrder.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
            canCancelFlag = false;
        }

        if (!canCancelFlag) {
            throw new BizException(CarrierDataExceptionEnum.OUTER_CANCEL_ORDER_NO_CANCEL);
        }
        return new CanCancelCarrierOrderModel()
                .setCarrierOrderId(tCarrierOrder.getId())
                .setExtVehicleSettlementId(extVehicleSettlementId);
    }

    /**
     * 判断当前运单是否可以取消
     *
     * @param requestModel 运单号
     */
    public void externalCanCancelCarrierOrder(ExternalCancelCarrierOrderRequestModel requestModel) {
        checkCanCancel(requestModel);
    }

    /**
     * 根据运单号查询运单上车辆司机信息（云盘调用）
     * @param requestModel
     * @return
     */
    public List<GetDriverVehicleInfoByCodeResponseModel> getDriverVehicleInfoByCode(GetDriverVehicleInfoByCodeRequestModel requestModel){
        if (ListUtils.isEmpty(requestModel.getCarrierOrderCodeList())){
            return new ArrayList<>();
        }
        return tCarrierOrderVehicleHistoryMapper.getDriverVehicleInfoByCode(requestModel);
    }

    /**
     * 查询云盘二维码
     * @param requestModel
     * @return
     */
    public GetLeYiQrCodeResponseModel getLeYiQrCode(GetLeYiQrCodeRequestModel requestModel){
        //查询运单信息
        TCarrierOrder dbCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getCarrierOrderId());
        if (dbCarrierOrder == null) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }

        GetCarrierOrderQRCodeResponseModel getCarrierOrderQRCodeResponseModel = trayOrderServiceClient.getCarrierOrderQRCode(dbCarrierOrder.getCarrierOrderCode());
        GetLeYiQrCodeResponseModel responseModel = new GetLeYiQrCodeResponseModel();
        if (getCarrierOrderQRCodeResponseModel != null){
            responseModel.setLeYiQrCode(getCarrierOrderQRCodeResponseModel.getQRCodeUrl());
        }
        return responseModel;
    }

    /**
     * 校验接口是否能满足多提接口
     * @param reqModel
     */
    public boolean verifyEnablePickUpMore(VerifyEnablePickUpMoreReqModel reqModel){
        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(reqModel.getCarrierOrderId());
        if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        // 不是回收入
        if (!EntrustTypeEnum.RECYCLE_IN.getKey().equals(tCarrierOrder.getDemandOrderEntrustType())){
            throw new BizException(CarrierDataExceptionEnum.CREATE_EXT_CARRIER_ORDER_TYPE_ERROR);
        }
        //运单状态：≠已取消/已放空 和待提货；
        Integer ifCancel = tCarrierOrder.getIfCancel();
        Integer ifEmpty = tCarrierOrder.getIfEmpty();
        Integer status = tCarrierOrder.getStatus();
        if (CommonConstant.INTEGER_ONE.equals(ifCancel) || CommonConstant.INTEGER_ONE.equals(ifEmpty)){
            throw new BizException(CarrierDataExceptionEnum.CREATE_EXT_CARRIER_ORDER_STATE_ERROR);
        }
        // 运单必须是待提货
        if (CarrierOrderStatusEnum.WAIT_LOAD.getKey().equals(status)){
            throw new BizException(CarrierDataExceptionEnum.CREATE_EXT_CARRIER_ORDER_STATE_ERROR);
        }

        // 不包含石化板块
        if (!tCarrierOrder.getProjectLabel().contains("1")){
            throw new BizException(CarrierDataExceptionEnum.CREATE_EXT_CARRIER_ORDER_PROJECT_ERROR);
        }
        // 非补单
        if (!tCarrierOrder.getIfExtCarrierOrder().equals(CommonConstant.INTEGER_ZERO)){
            throw new BizException(CarrierDataExceptionEnum.CREATE_EXT_CARRIER_ORDER_IS_EXIST);
        }

        // 运单下面没有补单
        TExtDemandOrderRelation tExtDemandOrderRelation = tExtDemandOrderRelationMapper.selectByCarrierOrderCode(tCarrierOrder.getCarrierOrderCode());
        if (tExtDemandOrderRelation!= null){
            throw new BizException(CarrierDataExceptionEnum.CREATE_EXT_CARRIER_ORDER_IS_EXIST);
        }

        //查询是否货物是共享托盘
        List<TCarrierOrderGoods> tCarrierOrderGoods = tCarrierOrderGoodsMapper.selectGoodsByCarrierOrderId(tCarrierOrder.getId());
        if (ListUtils.isEmpty(tCarrierOrderGoods)){
            throw new BizException(CarrierDataExceptionEnum.CREATE_EXT_CARRIER_ORDER_GOODS_ERROR);
        }
        if (ListUtils.isNotEmpty(tCarrierOrderGoods)){
            boolean ifAllTray = true;
            for (TCarrierOrderGoods e : tCarrierOrderGoods) {
                if (e.getCategoryName().equalsIgnoreCase(CommonConstant.TRAY_NAME)
                        || e.getCategoryName().equalsIgnoreCase(CommonConstant.TRAY_NAME1)) {
                } else {
                    ifAllTray = false;
                    break;
                }
            }
            if (!ifAllTray){
                throw new BizException(CarrierDataExceptionEnum.CREATE_EXT_CARRIER_ORDER_GOODS_ERROR);
            }
        }

        // 查询云盘客户是否是C1，并且是否签约了2.3.0以上
        TDemandOrder tDemandOrder = tDemandOrderMapper.selectByPrimaryKey(tCarrierOrder.getDemandOrderId());
        if (tDemandOrder == null || IfValidEnum.INVALID.getKey().equals(tDemandOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_NOT_EXIST);
        }
        BatchQueryCustomerInfoRequest batchQueryCustomerInfoRequest = new BatchQueryCustomerInfoRequest();
        batchQueryCustomerInfoRequest.setCompanyNames(Lists.newArrayList(tDemandOrder.getUpstreamCustomer()));

        Result<List<BatchQueryCustomerInfoResponse>> batchQueryCustomerInfo = leyiCommonAddressServiceApi.batchQueryCustomerInfo(batchQueryCustomerInfoRequest);
        batchQueryCustomerInfo.throwException();
        BatchQueryCustomerInfoResponse batchQueryCustomerInfoResponse = batchQueryCustomerInfo.getData().get(0);
        if (batchQueryCustomerInfoResponse== null
                || StringUtils.isEmpty(batchQueryCustomerInfoResponse.getChineseName())
                || StringUtils.isEmpty(batchQueryCustomerInfoResponse.getCustomerCompanyType())
                || StringUtils.isEmpty(batchQueryCustomerInfoResponse.getMemberVersion())
                || batchQueryCustomerInfoResponse.getId() == null){
            throw new BizException(CarrierDataExceptionEnum.EXT_CARRIER_ORDER_CONFIG_ERROR);
        }
        int type = Integer.parseInt(batchQueryCustomerInfoResponse.getCustomerCompanyType());
        if (type == CommonConstant.INTEGER_FIVE){
            throw new BizException(CarrierDataExceptionEnum.CREATE_EXT_CARRIER_ORDER_COMPANY_ERROR);
        }
        if (batchQueryCustomerInfoResponse.getMemberVersion().compareTo("2.3.0")<0){
            throw new BizException(CarrierDataExceptionEnum.CREATE_EXT_CARRIER_ORDER_COMPANY_VERSION_ERROR);
        }
        return true;
    }

}
