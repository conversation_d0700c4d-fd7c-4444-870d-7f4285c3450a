package com.logistics.management.webapi.client.carrierorderticketsaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class SearchReceiptAuditListResponseModel {


    @ApiModelProperty(value = "回单审核Id")
    private Long receiptAuditId;

    @ApiModelProperty(value = "运单Id")
    private Long carrierOrderId;

    @ApiModelProperty(value = "运单号")
    private String carrierOrderCode;

    @ApiModelProperty(value = "审核状态; 0 待审核，1 已审核，2 已驳回")
    private Integer auditStatus;

    @ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;

    @ApiModelProperty(value = "发货地")
    private String loadAddress;

    @ApiModelProperty(value = "发货仓库")
    private String loadWarehouse;

    @ApiModelProperty(value = "收货地")
    private String unloadAddress;

    @ApiModelProperty(value = "收货仓库")
    private String unloadWarehouse;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    private String driverMobile;

    @ApiModelProperty(value = "车辆")
    private String vehicleNo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "卸货时间")
    private Date unloadTime;

    @ApiModelProperty(value = "单据上传时间")
    private Date ticketUploadTime;

    @ApiModelProperty(value = "单据审核时间")
    private Date ticketAuditTime;

    @ApiModelProperty("操作人")
    private String lastModifiedBy;

    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;
}
