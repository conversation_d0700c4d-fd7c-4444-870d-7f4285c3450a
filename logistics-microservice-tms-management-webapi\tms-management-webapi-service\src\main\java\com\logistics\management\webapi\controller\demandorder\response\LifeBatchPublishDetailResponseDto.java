package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/9/24 17:47
 */
@Data
public class LifeBatchPublishDetailResponseDto {
    /**
     * 需求单id
     */
    @ApiModelProperty("需求单id")
    private String demandId="";
    /**
     * 需求单号
     */
    @ApiModelProperty("需求单号")
    private String demandOrderCode="";

    /**
     * 发货地址
     */
    @ApiModelProperty("发货地址")
    private String loadDetailAddress="";

    /**
     * 收货地址
     */
    @ApiModelProperty("收货地址")
    private String unloadDetailAddress="";

    /**
     * 委托数量
     */
    @ApiModelProperty("委托数量")
    private String goodsAmount="";
    /**
     * 货物单位：1 件，2 吨，3 方，4 块
     */
    @ApiModelProperty("货物单位：1 件，2 吨，3 方，4 块")
    private String goodsUnit="";

    /**
     * 货主公司名称
     */
    @ApiModelProperty("货主公司名称")
    private String companyEntrustName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark="";
}
