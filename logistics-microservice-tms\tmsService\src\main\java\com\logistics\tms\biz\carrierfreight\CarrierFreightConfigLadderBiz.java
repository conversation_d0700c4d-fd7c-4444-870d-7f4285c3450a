package com.logistics.tms.biz.carrierfreight;

import com.google.common.collect.Maps;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierFreightConfigLadderModeEnum;
import com.logistics.tms.base.enums.CarrierFreightConfigPriceDesignModeEnum;
import com.logistics.tms.base.enums.IfValidEnum;
import com.logistics.tms.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestModel;
import com.logistics.tms.controller.freightconfig.response.ladder.CarrierFreightConfigLadderResponseModel;
import com.logistics.tms.controller.freightconfig.response.ladder.CarrierFreightConfigPriceDesignResponseModel;
import com.logistics.tms.entity.TCarrierFreightConfigLadder;
import com.logistics.tms.mapper.TCarrierFreightConfigLadderMapper;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CarrierFreightConfigLadderBiz {

    @Resource
    private TCarrierFreightConfigLadderMapper carrierFreightConfigLadderMapper;

    /**
     * 根据方案查询阶梯信息
     *
     * @param modeIds    模式Id; 路线配置Id; 方案配置id;
     * @param ladderMode {@link CarrierFreightConfigLadderModeEnum#getKey()}
     * @return 阶梯信息Map<模式Id, 价格设计模块>
     */
    public Map<Long, CarrierFreightConfigPriceDesignResponseModel> getLadderDetail(Integer ladderMode, List<Long> modeIds) {

        Map<Long, CarrierFreightConfigPriceDesignResponseModel> responseModelMap = Maps.newHashMap();
        if (ListUtils.isEmpty(modeIds)) {
            return responseModelMap;
        }

        Map<Long, List<TCarrierFreightConfigLadder>> schemeLadderConfigMap =
                carrierFreightConfigLadderMapper.selectConfigLadderByLadderModeAndModeIds(ladderMode, modeIds)
                        .stream()
                        .collect(Collectors.groupingBy(TCarrierFreightConfigLadder::getModeId));
        if (CollectionUtils.isEmpty(schemeLadderConfigMap)) {
            return Collections.emptyMap();
        }

        schemeLadderConfigMap.forEach((schemeId, configLadders) -> {
            // 构建响应
            CarrierFreightConfigPriceDesignResponseModel responseModel = new CarrierFreightConfigPriceDesignResponseModel();
            if (ListUtils.isNotEmpty(configLadders)) {
                configLadders
                        .stream()
                        .max(Comparator.comparing(TCarrierFreightConfigLadder::getLadderLevel))
                        .ifPresent(ladder -> {
                            CarrierFreightConfigPriceDesignModeEnum priceDesignModeEnum =
                                    CarrierFreightConfigPriceDesignModeEnum.getEnumByLadderLevel(ladder.getLadderLevel(), ladder.getPriceMode());
                            responseModel.setPriceMode(priceDesignModeEnum.getKey());
                        });
                responseModel.setLadderConfigList(this.laddersConversion(configLadders));
            }
            responseModelMap.put(schemeId, responseModel);
        });
        return responseModelMap;
    }

    /**
     * 添加阶梯配置
     *
     * @param ladderMode 阶梯模式
     * @param modeId     模式Id
     * @param ladderPid  多层级父Id
     * @param ladderList 添加阶梯集合
     */
    @Transactional
    public void add(Integer ladderMode, Long modeId, Long ladderPid, List<CarrierFreightConfigLadderRequestModel> ladderList) {

        if (ListUtils.isEmpty(ladderList)) {
            return;
        }

        for (CarrierFreightConfigLadderRequestModel ladder : ladderList) {
            TCarrierFreightConfigLadder entity = new TCarrierFreightConfigLadder();
            entity.setLadderMode(ladderMode);
            entity.setModeId(modeId);
            entity.setPriceMode(ladder.getPriceMode());
            entity.setLadderPid(ladderPid);
            entity.setLadderLevel(ladder.getLadderLevel());
            entity.setLadderType(ladder.getLadderType());
            entity.setLadderFrom(ladder.getLadderFrom());
            entity.setLadderTo(ladder.getLadderTo());
            entity.setLadderUnit(ladder.getLadderUnit());
            entity.setUnitPrice(ladder.getUnitPrice());
            carrierFreightConfigLadderMapper.insertGeneratedKey(entity);
            this.add(ladderMode, modeId, entity.getId(), ladder.getLadderConfigList());
        }
    }

    /**
     * 方案类型编辑阶梯
     *
     * @param schemeId 方案Id
     * @param ladderList 阶梯集合
     */
    @Transactional
    public void schemeLadderEdit(Long schemeId, List<CarrierFreightConfigLadderRequestModel> ladderList) {
         this.edit(CarrierFreightConfigLadderModeEnum.SCHEME_CONFIG.getKey(),
                schemeId,
                CommonConstant.LONG_ZERO,
                ladderList);
    }

    /**
     * 编辑阶梯
     *
     * @param ladderList 阶梯集合
     */
    @Transactional
    public void edit(Integer ladderMode, Long modeId, Long ladderPid, List<CarrierFreightConfigLadderRequestModel> ladderList) {
        if (ListUtils.isNotEmpty(ladderList)) {
            List<TCarrierFreightConfigLadder> editEntities = Lists.newArrayList();
            List<CarrierFreightConfigLadderRequestModel> addEntities = Lists.newArrayList();
            ladderList.forEach(ladder -> {
                if (Objects.nonNull(ladder.getFreightConfigLadderId())) {
                    if (ListUtils.isNotEmpty(ladder.getLadderConfigList())) {
                        this.edit(ladderMode, modeId, ladder.getFreightConfigLadderId(), ladder.getLadderConfigList());
                    }
                    TCarrierFreightConfigLadder entity = MapperUtils.mapper(ladder, TCarrierFreightConfigLadder.class);
                    entity.setId(ladder.getFreightConfigLadderId());
                    editEntities.add(entity);
                } else {
                    addEntities.add(ladder);
                }
            });
            if (ListUtils.isNotEmpty(editEntities)) {
                carrierFreightConfigLadderMapper.batchUpdateSelective(editEntities);
            }
            if (ListUtils.isNotEmpty(addEntities)) {
                this.add(ladderMode, modeId, ladderPid, addEntities);
            }
        }
    }

    /**
     * 删除阶梯
     *
     * @param ladderIdList 阶梯Id集合
     */
    @Transactional
    public void delete(List<Long> ladderIdList) {
        if (ListUtils.isNotEmpty(ladderIdList)) {
            List<TCarrierFreightConfigLadder> editEntities = ladderIdList
                    .stream()
                    .map(s -> {
                        TCarrierFreightConfigLadder entity = new TCarrierFreightConfigLadder();
                        entity.setId(s);
                        entity.setValid(IfValidEnum.INVALID.getKey());
                        return entity;
                    })
                    .collect(Collectors.toList());
            carrierFreightConfigLadderMapper.batchUpdateSelective(editEntities);
        }
    }

    // 阶梯转换
    private List<CarrierFreightConfigLadderResponseModel> laddersConversion(List<TCarrierFreightConfigLadder> configLadders) {
        return configLadders
                .stream()
                .filter(f -> f.getLadderLevel().equals(CommonConstant.INTEGER_ZERO) ||
                        f.getLadderLevel().equals(CommonConstant.INTEGER_ONE))
                .map(ladder -> {
                    CarrierFreightConfigLadderResponseModel model;
                    if (ladder.getLadderLevel().equals(CommonConstant.INTEGER_ONE)) {
                        model = this.laddersRecursion(ladder, configLadders);
                    } else {
                        model = this.laddersConversionFunction.apply(ladder);
                    }
                    return model;
                }).collect(Collectors.toList());
    }

    // 递归处理多级阶梯
    private CarrierFreightConfigLadderResponseModel laddersRecursion(TCarrierFreightConfigLadder ladderParent, List<TCarrierFreightConfigLadder> configLadders) {
        CarrierFreightConfigLadderResponseModel model = this.laddersConversionFunction.apply(ladderParent);
        for (TCarrierFreightConfigLadder ladder : configLadders) {
            if (model.getFreightConfigLadderId().equals(ladder.getLadderPid())) {
                model.getLadderConfigList().add(this.laddersRecursion(ladder, configLadders));
            }
        }
        return model;
    }

    private final Function<TCarrierFreightConfigLadder, CarrierFreightConfigLadderResponseModel> laddersConversionFunction = (ladder) -> {
        return MapperUtils.mapper(ladder, CarrierFreightConfigLadderResponseModel.class,
                new MapperMapping<TCarrierFreightConfigLadder, CarrierFreightConfigLadderResponseModel>() {
                    @Override
                    public void configure() {
                        TCarrierFreightConfigLadder source = getSource();
                        CarrierFreightConfigLadderResponseModel destination = getDestination();

                        destination.setFreightConfigLadderId(source.getId());
                    }
                });
    };


    /**
     * 根据方案id删除阶梯
     *
     * @param modeIds        方案id
     * @param lastModifiedBy 操作人
     */
    public void deleteLadder(List<Long> modeIds, String lastModifiedBy, Integer ladderMode) {
        carrierFreightConfigLadderMapper.deleteByModeIds(modeIds, lastModifiedBy, ladderMode);
    }

    /**
     * 根据ModeId查询阶梯
     *
     * @param ladderMode 阶梯模式
     * @param modeId 模式Id
     * @return 阶梯集合
     */
    public List<TCarrierFreightConfigLadder> getLadderByModeId(Integer ladderMode, Long modeId) {
        return carrierFreightConfigLadderMapper.selectConfigLadderByLadderModeAndModeIds(ladderMode,
                Collections.singletonList(modeId));
    }
}
