package com.logistics.webapi.controller.carrierorder.request;

import com.logistics.webapi.client.carrierorder.response.TicketsForWebModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AssociateExtDemandOrderReqDto {

    @ApiModelProperty("补充需求单的id")
    private String demandOrderId;

    @ApiModelProperty("图片路径")
    private List<LoadFiles> filesList;

}
