package com.logistics.appapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class SaveErrorLoadCodeFileRequestDto {

    @Size(max = 3, message = "最多上传3张图片")
    @ApiModelProperty(value = "附件路径")
    @NotEmpty(message = "附件不能为空")
    List<String> filePaths;

    @ApiModelProperty(value = "运单Id", required = true)
    @NotBlank(message = "运单id不能为空")
    private String carrierOrderId;
}
