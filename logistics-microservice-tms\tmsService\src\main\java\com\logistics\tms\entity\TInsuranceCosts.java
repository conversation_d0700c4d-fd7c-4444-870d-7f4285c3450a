package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TInsuranceCosts extends BaseEntity {
    /**
    * 结算状态：0 待结算，1 已结算
    */
    @ApiModelProperty("结算状态：0 待结算，1 已结算")
    private Integer status;

    /**
    * 车辆id
    */
    @ApiModelProperty("车辆id")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 月份（yyyy-MM）
    */
    @ApiModelProperty("月份（yyyy-MM）")
    private String settlementMonth;

    /**
    * 商业险剩余未扣除费用
    */
    @ApiModelProperty("商业险剩余未扣除费用")
    private BigDecimal commercialInsuranceCost;

    /**
    * 交强险剩余未扣除费用
    */
    @ApiModelProperty("交强险剩余未扣除费用")
    private BigDecimal compulsoryInsuranceCost;

    /**
    * 货物险剩余未扣除费用
    */
    @ApiModelProperty("货物险剩余未扣除费用")
    private BigDecimal cargoInsuranceCost;

    /**
    * 承运人险剩余未扣除费用
    */
    @ApiModelProperty("承运人险剩余未扣除费用")
    private BigDecimal carrierInsuranceCost;

    /**
    * 保险理赔
    */
    @ApiModelProperty("保险理赔")
    private BigDecimal insuranceClaimsCost;

    /**
    * 理赔时间
    */
    @ApiModelProperty("理赔时间")
    private Date insuranceClaimsTime;

    /**
    * 已支付商业险费用
    */
    @ApiModelProperty("已支付商业险费用")
    private BigDecimal payCommercialInsuranceCost;

    /**
    * 已支付交强险内容
    */
    @ApiModelProperty("已支付交强险内容")
    private BigDecimal payCompulsoryInsuranceCost;

    /**
    * 已支持货物险
    */
    @ApiModelProperty("已支持货物险")
    private BigDecimal payCargoInsuranceCost;

    /**
    * 已支付承运人险
    */
    @ApiModelProperty("已支付承运人险")
    private BigDecimal payCarrierInsuranceCost;
}