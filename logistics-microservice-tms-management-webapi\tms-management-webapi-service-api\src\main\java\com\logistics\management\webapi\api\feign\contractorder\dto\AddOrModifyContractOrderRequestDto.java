package com.logistics.management.webapi.api.feign.contractorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 新增/修改合同信息
 * @Author: sj
 * @Date: 2019/4/4 17:37
 */
@Data
public class AddOrModifyContractOrderRequestDto {
    @ApiModelProperty("合同表ID")
    private String contractId;
    @ApiModelProperty("外部合同编号")
    private String contractNoExternal;
    @ApiModelProperty("合同性质：1 货源合同，2 车源合同 3 租赁合同")
    @NotBlank(message = "请选择合同性质")
    private String contractNature;
    @ApiModelProperty("合同类型：1 框架合同，2 单次合同")
    @NotBlank(message = "请选择合同类型")
    private String contractType;
    @ApiModelProperty("客户ID(签订对象)")
    private String contractObjectId;
    @ApiModelProperty("客户公司名称，租赁合同使用")
    @NotBlank(message="请输入公司客户")
    private String customerCompanyName;
    @ApiModelProperty("公司抬头")
    @NotBlank(message = "请维护我司抬头")
    private String contractHeader;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("合同有效期起始时间")
    @NotBlank(message = "请维护合同有效期")
    private String contractStartTime;
    @ApiModelProperty("合同有效期结束时间")
    @NotBlank(message = "请维护合同有效期")
    private String contractEndTime;
    @ApiModelProperty("合同附件列表")
    @NotEmpty(message = "请上传合同附件")
    private List<String> contractFiles;
}
