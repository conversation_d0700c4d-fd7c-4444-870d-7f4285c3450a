package com.logistics.tms.controller.settlestatement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/9
 */
@Data
public class TraditionStatementArchiveListResponseModel {

	@ApiModelProperty("对账单详情item id")
	private Long settleStatementItemId;

	@ApiModelProperty("运单ID")
	private Long carrierOrderId;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("归档原因")
	private String archiveRemark;

	@ApiModelProperty("操作人(归档人)")
	private String lastModifiedBy;

	@ApiModelProperty("操作时间(归档时间)")
	private Date lastModifiedTime;
}
