/**
 * Created by yun.zhou on 2017/12/12.
 */
package com.logistics.tms.base.enums;

public enum OperateLogsObjectTypeEnum {

    DISPATCH_ORDER(1, "调度单"),
    CARRIER_DRIVER_FREIGHT(2, "司机运费加减价"),
    CARRIER_RULE(3, "承运商规则日志"),
    UPDATE_AUDIT_VEHICLE(4,"修改车辆审核"),
    DRIVER_PAYEE(5,"司机收款账户"),
    CONTRACT(6, "合同"),
    LOAN_RECORDS(7,"贷款记录"),
    COMPANY_CARRIER_VEHICLE(8, "二级承运商与车辆关系"),
    ENTRUST_FREIGHT(9, "运价规则"),
    ENTRUST_BASE_PRICE(10, "运价基价"),
    FREIGHT(11, "运价规则"),
    COMPANY_CARRIER_DRIVER(12, "二级承运商与司机关系"),
    VEHICLE_OIL_CARD(13, "车辆油卡管理"),
    RENEWABLE_AUDIT_ORDER(14, "新生审核订单操作日志"),
    TEMPORARY_COST(15, "临时费用"),
    CARRIER_FREIGHT(16, "车主运价规则"),
    RESERVE_APPLY_AUDIT(17, "备用金申请审核操作日志"),
    DRIVER_ACCOUNT_ADD_EDIT(18, "司机账户"),
    DRIVER_COST_APPLY(19, "司机费用申请"),
    BIDDING_ORDER(20, "竞价单"),
    ROUTE_ENQUIRY(21, "路线询价单"),
    SHIPPING_ORDER(22, "零担运输单"),
    ;

    private Integer key;
    private String value;

    OperateLogsObjectTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
