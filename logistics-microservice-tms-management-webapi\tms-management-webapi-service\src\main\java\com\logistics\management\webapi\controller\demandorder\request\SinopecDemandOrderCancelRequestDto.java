package com.logistics.management.webapi.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 中石化需求单取消请求实体类
 *
 * @author: wei.wang
 * @date: 2021/12/3
 */
@Data
public class SinopecDemandOrderCancelRequestDto {

	@ApiModelProperty("委托单id 批量字符串")
	@NotBlank(message = "请选择需求单")
	private String demandIds;

	@ApiModelProperty("调度人员姓名")
	@Pattern(regexp = "^[\\u4e00-\\u9fa5]{2,20}$", message = "调度人员姓名不许为空且必须是汉字并且长度控制在2~20字内")
	private String dispatcherName;
	@ApiModelProperty("调度人员电话")
	@Size(min = 1, max = 50, message = "调度人员电话不许为空且长度控制在1~50个字符内")
	private String dispatcherPhone;
	@ApiModelProperty("取消原因")
	@Size(min = 2, max = 50, message = "取消原因不许为空且长度控制在2~50个字符内")
	private String cancelReason;

	@ApiModelProperty("原因类型, 1-承运商原因、2-托运人原因、3-装货人原因、4-政府政策原因、5-不可抗力原因")
	@NotBlank(message = "原因类型, 1-承运商原因、2-托运人原因、3-装货人原因、4-政府政策原因、5-不可抗力原因")
	@Range(min = 1, max = 5, message = "原因类型, 1-承运商原因、2-托运人原因、3-装货人原因、4-政府政策原因、5-不可抗力原因")
	private String cancelReasonType;
}
