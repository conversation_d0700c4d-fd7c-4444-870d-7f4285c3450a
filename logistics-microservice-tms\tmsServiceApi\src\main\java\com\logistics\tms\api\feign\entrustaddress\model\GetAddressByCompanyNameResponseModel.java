package com.logistics.tms.api.feign.entrustaddress.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetAddressByCompanyNameResponseModel {
    private Long entrustAddressId;
    @ApiModelProperty("购货公司")
    private String companyName;
    private String warehouse;
    private Long provinceId;
    private String provinceName;
    private Long cityId;
    private String cityName;
    private Long areaId;
    private String areaName;
    private String detailAddress;
    private String contactName;
    private String contactMobile;
    private Integer addressType;
}
