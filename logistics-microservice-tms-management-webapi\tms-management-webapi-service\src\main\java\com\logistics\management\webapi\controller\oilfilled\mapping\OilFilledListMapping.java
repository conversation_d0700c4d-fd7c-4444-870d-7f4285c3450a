package com.logistics.management.webapi.controller.oilfilled.mapping;

import com.logistics.management.webapi.client.oilfilled.response.OilFilledListResponseModel;
import com.logistics.management.webapi.controller.oilfilled.response.OilFilledListResponseDto;
import com.logistics.management.webapi.base.enums.*;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/9
 * @description:
 */
public class OilFilledListMapping extends MapperMapping<OilFilledListResponseModel, OilFilledListResponseDto> {
    @Override
    public void configure() {
        OilFilledListResponseModel source = getSource();
        OilFilledListResponseDto destination = getDestination();
        if (source.getStatus() != null) {
            destination.setStatusLabel(OilFilledStatusEnum.getEnum(source.getStatus()).getValue());
        }
        if (source.getOilFilledType() != null) {
            destination.setOilFilledTypeLabel(OilFilledTypeEnum.getEnum(source.getOilFilledType()).getValue());
        }
        if (source.getOilFilledDate() != null) {
            destination.setOilFilledDate(DateUtils.dateToString(source.getOilFilledDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
        }
        if (OilFilledTypeEnum.OIL_FILLED_CARD.getKey().equals(source.getOilFilledType())) {
            destination.setLiter("");
        }
        if (OilFilledTypeEnum.OIL_FILLED_CAR.getKey().equals(source.getOilFilledType())) {
            destination.setTopUpIntegral("");
            destination.setRewardIntegral("");
        }
        if (source.getSource() != null) {
            destination.setSourceDesc(OilFilledSourceEnum.getEnum(source.getSource()).getValue());
        }
        if (OilFilledSourceEnum.REFUND.getKey().equals(source.getSource())) {
            if (source.getRefundReasonType() != null) {
                destination.setRefundReasonTypeDesc(OilRefundReasonTypeEnum.getEnum(source.getRefundReasonType()).getValue());
            }
        } else {
            destination.setRefundReasonType("");
        }
        //车辆机构展示文本
        destination.setVehiclePropertyLabel(VehiclePropertyEnum.getEnum(source.getVehicleProperty()).getValue());
    }
}
