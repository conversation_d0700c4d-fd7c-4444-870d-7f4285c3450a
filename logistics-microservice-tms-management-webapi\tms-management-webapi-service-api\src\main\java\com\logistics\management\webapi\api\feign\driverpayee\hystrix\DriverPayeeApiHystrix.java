package com.logistics.management.webapi.api.feign.driverpayee.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.driverpayee.DriverPayeeApi;
import com.logistics.management.webapi.api.feign.driverpayee.dto.*;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


@Component
public class DriverPayeeApiHystrix implements DriverPayeeApi {


    @Override
    public Result<PageInfo<DriverPayeeListResponseDto>> driverPayeeList(DriverPayeeListRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addOrModifyDriverPayee(AddOrModifyDriverPayeeRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<DriverPayeeDetailResponseDto> driverPayeeDetail(DriverPayeeDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> auditOrReject(AuditRejectDriverPayeeRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void driverPayeeExport(DriverPayeeListRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }

    @Override
    public Result<ImportDriverPayeeResponseDto> importDriverPayee(MultipartFile file, HttpServletRequest request) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> importDriverPayeeInfo(MultipartFile file) {
        return Result.timeout();
    }

    @Override
    public Result<List<ViewLogsResponseDto>> driverPayeeLogs(DriverPayeeDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchDriverPayeesResponseDto>> searchDriverPayees(SearchDriverPayeesRequestDto requestDto) {
        return Result.timeout();
    }
}


