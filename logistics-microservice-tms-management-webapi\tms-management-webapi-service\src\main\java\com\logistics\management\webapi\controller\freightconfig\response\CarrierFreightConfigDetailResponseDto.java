package com.logistics.management.webapi.controller.freightconfig.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
public class CarrierFreightConfigDetailResponseDto {

    @ApiModelProperty(value = "运价配置Id")
    private String freightConfigId = "";

    @ApiModelProperty(value = "价格类型; 1: 固定路线; 2: 区域设置; 3: 距离阶梯")
    private String configType = "";

    @ApiModelProperty(value = "价格类型文本;")
    private String configTypeLabel = "";

    @ApiModelProperty(value = "需求类型")
    private List<Integer> entrustTypes;

    @ApiModelProperty(value = "创建人")
    private String createdBy = "";

    @ApiModelProperty(value = "创建时间")
    private String createdTime = "";

    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy = "";
    
    @ApiModelProperty(value = "操作时间")
    private String lastModifiedTime = "";

}
