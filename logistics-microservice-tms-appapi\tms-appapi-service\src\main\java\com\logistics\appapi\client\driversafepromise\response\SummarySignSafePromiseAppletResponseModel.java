package com.logistics.appapi.client.driversafepromise.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/12/13 15:36
 */
@Data
public class SummarySignSafePromiseAppletResponseModel {
    @ApiModelProperty("承诺书ID")
    private Long safePromiseId;
    @ApiModelProperty("已签订人数")
    private Integer hasSignCount;
    @ApiModelProperty("未签订人数")
    private Integer notSignCount;
}
