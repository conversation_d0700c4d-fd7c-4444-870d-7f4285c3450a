package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.base.enums.PriceTypeEnum;
import com.logistics.management.webapi.base.enums.SettlementTonnageEnum;
import com.logistics.management.webapi.client.carrierorder.response.SignDetailForYeloLifeGoodsCodeResponseModel;
import com.logistics.management.webapi.client.carrierorder.response.SignDetailForYeloLifeGoodsResponseModel;
import com.logistics.management.webapi.client.carrierorder.response.SignDetailForYeloLifeResponseModel;
import com.logistics.management.webapi.controller.carrierorder.response.SignDetailForYeloLifeResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/24
 */
public class CarrierOrderSignUpDetailForYeloLifeMapping extends MapperMapping<SignDetailForYeloLifeResponseModel, SignDetailForYeloLifeResponseDto> {

	@Override
	public void configure() {
		SignDetailForYeloLifeResponseModel source = getSource();
		SignDetailForYeloLifeResponseDto destination = getDestination();

		if (source != null) {
			//司机
			destination.setDriver(source.getDriverName());
			//提货地址
			destination.setLoadAddress(source.getLoadProvinceName() + source.getLoadCityName() + source.getLoadAreaName() + (StringUtils.isNotEmpty(source.getLoadWarehouse()) ? "【" + source.getLoadWarehouse() + "】" : ""));
			//卸货地址
			destination.setUnloadAddress(source.getUnloadProvinceName() + source.getUnloadCityName() + source.getUnloadAreaName() + (StringUtils.isNotEmpty(source.getUnloadWarehouse()) ? "【" + source.getUnloadWarehouse() + "】" : ""));

			//司机费用转换
			BigDecimal dispatchFreightFee = source.getDispatchFreightFee();
			if (dispatchFreightFee != null) {
				if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getDispatchFreightFeeType())) {
					destination.setDispatchFreightFee(dispatchFreightFee.multiply(Optional.ofNullable(source.getUnloadAmount()).orElse(BigDecimal.ZERO)).setScale(2, RoundingMode.HALF_UP).toPlainString());
				}else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getDispatchFreightFeeType())){
					destination.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee));
				}
			}

			//单位转换
			destination.setGoodsUnitLabel(GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());


			//货主实际结算数量
			BigDecimal entrustSettlementAmount = BigDecimal.ZERO;
			if (SettlementTonnageEnum.LOAD.getKey().equals(source.getSettlementTonnage())) {
				entrustSettlementAmount = source.getLoadAmount();
			} else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getSettlementTonnage())) {
				entrustSettlementAmount = source.getUnloadAmount();
			} else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getSettlementTonnage())) {
				entrustSettlementAmount = source.getUnloadAmount();
			} else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getSettlementTonnage())) {
				entrustSettlementAmount = source.getExpectAmount();
			}
			//货主费用
			BigDecimal actualEntrustFee = BigDecimal.ZERO;
			if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getEntrustFreightType())){
				actualEntrustFee = source.getEntrustFreight().multiply(entrustSettlementAmount).setScale(2,BigDecimal.ROUND_HALF_UP);
			}else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getEntrustFreightType())){
				actualEntrustFee = source.getEntrustFreight();
			}
			destination.setEntrustFreightFee(ConverterUtils.toString(actualEntrustFee));

			//车主实际结算数量
			BigDecimal carrierSettlementAmount = BigDecimal.ZERO;
			if (SettlementTonnageEnum.LOAD.getKey().equals(source.getCarrierSettlement())) {
				carrierSettlementAmount = source.getLoadAmount();
			} else if (SettlementTonnageEnum.UNLOAD.getKey().equals(source.getCarrierSettlement())) {
				carrierSettlementAmount = source.getUnloadAmount();
			} else if (SettlementTonnageEnum.SIGN.getKey().equals(source.getCarrierSettlement())) {
				carrierSettlementAmount = source.getUnloadAmount();
			} else if (SettlementTonnageEnum.EXPECT.getKey().equals(source.getCarrierSettlement())) {
				carrierSettlementAmount = source.getExpectAmount();
			}
			//车主费用
			BigDecimal carrierFreight = null;
			if (PriceTypeEnum.UNIT_PRICE.getKey().equals(source.getCarrierPriceType())){
				carrierFreight = source.getCarrierPrice().multiply(carrierSettlementAmount).setScale(2,BigDecimal.ROUND_HALF_UP);
			}else if (PriceTypeEnum.FIXED_PRICE.getKey().equals(source.getCarrierPriceType())){
				carrierFreight = source.getCarrierPrice();
			}
			if (carrierFreight != null){
				destination.setCarrierFreight(carrierFreight.compareTo(BigDecimal.ZERO) == CommonConstant.INTEGER_ZERO ? CommonConstant.BLANK_TEXT : ConverterUtils.toString(carrierFreight));
			}

		}
	}
}
