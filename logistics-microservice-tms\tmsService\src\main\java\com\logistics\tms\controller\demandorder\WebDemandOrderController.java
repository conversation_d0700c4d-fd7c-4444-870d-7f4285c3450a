package com.logistics.tms.controller.demandorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.demandorder.DemandOrderBiz;
import com.logistics.tms.controller.demandorder.request.DemandOrderDriverRequestModel;
import com.logistics.tms.controller.demandorder.request.WebDemandOrderSearchRequestModel;
import com.logistics.tms.controller.demandorder.response.WebDemandOrderResponseModel;
import com.logistics.tms.controller.demandorder.response.WebDemandOrderSearchAccountResponseModel;
import com.logistics.tms.controller.dispatch.response.VehicleSearchResponseModel;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@RestController
@RequestMapping(value = "/service/webDemandOrder")
public class WebDemandOrderController {

    @Autowired
    private DemandOrderBiz demandOrderBiz;

    /**
     * 承运商-调度车辆-获取需求单列表（调度车辆）
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "承运商-调度车辆-需求单列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<WebDemandOrderResponseModel>> searchList(@RequestBody WebDemandOrderSearchRequestModel requestModel) {
        return Result.success(demandOrderBiz.searchListWeb(requestModel));
    }

    /**
     * 获取需求单列表每个状态的需求单数量（调度车辆）
     * @return
     */
    @ApiOperation(value = "承运商-调度车辆-获取需求单列表每个状态的需求单数量")
    @PostMapping(value = "/searchListAccount")
    public Result<WebDemandOrderSearchAccountResponseModel> searchListAccount(@RequestBody WebDemandOrderSearchRequestModel requestModel) {
        return Result.success(demandOrderBiz.searchListAccount(requestModel));
    }

    /**
     * 承运商-调度车辆-导出需求单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "承运商-调度车辆-导出需求单")
    @PostMapping(value = "/exportDemandOrder")
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<WebDemandOrderResponseModel>> exportDemandOrder(@RequestBody WebDemandOrderSearchRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return Result.success(demandOrderBiz.searchListWeb(requestModel).getList());
    }

    /**
     * 查询车辆
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询车辆")
    @PostMapping(value = "/searchDriverAndVehicle")
    public Result<List<VehicleSearchResponseModel>> searchDriverAndVehicle(@RequestBody DemandOrderDriverRequestModel requestModel) {
        return Result.success(demandOrderBiz.searchDriverAndVehicle(requestModel));
    }
}
