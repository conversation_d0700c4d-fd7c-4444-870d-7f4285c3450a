package com.logistics.appapi.controller.carrierorder;


import com.github.pagehelper.PageInfo;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.client.carrierorder.CarrierOrderClient;
import com.logistics.appapi.client.carrierorder.request.CarrierOrderIdRequestModel;
import com.logistics.appapi.client.carrierorder.request.CarrierOrderLoadRequestModel;
import com.logistics.appapi.client.carrierorderpickup.CustomerOrderLoadCodeClient;
import com.logistics.appapi.client.carrierorderpickup.request.*;
import com.logistics.appapi.client.carrierorderpickup.response.*;
import com.logistics.appapi.controller.carrierorder.request.*;
import com.logistics.appapi.controller.carrierorder.response.*;
import com.logistics.appapi.controller.common.CommonBiz;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@Api(value = "运单编码提货管理", tags = "运单编码提货管理")
public class CarrierOrderPickUpController {

    @Resource
    private CustomerOrderLoadCodeClient customerOrderLoadCodeClient;
    @Resource
    private CommonBiz commonBiz;



//    @ApiOperation(value = "提货 (v2.46)")
//    @PostMapping(value = "/api/driverApplet/carrierOrder/pickUp")
//    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
//    public Result pickUp(@RequestBody @Valid CarrierOrderLoadRequestDto requestDto) {
//        List<CarrierOrderTicketRequestDto> tickets = requestDto.getTickets();
//        if (ListUtils.isNotEmpty(tickets)) {
//            //现场图片数量
//            int localeTicketCount = CommonConstant.INTEGER_ZERO;
//            //出库单数量
//            int outTicketCount = CommonConstant.INTEGER_ZERO;
//            for (CarrierOrderTicketRequestDto ticket : tickets) {
//                if (CommonConstant.TWO.equals(ticket.getTicketType())) {
//                    outTicketCount++;
//                    if (outTicketCount > CommonConstant.INTEGER_SIX) {
//                        //出库单最多6张
//                        throw new BizException(AppApiExceptionEnum.TICKET_COUNT_MAX);
//                    }
//                } else if (CommonConstant.EIGHT.equals(ticket.getTicketType())) {
//                    localeTicketCount++;
//                    if (localeTicketCount > CommonConstant.INTEGER_TWO) {
//                        //现场图片最多2张
//                        throw new BizException(AppApiExceptionEnum.LOCALE_TICKET_COUNT_MAX);
//                    }
//                } else {
//                    throw new BizException(AppApiExceptionEnum.PARAMS_ERROR);
//                }
//            }
//        }
//        //校验触达带料共享托盘图片
//        SiteOtherPalletsRequestDto siteOtherPalletsDto = requestDto.getSiteOtherPallets();
//        if (siteOtherPalletsDto != null) {
//            //空置数量
//            String emptyTraysAmountStr = siteOtherPalletsDto.getEmptyTraysAmount();
//            if (emptyTraysAmountStr != null) {
//                int emptyTraysAmountInt = Integer.parseInt(emptyTraysAmountStr);
//                if (emptyTraysAmountInt < CommonConstant.INTEGER_ONE || emptyTraysAmountInt > CommonConstant.INTEGER_TEN_THOUSAND) {
//                    //1-10000范围内的数字
//                    throw new BizException(AppApiExceptionEnum.TRAYS_AMOUNT_MAX);
//                }
//            }
//            //带料占用数量
//            String employTraysAmountStr = siteOtherPalletsDto.getEmployTraysAmount();
//            if (employTraysAmountStr != null) {
//                int employTraysAmountInt = Integer.parseInt(employTraysAmountStr);
//                if (employTraysAmountInt < CommonConstant.INTEGER_ONE || employTraysAmountInt > CommonConstant.INTEGER_TEN_THOUSAND) {
//                    //1-10000范围内的数字
//                    throw new BizException(AppApiExceptionEnum.TRAYS_AMOUNT_MAX);
//                }
//                //托盘非空置时,需要带料托盘的图片
//                if (ListUtils.isEmpty(siteOtherPalletsDto.getSharedTrayPics())) {
//                    throw new BizException(AppApiExceptionEnum.REACH_TRY_PIC);
//                }
//            }
//            if (ListUtils.isNotEmpty(siteOtherPalletsDto.getSharedTrayPics()) && siteOtherPalletsDto.getSharedTrayPics().size() > CommonConstant.INTEGER_THREE){
//                throw new BizException(AppApiExceptionEnum.REACH_TRY_PIC);
//            }
//        }
//        Result result = carrierOrderClient.pickUp(MapperUtils.mapper(requestDto, CarrierOrderLoadRequestModel.class));
//        if ( result.getErrcode()==CommonConstant.CODE_NOT_EXIST){
//            return new Result(CommonConstant.CODE_NOT_EXIST,result.getData(),result.getErrmsg());
//        }else {
//            result.throwException();
//        }
//        return result;
//    }


    @ApiOperation(value = "提货详情(编码提货) (v2.46)")
    @PostMapping(value = "/api/driverApplet/carrierOrder/codePickUpDetail")
    public Result<CodePickUpDetailResponseDto> codePickUpDetail(@RequestBody @Valid CarrierOrderIdRequestDto requestDto) {
        CarrierOrderIdRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, CarrierOrderIdRequestModel.class);
        Result<CodePickUpDetailResponseModel> result = customerOrderLoadCodeClient.codePickUpDetail(requestModel);
        result.throwException();
        CodePickUpDetailResponseDto responseDto = MapperUtils.mapperNoDefault(result.getData(), CodePickUpDetailResponseDto.class);
        return Result.success(responseDto);
    }


    @ApiOperation(value = "提货-正确临时编码列表 (v2.46)")
    @PostMapping(value = "/api/driverApplet/carrierOrder/correctLoadCodeList")
    public Result<PageInfo<CorrectLoadCodeListResponseDto>> correctLoadCodeList(@RequestBody @Valid CorrectLoadCodeListRequestDto requestDto) {
        CorrectLoadCodeListRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, CorrectLoadCodeListRequestModel.class);
        Result<PageInfo<CorrectLoadCodeListResponseModel>> result = customerOrderLoadCodeClient.correctLoadCodeList(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<CorrectLoadCodeListResponseDto> responseDtos = MapperUtils.mapperNoDefault(pageInfo.getList(), CorrectLoadCodeListResponseDto.class);
        pageInfo.setList(responseDtos);
        return Result.success(pageInfo);
    }

    @ApiOperation(value = "提货-有误临时编码列表 (v2.46)")
    @PostMapping(value = "/api/driverApplet/carrierOrder/errorLoadCodeList")
    public Result<PageInfo<ErrorLoadCodeListResponseDto>> errorLoadCodeList(@RequestBody @Valid ErrorLoadCodeListRequestDto requestDto) {
        ErrorLoadCodeListRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, ErrorLoadCodeListRequestModel.class);
        Result<PageInfo<ErrorLoadCodeListResponseModel>> result = customerOrderLoadCodeClient.errorLoadCodeList(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<ErrorLoadCodeListResponseDto> responseDtos = MapperUtils.mapperNoDefault(pageInfo.getList(), ErrorLoadCodeListResponseDto.class);
        pageInfo.setList(responseDtos);
        return Result.success(pageInfo);
    }


    @ApiOperation(value = "提货-保存正确的临时编码 (v2.46)")
    @PostMapping(value = "/api/driverApplet/carrierOrder/saveCorrectLoadCode")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<SaveCorrectLoadCodeResponseDto> saveCorrectLoadCode(@RequestBody @Valid SaveCorrectLoadCodeRequestDto requestDto) {
        SaveCorrectLoadCodeRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, SaveCorrectLoadCodeRequestModel.class);
        Result<SaveCorrectLoadCodeResponseModel> result = customerOrderLoadCodeClient.saveCorrectLoadCode(requestModel);
        result.throwException();
        SaveCorrectLoadCodeResponseDto responseDto = MapperUtils.mapperNoDefault(result.getData(), SaveCorrectLoadCodeResponseDto.class);
        return Result.success(responseDto);
    }


    @ApiOperation(value = "提货-保存错误的临时编码附件 (v2.46)")
    @PostMapping(value = "/api/driverApplet/carrierOrder/saveErrorLoadCodeFile")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<SaveErrorLoadCodeFileResponseDto> saveErrorLoadCodeFile(@RequestBody @Valid SaveErrorLoadCodeFileRequestDto requestDto) {
        SaveErrorLoadCodeFileRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, SaveErrorLoadCodeFileRequestModel.class);
        Result<SaveErrorLoadCodeFileResponseModel> result = customerOrderLoadCodeClient.saveErrorLoadCodeFile(requestModel);
        result.throwException();
        SaveErrorLoadCodeFileResponseDto responseDto = MapperUtils.mapperNoDefault(result.getData(), SaveErrorLoadCodeFileResponseDto.class);
        return Result.success(responseDto);
    }

    @ApiOperation(value = "提货-删除正确的临时编码 (v2.46)")
    @PostMapping(value = "/api/driverApplet/carrierOrder/deleteCorrectLoadCode")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> deleteCorrectLoadCode(@RequestBody @Valid DeleteCorrectLoadCodeRequestDto requestDto) {
        CarrierOrderLoadCodeIdRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, CarrierOrderLoadCodeIdRequestModel.class);
        Result<Boolean> result = customerOrderLoadCodeClient.deleteCarrierOrderLoadCode(requestModel);
        result.throwException();
        return Result.success(true);
    }

    @ApiOperation(value = "提货-删除有误的临时编码 (v2.46)")
    @PostMapping(value = "/api/driverApplet/carrierOrder/deleteErrorLoadCode")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> deleteErrorLoadCode(@RequestBody @Valid DeleteErrorLoadCodeRequestDto requestDto) {
        CarrierOrderLoadCodeIdRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, CarrierOrderLoadCodeIdRequestModel.class);
        Result<Boolean> result = customerOrderLoadCodeClient.deleteCarrierOrderLoadCode(requestModel);
        result.throwException();
        return Result.success(true);
    }


    @ApiOperation(value = "提货-有误临时编码查看 (v2.46)")
    @PostMapping(value = "/api/driverApplet/carrierOrder/errorLoadCodeGetDetail")
    public Result<ErrorLoadCodeGetDetailResponseDto> errorLoadCodeGetDetail(@RequestBody @Valid ErrorLoadCodeGetDetailRequestDto requestDto) {
        ErrorLoadCodeGetDetailRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, ErrorLoadCodeGetDetailRequestModel.class);
        Result<ErrorLoadCodeGetDetailResponseModel> result = customerOrderLoadCodeClient.errorLoadCodeGetDetail(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapperNoDefault(result.getData(),ErrorLoadCodeGetDetailResponseDto.class));
    }


}
