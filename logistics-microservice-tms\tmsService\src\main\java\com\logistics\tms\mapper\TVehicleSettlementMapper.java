package com.logistics.tms.mapper;

import com.logistics.tms.controller.vehiclesettlement.request.GetSettlementDriverRequestModel;
import com.logistics.tms.controller.vehiclesettlement.request.SearchDriverReconciliationListRequestModel;
import com.logistics.tms.controller.vehiclesettlement.request.SearchVehicleSettlementListRequestModel;
import com.logistics.tms.controller.vehiclesettlement.request.SendDriverSettleStatementListRequestModel;
import com.logistics.tms.controller.vehiclesettlement.response.*;
import com.logistics.tms.entity.TVehicleSettlement;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TVehicleSettlementMapper extends BaseMapper<TVehicleSettlement>{

    TVehicleSettlement getByVehicleIdAndMonth(@Param("vehicleId") Long vehicleId, @Param("settlementMonth") String settlementMonth);

    List<SearchVehicleSettlementListResponseModel> searchVehicleSettlementList(@Param("params") SearchVehicleSettlementListRequestModel requestModel);

    List<SendDriverSettleStatementListResponseModel> sendDriverSettleStatementList(SendDriverSettleStatementListRequestModel requestModel);

    SearchVehicleSettlementListCountResponseModel searchVehicleSettlementListCount(@Param("params") SearchVehicleSettlementListRequestModel requestModel);

    GetVehicleSettlementDetailResponseModel getVehicleSettlementDetail(@Param("id")Long id);

    List<VehicleSettlementKanBanResponseModel> vehicleSettlementKanBan(@Param("settlementYear")String settlementYear);

    TVehicleSettlement getFirstNotSettlementByVehicleId(@Param("vehicleId") Long vehicleId);

    List<Long> getVehicleByMonth(@Param("settlementMonth") String settlementMonth);

    CancelVehicleSettlementDetailResponseModel getCancelVehicleSettlementDetailById(@Param("vehicleSettlementId") Long vehicleSettlementId);

    List<GetSettlementDriverResponseModel> getSettlementVehicleDriverInfo(GetSettlementDriverRequestModel requestModel);

    SettleFreightDetailResponseModel settleFreightDetailById(@Param("vehicleSettlementId") Long vehicleSettlementId);

    List<TVehicleSettlement> getVehicleSettlementByIds(@Param("ids")String ids);

    int batchUpdateSelective(@Param("list") List<TVehicleSettlement> list);

    SettlementStatementRecordResponseModel getSettlementStatementRecordList(@Param("vehicleSettlementId") Long vehicleSettlementId);

    SettlementStatementHandleDetailResponseModel settlementStatementHandleDetail(@Param("vehicleSettlementId") Long vehicleSettlementId);

    SettlementStatementHandleModel settlementStatementHandleInfo(@Param("vehicleSettlementId") Long vehicleSettlementId);

    List<GetSettlementDriverModel> getSettlementVehicleDriverInfoByIds(@Param("vehicleSettlementIds") String vehicleSettlementIds);

    List<GetSettlementDriverModel> getSettlementVehicleDriverInfoNoCarrier(@Param("vehicleSettlementIds") String vehicleSettlementIds);


    //以下小程序司机对账用接口
    List<SearchDriverReconciliationListResponseModel> searchDriverReconciliationList(@Param("params") SearchDriverReconciliationListRequestModel requestModel);

    DriverReconciliationListCountResponseModel driverReconciliationListCount(@Param("params") SearchDriverReconciliationListRequestModel requestModel);

    DriverReconciliationDetailResponseModel driverReconciliationDetail(@Param("vehicleSettlementId")Long vehicleSettlementId, @Param("driverId") Long driverId);

    DriverReconciliationConfirmDetailResponseModel driverReconciliationConfirmDetail(@Param("vehicleSettlementId")Long vehicleSettlementId, @Param("driverId") Long driverId);

    ReconciliationCarrierOrderDetailResponseModel driverReconciliationCarrierOrder(@Param("vehicleSettlementId")Long vehicleSettlementId, @Param("driverId") Long driverId);

    ReconciliationTireDetailResponseModel driverReconciliationTire(@Param("vehicleSettlementId")Long vehicleSettlementId, @Param("driverId") Long driverId);

    ReconciliationOilFilledDetailResponseModel driverReconciliationOilFilled(@Param("vehicleSettlementId")Long vehicleSettlementId, @Param("driverId") Long driverId);
    //以上为小程序司机对账用接口


}