<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleGpsRecordMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleGpsRecord" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="vehicle_id" property="vehicleId" jdbcType="BIGINT" />
    <result column="install_time" property="installTime" jdbcType="TIMESTAMP" />
    <result column="terminal_type" property="terminalType" jdbcType="VARCHAR" />
    <result column="sim_number" property="simNumber" jdbcType="VARCHAR" />
    <result column="gps_service_provider" property="gpsServiceProvider" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, vehicle_id, install_time, terminal_type, sim_number, gps_service_provider, remark, 
    created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_gps_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_vehicle_gps_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleGpsRecord" >
    insert into t_vehicle_gps_record (id, vehicle_id, install_time,
      terminal_type, sim_number, gps_service_provider, 
      remark, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{installTime,jdbcType=TIMESTAMP}, 
      #{terminalType,jdbcType=VARCHAR}, #{simNumber,jdbcType=VARCHAR}, #{gpsServiceProvider,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleGpsRecord" useGeneratedKeys="true" keyProperty="id">
    insert into t_vehicle_gps_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="vehicleId != null" >
        vehicle_id,
      </if>
      <if test="installTime != null" >
        install_time,
      </if>
      <if test="terminalType != null" >
        terminal_type,
      </if>
      <if test="simNumber != null" >
        sim_number,
      </if>
      <if test="gpsServiceProvider != null" >
        gps_service_provider,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null" >
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="installTime != null" >
        #{installTime,jdbcType=TIMESTAMP},
      </if>
      <if test="terminalType != null" >
        #{terminalType,jdbcType=VARCHAR},
      </if>
      <if test="simNumber != null" >
        #{simNumber,jdbcType=VARCHAR},
      </if>
      <if test="gpsServiceProvider != null" >
        #{gpsServiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleGpsRecord" >
    update t_vehicle_gps_record
    <set >
      <if test="vehicleId != null" >
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="installTime != null" >
        install_time = #{installTime,jdbcType=TIMESTAMP},
      </if>
      <if test="terminalType != null" >
        terminal_type = #{terminalType,jdbcType=VARCHAR},
      </if>
      <if test="simNumber != null" >
        sim_number = #{simNumber,jdbcType=VARCHAR},
      </if>
      <if test="gpsServiceProvider != null" >
        gps_service_provider = #{gpsServiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleGpsRecord" >
    update t_vehicle_gps_record
    set vehicle_id = #{vehicleId,jdbcType=BIGINT},
      install_time = #{installTime,jdbcType=TIMESTAMP},
      terminal_type = #{terminalType,jdbcType=VARCHAR},
      sim_number = #{simNumber,jdbcType=VARCHAR},
      gps_service_provider = #{gpsServiceProvider,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>