package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/23 14:51
 */
@Data
public class CarrierOrderInfoForAutoModel {

    @ApiModelProperty("运单ID")
    private Long carrierOrderId;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("需求单ID")
    private Long demandOrderId;

    @ApiModelProperty("车主id")
    private Long companyCarrierId;

    @ApiModelProperty("预提数量")
    private BigDecimal expectAmount;

    @ApiModelProperty("实际实提数量")
    private BigDecimal loadAmount;

    @ApiModelProperty("实际卸货数量")
    private BigDecimal unloadAmount;

    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;

    @ApiModelProperty("货主运费价格类型 1 单价 2 一口价")
    private Integer entrustFreightType;

    @ApiModelProperty("车主价格：1 单价(元/吨，元/件)，2 一口价(元)")
    private Integer carrierPriceType;

    @ApiModelProperty("车主价格")
    private BigDecimal carrierPrice;

    @ApiModelProperty("货主运费")
    private BigDecimal entrustFreight;

    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;

    @ApiModelProperty(value = "委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private Integer entrustType;

    @ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer carrierSettlement;

    @ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
    private Integer settlementTonnage;

    @ApiModelProperty("车主对账单状态, -2 未关联对账，-1 待提交，0 待业务审核，1 待财务审核，2 已对账，3 已驳回")
    private Integer carrierSettleStatementStatus;

    @ApiModelProperty("货物信息")
    private List<CarrierOrderGoodsForAutoModel> goodsList;

    @ApiModelProperty("云仓异常数量")
    private BigDecimal abnormalAmount;

    @ApiModelProperty("预计里程数")
    private BigDecimal expectMileage;


    @ApiModelProperty("议价模式：1 指定车主，2 竞价抢单，3 临时定价，4 零担定价")
    private Integer bargainingMode;


    private Integer demandOrderEntrustType;

    private String projectLabel;
}
