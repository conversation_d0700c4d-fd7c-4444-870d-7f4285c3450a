package com.logistics.appapi.client.carrierorderpickup.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.carrierorder.request.CarrierOrderIdRequestModel;
import com.logistics.appapi.client.carrierorderpickup.CustomerOrderLoadCodeClient;
import com.logistics.appapi.client.carrierorderpickup.request.*;
import com.logistics.appapi.client.carrierorderpickup.response.*;
import com.logistics.appapi.client.customeraccount.CustomerAccountClient;
import com.logistics.appapi.client.customeraccount.request.*;
import com.logistics.appapi.client.customeraccount.response.CustomerLoginResponseModel;
import com.logistics.appapi.client.customeraccount.response.UpdatePhoneByVerifyPhoneResponseModel;
import com.logistics.appapi.controller.carrierorder.response.CodePickUpDetailResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.validation.Valid;


@Component
public class CustomerOrderLoadCodeClientHystrix implements CustomerOrderLoadCodeClient {

    @Override
    public Result<PageInfo<CorrectLoadCodeListResponseModel>> correctLoadCodeList(@Valid CorrectLoadCodeListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<ErrorLoadCodeListResponseModel>> errorLoadCodeList(@Valid ErrorLoadCodeListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SaveCorrectLoadCodeResponseModel> saveCorrectLoadCode(@Valid SaveCorrectLoadCodeRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SaveErrorLoadCodeFileResponseModel> saveErrorLoadCodeFile(@Valid SaveErrorLoadCodeFileRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> deleteCarrierOrderLoadCode(@Valid CarrierOrderLoadCodeIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<CodePickUpDetailResponseModel> codePickUpDetail(CarrierOrderIdRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ErrorLoadCodeGetDetailResponseModel> errorLoadCodeGetDetail(@Valid ErrorLoadCodeGetDetailRequestModel requestDto) {
        return Result.timeout();
    }

}
