package com.logistics.appapi.client.thirdparty.basicdata.baidu.hystrix;

import com.logistics.appapi.client.thirdparty.basicdata.baidu.BaiDuServiceApi;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.request.CheckSensitiveWordRequestModel;
import com.logistics.appapi.client.thirdparty.basicdata.baidu.response.CheckSensitiveWordResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/7 13:06
 */
@Component
public class BaiDuServiceApiHystrix implements BaiDuServiceApi {
    @Override
    public Result<CheckSensitiveWordResponseModel> checkSensitiveWord(CheckSensitiveWordRequestModel requestModel) {
        return Result.timeout();
    }
}
