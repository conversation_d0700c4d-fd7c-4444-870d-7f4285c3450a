<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDriverSafePromiseRelationMapper">
    <select id="getSummaryBySafePromiseIds" resultType="com.logistics.tms.controller.driversafepromise.response.SummarySavePromiseInfoModel">
        select
           rel.safe_promise_id as safePromiseId,
           ifnull(sum(if(rel.status = 0,1,0)),0) as notSignCount,
           ifnull(sum(if(rel.status = 1,1,0)),0) as hasSignCount
        from t_driver_safe_promise_relation rel
        where rel.valid = 1
        group by rel.safe_promise_id
        having rel.safe_promise_id in (${safePromiseIds})
    </select>


    <select id="getBySafePromiseIdAndStatus" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_driver_safe_promise_relation
        where valid = 1
        and safe_promise_id = #{safePromiseId,jdbcType = BIGINT}
        <if test="status!=null">
            and status = #{status,jdbcType = INTEGER}
        </if>
    </select>

    <select id="searchSignList" resultType="com.logistics.tms.controller.driversafepromise.response.SearchSignSafePromiseListResponseModel">
        select
        rel.safe_promise_id                              as safePromiseId,
        rel.id                                           as signSafePromiseId,
        rel.staff_id                                     as staffId,
        rel.staff_name                                   as staffName,
        rel.staff_mobile                                 as staffMobile,
        rel.status                                       as status,
        rel.sign_time                                    as signTime,
        rel.hand_promise_url                             as handPromiseUrl,
        rel.sign_responsibility_url                      as signResponsibilityUrl,
        rel.staff_property                               as staffProperty
        from t_driver_safe_promise_relation rel
        left join t_driver_safe_promise tdsp on tdsp.id = rel.safe_promise_id and tdsp.valid = 1
        where rel.valid = 1
        and rel.safe_promise_id = #{params.safePromiseId,jdbcType = BIGINT}
        <if test="params.staffName!=null and params.staffName!=''">
            and instr(rel.staff_name,#{params.staffName,jdbcType = VARCHAR})
        </if>
        <if test="params.staffMobile!=null and params.staffMobile!=''">
            and instr(rel.staff_mobile,#{params.staffMobile,jdbcType = VARCHAR})
        </if>
        <if test="params.status!=null">
          and rel.status = #{params.status,jdbcType = INTEGER}
        </if>
        <if test="params.staffProperty != null">
            and rel.staff_property = #{params.staffProperty,jdbcType=INTEGER}
        </if>
        order by tdsp.period desc,rel.sign_time desc,rel.created_time desc,rel.id desc
    </select>

    <select id="getSummarySignCount" resultType="com.logistics.tms.controller.driversafepromise.response.SummarySignSafePromiseResponseModel">
         select
           count(*) as allCount,
           ifnull(sum(if(rel.status = 0,1,0)),0) as notSignCount,
           ifnull(sum(if(rel.status = 1,1,0)),0) as hasSignCount
        from t_driver_safe_promise_relation rel
        where rel.valid = 1
        and rel.safe_promise_id = #{params.safePromiseId,jdbcType = BIGINT}
        <if test="params.staffName!=null and params.staffName!=''">
            and instr(rel.staff_name,#{params.staffName,jdbcType = VARCHAR})
        </if>
        <if test="params.staffMobile!=null and params.staffMobile!=''">
            and instr(rel.staff_mobile,#{params.staffMobile,jdbcType = VARCHAR})
        </if>
        <if test="params.staffProperty != null">
            and rel.staff_property = #{params.staffProperty,jdbcType=INTEGER}
        </if>
    </select>

    <select id="getAppletSignSummary" resultType="com.logistics.tms.controller.driversafepromise.response.SummarySignSafePromiseAppletResponseModel">
        select
        count(*) as allCount,
        ifnull(sum(if(rel.status = 0,1,0)),0) as notSignCount,
        ifnull(sum(if(rel.status = 1,1,0)),0) as hasSignCount
        from t_driver_safe_promise_relation rel
        where rel.valid = 1
        and rel.staff_id = #{params.staffId,jdbcType=BIGINT}
    </select>

    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TDriverSafePromiseRelation">
        <foreach collection="list" item="item" separator=";">
          insert into t_driver_safe_promise_relation
          <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="item.id != null">
                id,
            </if>
            <if test="item.safePromiseId != null">
                safe_promise_id,
            </if>
            <if test="item.staffId != null">
                staff_id,
            </if>
            <if test="item.staffProperty != null">
                staff_property,
            </if>
            <if test="item.staffName != null">
                staff_name,
            </if>
            <if test="item.staffMobile != null">
                staff_mobile,
            </if>
            <if test="item.vehicleNo != null">
                vehicle_no,
            </if>
            <if test="item.status != null">
                status,
            </if>
            <if test="item.signTime != null">
                sign_time,
            </if>
            <if test="item.handPromiseUrl != null">
                hand_promise_url,
            </if>
            <if test="item.signResponsibilityUrl != null">
                sign_responsibility_url,
            </if>
            <if test="item.createdBy != null">
                created_by,
            </if>
            <if test="item.createdTime != null">
                created_time,
            </if>
            <if test="item.lastModifiedBy != null">
                last_modified_by,
            </if>
            <if test="item.lastModifiedTime != null">
                last_modified_time,
            </if>
            <if test="item.valid != null">
                valid,
            </if>
          </trim>
          <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="item.id != null">
                #{item.id,jdbcType=BIGINT},
            </if>
            <if test="item.safePromiseId != null">
                #{item.safePromiseId,jdbcType=BIGINT},
            </if>
            <if test="item.staffId != null">
                #{item.staffId,jdbcType=BIGINT},
            </if>
            <if test="item.staffProperty != null">
                #{item.staffProperty,jdbcType=INTEGER},
            </if>
            <if test="item.staffName != null">
                #{item.staffName,jdbcType=VARCHAR},
            </if>
            <if test="item.staffMobile != null">
                #{item.staffMobile,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleNo != null">
                #{item.vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="item.status != null">
                #{item.status,jdbcType=INTEGER},
            </if>
            <if test="item.signTime != null">
                #{item.signTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.handPromiseUrl != null">
                #{item.handPromiseUrl,jdbcType=VARCHAR},
            </if>
            <if test="item.signResponsibilityUrl != null">
                #{item.signResponsibilityUrl,jdbcType=VARCHAR},
            </if>
            <if test="item.createdBy != null">
                #{item.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="item.createdTime != null">
                #{item.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastModifiedBy != null">
                #{item.lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="item.lastModifiedTime != null">
                #{item.lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.valid != null">
                #{item.valid,jdbcType=INTEGER},
            </if>
          </trim>
        </foreach>
    </insert>
    <select id="getAppletDetail"
            resultType="com.logistics.tms.controller.driversafepromise.response.SafePromiseAppletDetailResponseModel">
        select
            tdspr.id as relationId,
            tdspr.status,
            tdspr.hand_promise_url as handPromiseUrl,
            tdspr.sign_responsibility_url as signResponsibilityUrl,
            tdspr.sign_time as signTime,
            tdspr.vehicle_no as vehicleNo,
            tdsp.period as period,
            tdsp.created_time as publishTime,
            tdsp.agent,
            tdsp.content,
            tdsp.attachment_url as attachmentUrl
        from t_driver_safe_promise_relation tdspr
        left join t_driver_safe_promise tdsp on tdsp.id = tdspr.safe_promise_id and tdsp.valid=1
        where tdspr.valid=1
        and tdspr.id = #{id,jdbcType=BIGINT}
    </select>
</mapper>