package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/17 16:05
 */
@Data
public class CarrierOrderDetailFreightFeeInfoForYeloLifeDto {

    //货主运费
    @ApiModelProperty("预计货主费用类型")
    private String entrustFreightType="";

    @ApiModelProperty("实际货主费用类型")
    private String actualEntrustFreightType="";

    @ApiModelProperty("预计货主运费")
    private String entrustFreight="";

    @ApiModelProperty("预计货主运费合计")
    private String entrustFreightTotal="";

    @ApiModelProperty("实际货主运费")
    private String signEntrustFreight="";

    @ApiModelProperty("实际货主运费合计")
    private String signEntrustFreightTotal="";

    //司机运费
    @ApiModelProperty("司机运费价格类型")
    private String dispatchFreightFeeType="";

    private String dispatchFreightFeeTypeLabel="";

    @ApiModelProperty("预计司机运费")
    private String dispatchFreightFee="";

    @ApiModelProperty("预计司机运费合计")
    private String dispatchFreightFeeTotal="";

    @ApiModelProperty("调整费用")
    private String adjustFee="";

    @ApiModelProperty("多装多卸加价费用")
    private String markupFee="";

    @ApiModelProperty("实际司机运费")
    private String signDispatchFreightFee="";

    @ApiModelProperty("实际司机运费合计")
    private String signDispatchFreightFeeTotal="";

    @ApiModelProperty("调整费用")
    private String signAdjustFee="";

    @ApiModelProperty("多装多卸加价费用")
    private String signMarkupFee="";

    @ApiModelProperty("预计司机运费中文单位")
    private String dispatchFreightFeeUnit="";

    //车主运费
    /**
     * (3.23.0)预计车主费用类型
     */
    @ApiModelProperty("预计车主费用类型")
    private String carrierFreightType = "";

    /**
     * (3.23.0)实际车主费用类型
     */
    @ApiModelProperty("实际车主费用类型")
    private String actualCarrierFreightType = "";

    /**
     * (3.23.0)预计车主运费
     */
    @ApiModelProperty("预计车主运费")
    private String carrierFreight = "";

    /**
     * (3.23.0)预计车主运费合计
     */
    @ApiModelProperty("预计车主运费合计")
    private String carrierFreightTotal = "";

    /**
     * (3.23.0)实际车主运费
     */
    @ApiModelProperty("实际车主运费")
    private String signCarrierFreight = "";

    /**
     * (3.23.0)实际车主运费合计
     */
    @ApiModelProperty("实际车主运费合计")
    private String signCarrierFreightTotal = "";
}
