package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum AttendanceChangeAuditStatusEnum {

    DEFAULT(-1, ""),
    AUDIT_WAIT(0, "待审核"),
    AUDIT_THROUGH(1, "已审核"),
    AUDIT_REJECT(2, "已驳回"),
    AUDIT_UNDO(3, "已撤销"),
    ;

    private Integer key;
    private String value;

    public static AttendanceChangeAuditStatusEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

}
