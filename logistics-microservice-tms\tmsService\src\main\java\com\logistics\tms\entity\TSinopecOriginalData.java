package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/12/14
*/
@Data
public class TSinopecOriginalData extends BaseEntity {
    /**
    * 集成消息统一编码，基于32位大写GUID表示(43BAC2C6CAB42EC15B05ABC62C79CB8E)
    */
    @ApiModelProperty("集成消息统一编码，基于32位大写GUID表示(43BAC2C6CAB42EC15B05ABC62C79CB8E)")
    private String imGuid;

    /**
    * 发送时间格式：YYYYMMDDHHMMSS(20210401235959)
    */
    @ApiModelProperty("发送时间格式：YYYYMMDDHHMMSS(20210401235959)")
    private String sendTime;

    /**
    * 发送方系统编码(LISBS)
    */
    @ApiModelProperty("发送方系统编码(LISBS)")
    private String sender;

    /**
    * 接收方系统编码(ZEYI)
    */
    @ApiModelProperty("接收方系统编码(ZEYI)")
    private String receiver;

    /**
    * 接收方接口编码(consignOrder)
    */
    @ApiModelProperty("接收方接口编码(consignOrder)")
    private String receiveIfid;

    /**
    * 接收方目标方法/函数名(getConsignOrder)
    */
    @ApiModelProperty("接收方目标方法/函数名(getConsignOrder)")
    private String receiveMethod;

    /**
    * 发送方系统业务操作人账号(非必填)
    */
    @ApiModelProperty("发送方系统业务操作人账号(非必填)")
    private String sendOperator;

    /**
    * 租户Id
    */
    @ApiModelProperty("租户Id")
    private Long customerId;

    /**
    * 租户名称
    */
    @ApiModelProperty("租户名称")
    private String customerName;

    /**
    * 是否网货 0: 否 1: 是
    */
    @ApiModelProperty("是否网货 0: 否 1: 是")
    private Integer onlineGoodsFlag;

    /**
    * 托运人编码
    */
    @ApiModelProperty("托运人编码")
    private String consignorCode;

    /**
    * 托运人名称（货主）
    */
    @ApiModelProperty("托运人名称（货主）")
    private String consignorName;

    /**
    * 物流服务商代码(0041763020)
    */
    @ApiModelProperty("物流服务商代码(0041763020)")
    private String carrierCode;

    /**
    * 承运商名称（车主）
    */
    @ApiModelProperty("承运商名称（车主）")
    private String carrierName;

    /**
    * 委托日期
    */
    @ApiModelProperty("委托日期")
    private Date associatedDate;

    /**
    * 送达期限
    */
    @ApiModelProperty("送达期限")
    private Date arrivalDeadline;

    /**
    * 短驳次数 0: 不短驳
    */
    @ApiModelProperty("短驳次数 0: 不短驳")
    private Integer shortSplitCount;

    /**
    * 运输方式 1: 铁路 2: 公路 3: 管道 4: 水路 5: 航空
    */
    @ApiModelProperty("运输方式 1: 铁路 2: 公路 3: 管道 4: 水路 5: 航空")
    private Integer transportType;

    /**
    * 运输方式备注名称(非必填)
    */
    @ApiModelProperty("运输方式备注名称(非必填)")
    private String transportTypeRemark;

    /**
    * 委托总量（委托数量）
    */
    @ApiModelProperty("委托总量（委托数量）")
    private BigDecimal associatedCount;

    /**
    * 是否包含危化品, 0: 不包含 1: 包含
    */
    @ApiModelProperty("是否包含危化品, 0: 不包含 1: 包含")
    private Integer hasHazardousChemicals;

    /**
    * 委托单备注(非必填)
    */
    @ApiModelProperty("委托单备注(非必填)")
    private String associatedRemark;

    /**
    * 计划包装数(非必填)
    */
    @ApiModelProperty("计划包装数(非必填)")
    private Integer planPackageQuantity;

    /**
    * 预估费用(非必填)
    */
    @ApiModelProperty("预估费用(非必填)")
    private BigDecimal estimateCost;

    /**
    * 审核人姓名
    */
    @ApiModelProperty("审核人姓名")
    private String auditUser;

    /**
    * 0: 否，国内运输; 1: 是，国外运输(非必填)
    */
    @ApiModelProperty("0: 否，国内运输; 1: 是，国外运输(非必填)")
    private Integer isAbroad;

    /**
    * 最早装期(非必填)
    */
    @ApiModelProperty("最早装期(非必填)")
    private Long earliestLoadTime;

    /**
    * 最晚装期(非必填)
    */
    @ApiModelProperty("最晚装期(非必填)")
    private Long latestLoadTime;

    /**
    * 唛头数量(非必填)
    */
    @ApiModelProperty("唛头数量(非必填)")
    private Integer marksQuantity;

    /**
    * 唛头金额(非必填)
    */
    @ApiModelProperty("唛头金额(非必填)")
    private BigDecimal marksAmount;

    /**
    * 打托数量(非必填)
    */
    @ApiModelProperty("打托数量(非必填)")
    private Integer trayQuantity;

    /**
    * 打托金额(非必填)
    */
    @ApiModelProperty("打托金额(非必填)")
    private BigDecimal trayAmount;

    /**
    * 内外贸, 1: 内贸; 2: 外贸
    */
    @ApiModelProperty("内外贸, 1: 内贸; 2: 外贸")
    private Integer tradePositionType;

    /**
    * 贸易类型, 1: 合同贸易 2: 计划贸易
    */
    @ApiModelProperty("贸易类型, 1: 合同贸易 2: 计划贸易")
    private String tradeType;

    /**
    * 交接方式
    */
    @ApiModelProperty("交接方式")
    private String handoverName;

    /**
    * 起运国/地区(非必填)
    */
    @ApiModelProperty("起运国/地区(非必填)")
    private String originCountry;

    /**
    * 起运国/地区编码(非必填)
    */
    @ApiModelProperty("起运国/地区编码(非必填)")
    private String originCountryCode;

    /**
    * 送达国/地区(非必填)
    */
    @ApiModelProperty("送达国/地区(非必填)")
    private String destinationCountry;

    /**
    * 送达国/地区编码(非必填)
    */
    @ApiModelProperty("送达国/地区编码(非必填)")
    private String destinationCountryCode;

    /**
    * 港口编码(非必填)
    */
    @ApiModelProperty("港口编码(非必填)")
    private String port;

    /**
    * 港口名称(非必填)
    */
    @ApiModelProperty("港口名称(非必填)")
    private String portName;

    /**
    * 站编码(非必填)
    */
    @ApiModelProperty("站编码(非必填)")
    private String stringStation;

    /**
    * 站名称(非必填)
    */
    @ApiModelProperty("站名称(非必填)")
    private String stationName;

    /**
    * 省名称(非必填)
    */
    @ApiModelProperty("省名称(非必填)")
    private String provinceName;

    /**
    * 省编码(非必填)
    */
    @ApiModelProperty("省编码(非必填)")
    private String province;

    /**
    * 城市名称(非必填)
    */
    @ApiModelProperty("城市名称(非必填)")
    private String cityName;

    /**
    * 城市编码(非必填)
    */
    @ApiModelProperty("城市编码(非必填)")
    private String city;

    /**
    * 类型(非必填), 1: 地 2: 港 3: 站
    */
    @ApiModelProperty("类型(非必填), 1: 地 2: 港 3: 站")
    private String type;

    /**
    * 货主(华北分公司)
    */
    @ApiModelProperty("货主(华北分公司)")
    private String consignName;

    /**
    * 订单号(2600141565)
    */
    @ApiModelProperty("订单号(2600141565)")
    private String orderNo;

    /**
    * 生产企业(燕山分公司)
    */
    @ApiModelProperty("生产企业(燕山分公司)")
    private String manufacturerName;

    /**
    * 凭证日期(2021-12-02)
    */
    @ApiModelProperty("凭证日期(2021-12-02)")
    private Date expectExeDate;

    /**
    * 运输方式(公路运输)
    */
    @ApiModelProperty("运输方式(公路运输)")
    private String transMode;

    /**
    * 产品大类(合成树脂)
    */
    @ApiModelProperty("产品大类(合成树脂)")
    private String itemCategory1Name;

    /**
    * 产品小类(低密度聚乙烯)
    */
    @ApiModelProperty("产品小类(低密度聚乙烯)")
    private String itemCategoryName;

    /**
    * 产品代码(001201020160098123)
    */
    @ApiModelProperty("产品代码(001201020160098123)")
    private String itemCode;

    /**
    * 产品名称(低密度聚乙烯PEM187)
    */
    @ApiModelProperty("产品名称(低密度聚乙烯PEM187)")
    private String itemName;

    /**
    * 物料运输组(共享托盘)
    */
    @ApiModelProperty("物料运输组(共享托盘)")
    private String itemTransGroupName;

    /**
    * 包装规格(1.25吨/托)
    */
    @ApiModelProperty("包装规格(1.25吨/托)")
    private String itemPackSpecName;

    /**
    * 数量(30)
    */
    @ApiModelProperty("数量(30)")
    private BigDecimal qty;

    /**
    * 计量单位(吨)
    */
    @ApiModelProperty("计量单位(吨)")
    private String uomName;

    /**
    * 委托单号(TC2021101500001A)
    */
    @ApiModelProperty("委托单号(TC2021101500001A)")
    private String sn;

    /**
    * 发货仓库(燕山石化驻厂办)
    */
    @ApiModelProperty("发货仓库(燕山石化驻厂办)")
    private String outWarehouseName;

    /**
    * 发运城市(房山区)
    */
    @ApiModelProperty("发运城市(房山区)")
    private String loadZoneName;

    /**
    * 发运城市编码(110111)
    */
    @ApiModelProperty("发运城市编码(110111)")
    private String loadZoneCode;

    /**
    * 送达城市编码(120101)
    */
    @ApiModelProperty("送达城市编码(120101)")
    private String recvDistrictCode;

    /**
    * 送达城市(和平区)
    */
    @ApiModelProperty("送达城市(和平区)")
    private String recvDistrictName;

    /**
    * 订单有效期(2021-12-31必须提货时间)
    */
    @ApiModelProperty("订单有效期(2021-12-31必须提货时间)")
    private Date invalidatingStartDate;

    /**
    * 备注(目前送达时限，通过备注字段传输)
    */
    @ApiModelProperty("备注(目前送达时限，通过备注字段传输)")
    private String remark;

    /**
    * 备用字段1(非必填)
    */
    @ApiModelProperty("备用字段1(非必填)")
    private String remark1;

    /**
    * 备用字段2(非必填)
    */
    @ApiModelProperty("备用字段2(非必填)")
    private String remark2;

    /**
    * 备用字段3(非必填)
    */
    @ApiModelProperty("备用字段3(非必填)")
    private String remark3;

    /**
    * 备用字段4(非必填)
    */
    @ApiModelProperty("备用字段4(非必填)")
    private String remark4;

    /**
    * 备用字段5(非必填)
    */
    @ApiModelProperty("备用字段5(非必填)")
    private String remark5;

    /**
    * 备用字段6(非必填)
    */
    @ApiModelProperty("备用字段6(非必填)")
    private String remark6;

    /**
    * 备用字段7(非必填)
    */
    @ApiModelProperty("备用字段7(非必填)")
    private String remark7;

    /**
    * 备用字段8(非必填)
    */
    @ApiModelProperty("备用字段8(非必填)")
    private String remark8;

    /**
    * 备用字段9(非必填)
    */
    @ApiModelProperty("备用字段9(非必填)")
    private String remark9;

    /**
    * 备用字段10(非必填)
    */
    @ApiModelProperty("备用字段10(非必填)")
    private String remark10;

    /**
    * 是否生成需求单：0 否，1 是
    */
    @ApiModelProperty("是否生成需求单：0 否，1 是")
    private Integer ifHasDemandOrder;
}