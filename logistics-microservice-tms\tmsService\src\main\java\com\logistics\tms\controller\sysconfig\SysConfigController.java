package com.logistics.tms.controller.sysconfig;

import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.controller.sysconfig.request.SysConfigEditRequestModel;
import com.logistics.tms.controller.sysconfig.request.SysConfigRequestModel;
import com.logistics.tms.controller.sysconfig.response.SysConfigResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/7 17:13
 */
@RestController
@Api(value = "系统配置管理",tags = "系统配置管理")
@RequestMapping(value = "/service/sysConfig")
public class SysConfigController {

    @Resource
    private SysConfigBiz sysConfigBiz;

    /**
     * 获取系统配置
     * @param requestModel 请求Model
     * @return 配置 Value
     */
    @ApiOperation(value = "获取系统配置", tags = "1.2.6")
    @PostMapping(value = "/get")
    public Result<String> getSysConfig(@RequestBody SysConfigRequestModel requestModel) {
        String configValue = sysConfigBiz.getSysConfig(requestModel.getGroupCode(), requestModel.getConfigKey())
                .orElse("");
        return Result.success(configValue);
    }

    /**
     * 批量获取配置
     * @param requestModel 请求Model
     * @return 配置列表
     */
    @ApiOperation(value = "批量获取系统配置", tags = "1.2.6")
    @PostMapping(value = "/batchGet")
    public Result<List<SysConfigResponseModel>> batchGetSysConfig(@RequestBody Collection<SysConfigRequestModel> requestModel) {
        return Result.success(sysConfigBiz.batchGetSysConfig(requestModel));
    }

    /**
     * 修改系统配置
     * @param requestModel 请求 Model
     * @return true 成功 false 失败
     */
    @ApiOperation(value = "系统配置编辑", tags = "1.2.6")
    @PostMapping(value = "/edit")
    public Result<Boolean> editSysConfig(@RequestBody SysConfigEditRequestModel requestModel) {
        sysConfigBiz.editSysConfigValue(requestModel.getGroupCode(), requestModel.getConfigKey(), requestModel.getConfigValue());
        return Result.success(true);
    }
}
