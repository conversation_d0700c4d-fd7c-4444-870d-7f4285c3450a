package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2023/09/06
*/
@Data
public class TDemandOrderCustomerRel extends BaseEntity {
    /**
    * 需求单id
    */
    @ApiModelProperty("需求单id")
    private Long demandOrderId;

    /**
    * 云盘客户id
    */
    @ApiModelProperty("云盘客户id")
    private Long trayCustomerCompanyId;

    /**
    * 云盘地址id
    */
    @ApiModelProperty("云盘地址id")
    private Long trayCustomerAddressId;

    /**
    * 产品大类ID
    */
    @ApiModelProperty("产品大类ID")
    private Long productCategoryId;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}