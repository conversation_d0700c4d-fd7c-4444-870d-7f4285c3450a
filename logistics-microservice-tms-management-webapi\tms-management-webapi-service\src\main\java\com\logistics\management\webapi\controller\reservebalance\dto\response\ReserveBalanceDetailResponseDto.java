package com.logistics.management.webapi.controller.reservebalance.dto.response;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ReserveBalanceDetailResponseDto {

	@ApiModelProperty(value = "申请月份")
	@ExcelProperty(value = "月份")
	private String applyMonth = "";

	@ApiModelProperty(value = "充值金额")
	@ExcelProperty(value = "充值（元）")
	private String applyAmount = "";

	@ApiModelProperty(value = "已冲销金额")
	@ExcelProperty(value = "已冲销（元）")
	private String verificationAmount = "";
}
