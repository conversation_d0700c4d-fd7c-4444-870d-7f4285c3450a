package com.logistics.management.webapi.controller.reservebalance.mapping;

import com.logistics.management.webapi.controller.reservebalance.dto.response.ReserveBalanceDetailResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.reservebalance.model.response.ReserveBalanceDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;

public class ReserveBalanceDetailMapping extends MapperMapping<ReserveBalanceDetailResponseModel, ReserveBalanceDetailResponseDto> {

    @Override
    public void configure() {

        ReserveBalanceDetailResponseModel source = getSource();
        ReserveBalanceDetailResponseDto destination = getDestination();

        // 金额格式化
        String applyAmount = CommonConstant.PLUS_SYMBOL + amountConversion(source.getApplyAmount());
        destination.setApplyAmount(applyAmount);
        String verificationAmount = CommonConstant.MINUS_SYMBOL + amountConversion(source.getVerificationAmount());
        destination.setVerificationAmount(verificationAmount);
    }

    private String amountConversion(BigDecimal amount) {
        return amount.stripTrailingZeros().toPlainString();
    }
}
