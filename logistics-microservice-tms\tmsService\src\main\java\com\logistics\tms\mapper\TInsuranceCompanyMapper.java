package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.insurancecompany.model.FuzzyQueryInsuranceCompanyListResponseModel;
import com.logistics.tms.api.feign.insurancecompany.model.FuzzyQueryInsuranceCompanyRequestModel;
import com.logistics.tms.api.feign.insurancecompany.model.InsuranceCompanyListRequestModel;
import com.logistics.tms.api.feign.insurancecompany.model.InsuranceCompanyListResponseModel;
import com.logistics.tms.entity.TInsuranceCompany;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TInsuranceCompanyMapper extends BaseMapper<TInsuranceCompany>{

    List<InsuranceCompanyListResponseModel> searchInsuranceCompanyList(@Param("params") InsuranceCompanyListRequestModel requestModel);

    TInsuranceCompany findInsuranceCompanyByName(@Param("insuranceCompanyName") String insuranceCompanyName);

    int batchInsert(@Param("list") List<TInsuranceCompany> insuranceCompanyModels);

    int batchUpdate(@Param("list") List<TInsuranceCompany> insuranceCompanyModels);

    List<FuzzyQueryInsuranceCompanyListResponseModel> queryInsuranceCompanyByName(@Param("params") FuzzyQueryInsuranceCompanyRequestModel requestModel);
}