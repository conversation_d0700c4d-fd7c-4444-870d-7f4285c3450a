<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TReserveBalanceRunningRecordMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TReserveBalanceRunningRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="reserve_balance_id" jdbcType="BIGINT" property="reserveBalanceId" />
    <result column="running_date" jdbcType="TIMESTAMP" property="runningDate" />
    <result column="running_type" jdbcType="INTEGER" property="runningType" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, reserve_balance_id, running_date, running_type, amount, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_reserve_balance_running_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_reserve_balance_running_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TReserveBalanceRunningRecord">
    insert into t_reserve_balance_running_record (id, reserve_balance_id, running_date, 
      running_type, amount, created_by, 
      created_time, last_modified_by, last_modified_time, 
      valid)
    values (#{id,jdbcType=BIGINT}, #{reserveBalanceId,jdbcType=BIGINT}, #{runningDate,jdbcType=TIMESTAMP}, 
      #{runningType,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TReserveBalanceRunningRecord">
    insert into t_reserve_balance_running_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reserveBalanceId != null">
        reserve_balance_id,
      </if>
      <if test="runningDate != null">
        running_date,
      </if>
      <if test="runningType != null">
        running_type,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reserveBalanceId != null">
        #{reserveBalanceId,jdbcType=BIGINT},
      </if>
      <if test="runningDate != null">
        #{runningDate,jdbcType=TIMESTAMP},
      </if>
      <if test="runningType != null">
        #{runningType,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TReserveBalanceRunningRecord">
    update t_reserve_balance_running_record
    <set>
      <if test="reserveBalanceId != null">
        reserve_balance_id = #{reserveBalanceId,jdbcType=BIGINT},
      </if>
      <if test="runningDate != null">
        running_date = #{runningDate,jdbcType=TIMESTAMP},
      </if>
      <if test="runningType != null">
        running_type = #{runningType,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TReserveBalanceRunningRecord">
    update t_reserve_balance_running_record
    set reserve_balance_id = #{reserveBalanceId,jdbcType=BIGINT},
      running_date = #{runningDate,jdbcType=TIMESTAMP},
      running_type = #{runningType,jdbcType=INTEGER},
      amount = #{amount,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>