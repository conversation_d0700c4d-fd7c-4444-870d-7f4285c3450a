package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GetLoadDetailForYeliLifeCodeDto {

    @ApiModelProperty(value = "code", required = true)
    private String yeloCode;

    @ApiModelProperty(value = "装货数量/实提数量", required = true)
    private String loadAmount;

    @ApiModelProperty(value = "卸货数量/实卸数量", required = true)
    private String unloadAmount;

    @ApiModelProperty(value = "单位 1.kg", required = true)
    private String unit = "";
}
