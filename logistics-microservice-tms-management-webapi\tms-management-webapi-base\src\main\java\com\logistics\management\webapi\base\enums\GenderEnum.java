package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/6/5 20:26
 */
public enum GenderEnum {
    DEFAULT(0,"","0"),
    MAN(1,"男","1"),
    WOMAN(2,"女","2"),
    ;

    private Integer key;
    private String value;
    private String keyStr;

    GenderEnum(Integer key, String value, String keyStr) {
        this.key = key;
        this.value = value;
        this.keyStr = keyStr;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getKeyStr() {
        return keyStr;
    }

    public static GenderEnum getEnum(Integer key) {
        for (GenderEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
