package com.logistics.management.webapi.api.feign.contractorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同操作日志
 * @Author: sj
 * @Date: 2019/4/4 17:22
 */
@Data
public class ContractLogsResponseDto {
    @ApiModelProperty("日志id")
    private String operateLogsId = "";
    @ApiModelProperty("操作人")
    private String operateUserName = "";
    @ApiModelProperty("操作时间")
    private String operateTime = "";
    @ApiModelProperty("操作类型")
    private String operateType = "";
    @ApiModelProperty("说明")
    private String operateContents = "";
    @ApiModelProperty("备注")
    private String remark = "";
}
