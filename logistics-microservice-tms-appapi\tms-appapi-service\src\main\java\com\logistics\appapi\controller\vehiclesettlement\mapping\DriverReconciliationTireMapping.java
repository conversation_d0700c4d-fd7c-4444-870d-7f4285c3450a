package com.logistics.appapi.controller.vehiclesettlement.mapping;

import com.logistics.appapi.client.vehiclesettlement.response.ReconciliationTireDetailResponseModel;
import com.logistics.appapi.controller.vehiclesettlement.response.ReconciliationTireDetailResponseDto;
import com.logistics.appapi.controller.vehiclesettlement.response.ReconciliationTireListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

/**
 * @author：wjf
 * @date：2021/4/25 11:00
 */
public class DriverReconciliationTireMapping extends MapperMapping<ReconciliationTireDetailResponseModel,ReconciliationTireDetailResponseDto> {
    @Override
    public void configure() {
        ReconciliationTireDetailResponseDto destination = getDestination();

        if (ListUtils.isNotEmpty(destination.getVehicleTireList())){
            for (ReconciliationTireListResponseDto dto : destination.getVehicleTireList()) {
                if (StringUtils.isNotBlank(dto.getReplaceDate())){
                    dto.setReplaceDate(DateUtils.dateToString(DateUtils.stringToDate(dto.getReplaceDate(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
                }
            }
        }
    }
}
