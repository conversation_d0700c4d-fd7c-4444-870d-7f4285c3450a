package com.logistics.management.webapi.controller.biddingorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BiddingOrderDetailResponseDto {
    /**
     * 竞价单id
     */
    @ApiModelProperty("竞价单id")
    private String biddingOrderId="";

    /**
     * 竞价单号
     */
    @ApiModelProperty("竞价单号")
    private String biddingOrderCode="";

    /**
     * 竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消
     */
    @ApiModelProperty("竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消")
    private String biddingStatus="";

    /**
     * 竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消
     */
    @ApiModelProperty("竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消")
    private String biddingStatusLabel="";

    /**
     * 装卸方式 1一装一卸、2多装一卸
     */
    @ApiModelProperty("装卸方式 1一装一卸、2多装一卸")
    private String handlingMode="";

    /**
     * 装卸方式 1一装一卸、2多装一卸
     */
    @ApiModelProperty("装卸方式 1一装一卸、2多装一卸")
    private String handlingModeLabel="";

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdBy="";

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String createdTime="";

    /**
     * 倒计时结束时间
     */
    @ApiModelProperty("倒计时结束时间")
    private String countDownTime="";

    /**
     * 车长
     */
    @ApiModelProperty("车长")
    private String vehicleLength = "";

    /**
     * 期望提货时间
     */
    @ApiModelProperty(value = "期望提货时间")
    private String expectedLoadTime = "";

    /**
     * 期望卸货时间
     */
    @ApiModelProperty("期望卸货时间")
    private String expectedUnloadTime="";

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark="";

    /**
     * 历史最低价
     */
    @ApiModelProperty("历史最低价")
    private String lowestPrice="";

    /**
     * 需求单列表
     */
    @ApiModelProperty("需求单列表")
    private List<BiddingOrderDetailDemandDto> demandDtoList = new ArrayList<>();

    /**
     * 车主报价记录
     */
    private List<BiddingOrderQuoteListResponseDto> biddingOrderQuoteList=new ArrayList<>();

    /**
     * 操作记录
     */
    private List<OperateLogListResponseDto> operateLogList=new ArrayList<>();
}
