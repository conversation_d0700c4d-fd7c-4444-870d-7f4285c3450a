package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/12/06
*/
@Data
public class TCompanyAccount extends BaseEntity {
    /**
    * 银行账号（原长度50）
    */
    @ApiModelProperty("银行账号（原长度50）")
    private String bankAccount;

    /**
    * 开户银行名称
    */
    @ApiModelProperty("开户银行名称")
    private String bankAccountName;

    /**
    * 开户支行名称
    */
    @ApiModelProperty("开户支行名称")
    private String braBankName;

    /**
    * 银行编号
    */
    @ApiModelProperty("银行编号")
    private String bankCode;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 启用 1 禁用 0
    */
    @ApiModelProperty("启用 1 禁用 0")
    private Integer enabled;
}