package com.logistics.tms.controller.baiscinfo.web.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CarrierBasicInfoResponseModel {

    @ApiModelProperty("车主公司ID")
    private Long companyId;

    @ApiModelProperty("车主名")
    private String companyCarrierName;

    @ApiModelProperty("车主公司类型, 1:企业 2:个人")
    private Integer companyCarrierType;

    @ApiModelProperty("车主联系人姓名")
    private String carrierContactName;

    @ApiModelProperty("车主联系人手机号")
    private String carrierContactPhone;

    @ApiModelProperty("车主联系人身份证号")
    private String carrierContactIdentityNumber;

    @ApiModelProperty("实名认证(个人): 0 待实名 1 实名中 2 已实名")
    private Integer realNameAuthenticationStatus;

    @ApiModelProperty("授权状态, 0 待授权 1 待审核 2已驳回 3 已授权")
    private Integer authorizationStatus;

    @ApiModelProperty("驳回原因")
    private String remark;

    @ApiModelProperty("实名认证方式")
    private Integer authMode;

    @ApiModelProperty("车主创建时间")
    private Date companyCarrierCreateTime;

    @ApiModelProperty("授权书邮寄地址")
    private String mailingAddress;
}
