package com.logistics.tms.api.feign.companycarrierauthorization;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.companycarrierauthorization.hystrix.CompanyCarrierAuthorizationServiceApiHystrix;
import com.logistics.tms.api.feign.companycarrierauthorization.model.request.*;
import com.logistics.tms.api.feign.companycarrierauthorization.model.response.CarrierAuthorizationDetailResponseModel;
import com.logistics.tms.api.feign.companycarrierauthorization.model.response.CarrierAuthorizationListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "API-CompanyCarrierAuthorizationServiceApi", tags = "车主授权管理")
@FeignClient(name = "logistics-tms-services", fallback = CompanyCarrierAuthorizationServiceApiHystrix.class)
public interface CompanyCarrierAuthorizationServiceApi {

    @ApiOperation(value = "查询车主授权列表", tags = "1.2.6")
    @PostMapping(value = "/service/companyCarrierAuthorization/carrierAuthorizationList")
    Result<PageInfo<CarrierAuthorizationListResponseModel>> carrierAuthorizationList(@RequestBody CarrierAuthorizationListRequestModel requestModel);

    @ApiOperation(value = "车主授权详情", tags = "1.2.6")
    @PostMapping(value = "/service/companyCarrierAuthorization/carrierAuthorizationDetail")
    Result<CarrierAuthorizationDetailResponseModel> carrierAuthorizationDetail(@RequestBody CarrierAuthorizationDetailRequestModel requestModel);

    @ApiOperation(value = "新增车主授权信息", tags = "1.2.6")
    @PostMapping(value = "/service/companyCarrierAuthorization/carrierAuthorizationAdd")
    Result<Boolean> carrierAuthorizationAdd(@RequestBody CarrierAuthorizationAddRequestModel requestModel);

    @ApiOperation(value = "审核车主授权信息", tags = "1.2.6")
    @PostMapping(value = "/service/companyCarrierAuthorization/carrierAuthorizationAudit")
    Result<Boolean> carrierAuthorizationAudit(@RequestBody CarrierAuthorizationAuditRequestModel requestModel);

    @ApiOperation(value = "车主授权信息归档", tags = "1.2.6")
    @PostMapping(value = "/service/companyCarrierAuthorization/carrierAuthorizationArchived")
    Result<Boolean> carrierAuthorizationArchived(@RequestBody CarrierAuthorizationArchivedRequestModel requestModel);

    @ApiOperation(value = "车主授权信息重新归档", tags = "1.3.6")
    @PostMapping(value = "/service/companyCarrierAuthorization/carrierAuthorizationReArchive")
    Result<Boolean> carrierAuthorizationReArchive(@RequestBody CarrierAuthorizationReArchivedRequestModel requestModel);
}
