package com.logistics.tms.api.feign.freight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DriverFreightByDemandOrderIdsAndVehicleRequestModel {
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("装车数")
    private Integer loadCount;
    @ApiModelProperty("卸车数")
    private Integer unloadCount;
    @ApiModelProperty("费用类型 1 单价 2 一口价")
    private Integer freightType;
    @ApiModelProperty("需求单与预提货物")
    private List<DemandOrderIdAndExpectAmountRequestModel> demandOrderList;
}
