package com.logistics.tms.api.feign.parkingfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/10/9 15:40
 */
@Data
public class ParkingFeeOperationRecordResponsesModel {
    @ApiModelProperty("停车费用记录表ID")
    private Long parkingFeeOperationId;
    @ApiModelProperty("停车费用表ID")
    private Long parkingFeeId;
    @ApiModelProperty("车辆ID")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("合作公司")
    private String cooperationCompany;
    @ApiModelProperty("起始日期")
    private Date startDate;
    @ApiModelProperty("截止时间")
    private Date endDate;
    @ApiModelProperty("终止时间")
    private Date finishDate;
    @ApiModelProperty("停车费")
    private BigDecimal parkingFee;
    @ApiModelProperty("合作周期（月）")
    private Integer cooperationPeriod;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("创建人")
    private String createdBy;
    @ApiModelProperty("创建时间")
    private Date createdTime;
    @ApiModelProperty("最后修改人")
    private String lastModifiedBy;
    @ApiModelProperty("最后修改时间")
    private Date lastModifiedTime;
}
