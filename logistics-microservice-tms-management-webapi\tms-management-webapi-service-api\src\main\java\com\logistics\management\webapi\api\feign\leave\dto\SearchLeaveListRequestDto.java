package com.logistics.management.webapi.api.feign.leave.dto;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 请假记录列表查询请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
public class SearchLeaveListRequestDto extends AbstractPageForm<SearchLeaveListRequestDto> {

	//申请人
	@ApiModelProperty("申请人,姓名或手机号模糊搜索")
	private String leaveApplyUser;

	//审核状态
	@ApiModelProperty("请假申请审核状态,审核状态: 0 待审核，1 已审核，2 已驳回，3 已撤销")
	private String leaveAuditStatus;

	//人员机构
	@ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
	private String staffProperty;

	//请假申请开始时间
	@ApiModelProperty("请假申请开始时间")
	private String leaveApplyStartTime;

	//请假申请结束时间
	@ApiModelProperty("请假申请结束时间")
	private String leaveApplyEndTime;
}
