<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TVehicleDrivingLicenseMapper" >
  <select id="getByVehicleNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_vehicle_driving_license
    where valid = 1
    and vehicle_no = #{vehicleNo,jdbcType=VARCHAR}
  </select>

  <select id="getByVehicleId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_vehicle_driving_license
    where valid = 1
    and vehicle_id = #{vehicleId,jdbcType=BIGINT} LIMIT 1
  </select>

  <select id="getListByVehicleIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_vehicle_driving_license
    where valid = 1
    and vehicle_id in (${vehicleIds})
  </select>
  
  <update id="deleteDrivingLicenseInfo">
update t_vehicle_driving_license t1
left join t_vehicle_driving_license_annual_review t2 on t1.id = t2.driving_license_id and t2.valid = 1
left join t_certification_pictures t3 on ((t3.object_type = 13 and t3.object_id = t1.id) or (t3.object_type = 7 and t3.object_id = t2.id)) and t3.valid = 1
set t1.valid = 0,t2.valid = 0,t3.valid = 0,
t1.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},t1.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP},
t2.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},t2.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP},
t3.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},t3.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP}
where t1.valid = 1 and t1.vehicle_id in (${vehicleIds});
  </update>

  <select id="getDueObsolescenceCount" resultType="java.util.HashMap">
    select ifnull(group_concat(if(thrityDueCount = 1, vehicle_id, null)), '') as totalIds,
           ifnull(group_concat(if(sevenDueCount = 1, vehicle_id, null)), '')  as weekIds,
           ifnull(group_concat(if(dueCount = 1, vehicle_id, null)), '')       as hasExpiredIds,
           ifnull(sum(thrityDueCount), 0)                                     as total,
           ifnull(sum(sevenDueCount), 0)                                      as week,
           ifnull(sum(dueCount), 0)                                           as hasExpired
    from (SELECT tcvr.id as  vehicle_id,
                 if(DATE_SUB(max(obsolescence_date), INTERVAL #{remindDays,jdbcType=INTEGER} DAY) &lt;= DATE_FORMAT(now(), '%Y-%m-%d') and max(obsolescence_date) >= DATE_FORMAT(now(), '%Y-%m-%d'), 1,
                    0)                                                                                                                                                        as thrityDueCount,
                 if(DATE_SUB(max(obsolescence_date), INTERVAL 7 DAY) &lt;= DATE_FORMAT(now(), '%Y-%m-%d') and max(obsolescence_date) >= DATE_FORMAT(now(), '%Y-%m-%d'), 1, 0) as sevenDueCount,
                 if(max(obsolescence_date) &lt; DATE_FORMAT(now(), '%Y-%m-%d'), 1, 0)                                                                                         as dueCount
          from t_vehicle_driving_license t1
                 left join t_vehicle_basic t2 on t2.id = t1.vehicle_id and t2.valid = 1
                 left join t_carrier_vehicle_relation tcvr on tcvr.valid = 1 and tcvr.vehicle_id = t2.id and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
          where t1.valid = 1
            and t2.operating_state = 1
            and tcvr.id is not null
          group by vehicle_id) tmp
  </select>

  <update id="updateByPrimaryKeySelectiveExt" parameterType="com.logistics.tms.entity.TVehicleDrivingLicense">
    update t_vehicle_driving_license
    <set>
      <if test="params.vehicleId != null">
        vehicle_id = #{params.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="params.vehicleNo != null">
        vehicle_no = #{params.vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="params.vehicleType != null">
        vehicle_type = #{params.vehicleType,jdbcType=BIGINT},
      </if>
        address = #{params.address,jdbcType=VARCHAR},
        owner = #{params.owner,jdbcType=VARCHAR},
        brand = #{params.brand,jdbcType=VARCHAR},
        model = #{params.model,jdbcType=VARCHAR},
        vehicle_identification_number = #{params.vehicleIdentificationNumber,jdbcType=VARCHAR},
        engine_number = #{params.engineNumber,jdbcType=VARCHAR},
        certification_department = #{params.certificationDepartment,jdbcType=VARCHAR},
        registration_date = #{params.registrationDate,jdbcType=TIMESTAMP},
        issue_date = #{params.issueDate,jdbcType=TIMESTAMP},
        filing_number = #{params.filingNumber,jdbcType=VARCHAR},
        authorized_carrying_capacity = #{params.authorizedCarryingCapacity,jdbcType=INTEGER},
        total_weight = #{params.totalWeight,jdbcType=DECIMAL},
        curb_weight = #{params.curbWeight,jdbcType=DECIMAL},
        traction_mass_weight = #{params.tractionMassWeight,jdbcType=DECIMAL},
        approved_load_weight = #{params.approvedLoadWeight,jdbcType=DECIMAL},
        length = #{params.length,jdbcType=INTEGER},
        width = #{params.width,jdbcType=INTEGER},
        height = #{params.height,jdbcType=INTEGER},
        obsolescence_date = #{params.obsolescenceDate,jdbcType=TIMESTAMP},
        axle_number = #{params.axleNumber,jdbcType=INTEGER},
        drive_shaft_number = #{params.driveShaftNumber,jdbcType=INTEGER},
        tires_number = #{params.tiresNumber,jdbcType=INTEGER},
        plate_color = #{params.plateColor,jdbcType=INTEGER},
        body_color = #{params.bodyColor,jdbcType=VARCHAR},
      <if test="params.createdBy!=null">
        created_by = #{params.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="params.createdTime!=null">
        created_time = #{params.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="params.lastModifiedBy != null">
        last_modified_by = #{params.lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="params.lastModifiedTime != null">
        last_modified_time = #{params.lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="params.valid != null">
        valid = #{params.valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{params.id,jdbcType=BIGINT}
  </update>


  <update id="updateByPrimaryKeySelectiveForExcel" parameterType="com.logistics.tms.entity.TVehicleDrivingLicense">
    update t_vehicle_driving_license
    <set>
      <if test="params.vehicleId != null">
        vehicle_id = #{params.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="params.vehicleNo != null and params.vehicleNo != ''">
        vehicle_no = #{params.vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="params.vehicleType != null">
        vehicle_type = #{params.vehicleType,jdbcType=BIGINT},
      </if>
      <if test="params.address != null and params.address !=''">
        address = #{params.address,jdbcType=VARCHAR},
      </if>
      <if test="params.owner != null and params.owner != ''">
        owner = #{params.owner,jdbcType=VARCHAR},
      </if>
      <if test="params.brand != null and params.brand != ''">
        brand = #{params.brand,jdbcType=VARCHAR},
      </if>
      <if test="params.model != null and params.model != ''">
        model = #{params.model,jdbcType=VARCHAR},
      </if>
      <if test="params.vehicleIdentificationNumber != null and params.vehicleIdentificationNumber != ''">
        vehicle_identification_number = #{params.vehicleIdentificationNumber,jdbcType=VARCHAR},
      </if>
      <if test="params.engineNumber != null and params.engineNumber != ''">
        engine_number = #{params.engineNumber,jdbcType=VARCHAR},
      </if>
      <if test="params.certificationDepartment != null and params.certificationDepartment != ''">
        certification_department = #{params.certificationDepartment,jdbcType=VARCHAR},
      </if>
      <if test="params.registrationDate != null">
        registration_date = #{params.registrationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="params.issueDate != null">
        issue_date = #{params.issueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="params.filingNumber != null and params.filingNumber != ''">
        filing_number = #{params.filingNumber,jdbcType=VARCHAR},
      </if>
      <if test="params.authorizedCarryingCapacity != null">
        authorized_carrying_capacity = #{params.authorizedCarryingCapacity,jdbcType=INTEGER},
      </if>
      <if test="params.totalWeight != null">
        total_weight = #{params.totalWeight,jdbcType=DECIMAL},
      </if>
      <if test="params.curbWeight != null">
        curb_weight = #{params.curbWeight,jdbcType=DECIMAL},
      </if>
      <if test="params.tractionMassWeight != null">
        traction_mass_weight = #{params.tractionMassWeight,jdbcType=DECIMAL},
      </if>
      <if test="params.approvedLoadWeight != null">
        approved_load_weight = #{params.approvedLoadWeight,jdbcType=DECIMAL},
      </if>
      <if test="params.length != null">
        length = #{params.length,jdbcType=INTEGER},
      </if>
      <if test="params.width != null">
        width = #{params.width,jdbcType=INTEGER},
      </if>
      <if test="params.height != null">
        height = #{params.height,jdbcType=INTEGER},
      </if>
      <if test="params.obsolescenceDate != null">
        obsolescence_date = #{params.obsolescenceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="params.axleNumber != null">
        axle_number = #{params.axleNumber,jdbcType=INTEGER},
      </if>
      <if test="params.driveShaftNumber != null">
        drive_shaft_number = #{params.driveShaftNumber,jdbcType=INTEGER},
      </if>
      <if test="params.tiresNumber != null">
        tires_number = #{params.tiresNumber,jdbcType=INTEGER},
      </if>
      <if test="params.plateColor != null">
        plate_color = #{params.plateColor,jdbcType=INTEGER},
      </if>
      <if test="params.bodyColor != null and params.bodyColor != ''">
        body_color = #{params.bodyColor,jdbcType=VARCHAR},
      </if>
      <if test="params.createdBy != null">
        created_by = #{params.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="params.createdTime != null">
        created_time = #{params.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="params.lastModifiedBy != null">
        last_modified_by = #{params.lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="params.lastModifiedTime != null">
        last_modified_time = #{params.lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="params.valid != null">
        valid = #{params.valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{params.id,jdbcType=BIGINT}
  </update>
  <select id="getVehicleByVechileNo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from t_vehicle_driving_license
    where valid = 1 and vehicle_no = #{vehicleNo,jdbcType=VARCHAR} LIMIT 1
  </select>

  <resultMap id="getGpsInfoByVehicleNo_Map" type="com.logistics.tms.controller.vehicleassetmanagement.response.GetGpsInfoByVehicleNoResponseModel">
    <result column="vehicleId" property="vehicleId" jdbcType="BIGINT"/>
    <result column="vehicleNo" property="vehicleNo" jdbcType="VARCHAR"/>
    <result column="staffId" property="staffId" jdbcType="BIGINT"/>
    <result column="driverName" property="driverName" jdbcType="VARCHAR"/>
    <result column="driverPhone" property="driverPhone" jdbcType="VARCHAR"/>
    <collection property="gpsList" ofType="com.logistics.tms.controller.vehicleassetmanagement.response.GetGpsInfoByVehicleNoModel">
      <result column="installTime" property="installTime" jdbcType="TIMESTAMP"/>
      <result column="terminalType" property="terminalType" jdbcType="VARCHAR"/>
      <result column="simNumber" property="simNumber" jdbcType="VARCHAR"/>
      <result column="gpsServiceProvider" property="gpsServiceProvider" jdbcType="VARCHAR"/>
    </collection>
  </resultMap>
  <select id="getGpsInfoByVehicleNo" resultMap="getGpsInfoByVehicleNo_Map">
    select
    tvdl.vehicle_id as vehicleId,
    tvdl.vehicle_no as vehicleNo,
    tsb.id as staffId,
    tsb.name as driverName,
    tsb.mobile as driverPhone,
    tvgr.install_time as installTime,
    tvgr.terminal_type as terminalType,
    tvgr.sim_number as simNumber,
    tvgr.gps_service_provider as gpsServiceProvider
    from t_vehicle_driving_license tvdl
    left join t_vehicle_basic tvb on tvb.id = tvdl.vehicle_id and tvb.valid = 1
    left join t_vehicle_type tvt on tvt.id = tvdl.vehicle_type and tvt.valid = 1
    left join t_staff_vehicle_relation tsvr on tsvr.vehicle_id = tvdl.vehicle_id and tsvr.valid = 1
    left join t_staff_basic tsb on tsb.id = tsvr.staff_id and tsb.valid = 1
    left join t_vehicle_gps_record tvgr on tvgr.vehicle_id = tvdl.vehicle_id and tvgr.valid = 1
    where tvdl.valid = 1
    and tvb.vehicle_property in (1,3)
    and tvb.operating_state = 1
    and tvt.vehicle_category in (1,3)
    and instr(tvdl.vehicle_no,#{vehicleNo,jdbcType=VARCHAR})
    order by tvdl.id desc,tvgr.install_time desc,tvgr.id desc
  </select>
</mapper>