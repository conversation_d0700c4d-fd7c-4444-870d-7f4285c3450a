package com.logistics.tms.controller.vehiclesettlement.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author:lei.zhu
 * @date:2021/4/13 11:08
 */
@Data
public class SettlementStatementHandleModel {
    private Long vehicleSettlementId;
    private Integer vehicleSettlementStatus;
    private Long vehicleSettlementDriverRelationId;
    private Integer vehicleSettlementDriverRelationStatus;
    private BigDecimal actualExpensesPayable;
    private String settlementMonth;
    private BigDecimal adjustFee;
    private Long vehicleId;
}
