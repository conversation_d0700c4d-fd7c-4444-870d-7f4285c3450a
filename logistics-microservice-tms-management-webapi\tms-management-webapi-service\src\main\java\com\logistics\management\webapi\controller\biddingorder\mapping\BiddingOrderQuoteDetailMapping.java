package com.logistics.management.webapi.controller.biddingorder.mapping;

import com.logistics.management.webapi.client.biddingorder.response.BiddingOrderQuoteDetailResponseModel;
import com.logistics.management.webapi.controller.biddingorder.response.BiddingOrderQuoteDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/05/11
 */
public class BiddingOrderQuoteDetailMapping extends MapperMapping<BiddingOrderQuoteDetailResponseModel, BiddingOrderQuoteDetailResponseDto> {
    @Override
    public void configure() {
        BiddingOrderQuoteDetailResponseModel source = getSource();
        BiddingOrderQuoteDetailResponseDto destination = getDestination();
        if (source != null) {
            if (source.getQuotePrice() != null) {
                destination.setQuotePrice(source.getQuotePrice().stripTrailingZeros().toPlainString());
            }
            if (source.getUnitPrice() != null) {
                destination.setUnitPrice(source.getUnitPrice().stripTrailingZeros().toPlainString());
            }
            if (ListUtils.isNotEmpty(source.getDemandDtoList())){
                destination.getDemandDtoList().forEach(e->{
                    e.setGoodsCount(new BigDecimal(e.getGoodsCount()).stripTrailingZeros().toPlainString());
                    e.setOnePrice(new BigDecimal(e.getOnePrice()).stripTrailingZeros().toPlainString());
                    e.setUnitPrice(new BigDecimal(e.getUnitPrice()).stripTrailingZeros().toPlainString());
                });
            }
        }

    }
}
