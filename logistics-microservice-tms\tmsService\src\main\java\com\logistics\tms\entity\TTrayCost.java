package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TTrayCost extends BaseEntity {
    /**
    * 委托类型：1 发货，2 回收，3 采购，4 调拨
    */
    @ApiModelProperty("委托类型：1 发货，2 回收，3 采购，4 调拨")
    private Integer entrustType;

    /**
    * 货物单位：1 件，2 吨，3 件（方），4 块
    */
    @ApiModelProperty("货物单位：1 件，2 吨，3 件（方），4 块")
    private Integer goodsUnit;

    /**
    * 单价
    */
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    /**
    * 生效时间
    */
    @ApiModelProperty("生效时间")
    private Date startTime;

    /**
    * 失效时间
    */
    @ApiModelProperty("失效时间")
    private Date endTime;
}