package com.logistics.management.webapi.client.freightconfig;

import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.freightconfig.hystrix.CarrierFreightConfigHystrix;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigAddRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigEditRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigListRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.CarrierFreightConfigRequestModel;
import com.logistics.management.webapi.client.freightconfig.response.CarrierFreightConfigDetailResponseModel;
import com.logistics.management.webapi.client.freightconfig.response.CarrierFreightConfigListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = CarrierFreightConfigHystrix.class)
public interface CarrierFreightConfigClient {

    @PostMapping(value = "/service/freight/config/searchList")
    @ApiOperation(value = "车主运价配置管理列表", tags = "1.3.5")
    Result<List<CarrierFreightConfigListResponseModel>> searchList(@Valid @RequestBody CarrierFreightConfigListRequestModel requestModel);

    @PostMapping(value = "/service/freight/config/detail")
    @ApiOperation(value = "车主运价配置查看", tags = "1.3.5")
    Result<CarrierFreightConfigDetailResponseModel> detail(@Valid @RequestBody CarrierFreightConfigRequestModel requestModel);

    @PostMapping(value = "/service/freight/config/add")
    @ApiOperation(value = "新增车主运价配置", tags = "1.3.5")
    Result<Boolean> add(@Valid @RequestBody CarrierFreightConfigAddRequestModel requestModel);

    @PostMapping(value = "/service/freight/config/edit")
    @ApiOperation(value = "编辑车主运价配置", tags = "1.3.5")
    Result<Boolean> edit(@Valid @RequestBody CarrierFreightConfigEditRequestModel requestModel);

    @PostMapping(value = "/service/freight/config/delete")
    @ApiOperation(value = "删除车主运价配置", tags = "1.3.5")
    Result<Boolean> delete(@Valid @RequestBody CarrierFreightConfigRequestModel requestModel);

}
