package com.logistics.management.webapi.client.thirdparty.basicdata.file.hystrix;

import com.logistics.management.webapi.client.thirdparty.basicdata.file.FileClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.request.BatchGetOSSFileUrlRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.request.GetFileByFilePathRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.request.GetOSSUrlRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.response.FileUploadForAIResponseModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.response.GetFileByteOSSResponseModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.response.GetOSSUrlResponseModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.response.UploadFileOSSResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/27
 */
@Component
public class FileClientHystrix implements FileClient {


    @Override
    public Result<FileUploadForAIResponseModel> uploadOSSFileForAI(MultipartFile file, String picType, String idCardSide) {
        return Result.timeout();
    }

    @Override
    public Result<UploadFileOSSResponseModel> uploadMultiPartFileOSS(MultipartFile file) {
        return Result.timeout();
    }

    @Override
    public Result<List<GetOSSUrlResponseModel>> batchGetOSSFileUrl(BatchGetOSSFileUrlRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetOSSUrlResponseModel> getOSSFileUrl(GetOSSUrlRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetFileByteOSSResponseModel> getFileByteOSS(GetFileByFilePathRequestModel requestModel) {
        return Result.timeout();
    }
}
