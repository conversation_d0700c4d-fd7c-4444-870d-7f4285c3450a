package com.logistics.tms.base.enums;

/**
 * 新生货物确认状态
 *
 * <AUTHOR>
 */
public enum RenewableGoodsVerifyStatusEnum {

    WAIT_CONFIRM(0,"待确认"),
    HAS_CONFIRM(1,"已确认");

    private Integer key;
    private String value;

    RenewableGoodsVerifyStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
