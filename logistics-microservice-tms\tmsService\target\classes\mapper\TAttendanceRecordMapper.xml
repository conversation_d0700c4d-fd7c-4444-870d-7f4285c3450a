<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TAttendanceRecordMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TAttendanceRecord" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="staff_id" property="staffId" jdbcType="BIGINT" />
    <result column="staff_name" property="staffName" jdbcType="VARCHAR" />
    <result column="staff_mobile" property="staffMobile" jdbcType="VARCHAR" />
    <result column="staff_property" property="staffProperty" jdbcType="INTEGER" />
    <result column="attendance_date" property="attendanceDate" jdbcType="TIMESTAMP" />
    <result column="man_hour" property="manHour" jdbcType="DECIMAL" />
    <result column="on_duty_punch_time" property="onDutyPunchTime" jdbcType="TIMESTAMP" />
    <result column="on_duty_punch_location" property="onDutyPunchLocation" jdbcType="VARCHAR" />
    <result column="on_duty_punch_pic" property="onDutyPunchPic" jdbcType="VARCHAR" />
    <result column="off_duty_punch_time" property="offDutyPunchTime" jdbcType="TIMESTAMP" />
    <result column="off_duty_punch_location" property="offDutyPunchLocation" jdbcType="VARCHAR" />
    <result column="off_duty_punch_pic" property="offDutyPunchPic" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, staff_id, staff_name, staff_mobile, staff_property, attendance_date, man_hour, 
    on_duty_punch_time, on_duty_punch_location, on_duty_punch_pic, off_duty_punch_time, 
    off_duty_punch_location, off_duty_punch_pic, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_attendance_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_attendance_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TAttendanceRecord" >
    insert into t_attendance_record (id, staff_id, staff_name, 
      staff_mobile, staff_property, attendance_date, 
      man_hour, on_duty_punch_time, on_duty_punch_location, 
      on_duty_punch_pic, off_duty_punch_time, off_duty_punch_location, 
      off_duty_punch_pic, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{staffId,jdbcType=BIGINT}, #{staffName,jdbcType=VARCHAR}, 
      #{staffMobile,jdbcType=VARCHAR}, #{staffProperty,jdbcType=INTEGER}, #{attendanceDate,jdbcType=TIMESTAMP}, 
      #{manHour,jdbcType=DECIMAL}, #{onDutyPunchTime,jdbcType=TIMESTAMP}, #{onDutyPunchLocation,jdbcType=VARCHAR}, 
      #{onDutyPunchPic,jdbcType=VARCHAR}, #{offDutyPunchTime,jdbcType=TIMESTAMP}, #{offDutyPunchLocation,jdbcType=VARCHAR}, 
      #{offDutyPunchPic,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TAttendanceRecord" >
    insert into t_attendance_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="staffId != null" >
        staff_id,
      </if>
      <if test="staffName != null" >
        staff_name,
      </if>
      <if test="staffMobile != null" >
        staff_mobile,
      </if>
      <if test="staffProperty != null" >
        staff_property,
      </if>
      <if test="attendanceDate != null" >
        attendance_date,
      </if>
      <if test="manHour != null" >
        man_hour,
      </if>
      <if test="onDutyPunchTime != null" >
        on_duty_punch_time,
      </if>
      <if test="onDutyPunchLocation != null" >
        on_duty_punch_location,
      </if>
      <if test="onDutyPunchPic != null" >
        on_duty_punch_pic,
      </if>
      <if test="offDutyPunchTime != null" >
        off_duty_punch_time,
      </if>
      <if test="offDutyPunchLocation != null" >
        off_duty_punch_location,
      </if>
      <if test="offDutyPunchPic != null" >
        off_duty_punch_pic,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="staffId != null" >
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null" >
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null" >
        #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="staffProperty != null" >
        #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="attendanceDate != null" >
        #{attendanceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="manHour != null" >
        #{manHour,jdbcType=DECIMAL},
      </if>
      <if test="onDutyPunchTime != null" >
        #{onDutyPunchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onDutyPunchLocation != null" >
        #{onDutyPunchLocation,jdbcType=VARCHAR},
      </if>
      <if test="onDutyPunchPic != null" >
        #{onDutyPunchPic,jdbcType=VARCHAR},
      </if>
      <if test="offDutyPunchTime != null" >
        #{offDutyPunchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="offDutyPunchLocation != null" >
        #{offDutyPunchLocation,jdbcType=VARCHAR},
      </if>
      <if test="offDutyPunchPic != null" >
        #{offDutyPunchPic,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TAttendanceRecord" >
    update t_attendance_record
    <set >
      <if test="staffId != null" >
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffName != null" >
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null" >
        staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="staffProperty != null" >
        staff_property = #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="attendanceDate != null" >
        attendance_date = #{attendanceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="manHour != null" >
        man_hour = #{manHour,jdbcType=DECIMAL},
      </if>
      <if test="onDutyPunchTime != null" >
        on_duty_punch_time = #{onDutyPunchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onDutyPunchLocation != null" >
        on_duty_punch_location = #{onDutyPunchLocation,jdbcType=VARCHAR},
      </if>
      <if test="onDutyPunchPic != null" >
        on_duty_punch_pic = #{onDutyPunchPic,jdbcType=VARCHAR},
      </if>
      <if test="offDutyPunchTime != null" >
        off_duty_punch_time = #{offDutyPunchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="offDutyPunchLocation != null" >
        off_duty_punch_location = #{offDutyPunchLocation,jdbcType=VARCHAR},
      </if>
      <if test="offDutyPunchPic != null" >
        off_duty_punch_pic = #{offDutyPunchPic,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TAttendanceRecord" >
    update t_attendance_record
    set staff_id = #{staffId,jdbcType=BIGINT},
      staff_name = #{staffName,jdbcType=VARCHAR},
      staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      staff_property = #{staffProperty,jdbcType=INTEGER},
      attendance_date = #{attendanceDate,jdbcType=TIMESTAMP},
      man_hour = #{manHour,jdbcType=DECIMAL},
      on_duty_punch_time = #{onDutyPunchTime,jdbcType=TIMESTAMP},
      on_duty_punch_location = #{onDutyPunchLocation,jdbcType=VARCHAR},
      on_duty_punch_pic = #{onDutyPunchPic,jdbcType=VARCHAR},
      off_duty_punch_time = #{offDutyPunchTime,jdbcType=TIMESTAMP},
      off_duty_punch_location = #{offDutyPunchLocation,jdbcType=VARCHAR},
      off_duty_punch_pic = #{offDutyPunchPic,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>