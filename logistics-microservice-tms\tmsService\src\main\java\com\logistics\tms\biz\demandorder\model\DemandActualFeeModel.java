package com.logistics.tms.biz.demandorder.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DemandActualFeeModel {

    /**
     * 需求单id
     */
    private Long demandOrderId;

    /**
     * 货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位
     */
    private Integer settlementTonnage;

    /**
     * 委托单来源：1 乐医托盘委托单，2 委托方发单，3 中石化系统，4 扬巴邮件，5 乐橘新生
     */
    private Integer source;
}
