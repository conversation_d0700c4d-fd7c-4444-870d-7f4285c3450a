package com.logistics.appapi.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/17
 */
@Data
public class SearchLifeSkuResponseDto {

	@ApiModelProperty("sku编号(code)")
	private String skuCode = "";

	@ApiModelProperty("sku名")
	private String skuName = "";

	@ApiModelProperty("建议单价")
	private String suggestGoodsPrice = "";

	@ApiModelProperty("建议单价单位")
	private String suggestGoodsPriceUnit = "";
}
