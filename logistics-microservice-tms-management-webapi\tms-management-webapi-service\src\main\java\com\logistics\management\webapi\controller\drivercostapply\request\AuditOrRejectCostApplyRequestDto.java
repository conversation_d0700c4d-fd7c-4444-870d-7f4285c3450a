package com.logistics.management.webapi.controller.drivercostapply.request;

import com.logistics.management.webapi.controller.drivercostapply.response.ReserveItemDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/3 14:51
 */
@Data
public class AuditOrRejectCostApplyRequestDto {
    @ApiModelProperty(value = "司机费用申请表id",required = true)
    @NotBlank(message = "id不能为空")
    private String driverCostApplyId;

    @ApiModelProperty(value = "(3.17.0)操作人员类型 1:业务审核 2:财务审核", required = true)
    @NotBlank(message = "请选择操作人员类型")
    @Range(min = 1, max = 2, message = "操作人员类型只能为1-2")
    private String auditorType;

    @ApiModelProperty(value = "操作类型：1 审核通过，2 驳回",required = true)
    @NotBlank(message = "操作类型不能为空")
    @Range(min = 1, max = 2, message = "操作类型只能为1-2")
    private String operateType;

    @ApiModelProperty("撤销说明")
    @Size(max = 100, message = "备注最多100字")
    private String remark;

    @ApiModelProperty("冲销费用列表")
    private List<ReserveItemDto> reserveList;

    @ApiModelProperty("垫付费用（元）; 1.3.6 新增")
    private String advanceCosts;
}
