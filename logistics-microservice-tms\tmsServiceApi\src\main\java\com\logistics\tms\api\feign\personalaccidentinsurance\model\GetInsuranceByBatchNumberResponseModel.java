package com.logistics.tms.api.feign.personalaccidentinsurance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/5/31 14:00
 */
@Data
public class GetInsuranceByBatchNumberResponseModel {
    @ApiModelProperty("个人意外险表id")
    private Long personalAccidentInsuranceId;
    @ApiModelProperty("批单号")
    private String batchNumber;
    @ApiModelProperty("批单总额")
    private BigDecimal grossPremium;
    @ApiModelProperty("批单人数")
    private Integer policyPersonCount;
    @ApiModelProperty("保险生效时间")
    private Date startTime;
    @ApiModelProperty("保险截止时间")
    private Date endTime;
    @ApiModelProperty("保险公司名称")
    private String insuranceCompany;
    @ApiModelProperty("个人意外险批单单据")
    private List<PersonalAccidentInsuranceTicketsResponseModel> ticketsList;
}
