package com.logistics.appapi.client.verificationcode.hystrix;

import com.logistics.appapi.client.verificationcode.VerificationCodeClient;
import com.logistics.appapi.client.verificationcode.request.VerificationCodeRequestModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2023/10/26 13:56
 */
@Component
public class VerificationCodeHystrix implements VerificationCodeClient {
    @Override
    public Result<Boolean> getVerifyCode(VerificationCodeRequestModel requestModel) {
        return Result.timeout();
    }
}
