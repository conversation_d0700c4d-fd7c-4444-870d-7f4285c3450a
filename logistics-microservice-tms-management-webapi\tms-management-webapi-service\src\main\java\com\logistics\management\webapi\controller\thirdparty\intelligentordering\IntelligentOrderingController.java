package com.logistics.management.webapi.controller.thirdparty.intelligentordering;

import com.logistics.management.webapi.api.feign.violationregulation.dto.FuzzyQueryVehicleInfoRequestDto;
import com.logistics.management.webapi.api.feign.violationregulation.dto.FuzzyQueryVehicleInfoResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.client.companycarrier.CompanyCarrierClient;
import com.logistics.management.webapi.client.companycarrier.request.FuzzySearchCompanyCarrierRequestModel;
import com.logistics.management.webapi.client.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.management.webapi.client.demandorder.DemandOrderForLeYiClient;
import com.logistics.management.webapi.client.demandorder.request.BatchPublishRequestModel;
import com.logistics.management.webapi.client.demandorder.request.SearchDemandUnLoadAddressRequestModel;
import com.logistics.management.webapi.client.demandorder.response.SearchDemandUnLoadAddressResponseModel;
import com.logistics.management.webapi.client.dispatch.DispatchClient;
import com.logistics.management.webapi.client.dispatch.request.DispatchRequestModel;
import com.logistics.management.webapi.client.vehicleassetmanagement.VehicleAssetManagementClient;
import com.logistics.management.webapi.client.vehicleassetmanagement.request.FuzzyQueryVehicleInfoRequestModel;
import com.logistics.management.webapi.client.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel;
import com.logistics.management.webapi.controller.companycarrier.mapping.FuzzyQueryMapping;
import com.logistics.management.webapi.controller.companycarrier.request.FuzzySearchCompanyCarrierRequestDto;
import com.logistics.management.webapi.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseDto;
import com.logistics.management.webapi.controller.demandorder.request.BatchPublishRequestDto;
import com.logistics.management.webapi.controller.demandorder.request.SearchDemandUnLoadAddressRequestDto;
import com.logistics.management.webapi.controller.demandorder.response.SearchDemandUnLoadAddressResponseDto;
import com.logistics.management.webapi.controller.thirdparty.intelligentordering.request.DispatchForSmartRequestDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/12/26 13:29
 */
@RestController
@Api(value = "智能拼单页面", tags = "智能拼单页面")
@RequestMapping(value = "/api/intelligentordering")
public class IntelligentOrderingController {

    @Resource
    private CompanyCarrierClient companyCarrierClient;
    @Resource
    private DemandOrderForLeYiClient demandOrderForLeYiClient;
    @Resource
    private VehicleAssetManagementClient vehicleAssetManagementClient;
    @Resource
    private DispatchClient dispatchClient;

    @ApiOperation(value = "模糊查询车主公司信息")
    @PostMapping(value = "/companyCarrier/getCompanyByName")
    public Result<List<FuzzySearchCompanyCarrierResponseDto>> fuzzyQuery(@RequestBody FuzzySearchCompanyCarrierRequestDto requestDto) {
        Result<List<FuzzySearchCompanyCarrierResponseModel>> result = companyCarrierClient.fuzzyQuery(MapperUtils.mapper(requestDto, FuzzySearchCompanyCarrierRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), FuzzySearchCompanyCarrierResponseDto.class,new FuzzyQueryMapping()));
    }

    @ApiOperation(value = "模糊搜索仓库地址(发布页面调用云盘仓库接口)")
    @PostMapping(value = "/demandOrder/searchYPWarehouse")
    public Result<List<SearchDemandUnLoadAddressResponseDto>> searchYPWarehouse(@RequestBody @Valid SearchDemandUnLoadAddressRequestDto requestDto) {
        Result<List<SearchDemandUnLoadAddressResponseModel>> result = demandOrderForLeYiClient.searchYPWarehouse(MapperUtils.mapper(requestDto, SearchDemandUnLoadAddressRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SearchDemandUnLoadAddressResponseDto.class));
    }

    @ApiOperation(value = "云盘需求单批量发布")
    @PostMapping(value = "/demandOrder/confirmPublish")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> confirmPublish(@RequestBody @Valid BatchPublishRequestDto requestDto) {
        //需求单为空
        if (ListUtils.isEmpty(requestDto.getDemandDtoList())){
            throw new BizException(ManagementWebApiExceptionEnum.DEMADNLIST_ISNOTNULL);
        }
        if (requestDto.getDemandDtoList().size() > CommonConstant.INT_TEN){
            throw new BizException(ManagementWebApiExceptionEnum.PUBLISH_DEMAND_ORDER_MAX);
        }

        if (CommonConstant.TWO.equals(requestDto.getIsOurCompany())) {//其他车主
            //车主ID校验
            if (StringUtils.isBlank(requestDto.getCompanyCarrierId())) {
                throw new BizException(ManagementWebApiExceptionEnum.COMPANY_CARRIER_ID_IS_NULL);
            }
        }
        return demandOrderForLeYiClient.confirmPublish(MapperUtils.mapper(requestDto, BatchPublishRequestModel.class));
    }

    @PostMapping(value = "/dispatch/dispatchVehicle")
    @ApiOperation(value = "调度车辆-确认调度")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> dispatchVehicle(@RequestBody @Valid DispatchForSmartRequestDto requestDto) {
        DispatchRequestModel model = MapperUtils.mapperNoDefault(requestDto, DispatchRequestModel.class);
        model.setSource(CommonConstant.INTEGER_ONE);
        return dispatchClient.dispatchVehicle(model);
    }

    @ApiOperation(value = "模糊查询车辆车牌号信息")
    @PostMapping(value = "/vehicleAssetManagement/fuzzyQueryVehicleInfo")
    public Result<List<FuzzyQueryVehicleInfoResponseDto>> fuzzyQueryVehicleInfo(@RequestBody FuzzyQueryVehicleInfoRequestDto requestDto) {
        if (CommonConstant.TWO.equals(requestDto.getIsOurCompany()) && StringUtils.isBlank(requestDto.getCompanyCarrierId())) {
            return Result.success(new ArrayList<>());
        }
        Result<List<FuzzyQueryVehicleInfoResponseModel>> result = vehicleAssetManagementClient.fuzzyQueryVehicleInfo(MapperUtils.mapper(requestDto, FuzzyQueryVehicleInfoRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),FuzzyQueryVehicleInfoResponseDto.class));
    }
}
