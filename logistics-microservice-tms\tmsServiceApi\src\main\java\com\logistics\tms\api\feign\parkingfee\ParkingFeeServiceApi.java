package com.logistics.tms.api.feign.parkingfee;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.parkingfee.dto.*;
import com.logistics.tms.api.feign.parkingfee.hystrix.ParkingFeeServiceApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/10/9 15:28
 */
@Api(value = "API-ParkingFeeServiceApi-停车费用")
@FeignClient(name = "logistics-tms-services", fallback = ParkingFeeServiceApiHystrix.class)
public interface ParkingFeeServiceApi {

    @ApiOperation(value = "列表查询")
    @PostMapping(value = "/service/parkingFee/searchList")
    Result<PageInfo<ParkingFeeListResponseModel>> searchList(@RequestBody ParkingFeeListRequestModel requestModel);

    @ApiOperation(value = "获取列表汇总")
    @PostMapping(value = "/service/parkingFee/getSummary")
    Result<SummaryParkingFeeResponseModel> getSummary(@RequestBody ParkingFeeListRequestModel requestModel);

    @ApiOperation(value = "详情")
    @PostMapping(value = "/service/parkingFee/getDetail")
    Result<ParkingFeeDetailResponseModel> getDetail(@RequestBody ParkingFeeDetailRequestModel requestModel);

    @ApiOperation(value = "修改/保存")
    @PostMapping(value = "/service/parkingFee/saveOrUpdate")
    Result<Boolean> saveOrUpdate(@RequestBody SaveOrUpdateParkingFeeRequestModel requestModel);

    @ApiOperation(value = "费用终止")
    @PostMapping(value = "/service/parkingFee/terminateParkingFee")
    Result<Boolean> terminateParkingFee(@RequestBody TerminateParkingFeeRequestModel requestModel);

    @ApiOperation(value = "扣减历史列表")
    @PostMapping(value = "/service/parkingFee/getDeductingHistoryList")
    Result<List<ParkingFeeDeductingHistoryResponseModel>> getDeductingHistoryList(@RequestBody ParkingFeeDeductingHistoryRequestModel requestModel);

    @ApiOperation(value = "操作记录列表")
    @PostMapping(value = "/service/parkingFee/getOperationRecordList")
    Result<List<ParkingFeeOperationRecordResponsesModel>> getOperationRecordList(@RequestBody ParkingFeeOperationRecordRequestModel requestModel);

}
