package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleSettlementPayment extends BaseEntity {
    /**
    * 车辆结算id
    */
    @ApiModelProperty("车辆结算id")
    private Long vehicleSettlementId;

    /**
    * 打款公司
    */
    @ApiModelProperty("打款公司")
    private String payCompany;

    /**
    * 收款人
    */
    @ApiModelProperty("收款人")
    private String receiverName;

    /**
    * 打款时间
    */
    @ApiModelProperty("打款时间")
    private Date payTime;

    /**
    * 打款金额
    */
    @ApiModelProperty("打款金额")
    private BigDecimal payFee;
}