package com.logistics.tms.controller.routeenquiry.response;

import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2024/7/9 17:16
 */
@Data
public class SearchRouteEnquiryListResponseModel {

    /**
     * 路线询价单表id
     */
    private Long routeEnquiryId;

    /**
     * 竞价单号
     */
    private String orderCode;

    /**
     * 竞价状态：1 待业务审核，2 待车主确认，3 待结算审核，4 完成竞价
     */
    private Integer status;

    /**
     * 是否取消：0 否，1 是
     */
    private Integer ifCancel;

    /**
     * 是否归档：0 否，1 是
     */
    private Integer ifArchive;

    /**
     * 路线数
     */
    private Integer addressCount;

    /**
     * 报价承运商数
     */
    private Integer quotedCarrierCount;

    /**
     * 中标承运商-车主公司类型：1 公司，2 个人
     */
    private Integer companyCarrierType;

    /**
     * 中标承运商-车主公司名称
     */
    private String companyCarrierName;

    /**
     * 中标承运商-车主账号名称
     */
    private String carrierContactName;

    /**
     * 中标承运商-车主账号手机号
     */
    private String carrierContactPhone;

    /**
     * 报价生效开始时间
     */
    private Date quoteStartTime;

    /**
     * 报价生效结束时间
     */
    private Date quoteEndTime;

    /**
     * 关联合同号
     */
    private String contractCode;

    /**
     * 审核人
     */
    private String auditorNameOne;

    /**
     * 审核时间
     */
    private Date auditTimeOne;

    /**
     * 结算审核人
     */
    private String auditorNameTwo;

    /**
     * 结算审核时间
     */
    private Date auditTimeTwo;

}
