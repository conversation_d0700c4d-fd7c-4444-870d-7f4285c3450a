package com.logistics.management.webapi.controller.settlestatement.tradition.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/1
 */
@Data
public class ExportTraditionStatementDetailRequestDto {

	@ApiModelProperty(value = "对账单id", required = true)
	@NotBlank(message = "对账单id不能为空")
	private String settleStatementId;
}
