<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderOtherFeeMapper" >
    <select id="searchListId" resultType="java.lang.Long">
        select distinct
        tcoof.id
        from t_carrier_order_other_fee tcoof
        left join t_carrier_order_other_fee_item tcoofi on tcoof.id = tcoofi.carrier_order_other_fee_id and tcoofi.valid = 1
        where tcoof.valid = 1
        <if test="params.auditStatus != null">
            and tcoof.audit_status = #{params.auditStatus,jdbcType=INTEGER}
        </if>
        <if test="params.feeType != null">
            and tcoofi.fee_type = #{params.feeType,jdbcType=INTEGER}
        </if>
        <if test="params.lastModifiedBy != null and params.lastModifiedBy != ''">
            and instr(tcoof.last_modified_by, #{params.lastModifiedBy,jdbcType=VARCHAR})
        </if>
        <if test="params.lastModifiedTimeStart != null and params.lastModifiedTimeStart != ''">
            and tcoof.last_modified_time &gt;= DATE_FORMAT(#{params.lastModifiedTimeStart,jdbcType=VARCHAR},'%Y-%m-%d %H:%i:%S')
        </if>
        <if test="params.lastModifiedTimeEnd != null and params.lastModifiedTimeEnd != ''">
            and tcoof.last_modified_time &lt;= DATE_FORMAT(#{params.lastModifiedTimeEnd,jdbcType=VARCHAR},'%Y-%m-%d 23:59:59')
        </if>
        <if test="params.carrierOrderOtherFeeIds != null and params.carrierOrderOtherFeeIds != ''">
            and tcoof.id in (${params.carrierOrderOtherFeeIds})
        </if>
        <if test="params.carrierOrderIds != null and params.carrierOrderIds != ''">
            and tcoof.carrier_order_id in (${params.carrierOrderIds})
        </if>
        order by tcoof.last_modified_time desc, tcoof.id desc
    </select>

    <select id="searchList" resultType="com.logistics.tms.controller.carrierorderotherfee.response.SearchOtherFeeListResponseModel">
        select
        tcoof.id as carrierOrderOtherFeeId,
        tcoof.audit_status as auditStatus,
        tcoof.carrier_order_id as carrierOrderId,
        tcoof.carrier_order_code as carrierOrderCode,
        tcoof.total_amount as totalAmount,
        tcoof.created_by as createdBy,
        tcoof.created_time as createdTime,
        tcoof.last_modified_by as lastModifiedBy,
        tcoof.last_modified_time as lastModifiedTime
        from t_carrier_order_other_fee tcoof
        where tcoof.valid = 1
        and tcoof.id in (${ids})
        order by tcoof.last_modified_time desc, tcoof.id desc
    </select>

    <resultMap id="OtherFeeDetailMap" type="com.logistics.tms.controller.carrierorderotherfee.response.CarrierOrderOtherFeeDetailResponseModel" >
        <id column="id" property="carrierOrderOtherFeeId" jdbcType="BIGINT" />
        <result column="audit_status" property="auditStatus" jdbcType="INTEGER" />
        <result column="carrier_order_id" property="carrierOrderId" jdbcType="BIGINT" />
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR" />
        <result column="total_amount" property="totalAmount" jdbcType="DECIMAL" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <collection property="otherFeeList" ofType="com.logistics.tms.controller.carrierorderotherfee.response.CarrierOrderOtherFeeItemDetailResponseModel">
            <result column="carrier_order_other_fee_item_id" property="carrierOrderOtherFeeItemId" jdbcType="BIGINT"/>
            <result column="fee_type" property="feeType" jdbcType="INTEGER"/>
            <result column="fee_source" property="feeSource" jdbcType="INTEGER"/>
            <result column="fee_amount" property="feeAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="getCarrierOrderOtherFeeDetail" resultMap="OtherFeeDetailMap">
        SELECT
        tcoof.id,
        tcoof.audit_status,
        tcoof.carrier_order_id,
        tcoof.carrier_order_code,
        tcoof.total_amount,
        tcoof.remark,

        tcoofi.id AS carrier_order_other_fee_item_id,
        tcoofi.fee_type,
        tcoofi.fee_amount,
        tcoofi.fee_source


        FROM t_carrier_order_other_fee tcoof
        LEFT JOIN t_carrier_order_other_fee_item tcoofi ON tcoof.id = tcoofi.carrier_order_other_fee_id AND tcoofi.valid = 1
        WHERE tcoof.valid = 1
        AND tcoof.id = #{carrierOrderOtherFeeId}
    </select>

    <select id="getAuditByCarrierOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_other_fee
        where valid = 1
        and carrier_order_id in (${carrierOrderIds})
        and audit_status = 5
    </select>

    <select id="getNoCancelByCarrierOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_other_fee
        where valid = 1
        and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
        and audit_status != 4
    </select>

    <select id="getTopWaitCommitByCarrierOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_other_fee
        where valid = 1
        and carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
        and audit_status = 1
        order by id desc
        limit 1
    </select>
</mapper>