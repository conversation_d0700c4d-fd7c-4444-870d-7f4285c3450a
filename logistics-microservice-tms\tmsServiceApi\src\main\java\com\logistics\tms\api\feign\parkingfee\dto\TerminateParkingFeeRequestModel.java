package com.logistics.tms.api.feign.parkingfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/10/9 15:36
 */
@Data
public class TerminateParkingFeeRequestModel {
    @ApiModelProperty("停车费用ID")
    private Long parkingFeeId;
    @ApiModelProperty("终止时间")
    private Date finishDate;
    @ApiModelProperty("备注")
    private String remark;
}
