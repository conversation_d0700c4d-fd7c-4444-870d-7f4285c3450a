package com.logistics.tms.controller.carrierorderapplet;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.carrierorderapplet.CarrierOrderAppletBiz;
import com.logistics.tms.controller.carrierorderapplet.request.*;
import com.logistics.tms.controller.carrierorderapplet.response.*;
import com.yelo.tools.redis.redisdistributedlock.DistributedLock;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping(value = "/service/driverApplet/carrierOrder")
public class CarrierOrderAppletController {

    @Resource
    private CarrierOrderAppletBiz carrierOrderAppletBiz;

    /**
     * 查询运单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询运单列表")
    @PostMapping(value = "/searchList")
    public Result<PageInfo<SearchCarrierOrderListAppResponseModel>> searchList(@RequestBody SearchCarrierOrderListAppRequestModel requestModel) {
        PageInfo pageInfo = carrierOrderAppletBiz.searchList(requestModel);
        return Result.success(pageInfo);
    }

    /**
     * 查询运单列表每个状态的数量
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询运单列表每个状态的数量")
    @PostMapping(value = "/searchListAccount")
    public Result<SearchCarrierOrderCountResponseModel> searchListAccount(@RequestBody SearchCarrierOrderListAppRequestModel requestModel) {
        return Result.success(carrierOrderAppletBiz.searchListAccount(requestModel));
    }

    /**
     * 运单详情页
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "运单详情页")
    @PostMapping(value = "/carrierOrderDetail")
    public Result<CarrierOrderDetailAppResponseModel> carrierOrderDetail(@RequestBody CarrierOrderIdRequestModel requestModel) {
        return Result.success(carrierOrderAppletBiz.carrierOrderDetail(requestModel));
    }

    /**
     * 物流详情页
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "物流详情页")
    @PostMapping(value = "/carrierOrderLogisticsDetail")
    public Result<CarrierOrderLogisticsDetailResponseModel> carrierOrderLogisticsDetail(@RequestBody CarrierOrderIdRequestModel requestModel) {
        return Result.success(carrierOrderAppletBiz.carrierOrderLogisticsDetail(requestModel));
    }

    /**
     * 触达到达提货地
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "到达提货地v2(司机承担触达)")
    @PostMapping(value = "/arrivePickUpV2")
    @DistributedLock(prefix = CommonConstant.CARRIER_ORDER_LOAD_LOCK,
            keys = "#requestModel.carrierOrderId",
            waitTime = 3)
    public Result<Boolean> arrivePickUpV2(@RequestBody ArrivePickUpV2RequestModel requestModel) {
        carrierOrderAppletBiz.arrivePickUpV2(requestModel);
        return Result.success(true);
    }

    @ApiOperation(value = "提货")
    @PostMapping(value = "/pickUp")
    @DistributedLock(prefix = CommonConstant.CARRIER_ORDER_LOAD_LOCK,
            keys = "#requestModel.carrierOrderId",
            waitTime = 3)
    public Result pickUp(@RequestBody CarrierOrderLoadRequestModel requestModel) {
       return carrierOrderAppletBiz.pickUp(requestModel);

    }

    /**
     * 卸货
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "卸货")
    @PostMapping(value = "/unloading")
    @DistributedLock(prefix = CommonConstant.CARRIER_ORDER_UNLOAD_LOCK,
            keys = "#requestModel.carrierOrderId",
            waitTime = 3)
    public Result<Boolean> unloading(@RequestBody CarrierOrderUnloadRequestModel requestModel) {
        carrierOrderAppletBiz.unloading(requestModel);
        return Result.success(true);
    }

    /**
     * 到达提货地
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "到达提货地")
    @PostMapping(value = "/arrivePickUp")
    @DistributedLock(prefix = CommonConstant.CARRIER_ORDER_REACH_LOAD_ADDRESS_LOCK,
            keys = "#requestModel.carrierOrderId",
            waitTime = 3)
    public Result<Boolean> arrivePickUp(@RequestBody CarrierOrderArriveLoadUnloadRequestModel requestModel) {
        carrierOrderAppletBiz.arrivePickUp(requestModel);
        return Result.success(true);
    }

    /**
     * 到达卸货地
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "到达卸货地")
    @PostMapping(value = "/arriveUnloading")
    @DistributedLock(prefix = CommonConstant.CARRIER_ORDER_REACH_UNLOAD_ADDRESS_LOCK,
            keys = "#requestModel.carrierOrderId",
            waitTime = 3)
    public Result<Boolean> arriveUnloading(@RequestBody CarrierOrderArriveLoadUnloadRequestModel requestModel) {
        carrierOrderAppletBiz.arriveUnloading(requestModel);
        return Result.success(true);
    }

    /**
     * 根据运单号查询运单id
     *
     * @param requestModel 运单号
     * @return 运单id
     */
    @ApiOperation(value = "扫一扫根据运单号查询运单id")
    @PostMapping(value = "/queryIdByCarrierOrderCode")
    public Result<QueryIdByCarrierOrderCodeResponseModel> queryIdByCarrierOrderCode(@RequestBody QueryIdByCarrierOrderCodeRequestModel requestModel) {
        return Result.success(carrierOrderAppletBiz.queryIdByCarrierOrderCode(requestModel));
    }

    /**
     * 司机打印详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "司机打印详情")
    @PostMapping(value = "/printBillDetail")
    public Result<PrintBillDetailResponseModel> printBillDetail(@RequestBody CarrierOrderIdRequestModel requestModel) {
        return Result.success(carrierOrderAppletBiz.printBillDetail(requestModel));
    }

    @ApiOperation(value = "(云盘)提货确认", tags = "3.7.0")
    @PostMapping(value = "/pickupConfirm")
    public Result<LeyiPickupConfirmResponseModel> pickupConfirm(@RequestBody PickupConfirmRequestModel requestModel) {
        return Result.success(carrierOrderAppletBiz.pickupConfirm(requestModel));
    }

    @ApiOperation(value = "司机申请修改提货数量-(拒绝)")
    @PostMapping(value = "/denyLoadAmount")
    public Result<Boolean> denyLoadAmount(@RequestBody CarrierOrderIdRequestModel requestModel) {
        carrierOrderAppletBiz.denyLoadAmount(requestModel);
        return Result.success(true);
    }

}
