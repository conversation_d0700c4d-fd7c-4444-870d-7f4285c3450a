package com.logistics.management.webapi.client.warehouseaddress;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.warehouseaddress.hystrix.WarehouseAddressClientHystrix;
import com.logistics.management.webapi.client.warehouseaddress.request.*;
import com.logistics.management.webapi.client.warehouseaddress.response.SearchWarehouseAddressResponseModel;
import com.logistics.management.webapi.client.warehouseaddress.response.WarehouseAddressDetailResponseModel;
import com.logistics.management.webapi.client.warehouseaddress.response.WarehouseAddressListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/15 9:25
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/warehouseaddress",
        fallback = WarehouseAddressClientHystrix.class)
public interface WarehouseAddressClient {

    /**
     * 中石化仓库列表
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/list")
    Result<PageInfo<WarehouseAddressListResponseModel>> warehouseAddressList(@RequestBody WarehouseAddressListRequestModel requestModel);

    /**
     * 中石化仓库新增/编辑/删除
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/addOrModifyOrDel")
    Result<Boolean> warehouseAddressAddOrModifyOrDel(@RequestBody AddWarehouseAddressRequestModel requestModel);

    /**
     * 中石化仓库详情
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/detail")
    Result<WarehouseAddressDetailResponseModel> warehouseAddressDetail(@RequestBody WarehouseAddressDetailRequestModel requestModel);

    /**
     * 导出
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/export")
    Result<List<WarehouseAddressListResponseModel>> export(@RequestBody WarehouseAddressListRequestModel requestModel);

    /**
     * 仓库启用/禁用
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/enable")
    Result<Boolean> enable(@RequestBody WarehouseAddressEnableRequestModel requestModel);

    /**
     * 根据仓库名模糊搜索地址信息
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/searchWarehouseAddress")
    Result<PageInfo<SearchWarehouseAddressResponseModel>> searchWarehouseAddress(@RequestBody SearchWarehouseAddressRequestModel requestModel);

}
