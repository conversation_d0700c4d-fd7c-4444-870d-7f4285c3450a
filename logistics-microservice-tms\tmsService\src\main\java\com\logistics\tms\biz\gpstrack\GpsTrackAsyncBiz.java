package com.logistics.tms.biz.gpstrack;


import com.logistics.tms.api.feign.gpstrack.model.VehicleLatestPositionModel;
import com.logistics.tms.base.enums.TransErrorCodeEnum;
import com.logistics.tms.base.enums.VehicleStatusEnum;
import com.logistics.tms.base.utils.RegExpValidatorUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TVehicleGps;
import com.logistics.tms.mapper.TVehicleGpsMapper;
import com.yelo.basicdata.api.feign.thirdpartclient.ZjxlClient;
import com.yelo.basicdata.api.feign.thirdpartclient.entity.zjxl.OpGpVclPosInfoMultipleModel;
import com.yelo.basicdata.api.feign.thirdpartclient.entity.zjxl.OpGpVclPosMultipleRequestModel;
import com.yelo.basicdata.api.feign.thirdpartclient.entity.zjxl.OpGpVclPosMultipleResponseModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class GpsTrackAsyncBiz {

    @Autowired
    private TVehicleGpsMapper tVehicleGpsMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private ZjxlClient zjxlClient;

    @Async
    public void pullSyncVehicleLatestInfo() {
        List<TVehicleGps> tVehicleGps = tVehicleGpsMapper.selectAllVehicles();
        List<TVehicleGps> updateVehicleGps = new ArrayList<>();
        List<String> vehicles = new ArrayList<>();
        int unit = 90;
        int batch = tVehicleGps.size() / unit + 1;
        for (int i = 0; i < batch; i++) {
            for (TVehicleGps tmp : (tVehicleGps.subList(i * unit, ((i + 1) * unit < tVehicleGps.size() ? (i + 1) * unit : tVehicleGps.size())))) {
                String vehicleNo = tmp.getVehicleNo();
                if (!RegExpValidatorUtil.isVehicleNo(vehicleNo)) {
                    continue;
                }
                vehicles.add(tmp.getVehicleNo() + "_2");
            }
            if (vehicles.isEmpty()) {
                return;
            } else {
                Result<OpGpVclPosMultipleResponseModel> result = opGpVclPosMultiple(StringUtils.listToString(vehicles, ','));
                if (result.isSuccess()) {
                    List<OpGpVclPosInfoMultipleModel> opGpVclPosInfoModelList = result.getData().getResult();
                    if (opGpVclPosInfoModelList == null)
                        return;
                    for (OpGpVclPosInfoMultipleModel tmp1 : opGpVclPosInfoModelList) {
                        if (!TransErrorCodeEnum.ERROR_CODE_1001.getKey().equals(tmp1.getState()))
                            continue;
                        String vehicleNo2 = tmp1.getVno();
                        TVehicleGps upVehicleGps = new TVehicleGps();
                        upVehicleGps.setVehicleNo(vehicleNo2);
                        upVehicleGps.setUploadTime(new Date(ConverterUtils.toLong(tmp1.getUtc())));
                        if (new BigDecimal(tmp1.getSpd()).compareTo(BigDecimal.ZERO) > 0) {
                            upVehicleGps.setVehicleStatus(VehicleStatusEnum.DRIVING.getKey());
                        } else {
                            upVehicleGps.setVehicleStatus(VehicleStatusEnum.STOP.getKey());
                        }
                        upVehicleGps.setCurrentLocation(tmp1.getAdr());
                        commonBiz.setBaseEntityModify(upVehicleGps, BaseContextHandler.getUserName());
                        updateVehicleGps.add(upVehicleGps);
                    }
                }
            }
            vehicles.clear();
        }
        if (!updateVehicleGps.isEmpty()) {
            tVehicleGpsMapper.batchUpdateVehicleGps(updateVehicleGps);
        }
    }

    //搜索最新地址更新本地地址信息
    public VehicleLatestPositionModel getVehicleLatestPositionAndUpdateLocal(String searchVehicleNo) {
        Result<OpGpVclPosMultipleResponseModel> resultModel = opGpVclPosMultiple(searchVehicleNo + "_2");
        resultModel.throwException();
        OpGpVclPosMultipleResponseModel opGpVclPosResponseModel = resultModel.getData();

        if (TransErrorCodeEnum.ERROR_CODE_1001.getKey().equals(opGpVclPosResponseModel.getStatus())) {//有结果的情况，更新
            OpGpVclPosInfoMultipleModel result = null;
            for (OpGpVclPosInfoMultipleModel model : opGpVclPosResponseModel.getResult()) {
                if (TransErrorCodeEnum.ERROR_CODE_1001.getKey().equals(model.getState())) {
                    result = model;
                    break;
                }
            }
            if (result == null) {
                return null;
            }
            BigDecimal speed = ConverterUtils.toBigDecimal(result.getSpd());
            Date uploadTime = new Date(ConverterUtils.toLong(result.getUtc()));
            String address = result.getAdr();
            //更新本地的位置信息
            List<TVehicleGps> vehicleGpsByNoList = tVehicleGpsMapper.selectVehicleGpsByVehicleNo(searchVehicleNo);
            if (ListUtils.isNotEmpty(vehicleGpsByNoList)) {
                List<TVehicleGps> upTVehicleGpsList = new ArrayList<>();
                vehicleGpsByNoList.stream().forEach(tmp -> {
                    TVehicleGps upTVehicleGps = new TVehicleGps();
                    upTVehicleGps.setId(tmp.getId());
                    if (speed.compareTo(BigDecimal.ZERO) > 0) {
                        upTVehicleGps.setVehicleStatus(VehicleStatusEnum.DRIVING.getKey());
                    } else {
                        upTVehicleGps.setVehicleStatus(VehicleStatusEnum.STOP.getKey());
                    }
                    upTVehicleGps.setCurrentLocation(address);
                    upTVehicleGps.setUploadTime(uploadTime);
                    commonBiz.setBaseEntityModify(upTVehicleGps, BaseContextHandler.getUserName());
                    upTVehicleGpsList.add(upTVehicleGps);
                });
                if(ListUtils.isNotEmpty(upTVehicleGpsList)){
                    tVehicleGpsMapper.batchUpdateVehicleGps(upTVehicleGpsList);
                }
            }
            return MapperUtils.mapper(result, VehicleLatestPositionModel.class);
        } else {
            return null;
        }
    }

    // 获取运输节点服务
    private Result<OpGpVclPosMultipleResponseModel> opGpVclPosMultiple(String vehicles) {
        OpGpVclPosMultipleRequestModel requestModel = new OpGpVclPosMultipleRequestModel();
        requestModel.setTimeNearby("240");//h
        requestModel.setVclNs(vehicles);
        Result<OpGpVclPosMultipleResponseModel> result = zjxlClient.opGpVclPosMultiple(requestModel);
        return result;
    }

}
