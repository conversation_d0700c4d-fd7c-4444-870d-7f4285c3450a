package com.logistics.appapi.client.reserveapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class ReserveBalanceApplyLogModel {

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("备注")
    private String remark;
}
