package com.logistics.tms.controller.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @data:2019/6/3 13:30
 */
@Data
public class VehicleAssetManagementDetailResponseModel {
    @ApiModelProperty("车主车辆关联Id")
    private Long carrierVehicleId;
    @ApiModelProperty("车辆基本信息Id")
    private Long vehicleBasicId;
    //基础信息
    @ApiModelProperty("车辆使用性质：1 普货 2 危货")
    private Integer usageProperty;
    @ApiModelProperty("是否安装GPS 1 是 0 否")
    private Integer ifInstallGps;
    @ApiModelProperty("是否入网石化 1 是 0 否")
    private Integer ifAccessSinopec;
    @ApiModelProperty("类型 1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("真实所属车主")
    private String vehicleOwner;
    @ApiModelProperty("排放标准类型：1 国一，2 国二，3 国三，4 国四，5 国五，6 国六")
    private Integer emissionStandardType;

    //机动车行驶证
    @ApiModelProperty("机动车行驶证Id")
    private  Long vehicleDrivingLicenseId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆类型Id")
    private Integer vehicleTypeId;
    @ApiModelProperty("车辆类型")
    private String vehicleType;
    @ApiModelProperty("装载量（可装载托盘数）")
    private Integer loadingCapacity;
    @ApiModelProperty("住址")
    private String address;
    @ApiModelProperty("所有人")
    private String owner;
    @ApiModelProperty("品牌")
    private String  brand;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("车辆识别号")
    private String vehicleIdentificationNumber;
    @ApiModelProperty("发动机号码")
    private String engineNumber;
    @ApiModelProperty("发证部门")
    private String certificationDepartment;
    @ApiModelProperty("注册日期")
    private Date registrationDate;
    @ApiModelProperty("发证日期")
    private Date issueDate;
    @ApiModelProperty("归档编号")
    private String filingNumber;
    @ApiModelProperty("核定载人数")
    private Integer authorizedCarryingCapacity;
    @ApiModelProperty("总质量")
    private BigDecimal totalWeight;
    @ApiModelProperty("整备质量")
    private BigDecimal curbWeight;
    @ApiModelProperty("准牵引总质量")
    private BigDecimal tractionMassWeight;
    @ApiModelProperty("核定载质量")
    private BigDecimal approvedLoadWeight;
    @ApiModelProperty("长")
    private Integer length;
    @ApiModelProperty("宽")
    private Integer width;
    @ApiModelProperty("高")
    private Integer height;
    @ApiModelProperty("车辆强制报废期")
    private Date obsolescenceDate;
    @ApiModelProperty("车辆轴数")
    private Integer axleNumber;
    @ApiModelProperty("驱动轴数")
    private Integer driveShaftNumber;
    @ApiModelProperty("轮胎数")
    private Integer tiresNumber;
    @ApiModelProperty("车牌颜色 1 黄色 2 蓝色")
    private Integer plateColor;
    @ApiModelProperty("车身颜色")
    private String bodyColor;
    //道路运输证
    @ApiModelProperty("道路运输证Id")
    private Long vehicleRoadTransportCertificateId;
    @ApiModelProperty("发签证")
    private String certificationSign;
    @ApiModelProperty("经营许可证号")
    private String businessLicenseNumber;
    @ApiModelProperty("经济类型")
    private String economicType;
    @ApiModelProperty("吨位")
    private BigDecimal transportTonnage;
    @ApiModelProperty("经营类型")
    private String businessScope;
    @ApiModelProperty("发证部门")
    private String roadCertificationDepartment;
    @ApiModelProperty("发证日期")
    private Date roadIssueDate;
    @ApiModelProperty("初领日期")
    private Date obtainDate;
    @ApiModelProperty("备注")
    private String remark;
    //认证信息（SGS）
    @ApiModelProperty("认证期限开始时间")
    private Date authenticationStartTime;
    @ApiModelProperty("认证期限到期时间")
    private Date authenticationExpireTime;
    //入网石化
    @ApiModelProperty("入网时间")
    private Date connectTime;
    @ApiModelProperty("入网方式 1 转发 2 新装")
    private Integer connectWay;
    @ApiModelProperty("登记证书编号")
    private String registrationCertificationNumber;

    //列表
    @ApiModelProperty("行驶证年审记录列表")
    private List<DrivingLicenseAnnualReviewListResponseModel> vehicleDrivingLicensePageList;
    @ApiModelProperty("营运证年审记录列表")
    private List<VehicleRoadTransportCertificateListResponseModel> vehicleRoadTransportCertificateList;
    @ApiModelProperty("等级评定列表")
    private List<VehicleGradeEstimationListResponseModel> vehicleGradeEstimationList;
    @ApiModelProperty("GPS终端记录列表")
    private List<VehicleGpsRecordListResponseModel> vehicleGpsRecordList;

    //车辆资产证件,凭证（"车辆资产证件,凭证"）
    @ApiModelProperty("车辆资产证件,凭证")
    private List<CertificationPicturesRecordModel> otherDocumentsRecord;

    //车主信息
    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;
    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty(value = "车主名称")
    private String companyCarrierName;
    @ApiModelProperty(value = "车主公司类型：1 公司，2 个人")
    private Integer companyCarrierType;
    @ApiModelProperty("车主账号名称")
    private String carrierContactName;
    @ApiModelProperty("车主账号手机号（原长度50）")
    private String carrierContactPhone;
}
