package com.logistics.management.webapi.api.feign.driveraccount.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DriverAccountDetailResponseDto {

    @ApiModelProperty(value = "开户银行")
    private String bankAccountName = "";

    @ApiModelProperty(value = "银行账号")
    private String bankAccount = "";

    @ApiModelProperty("司机id")
    private String driverId = "";

    @ApiModelProperty(value = "司机名称, 姓名 + 手机号")
    private String driverName = "";

    @ApiModelProperty(value = "开户支行名称")
    private String braBankName;

    @ApiModelProperty(value = "行号")
    private String bankCode = "";

    @ApiModelProperty(value = "银行卡图片")
    private List<DriverAccountImageDetailResponseDto> bankAccountImages;

}
