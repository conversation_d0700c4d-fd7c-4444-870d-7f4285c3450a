package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/6/4 19:58
 */
public class ExportExcelInsurance {
    private ExportExcelInsurance() {

    }

    private static final Map<String, String> EXPORT_INSURANCE;

    static {
        EXPORT_INSURANCE = new LinkedHashMap<>();
        EXPORT_INSURANCE.put("保单状态", "policyStatusDesc");
        EXPORT_INSURANCE.put("结算状态", "settlementStatusDesc");
        EXPORT_INSURANCE.put("车辆机构", "vehiclePropertyLabel");
        EXPORT_INSURANCE.put("车牌号", "vehicleNo");
        EXPORT_INSURANCE.put("司机", "driverNamePhone");
        EXPORT_INSURANCE.put("保险险种", "insuranceTypeDesc");
        EXPORT_INSURANCE.put("保险公司", "insuranceCompanyName");
        EXPORT_INSURANCE.put("保单号", "policyNumberExport");
        EXPORT_INSURANCE.put("保费合计（元）", "premium");
        EXPORT_INSURANCE.put("未还保险费（元）", "outstandingPremium");
        EXPORT_INSURANCE.put("退保金额（元）", "refundPremium");
        EXPORT_INSURANCE.put("保险生效时间", "startTime");
        EXPORT_INSURANCE.put("保险截止时间", "endTime");
        EXPORT_INSURANCE.put("备注", "remark");
        EXPORT_INSURANCE.put("新增人", "addUserName");
        EXPORT_INSURANCE.put("最新操作人", "lastModifiedBy");
        EXPORT_INSURANCE.put("操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getExportInsurance() {
        return EXPORT_INSURANCE;
    }
}
