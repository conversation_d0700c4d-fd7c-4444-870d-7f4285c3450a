package com.logistics.tms.controller.reservebalance.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DriverReserveBalanceListRequestModel extends AbstractPageForm<DriverReserveBalanceListRequestModel> {

    @ApiModelProperty(value = "人员机构, 1 自主 2 外包 3 自营")
    private Integer staffProperty;

    @ApiModelProperty(value = "司机, 姓名 + 手机号")
    private String driverName;

    @ApiModelProperty(value = "余额排序, 0 升序 1 降序")
    private Integer balanceSort;

    @ApiModelProperty(value = "已冲销排序, 0 升序 1 降序")
    private Integer verificationSort;

}
