package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class GetPublishDemandOrderDetailResponseDto {

    @ApiModelProperty("需求单ID")
    private String demandOrderId;
    @ApiModelProperty("需求单号")
    private String demandOrderCode;
    @ApiModelProperty("客户单号")
    private String customerOrderCode;
    @ApiModelProperty(value="发货公司id",required = true)
    private String companyEntrustId;
    @ApiModelProperty(value = "发货公司名称",required = true)
    private String companyEntrustName;
    @ApiModelProperty(value = "发货公司类型",required = true)
    private String companyEntrustType;
    @ApiModelProperty(value = "发货公司联系人名称",required = true)
    private String companyEntrustContactName;
    @ApiModelProperty(value = "发货公司联系方式",required = true)
    private String companyEntrustContactPhone;
    @ApiModelProperty(value = "货主联系人",required = true)
    private String entrustContactId;
    @ApiModelProperty(value = "发货地址",required = true)
    private String loadProvinceId;
    private String loadProvinceName;
    @ApiModelProperty(value = "发货地址",required = true)
    private String loadCityId;
    private String loadCityName;
    @ApiModelProperty(value = "发货地址",required = true)
    private String loadAreaId;
    private String loadAreaName;
    private String loadWarehouse;
    private String loadDetailAddress;
    private String loadContactName;
    private String loadContactMobile;
    @ApiModelProperty("期望提货时间")
    private String expectedLoadTime;
    @ApiModelProperty(value = "收货地址",required = true)
    private String unloadProvinceId;
    private String unloadProvinceName;
    @ApiModelProperty(value = "收货地址",required = true)
    private String unloadCityId;
    private String unloadCityName;
    @ApiModelProperty(value = "收货地址",required = true)
    private String unloadAreaId;
    private String unloadAreaName;
    private String unloadWarehouse;
    private String unloadDetailAddress;
    private String unloadContactName;
    @ApiModelProperty(value = "收货地址联系方式",required = true)
    private String unloadContactMobile;
    @ApiModelProperty("期望卸货时间")
    private String expectedUnloadTime;
    @ApiModelProperty(value = "货主结算费用类型：0 空 1 单价(元/吨，元/件)，2 一口价(元)",required = true)
    private String contractPriceType;
    @ApiModelProperty(value = "合同价：0 空",required = true)
    private String contractPrice;
    @ApiModelProperty(value = "承运商公司id：0 空",required = true)
    private String companyCarrierId;
    @ApiModelProperty(value = "承运商公司名称",required = true)
    private String companyCarrierName;
    @ApiModelProperty(value = "承运商公司类型",required = true)
    private String companyCarrierType;
    @ApiModelProperty(value = "车主账号id：0 空",required = true)
    private String carrierContactId;
    @ApiModelProperty(value = "承运商公司联系人名称",required = true)
    private String companyCarrierContactName;
    @ApiModelProperty(value = "承运商公司联系方式",required = true)
    private String companyCarrierContactPhone;
    @ApiModelProperty(value = "车主价格：0 空，1 单价(元/吨，元/件)，2 一口价(元)",required = true)
    private String carrierPriceType;
    @ApiModelProperty(value = "车主价格：0 空",required = true)
    private String carrierPrice;
    @ApiModelProperty("是否我司 1:我司,2:其他车主")
    private String isOurCompany;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("备注")
    private String goodsUnit;
    @ApiModelProperty("委托单货物信息")
    private List<DemandOrderGoodsBaseInfoResponseDto> demandOrderGoodsList;
}
