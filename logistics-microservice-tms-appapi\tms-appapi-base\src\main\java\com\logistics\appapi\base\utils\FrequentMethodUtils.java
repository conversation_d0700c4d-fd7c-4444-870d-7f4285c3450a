package com.logistics.appapi.base.utils;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.EncodeTypeEnum;
import com.yelo.tools.utils.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: wjf
 * @date: 2019/10/31 19:20
 */
public class FrequentMethodUtils {
    private FrequentMethodUtils() {
    }

    public static String encodePhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return "";
        }
        phone = phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        return phone;
    }

    public static String encodeVehicleNo(String vehicleNo) {
        if (StringUtils.isBlank(vehicleNo)) {
            return "";
        }
        vehicleNo = vehicleNo.replaceAll("([\\u4E00-\\u9FA5][A-Z])\\w{4}(\\w{1,2})", "$1****$2");
        return vehicleNo;
    }

    public static String encodeUserName(String userName) {
        if (StringUtils.isBlank(userName)) {
            return "";
        }
        userName = userName.replaceAll("([\\u4E00-\\u9FA5a-zA-Z])[\\u4E00-\\u9FA5\\w]{1,}", "$1*");
        return userName;
    }

    public static String encodeYearMonth(String yearMonth) {
        if (StringUtils.isBlank(yearMonth)) {
            return "";
        }
        yearMonth = yearMonth.replaceAll("(\\d{4})-(\\d{2})", "$1年$2月");
        return yearMonth;
    }

    public static String encodeString(String str) {
        if (StringUtils.isBlank(str)) {
            return "";
        }
        int length = str.length();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length-1; i++) {
            sb.append("*");
        }
        sb.append(str.substring(str.length()-1));
        return sb.toString();
    }

    //校验手机号：需11位，1开头即可
    public static boolean checkMobilePhone(String mobilePhone){
        if(StringUtils.isBlank(mobilePhone)){
            return false;
        }
        Pattern pattern = Pattern.compile("^[1]\\d{10}$");
        Matcher matcher = pattern.matcher(mobilePhone);
        return matcher.find();
    }

    //校验密码：需8~16位，且必须包含大小写字母和数字
    public static boolean checkPassword(String password){
        if(StringUtils.isBlank(password)){
            return false;
        }
        Pattern pattern = Pattern.compile("^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,16}$");
        Matcher matcher = pattern.matcher(password);
        return matcher.find();
    }

    /**
     * 源字符串除去保留的部分其余都用'*'替换
     * @param source 源数据
     * @param typeEnum 加密类型
     * @return
     */
    public static String encryptionData(String source, EncodeTypeEnum typeEnum){
        if(StringUtils.isBlank(source) || typeEnum == null){
            return "";
        }

        int startLength = typeEnum.getStartLength();
        int endLength = typeEnum.getEndLength();

        int encodeLength = source.length() - startLength - endLength;
        if (encodeLength <= 0){
            return source;
        }

        StringBuffer encodeStr = new StringBuffer();
        for (int i = 1; i <= encodeLength ;i++) {
            encodeStr.append("*");
        }

        String startStr = source.substring(CommonConstant.INTEGER_ZERO, startLength);
        String endStr = source.substring(source.length()-endLength);
        return startStr + encodeStr + endStr;
    }
}
