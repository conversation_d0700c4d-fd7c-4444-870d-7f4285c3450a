package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2024/4/26 13:42
 */
public enum YesOrNoEnum {
    DEFAULT(-99999, ""),
    NO(0, "否"),
    YES(1, "是"),;

    private Integer key;
    private String value;

    YesOrNoEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static YesOrNoEnum getEnum(Integer key) {
        for (YesOrNoEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return DEFAULT;
    }
}
