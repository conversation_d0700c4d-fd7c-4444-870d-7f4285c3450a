package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.bank.model.*;
import com.logistics.tms.entity.TBank;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TBankMapper extends BaseMapper<TBank> {

    List<SearchBankResponseModel> searchBankList(@Param("params") SearchBankRequestModel requestModel);

    List<FuzzyQueryBankListResponseModel> queryBankListByName(@Param("params") FuzzyQueryBankRequestModel requestModel);

    TBank findBankByName(@Param("bankName") String bankName);

    TBank findByName(@Param("bankName") String bankName,@Param("branchName") String branchName);

    BankDetailResponseModel getBankDetailById(@Param("bankId") Long bankId);

    int batchInsert(@Param("list") List<TBank> list);

    int batchUpdateByName(@Param("list") List<TBank> list);

    int batchUpdate(@Param("list") List<TBank> list);
}