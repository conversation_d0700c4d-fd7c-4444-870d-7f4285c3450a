package com.logistics.management.webapi.controller.reservationorder.mapping;

import cn.hutool.core.date.DateUtil;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.reservation.response.ReservationOrderSearchListForManagementWebResModel;
import com.logistics.management.webapi.controller.reservationorder.dto.ReservationOrderSearchListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

/**
 * @author: wjf
 * @date: 2024/7/10 13:53
 */
public class ReservationOrderSearchListMapping extends MapperMapping<ReservationOrderSearchListForManagementWebResModel, ReservationOrderSearchListResponseDto> {
    @Override
    public void configure() {
        ReservationOrderSearchListForManagementWebResModel source = getSource();
        ReservationOrderSearchListResponseDto destination = getDestination();

        //个人类型 把联系人+手机号当作公司名
        if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())) {
            destination.setCarrierName(source.getCarrierContactName() + " " + source.getCarrierContactMobile());
        }else {
            destination.setCarrierName(source.getCompanyCarrierName());
        }
        destination.setReservationRoleLabel(ReservationRoleEnum.getEnum(source.getReservationRole()).getValue());
        destination.setReservationSourceLabel(ReservationSourceEnum.getEnum(source.getReservationSource()).getValue());
        destination.setReservationTypeLabel(ReservationTypeEnum.getEnum(source.getReservationType()).getValue());
        destination.setStateStr(ReservationOrderStatusEnum.getEnum(source.getState()).getValue());
        if (source.getReservationStartTime() != null) {
            destination.setReservationDate(DateUtil.format(source.getReservationStartTime(), CommonConstant.DATE_TO_STRING_YMD_PATTERN));
        }
        if (source.getReservationStartTime() != null && source.getReservationEndTime() != null) {
            destination.setReservationTimeRange(DateUtil.format(source.getReservationStartTime(), CommonConstant.DATE_TO_STRING_HM_PATTERN) + "-" + DateUtil.format(source.getReservationEndTime(), CommonConstant.DATE_TO_STRING_HM_PATTERN));
        }
        if (source.getSignInTime() != null) {
            destination.setSignInTime(DateUtil.format(source.getSignInTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
        }
        if (StringUtils.isNotEmpty(source.getIdentityCardNumber())) {
            destination.setIdentityCardNumber(FrequentMethodUtils.encryptionData(source.getIdentityCardNumber(), EncodeTypeEnum.IDENTITY_NUMBER));
        }
        if (ReservationRoleEnum.VISITOR.getKey().equals(source.getReservationRole())){
            destination.setDriver("");
        }
        if (source.getReservationCount() != null) {
            destination.setReservationCount(source.getReservationCount().stripTrailingZeros().toPlainString());
        }

    }
}
