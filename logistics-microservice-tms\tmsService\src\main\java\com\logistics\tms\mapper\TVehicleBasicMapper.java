package com.logistics.tms.mapper;

import com.logistics.tms.controller.vehicleassetmanagement.request.FuzzyQueryVehicleInfoRequestModel;
import com.logistics.tms.controller.vehicleassetmanagement.response.FuzzyQueryVehicleInfoResponseModel;
import com.logistics.tms.biz.vehicleassetmanagement.model.*;
import com.logistics.tms.controller.dispatch.request.VehicleSearchRequestModel;
import com.logistics.tms.controller.dispatch.response.VehicleSearchResponseModel;
import com.logistics.tms.controller.vehicleassetmanagement.request.SearchTrailerVehicleRequestModel;
import com.logistics.tms.controller.vehicleassetmanagement.response.*;
import com.logistics.tms.entity.TVehicleBasic;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TVehicleBasicMapper extends BaseMapper<TVehicleBasic>{

    AssetsBoardResponseModel getVehicleStatics(@Param("companyCarrierId") Long companyCarrierId);

    List<VehicleRelationInfoModel>  getVehicleRelationInfoByVehicleNo(@Param("vehicleNo") String vehicleNo);

    List<VehicleRelationInfoModel>  getVehicleRelationInfoByVehicleIds(@Param("vehicleIds") List<Long> vehicleIds);

    List<VehicleAssetManagementListResponseModel> getVehicleBasicByIds(@Param("ids") String ids);

    List<VehicleAssetManagementListResponseModel> getVehicleBasicByVehicleNos(@Param("vehicleNos") String vehicleNos);

    List<GetVehicleIfCompleteByIdsModel> getVehicleLicenseComplete(@Param("ids") String ids);
    List<GetVehicleIfCompleteByIdsModel> getVehicleTransportCertificateComplete(@Param("ids") String ids);
    List<GetVehicleIfCompleteByIdsModel> getVehicleGradeEstimationComplete(@Param("ids") String ids);

    VehicleAssetManagementDetailResponseModel getVehicleBasicDetailByCarrierVehicleId(@Param("carrierVehicleId") Long carrierVehicleId);

    VehicleBasicModel getVehicleBasicModelById(@Param("vehicleBasicId") Long vehicleBasicId);

    VehicleDrivingLicenseModel getVehicleDrivingLicenseModelById(@Param("vehicleBasicId")Long vehicleBasicId);
    VehicleRoadTransportCertificateModel getVehicleRoadTransportCertificateModelById(@Param("vehicleBasicId")Long vehicleBasicId);

    List<VehicleBasicModel> getVehicleBasicModelByIds(@Param("vehicleBasicIds") String vehicleBasicIds);
    List<VehicleDrivingLicenseModel> getVehicleDrivingLicenseModelByIds(@Param("vehicleBasicIds")String vehicleBasicIds);
    List<VehicleRoadTransportCertificateModel> getVehicleRoadTransportCertificateModelByIds(@Param("vehicleBasicIds")String vehicleBasicIds);


    int updateByPrimaryKeySelectiveExt(@Param("params") TVehicleBasic vehicleBasic);
    int updateByPrimaryKeySelectiveExtTwo(@Param("params") TVehicleBasic vehicleBasic);

    TVehicleBasic getInfoByVehicleNo(@Param("vehicleNo") String vehicleNo);

    int deleteVehicleBasicInfo(@Param("vehicleIds") String vehicleIds, @Param("modifiedBy") String modifiedBy, @Param("modifiedTime") Date modifiedTime);

    List<FuzzyQueryVehicleInfoResponseModel> fuzzyQueryVehicleInfo(@Param("params") FuzzyQueryVehicleInfoRequestModel requestModel);

    List<FuzzyQueryVehicleInfoResponseModel> fuzzyQueryVehicleInfoByVehicleNo(@Param("vehicleNo") String vehicleNo);

    List<FuzzyQueryVehicleInfoResponseModel> queryVehicleInfoByVehicleNos(@Param("vehicleNos") String vehicleNos);

    List<ExportGradeEstimationModel> getExportGradeEstimationByVehicleIds(@Param("vehicleIds") String vehicleIds);

    List<ExportDrivingLicenseReviewModel> getExportDrivingLicenseReviewByDrivingIds(@Param("drivingLicenseIds") String drivingLicenseIds);

    List<ExportRoadAnnualModel> getExportRoadAnnualByRoadIds(@Param("roadIds") String roadIds);

    List<ExportGpsRecordModel> findNewlyInstallTimeGpsRecord(@Param("vehicleIds") String vehicleIds);

    VehicleBasicPropertyModel getVehicleBasicPropertyById(@Param("id") Long id);

    List<VehicleBasicPropertyModel> getVehicleNoByIds(@Param("ids")String ids);

    List<VehicleBasicPropertyModel> getVehicleNoByNos(@Param("vehicleNos") String vehicleNos);

    /**
     * 内部，非停运，（牵引车、一体车）车辆数量
     */
    int getInternalTractorCount();

    List<TVehicleBasic> getByIds(@Param("ids") String ids);

    VehicleAssertScrapDetailResponseModel getVehicleScrapDetail(@Param("vehicleId") Long vehicleBasicId);

    List<VehicleSearchResponseModel> searchVehicleForOurCompany(@Param("params") VehicleSearchRequestModel requestModel);

    List<VehicleSearchResponseModel> searchVehicle(@Param("vehicleIds") String vehicleIds, @Param("vehicleNo") String vehicleNo, @Param("companyCarrierId") Long companyCarrierId);

    List<SearchVehicleByPropertyResponseModel> searchVehicleByProperty(@Param("vehicleNo") String vehicleNo, @Param("vehicleProperty") List<Integer> vehicleProperty, @Param("vehicleCategory") String vehicleCategory);

    List<SearchTrailerVehicleResponseModel> searchTrailerVehicle(SearchTrailerVehicleRequestModel requestModel);
}