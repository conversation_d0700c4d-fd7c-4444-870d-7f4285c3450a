package com.logistics.management.webapi.controller.reservebalance.dto.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/5
 */
@Data
public class ReserveBalanceDetailRequestDto extends AbstractPageForm<ReserveBalanceDetailRequestDto> {

	@ApiModelProperty(value = "余额ID", required = true)
	@NotBlank(message = "请选择要查看的记录")
	private String reserveBalanceId;

	@ApiModelProperty(value = "开始月份")
	private String startMonth;

	@ApiModelProperty(value = "结束月份")
	private String endMonth;
}
