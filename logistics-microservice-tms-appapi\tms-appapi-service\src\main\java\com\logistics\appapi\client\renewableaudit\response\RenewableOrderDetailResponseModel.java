package com.logistics.appapi.client.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderDetailResponseModel {

	@ApiModelProperty("乐橘新生订单审核表id")
	private Long renewableAuditId;

	@ApiModelProperty("订单状态：0 待指派，1 待确认，2 待审核，3 已审核")
	private Integer status;

	@ApiModelProperty("订单状态展示文本")
	private String statusLabel;

	//新生客户
	@ApiModelProperty(value = "业务类型:1:公司,2:个人")
	private Integer businessType;
	@ApiModelProperty("乐橘新生客户名称（企业）")
	private String customerName;
	@ApiModelProperty("乐橘新生客户姓名（个人）")
	private String customerUserName;
	@ApiModelProperty("乐橘新生客户手机号（个人）")
	private String customerUserMobile;

	@ApiModelProperty("发货省市区")
	private String loadProvinceName;
	private String loadCityName;
	private String loadAreaName;
	@ApiModelProperty("发货详细地址")
	private String loadDetailAddress;
	@ApiModelProperty("发货仓库")
	private String loadWarehouse;
	@ApiModelProperty("提货经度")
	private String loadLongitude;
	@ApiModelProperty("提货纬度")
	private String loadLatitude;
	@ApiModelProperty("发货人")
	private String consignorName;
	private String consignorMobile;

	@ApiModelProperty("收货省市区")
	private String unloadProvinceName;
	private String unloadCityName;
	private String unloadAreaName;
	@ApiModelProperty("收货地址详细")
	private String unloadDetailAddress;
	@ApiModelProperty("收货仓库")
	private String unloadWarehouse;
	@ApiModelProperty("卸货经度")
	private String unloadLongitude;
	@ApiModelProperty("卸货纬度")
	private String unloadLatitude;
	@ApiModelProperty("收货人")
	private String receiverName;
	private String receiverMobile;

	@ApiModelProperty("货物合计")
	private BigDecimal verifiedGoodsAmountTotal;

	@ApiModelProperty("合计")
	private BigDecimal goodsPriceTotal;

	@ApiModelProperty("货物确认状态: 0:待确认 1:已确认")
	private Integer goodsVerifyStatus;

	@ApiModelProperty("新生订单货物信息")
	private List<RenewableOrderGoodModel> renewableOrderGoods;

	@ApiModelProperty("单据上传状态: 0:待上传 1:已上传")
	private Integer ticketUploadStatus;
	@ApiModelProperty("单据上传状态: 0:待上传 1:已上传")
	private String ticketUploadStatusLabel;

	@ApiModelProperty("新生订单单据信息")
	private List<RenewableOrderTicketModel> renewableOrderTickets;

	@ApiModelProperty("驳回备注")
	private String remark;
}
