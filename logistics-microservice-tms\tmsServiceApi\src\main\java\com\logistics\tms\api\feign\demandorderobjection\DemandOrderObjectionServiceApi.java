package com.logistics.tms.api.feign.demandorderobjection;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionRequestModel;
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionResponseModel;
import com.logistics.tms.api.feign.demandorderobjection.hystrix.DemandOrderObjectionServiceApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "API - DemandOrderObjectionServiceApi-云盘需求单异常管理")
@FeignClient(name = "logistics-tms-services", fallback = DemandOrderObjectionServiceApiHystrix.class)
public interface DemandOrderObjectionServiceApi {

    @ApiOperation(value = "云盘需求单异常列表")
    @PostMapping(value = "/service/demandOrderObjection/searchDemandOrderObjection")
    Result<PageInfo<SearchDemandOrderObjectionResponseModel>> searchDemandOrderObjection(@RequestBody SearchDemandOrderObjectionRequestModel requestModel);

    @ApiOperation(value = "导出云盘需求单异常列表")
    @PostMapping(value = "/service/demandOrderObjection/exportDemandOrderObjection")
    Result<PageInfo<SearchDemandOrderObjectionResponseModel>> exportDemandOrderObjection(@RequestBody SearchDemandOrderObjectionRequestModel requestModel);

}
