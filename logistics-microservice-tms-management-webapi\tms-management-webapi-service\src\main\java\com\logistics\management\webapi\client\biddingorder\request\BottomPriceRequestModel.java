package com.logistics.management.webapi.client.biddingorder.request;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BottomPriceRequestModel {

    /**
     * 竞价单id
     */
    private Long biddingOrderId;


    /**
     * 发货地省id
     */
    private Long loadProvinceId;
    /**
     * 发货地市id
     */
    private Long loadCityId;
    /**
     * 发货地区id
     */
    private Long loadAreaId;


    /**
     * 收货地省id
     */
    private Long unloadProvinceId;
    /**
     * 收货地市id
     */
    private Long unloadCityId;
    /**
     * 收货地区id
     */
    private Long unloadAreaId;

    /**
     * 货物数量
     */
    private BigDecimal goodsCount;




}
