package com.logistics.tms.controller.biddingorder.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/04/26
 */
@Data
public class BiddingOrderDetailResponseModel {
    /**
     * 竞价单id
     */
    private Long biddingOrderId;

    /**
     * 竞价单号
     */
    private String biddingOrderCode;

    /**
     * 竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消
     */
    private Integer biddingStatus;

    /**
     * 竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消
     */
    private String biddingStatusLabel;

    /**
     * 装卸方式 1 一装一卸、2 多装一卸
     */
    private Integer handlingMode;

    /**
     * 装卸方式 1 一装一卸、2 多装一卸
     */
    private String handlingModeLabel;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 倒计时结束时间
     */
    private Long countDownTime;

    /**
     * 车长
     */
    private BigDecimal vehicleLength;

    /**
     * 期望提货时间
     */
    private Date expectedLoadTime;

    /**
     * 期望卸货时间
     */
    private Date expectedUnloadTime;

    /**
     * 备注
     */
    private String remark="";

    /**
     * 需求单列表
     */
    private List<BiddingDemandModel> demandDtoList;


    /**
     * 您的报价记录
     */
    private List<BiddingOrderQuoteListResponseModel> biddingOrderQuoteList;
}
