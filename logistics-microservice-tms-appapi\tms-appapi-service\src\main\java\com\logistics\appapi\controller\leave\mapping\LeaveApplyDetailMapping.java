package com.logistics.appapi.controller.leave.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.LeaveApplyAuditStatusEnum;
import com.logistics.appapi.base.enums.LeaveApplyTimeTypeEnum;
import com.logistics.appapi.base.enums.LeaveApplyTypeEnum;
import com.logistics.appapi.client.leave.response.DriverLeaveApplyDetailResponseModel;
import com.logistics.appapi.controller.leave.response.LeaveApplyDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

import java.util.Date;

public class LeaveApplyDetailMapping extends MapperMapping<DriverLeaveApplyDetailResponseModel, LeaveApplyDetailResponseDto> {

    @Override
    public void configure() {

        DriverLeaveApplyDetailResponseModel source = getSource();
        LeaveApplyDetailResponseDto destination = getDestination();

        // 名字转换
        String leaveApplyStaff = String.format(CommonConstant.NAME_MOBILE_FORMAT, source.getStaffName(), source.getStaffMobile());
        destination.setLeaveApplyStaff(leaveApplyStaff);

        // 审核状态转换
        destination.setLeaveAuditStatusLabel(LeaveApplyAuditStatusEnum.getEnumByKey(source.getLeaveAuditStatus()).getValue());

        // 请假类型转换
        destination.setLeaveTypeLabel(LeaveApplyTypeEnum.getEnumByKey(source.getLeaveType()).getValue());

        // 请假时间转换
        destination.setLeaveStartTime(leaveTimeConversion(source.getLeaveStartTime(), source.getLeaveStartTimeType()));
        destination.setLeaveEndTime(leaveTimeConversion(source.getLeaveEndTime(), source.getLeaveEndTimeType()));

        // 请假时长转换
        String duration = source.getLeaveDuration().stripTrailingZeros().toPlainString();
        destination.setLeaveDuration(duration);
    }

    private String leaveTimeConversion(Date leaveDate, Integer leaveDateType) {
        String leaveDateLabel = DateUtils.dateToString(leaveDate, DateUtils.DATE_TO_STRING_SHORT_PATTERN);
        String leaveDateTypeLabel = LeaveApplyTimeTypeEnum.getEnumByKey(leaveDateType).getValue();
        return String.join(" ", leaveDateLabel, leaveDateTypeLabel);
    }
}
