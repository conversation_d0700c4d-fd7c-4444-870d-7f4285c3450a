package com.logistics.tms.api.feign.violationregulation.model;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: sj
 * @Date: 2019/6/3 10:11
 */
@Data
public class SearchViolationRegulationListResponseModel {
    @ApiModelProperty("违章事故记录数")
    private Integer countViolationRegulations = 0;
    @ApiModelProperty("总的扣分")
    private Integer sumDeduction = 0;
    @ApiModelProperty("总的罚款")
    private BigDecimal sumFine = BigDecimal.ZERO;
    @ApiModelProperty("违章事故记录列表")
    private PageInfo<ViolationRegulationListResponseModel> pageInfo;
}






