package com.logistics.management.webapi.api.impl.vehicleassetmanagement.mapper;

import com.logistics.management.webapi.api.feign.vehicleassetmanagement.dto.VehicleAssertScrapDetailResponseDto;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.tms.api.feign.vehicleassetmanagement.model.VehicleAssertScrapDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author:lei.zhu
 * @date:2021/7/22 18:07
 */
public class VehicleScrapDetailMapping extends MapperMapping<VehicleAssertScrapDetailResponseModel, VehicleAssertScrapDetailResponseDto> {
    private ConfigKeyConstant configKeyConstant;
    private Map<String, String> imageMap;
    public VehicleScrapDetailMapping(ConfigKeyConstant configKeyConstant, Map<String, String> imageMap) {
        this.configKeyConstant = configKeyConstant;
        this.imageMap=imageMap;
    }
    @Override
    public void configure() {
        VehicleAssertScrapDetailResponseModel source = getSource();
        VehicleAssertScrapDetailResponseDto destination = getDestination();
        if(ListUtils.isNotEmpty(source.getPathList())){
            List<String> filePathList=new ArrayList<>();
            source.getPathList().forEach(item->{
                filePathList.add(configKeyConstant.fileAccessAddress+imageMap.get(item));
            });
            destination.setPathList(filePathList);
        }
    }
}
