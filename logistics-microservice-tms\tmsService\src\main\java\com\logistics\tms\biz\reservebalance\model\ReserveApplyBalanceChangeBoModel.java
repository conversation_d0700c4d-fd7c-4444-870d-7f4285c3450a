package com.logistics.tms.biz.reservebalance.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 备用金申请余额变更
 */
@Data
@Accessors(chain = true)
public class ReserveApplyBalanceChangeBoModel {

    /**
     * 备用金申请编号
     */
    private String reserveCode;

    /**
     * 备用金余额
     */
    private BigDecimal balance;

    /**
     * 备用金冲销金额
     */
    private BigDecimal writeOffAmount;
}
