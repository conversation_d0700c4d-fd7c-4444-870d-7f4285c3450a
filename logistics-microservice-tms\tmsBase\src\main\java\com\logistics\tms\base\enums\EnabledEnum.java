package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2018/9/29 10:46
 */
public enum EnabledEnum {
    ENABLED(1, "启用"),
    DISABLED(0, "禁用"),;

    private Integer key;
    private String value;

    EnabledEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
