package com.logistics.appapi.controller.drivercostapply.response;

import com.logistics.appapi.controller.uploadfile.response.SrcUrlDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/3 9:28
 */
@Data
public class DriverCostApplyDetailResponseDto {

    @ApiModelProperty("司机费用申请表id")
    private String driverCostApplyId = "";

    //申请费用信息
    @ApiModelProperty("申请人")
    private String staffName = "";
    @ApiModelProperty("车辆id")
    private String vehicleId = "";
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("发生时间")
    private String occurrenceTime = "";
    @ApiModelProperty("费用类型：100 住宿费，101 装卸费，102 其他费用，103 加班费，104 劳保费，105 电话费，106 交通费， 107 打印费，108 叉车费，109 盖雨布， 110破包赔偿，111 交通罚款；200 维修费，201 车辆保养费，202 过路过桥费，203 停车费，204 尿素费，205 加油费；300 核酸检测费，301 医疗防护用品；400 扣款")
    private String costType = "";
    @ApiModelProperty("费用类型")
    private String costTypeLabel = "";
    @ApiModelProperty("申请费用")
    private String applyCost = "";
    @ApiModelProperty("申请时间")
    private String applyTime = "";
    @ApiModelProperty("申请说明")
    private String applyRemark = "";
    @ApiModelProperty("申请依据")
    private List<DriverCostApplyTickDetail> ticketList;

    //审核信息
    @ApiModelProperty("审核状态：-1 待业务审核，0 待财务审核，1 已审核，2 已驳回，3 已撤销，4 已红冲")
    private String auditStatus = "";
    @ApiModelProperty("审核状态")
    private String auditStatusLabel = "";
    @ApiModelProperty("审核人")
    private String auditorName = "";
    @ApiModelProperty("备注")
    private String remark = "";
    @ApiModelProperty("审核时间")
    private String auditTime = "";

    @ApiModelProperty("发票信息; 1.1.8新增")
    private List<DriverCostApplyInvoiceResponseDto> invoiceInfoList;

    @ApiModelProperty("发票数量; 1.1.8新增")
    private String invoiceCount = "0";

    @ApiModelProperty("发票总价税合计; 1.1.8新增")
    private String invoiceTotalTaxAndPrice = "0";

    @Data
    public static class DriverCostApplyTickDetail {

        @ApiModelProperty(value = "图片类型: 1 现场图片, 2 支付图片; 1.1.8删除(3 发票图片)")
        private String type;

        @ApiModelProperty(value = "图片依据路径")
        private List<SrcUrlDto> imagePathList;
    }
}
