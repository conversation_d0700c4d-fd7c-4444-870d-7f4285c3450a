package com.logistics.management.webapi.controller.oilfilled.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OilFilledOperationRecordResponseDto {

    @ApiModelProperty(value = "充油记录ID")
    private String oilFilledId = "";
    @ApiModelProperty(value = "车牌号")
    private String vehicleNo = "";
    @ApiModelProperty(value = "司机姓名")
    private String name = "";
    @ApiModelProperty(value = "司机手机号")
    private String mobile = "";
    @ApiModelProperty(value = "充油金额")
    private String oilFilledFee = "";
    @ApiModelProperty(value = "充值时间")
    private String oilFilledDate = "";
    @ApiModelProperty(value = "充油方式")
    private String oilFilledType = "";
    @ApiModelProperty(value = "充油方式文字")
    private String oilFilledTypeLabel = "";
    @ApiModelProperty(value = "升数")
    private String liter = "";
    @ApiModelProperty(value = "充值积分")
    private String topUpIntegral = "";
    @ApiModelProperty(value = "奖励积分")
    private String rewardIntegral = "";
    @ApiModelProperty(value = "副卡卡号")
    private String subCardNumber = "";
    @ApiModelProperty(value = "副卡所属人")
    private String subCardOwner = "";
    @ApiModelProperty(value = "备注")
    private String remark = "";
    @ApiModelProperty(value = "操作人")
    private String lastModifiedBy = "";
    @ApiModelProperty(value = "操作时间")
    private String lastModifiedTime = "";

}
