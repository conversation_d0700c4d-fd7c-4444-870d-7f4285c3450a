package com.logistics.management.webapi.controller.oilfilled.mapping;

import com.logistics.management.webapi.base.enums.OilFilledTypeEnum;
import com.logistics.management.webapi.base.enums.OilRefundReasonTypeEnum;
import com.logistics.management.webapi.client.oilfilled.response.OilRefundDetailResponseModel;
import com.logistics.management.webapi.controller.oilfilled.response.OilRefundDetailResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/12/23 16:37
 */
public class OilRefundDetailMapping extends MapperMapping<OilRefundDetailResponseModel, OilRefundDetailResponseDto> {

    private final String imagePrefix;
    private final Map<String, String> picOSSPathMap;

    public OilRefundDetailMapping(String imagePrefix, Map<String, String> picOSSPathMap) {
        this.imagePrefix = imagePrefix;
        this.picOSSPathMap = picOSSPathMap;
    }

    @Override
    public void configure() {
        OilRefundDetailResponseModel source = getSource();
        OilRefundDetailResponseDto destination = getDestination();

        if (source != null) {
            if (source.getOilFilledType() != null) {
                destination.setOilFilledTypeLabel(OilFilledTypeEnum.getEnum(source.getOilFilledType()).getValue());
            }
            if (source.getOilFilledDate() != null) {
                destination.setOilFilledDate(DateUtils.dateToString(source.getOilFilledDate(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            //拼接司机姓名跟手机号
            destination.setName(source.getName() + " " + source.getMobile());
            if (source.getRefundReasonType() != null) {
                destination.setRefundReasonTypeDesc(OilRefundReasonTypeEnum.getEnum(source.getRefundReasonType()).getValue());
            }
            if (StringUtils.isNotBlank(source.getRefundFile())){
                destination.setRefundFilePath(imagePrefix + picOSSPathMap.get(source.getRefundFile()));
            }
        }
    }
}
