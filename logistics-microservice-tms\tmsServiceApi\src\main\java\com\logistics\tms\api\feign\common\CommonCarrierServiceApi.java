package com.logistics.tms.api.feign.common;

import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.common.hystrix.CommonCarrierServiceApiHystrix;
import com.logistics.tms.api.feign.common.model.LoginUserCompanyCarrierBasicInfoResponseModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@Api(value = "API-CommonCarrierServiceApi")
@FeignClient(name = "logistics-tms-services",fallback = CommonCarrierServiceApiHystrix.class)
public interface CommonCarrierServiceApi {

    @ApiOperation(value="根据当前登陆人查询车主ID")
    @PostMapping(value = "/service/commonCarrier/getLoginUserCompanyCarrierId")
    Result<Long> getLoginUserCompanyCarrierId();

    @ApiOperation(value="根据当前登陆人查询车主基本信息")
    @PostMapping(value = "/service/commonCarrier/getLoginUserCompanyCarrierBasicInfo")
    Result<LoginUserCompanyCarrierBasicInfoResponseModel> getLoginUserCompanyCarrierBasicInfo();


}
