package com.logistics.tms.biz.website.demand;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.website.demand.request.AddDemandSourceRequestModel;
import com.logistics.tms.controller.website.demand.request.DemandSourceListRequestModel;
import com.logistics.tms.controller.website.demand.response.DemandSourceListResponseModel;
import com.logistics.tms.entity.TDemandSource;
import com.logistics.tms.mapper.TDemandSourceMapper;
import com.yelo.tools.mapper.utils.MapperUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: zhongwu.wu
 * @Date: 2019/10/31
 * @description:
 */
@Service
public class DemandSourceBiz {

    @Resource
    private TDemandSourceMapper tDemandSourceMapper;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 货源列表接口
     * @param requestModel
     * @return
     */
    public PageInfo<DemandSourceListResponseModel> searchList(DemandSourceListRequestModel requestModel) {
        requestModel.enablePaging();
        List<DemandSourceListResponseModel> demandSourceListResponseModels = tDemandSourceMapper.searchList();
        return new PageInfo<>(demandSourceListResponseModels);
    }

    /**
     * 发布货源
     * @param requestModel
     */
    @Transactional
    public void add(AddDemandSourceRequestModel requestModel) {
        TDemandSource tDemandSource = MapperUtils.mapper(requestModel, TDemandSource.class);
        commonBiz.setBaseEntityAdd(tDemandSource, CommonConstant.WEBSITE);
        tDemandSourceMapper.insertSelective(tDemandSource);
    }

}
