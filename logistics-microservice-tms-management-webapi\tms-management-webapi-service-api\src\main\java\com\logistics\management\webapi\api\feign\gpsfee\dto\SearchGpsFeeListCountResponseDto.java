package com.logistics.management.webapi.api.feign.gpsfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/8 13:32
 */
@Data
public class SearchGpsFeeListCountResponseDto {
    @ApiModelProperty("总数量")
    private String allSettlementCount="0";
    @ApiModelProperty("待结算数量")
    private String waitSettlementCount="0";
    @ApiModelProperty("部分结算数量")
    private String partSettlementCount="0";
    @ApiModelProperty("结算完成数量")
    private String completeSettlementCount="0";
    @ApiModelProperty("已终止数量")
    private String terminationCount="0";
}
