package com.logistics.management.webapi.api.feign.dispatchorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by yuhong.lin on 2019/1/21
 */
@Data
public class OperateLogsResponseDto {
    @ApiModelProperty("日志id")
    private String operateLogsId;
    @ApiModelProperty("操作人")
    private String operateUserName;
    @ApiModelProperty("操作时间")
    private String operateTime;
    @ApiModelProperty("操作动作")
    private String operateType;
    @ApiModelProperty("说明")
    private String operateContents;
}
