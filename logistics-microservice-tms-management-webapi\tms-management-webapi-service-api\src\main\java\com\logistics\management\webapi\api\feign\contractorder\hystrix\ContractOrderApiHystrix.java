package com.logistics.management.webapi.api.feign.contractorder.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.contractorder.ContractOrderApi;
import com.logistics.management.webapi.api.feign.contractorder.dto.*;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;

@Component
public class ContractOrderApiHystrix implements ContractOrderApi{

    @Override
    public Result<PageInfo<ContractOrderSearchResponseDto>> searchContractOrderList(ContractOrderSearchRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<ContractOrderDetailResponseDto> getDetail(ContractOrderDetailRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result saveContract(AddOrModifyContractOrderRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result terminateOrCancelContract(TerminateOrCancelContractRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void exportContractOrder(ContractOrderSearchRequestDto requestDto, HttpServletResponse response) {
        Result.timeout();
    }
}
