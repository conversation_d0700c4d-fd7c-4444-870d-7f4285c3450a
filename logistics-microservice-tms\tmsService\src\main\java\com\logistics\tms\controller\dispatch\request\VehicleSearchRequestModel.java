package com.logistics.tms.controller.dispatch.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VehicleSearchRequestModel  extends AbstractPageForm<VehicleSearchRequestModel> {

    @ApiModelProperty(value = "车辆机构 1 自主，2 外部，3 自营")
    private String vehicleProperty;

    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;

    @ApiModelProperty(value = "车主ID")
    private Long companyCarrierId;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    //拆分表查询用字段, 车辆类型id
    private String vehicleTypeIds;
}
