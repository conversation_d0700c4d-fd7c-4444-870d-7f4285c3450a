package com.logistics.tms.api.feign.vehicleoilcard.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2022/8/3 11:23
 */
@Data
public class GetVehicleOilCardRecordResponseModel {
    @ApiModelProperty("操作")
    private Integer operateType;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("操作人")
    private String operateUserName;
    @ApiModelProperty("操作时间")
    private Date operateTime;
}
