package com.logistics.management.webapi.api.impl.loanrecord.mapping;

import com.logistics.management.webapi.api.feign.loanrecord.dto.LoanRecordListResponseDto;
import com.logistics.management.webapi.base.enums.LoanStatusEnum;
import com.logistics.tms.api.feign.loanrecord.model.LoanRecordListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

import java.util.Optional;

/**
 * @Author: sj
 * @Date: 2019/9/30 10:00
 */
public class LoanRecordListMapping extends MapperMapping<LoanRecordListResponseModel,LoanRecordListResponseDto> {
    @Override
    public void configure() {
        LoanRecordListResponseModel source = this.getSource();
        LoanRecordListResponseDto target = this.getDestination();
        if(source!=null){
            if(source.getLastModifiedTime()!=null){
               target.setLastModifiedTime(DateUtils.dateToString(source.getLastModifiedTime(),DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
            }
            target.setLoanRate(target.getLoanRate()+"%");
            target.setBrandModel(Optional.ofNullable(target.getBrand()).orElse("")+Optional.ofNullable(target.getModel()).orElse(""));
            target.setStatusLabel(LoanStatusEnum.getEnum(source.getStatus()).getValue());
            target.setDriverLabel(Optional.ofNullable(target.getName()).orElse("")+" "+ Optional.ofNullable(target.getMobile()).orElse("") );
            if(source.getRemainingRepaymentFee() == null){
                target.setRemainingRepaymentFee(target.getLoanFee());
            }
        }
    }
}
