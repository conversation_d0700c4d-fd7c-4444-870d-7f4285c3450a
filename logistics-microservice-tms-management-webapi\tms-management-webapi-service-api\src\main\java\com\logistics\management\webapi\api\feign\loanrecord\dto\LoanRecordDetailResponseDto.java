package com.logistics.management.webapi.api.feign.loanrecord.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/9/30 9:39
 */
@Data
public class LoanRecordDetailResponseDto {
    @ApiModelProperty("贷款记录ID")
    private String loanRecordId = "";
    @ApiModelProperty("贷款状态：0 待结算，1 部分结算，2 已结算")
    private String status = "";
    @ApiModelProperty("贷款状态文本")
    private String statusLabel = "";
    @ApiModelProperty("车辆ID")
    private String vehicleId = "";
    @ApiModelProperty("车牌号")
    private String vehicleNo = "";
    @ApiModelProperty("品牌")
    private String brand = "";
    @ApiModelProperty("型号")
    private String model = "";
    @ApiModelProperty("车辆识别号")
    private String vehicleIdentificationNumber = "";
    @ApiModelProperty("发动机号码")
    private String engineNumber = "";
    @ApiModelProperty("车身颜色")
    private String bodyColor = "";
    @ApiModelProperty("生产地")
    private String producer = "";
    @ApiModelProperty("生产厂商")
    private String manufacturers = "";
    @ApiModelProperty("生产日期")
    private String productionDate = "";
    @ApiModelProperty("司机id")
    private String staffId = "";
    @ApiModelProperty("司机姓名")
    private String name = "";
    @ApiModelProperty("手机号")
    private String mobile = "";
    @ApiModelProperty("司机文本：姓名+手机号拼接")
    private String driverLabel = "";
    @ApiModelProperty("身份证号码")
    private String identityNumber = "";
    @ApiModelProperty("裸车价")
    private String nakedCarPrice = "";
    @ApiModelProperty("保险费")
    private String insurancePremium = "";
    @ApiModelProperty("购置税")
    private String purchaseTax = "";
    @ApiModelProperty("购车总价")
    private String carPrice = "";
    @ApiModelProperty("司机已承担费用")
    private String driverExpense = "";
    @ApiModelProperty("总贷款费用")
    private String loanFee = "";
    @ApiModelProperty("贷款总期数")
    private String loanPeriods = "";
    @ApiModelProperty("贷款开始时间")
    private String loanStartTime = "";
    @ApiModelProperty("贷款利率")
    private String loanRate = "";
    @ApiModelProperty("贷款手续费")
    private String loanCommission = "";
    @ApiModelProperty("贷款利息")
    private String loanInterest = "";
    @ApiModelProperty("备注")
    private String remark = "";
    @ApiModelProperty("操作人")
    private String lastModifiedBy = "";
    @ApiModelProperty("操作时间")
    private String lastModifiedTime = "";
    @ApiModelProperty("是否有账单月数据,1有，0没有")
    private String ifSettlement = "0";
    @ApiModelProperty("附件列表")
    private List<LoanRecordFileResponseDto> fileList;
}
