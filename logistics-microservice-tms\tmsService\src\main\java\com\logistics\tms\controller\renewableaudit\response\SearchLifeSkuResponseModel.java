package com.logistics.tms.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/17
 */
@Data
public class SearchLifeSkuResponseModel {

	@ApiModelProperty("sku编号(code)")
	private String skuCode;

	@ApiModelProperty("sku名")
	private String skuName;

	@ApiModelProperty("建议单价")
	private BigDecimal suggestGoodsPrice;

	@ApiModelProperty("建议单价单位")
	private Integer suggestGoodsPriceUnit;
}
