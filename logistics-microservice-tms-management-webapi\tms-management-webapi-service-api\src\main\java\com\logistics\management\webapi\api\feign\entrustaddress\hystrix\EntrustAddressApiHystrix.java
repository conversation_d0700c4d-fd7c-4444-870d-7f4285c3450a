package com.logistics.management.webapi.api.feign.entrustaddress.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.entrustaddress.EntrustAddressApi;
import com.logistics.management.webapi.api.feign.entrustaddress.dto.GetAddressByCompanyNameOrWarehouseRequestDto;
import com.logistics.management.webapi.api.feign.entrustaddress.dto.GetAddressByCompanyNameOrWarehouseResponseDto;
import com.logistics.management.webapi.api.feign.entrustaddress.dto.SearchEntrustAddressRequestDto;
import com.logistics.management.webapi.api.feign.entrustaddress.dto.SearchEntrustAddressResponseDto;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: wjf
 * @date: 2019/9/19 14:29
 */
@Component
public class EntrustAddressApiHystrix implements EntrustAddressApi {
    @Override
    public Result<PageInfo<GetAddressByCompanyNameOrWarehouseResponseDto>> getAddressByCompanyNameOrWarehouse(GetAddressByCompanyNameOrWarehouseRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<PageInfo<SearchEntrustAddressResponseDto>> searchList(SearchEntrustAddressRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public void export(SearchEntrustAddressRequestDto requestDto, HttpServletResponse response) {
        Result.failThrow();
    }
}
