package com.logistics.tms.biz.vehiclesafecheck;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.violationregulation.model.UpdateCertificationPicturesModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel;
import com.logistics.tms.controller.staffvehiclerelation.response.SafeCheckStaffRelResponseModel;
import com.logistics.tms.controller.vehiclesafecheck.request.*;
import com.logistics.tms.controller.vehiclesafecheck.response.*;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.basicdata.api.feign.user.UserServiceApi;
import com.yelo.basicdata.api.feign.user.model.GetUserByIdsRequestModel;
import com.yelo.basicdata.api.feign.user.model.GetUserByIdsResponseModel;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tools.utils.UUIDGenerateUtil;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sj
 * @Date: 2019/11/8 10:26
 */
@Service
public class SafeCheckBiz {
    
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private TVehicleSafeCheckMapper tVehicleSafeCheckMapper;
    @Resource
    private TVehicleSafeCheckVehicleMapper tVehicleSafeCheckVehicleMapper;
    @Resource
    private TVehicleSafeCheckItemMapper tVehicleSafeCheckItemMapper;
    @Resource
    private TVehicleSafeCheckReformMapper tVehicleSafeCheckReformMapper;
    @Resource
    private TVehicleBasicMapper tVehicleBasicMapper;
    @Resource
    private TStaffVehicleRelationMapper tStaffVehicleRelationMapper;
    @Resource
    private TCertificationPicturesMapper tCertificationPicturesMapper;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private UserServiceApi userServiceApi;

    /**
     * 列表
     * @param requestModel
     * @return
     */
    public PageInfo<SearchSafeCheckListResponseModel> searchList(SearchSafeCheckListRequestModel requestModel) {
        requestModel.enablePaging();
        List<SearchSafeCheckListResponseModel> resultList = tVehicleSafeCheckVehicleMapper.searchList(requestModel);
        return new PageInfo<>(resultList);
    }

    /**
     * 列表汇总
     * @param requestModel
     * @return
     */
    public SummarySafeCheckResponseModel getListSummary(SearchSafeCheckListRequestModel requestModel) {
        return tVehicleSafeCheckVehicleMapper.getListSummary(requestModel);
    }

    /**
     * 新建安全检查
     * @param requestModel
     */
    @Transactional
    public void addSafeCheck(AddSafeCheckRequestModel requestModel) {
        if(ListUtils.isEmpty(requestModel.getVehicleIdList())){
            throw new BizException(CarrierDataExceptionEnum.INTERNAL_VEHICLE_ERROR);
        }

        if(tVehicleSafeCheckMapper.getCountBySpecifyPeriod(requestModel.getPeriod()) > CommonConstant.INTEGER_ZERO){
            throw new BizException(CarrierDataExceptionEnum.SPECIFY_PERIOD_SAFE_CHECK_INFO_EXIST);
        }

        List<VehicleBasicPropertyModel> vehicleBasicList = tVehicleBasicMapper.getVehicleNoByIds(StringUtils.listToString(requestModel.getVehicleIdList(),','));
        if(ListUtils.isEmpty(vehicleBasicList) || vehicleBasicList.size() !=requestModel.getVehicleIdList().size()){
           throw new BizException(CarrierDataExceptionEnum.SELECT_VEHICLE_INFO_NOT_MATCHING);
        }

        Date now = new Date();
        String addUser = BaseContextHandler.getUserName();

        //新增检查批次
        TVehicleSafeCheck addTVehicleSafeCheck = new TVehicleSafeCheck();
        addTVehicleSafeCheck.setPeriod(requestModel.getPeriod());
        addTVehicleSafeCheck.setVehicleCount(tVehicleBasicMapper.getInternalTractorCount());
        addTVehicleSafeCheck.setBatchUuid(UUIDGenerateUtil.generateUUID());
        addTVehicleSafeCheck.setLastModifiedBy(addUser);
        addTVehicleSafeCheck.setLastModifiedTime(now);
        addTVehicleSafeCheck.setCreatedBy(addUser);
        addTVehicleSafeCheck.setCreatedTime(now);
        tVehicleSafeCheckMapper.insertSelective(addTVehicleSafeCheck);

        //检查项
        List<TVehicleSafeCheckItem> checkItemList = new ArrayList<>();
        SafeCheckItemEnum[] enumList = SafeCheckItemEnum.values();
        TVehicleSafeCheckItem checkItem;
        for (SafeCheckItemEnum itemEnum : enumList) {
            checkItem =new TVehicleSafeCheckItem();
            checkItem.setItemType(itemEnum.getItemType());
            checkItem.setStatus(SafeCheckItemStatusEnum.NOT_CHECK.getKey());
            checkItem.setLastModifiedBy(addUser);
            checkItem.setLastModifiedTime(now);
            checkItem.setCreatedBy(addUser);
            checkItem.setCreatedTime(now);
            checkItemList.add(checkItem);
        }

        //车辆司机关联关系
        List<SafeCheckStaffRelResponseModel> relList = tStaffVehicleRelationMapper.findRelList4SafeCheck(StringUtils.listToString(requestModel.getVehicleIdList(),','));
        if(ListUtils.isEmpty(relList)){
            throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
        }
        Map<Long,SafeCheckStaffRelResponseModel> relMap = new HashMap<>();
        for (SafeCheckStaffRelResponseModel tempModel : relList) {
            relMap.put(tempModel.getVehicleId(),tempModel);
        }

        //车辆安全检查车辆表 与 检查项目
        for (VehicleBasicPropertyModel tempModel : vehicleBasicList) {
            //是否停运
            if (OperatingStateEnum.OUT_OPERATION.getKey().equals(tempModel.getOperatingState())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLES_ARE_OUT_OF_SERVICE);
            }
            //是否自主或自营
            if (!(VehiclePropertyEnum.OWN_VEHICLE.getKey().equals(tempModel.getVehicleProperty()) ||
                    VehiclePropertyEnum.AFFILIATION_VEHICLE.getKey().equals(tempModel.getVehicleProperty()))) {
                throw new BizException(CarrierDataExceptionEnum.INTERNAL_VEHICLE_ERROR);
            }
            //是否牵引车或者一体车
            if (!VehicleCategoryEnum.WHOLE.getKey().equals(tempModel.getVehicleCategory()) && !VehicleCategoryEnum.TRACTOR.getKey().equals(tempModel.getVehicleCategory())) {
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_UNFINISH_CHECK_NOT_ALLOW_OUTAGE);
            }
            //司机车辆关系
            SafeCheckStaffRelResponseModel relModel = relMap.get(tempModel.getVehicleId());
            if (relModel == null) {
                throw new BizException(CarrierDataExceptionEnum.STAFF_VEHICLE_RELATION_NOT_EXIST);
            }

            TVehicleSafeCheckVehicle addTVehicleSafeCheckVehicle = new TVehicleSafeCheckVehicle();
            addTVehicleSafeCheckVehicle.setSafeCheckId(addTVehicleSafeCheck.getId());
            addTVehicleSafeCheckVehicle.setVehicleId(tempModel.getVehicleId());
            addTVehicleSafeCheckVehicle.setVehicleProperty(tempModel.getVehicleProperty());
            addTVehicleSafeCheckVehicle.setStaffId(relModel.getStaffId());
            addTVehicleSafeCheckVehicle.setStaffName(relModel.getStaffName());
            addTVehicleSafeCheckVehicle.setStaffMobile(relModel.getStaffPhone());
            addTVehicleSafeCheckVehicle.setVehicleNo(relModel.getVehicleNo());
            addTVehicleSafeCheckVehicle.setTrailerVehicleNo(relModel.getTrailerVehicleNo());
            addTVehicleSafeCheckVehicle.setStatus(SafeCheckStatusEnum.NOT_CHECK.getKey());
            addTVehicleSafeCheckVehicle.setLastModifiedBy(addUser);
            addTVehicleSafeCheckVehicle.setLastModifiedTime(now);
            addTVehicleSafeCheckVehicle.setCreatedBy(addUser);
            addTVehicleSafeCheckVehicle.setCreatedTime(now);
            tVehicleSafeCheckVehicleMapper.insertSelective(addTVehicleSafeCheckVehicle);

            checkItemList.forEach(tVehicleSafeCheckItem -> tVehicleSafeCheckItem.setSafeCheckVehicleId(addTVehicleSafeCheckVehicle.getId()));
            tVehicleSafeCheckItemMapper.batchInsert(checkItemList);
        }
    }

    /**
     * 删除安全检查
     * @param requestModel
     */
    @Transactional
    public void delSafeCheck(DelSafeCheckRequestModel requestModel) {
        TVehicleSafeCheckVehicle  tVehicleSafeCheckVehicle = tVehicleSafeCheckVehicleMapper.selectByPrimaryKey(requestModel.getSafeCheckVehicleId());
        if(tVehicleSafeCheckVehicle == null || IfValidEnum.INVALID.getKey().equals(tVehicleSafeCheckVehicle.getValid())){
            throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_STATUS_HAS_CHANGE);
        }
        tVehicleSafeCheckVehicleMapper.delSafeCheck(tVehicleSafeCheckVehicle.getId(),BaseContextHandler.getUserName(),new Date());

        List<TVehicleSafeCheckVehicle> checkVehicleList = tVehicleSafeCheckVehicleMapper.getListByCheckId(tVehicleSafeCheckVehicle.getSafeCheckId());
        if(ListUtils.isEmpty(checkVehicleList)){
            TVehicleSafeCheck upTVehicleSafeCheck = new TVehicleSafeCheck();
            upTVehicleSafeCheck.setId(tVehicleSafeCheckVehicle.getSafeCheckId());
            upTVehicleSafeCheck.setValid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(upTVehicleSafeCheck,BaseContextHandler.getUserName());
            tVehicleSafeCheckMapper.updateByPrimaryKeySelective(upTVehicleSafeCheck);
        }
    }

    /**
     * 详情: 未检查完成的车辆不能删除,以及修改车辆机构
     * @param requestModel
     * @return
     */
    public SafeCheckDetailResponseModel getDetail(SafeCheckDetailRequestModel requestModel) {
        SafeCheckDetailResponseModel retModel = tVehicleSafeCheckVehicleMapper.getDetail(requestModel.getSafeCheckVehicleId(),CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_VEHICLE.getObjectType());
        if(retModel == null){
            throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_INFO_NOT_EXIST);
        }

        if(retModel.getCheckReformInfo() != null){
            SafeCheckReformResponseModel checkReformInfo = retModel.getCheckReformInfo();
            checkReformInfo.setItemFileList(this.getFileList(checkReformInfo.getCheckReformId(), CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_REFORM.getObjectType(), CertificationPicturesFileTypeEnum.SAFE_CHECK_ITEM_REFORM_IMAGE_FILE.getFileType()));
            checkReformInfo.setResultFileList(this.getFileList(checkReformInfo.getCheckReformId(),CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_REFORM.getObjectType(), CertificationPicturesFileTypeEnum.SAFE_CHECK_REFORM_IMAGE_FILE.getFileType()));
        }

        if( retModel.getCheckUserId() != null && !retModel.getCheckUserId().equals(CommonConstant.LONG_ZERO)){
            GetUserByIdsRequestModel  getUserByIdsRequestModel = new GetUserByIdsRequestModel();
            getUserByIdsRequestModel.setUserIds(Arrays.asList(retModel.getCheckUserId()));
            Result<List<GetUserByIdsResponseModel>> result = userServiceApi.getUserByIds(getUserByIdsRequestModel);
            if(result.isSuccess() && ListUtils.isNotEmpty(result.getData())){
                retModel.setCheckUserMobile(result.getData().get(CommonConstant.INTEGER_ZERO).getMobilePhone());
            }
        }

        return retModel;
    }

    /**
     * 获取图片信息
     * @param checkReformId
     * @param objectType
     * @param fileType
     * @return
     */
    public List<SafeCheckFileResponseModel> getFileList(Long checkReformId, Integer objectType, Integer fileType){
        List<SafeCheckFileResponseModel> retList = new ArrayList<>();
        List<TCertificationPictures> fileList = tCertificationPicturesMapper.getByObjectIdType(checkReformId,objectType,fileType);
        if(ListUtils.isEmpty(fileList)){
           return retList;
        }

        SafeCheckFileResponseModel retModel;
        for (TCertificationPictures tempFile : fileList) {
            retModel = new SafeCheckFileResponseModel();
            retModel.setFileId(tempFile.getId());
            retModel.setRelativeFilepath(tempFile.getFilePath());
            retList.add(retModel);
        }
        return  retList;
    }

    /**
     * 待检查/待整改 - 提交
     * @param requestModel
     */
    @Transactional
    public void addSafeCheckDetail(AddSafeCheckDetailRequestModel requestModel) {

        TVehicleSafeCheckVehicle dbTVehicleSafeCheckVehicle = tVehicleSafeCheckVehicleMapper.selectByPrimaryKey(requestModel.getSafeCheckVehicleId());
        if(dbTVehicleSafeCheckVehicle == null || IfValidEnum.INVALID.getKey().equals(dbTVehicleSafeCheckVehicle.getValid())){
          throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_INFO_NOT_EXIST);
        }

        if(!SafeCheckStatusEnum.NOT_CHECK.getKey().equals(dbTVehicleSafeCheckVehicle.getStatus())
                && !SafeCheckStatusEnum.WAIT_CONFIRM.getKey().equals(dbTVehicleSafeCheckVehicle.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_STATUS_HAS_CHANGE);
        }

        if(ListUtils.isEmpty(requestModel.getItemList())){
            throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_ITEM_INFO_NOT_EXIST);
        }

        Map<Integer,Integer> pageItemMap = new HashMap<>();
        for (SafeCheckItemRequestModel tempModel: requestModel.getItemList()) {
            pageItemMap.put(tempModel.getItemType(),tempModel.getItemStatus());
        }

        List<TVehicleSafeCheckItem> dbItemList = tVehicleSafeCheckItemMapper.getListBySafeCheckVehicleId(requestModel.getSafeCheckVehicleId());
        Long operatorUserId =BaseContextHandler.getUserId();
        String operatorName = BaseContextHandler.getUserName();
        Date operatorTime = new Date();
        for (TVehicleSafeCheckItem tempItem : dbItemList) {
            tempItem.setStatus(pageItemMap.get(tempItem.getItemType()));
            tempItem.setLastModifiedTime(operatorTime);
            tempItem.setLastModifiedBy(operatorName);
        }

        //安全检车车辆信息表
        TVehicleSafeCheckVehicle vehicleSafeCheckVehicle = new TVehicleSafeCheckVehicle();
        vehicleSafeCheckVehicle.setLastModifiedTime(operatorTime);
        vehicleSafeCheckVehicle.setLastModifiedBy(operatorName);
        vehicleSafeCheckVehicle.setCheckUserName(operatorName);
        vehicleSafeCheckVehicle.setCheckUserId(operatorUserId);
        vehicleSafeCheckVehicle.setCheckTime(operatorTime);

        //1.待确认提交
        if(CommonConstant.INTEGER_ONE.equals(requestModel.getOperatorType())){
            SafeCheckItemRequestModel unqualifiedItem = requestModel.getItemList().stream().filter(o-> CommonConstant.INTEGER_ZERO.equals(o.getItemStatus()) || CommonConstant.INTEGER_TWO.equals(o.getItemStatus()) ).findFirst().orElse(null);
            if(unqualifiedItem != null){
                throw new BizException(CarrierDataExceptionEnum.CHECK_ITEM_INFO_ERROR);
            }
            vehicleSafeCheckVehicle.setId(dbTVehicleSafeCheckVehicle.getId());
            vehicleSafeCheckVehicle.setStatus(SafeCheckStatusEnum.HAS_CHECK.getKey());
            vehicleSafeCheckVehicle.setCheckUserId(BaseContextHandler.getUserId());
            vehicleSafeCheckVehicle.setCheckUserName(BaseContextHandler.getUserName());
            vehicleSafeCheckVehicle.setCheckTime(new Date());

            tVehicleSafeCheckVehicleMapper.updateByPrimaryKeySelective(vehicleSafeCheckVehicle);
            if(ListUtils.isNotEmpty(dbItemList)){
                tVehicleSafeCheckItemMapper.batchUpdate(dbItemList);
            }

            //新增修改附件信息
            this.saveOrUpdateFile(
                    dbTVehicleSafeCheckVehicle.getId(),
                    CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_VEHICLE,
                    CertificationPicturesFileTypeEnum.SAFE_CHECK_VEHICLE_IMAGE_FILE,
                    CopyFileTypeEnum.DRIVER_SAFE_CHECK_FILE,
                    configKeyConstant.safeCheckCatalog,
                    requestModel.getVehicleFileList()
            );
        }

        //2.添加整改项提交
        if(CommonConstant.INTEGER_TWO.equals(requestModel.getOperatorType())){

            vehicleSafeCheckVehicle.setId(dbTVehicleSafeCheckVehicle.getId());
            vehicleSafeCheckVehicle.setStatus( SafeCheckStatusEnum.WAIT_REFORM.getKey());
            tVehicleSafeCheckVehicleMapper.updateByPrimaryKeySelective(vehicleSafeCheckVehicle);

            if(ListUtils.isNotEmpty(dbItemList)){
                tVehicleSafeCheckItemMapper.batchUpdate(dbItemList);
            }
            TVehicleSafeCheckReform addTVehicleSafeCheckReform = new TVehicleSafeCheckReform();
            addTVehicleSafeCheckReform.setSafeCheckVehicleId(vehicleSafeCheckVehicle.getId());
            addTVehicleSafeCheckReform.setReformCount(requestModel.getReformCount());
            addTVehicleSafeCheckReform.setReformContent(requestModel.getReformContent());
            addTVehicleSafeCheckReform.setAddReformTime(operatorTime);
            addTVehicleSafeCheckReform.setLastModifiedBy(operatorName);
            addTVehicleSafeCheckReform.setLastModifiedTime(operatorTime);
            addTVehicleSafeCheckReform.setCreatedBy(operatorName);
            addTVehicleSafeCheckReform.setCreatedTime(operatorTime);
            tVehicleSafeCheckReformMapper.insertSelective(addTVehicleSafeCheckReform);

            //新增修改附件信息
            this.saveOrUpdateFile(
                    addTVehicleSafeCheckReform.getId(),
                    CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_REFORM,
                    CertificationPicturesFileTypeEnum.SAFE_CHECK_ITEM_REFORM_IMAGE_FILE,
                    CopyFileTypeEnum.DRIVER_SAFE_CHECK_REFORM_FILE,
                    configKeyConstant.safeCheckReformCatalog,
                    requestModel.getItemFileList()
            );
        }
    }

    /**
     * 提交-整改结果
     * @param requestModel
     */
    @Transactional
    public void addReformResult(AddReformResultRequestModel requestModel) {
        TVehicleSafeCheckVehicle dbTVehicleSafeCheckVehicle = tVehicleSafeCheckVehicleMapper.selectByPrimaryKey(requestModel.getSafeCheckVehicleId());
        if(dbTVehicleSafeCheckVehicle == null || IfValidEnum.INVALID.getKey().equals(dbTVehicleSafeCheckVehicle.getValid())){
            throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_INFO_NOT_EXIST);
        }
        if(!SafeCheckStatusEnum.WAIT_REFORM.getKey().equals(dbTVehicleSafeCheckVehicle.getStatus()) && !SafeCheckStatusEnum.HAS_REFORM.getKey().equals(dbTVehicleSafeCheckVehicle.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_STATUS_HAS_CHANGE);
        }
        if(SafeCheckStatusEnum.WAIT_REFORM.getKey().equals(dbTVehicleSafeCheckVehicle.getStatus()) && ListUtils.isEmpty(requestModel.getResultFileList())){
            throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_REFORM_RESULT_FILE_IS_EMPTY);
        }
        //整改结果提交-检查完成,设置车辆数据

        String operatorName = BaseContextHandler.getUserName();
        Date operatorTime = new Date();
        TVehicleSafeCheckVehicle updateTVehicleSafeCheckVehicle = new TVehicleSafeCheckVehicle();
        updateTVehicleSafeCheckVehicle.setId(dbTVehicleSafeCheckVehicle.getId());
        updateTVehicleSafeCheckVehicle.setStatus(SafeCheckStatusEnum.HAS_CHECK.getKey());
        updateTVehicleSafeCheckVehicle.setLastModifiedBy(operatorName);
        updateTVehicleSafeCheckVehicle.setLastModifiedTime(operatorTime);
        updateTVehicleSafeCheckVehicle.setCreatedBy(operatorName);
        updateTVehicleSafeCheckVehicle.setCreatedTime(operatorTime);
        tVehicleSafeCheckVehicleMapper.updateByPrimaryKeySelective(updateTVehicleSafeCheckVehicle);

        TVehicleSafeCheckReform  dbTVehicleSafeCheckReform = tVehicleSafeCheckReformMapper.getBySafeCheckVehicleId(dbTVehicleSafeCheckVehicle.getId());
        TVehicleSafeCheckReform vehicleSafeCheckReform = new TVehicleSafeCheckReform();
        vehicleSafeCheckReform.setSafeCheckVehicleId(dbTVehicleSafeCheckVehicle.getId());
        vehicleSafeCheckReform.setReformResult(requestModel.getReformResult());
        vehicleSafeCheckReform.setAddReformResultTime(operatorTime);

        if(dbTVehicleSafeCheckReform == null){
            commonBiz.setBaseEntityAdd(vehicleSafeCheckReform,BaseContextHandler.getUserName());
            tVehicleSafeCheckReformMapper.insertSelective(vehicleSafeCheckReform);
        }else{
            vehicleSafeCheckReform.setId(dbTVehicleSafeCheckReform.getId());
            commonBiz.setBaseEntityModify(vehicleSafeCheckReform,BaseContextHandler.getUserName());
            tVehicleSafeCheckReformMapper.updateByPrimaryKeySelective(vehicleSafeCheckReform);
        }

        //新增修改附件信息
        this.saveOrUpdateFile(
                vehicleSafeCheckReform.getId(),
                CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_REFORM,
                CertificationPicturesFileTypeEnum.SAFE_CHECK_REFORM_IMAGE_FILE,
                CopyFileTypeEnum.DRIVER_SAFE_CHECK_RESULT_FILE,
                configKeyConstant.safeCheckReformCatalog,
                requestModel.getResultFileList()
        );
    }

    /**
     * 提交-重新检查
     * @param requestModel
     */
    @Transactional
    public void reformCheck(ReformCheckRequestModel requestModel) {
        TVehicleSafeCheckVehicle dbTVehicleSafeCheckVehicle = tVehicleSafeCheckVehicleMapper.selectByPrimaryKey(requestModel.getSafeCheckVehicleId());
        if(dbTVehicleSafeCheckVehicle == null || IfValidEnum.INVALID.getKey().equals(dbTVehicleSafeCheckVehicle.getValid())){
            throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_INFO_NOT_EXIST);
        }

        if(!SafeCheckStatusEnum.HAS_REFORM.getKey().equals(dbTVehicleSafeCheckVehicle.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_STATUS_HAS_CHANGE);
        }
        //1.车辆检查
        String operatorName = BaseContextHandler.getUserName();
        Date operatorTime = new Date();
        dbTVehicleSafeCheckVehicle.setStatus(SafeCheckStatusEnum.NOT_CHECK.getKey());
        dbTVehicleSafeCheckVehicle.setLastModifiedTime(operatorTime);
        dbTVehicleSafeCheckVehicle.setLastModifiedBy(operatorName);
        dbTVehicleSafeCheckVehicle.setCheckTime(null);
        dbTVehicleSafeCheckVehicle.setCheckUserName("");
        dbTVehicleSafeCheckVehicle.setCheckUserId(null);
        tVehicleSafeCheckVehicleMapper.updateByPrimaryKey(dbTVehicleSafeCheckVehicle);

        List<TCertificationPictures> updateFileList = new ArrayList<>();
        List<TCertificationPictures>  checkVehicleFileList = tCertificationPicturesMapper.getByObjectIdType(dbTVehicleSafeCheckVehicle.getId(),CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_VEHICLE.getObjectType(),CertificationPicturesFileTypeEnum.SAFE_CHECK_VEHICLE_IMAGE_FILE.getFileType());
        this.addList(checkVehicleFileList,updateFileList,operatorName,operatorTime);

        //2.检查项
        List<TVehicleSafeCheckItem> dbItemList = tVehicleSafeCheckItemMapper.getListBySafeCheckVehicleId(dbTVehicleSafeCheckVehicle.getId());
        if(ListUtils.isNotEmpty(dbItemList)){
            for (TVehicleSafeCheckItem tempItem : dbItemList) {
                tempItem.setStatus(SafeCheckItemStatusEnum.NOT_CHECK.getKey());
                tempItem.setLastModifiedBy(operatorName);
                tempItem.setLastModifiedTime(operatorTime);
            }
            tVehicleSafeCheckItemMapper.batchUpdate(dbItemList);
        }

        //3.整改信息
        TVehicleSafeCheckReform dbTVehicleSafeCheckReform = tVehicleSafeCheckReformMapper.getBySafeCheckVehicleId(dbTVehicleSafeCheckVehicle.getId());
        if(dbTVehicleSafeCheckReform!=null){
            TVehicleSafeCheckReform updateTVehicleSafeCheckReform = new TVehicleSafeCheckReform();
            updateTVehicleSafeCheckReform.setId(dbTVehicleSafeCheckReform.getId());
            updateTVehicleSafeCheckReform.setValid(IfValidEnum.INVALID.getKey());
            updateTVehicleSafeCheckReform.setLastModifiedTime(operatorTime);
            updateTVehicleSafeCheckReform.setLastModifiedBy(operatorName);
            tVehicleSafeCheckReformMapper.updateByPrimaryKeySelective(updateTVehicleSafeCheckReform);

            //整改项目
            List<TCertificationPictures> checkReformContentList  = tCertificationPicturesMapper.getByObjectIdType(dbTVehicleSafeCheckReform.getId(),CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_REFORM.getObjectType(),CertificationPicturesFileTypeEnum.SAFE_CHECK_ITEM_REFORM_IMAGE_FILE.getFileType());
            this.addList(checkReformContentList,updateFileList,operatorName,operatorTime);
            //整改结果
            List<TCertificationPictures> checkReformResultList  = tCertificationPicturesMapper.getByObjectIdType(dbTVehicleSafeCheckReform.getId(),CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_REFORM.getObjectType(),CertificationPicturesFileTypeEnum.SAFE_CHECK_REFORM_IMAGE_FILE.getFileType());
            this.addList(checkReformResultList,updateFileList,operatorName,operatorTime);
        }

        if(ListUtils.isNotEmpty(updateFileList)){
            tCertificationPicturesMapper.batchUpdate(updateFileList);
        }
    }

    /**
     * 添加待更新的附件信息，设置无效状态
     * @param source
     * @param updateList
     * @param operatorName
     * @param operatorTime
     */
    public void addList(List<TCertificationPictures> source,List<TCertificationPictures> updateList,String operatorName, Date operatorTime){
        if(ListUtils.isNotEmpty(source)){
            for (TCertificationPictures tempFile : source) {
                tempFile.setValid(IfValidEnum.INVALID.getKey());
                tempFile.setLastModifiedBy(operatorName);
                tempFile.setLastModifiedTime(operatorTime);
                updateList.add(tempFile);
            }
        }
    }

    /**
     * 新增修改附件信息
     * @param objectId 主题对象ID
     * @param objectTypeEnum 主题对象类型
     * @param fileTypeEnum 附件类型
     * @param copyFileTypeEnum 复制文件类型枚举
     * @param catalog 配置的相对目录
     * @param pageFileList 页面文件相对路径列表
     */
    public void saveOrUpdateFile(Long objectId,
                                 CertificationPicturesObjectTypeEnum objectTypeEnum,
                                 CertificationPicturesFileTypeEnum fileTypeEnum,
                                 CopyFileTypeEnum copyFileTypeEnum,
                                 String catalog,
                                 List<String> pageFileList){
        if(ListUtils.isEmpty(pageFileList)){
            return;
        }

        List<TCertificationPictures> certificationList = tCertificationPicturesMapper.getByObjectIdType(objectId, objectTypeEnum.getObjectType(), fileTypeEnum.getFileType());
        if(ListUtils.isNotEmpty(certificationList)){
            UpdateCertificationPicturesModel updateModel = new UpdateCertificationPicturesModel();
            updateModel.setObjectId(objectId);
            updateModel.setObjectType(objectTypeEnum.getObjectType());
            updateModel.setFileType(fileTypeEnum.getFileType());
            updateModel.setFilePaths(LocalStringUtil.listTostring(pageFileList,','));
            tCertificationPicturesMapper.updateFilePath(updateModel);
        }

        List<String> batchInsertList = new ArrayList<>();
        if(ListUtils.isNotEmpty(pageFileList)){
            for (String filePath : pageFileList) {
                if(!filePath.contains(catalog) && StringUtils.isNotBlank(filePath)){
                    batchInsertList.add(filePath);
                }
            }
        }

        if(ListUtils.isNotEmpty(batchInsertList)){
            List<TCertificationPictures> batchInsertModelList = new ArrayList<>();
            TCertificationPictures pageCertificationPictures;
            for (String picturesPath : batchInsertList ) {
                pageCertificationPictures = new TCertificationPictures();
                pageCertificationPictures.setObjectId(objectId);
                pageCertificationPictures.setObjectType(objectTypeEnum.getObjectType());
                pageCertificationPictures.setFileType(fileTypeEnum.getFileType());
                pageCertificationPictures.setFileTypeName(fileTypeEnum.getFileName());
                pageCertificationPictures.setUploadTime(new Date());
                pageCertificationPictures.setUploadUserName(BaseContextHandler.getUserName());
                pageCertificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(copyFileTypeEnum.getKey(),"",picturesPath,null));
                pageCertificationPictures.setSuffix(pageCertificationPictures.getFilePath().substring(pageCertificationPictures.getFilePath().lastIndexOf('.')));
                commonBiz.setBaseEntityAdd(pageCertificationPictures,BaseContextHandler.getUserName());
                batchInsertModelList.add(pageCertificationPictures);
            }
            tCertificationPicturesMapper.batchInsert(batchInsertModelList);
        }
    }

    /**
     * 车辆检查看板
     * @param requestModel
     * @return
     */
    public List<SafeCheckBoardResponseModel> getSafeCheckBoardInfo(SafeCheckBoardRequestModel requestModel){
        if(StringUtils.isBlank(requestModel.getSafeCheckYear())){
            requestModel.setSafeCheckYear(DateUtils.dateToString(new Date(),CommonConstant.YYYY_FORMAT));
        }

        String safeCheckYear = requestModel.getSafeCheckYear() ;
        List<SafeCheckBoardResponseModel> safeCheckBoardList = tVehicleSafeCheckVehicleMapper.getSafeCheckBoardInfo(requestModel);

        //初始化年-月日期
        List<String> monthList = Arrays.asList(safeCheckYear + "-" + CommonConstant.JANUARY,safeCheckYear + "-" + CommonConstant.FEBRUARY,safeCheckYear + "-" + CommonConstant.MARCH,
                safeCheckYear + "-" + CommonConstant.APRIL,safeCheckYear + "-" + CommonConstant.MAY,safeCheckYear + "-" + CommonConstant.JUNE,
                safeCheckYear + "-" + CommonConstant.JULY,safeCheckYear + "-" + CommonConstant.AUGUST,safeCheckYear + "-" + CommonConstant.SEPTEMBER,
                safeCheckYear + "-" + CommonConstant.OCTOBER,safeCheckYear + "-" + CommonConstant.NOVEMBER,safeCheckYear + "-" + CommonConstant.DECEMBER);

        String currentYear = DateUtils.dateToString(new Date(),"yyyy");
        if (currentYear.equals(safeCheckYear)){//当前年
            String currentMonth = DateUtils.dateToString(new Date(),CommonConstant.DATE_TO_STRING_YM_PATTERN);
            monthList = monthList.stream().filter(month -> month.compareTo(currentMonth) <= CommonConstant.INTEGER_ZERO).collect(Collectors.toList());
        }

        Map<String,SafeCheckBoardResponseModel> boardModelMap = new HashMap<>();
        if(ListUtils.isNotEmpty(safeCheckBoardList)){
            for (SafeCheckBoardResponseModel tempBoardModel : safeCheckBoardList) {
                boardModelMap.put(tempBoardModel.getPeriod(),tempBoardModel);
            }
        }
        //初始化汇总数据
        SafeCheckBoardResponseModel retModel;
        for (String tempMoth: monthList) {
            if(boardModelMap.get(tempMoth) == null){
                retModel = new SafeCheckBoardResponseModel();
                retModel.setCheckItemCount(CommonConstant.INTEGER_ZERO);
                retModel.setCheckVehicleCount(CommonConstant.INTEGER_ZERO);
                retModel.setPeriod(tempMoth);
                retModel.setReformCount(CommonConstant.INTEGER_ZERO);
                retModel.setVehicleCount(CommonConstant.INTEGER_ZERO);
                retModel.setOwnVehicleCount(CommonConstant.INTEGER_ZERO);
                retModel.setAffiliationVehicleCount(CommonConstant.INTEGER_ZERO);
                safeCheckBoardList.add(retModel);
            }
        }
        Collections.sort(safeCheckBoardList,(o1, o2) -> o2.getPeriod().compareTo(o1.getPeriod()));
        return safeCheckBoardList;
    }

    /**
     * 小程序-车辆检查列表
     * @param requestModel
     * @return
     */
    public PageInfo<AppletSafeCheckListResponseModel> searchAppletList(AppletSafeCheckListRequestModel requestModel) {
        requestModel.setStaffId(commonBiz.getLoginDriverAppletUserId());
        requestModel.enablePaging();
        List<AppletSafeCheckListResponseModel> result = tVehicleSafeCheckVehicleMapper.searchAppletList(requestModel);
        return new PageInfo<>(result);
    }

    /**
     * 小程序-车辆检查汇总
     * @param requestModel
     * @return
     */
    public AppletSafeCheckSummaryResponseModel getAppletSummary(AppletSafeCheckListRequestModel requestModel) {

        Long driverId = commonBiz.getLoginDriverAppletUserId();
        requestModel.setStaffId(driverId);
        return tVehicleSafeCheckVehicleMapper.getAppletSummary(requestModel);
    }

    /**
     * 小程序-提交待检查
     * @param requestModel
     */
    @Transactional
    public void submitWaitCheck(AppletAddWaitCheckRequestModel requestModel){
       TVehicleSafeCheckVehicle dbSafeCheckVehicle = tVehicleSafeCheckVehicleMapper.selectByPrimaryKey(requestModel.getSafeCheckVehicleId());
       if(dbSafeCheckVehicle == null || IfValidEnum.INVALID.getKey().equals(dbSafeCheckVehicle.getValid())){
           throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_INFO_NOT_EXIST);
       }
       if(!SafeCheckStatusEnum.NOT_CHECK.getKey().equals(dbSafeCheckVehicle.getStatus())){
          throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_STATUS_HAS_CHANGE);
       }

       TVehicleSafeCheckVehicle upSafeCheckVehicle  =new TVehicleSafeCheckVehicle();
       upSafeCheckVehicle.setId(dbSafeCheckVehicle.getId());
       upSafeCheckVehicle.setStatus(SafeCheckStatusEnum.WAIT_CONFIRM.getKey());
       upSafeCheckVehicle.setRemark(requestModel.getRemark());
       commonBiz.setBaseEntityModify(upSafeCheckVehicle,BaseContextHandler.getUserName());
       tVehicleSafeCheckVehicleMapper.updateByPrimaryKeySelective(upSafeCheckVehicle);

       this.saveOrUpdateFile(dbSafeCheckVehicle.getId(), CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_VEHICLE,
               CertificationPicturesFileTypeEnum.SAFE_CHECK_VEHICLE_IMAGE_FILE, CopyFileTypeEnum.DRIVER_SAFE_CHECK_FILE,
               configKeyConstant.safeCheckCatalog, requestModel.getFileList());
    }

    /**
     * 小程序-提交待整改
     * @param requestModel
     */
    @Transactional
    public void submitWaitReform(AppletAddWaitReformRequestModel requestModel){
        TVehicleSafeCheckVehicle dbSafeCheckVehicle = tVehicleSafeCheckVehicleMapper.selectByPrimaryKey(requestModel.getSafeCheckVehicleId());
        if(dbSafeCheckVehicle == null || IfValidEnum.INVALID.getKey().equals(dbSafeCheckVehicle.getValid())){
            throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_INFO_NOT_EXIST);
        }
        if(!SafeCheckStatusEnum.WAIT_REFORM.getKey().equals(dbSafeCheckVehicle.getStatus())){
            throw new BizException(CarrierDataExceptionEnum.SAFE_CHECK_STATUS_HAS_CHANGE);
        }
        TVehicleSafeCheckVehicle upSafeCheckVehicle  =new TVehicleSafeCheckVehicle();
        upSafeCheckVehicle.setId(dbSafeCheckVehicle.getId());
        upSafeCheckVehicle.setStatus(SafeCheckStatusEnum.HAS_REFORM.getKey());
        commonBiz.setBaseEntityModify(upSafeCheckVehicle,BaseContextHandler.getUserName());
        tVehicleSafeCheckVehicleMapper.updateByPrimaryKeySelective(upSafeCheckVehicle);

        TVehicleSafeCheckReform tVehicleSafeCheckReform= tVehicleSafeCheckReformMapper.getBySafeCheckVehicleId(dbSafeCheckVehicle.getId());
        this.saveOrUpdateFile(tVehicleSafeCheckReform.getId(), CertificationPicturesObjectTypeEnum.T_VEHICLE_SAFE_CHECK_REFORM,
                CertificationPicturesFileTypeEnum.SAFE_CHECK_REFORM_IMAGE_FILE, CopyFileTypeEnum.DRIVER_SAFE_CHECK_RESULT_FILE,
                configKeyConstant.safeCheckReformCatalog, requestModel.getFileList());
    }
}
