package com.logistics.management.webapi.controller.carrierorderticketsaudit.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchReceiptAuditListRequestDto extends AbstractPageForm<SearchReceiptAuditListRequestDto> {

    @ApiModelProperty(value = "审核状态; 0 待审核，1 已审核，2 已驳回")
    private String auditStatus;

    @ApiModelProperty("1.3.1新增；需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private String entrustType;

    @ApiModelProperty(value = "运单号")
    private String carrierOrderCode;

    @ApiModelProperty(value = "发货地")
    private String loadAddress;

    @ApiModelProperty(value = "收货地")
    private String unloadAddress;

    @ApiModelProperty(value = "卸货日期; 起始日期")
    private String unloadStartDate;

    @ApiModelProperty(value = "卸货日期; 终止日期")
    private String unloadEndDate;

    @ApiModelProperty(value = "单据上传日期; 起始日期")
    private String ticketUploadStartDate;

    @ApiModelProperty(value = "单据上传日期; 终止日期")
    private String ticketUploadEndDate;

    @ApiModelProperty(value = "单据上传日期; 起始日期")
    private String ticketAuditStartDate;

    @ApiModelProperty(value = "单据上传日期; 终止日期")
    private String ticketAuditEndDate;

}
