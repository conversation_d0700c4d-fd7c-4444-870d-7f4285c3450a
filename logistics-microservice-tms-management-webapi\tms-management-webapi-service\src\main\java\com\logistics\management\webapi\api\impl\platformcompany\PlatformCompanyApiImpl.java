package com.logistics.management.webapi.api.impl.platformcompany;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.platformcompany.PlatformCompanyApi;
import com.logistics.management.webapi.api.feign.platformcompany.dto.*;
import com.logistics.management.webapi.api.impl.platformcompany.mapping.SearchPlatformCompanyListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.platformcompany.PlatformCompanyServiceApi;
import com.logistics.tms.api.feign.platformcompany.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/9
 */
@RestController
public class PlatformCompanyApiImpl implements PlatformCompanyApi {

	@Autowired
	private PlatformCompanyServiceApi platformCompanyServiceApi;

	/**
	 * 查询结算主体列表
	 *
	 * @param requestDto 筛选条件
	 * @return 结算主体列表
	 */
	@Override
	public Result<PageInfo<SearchPlatformCompanyListResponseDto>> searchPlatformCompanyList(@RequestBody SearchPlatformCompanyListRequestDto requestDto) {
		Result<PageInfo<SearchPlatformCompanyListResponseModel>> result = platformCompanyServiceApi.searchPlatformCompanyList(MapperUtils.mapper(requestDto, SearchPlatformCompanyListRequestModel.class));
		result.throwException();
		PageInfo pageInfo = result.getData();
		List<SearchPlatformCompanyListResponseDto> list = MapperUtils.mapper(pageInfo.getList(), SearchPlatformCompanyListResponseDto.class, new SearchPlatformCompanyListMapping());
		pageInfo.setList(list);
		return Result.success(pageInfo);
	}

	/**
	 * 新增结算主体
	 *
	 * @param requestDto 结算主体信息
	 * @return 操作结果
	 */
	@Override
	@IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
	public Result<Boolean> addPlatformCompany(@RequestBody @Valid AddPlatformCompanyRequestDto requestDto) {
		return platformCompanyServiceApi.addPlatformCompany(MapperUtils.mapper(requestDto, AddPlatformCompanyRequestModel.class));
	}

	/**
	 * 删除结算主体
	 *
	 * @param requestDto 结算主体id
	 * @return 操作结果
	 */
	@Override
	@IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
	public Result<Boolean> delPlatformCompany(@RequestBody @Valid DelPlatformCompanyRequestDto requestDto) {
		return platformCompanyServiceApi.delPlatformCompany(MapperUtils.mapper(requestDto, DelPlatformCompanyRequestModel.class));
	}

	/**
	 * 查询结算主体下拉列表
	 *
	 * @param requestDto 模糊查询主体筛选条件
	 * @return 结算主体列表
	 */
	@Override
	public Result<List<PlatformCompanySelectListResponseDto>> platformCompanySelectList(@RequestBody PlatformCompanySelectListRequestDto requestDto) {
		Result<List<PlatformCompanySelectListResponseModel>> result = platformCompanyServiceApi.platformCompanySelectList(MapperUtils.mapper(requestDto, PlatformCompanySelectListRequestModel.class));
		result.throwException();
		return Result.success(MapperUtils.mapper(result.getData(), PlatformCompanySelectListResponseDto.class));
	}
}
