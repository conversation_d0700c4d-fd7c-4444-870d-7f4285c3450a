<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDriverCostDeductionMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDriverCostDeduction">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="driver_cost_apply_id" jdbcType="BIGINT" property="driverCostApplyId" />
    <result column="reserve_apply_id" jdbcType="BIGINT" property="reserveApplyId" />
    <result column="reserve_apply_code" jdbcType="VARCHAR" property="reserveApplyCode" />
    <result column="reserve_type" jdbcType="INTEGER" property="reserveType" />
    <result column="balance_amount" jdbcType="DECIMAL" property="balanceAmount" />
    <result column="verification_amount" jdbcType="DECIMAL" property="verificationAmount" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, driver_cost_apply_id, reserve_apply_id, reserve_apply_code, reserve_type, balance_amount, 
    verification_amount, created_by, created_time, last_modified_by, last_modified_time, 
    valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_driver_cost_deduction
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_driver_cost_deduction
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDriverCostDeduction">
    insert into t_driver_cost_deduction (id, driver_cost_apply_id, reserve_apply_id, 
      reserve_apply_code, reserve_type, balance_amount, 
      verification_amount, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{driverCostApplyId,jdbcType=BIGINT}, #{reserveApplyId,jdbcType=BIGINT}, 
      #{reserveApplyCode,jdbcType=VARCHAR}, #{reserveType,jdbcType=INTEGER}, #{balanceAmount,jdbcType=DECIMAL}, 
      #{verificationAmount,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDriverCostDeduction">
    insert into t_driver_cost_deduction
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="driverCostApplyId != null">
        driver_cost_apply_id,
      </if>
      <if test="reserveApplyId != null">
        reserve_apply_id,
      </if>
      <if test="reserveApplyCode != null">
        reserve_apply_code,
      </if>
      <if test="reserveType != null">
        reserve_type,
      </if>
      <if test="balanceAmount != null">
        balance_amount,
      </if>
      <if test="verificationAmount != null">
        verification_amount,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="driverCostApplyId != null">
        #{driverCostApplyId,jdbcType=BIGINT},
      </if>
      <if test="reserveApplyId != null">
        #{reserveApplyId,jdbcType=BIGINT},
      </if>
      <if test="reserveApplyCode != null">
        #{reserveApplyCode,jdbcType=VARCHAR},
      </if>
      <if test="reserveType != null">
        #{reserveType,jdbcType=INTEGER},
      </if>
      <if test="balanceAmount != null">
        #{balanceAmount,jdbcType=DECIMAL},
      </if>
      <if test="verificationAmount != null">
        #{verificationAmount,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDriverCostDeduction">
    update t_driver_cost_deduction
    <set>
      <if test="driverCostApplyId != null">
        driver_cost_apply_id = #{driverCostApplyId,jdbcType=BIGINT},
      </if>
      <if test="reserveApplyId != null">
        reserve_apply_id = #{reserveApplyId,jdbcType=BIGINT},
      </if>
      <if test="reserveApplyCode != null">
        reserve_apply_code = #{reserveApplyCode,jdbcType=VARCHAR},
      </if>
      <if test="reserveType != null">
        reserve_type = #{reserveType,jdbcType=INTEGER},
      </if>
      <if test="balanceAmount != null">
        balance_amount = #{balanceAmount,jdbcType=DECIMAL},
      </if>
      <if test="verificationAmount != null">
        verification_amount = #{verificationAmount,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDriverCostDeduction">
    update t_driver_cost_deduction
    set driver_cost_apply_id = #{driverCostApplyId,jdbcType=BIGINT},
      reserve_apply_id = #{reserveApplyId,jdbcType=BIGINT},
      reserve_apply_code = #{reserveApplyCode,jdbcType=VARCHAR},
      reserve_type = #{reserveType,jdbcType=INTEGER},
      balance_amount = #{balanceAmount,jdbcType=DECIMAL},
      verification_amount = #{verificationAmount,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>