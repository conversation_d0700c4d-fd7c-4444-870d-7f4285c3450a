package com.logistics.appapi.controller.drivercostapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Pattern;

/**
 * @author: wjf
 * @date: 2022/8/3 9:28
 */
@Data
public class DriverCostApplyInvoiceRequestDto {

    @ApiModelProperty(value = "发票id,新增不传")
    private String invoiceId;

    @ApiModelProperty("1 增值税，2 出租车，3 火车票，4 定额，5 卷票，6 机打，7 过路")
    private String type;

    @ApiModelProperty("发票名称")
    @Length(max = 50, message = "发票名称长度不可超过50")
    private String invoiceName;

    @ApiModelProperty("票据类型")
    private String invoiceType;

    @ApiModelProperty("发票代码")
    private String invoiceCode;

    @ApiModelProperty("发票号码")
    @Pattern(regexp = "^[A-Za-z0-9]{1,30}$", message = "发票号码为数字以及大小写字母的1-30位字符")
    private String invoiceNum;

    @ApiModelProperty("合计金额")
    private String totalPrice;

    @ApiModelProperty("合计税额")
    private String totalTax;

    @ApiModelProperty("价税合计")
    private String totalTaxAndPrice;

    @ApiModelProperty(value = "图片相对路径")
    private String imagePath;
}
