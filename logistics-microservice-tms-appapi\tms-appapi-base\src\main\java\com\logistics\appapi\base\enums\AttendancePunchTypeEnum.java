package com.logistics.appapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum AttendancePunchTypeEnum {

    NOT_CLOCK_IN(0, "未打卡"),
    ON_DUTY_CLOCK_IN(1, "上班打卡"),
    OFF_DUTY_CLOCK_IN(2, "下班打卡"),
    ;

    private Integer key;
    private String value;

    public static AttendancePunchTypeEnum getEnumByKey(Integer key) {
        return Stream.of(AttendancePunchTypeEnum.values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(NOT_CLOCK_IN);
    }
}
