package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:lei.zhu
 * @date:2021/12/3 16:52:22
 */
@Data
public class CancelSinopecDemandRequestModel {
    @ApiModelProperty("集成消息统一编码 基于32位大写GUID表示")
    private String imGuid; //43BAC2C6CAB42EC15B05ABC62C79CB8E   注：随机生成GUID

    @ApiModelProperty("发送时间格式：YYYYMMDDHHMMSS")
    private String sendTime; // 20210401235959   注：取当前时间

    @ApiModelProperty("发送方系统编码")
    private String sender;  //LISBS

    @ApiModelProperty("接收方系统编码")
    private String receiver;  //LEJU    注：乐橘为LEJU

    @ApiModelProperty("接收方接口编码")
    private String receiveIfid;  //cancelConsignOrder

    @ApiModelProperty("接收方目标方法/函数名")
    private String receiveMethod;  //cancelConsignOrder

    @ApiModelProperty("发送方系统业务操作人账号，不涉及 具体操作人填写NULL")
    private String sendOperator;  //system

    @ApiModelProperty("委托单号")
    private String sn;  //TC2021101500001A

    @ApiModelProperty("请求ip")
    private String ip;
}
