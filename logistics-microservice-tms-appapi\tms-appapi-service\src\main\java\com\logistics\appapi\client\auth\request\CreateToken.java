package com.logistics.appapi.client.auth.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @author: wjf
 * @date: 2023/12/25 13:39
 */
@Data
public class CreateToken {
    @ApiModelProperty("用户id")
    private Long userId;
    @ApiModelProperty("登录来源")
    private String loginSource;
    @ApiModelProperty("用户名")
    private String userName;
    @ApiModelProperty("用户登录账户")
    private String userAccount;
    @ApiModelProperty("用户权限")
    private List<String> userRoles;
    @ApiModelProperty("过期时间（单位秒）")
    private String expireTime;
    @ApiModelProperty("其他参数")
    private ConcurrentHashMap<String, Object> params;
}
