package com.logistics.management.webapi.client.companyaccount;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.companyaccount.hystrix.CompanyAccountClientHystrix;
import com.logistics.management.webapi.client.companyaccount.request.CompanyAccountAddRequestModel;
import com.logistics.management.webapi.client.companyaccount.request.CompanyAccountEnabledRequestModel;
import com.logistics.management.webapi.client.companyaccount.request.CompanyAccountImageRequestModel;
import com.logistics.management.webapi.client.companyaccount.request.SearchCompanyAccountRequestModel;
import com.logistics.management.webapi.client.companyaccount.response.CompanyAccountImageResponseModel;
import com.logistics.management.webapi.client.companyaccount.response.SearchCompanyAccountResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/03/27
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = CompanyAccountClientHystrix.class)
public interface CompanyAccountClient {

	@ApiOperation(value = "新增公司账户")
	@PostMapping("/service/companyAccount/addAccount")
	Result<Boolean> addAccount(@RequestBody CompanyAccountAddRequestModel requestModel);

	@ApiOperation(value = "公司账户查询列表")
	@PostMapping("/service/companyAccount/searchList")
	Result<PageInfo<SearchCompanyAccountResponseModel>> searchList(@RequestBody SearchCompanyAccountRequestModel requestModel);

	@ApiOperation(value = "公司账户图片查询")
	@PostMapping("/service/companyAccount/getAccountImageList")
	Result<CompanyAccountImageResponseModel> getAccountImageList(@RequestBody @Valid CompanyAccountImageRequestModel requestModel);

	@ApiOperation(value = "公司账户禁用/启用")
	@PostMapping("/service/companyAccount/enabled")
	Result<Boolean> enabled(@RequestBody CompanyAccountEnabledRequestModel requestModel);
}
