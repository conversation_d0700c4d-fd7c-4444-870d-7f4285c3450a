package com.logistics.tms.api.feign.violationregulation.model;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/6/3 10:10
 */
@Data
public class SearchViolationRegulationListRequestModel extends AbstractPageForm<SearchViolationRegulationListRequestModel> {
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主 2 外部 3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("违章时间范围上线")
    private String occuranceTimeFrom;
    @ApiModelProperty("违章时间范围下线")
    private String occuranceTimeTo;
    @ApiModelProperty("最后操作人")
    private String lastModifiedBy;
    @ApiModelProperty("最后操作时间")
    private String lastModifiedTimeFrom;
    @ApiModelProperty("最后操作时间")
    private String lastModifiedTimeTo;
}
