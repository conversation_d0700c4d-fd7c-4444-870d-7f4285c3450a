<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderVehicleHistoryMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCarrierOrderVehicleHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="carrier_order_id" jdbcType="BIGINT" property="carrierOrderId" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="trailer_vehicle_id" jdbcType="BIGINT" property="trailerVehicleId" />
    <result column="trailer_vehicle_no" jdbcType="VARCHAR" property="trailerVehicleNo" />
    <result column="driver_id" jdbcType="BIGINT" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_mobile" jdbcType="VARCHAR" property="driverMobile" />
    <result column="driver_identity" jdbcType="VARCHAR" property="driverIdentity" />
    <result column="expect_load_time" jdbcType="TIMESTAMP" property="expectLoadTime" />
    <result column="expect_arrival_time" jdbcType="TIMESTAMP" property="expectArrivalTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="vehicle_history_id" jdbcType="BIGINT" property="vehicleHistoryId" />
    <result column="if_invalid" jdbcType="INTEGER" property="ifInvalid" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="audit_user_id" jdbcType="BIGINT" property="auditUserId" />
    <result column="audit_user_name" jdbcType="VARCHAR" property="auditUserName" />
    <result column="audit_source" jdbcType="INTEGER" property="auditSource" />
    <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, carrier_order_id, vehicle_id, vehicle_no, trailer_vehicle_id, trailer_vehicle_no, 
    driver_id, driver_name, driver_mobile, driver_identity, expect_load_time, expect_arrival_time, 
    remark, vehicle_history_id, if_invalid, audit_status, audit_time, audit_user_id, 
    audit_user_name, audit_source, reject_reason, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_carrier_order_vehicle_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_carrier_order_vehicle_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCarrierOrderVehicleHistory">
    insert into t_carrier_order_vehicle_history (id, carrier_order_id, vehicle_id, 
      vehicle_no, trailer_vehicle_id, trailer_vehicle_no, 
      driver_id, driver_name, driver_mobile, 
      driver_identity, expect_load_time, expect_arrival_time, 
      remark, vehicle_history_id, if_invalid, 
      audit_status, audit_time, audit_user_id, 
      audit_user_name, audit_source, reject_reason, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{carrierOrderId,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehicleNo,jdbcType=VARCHAR}, #{trailerVehicleId,jdbcType=BIGINT}, #{trailerVehicleNo,jdbcType=VARCHAR}, 
      #{driverId,jdbcType=BIGINT}, #{driverName,jdbcType=VARCHAR}, #{driverMobile,jdbcType=VARCHAR}, 
      #{driverIdentity,jdbcType=VARCHAR}, #{expectLoadTime,jdbcType=TIMESTAMP}, #{expectArrivalTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{vehicleHistoryId,jdbcType=BIGINT}, #{ifInvalid,jdbcType=INTEGER}, 
      #{auditStatus,jdbcType=INTEGER}, #{auditTime,jdbcType=TIMESTAMP}, #{auditUserId,jdbcType=BIGINT}, 
      #{auditUserName,jdbcType=VARCHAR}, #{auditSource,jdbcType=INTEGER}, #{rejectReason,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderVehicleHistory" keyProperty="id" useGeneratedKeys="true">
    insert into t_carrier_order_vehicle_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="carrierOrderId != null">
        carrier_order_id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="trailerVehicleId != null">
        trailer_vehicle_id,
      </if>
      <if test="trailerVehicleNo != null">
        trailer_vehicle_no,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="driverMobile != null">
        driver_mobile,
      </if>
      <if test="driverIdentity != null">
        driver_identity,
      </if>
      <if test="expectLoadTime != null">
        expect_load_time,
      </if>
      <if test="expectArrivalTime != null">
        expect_arrival_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="vehicleHistoryId != null">
        vehicle_history_id,
      </if>
      <if test="ifInvalid != null">
        if_invalid,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="auditUserId != null">
        audit_user_id,
      </if>
      <if test="auditUserName != null">
        audit_user_name,
      </if>
      <if test="auditSource != null">
        audit_source,
      </if>
      <if test="rejectReason != null">
        reject_reason,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierOrderId != null">
        #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="trailerVehicleId != null">
        #{trailerVehicleId,jdbcType=BIGINT},
      </if>
      <if test="trailerVehicleNo != null">
        #{trailerVehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="driverIdentity != null">
        #{driverIdentity,jdbcType=VARCHAR},
      </if>
      <if test="expectLoadTime != null">
        #{expectLoadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectArrivalTime != null">
        #{expectArrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="vehicleHistoryId != null">
        #{vehicleHistoryId,jdbcType=BIGINT},
      </if>
      <if test="ifInvalid != null">
        #{ifInvalid,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditUserId != null">
        #{auditUserId,jdbcType=BIGINT},
      </if>
      <if test="auditUserName != null">
        #{auditUserName,jdbcType=VARCHAR},
      </if>
      <if test="auditSource != null">
        #{auditSource,jdbcType=INTEGER},
      </if>
      <if test="rejectReason != null">
        #{rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCarrierOrderVehicleHistory">
    update t_carrier_order_vehicle_history
    <set>
      <if test="carrierOrderId != null">
        carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="trailerVehicleId != null">
        trailer_vehicle_id = #{trailerVehicleId,jdbcType=BIGINT},
      </if>
      <if test="trailerVehicleNo != null">
        trailer_vehicle_no = #{trailerVehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="driverIdentity != null">
        driver_identity = #{driverIdentity,jdbcType=VARCHAR},
      </if>
      <if test="expectLoadTime != null">
        expect_load_time = #{expectLoadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectArrivalTime != null">
        expect_arrival_time = #{expectArrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="vehicleHistoryId != null">
        vehicle_history_id = #{vehicleHistoryId,jdbcType=BIGINT},
      </if>
      <if test="ifInvalid != null">
        if_invalid = #{ifInvalid,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditUserId != null">
        audit_user_id = #{auditUserId,jdbcType=BIGINT},
      </if>
      <if test="auditUserName != null">
        audit_user_name = #{auditUserName,jdbcType=VARCHAR},
      </if>
      <if test="auditSource != null">
        audit_source = #{auditSource,jdbcType=INTEGER},
      </if>
      <if test="rejectReason != null">
        reject_reason = #{rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCarrierOrderVehicleHistory">
    update t_carrier_order_vehicle_history
    set carrier_order_id = #{carrierOrderId,jdbcType=BIGINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      trailer_vehicle_id = #{trailerVehicleId,jdbcType=BIGINT},
      trailer_vehicle_no = #{trailerVehicleNo,jdbcType=VARCHAR},
      driver_id = #{driverId,jdbcType=BIGINT},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      driver_identity = #{driverIdentity,jdbcType=VARCHAR},
      expect_load_time = #{expectLoadTime,jdbcType=TIMESTAMP},
      expect_arrival_time = #{expectArrivalTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      vehicle_history_id = #{vehicleHistoryId,jdbcType=BIGINT},
      if_invalid = #{ifInvalid,jdbcType=INTEGER},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      audit_user_id = #{auditUserId,jdbcType=BIGINT},
      audit_user_name = #{auditUserName,jdbcType=VARCHAR},
      audit_source = #{auditSource,jdbcType=INTEGER},
      reject_reason = #{rejectReason,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>