package com.logistics.tms.base.enums;

/**
 * 云仓仓库类型枚举
 *
 * <AUTHOR>
 * @date 2022/10/12 15:19
 */
public enum WarehouseTypeEnum {

    OWN(1,"自有"),
    THIRD_PARTY(2,"第三方"),
    VIRTUAL(3,"虚拟"),
    OTHER(4,"其他"),
    CRUSHING_PLANT(5,"粉碎工厂"),
    ;
    private Integer key;
    private String value;

    WarehouseTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
