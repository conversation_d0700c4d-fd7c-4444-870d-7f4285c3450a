package com.logistics.management.webapi.api.impl.vehicleassetmanagement.mapper;

import com.logistics.management.webapi.api.feign.vehicleassetmanagement.dto.ExternalVehicleDetailResponseDto;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.tms.api.feign.vehicleassetmanagement.model.ExternalVehicleDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * @author: wjf
 * @date: 2022/10/19 10:09
 */
public class GetExternalVehicleMapping extends MapperMapping<ExternalVehicleDetailResponseModel, ExternalVehicleDetailResponseDto> {
    @Override
    public void configure() {
        ExternalVehicleDetailResponseModel source = getSource();
        ExternalVehicleDetailResponseDto destination = getDestination();

        destination.setCompanyCarrierName(CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyCarrierType()) ?
                source.getCompanyCarrierName() :
                source.getCarrierContactName() + " " + source.getCarrierContactPhone());
    }
}
