package com.logistics.management.webapi.base.enums;

public enum FreightRuleAdjustUnitEnum {
    DEFAULT(0,"未设置"),
    PERCENTAGE(1,"百分比"),
    YUAN(2,"元");

    private Integer key;
    private String value;

    FreightRuleAdjustUnitEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static FreightRuleAdjustUnitEnum getEnum(Integer key) {
        for (FreightRuleAdjustUnitEnum t : values()) {
            if (t.getKey().equals(key) ) {
                return t;
            }
        }
        return null;
    }
}
