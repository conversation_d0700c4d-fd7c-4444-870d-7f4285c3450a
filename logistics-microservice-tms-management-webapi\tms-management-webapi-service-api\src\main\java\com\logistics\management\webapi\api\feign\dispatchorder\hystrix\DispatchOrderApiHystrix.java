package com.logistics.management.webapi.api.feign.dispatchorder.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.dispatchorder.DispatchOrderApi;
import com.logistics.management.webapi.api.feign.dispatchorder.dto.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Component
public class DispatchOrderApiHystrix implements DispatchOrderApi {

    @Override
    public Result<PageInfo<DispatchOrderSearchResponseDto>> searchList(DispatchOrderSearchRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<List<DispatchOrderCarrierChildResponseDto>> getCarrierOrder(DispatchOrderCarrierRequestDto requestDto) {
        return Result.timeout();
    }

    @Override
    public Result<DispatchOrderDetailResponseDto> getDetail(DispatchOrderCarrierRequestDto requestDto) {
        return Result.timeout();
    }


}
