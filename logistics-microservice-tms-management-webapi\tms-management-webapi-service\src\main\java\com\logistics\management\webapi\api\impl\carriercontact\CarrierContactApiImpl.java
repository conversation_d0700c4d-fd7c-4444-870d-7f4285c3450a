package com.logistics.management.webapi.api.impl.carriercontact;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.carriercontact.CarrierContactApi;
import com.logistics.management.webapi.api.feign.carriercontact.dto.*;
import com.logistics.management.webapi.api.impl.carriercontact.mapping.CarrierContactDetailMapping;
import com.logistics.management.webapi.api.impl.carriercontact.mapping.CarrierContactListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelCarrierContact;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.tms.api.feign.carriercontact.CarrierContactAccountApi;
import com.logistics.tms.api.feign.carriercontact.dto.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;


@RestController
public class CarrierContactApiImpl implements CarrierContactApi {

    @Autowired
    private CarrierContactAccountApi carrierContactAccountApi;

    /**
     * 车主账号列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchCarrierContactResponseDto>> searchList(@RequestBody SearchCarrierContactRequestDto requestDto) {
        Result<PageInfo<SearchCarrierContactResponseModel>> pageInfoResult = carrierContactAccountApi.searchList(MapperUtils.mapper(requestDto, SearchCarrierContactRequestModel.class));
        pageInfoResult.throwException();
        PageInfo data = pageInfoResult.getData();
        List<SearchCarrierContactResponseDto> dtoList = MapperUtils.mapper(pageInfoResult.getData().getList(), SearchCarrierContactResponseDto.class, new CarrierContactListMapping());
        data.setList(dtoList);
        return Result.success(data);
    }

    /**
     * 车主账号导出
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportCarrierContact(SearchCarrierContactRequestDto requestDto, HttpServletResponse response) {
        SearchCarrierContactRequestModel model = MapperUtils.mapper(requestDto, SearchCarrierContactRequestModel.class);
        model.setPageNum(CommonConstant.INTEGER_ZERO);
        model.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<SearchCarrierContactResponseModel>> infoResult = carrierContactAccountApi.searchList(model);
        infoResult.throwException();
        List<SearchCarrierContactResponseDto> dtoList = MapperUtils.mapper(infoResult.getData().getList(), SearchCarrierContactResponseDto.class, new CarrierContactListMapping());
        String fileName = "车主账号" + DateUtils.dateToString(new Date(), CommonConstant.DATE_TO_STRING_YMD_PATTERN);
        Map<String, String> exportTypeMap = ExportExcelCarrierContact.getExportCarrierAccount();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportTypeMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }

    /**
     * 车主账号详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<CarrierContactDetailResponseDto> getDetail(@RequestBody @Valid CarrierContactDetailRequestDto requestDto) {
        Result<CarrierContactDetailResponseModel> result = carrierContactAccountApi.getDetail(MapperUtils.mapper(requestDto, CarrierContactDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),CarrierContactDetailResponseDto.class,new CarrierContactDetailMapping()));
    }

    /**
     * 新增编辑车主账号
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> saveAccount(@RequestBody @Valid SaveCarrierContactRequestDto requestDto) {
        if(StringUtils.isNotBlank(requestDto.getIdentityNumber()) && !IDCardValidator.isValidatedAllIdcard(requestDto.getIdentityNumber())){
            throw new BizException(ManagementWebApiExceptionEnum.ID_CARD_FORMAT_ERROR);
        }
        return carrierContactAccountApi.saveAccount(MapperUtils.mapper(requestDto, SaveCarrierContactRequestModel.class));
    }

    /**
     * 启用禁用车主账号
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> enableDisableClosed(@RequestBody @Valid CarrierContactEnableRequestDto requestDto) {
        Result<Boolean> result = carrierContactAccountApi.enableDisableClosed(MapperUtils.mapper(requestDto, CarrierContactEnableRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 车主账号删除
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delCarrierAccount(@RequestBody @Valid DelCarrierContactRequestDto requestDto) {
        Result<Boolean> result = carrierContactAccountApi.delCarrierAccount(MapperUtils.mapper(requestDto, DelCarrierContactRequestModel.class));
        result.throwException();
        return Result.success(true);
    }
}
