package com.logistics.tms.client.feign.tray.order.customerinorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RollbackDemandRequestModel {


    @ApiModelProperty(name = "需求单号")
    private String logisticsDemandCode;

    @ApiModelProperty("回退原因类型一级：2 客户原因，3 不可抗力，4 物流原因，5 平台问题，6 其他原因")
    private Integer rollbackCauseType;
    @ApiModelProperty("回退原因类型二级：201 重复上报，202 更换签收单抬头，203 数据报错，重新下单，204 地址原因，205 等待问题，206 托盘占用，207 流向核对，208 不配合装车，209 客户临时有事，210 现场电话联系不上；301 恶劣天气，302 政府管制，303 修路，304 洪涝；401 回收不及时，402 车辆已满载；501 重复下单，502 操作不规范；601 物流提货现场并单")
    private Integer rollbackCauseTypeTwo;

    @ApiModelProperty("回退备注")
    private String rollbackRemark;


    @ApiModelProperty("是否是tms直接取消的")
    private Integer ifTmsCancel;


}
