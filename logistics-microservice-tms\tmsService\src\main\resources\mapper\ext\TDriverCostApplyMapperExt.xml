<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDriverCostApplyMapper">
    <sql id="Base_Column_List_Decrypt" >
        id, cost_apply_code, staff_id, staff_name,AES_DECRYPT(UNHEX(staff_mobile),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as staff_mobile,
    staff_property, vehicle_id, vehicle_no, cost_type,
    apply_cost, occurrence_time, apply_time, apply_remark, associated_oil_card, audit_status,
    auditor_name, audit_time, remark, created_by, created_time, last_modified_by, last_modified_time,
    valid
    </sql>

    <select id="selectByPrimaryKeyDecrypt" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt" />
        from t_driver_cost_apply
        where id = #{id,jdbcType=BIGINT}
        and valid = 1
    </select>

    <select id="searchCostApplyList" resultType="com.logistics.tms.controller.drivercostapply.response.SearchCostApplyListResponseModel">
        select DISTINCT
        tdcp.id                                                                                                                as driverCostApplyId,
        tdcp.audit_status                                                                                                      as auditStatus,
        tdcp.cost_apply_code                                                                                                   as costApplyCode,
        tdcp.staff_name                                                                                                        as staffName,
        AES_DECRYPT(UNHEX(tdcp.staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as staffMobile,
        tdcp.staff_property                                                                                                    as staffProperty,
        tdcp.vehicle_no                                                                                                        as vehicleNo,
        tdcp.cost_type                                                                                                         as costType,
        tdcp.apply_cost                                                                                                        as applyCost,
        tdcp.occurrence_time                                                                                                   as occurrenceTime,
        tdcp.apply_time                                                                                                        as applyTime,
        tdcp.associated_oil_card                                                                                               as associatedOilCard,
        tdcp.audit_time                                                                                                        as auditTime,
        tdcp.auditor_name                                                                                                      as auditorName,
        IF(tdcai.id is NULL, 0, 1)                                                                                             as invoice
        FROM t_driver_cost_apply tdcp
                 LEFT OUTER JOIN t_driver_cost_apply_invoice tdcai ON tdcp.id = tdcai.driver_cost_apply_id AND tdcai.valid = 1
        where tdcp.valid = 1
        <if test="staffName != null and staffName != ''">
            and (instr(tdcp.staff_name, #{staffName,jdbcType=VARCHAR})
                or instr(AES_DECRYPT(UNHEX(tdcp.staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{staffName,jdbcType=VARCHAR}))
        </if>
        <if test="staffProperty != null">
            and tdcp.staff_property = #{staffProperty,jdbcType=INTEGER}
        </if>
        <if test="vehicleNo != null and vehicleNo != ''">
            and instr(tdcp.vehicle_no, #{vehicleNo,jdbcType=VARCHAR})
        </if>
        <if test="costType != null">
            and tdcp.cost_type = #{costType,jdbcType=INTEGER}
        </if>
        <if test="invoice != null and invoice != ''">
            and IF(tdcai.id is NULL, 0, 1) = #{invoice,jdbcType=VARCHAR}
        </if>
        <if test="auditStatus != null">
            and tdcp.audit_status = #{auditStatus,jdbcType=INTEGER}
        </if>
        <if test="auditorName != null and auditorName != ''">
            and instr(tdcp.auditor_name, #{auditorName,jdbcType=VARCHAR})
        </if>
        <if test="auditTimeStart != null and auditTimeStart != '' and auditTimeEnd != null and auditTimeEnd != ''">
            and tdcp.audit_time &gt;= DATE_FORMAT(#{auditTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d 00:00:00')
            and tdcp.audit_time &lt;= DATE_FORMAT(#{auditTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="applyTimeStart != null and applyTimeStart != '' and applyTimeEnd != null and applyTimeEnd != ''">
            and tdcp.apply_time &gt;= DATE_FORMAT(#{applyTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d 00:00:00')
            and tdcp.apply_time &lt;= DATE_FORMAT(#{applyTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="applyTime != null and applyTime != ''">
            and DATE_FORMAT(tdcp.apply_time, '%Y-%m') = #{applyTime,jdbcType=VARCHAR}
        </if>
        <if test="staffId != null">
            and tdcp.staff_id = #{staffId,jdbcType=BIGINT}
        </if>
        <if test="driverCostApplyIds != null and driverCostApplyIds.size() != 0">
            and tdcp.id in
            <foreach collection="driverCostApplyIds" open="(" separator="," close=")" item="item">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <choose>
            <when test="source == 3">
                order by tdcp.apply_time desc, tdcp.id desc
            </when>
            <otherwise>
                order by tdcp.last_modified_time desc ,tdcp.id desc
            </otherwise>
        </choose>
    </select>

    <resultMap id="selectDriverCostApplyDetailMap" type="com.logistics.tms.controller.drivercostapply.response.DriverCostApplyDetailResponseModel">
        <id property="driverCostApplyId" column="driverCostApplyId"/>
        <result property="staffId" column="staffId"/>
        <result property="staffName" column="staffName"/>
        <result property="staffMobile" column="staffMobile"/>
        <result property="staffProperty" column="staffProperty"/>
        <result property="vehicleId" column="vehicleId"/>
        <result property="vehicleNo" column="vehicleNo"/>
        <result property="costType" column="costType"/>
        <result property="applyCost" column="applyCost"/>
        <result property="occurrenceTime" column="occurrenceTime"/>
        <result property="applyTime" column="applyTime"/>
        <result property="associatedOilCard" column="associatedOilCard"/>
        <result property="applyRemark" column="applyRemark"/>
        <result property="auditStatus" column="auditStatus"/>
        <result property="auditTime" column="auditTime"/>
        <result property="auditorName" column="auditorName"/>
        <result property="remark" column="remark"/>
        <collection property="ticketList" ofType="com.logistics.tms.controller.drivercostapply.response.DriverCostApplyTicketModel">
            <id column="tcpId" property="id"/>
            <result column="file_path" property="filePath"/>
            <result column="file_type" property="fileType"/>
        </collection>
    </resultMap>

    <select id="selectDriverCostApplyDetail"  resultMap="selectDriverCostApplyDetailMap">
        select
        tdcp.id                                                                                                                as driverCostApplyId,
        tdcp.staff_id                                                                                                          as staffId,
        tdcp.staff_name                                                                                                        as staffName,
        AES_DECRYPT(UNHEX(tdcp.staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as staffMobile,
        tdcp.staff_property                                                                                                    as staffProperty,
        tdcp.vehicle_id                                                                                                        as vehicleId,
        tdcp.vehicle_no                                                                                                        as vehicleNo,
        tdcp.cost_type                                                                                                         as costType,
        tdcp.apply_cost                                                                                                        as applyCost,
        tdcp.occurrence_time                                                                                                   as occurrenceTime,
        tdcp.apply_time                                                                                                        as applyTime,
        tdcp.associated_oil_card                                                                                               as associatedOilCard,
        tdcp.apply_remark                                                                                                      as applyRemark,
        tdcp.audit_status                                                                                                      as auditStatus,
        tdcp.audit_time                                                                                                        as auditTime,
        tdcp.auditor_name                                                                                                      as auditorName,
        tdcp.remark                                                                                                            as remark,
        tcp.id                                                                                                                 as tcpId,
        tcp.file_path                                                                                                          ,
        tcp.file_type
        from t_driver_cost_apply tdcp
        left join t_certification_pictures tcp on tdcp.id = tcp.object_id and object_type = 29 and tcp.valid = 1
        where tdcp.id = #{driverCostApplyId,jdbcType=BIGINT}
        <if test="driverId != null">
        and tdcp.staff_id = #{driverId,jdbcType=BIGINT}
        </if>
        and tdcp.valid = 1
    </select>

    <select id="searchCostStatisticsByApplyTime" resultType="com.logistics.tms.controller.drivercostapply.response.DriverCostStatisticsModel">
        select
        ifnull(count(id),0)                                        as applyCount,
        ifnull(sum(if(audit_status = 1, 1, 0)), 0)               as approvalCount,
        ifnull(sum(if(audit_status = 1, tdcp.apply_cost, 0)), 0) as approvalFee
        from t_driver_cost_apply tdcp
        where tdcp.valid = 1
        <if test="applyTime != null and applyTime != ''">
            and DATE_FORMAT(tdcp.apply_time, '%Y-%m') = #{applyTime,jdbcType=VARCHAR}
        </if>
        <if test="loginDriverUserId != null">
            and tdcp.staff_id = #{loginDriverUserId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectByIdAndDriverId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_driver_cost_apply
        where id = #{driverCostApplyId,jdbcType=BIGINT}
        <if test="staffId != null">
            and staff_id = #{staffId,jdbcType=BIGINT}
        </if>
        and valid = 1
    </select>

    <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TDriverCostApply" keyProperty="id" useGeneratedKeys="true">
        insert into t_driver_cost_apply
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="costApplyCode != null">
                cost_apply_code,
            </if>
            <if test="staffId != null" >
                staff_id,
            </if>
            <if test="staffName != null" >
                staff_name,
            </if>
            <if test="staffMobile != null" >
                staff_mobile,
            </if>
            <if test="staffProperty != null" >
                staff_property,
            </if>
            <if test="vehicleId != null" >
                vehicle_id,
            </if>
            <if test="vehicleNo != null" >
                vehicle_no,
            </if>
            <if test="costType != null" >
                cost_type,
            </if>
            <if test="applyCost != null" >
                apply_cost,
            </if>
            <if test="occurrenceTime != null" >
                occurrence_time,
            </if>
            <if test="applyTime != null" >
                apply_time,
            </if>
            <if test="applyRemark != null" >
                apply_remark,
            </if>
            <if test="associatedOilCard != null" >
                associated_oil_card,
            </if>
            <if test="auditStatus != null" >
                audit_status,
            </if>
            <if test="auditorName != null" >
                auditor_name,
            </if>
            <if test="auditTime != null" >
                audit_time,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="createdBy != null" >
                created_by,
            </if>
            <if test="createdTime != null" >
                created_time,
            </if>
            <if test="lastModifiedBy != null" >
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null" >
                last_modified_time,
            </if>
            <if test="valid != null" >
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="costApplyCode != null">
                #{costApplyCode,jdbcType=VARCHAR},
            </if>
            <if test="staffId != null">
                #{staffId,jdbcType=BIGINT},
            </if>
            <if test="staffName != null">
                #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="staffMobile != null">
                HEX(AES_ENCRYPT(#{staffMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="staffProperty != null">
                #{staffProperty,jdbcType=INTEGER},
            </if>
            <if test="vehicleId != null">
                #{vehicleId,jdbcType=BIGINT},
            </if>
            <if test="vehicleNo != null">
                #{vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="costType != null">
                #{costType,jdbcType=INTEGER},
            </if>
            <if test="applyCost != null">
                #{applyCost,jdbcType=DECIMAL},
            </if>
            <if test="occurrenceTime != null">
                #{occurrenceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applyTime != null">
                #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applyRemark != null">
                #{applyRemark,jdbcType=VARCHAR},
            </if>
            <if test="associatedOilCard != null">
                #{associatedOilCard,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="auditorName != null">
                #{auditorName,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null">
                #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TDriverCostApply" >
        update t_driver_cost_apply
        <set >
            <if test="costApplyCode != null">
                cost_apply_code = #{costApplyCode,jdbcType=VARCHAR},
            </if>
            <if test="staffId != null" >
                staff_id = #{staffId,jdbcType=BIGINT},
            </if>
            <if test="staffName != null" >
                staff_name = #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="staffMobile != null" >
                staff_mobile = HEX(AES_ENCRYPT(#{staffMobile,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="staffProperty != null" >
                staff_property = #{staffProperty,jdbcType=INTEGER},
            </if>
            <if test="vehicleId != null" >
                vehicle_id = #{vehicleId,jdbcType=BIGINT},
            </if>
            <if test="vehicleNo != null" >
                vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="costType != null" >
                cost_type = #{costType,jdbcType=INTEGER},
            </if>
            <if test="applyCost != null" >
                apply_cost = #{applyCost,jdbcType=DECIMAL},
            </if>
            <if test="occurrenceTime != null" >
                occurrence_time = #{occurrenceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applyTime != null" >
                apply_time = #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applyRemark != null" >
                apply_remark = #{applyRemark,jdbcType=VARCHAR},
            </if>
            <if test="associatedOilCard != null" >
                associated_oil_card = #{associatedOilCard,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null" >
                audit_status = #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="auditorName != null" >
                auditor_name = #{auditorName,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null" >
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null" >
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null" >
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null" >
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null" >
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="searchCostApplySummaryIds" resultType="java.lang.String">
        select group_concat(id)
        from t_driver_cost_apply
        where valid = 1
        and audit_status = 1
        <if test="staffName != null and staffName != ''">
            and (instr(staff_name, #{staffName,jdbcType=VARCHAR}) or
                 instr(AES_DECRYPT(UNHEX(staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'), #{staffName,jdbcType=VARCHAR}))
        </if>
        <if test="staffProperty != null">
            and staff_property = #{staffProperty,jdbcType=INTEGER}
        </if>
        <if test="occurrenceTimeStart != null and occurrenceTimeStart != '' and occurrenceTimeEnd != null and occurrenceTimeEnd != ''">
            and occurrence_time >= str_to_date(#{occurrenceTimeStart,jdbcType=VARCHAR}, '%Y-%m-%d')
            and occurrence_time &lt;= str_to_date(#{occurrenceTimeEnd,jdbcType=VARCHAR}, '%Y-%m-%d')
        </if>
        GROUP BY staff_id, date_format(occurrence_time, '%Y-%m-%d'), staff_property
        order by date_format(occurrence_time, '%Y-%m-%d') desc, AES_DECRYPT(UNHEX(staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') desc
    </select>

    <resultMap id="searchCostApplySummary_Map" type="com.logistics.tms.controller.drivercostapply.response.SearchCostApplySummaryResponseModel">
        <result column="staffId" property="staffId" jdbcType="BIGINT"/>
        <result column="staffName" property="staffName" jdbcType="VARCHAR"/>
        <result column="staffMobile" property="staffMobile" jdbcType="VARCHAR"/>
        <result column="staffProperty" property="staffProperty" jdbcType="INTEGER"/>
        <result column="occurrenceTime" property="occurrenceTime" jdbcType="TIMESTAMP"/>

        <collection property="costList" ofType="com.logistics.tms.controller.drivercostapply.response.SearchCostApplySummaryListResponseModel">
            <result column="driverCostApplyId" property="driverCostApplyId" jdbcType="BIGINT"/>
            <result column="costType" property="costType" jdbcType="INTEGER"/>
            <result column="applyCost" property="applyCost" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="searchCostApplySummary" resultMap="searchCostApplySummary_Map">
        select
        id as driverCostApplyId,
        staff_id                                                                                                          as staffId,
        staff_name                                                                                                        as staffName,
        AES_DECRYPT(UNHEX(staff_mobile), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as staffMobile,
        staff_property                                                                                                    as staffProperty,
        date_format(occurrence_time, '%Y-%m-%d')                                                                          as occurrenceTime,
        cost_type                                                                                                         as costType,
        apply_cost                                                                                                        as applyCost
        from t_driver_cost_apply
        where valid =1
        and id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        order by occurrenceTime desc, staffMobile desc
    </select>

    <select id="selectByOilCardNumAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt" />
        from t_driver_cost_apply
        where associated_oil_card = #{cardNumber,jdbcType=VARCHAR}
        and audit_status in (${auditStatus})
        limit 1
    </select>

    <select id="statisticsAwaitVerificationAmountByDriver" resultType="com.logistics.tms.biz.reservebalance.model.DriverBalanceDetailModel">
        SELECT staff_id driverId,
        IFNULL(SUM(apply_cost), 0) awaitVerificationAmount
        FROM t_driver_cost_apply
        WHERE valid = 1
        AND audit_status IN (0, 2)
        AND staff_property = 1
        AND staff_id IN
        <foreach collection="driverIds" item="driverId" separator="," open="(" close=")">
            #{driverId}
        </foreach>
        GROUP BY staff_id
    </select>
</mapper>