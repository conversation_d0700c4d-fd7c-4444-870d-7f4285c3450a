package com.logistics.management.webapi.controller.carrierorderticketsaudit;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.client.carrierorderticketsaudit.ReceiptAuditClient;
import com.logistics.management.webapi.client.carrierorderticketsaudit.enums.CarrierOrderTicketsAuditStatusEnum;
import com.logistics.management.webapi.client.carrierorderticketsaudit.response.GetReceiptAuditDetailResponseModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.response.SearchReceiptAuditListResponseModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.request.GetReceiptAuditDetailRequestModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.request.ReceiptAgainAuditRequestModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.request.ReceiptAuditRequestModel;
import com.logistics.management.webapi.client.carrierorderticketsaudit.request.SearchReceiptAuditListRequestModel;
import com.logistics.management.webapi.controller.carrierorderticketsaudit.mapping.GetReceiptAuditDetailMapping;
import com.logistics.management.webapi.controller.carrierorderticketsaudit.mapping.SearchReceiptAuditListMapping;
import com.logistics.management.webapi.controller.carrierorderticketsaudit.request.GetReceiptAuditDetailRequestDto;
import com.logistics.management.webapi.controller.carrierorderticketsaudit.request.ReceiptAgainAuditRequestDto;
import com.logistics.management.webapi.controller.carrierorderticketsaudit.request.ReceiptAuditRequestDto;
import com.logistics.management.webapi.controller.carrierorderticketsaudit.request.SearchReceiptAuditListRequestDto;
import com.logistics.management.webapi.controller.carrierorderticketsaudit.response.GetReceiptAuditDetailResponseDto;
import com.logistics.management.webapi.controller.carrierorderticketsaudit.response.SearchReceiptAuditListResponseDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/03/27
 */
@Api(value = "API-ReceiptReviewApi", tags = "回单审核管理")
@RestController
public class ReceiptAuditController{

    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private ReceiptAuditClient receiptAuditClient;

    /**
     * 分页查询回单审核列表
     * @param requestDto 请求 DTO
     * @return 回单审核分页列表
     */
    @ApiOperation(value = "回单审核列表", tags = "1.3.1")
    @PostMapping("/api/receiptReviewManagement/searchList")
    public Result<PageInfo<SearchReceiptAuditListResponseDto>> searchList(@RequestBody SearchReceiptAuditListRequestDto requestDto) {
        SearchReceiptAuditListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchReceiptAuditListRequestModel.class);
        Result<PageInfo<SearchReceiptAuditListResponseModel>> result = receiptAuditClient.searchList(requestModel);
        result.throwException();
        PageInfo pageInfo = result.getData();
        pageInfo.setList(MapperUtils.mapper(pageInfo.getList(), SearchReceiptAuditListResponseDto.class, new SearchReceiptAuditListMapping()));
        return Result.success(pageInfo);
    }

    /**
     * 回单审核详情
     * @param requestDto 请求 Dto
     * @return GetReceiptAuditDetailResponseDto
     */
    @ApiOperation(value = "回单审核详情", tags = "1.2.9")
    @PostMapping("/api/receiptReviewManagement/getDetail")
    public Result<GetReceiptAuditDetailResponseDto> getDetail(@Valid @RequestBody GetReceiptAuditDetailRequestDto requestDto) {
        GetReceiptAuditDetailRequestModel requestModel = MapperUtils.mapper(requestDto, GetReceiptAuditDetailRequestModel.class);
        Result<GetReceiptAuditDetailResponseModel> result = receiptAuditClient.getDetail(requestModel);
        result.throwException();
        Map<String, String> imageMap = Optional.ofNullable(result.getData())
                .map(s -> commonBiz.batchGetOSSFileUrl(s.getTicketImages()))
                .orElse(new HashMap<>());
        GetReceiptAuditDetailResponseDto responseDto = MapperUtils.mapper(result.getData(), GetReceiptAuditDetailResponseDto.class,
                new GetReceiptAuditDetailMapping(configKeyConstant.fileAccessAddress, imageMap));
        return Result.success(responseDto);
    }

    /**
     * 回单审核
     * @param requestDto 请求Dto
     * @return boolean
     */
    @ApiOperation(value = "回单审核", tags = "1.2.9")
    @PostMapping("/api/receiptReviewManagement/audit")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> audit(@Valid @RequestBody ReceiptAuditRequestDto requestDto) {
        if (CarrierOrderTicketsAuditStatusEnum.REJECT_AUDIT.getKey().equals(Integer.valueOf(requestDto.getAuditStatus()))) {
            if (StringUtils.isBlank(requestDto.getRemark())) {
                throw new BizException(ManagementWebApiExceptionEnum.REMARK_EMPTY);
            }
        }
        ReceiptAuditRequestModel requestModel = MapperUtils.mapper(requestDto, ReceiptAuditRequestModel.class);
        return receiptAuditClient.audit(requestModel);
    }

    /**
     * 重新审核
     * @param requestDto 请求Dto
     * @return boolean
     */
    @ApiOperation(value = "回单重新审核", tags = "1.2.9")
    @PostMapping("/api/receiptReviewManagement/againAudit")
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> againAudit(@Valid @RequestBody ReceiptAgainAuditRequestDto requestDto) {
        ReceiptAgainAuditRequestModel requestModel = MapperUtils.mapper(requestDto, ReceiptAgainAuditRequestModel.class);
        return receiptAuditClient.againAudit(requestModel);
    }
}
