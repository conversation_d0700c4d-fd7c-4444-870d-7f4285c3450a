package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运单列表数量统计
 *
 * <AUTHOR>
 * @date 2022/8/18 9:28
 */
@ApiModel("运单列表数量统计")
@Data
public class SearchCarrierListStatisticsResponseDto {

    @ApiModelProperty("全部")
    private String allCount = "0";
    @ApiModelProperty("待到达提货地")
    private String waitArriveLoadSiteCount ="0";
    @ApiModelProperty("待提货")
    private String waitLoadCount = "0";
    @ApiModelProperty("待到达卸货地")
    private String waitArriveUnloadSiteCount = "0";
    @ApiModelProperty("待卸货")
    private String waitUnloadCount = "0";
    @ApiModelProperty("待签收数量")
    private String waitSignedAccount="0";
    @ApiModelProperty("已签收数量")
    private String signedAccount="0";
    @ApiModelProperty("已取消")
    private String cancelCount = "0";
}
