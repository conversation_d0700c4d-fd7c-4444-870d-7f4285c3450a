package com.logistics.tms.biz.carrierfreight.service;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.CarrierFreightConfigLadderModeEnum;
import com.logistics.tms.biz.carrierfreight.CarrierFreightConfigLadderBiz;
import com.logistics.tms.biz.carrierfreight.CarrierFreightConfigSchemeBiz;
import com.logistics.tms.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestModel;
import com.logistics.tms.controller.freightconfig.request.mileage.CarrierFreightConfigMileageAddRequestModel;
import com.logistics.tms.controller.freightconfig.request.mileage.CarrierFreightConfigMileageEditRequestModel;
import com.logistics.tms.controller.freightconfig.request.mileage.CarrierFreightConfigMileageRequestModel;
import com.logistics.tms.controller.freightconfig.response.ladder.CarrierFreightConfigPriceDesignResponseModel;
import com.logistics.tms.controller.freightconfig.response.mileage.CarrierFreightConfigMileageResponseModel;
import com.logistics.tms.entity.TCarrierFreightConfigLadder;
import com.logistics.tms.entity.TCarrierFreightConfigScheme;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CarrierFreightConfigMileageService {

    @Resource
    private CarrierFreightConfigSchemeBiz carrierFreightConfigSchemeBiz;

    @Resource
    private CarrierFreightConfigLadderBiz carrierFreightConfigLadderBiz;

    /**
     * 查询里程数配置详情
     *
     * @param requestModel 请求Model
     * @return 里程数配置详情
     */
    public CarrierFreightConfigMileageResponseModel detail(CarrierFreightConfigMileageRequestModel requestModel) {

        CarrierFreightConfigMileageResponseModel responseModel = new CarrierFreightConfigMileageResponseModel();
        // 根据配置ID查询方案信息
        List<TCarrierFreightConfigScheme> configSchemes = carrierFreightConfigSchemeBiz.getConfigScheme(requestModel.getFreightConfigId());
        if (ListUtils.isNotEmpty(configSchemes)) {
            TCarrierFreightConfigScheme schemeConfig = configSchemes.get(CommonConstant.INTEGER_ZERO);
            // 根据方案查询阶梯配置信息
            Integer ladderMode = CarrierFreightConfigLadderModeEnum.SCHEME_CONFIG.getKey();
            Map<Long, CarrierFreightConfigPriceDesignResponseModel> ladderDetailMap =
                    carrierFreightConfigLadderBiz.getLadderDetail(ladderMode, Collections.singletonList(schemeConfig.getId()));
            responseModel.setPriceDesign(ladderDetailMap.get(schemeConfig.getId()));
            responseModel.setFreightConfigSchemeId(schemeConfig.getId());
            responseModel.setSchemeType(schemeConfig.getSchemeType());
        }
        return responseModel;
    }

    /**
     * 新增里程数配置
     *
     * @param requestModel 请求 Model
     * @return boolean
     */
    @Transactional
    public boolean add(CarrierFreightConfigMileageAddRequestModel requestModel) {
        // 新增方案
        TCarrierFreightConfigScheme scheme =
                carrierFreightConfigSchemeBiz.addConfigScheme(requestModel.getFreightConfigId(), requestModel.getSchemeType());
        // 新增阶梯
        CarrierFreightConfigLadderModeEnum ladderModeEnum =
                CarrierFreightConfigLadderModeEnum.getEnumBySchemeType(scheme.getSchemeType());
        carrierFreightConfigLadderBiz.add(ladderModeEnum.getKey(),
                scheme.getId(),
                CommonConstant.LONG_ZERO,
                requestModel.getLadderConfigList());
        return true;
    }

    /**
     * 编辑里程数配置
     *
     * @param requestModel 请求 Model
     * @return boolean
     */
    @Transactional
    public boolean edit(CarrierFreightConfigMileageEditRequestModel requestModel) {

        // 查询方案配置
        TCarrierFreightConfigScheme configScheme = carrierFreightConfigSchemeBiz.getConfigSchemeById(requestModel.getFreightConfigSchemeId())
                .orElseThrow(() -> new BizException(CarrierDataExceptionEnum.CARRIER_FREIGHT_CONFIG_SCHEME_NOT_EXIST));

        // 编辑方案
        carrierFreightConfigSchemeBiz.editSchemeType(configScheme.getId(), requestModel.getSchemeType());

        List<CarrierFreightConfigLadderRequestModel> ladderList = requestModel.getLadderConfigList();
        Map<Boolean, List<CarrierFreightConfigLadderRequestModel>> ladderMap = ladderList
                .stream()
                .collect(Collectors.partitioningBy(p -> Objects.nonNull(p.getFreightConfigLadderId())));

        // 新增的阶梯
        CarrierFreightConfigLadderModeEnum ladderModeEnum = CarrierFreightConfigLadderModeEnum.getEnumBySchemeType(requestModel.getSchemeType());
        List<CarrierFreightConfigLadderRequestModel> addLadderList = ladderMap.get(Boolean.FALSE);
        List<CarrierFreightConfigLadderRequestModel> editLadderList = ladderMap.get(Boolean.TRUE);

        // 移除的阶梯
        Integer ladderMode = CarrierFreightConfigLadderModeEnum.SCHEME_CONFIG.getKey();
        List<Long> ladderIds = editLadderList
                .stream()
                .map(CarrierFreightConfigLadderRequestModel::getAllLadderId)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        List<Long> deleteIds = carrierFreightConfigLadderBiz.getLadderByModeId(ladderMode, configScheme.getId())
                .stream()
                .map(TCarrierFreightConfigLadder::getId)
                .filter(id -> !ladderIds.contains(id))
                .collect(Collectors.toList());
        carrierFreightConfigLadderBiz.delete(deleteIds);

        // 编辑的阶梯
        carrierFreightConfigLadderBiz.schemeLadderEdit(configScheme.getId(), editLadderList);

        // 新增阶梯
        carrierFreightConfigLadderBiz.add(ladderModeEnum.getKey(),
                requestModel.getFreightConfigSchemeId(),
                CommonConstant.LONG_ZERO,
                addLadderList);
        return true;
    }
}
