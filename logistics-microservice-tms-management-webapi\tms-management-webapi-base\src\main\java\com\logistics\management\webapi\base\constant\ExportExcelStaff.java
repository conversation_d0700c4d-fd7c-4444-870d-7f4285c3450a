package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/6/10 17:44
 */
public class ExportExcelStaff {
    private ExportExcelStaff() {
    }

    private static final Map<String, String> EXPORT_STAFF;

    static {
        EXPORT_STAFF = new LinkedHashMap<>();
        EXPORT_STAFF.put("实名状态", "realNameAuthenticationStatusLabel");
        EXPORT_STAFF.put("人员类型", "type");
        EXPORT_STAFF.put("人员机构","StaffPropertyLabel");
        EXPORT_STAFF.put("性别", "gender");
        EXPORT_STAFF.put("人员姓名", "name");
        EXPORT_STAFF.put("关联车主", "companyCarrierName");
        EXPORT_STAFF.put("手机号", "mobile");
        EXPORT_STAFF.put("身份证号码", "identityNumber");
        EXPORT_STAFF.put("年龄", "age");
        EXPORT_STAFF.put("身份证期限", "identityValidity");
        EXPORT_STAFF.put("劳动合同编号", "laborContractNo");
        EXPORT_STAFF.put("劳动合同有效期", "laborContractValidDate");
        EXPORT_STAFF.put("从业资格证证号", "occupationalRequirementsCredentialNo");
        EXPORT_STAFF.put("初次发证日期", "initialIssuanceDate");
        EXPORT_STAFF.put("机动车驾驶证号", "driversLicenseNo");
        EXPORT_STAFF.put("准驾车型", "permittedType");
        EXPORT_STAFF.put("驾照开始时间", "driversLicenseDateFrom");
        EXPORT_STAFF.put("驾照截止时间", "driversLicenseDateTo");
        EXPORT_STAFF.put("从业资格证发证期", "occupationalIssueDate");
        EXPORT_STAFF.put("从业资格证有效期", "occupationalValidDate");
        EXPORT_STAFF.put("诚信考核有效期", "examinationValidDate");
        EXPORT_STAFF.put("继续教育有效期", "learningValidDate");

    }

    public static Map<String, String> getExportStaff() {
        return EXPORT_STAFF;
    }
}
