package com.logistics.tms.api.feign.insuarance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/6/4 19:20
 */
@Data
public class GetInsuranceDetailResponseModel {
    @ApiModelProperty("保险id")
    private Long insuranceId;
    @ApiModelProperty("状态类型：0 正常状态，1 取消，2 退保")
    private Integer statusType;
    @ApiModelProperty("险种：1 商业险，2 交强险，3 个人意外险，4 货物险，5 危货承运人险")
    private Integer insuranceType;
    @ApiModelProperty("车牌Id")
    private Long vehicleId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机id")
    private Long driverId;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driverPhone;
    @ApiModelProperty("保险公司id")
    private Long insuranceCompanyId;
    @ApiModelProperty("保险公司")
    private String insuranceCompanyName;
    @ApiModelProperty("保单号")
    private String policyNumber;
    @ApiModelProperty("保费")
    private BigDecimal premium;
    @ApiModelProperty("保险生效时间")
    private Date startTime;
    @ApiModelProperty("保险截止时间")
    private Date endTime;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("凭证")
    private List<InsuranceTicketsResponseModel> ticketList;
    @ApiModelProperty("退保证明")
    private String refundPath;

    @ApiModelProperty("代缴车船税")
    private BigDecimal paymentOfVehicleAndVesselTax;

    @ApiModelProperty("个人意外险表id")
    private Long personalAccidentInsuranceId;
    @ApiModelProperty("批单号")
    private String batchNumber;
    @ApiModelProperty("保单类型：1 保单，2 批单")
    private Integer policyType;

    @ApiModelProperty("保险公司id")
    private Long insuranceCompanyIdPerson;
    @ApiModelProperty("保险公司")
    private String insuranceCompanyNamePerson;
    @ApiModelProperty("保单号")
    private String policyNumberPerson;
    @ApiModelProperty("保险生效时间")
    private Date startTimePerson;
    @ApiModelProperty("保险截止时间")
    private Date endTimePerson;
    @ApiModelProperty("保单总额")
    private BigDecimal grossPremiumPerson;
    @ApiModelProperty("保单人数")
    private Integer policyPersonCountPerson;

    @ApiModelProperty("关联扣费保单id")
    private Long relatedPersonalAccidentInsuranceId;
    @ApiModelProperty("保单类型：1 保单，2 批单")
    private Integer relatedPolicyType;
    @ApiModelProperty("保单号")
    private String relatedPolicyNumber;
    @ApiModelProperty("批单号")
    private String relatedBatchNumber;
    @ApiModelProperty("保单总额")
    private BigDecimal relatedGrossPremium;
    @ApiModelProperty("保单人数")
    private Integer relatedPolicyPersonCount;

    @ApiModelProperty("保单状态：结算状态：-1 未开始，0 待结算，1 部分结算，2 已结算")
    private Integer settlementStatus;

    @ApiModelProperty(value = "是否有账单月数据,1有，0没有")
    private Integer ifSettlement = 0;

}
