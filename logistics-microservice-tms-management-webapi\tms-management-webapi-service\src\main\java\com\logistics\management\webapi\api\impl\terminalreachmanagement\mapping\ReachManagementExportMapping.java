package com.logistics.management.webapi.api.impl.terminalreachmanagement.mapping;

import com.logistics.management.webapi.api.feign.terminalreachmanagement.dto.ReachManagementExportResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CheckReachContactEnum;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public class ReachManagementExportMapping extends MapperMapping<SearchReachManagementListResponseModel, ReachManagementExportResponseDto> {

    @Override
    public void configure() {
        SearchReachManagementListResponseModel source = getSource();
        ReachManagementExportResponseDto destination = getDestination();
        if (source != null){
            destination.setLoadDetailAddress(source.getLoadProvinceName() + source.getLoadCityName() + source.getLoadAreaName() + source.getLoadDetailAddress());

            destination.setReachDriver(source.getReachDriverName() + " " + source.getReachDriverPhone());
            destination.setCheckReachContactLabel(CheckReachContactEnum.getEnum(source.getCheckReachContact()).getValue());

            destination.setReachAddress(source.getReachProvinceName() + source.getReachCityName() + source.getReachAreaName() + source.getReachAddressDetail());

            destination.setDistanceDeviation(Objects.isNull(source.getDistanceDeviation())? CommonConstant.ZERO:new BigDecimal(source.getDistanceDeviation()).setScale(CommonConstant.INTEGER_TWO, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
        }

    }

}
