package com.logistics.management.webapi.api.impl.parkingfee.mapping;

import com.logistics.management.webapi.api.feign.parkingfee.dto.ParkingFeeOperationRecordResponsesDto;
import com.logistics.tms.api.feign.parkingfee.dto.ParkingFeeOperationRecordResponsesModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

/**
 * @Author: sj
 * @Date: 2019/10/9 16:32
 */
public class ParkingFeeOperationRecordMapping extends MapperMapping<ParkingFeeOperationRecordResponsesModel,ParkingFeeOperationRecordResponsesDto> {
    @Override
    public void configure() {
        ParkingFeeOperationRecordResponsesModel source = this.getSource();
        ParkingFeeOperationRecordResponsesDto target = this.getDestination();
        if(source!=null){
            if(source.getParkingFee() != null && source.getCooperationPeriod() != null){
                target.setParkingFeeLabel(source.getParkingFee()+"*"+source.getCooperationPeriod());
            }
            if(source.getStartDate() != null){
                target.setStartDate(DateUtils.dateToString(source.getStartDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getEndDate() != null){
                target.setEndDate(DateUtils.dateToString(source.getEndDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getFinishDate() != null){
                target.setFinishDate(DateUtils.dateToString(source.getFinishDate(),DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
            if(source.getLastModifiedTime() != null){
                target.setLastModifiedTime(DateUtils.dateToString(source.getLastModifiedTime(),DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
            }

        }
    }
}
