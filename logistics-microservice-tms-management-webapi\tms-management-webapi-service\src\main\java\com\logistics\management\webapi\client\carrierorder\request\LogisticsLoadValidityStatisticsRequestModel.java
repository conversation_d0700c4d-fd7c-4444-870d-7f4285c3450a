package com.logistics.management.webapi.client.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/10/27 14:39
 */
@Data
public class LogisticsLoadValidityStatisticsRequestModel {
    @ApiModelProperty("日期范围：起始时间（yyyy-MM）")
    private String yearMonthStart;
    @ApiModelProperty("日期范围：结束时间（yyyy-MM）")
    private String yearMonthEnd;
}
