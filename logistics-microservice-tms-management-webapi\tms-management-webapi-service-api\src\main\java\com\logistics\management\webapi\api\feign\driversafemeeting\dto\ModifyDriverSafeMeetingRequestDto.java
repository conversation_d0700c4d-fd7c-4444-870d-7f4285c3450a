package com.logistics.management.webapi.api.feign.driversafemeeting.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2020/6/5 16:13
 */
@Data
public class ModifyDriverSafeMeetingRequestDto {
    @ApiModelProperty(value = "学习例会id",required = true)
    @NotBlank(message = "id不能为空")
    private String safeMeetingId;
    @ApiModelProperty(value = "学习标题",required = true)
    @Size(min = 1, max = 255, message = "请填写标题，1-255字")
    private String title;
    @ApiModelProperty(value = "学习简介",required = true)
    @Size(min = 1, max = 255, message = "请填写简介，1-255字")
    private String introduction;
    @ApiModelProperty(value = "学习内容",required = true)
    @Size(min = 1, max = 20000, message = "请填写内容，1-10000字")
    private String content;
}
