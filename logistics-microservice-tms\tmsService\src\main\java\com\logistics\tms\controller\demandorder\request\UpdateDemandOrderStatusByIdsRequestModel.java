package com.logistics.tms.controller.demandorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/3/14 13:24
 */
@Data
public class UpdateDemandOrderStatusByIdsRequestModel {
    @ApiModelProperty("需求单ID")
    private List<Long> demandOrderIdList;
    @ApiModelProperty("需求单状态")
    private Integer entrustStatus;
    @ApiModelProperty("操作人")
    private String operatorName;

    @ApiModelProperty("单个需求单时，某个运单的差异数")
    private BigDecimal differenceAmount;
}
