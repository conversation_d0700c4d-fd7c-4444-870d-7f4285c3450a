package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.vehicleassetmanagement.response.GetGpsInfoByVehicleNoResponseModel;
import com.logistics.tms.entity.TVehicleDrivingLicense;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface TVehicleDrivingLicenseMapper extends BaseMapper<TVehicleDrivingLicense> {

    TVehicleDrivingLicense getByVehicleNo(@Param("vehicleNo") String vehicleNo);

    TVehicleDrivingLicense getByVehicleId(@Param("vehicleId") Long vehicleId);

    List<TVehicleDrivingLicense> getListByVehicleIds(@Param("vehicleIds") String vehicleIds);

    int deleteDrivingLicenseInfo(@Param("vehicleIds") String vehicleIds, @Param("modifiedBy") String modifiedBy, @Param("modifiedTime") Date modifiedTime);

    Map<String,String> getDueObsolescenceCount(@Param("companyCarrierId") Long companyCarrierId,@Param("remindDays") Integer remindDays);

    int updateByPrimaryKeySelectiveExt(@Param("params") TVehicleDrivingLicense vehicleDrivingLicense);

    int updateByPrimaryKeySelectiveForExcel(@Param("params") TVehicleDrivingLicense vehicleDrivingLicense);

    TVehicleDrivingLicense getVehicleByVechileNo(@Param("vehicleNo")String vehicleNo);

    List<GetGpsInfoByVehicleNoResponseModel> getGpsInfoByVehicleNo(@Param("vehicleNo") String vehicleNo);
}