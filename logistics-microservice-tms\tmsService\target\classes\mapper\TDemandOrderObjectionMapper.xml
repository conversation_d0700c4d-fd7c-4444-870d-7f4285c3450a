<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDemandOrderObjectionMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDemandOrderObjection" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="demand_order_id" property="demandOrderId" jdbcType="BIGINT" />
    <result column="customer_name" property="customerName" jdbcType="VARCHAR" />
    <result column="objection_type" property="objectionType" jdbcType="INTEGER" />
    <result column="objection_reason" property="objectionReason" jdbcType="VARCHAR" />
    <result column="report_contact_name" property="reportContactName" jdbcType="VARCHAR" />
    <result column="report_time" property="reportTime" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, demand_order_id, customer_name, objection_type, objection_reason, report_contact_name, 
    report_time, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_demand_order_objection
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_demand_order_objection
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDemandOrderObjection" >
    insert into t_demand_order_objection (id, demand_order_id, customer_name, 
      objection_type, objection_reason, report_contact_name, 
      report_time, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{demandOrderId,jdbcType=BIGINT}, #{customerName,jdbcType=VARCHAR}, 
      #{objectionType,jdbcType=INTEGER}, #{objectionReason,jdbcType=VARCHAR}, #{reportContactName,jdbcType=VARCHAR}, 
      #{reportTime,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDemandOrderObjection" >
    insert into t_demand_order_objection
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="demandOrderId != null" >
        demand_order_id,
      </if>
      <if test="customerName != null" >
        customer_name,
      </if>
      <if test="objectionType != null" >
        objection_type,
      </if>
      <if test="objectionReason != null" >
        objection_reason,
      </if>
      <if test="reportContactName != null" >
        report_contact_name,
      </if>
      <if test="reportTime != null" >
        report_time,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="demandOrderId != null" >
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="customerName != null" >
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="objectionType != null" >
        #{objectionType,jdbcType=INTEGER},
      </if>
      <if test="objectionReason != null" >
        #{objectionReason,jdbcType=VARCHAR},
      </if>
      <if test="reportContactName != null" >
        #{reportContactName,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null" >
        #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDemandOrderObjection" >
    update t_demand_order_objection
    <set >
      <if test="demandOrderId != null" >
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="customerName != null" >
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="objectionType != null" >
        objection_type = #{objectionType,jdbcType=INTEGER},
      </if>
      <if test="objectionReason != null" >
        objection_reason = #{objectionReason,jdbcType=VARCHAR},
      </if>
      <if test="reportContactName != null" >
        report_contact_name = #{reportContactName,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null" >
        report_time = #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDemandOrderObjection" >
    update t_demand_order_objection
    set demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      customer_name = #{customerName,jdbcType=VARCHAR},
      objection_type = #{objectionType,jdbcType=INTEGER},
      objection_reason = #{objectionReason,jdbcType=VARCHAR},
      report_contact_name = #{reportContactName,jdbcType=VARCHAR},
      report_time = #{reportTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>