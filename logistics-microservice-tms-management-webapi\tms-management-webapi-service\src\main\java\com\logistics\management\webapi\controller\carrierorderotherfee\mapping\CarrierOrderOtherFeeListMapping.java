package com.logistics.management.webapi.controller.carrierorderotherfee.mapping;

import com.logistics.management.webapi.base.enums.CarrierOrderOtherFeeStatusEnum;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.enums.ProjectLabelEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.carrierorderotherfee.response.SearchOtherFeeListResponseModel;
import com.logistics.management.webapi.controller.carrierorderotherfee.response.SearchOtherFeeListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;

import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Collectors;

public class CarrierOrderOtherFeeListMapping extends MapperMapping<SearchOtherFeeListResponseModel, SearchOtherFeeListResponseDto> {
    @Override
    public void configure() {
        SearchOtherFeeListResponseModel source = getSource();
        SearchOtherFeeListResponseDto destination = getDestination();

        //费用状态转换
        destination.setAuditStatusDesc(CarrierOrderOtherFeeStatusEnum.getEnum(source.getAuditStatus()).getValue());

        //车主转换
        if(CompanyTypeEnum.COMPANY.getKey().equals(source.getCompanyCarrierType())){
            destination.setCompanyCarrierName(Optional.ofNullable(source.getCompanyCarrierName()).orElse(""));
            destination.setExportCompanyCarrierName(Optional.ofNullable(source.getCompanyCarrierName()).orElse(""));
        }else if(CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())){
            destination.setCompanyCarrierName(Optional.ofNullable(source.getCarrierContactName()).orElse("")+" "+FrequentMethodUtils.encryptionData(source.getCarrierContactMobile(), EncodeTypeEnum.MOBILE_PHONE));
            //导出使用
            destination.setExportCompanyCarrierName(Optional.ofNullable(source.getCarrierContactName()).orElse("")+" "+Optional.ofNullable(source.getCarrierContactMobile()).orElse(""));
        }
        //司机转换
        destination.setDriver(Optional.ofNullable(source.getDriverName()).orElse("")+" "+FrequentMethodUtils.encryptionData(source.getDriverMobile(), EncodeTypeEnum.MOBILE_PHONE));
        //导出使用
        destination.setDriverForExport(Optional.ofNullable(source.getDriverName()).orElse("")+" "+Optional.ofNullable(source.getDriverMobile()).orElse(""));

        //路线转换
        destination.setRoute(Optional.ofNullable(source.getLoadCityName()).orElse("")+"-"+Optional.ofNullable(source.getUnloadCityName()).orElse(""));

        //项目标签
        if (StringUtils.isNotBlank(source.getProjectLabel())){
            String projectLabel = Arrays.stream(source.getProjectLabel().split(","))
                    .map(s -> ProjectLabelEnum.getEnum(Integer.valueOf(s))
                            .getValue()).collect(Collectors.joining("、"));
            destination.setProjectLabel(projectLabel);
        }
    }
}
