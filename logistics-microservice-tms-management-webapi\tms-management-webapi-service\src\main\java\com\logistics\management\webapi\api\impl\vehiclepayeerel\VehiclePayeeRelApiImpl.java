package com.logistics.management.webapi.api.impl.vehiclepayeerel;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.vehiclepayeerel.VehiclePayeeRelApi;
import com.logistics.management.webapi.api.feign.vehiclepayeerel.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.vehiclepayeerel.mapping.VehiclePayeeRelListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelVehiclePayeeRel;
import com.logistics.management.webapi.base.constant.ImportExcelHeaders;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.vehiclepayeerel.VehiclePayeeRelServiceApi;
import com.logistics.tms.api.feign.vehiclepayeerel.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.*;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/7/10 18:50
 */
@Slf4j
@RestController
public class VehiclePayeeRelApiImpl implements VehiclePayeeRelApi {

    @Autowired
    private VehiclePayeeRelServiceApi vehiclePayeeRelServiceApi;
    @Autowired
    private CommonBiz commonBiz;

    /**
     * 车辆收款账户关联关系列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchVehiclePayeeRelListResponseDto>> searchVehiclePayeeRelList(@RequestBody SearchVehiclePayeeRelListRequestDto requestDto) {
        Result<PageInfo<SearchVehiclePayeeRelListResponseModel>> result = vehiclePayeeRelServiceApi.searchVehiclePayeeRelList(MapperUtils.mapperNoDefault(requestDto, SearchVehiclePayeeRelListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchVehiclePayeeRelListResponseDto> list = MapperUtils.mapper(pageInfo.getList(),SearchVehiclePayeeRelListResponseDto.class,new VehiclePayeeRelListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 车辆收款账户关联关系详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<VehiclePayeeRelDetailResponseDto> getVehiclePayeeRelDetail(@RequestBody @Valid VehiclePayeeRelIdRequestDto requestDto) {
        Result<VehiclePayeeRelDetailResponseModel> result = vehiclePayeeRelServiceApi.getVehiclePayeeRelDetail(MapperUtils.mapper(requestDto, VehiclePayeeRelIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),VehiclePayeeRelDetailResponseDto.class));
    }

    /**
     * 新增/修改车辆收款账户关联关系
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addOrModifyVehiclePayeeRel(@RequestBody @Valid AddOrModifyVehiclePayeeRelRequestDto requestDto) {
        Result result = vehiclePayeeRelServiceApi.addOrModifyVehiclePayeeRel(MapperUtils.mapper(requestDto, AddOrModifyVehiclePayeeRelRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 删除车辆收款账户关联关系
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delVehiclePayeeRel(@RequestBody @Valid VehiclePayeeRelIdsRequestDto requestDto) {
        Result result = vehiclePayeeRelServiceApi.delVehiclePayeeRel(MapperUtils.mapper(requestDto, VehiclePayeeRelIdsRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 导出车辆收款账户关联关系列表
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportVehiclePayeeRel(SearchVehiclePayeeRelListRequestDto requestDto, HttpServletResponse response) {
        Result<List<SearchVehiclePayeeRelListResponseModel>> result = vehiclePayeeRelServiceApi.exportVehiclePayeeRel(MapperUtils.mapperNoDefault(requestDto,SearchVehiclePayeeRelListRequestModel.class));
        result.throwException();
        List<SearchVehiclePayeeRelListResponseDto> list = MapperUtils.mapper(result.getData(),SearchVehiclePayeeRelListResponseDto.class);
        String fileName = "车辆收款账户关联关系" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportExcelVehiclePayeeRel.getExportVehiclePayeeRel();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * 导入车辆收款账户关联关系
     * @param file
     * @param request
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<ImportVehiclePayeeRelResponseDto> importVehiclePayeeRel(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        if (null == file) {
            throw new BizException(ManagementWebApiExceptionEnum.VEHICLE_PAYEE_REL_EMPTY);
        }
        InputStream in;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            log.error("导入车辆收款账户关联关系失败，",e);
            throw new BizException(ManagementWebApiExceptionEnum.VEHICLE_PAYEE_REL_EMPTY);
        }
        List<List<Object>> listByExcel = new ImportExcelUtil().getDataListByExcel(in, file.getOriginalFilename(), ImportExcelHeaders.getImportVehiclePayeeRelType());
        ImportVehiclePayeeRelRequestDto requestDto = new ImportVehiclePayeeRelRequestDto();
        requestDto.setRelList(commonBiz.transferExcelToObject(ImportVehiclePayeeRelListRequestDto.class,listByExcel));
        ImportVehiclePayeeRelRequestModel model = checkRepeatVehiclePayeeRel(requestDto);
        if (ListUtils.isNotEmpty(model.getRelList())) {
            Result<ImportVehiclePayeeRelResponseModel> result = vehiclePayeeRelServiceApi.importVehiclePayeeRel(model);
            result.throwException();
            return Result.success(MapperUtils.mapper(result.getData(),ImportVehiclePayeeRelResponseDto.class));
        }
        ImportVehiclePayeeRelResponseDto responseDto = new ImportVehiclePayeeRelResponseDto();
        responseDto.setNumberSuccessful(CommonConstant.ZERO);
        if (model.getNumberFailures() != null && model.getNumberFailures() > 0){
            responseDto.setNumberFailures(model.getNumberFailures().toString());
        }else{
            responseDto.setNumberFailures(CommonConstant.ZERO);
        }
        return Result.success(responseDto);
    }
    //入参校验及转换
    public ImportVehiclePayeeRelRequestModel checkRepeatVehiclePayeeRel(ImportVehiclePayeeRelRequestDto model){
        ImportVehiclePayeeRelRequestModel requestModel = new ImportVehiclePayeeRelRequestModel();
        if (model == null || ListUtils.isEmpty(model.getRelList())){
            return requestModel;
        }
        Integer numberFailures = 0;
        ImportVehiclePayeeRelListRequestModel relRequestModel;
        List<ImportVehiclePayeeRelListRequestModel> relList = new ArrayList<>();
        for (ImportVehiclePayeeRelListRequestDto rel:model.getRelList()) {
            if (!FrequentMethodUtils.validateVehicleFormat(rel.getVehicleNo())){
                numberFailures++;
                continue;
            }
            if (StringUtils.isBlank(rel.getName()) || !FrequentMethodUtils.match("[\\u4E00-\\u9FA5a-zA-Z]{2,10}",rel.getName())){
                numberFailures++;
                continue;
            }
            if (StringUtils.isBlank(rel.getIdentityNo()) || !IDCardValidator.isValidatedAllIdcard(rel.getIdentityNo())){
                numberFailures++;
                continue;
            }
            if (StringUtils.isBlank(rel.getBankCardNo()) || !FrequentMethodUtils.validateDriversLicenseNo(rel.getBankCardNo())){
                numberFailures++;
                continue;
            }
            if (StringUtils.isBlank(rel.getBankName()) || !FrequentMethodUtils.match("[\\u4E00-\\u9FA5]{2,50}",rel.getBankName())){
                numberFailures++;
                continue;
            }
            if (StringUtils.isNotBlank(rel.getRemark()) && rel.getRemark().length() > CommonConstant.INTEGER_THREE_HUNDRED){
                numberFailures++;
                continue;
            }
            relRequestModel = new ImportVehiclePayeeRelListRequestModel();
            relRequestModel.setVehicleNo(rel.getVehicleNo());
            relRequestModel.setName(rel.getName());
            relRequestModel.setIdentityNo(rel.getIdentityNo());
            relRequestModel.setBankCardNo(rel.getBankCardNo());
            relRequestModel.setBankName(rel.getBankName());
            relRequestModel.setRemark(StringUtils.isNotBlank(rel.getRemark())?rel.getRemark():null);
            relList.add(relRequestModel);
        }
        requestModel.setNumberFailures(numberFailures);
        requestModel.setRelList(relList);
        return requestModel;
    }
}
