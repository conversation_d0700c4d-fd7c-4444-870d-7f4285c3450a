package com.logistics.management.webapi.controller.settlestatement.packaging.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CompanyTypeEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.enums.SettleStatementStatusEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.settlestatement.packaging.response.CarrierSettleStatementItemModel;
import com.logistics.management.webapi.client.settlestatement.packaging.response.CarrierSettleStatementListResponseModel;
import com.logistics.management.webapi.controller.settlestatement.packaging.response.CarrierSettleStatementListResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2022/11/17 13:27
 */
public class CarrierSettleStatementListMapping extends MapperMapping<CarrierSettleStatementListResponseModel, CarrierSettleStatementListResponseDto> {
    @Override
    public void configure() {
        CarrierSettleStatementListResponseModel source = getSource();
        CarrierSettleStatementListResponseDto destination = getDestination();

        //对账单状态
        destination.setSettleStatementStatusLabel(SettleStatementStatusEnum.getEnum(source.getSettleStatementStatus()).getValue());

        //对账单明细
        List<CarrierSettleStatementItemModel> itemList = source.getItemList();

        //关联运单数量
        destination.setCarrierOrderAmount(ConverterUtils.toString(itemList.size()));

        //车主
        CarrierSettleStatementItemModel itemModel = itemList.get(CommonConstant.INTEGER_ZERO);
        if (CompanyTypeEnum.PERSONAL.getKey().equals(itemModel.getCompanyCarrierType())) {
            //个人车主:姓名+手机号
            destination.setCompanyCarrierName(itemModel.getCarrierContactName() + FrequentMethodUtils.encryptionData(itemModel.getCarrierContactMobile(), EncodeTypeEnum.MOBILE_PHONE));
            destination.setExportCompanyCarrierName(itemModel.getCarrierContactName() + itemModel.getCarrierContactMobile());
        } else {
            destination.setCompanyCarrierName(itemModel.getCompanyCarrierName());
            destination.setExportCompanyCarrierName(itemModel.getCompanyCarrierName());
        }
        destination.setCompanyCarrierId(ConverterUtils.toString(itemModel.getCompanyCarrierId()));

        BigDecimal carrierFreight = BigDecimal.ZERO;//车主运费
        BigDecimal otherFees = BigDecimal.ZERO;//临时费用
        //计算数量
        for (CarrierSettleStatementItemModel statementItemModel : itemList) {
            carrierFreight = carrierFreight.add(statementItemModel.getEntrustFreight());
            otherFees = otherFees.add(statementItemModel.getOtherFees());
        }

        //车主运费
        carrierFreight = carrierFreight.setScale(2, BigDecimal.ROUND_HALF_UP);
        destination.setCarrierFreight(ConverterUtils.toString(carrierFreight));

        //费额合计
        BigDecimal carrierFreightTotal = carrierFreight.add(carrierFreight.multiply(source.getFreightTaxPoint()).divide(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_NET_ZERO, 2, BigDecimal.ROUND_HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_UP);
        destination.setCarrierFreightTotal(ConverterUtils.toString(carrierFreightTotal));

        //临时运费
        otherFees = otherFees.setScale(2, BigDecimal.ROUND_HALF_UP);
        if (otherFees.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
            destination.setOtherFee(CommonConstant.PLUS_SYMBOL + ConverterUtils.toString(otherFees));
        }else {
            destination.setOtherFee(ConverterUtils.toString(otherFees));
        }

        //申请运费总额
        BigDecimal otherFeesTotal;
        if (otherFees.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO && source.getOtherFeeTaxPoint().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
            otherFeesTotal = otherFees.add(otherFees.multiply(source.getOtherFeeTaxPoint()).divide(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_NET_ZERO, 2, BigDecimal.ROUND_HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_UP);
        }else{
            otherFeesTotal = otherFees;
        }
        BigDecimal applyFeeTotal = carrierFreightTotal.add(otherFeesTotal).setScale(2, BigDecimal.ROUND_HALF_UP);
        destination.setApplyFeeTotal(ConverterUtils.toString(applyFeeTotal));

        //差异调整
        if (source.getAdjustFee().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO){
            destination.setAdjustFee(CommonConstant.PLUS_SYMBOL + ConverterUtils.toString(source.getAdjustFee()));
        }else{
            destination.setAdjustFee(ConverterUtils.toString(source.getAdjustFee()));
        }

        //对账费用
        if (SettleStatementStatusEnum.CANCEL.getKey().equals(source.getSettleStatementStatus())) {
            //已撤销不展示
            destination.setReconciliationFee(CommonConstant.BLANK_TEXT);
        } else {
            destination.setReconciliationFee(ConverterUtils.toString(applyFeeTotal.add(source.getAdjustFee()).setScale(2, BigDecimal.ROUND_HALF_UP)));
        }

        //生成日期
        destination.setCreatedTime(DateUtils.dateToString(source.getCreatedTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));

        //关联开票
        if (CommonConstant.INTEGER_ONE.equals(source.getIfInvoice())) {
            destination.setIfInvoiceLabel(CommonConstant.YES);
        }else{
            destination.setIfInvoiceLabel(CommonConstant.NO);
        }
    }
}
