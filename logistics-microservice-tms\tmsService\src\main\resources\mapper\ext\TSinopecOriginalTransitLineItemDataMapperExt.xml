<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSinopecOriginalTransitLineItemDataMapper">

    <insert id="batchInsertSelective">
        <foreach collection="list" item="item" separator=";">
            insert into t_sinopec_original_transit_line_item_data
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.sinopecOriginalDataId != null">
                    sinopec_original_data_id,
                </if>
                <if test="item.logisticsOrderNumber != null">
                    logistics_order_number,
                </if>
                <if test="item.businessOrderNumber != null">
                    business_order_number,
                </if>
                <if test="item.businessOrderValidEnd != null">
                    business_order_valid_end,
                </if>
                <if test="item.certificateTime != null">
                    certificate_time,
                </if>
                <if test="item.existState != null">
                    exist_state,
                </if>
                <if test="item.materialAttribute != null">
                    material_attribute,
                </if>
                <if test="item.makeDrug != null">
                    make_drug,
                </if>
                <if test="item.supportAuthOnline != null">
                    support_auth_online,
                </if>
                <if test="item.packingName != null">
                    packing_name,
                </if>
                <if test="item.packingSpecification != null">
                    packing_specification,
                </if>
                <if test="item.productionEnterpriseType != null">
                    production_enterprise_type,
                </if>
                <if test="item.productionEnterpriseName != null">
                    production_enterprise_name,
                </if>
                <if test="item.productionEnterpriseCode != null">
                    production_enterprise_code,
                </if>
                <if test="item.batch != null">
                    batch,
                </if>
                <if test="item.materialMainCategory != null">
                    material_main_category,
                </if>
                <if test="item.materialMainCategoryCode != null">
                    material_main_category_code,
                </if>
                <if test="item.materialCategory != null">
                    material_category,
                </if>
                <if test="item.materialCategoryCode != null">
                    material_category_code,
                </if>
                <if test="item.materialName != null">
                    material_name,
                </if>
                <if test="item.materialCode != null">
                    material_code,
                </if>
                <if test="item.associatedCount != null">
                    associated_count,
                </if>
                <if test="item.unitId != null">
                    unit_id,
                </if>
                <if test="item.unitName != null">
                    unit_name,
                </if>
                <if test="item.clientCode != null">
                    client_code,
                </if>
                <if test="item.clientName != null">
                    client_name,
                </if>
                <if test="item.transportPrice != null">
                    transport_price,
                </if>
                <if test="item.estimateTransportFee != null">
                    estimate_transport_fee,
                </if>
                <if test="item.freighterType != null">
                    freighter_type,
                </if>
                <if test="item.freighterCode != null">
                    freighter_code,
                </if>
                <if test="item.freighterName != null">
                    freighter_name,
                </if>
                <if test="item.originPortLocationType != null">
                    origin_port_location_type,
                </if>
                <if test="item.originPortLocationCode != null">
                    origin_port_location_code,
                </if>
                <if test="item.originPortLocationName != null">
                    origin_port_location_name,
                </if>
                <if test="item.originPortDetailAddress != null">
                    origin_port_detail_address,
                </if>
                <if test="item.originPortProvinceCode != null">
                    origin_port_province_code,
                </if>
                <if test="item.originPortProvinceName != null">
                    origin_port_province_name,
                </if>
                <if test="item.originPortCityCode != null">
                    origin_port_city_code,
                </if>
                <if test="item.originPortCityName != null">
                    origin_port_city_name,
                </if>
                <if test="item.originPortCountyCode != null">
                    origin_port_county_code,
                </if>
                <if test="item.originPortCountyName != null">
                    origin_port_county_name,
                </if>
                <if test="item.originPortTownCode != null">
                    origin_port_town_code,
                </if>
                <if test="item.originPortTownName != null">
                    origin_port_town_name,
                </if>
                <if test="item.originPortLatitude != null">
                    origin_port_latitude,
                </if>
                <if test="item.originPortLongitude != null">
                    origin_port_longitude,
                </if>
                <if test="item.destinationPortLocationType != null">
                    destination_port_location_type,
                </if>
                <if test="item.destinationPortLocationCode != null">
                    destination_port_location_code,
                </if>
                <if test="item.destinationPortLocationName != null">
                    destination_port_location_name,
                </if>
                <if test="item.destinationPortDetailAddress != null">
                    destination_port_detail_address,
                </if>
                <if test="item.destinationPortProvinceCode != null">
                    destination_port_province_code,
                </if>
                <if test="item.destinationPortProvinceName != null">
                    destination_port_province_name,
                </if>
                <if test="item.destinationPortCityCode != null">
                    destination_port_city_code,
                </if>
                <if test="item.destinationPortCityName != null">
                    destination_port_city_name,
                </if>
                <if test="item.destinationPortCountyCode != null">
                    destination_port_county_code,
                </if>
                <if test="item.destinationPortCountyName != null">
                    destination_port_county_name,
                </if>
                <if test="item.destinationPortTownCode != null">
                    destination_port_town_code,
                </if>
                <if test="item.destinationPortTownName != null">
                    destination_port_town_name,
                </if>
                <if test="item.destinationPortLatitude != null">
                    destination_port_latitude,
                </if>
                <if test="item.destinationPortLongitude != null">
                    destination_port_longitude,
                </if>
                <if test="item.receiverCode != null">
                    receiver_code,
                </if>
                <if test="item.receiverName != null">
                    receiver_name,
                </if>
                <if test="item.receiverType != null">
                    receiver_type,
                </if>
                <if test="item.receiverPhone != null">
                    receiver_phone,
                </if>
                <if test="item.otherfee != null">
                    otherFee,
                </if>
                <if test="item.remark1 != null">
                    remark1,
                </if>
                <if test="item.remark2 != null">
                    remark2,
                </if>
                <if test="item.remark3 != null">
                    remark3,
                </if>
                <if test="item.remark4 != null">
                    remark4,
                </if>
                <if test="item.remark5 != null">
                    remark5,
                </if>
                <if test="item.remark6 != null">
                    remark6,
                </if>
                <if test="item.remark7 != null">
                    remark7,
                </if>
                <if test="item.remark8 != null">
                    remark8,
                </if>
                <if test="item.remark9 != null">
                    remark9,
                </if>
                <if test="item.remark10 != null">
                    remark10,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.sinopecOriginalDataId != null">
                    #{item.sinopecOriginalDataId,jdbcType=BIGINT},
                </if>
                <if test="item.logisticsOrderNumber != null">
                    #{item.logisticsOrderNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.businessOrderNumber != null">
                    #{item.businessOrderNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.businessOrderValidEnd != null">
                    #{item.businessOrderValidEnd,jdbcType=TIMESTAMP},
                </if>
                <if test="item.certificateTime != null">
                    #{item.certificateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.existState != null">
                    #{item.existState,jdbcType=INTEGER},
                </if>
                <if test="item.materialAttribute != null">
                    #{item.materialAttribute,jdbcType=INTEGER},
                </if>
                <if test="item.makeDrug != null">
                    #{item.makeDrug,jdbcType=INTEGER},
                </if>
                <if test="item.supportAuthOnline != null">
                    #{item.supportAuthOnline,jdbcType=INTEGER},
                </if>
                <if test="item.packingName != null">
                    #{item.packingName,jdbcType=VARCHAR},
                </if>
                <if test="item.packingSpecification != null">
                    #{item.packingSpecification,jdbcType=VARCHAR},
                </if>
                <if test="item.productionEnterpriseType != null">
                    #{item.productionEnterpriseType,jdbcType=INTEGER},
                </if>
                <if test="item.productionEnterpriseName != null">
                    #{item.productionEnterpriseName,jdbcType=VARCHAR},
                </if>
                <if test="item.productionEnterpriseCode != null">
                    #{item.productionEnterpriseCode,jdbcType=VARCHAR},
                </if>
                <if test="item.batch != null">
                    #{item.batch,jdbcType=VARCHAR},
                </if>
                <if test="item.materialMainCategory != null">
                    #{item.materialMainCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.materialMainCategoryCode != null">
                    #{item.materialMainCategoryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialCategory != null">
                    #{item.materialCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.materialCategoryCode != null">
                    #{item.materialCategoryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialName != null">
                    #{item.materialName,jdbcType=VARCHAR},
                </if>
                <if test="item.materialCode != null">
                    #{item.materialCode,jdbcType=VARCHAR},
                </if>
                <if test="item.associatedCount != null">
                    #{item.associatedCount,jdbcType=DECIMAL},
                </if>
                <if test="item.unitId != null">
                    #{item.unitId,jdbcType=INTEGER},
                </if>
                <if test="item.unitName != null">
                    #{item.unitName,jdbcType=VARCHAR},
                </if>
                <if test="item.clientCode != null">
                    #{item.clientCode,jdbcType=VARCHAR},
                </if>
                <if test="item.clientName != null">
                    #{item.clientName,jdbcType=VARCHAR},
                </if>
                <if test="item.transportPrice != null">
                    #{item.transportPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.estimateTransportFee != null">
                    #{item.estimateTransportFee,jdbcType=DECIMAL},
                </if>
                <if test="item.freighterType != null">
                    #{item.freighterType,jdbcType=INTEGER},
                </if>
                <if test="item.freighterCode != null">
                    #{item.freighterCode,jdbcType=VARCHAR},
                </if>
                <if test="item.freighterName != null">
                    #{item.freighterName,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortLocationType != null">
                    #{item.originPortLocationType,jdbcType=INTEGER},
                </if>
                <if test="item.originPortLocationCode != null">
                    #{item.originPortLocationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortLocationName != null">
                    #{item.originPortLocationName,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortDetailAddress != null">
                    #{item.originPortDetailAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortProvinceCode != null">
                    #{item.originPortProvinceCode,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortProvinceName != null">
                    #{item.originPortProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortCityCode != null">
                    #{item.originPortCityCode,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortCityName != null">
                    #{item.originPortCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortCountyCode != null">
                    #{item.originPortCountyCode,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortCountyName != null">
                    #{item.originPortCountyName,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortTownCode != null">
                    #{item.originPortTownCode,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortTownName != null">
                    #{item.originPortTownName,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortLatitude != null">
                    #{item.originPortLatitude,jdbcType=VARCHAR},
                </if>
                <if test="item.originPortLongitude != null">
                    #{item.originPortLongitude,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortLocationType != null">
                    #{item.destinationPortLocationType,jdbcType=INTEGER},
                </if>
                <if test="item.destinationPortLocationCode != null">
                    #{item.destinationPortLocationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortLocationName != null">
                    #{item.destinationPortLocationName,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortDetailAddress != null">
                    #{item.destinationPortDetailAddress,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortProvinceCode != null">
                    #{item.destinationPortProvinceCode,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortProvinceName != null">
                    #{item.destinationPortProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortCityCode != null">
                    #{item.destinationPortCityCode,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortCityName != null">
                    #{item.destinationPortCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortCountyCode != null">
                    #{item.destinationPortCountyCode,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortCountyName != null">
                    #{item.destinationPortCountyName,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortTownCode != null">
                    #{item.destinationPortTownCode,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortTownName != null">
                    #{item.destinationPortTownName,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortLatitude != null">
                    #{item.destinationPortLatitude,jdbcType=VARCHAR},
                </if>
                <if test="item.destinationPortLongitude != null">
                    #{item.destinationPortLongitude,jdbcType=VARCHAR},
                </if>
                <if test="item.receiverCode != null">
                    #{item.receiverCode,jdbcType=VARCHAR},
                </if>
                <if test="item.receiverName != null">
                    #{item.receiverName,jdbcType=VARCHAR},
                </if>
                <if test="item.receiverType != null">
                    #{item.receiverType,jdbcType=INTEGER},
                </if>
                <if test="item.receiverPhone != null">
                    #{item.receiverPhone,jdbcType=VARCHAR},
                </if>
                <if test="item.otherfee != null">
                    #{item.otherfee,jdbcType=DECIMAL},
                </if>
                <if test="item.remark1 != null">
                    #{item.remark1,jdbcType=VARCHAR},
                </if>
                <if test="item.remark2 != null">
                    #{item.remark2,jdbcType=VARCHAR},
                </if>
                <if test="item.remark3 != null">
                    #{item.remark3,jdbcType=VARCHAR},
                </if>
                <if test="item.remark4 != null">
                    #{item.remark4,jdbcType=VARCHAR},
                </if>
                <if test="item.remark5 != null">
                    #{item.remark5,jdbcType=VARCHAR},
                </if>
                <if test="item.remark6 != null">
                    #{item.remark6,jdbcType=VARCHAR},
                </if>
                <if test="item.remark7 != null">
                    #{item.remark7,jdbcType=VARCHAR},
                </if>
                <if test="item.remark8 != null">
                    #{item.remark8,jdbcType=VARCHAR},
                </if>
                <if test="item.remark9 != null">
                    #{item.remark9,jdbcType=VARCHAR},
                </if>
                <if test="item.remark10 != null">
                    #{item.remark10,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>
</mapper>