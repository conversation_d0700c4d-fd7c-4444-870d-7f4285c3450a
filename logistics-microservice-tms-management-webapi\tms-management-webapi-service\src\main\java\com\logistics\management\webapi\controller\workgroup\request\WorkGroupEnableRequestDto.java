package com.logistics.management.webapi.controller.workgroup.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2023/12/21 14:55
 */
@Data
public class WorkGroupEnableRequestDto {

    @ApiModelProperty(value = "智能推送配置表id", required = true)
    @NotBlank(message = "请选择配置")
    private String workGroupId;

    @ApiModelProperty(value = "禁用/启用。1：启用，0：禁用",required = true)
    @NotBlank(message = "操作类型不能为空")
    private String enabled;
}
