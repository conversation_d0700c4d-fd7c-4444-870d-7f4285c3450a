package com.logistics.appapi.client.workordercenter.response;

import com.yelo.tools.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class WorkOrderDetailAppletResponseModel {

    @ApiModelProperty("工单id")
    private Long workOrderId;

    @ApiModelProperty("状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销")
    private Integer status;

    @ApiModelProperty("运单id")
    private Long carrierOrderId;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("提货省")
    private String loadProvinceName;

    @ApiModelProperty("提货市")
    private String loadCityName;

    @ApiModelProperty("提货区")
    private String loadAreaName;

    @ApiModelProperty("提货详细地址")
    private String loadDetailAddress;

    @ApiModelProperty("提货仓库")
    private String loadWarehouse;

    @ApiModelProperty("提货地址联系人")
    private String loadPerson;

    @ApiModelProperty("提货地址联系人联系方式")
    private String loadMobile;

    @ApiModelProperty("提货经度")
    private String loadLongitude;

    @ApiModelProperty("提货纬度")
    private String loadLatitude;

    @ApiModelProperty("提报时间")
    private Date reportTime;

    @ApiModelProperty("提报人")
    private String reportUserName;

    @ApiModelProperty(value = "定位抬头")
    private String addressHead;

    @ApiModelProperty(value = "定位详细地址")
    private String addressDetail;

    @ApiModelProperty(value = "异常类型（一级）：10 联系不上客户，20 不想还盘，30 不可抗力，40 重复下单")
    private Integer anomalyTypeOne;

    @ApiModelProperty(value = "异常类型（二级）：" +
            "101 电话空号 102 无人接听 103联系方式错误;" +
            "201 客户占用 202 客户不协助装车 203 贸易商流向错误 204 装车费用确认;" +
            "301 暴风暴雨 302 当地修路;" +
            "401 重复下单;")
    private Integer anomalyTypeTwo;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "联系人联系方式")
    private String contactTelephone;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "司机是否到达现场：0 否，1 是")
    private Integer isArriveScene;

    @ApiModelProperty(value = "核验联系方式: 1 无误，2 有误")
    private Integer checkContact;

    @ApiModelProperty("提报来源：1 后台，2 前台，3 小程序")
    private Integer reportSource;

    @ApiModelProperty("发货人手机号")
    private String consignorMobile;

    @ApiModelProperty("发货人姓名")
    private String consignorName;

    public String getConsignorMobile() {
        return StringUtils.isNotBlank(consignorMobile) ? consignorMobile : contactTelephone;
    }

    public String getConsignorName() {
        return StringUtils.isNotBlank(consignorName) ? consignorName : contactName;
    }
}
