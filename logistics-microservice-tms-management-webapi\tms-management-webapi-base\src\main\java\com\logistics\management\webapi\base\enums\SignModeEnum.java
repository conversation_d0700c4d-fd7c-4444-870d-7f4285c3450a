package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/7/1 9:44
 */
public enum SignModeEnum {

    DEFAULT(0, "",""),
    MANUALLY_SIGN(1,"人工签收","人工签收"),
    AUTO_SIGN(2,"自动签收","%1$s个自然日默认签收"),
    ;

    private Integer key;
    private String value;
    private String desc;

    SignModeEnum(Integer key, String value,String desc) {
        this.key = key;
        this.value = value;
        this.desc = desc;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static SignModeEnum getEnum(Integer key) {
        for (SignModeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
