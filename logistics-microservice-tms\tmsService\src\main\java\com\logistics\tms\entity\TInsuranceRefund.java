package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TInsuranceRefund extends BaseEntity {
    /**
    * 结算状态：0 待结算，1 已结算
    */
    @ApiModelProperty("结算状态：0 待结算，1 已结算")
    private Integer status;

    /**
    * 车辆id
    */
    @ApiModelProperty("车辆id")
    private Long vehicleId;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 月份（yyyy-MM）
    */
    @ApiModelProperty("月份（yyyy-MM）")
    private String settlementMonth;

    /**
    * 保单id
    */
    @ApiModelProperty("保单id")
    private Long insuranceId;

    /**
    * 险种 1 商业险 2 交强险 3 个人意外险 4货物险 5 危货承运人险
    */
    @ApiModelProperty("险种 1 商业险 2 交强险 3 个人意外险 4货物险 5 危货承运人险")
    private Integer insuranceType;

    /**
    * 退保金额
    */
    @ApiModelProperty("退保金额")
    private BigDecimal refundCost;

    /**
    * 退保证明
    */
    @ApiModelProperty("退保证明")
    private String refundPath;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}