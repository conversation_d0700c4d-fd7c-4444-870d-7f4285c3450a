package com.logistics.tms.controller.freightconfig.response.ladder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CarrierFreightConfigPriceDesignResponseModel {

    @ApiModelProperty(value = "价格模式; 1:固定单价; 2:阶梯单价; 3:阶梯总价; 4:多级阶梯单价; 5: 多级阶梯总价")
    private Integer priceMode;

    @ApiModelProperty(value = "阶梯")
    private List<CarrierFreightConfigLadderResponseModel> ladderConfigList;
}
