package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2023/8/23 14:40
 */
public enum ProjectLabelEnum {

    DEFAULT(0, ""),
    PETROCHEMICAL(1, "石化板块"),
    CARBON_BLACK(2, "轮胎板块"),
    COATING(3, "涂料板块"),
    OTHER(4, "其他"),
    ;

    private Integer key;
    private String value;

    ProjectLabelEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static ProjectLabelEnum getEnum(Integer key) {
        for (ProjectLabelEnum t : values()) {
            if (t.getKey() .equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }

}
