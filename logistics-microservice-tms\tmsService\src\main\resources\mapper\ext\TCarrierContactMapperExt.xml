<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TCarrierContactMapper">
    <sql id="Base_Column_List_Decrypt">
        id
        , company_carrier_id, contact_name,
    AES_DECRYPT(UNHEX(contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as contact_phone,
    AES_DECRYPT(UNHEX(identity_number),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as identity_number, identity_face_file,
    identity_face_file_is_amend, identity_national_file, identity_national_file_is_amend,
    identity_validity, identity_is_forever, province_id, province_name, city_id, city_name,
    area_id, area_name, certification_department_detail, remark, enabled, created_by,
    created_time, last_modified_by, last_modified_time, valid
    </sql>
    <select id="selectByPrimaryKeyDecrypt" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_carrier_contact
        where valid = 1
        AND id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByContactPhone" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_carrier_contact
        where valid = 1
        AND contact_phone =
        HEX(AES_ENCRYPT(#{contactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'))
    </select>

    <select id="getByCompanyCarrierId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_carrier_contact
        where valid = 1
        AND company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
    </select>

    <select id="getCarrierContactDetailById"
            resultType="com.logistics.tms.api.feign.carriercontact.dto.CarrierContactDetailResponseModel">
        SELECT
        tac.id                                                                                                                   AS carrierContactId,
        tca.type,
        tac.contact_name                                                                                                         AS contactName,
        AES_DECRYPT(UNHEX(tac.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')   as contactPhone,
        AES_DECRYPT(UNHEX(tac.identity_number), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as identityNumber,
        tc.company_name                                                                                                          as companyCarrierName,
        tac.company_carrier_id                                                                                                   as companyCarrierId
        FROM t_carrier_contact tac
        LEFT JOIN t_company_carrier tca ON tac.company_carrier_id = tca.id AND tca.valid = 1
        LEFT JOIN t_company tc ON tca.company_id = tc.id AND tc.valid = 1
        WHERE tac.valid = 1
        AND tac.id = #{carrierContactId,jdbcType=BIGINT}
    </select>
    <select id="selectCarrierContactList"
            resultType="com.logistics.tms.api.feign.carriercontact.dto.SearchCarrierContactResponseModel">
        SELECT
        tc.id as carrierContactId,
        tc.enabled as carrierContactStatus,
        tc.contact_name as contactName,
        AES_DECRYPT(UNHEX(tc.contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as contactPhone,
        tcc.id as companyCarrierId,
        tcc.company_id as companyId,
        tcc.type as type
        FROM
        t_carrier_contact tc
        LEFT JOIN t_company_carrier tcc on tcc.id=tc.company_carrier_id  and tcc.valid=1
        WHERE tc.valid = 1
        <if test="params.account !='' and params.account != null">
            and (instr(tc.contact_name,#{params.account,jdbcType=VARCHAR}) or instr(AES_DECRYPT(UNHEX(tc.contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),#{params.account,jdbcType=VARCHAR}))
        </if>
        <if test="param1.carrierContactStatus != null">
            and tc.enabled =#{params.carrierContactStatus}
        </if>
        <if test="params.type != null">
            and tcc.type =#{params.type,jdbcType=INTEGER}
        </if>
        <if test="params.companyCarrierName != null and params.companyCarrierName != ''">
            and (
            <if test="params.companyIds!=null and params.companyIds!=''">
                (tcc.type = 1 and tcc.company_id in (${params.companyIds}))
                or
            </if>
            (tcc.type = 2 and (instr(tc.contact_name,#{params.companyCarrierName,jdbcType = VARCHAR})
            or instr(AES_DECRYPT(UNHEX(tc.contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),#{params.companyCarrierName,jdbcType = VARCHAR})))
            )
        </if>
    </select>

    <update id="updateByPrimaryKeySelectiveTime">
        update t_carrier_contact
        <set>
            <if test="companyCarrierId != null">
                company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
            </if>
            <if test="contactName != null">
                contact_name = #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null">
                contact_phone =
                HEX(AES_ENCRYPT(#{contactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="identityNumber != null">
                identity_number =
                HEX(AES_ENCRYPT(#{identityNumber,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="identityFaceFile != null">
                identity_face_file = #{identityFaceFile,jdbcType=VARCHAR},
            </if>
            <if test="identityFaceFileIsAmend != null">
                identity_face_file_is_amend = #{identityFaceFileIsAmend,jdbcType=INTEGER},
            </if>
            <if test="identityNationalFile != null">
                identity_national_file = #{identityNationalFile,jdbcType=VARCHAR},
            </if>
            <if test="identityNationalFileIsAmend != null">
                identity_national_file_is_amend = #{identityNationalFileIsAmend,jdbcType=INTEGER},
            </if>
            identity_validity = #{identityValidity,jdbcType=TIMESTAMP},
            <if test="identityIsForever != null">
                identity_is_forever = #{identityIsForever,jdbcType=INTEGER},
            </if>
            <if test="provinceId != null">
                province_id = #{provinceId,jdbcType=BIGINT},
            </if>
            <if test="provinceName != null">
                province_name = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                city_id = #{cityId,jdbcType=BIGINT},
            </if>
            <if test="cityName != null">
                city_name = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="areaId != null">
                area_id = #{areaId,jdbcType=BIGINT},
            </if>
            <if test="areaName != null">
                area_name = #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="certificationDepartmentDetail != null">
                certification_department_detail = #{certificationDepartmentDetail,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null">
                enabled = #{enabled,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TCarrierContact" keyProperty="id"
            useGeneratedKeys="true">
        insert into t_carrier_contact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="companyCarrierId != null">
                company_carrier_id,
            </if>
            <if test="contactName != null">
                contact_name,
            </if>
            <if test="contactPhone != null">
                contact_phone,
            </if>
            <if test="identityNumber != null">
                identity_number,
            </if>
            <if test="identityFaceFile != null">
                identity_face_file,
            </if>
            <if test="identityFaceFileIsAmend != null">
                identity_face_file_is_amend,
            </if>
            <if test="identityNationalFile != null">
                identity_national_file,
            </if>
            <if test="identityNationalFileIsAmend != null">
                identity_national_file_is_amend,
            </if>
            <if test="identityValidity != null">
                identity_validity,
            </if>
            <if test="identityIsForever != null">
                identity_is_forever,
            </if>
            <if test="provinceId != null">
                province_id,
            </if>
            <if test="provinceName != null">
                province_name,
            </if>
            <if test="cityId != null">
                city_id,
            </if>
            <if test="cityName != null">
                city_name,
            </if>
            <if test="areaId != null">
                area_id,
            </if>
            <if test="areaName != null">
                area_name,
            </if>
            <if test="certificationDepartmentDetail != null">
                certification_department_detail,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="enabled != null">
                enabled,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time,
            </if>
            <if test="valid != null">
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="companyCarrierId != null">
                #{companyCarrierId,jdbcType=BIGINT},
            </if>
            <if test="contactName != null">
                #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null">
                HEX(AES_ENCRYPT(#{contactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="identityNumber != null">
                HEX(AES_ENCRYPT(#{identityNumber,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="identityFaceFile != null">
                #{identityFaceFile,jdbcType=VARCHAR},
            </if>
            <if test="identityFaceFileIsAmend != null">
                #{identityFaceFileIsAmend,jdbcType=INTEGER},
            </if>
            <if test="identityNationalFile != null">
                #{identityNationalFile,jdbcType=VARCHAR},
            </if>
            <if test="identityNationalFileIsAmend != null">
                #{identityNationalFileIsAmend,jdbcType=INTEGER},
            </if>
            <if test="identityValidity != null">
                #{identityValidity,jdbcType=TIMESTAMP},
            </if>
            <if test="identityIsForever != null">
                #{identityIsForever,jdbcType=INTEGER},
            </if>
            <if test="provinceId != null">
                #{provinceId,jdbcType=BIGINT},
            </if>
            <if test="provinceName != null">
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=BIGINT},
            </if>
            <if test="cityName != null">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="areaId != null">
                #{areaId,jdbcType=BIGINT},
            </if>
            <if test="areaName != null">
                #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="certificationDepartmentDetail != null">
                #{certificationDepartmentDetail,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null">
                #{enabled,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TCarrierContact">
        update t_carrier_contact
        <set>
            <if test="companyCarrierId != null">
                company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
            </if>
            <if test="contactName != null">
                contact_name = #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null">
                contact_phone =
                HEX(AES_ENCRYPT(#{contactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="identityNumber != null">
                identity_number =
                HEX(AES_ENCRYPT(#{identityNumber,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="identityFaceFile != null">
                identity_face_file = #{identityFaceFile,jdbcType=VARCHAR},
            </if>
            <if test="identityFaceFileIsAmend != null">
                identity_face_file_is_amend = #{identityFaceFileIsAmend,jdbcType=INTEGER},
            </if>
            <if test="identityNationalFile != null">
                identity_national_file = #{identityNationalFile,jdbcType=VARCHAR},
            </if>
            <if test="identityNationalFileIsAmend != null">
                identity_national_file_is_amend = #{identityNationalFileIsAmend,jdbcType=INTEGER},
            </if>
            <if test="identityValidity != null">
                identity_validity = #{identityValidity,jdbcType=TIMESTAMP},
            </if>
            <if test="identityIsForever != null">
                identity_is_forever = #{identityIsForever,jdbcType=INTEGER},
            </if>
            <if test="provinceId != null">
                province_id = #{provinceId,jdbcType=BIGINT},
            </if>
            <if test="provinceName != null">
                province_name = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                city_id = #{cityId,jdbcType=BIGINT},
            </if>
            <if test="cityName != null">
                city_name = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="areaId != null">
                area_id = #{areaId,jdbcType=BIGINT},
            </if>
            <if test="areaName != null">
                area_name = #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="certificationDepartmentDetail != null">
                certification_department_detail = #{certificationDepartmentDetail,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null">
                enabled = #{enabled,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>