package com.logistics.management.webapi.client.reservation.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReservationOrderSearchListForManagementWebReqModel extends AbstractPageForm<ReservationOrderSearchListForManagementWebReqModel> {

    /**
     * 预约单号
     */
    @ApiModelProperty("预约单号")
    private String reservationCode;

    /**
     * 状态 1待签到 2已签到 3已失效
     */
    @ApiModelProperty("状态 1待签到 2已签到 3已失效")
    private Integer state;


    /**
     * 车主
     */
    @ApiModelProperty("车主")
    private String carrierName;


    /**
     * 运单号
     */
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
     * 预约方式 0.小程序 1.微信 2.车主
     */
    @ApiModelProperty(value = "预约方式 0.小程序 1.微信 2.车主")
    private Integer reservationSource;


    /**
     * 预约方式 0.小程序 1.微信 2.车主
     */
    @ApiModelProperty(value = "预约角色0.司机 1.访客 2.车主")
    private Integer reservationRole;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 预约日期 start
     */
    @ApiModelProperty(value = "预约日期 start")
    private String reservationTimeStart;
    /**
     * 预约日期 end
     */
    @ApiModelProperty(value = "预约日期 end")
    private String reservationTimeEnd;

    /**
     * 签到时间 start
     */
    @ApiModelProperty(value = "签到时间 start")
    private String signInTimeStart;
    /**
     * 签到时间 end
     */
    @ApiModelProperty(value = "签到时间 end")
    private String signInTimeEnd;



}
