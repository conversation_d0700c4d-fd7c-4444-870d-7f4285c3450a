package com.logistics.management.webapi.client.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @author: wei.wang
 * @date: 2022/1/5
 */
@Data
public class WaitDispatchStatisticsModel {
    @ApiModelProperty("发布时间")
    private LocalDate publishTime;
    @ApiModelProperty("委托单状态：500待发布 1000待调度 2000部分调度")
    private Integer status;
    @ApiModelProperty("货物数量")
    private BigDecimal goodsAmount;
}
