package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CarrierFreightConfigLadderModeEnum {

    DEFAULT(0, ""),
    ROUTE_CONFIG(1, "路线配置"),
    SCHEME_CONFIG(2, "方案配置"),
    ;

    private final Integer key;

    private final String value;

    public static CarrierFreightConfigLadderModeEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

    public static CarrierFreightConfigLadderModeEnum getEnumBySchemeType(Integer schemeType) {
        if (CarrierFreightConfigSchemeTypeEnum.ROUTE_CONFIG.getKey().equals(schemeType)) {
            return ROUTE_CONFIG;
        }
        return SCHEME_CONFIG;
    }
}
