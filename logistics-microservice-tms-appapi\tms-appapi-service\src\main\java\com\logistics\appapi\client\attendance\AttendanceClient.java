package com.logistics.appapi.client.attendance;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.attendance.hystrix.AttendanceClientHystrix;
import com.logistics.appapi.client.attendance.request.*;
import com.logistics.appapi.client.attendance.response.AttendanceClockDetailResponseModel;
import com.logistics.appapi.client.attendance.response.AttendanceHistoryListResponseModel;
import com.logistics.appapi.client.attendance.response.QueryPathByLonAndLatResponseModel;
import com.logistics.appapi.client.attendance.response.UpdateAttendanceHistoryDetailResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/7 14:49
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = AttendanceClientHystrix.class)
public interface AttendanceClient {

    @ApiOperation(value = "根据登陆人获取打卡状态", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/attendanceClockDetail")
    Result<AttendanceClockDetailResponseModel> attendanceClockDetail();

    @ApiOperation(value = "根据经纬度获取地址", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/queryPathByLonAndLat")
    Result<QueryPathByLonAndLatResponseModel> queryPathByLonAndLat(@RequestBody QueryPathByLonAndLatRequestModel requestModel);

    @ApiOperation(value = "考勤打卡", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/attendanceClock")
    Result<Boolean> attendanceClock(@RequestBody AttendanceClockRequestModel requestModel);

    @ApiOperation(value = "考勤历史列表", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/attendanceHistoryList")
    Result<AttendanceHistoryListResponseModel> attendanceHistoryList(@RequestBody AttendanceHistoryListRequestModel requestModel);

    @ApiOperation(value = "修改考勤打卡", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/updateAttendanceHistory")
    Result<Boolean> updateAttendanceHistory(@RequestBody UpdateAttendanceHistoryRequestModel requestDto);

    @ApiOperation(value = "修改考勤打卡撤销", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/cancelUpdateAttendanceHistory")
    Result<Boolean> cancelUpdateAttendanceHistory(@RequestBody CancelUpdateAttendanceHistoryRequestModel requestModel);

    @ApiOperation(value = "修改考勤打卡详情查询", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/attendance/updateAttendanceHistoryDetail")
    Result<UpdateAttendanceHistoryDetailResponseModel> updateAttendanceHistoryDetail(@RequestBody UpdateAttendanceHistoryDetailRequestModel requestModel);

}
