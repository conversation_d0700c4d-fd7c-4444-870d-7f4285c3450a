package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2024/6/20 13:27
 */
@Data
public class CarrierOrderGoodsToYeloLifeModel {
    @ApiModelProperty(value = "产品类型code")
    private String skuCode;

    @ApiModelProperty(value = "数量")
    private BigDecimal count;


    @ApiModelProperty(value = "产品类型code")
    private String recycleBagCode;


    @ApiModelProperty(value = "单位 1.KG 2.件 ")
    private Integer unit;

}
