package com.logistics.tms.base.enums;

/**
 * 新生商品货物来源枚举
 */
public enum RenewableSourceTypeEnum {

    YELOLIFE_SYNC(1,"新生同步"),
    DRIVER_PLACE_ORDER(2,"司机下单");

    private Integer key;
    private String value;

    RenewableSourceTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
