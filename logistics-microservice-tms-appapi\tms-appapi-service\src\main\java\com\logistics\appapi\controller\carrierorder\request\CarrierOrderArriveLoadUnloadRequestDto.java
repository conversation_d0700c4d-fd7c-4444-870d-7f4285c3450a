package com.logistics.appapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @author: wjf
 * @date: 2018/10/17 16:22
 */
@Data
public class CarrierOrderArriveLoadUnloadRequestDto {

    @ApiModelProperty(value = "运单Id",required = true)
    @NotBlank(message = "运单ID不能为空")
    private String carrierOrderId;

    @ApiModelProperty(value = "经度",required = true)
    @NotBlank(message = "请开启定位")
    private String longitude;
    @ApiModelProperty(value = "纬度",required = true)
    @NotBlank(message = "请开启定位")
    private String latitude;

    @ApiModelProperty("上传图片临时路径")
    private List<String> tmpUrl;
}
