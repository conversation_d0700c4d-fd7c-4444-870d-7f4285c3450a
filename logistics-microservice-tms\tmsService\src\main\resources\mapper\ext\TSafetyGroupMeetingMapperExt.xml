<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSafetyGroupMeetingMapper" >

  <select id="getSafetyGroupMeetingByYearAndSeason" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_safety_group_meeting
    where valid = 1
    and meeting_year = #{meetingYear,jdbcType=VARCHAR}
    and meeting_season = #{meetingSeason,jdbcType=INTEGER}
  </select>


  <select id="getSafeGroupMeetingData" parameterType="String" resultType="com.logistics.tms.api.feign.safegroupmeeting.model.SafeGroupMeetingResponseModel">
    select
    tsgm.id safetyGroupMeetingId,
    count(tsgmr.id) joinInPersonCount,
    tsgm.meeting_title meetingTitle,
    tsgm.meeting_season meetingSeason
    from t_safety_group_meeting tsgm
    left join t_safety_group_meeting_relation tsgmr on tsgm.id = tsgmr.safety_group_meeting_id and tsgmr.valid = 1
    where
    tsgm.valid = 1
    <if test="meetingYear!=null and meetingYear!=''">
      and tsgm.meeting_year = #{meetingYear,jdbcType=VARCHAR}
    </if>
    group by tsgm.id
    order by tsgm.meeting_season asc
  </select>



  <resultMap id="getSafeGroupMeetingDetailMap" type="com.logistics.tms.api.feign.safegroupmeeting.model.SafeGroupMeetingDetailResponseModel">
    <id column="id" property="safetyGroupMeetingId" jdbcType="BIGINT"/>
    <result column="meeting_year" property="meetingYear" jdbcType="VARCHAR"/>
    <result column="meeting_season" property="meetingSeason" jdbcType="INTEGER"/>

    <result column="meeting_title" property="meetingTitle" jdbcType="VARCHAR"/>
    <result column="meeting_time" property="meetingTime" jdbcType="TIMESTAMP"/>
    <result column="meeting_place" property="meetingPlace" jdbcType="VARCHAR"/>
    <result column="anchor" property="anchor" jdbcType="VARCHAR"/>
    <result column="record_person" property="recordPerson" jdbcType="VARCHAR"/>
    <result column="content" property="content" jdbcType="LONGVARCHAR"/>

    <collection property="safeGroupMeetingRelationList" ofType="com.logistics.tms.api.feign.safegroupmeeting.model.SafeGroupMeetingRelationModel">
      <id column="safeGroupMeetingRelationId" property="safeGroupMeetingRelationId" jdbcType="BIGINT"/>
      <result column="position" property="position" jdbcType="VARCHAR"/>
      <result column="participate_person" property="participatePerson" jdbcType="VARCHAR"/>
    </collection>

    <collection property="safetyGroupMeetingAttachmentList" ofType="com.logistics.tms.api.feign.safegroupmeeting.model.SafetyGroupMeetingAttachmentModel">
      <id column="safetyGroupMeetingAttachmentId" property="safetyGroupMeetingAttachmentId" jdbcType="BIGINT"/>
      <result column="type" property="type" jdbcType="INTEGER"/>
      <result column="meeting_image_path" property="meetingImagePath" jdbcType="VARCHAR"/>
    </collection>
  </resultMap>


  <select id="getSafeGroupMeetingDetail" parameterType="Long" resultMap="getSafeGroupMeetingDetailMap">
    select
    tsgm.id,
    tsgm.meeting_year,
    tsgm.meeting_season,
    tsgm.meeting_title,
    tsgm.meeting_time,
    tsgm.meeting_place,
    tsgm.anchor,
    tsgm.record_person,
    tsgm.content,
    tsgmr.id safeGroupMeetingRelationId,
    tsgmr.position,
    tsgmr.participate_person,
    tsgma.id safetyGroupMeetingAttachmentId,
    tsgma.type,
    tsgma.meeting_image_path
    from t_safety_group_meeting tsgm
    left join t_safety_group_meeting_relation tsgmr on tsgm.id = tsgmr.safety_group_meeting_id and tsgmr.valid = 1
    left join t_safety_group_meeting_attachment tsgma on tsgm.id = tsgma.safety_group_meeting_id and tsgma.valid = 1
    where
    tsgm.valid = 1
    and tsgm.id = #{safetyGroupingMeetingId,jdbcType=BIGINT}
  </select>
</mapper>