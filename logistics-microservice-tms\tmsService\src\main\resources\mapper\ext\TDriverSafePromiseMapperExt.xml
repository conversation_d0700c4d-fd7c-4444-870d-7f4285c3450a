<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDriverSafePromiseMapper" >
  <select id="getCountByPeriod" resultType="java.lang.Integer">
    select count(*)
    from t_driver_safe_promise
    where VALID = 1
    and period = #{period,jdbcType = VARCHAR}
  </select>

  <select id="getCountById" resultType="java.lang.Integer">
    select count(*)
    from t_driver_safe_promise
    where VALID = 1
    and id = #{safePromiseId,jdbcType = BIGINT}
  </select>

  <select id="searchList" resultType="com.logistics.tms.controller.driversafepromise.response.SearchSafePromiseListResponseModel">
    select
      p.id as safePromiseId,
      p.title as title,
      p.agent as agent,
      p.period as period,
      p.created_time as uploadTime
    from t_driver_safe_promise p
    where p.valid = 1
    <if test="params.periodStart!=null and params.periodStart!=''">
        and p.period >= #{params.periodStart,jdbcType=VARCHAR}
    </if>
    <if test="params.periodEnd!=null and params.periodEnd!=''">
        and p.period &lt;= #{params.periodEnd,jdbcType=VARCHAR}
    </if>
  </select>

  <update id="deleteSafePromiseById">
    update t_driver_safe_promise tdsp
    left join t_driver_safe_promise_relation tdspr on tdspr.safe_promise_id =tdsp.id
    set tdsp.valid = 0,
        tdsp.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},
        tdsp.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP},
        tdspr.valid = 0,
        tdspr.last_modified_by = #{modifiedBy,jdbcType=VARCHAR},
        tdspr.last_modified_time = #{modifiedTime,jdbcType=TIMESTAMP}
    where tdsp.valid =1
    and tdsp.id = #{safePromiseId,jdbcType = BIGINT}
  </update>
  <select id="searchAppletList"
          resultType="com.logistics.tms.controller.driversafepromise.response.SearchSafePromiseAppletListResponseModel">
    select
    tdspr.id as relationId,
    tdspr.status,

    tdsp.period,
    tdsp.created_time as publishTime,
    tdsp.agent
    from t_driver_safe_promise_relation tdspr
    left join t_driver_safe_promise tdsp on tdsp.id = tdspr.safe_promise_id and tdsp.valid=1
    where tdspr.valid=1
    and staff_id = #{params.staffId,jdbcType=BIGINT}
    <if test="params.status!=null">
        and tdspr.status = #{params.status,jdbcType=INTEGER}
    </if>
    order by tdsp.created_time desc
  </select>

</mapper>