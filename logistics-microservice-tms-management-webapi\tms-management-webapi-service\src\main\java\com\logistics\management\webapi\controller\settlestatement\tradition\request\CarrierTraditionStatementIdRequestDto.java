package com.logistics.management.webapi.controller.settlestatement.tradition.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 结款记录查询 请求体
 */
@Data
public class CarrierTraditionStatementIdRequestDto {

    @ApiModelProperty(value = "对账单id", required = true)
    @NotBlank(message = "id不能为空")
    private String settleStatementId;
}
