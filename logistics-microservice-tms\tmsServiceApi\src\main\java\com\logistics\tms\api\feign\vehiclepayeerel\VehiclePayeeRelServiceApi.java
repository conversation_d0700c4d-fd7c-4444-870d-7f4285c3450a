package com.logistics.tms.api.feign.vehiclepayeerel;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.vehiclepayeerel.hystrix.VehiclePayeeRelServiceApiHystrix;
import com.logistics.tms.api.feign.vehiclepayeerel.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/7/11 14:05
 */
@Api(value = "API-VehiclePayeeRelServiceApi-车辆收款账户关联关系")
@FeignClient(name = "logistics-tms-services", fallback = VehiclePayeeRelServiceApiHystrix.class)
public interface VehiclePayeeRelServiceApi {
    
    @ApiOperation(value = "车辆收款账户关联关系列表")
    @PostMapping(value = "/service/vehiclePayeeRel/searchVehiclePayeeRelList")
    Result<PageInfo<SearchVehiclePayeeRelListResponseModel>> searchVehiclePayeeRelList(@RequestBody SearchVehiclePayeeRelListRequestModel requestModel);

    @ApiOperation(value = "车辆收款账户关联关系详情")
    @PostMapping(value = "/service/vehiclePayeeRel/getVehiclePayeeRelDetail")
    Result<VehiclePayeeRelDetailResponseModel> getVehiclePayeeRelDetail(@RequestBody VehiclePayeeRelIdRequestModel requestModel);

    @ApiOperation(value = "新增/修改车辆收款账户关联关系")
    @PostMapping(value = "/service/vehiclePayeeRel/addOrModifyVehiclePayeeRel")
    Result<Boolean> addOrModifyVehiclePayeeRel(@RequestBody AddOrModifyVehiclePayeeRelRequestModel requestModel);

    @ApiOperation(value = "删除车辆收款账户关联关系")
    @PostMapping(value = "/service/vehiclePayeeRel/delVehiclePayeeRel")
    Result<Boolean> delVehiclePayeeRel(@RequestBody VehiclePayeeRelIdsRequestModel requestModel);

    @ApiOperation(value = "导出车辆收款账户关联关系列表")
    @PostMapping(value = "/service/vehiclePayeeRel/exportVehiclePayeeRel")
    Result<List<SearchVehiclePayeeRelListResponseModel>> exportVehiclePayeeRel(@RequestBody SearchVehiclePayeeRelListRequestModel requestModel);

    @ApiOperation(value = "导入车辆收款账户关联关系")
    @PostMapping(value = "/service/vehiclePayeeRel/importVehiclePayeeRel")
    Result<ImportVehiclePayeeRelResponseModel> importVehiclePayeeRel(@RequestBody ImportVehiclePayeeRelRequestModel requestModel);
}
