package com.logistics.management.webapi.controller.invoicingmanagement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/25 11:19
 */
@Data
public class InvoicingArchiveRequestDto {

    /**
     * 发票管理id
     */
    @ApiModelProperty(value = "发票管理id",required = true)
    @NotBlank(message = "发票管理id不能为空")
    private String invoicingId;

    /**
     * 归档文件url
     */
    @ApiModelProperty(value = "归档文件url",required = true)
    @NotEmpty(message = "请上传归档文件")
    @Size(max = 6, message = "归档文件最多上传6张")
    private List<String> tmpUrl;
}
