<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDemandOrderMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDemandOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="entrust_status" jdbcType="INTEGER" property="entrustStatus" />
    <result column="status_update_time" jdbcType="TIMESTAMP" property="statusUpdateTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="if_cancel" jdbcType="INTEGER" property="ifCancel" />
    <result column="cancel_type" jdbcType="INTEGER" property="cancelType" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="demand_order_code" jdbcType="VARCHAR" property="demandOrderCode" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_user_name" jdbcType="VARCHAR" property="customerUserName" />
    <result column="customer_user_mobile" jdbcType="VARCHAR" property="customerUserMobile" />
    <result column="customer_order_source" jdbcType="INTEGER" property="customerOrderSource" />
    <result column="customer_order_code" jdbcType="VARCHAR" property="customerOrderCode" />
    <result column="publish_name" jdbcType="VARCHAR" property="publishName" />
    <result column="publish_mobile" jdbcType="VARCHAR" property="publishMobile" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="publish_org_code" jdbcType="VARCHAR" property="publishOrgCode" />
    <result column="publish_org_name" jdbcType="VARCHAR" property="publishOrgName" />
    <result column="ticket_time" jdbcType="TIMESTAMP" property="ticketTime" />
    <result column="settlement_tonnage" jdbcType="INTEGER" property="settlementTonnage" />
    <result column="carrier_settlement" jdbcType="INTEGER" property="carrierSettlement" />
    <result column="goods_amount" jdbcType="DECIMAL" property="goodsAmount" />
    <result column="arranged_amount" jdbcType="DECIMAL" property="arrangedAmount" />
    <result column="not_arranged_amount" jdbcType="DECIMAL" property="notArrangedAmount" />
    <result column="back_amount" jdbcType="DECIMAL" property="backAmount" />
    <result column="difference_amount" jdbcType="DECIMAL" property="differenceAmount" />
    <result column="abnormal_amount" jdbcType="DECIMAL" property="abnormalAmount" />
    <result column="expect_contract_price" jdbcType="DECIMAL" property="expectContractPrice" />
    <result column="expect_contract_price_type" jdbcType="INTEGER" property="expectContractPriceType" />
    <result column="contract_price_type" jdbcType="INTEGER" property="contractPriceType" />
    <result column="contract_price" jdbcType="DECIMAL" property="contractPrice" />
    <result column="goods_unit" jdbcType="INTEGER" property="goodsUnit" />
    <result column="company_entrust_id" jdbcType="BIGINT" property="companyEntrustId" />
    <result column="company_entrust_name" jdbcType="VARCHAR" property="companyEntrustName" />
    <result column="upstream_customer" jdbcType="VARCHAR" property="upstreamCustomer" />
    <result column="company_carrier_type" jdbcType="INTEGER" property="companyCarrierType" />
    <result column="company_carrier_id" jdbcType="BIGINT" property="companyCarrierId" />
    <result column="company_carrier_name" jdbcType="VARCHAR" property="companyCarrierName" />
    <result column="carrier_contact_id" jdbcType="BIGINT" property="carrierContactId" />
    <result column="carrier_contact_name" jdbcType="VARCHAR" property="carrierContactName" />
    <result column="carrier_contact_phone" jdbcType="VARCHAR" property="carrierContactPhone" />
    <result column="company_carrier_level" jdbcType="INTEGER" property="companyCarrierLevel" />
    <result column="dispatch_vehicle_count" jdbcType="INTEGER" property="dispatchVehicleCount" />
    <result column="entrust_type" jdbcType="INTEGER" property="entrustType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="carrier_price_type" jdbcType="INTEGER" property="carrierPriceType" />
    <result column="carrier_price" jdbcType="DECIMAL" property="carrierPrice" />
    <result column="if_urgent" jdbcType="INTEGER" property="ifUrgent" />
    <result column="dispatch_validity" jdbcType="INTEGER" property="dispatchValidity" />
    <result column="if_overdue" jdbcType="INTEGER" property="ifOverdue" />
    <result column="if_empty" jdbcType="INTEGER" property="ifEmpty" />
    <result column="if_rollback" jdbcType="INTEGER" property="ifRollback" />
    <result column="rollback_cause_type" jdbcType="INTEGER" property="rollbackCauseType" />
    <result column="rollback_cause_type_two" jdbcType="INTEGER" property="rollbackCauseTypeTwo" />
    <result column="rollback_remark" jdbcType="VARCHAR" property="rollbackRemark" />
    <result column="available_on_weekends" jdbcType="INTEGER" property="availableOnWeekends" />
    <result column="loading_unloading_part" jdbcType="INTEGER" property="loadingUnloadingPart" />
    <result column="loading_unloading_charge" jdbcType="DECIMAL" property="loadingUnloadingCharge" />
    <result column="recycle_task_type" jdbcType="INTEGER" property="recycleTaskType" />
    <result column="ground_push_task_code" jdbcType="VARCHAR" property="groundPushTaskCode" />
    <result column="project_label" jdbcType="VARCHAR" property="projectLabel" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="sinopec_order_no" jdbcType="VARCHAR" property="sinopecOrderNo" />
    <result column="manufacturer_name" jdbcType="VARCHAR" property="manufacturerName" />
    <result column="item_trans_group_name" jdbcType="VARCHAR" property="itemTransGroupName" />
    <result column="item_pack_spec_name" jdbcType="VARCHAR" property="itemPackSpecName" />
    <result column="dispatcher_name" jdbcType="VARCHAR" property="dispatcherName" />
    <result column="dispatcher_phone" jdbcType="VARCHAR" property="dispatcherPhone" />
    <result column="if_objection" jdbcType="INTEGER" property="ifObjection" />
    <result column="if_objection_sinopec" jdbcType="INTEGER" property="ifObjectionSinopec" />
    <result column="sinopec_customer_id" jdbcType="BIGINT" property="sinopecCustomerId" />
    <result column="sinopec_customer_name" jdbcType="VARCHAR" property="sinopecCustomerName" />
    <result column="sinopec_online_goods_flag" jdbcType="INTEGER" property="sinopecOnlineGoodsFlag" />
    <result column="lbs_code" jdbcType="VARCHAR" property="lbsCode" />
    <result column="fixed_demand" jdbcType="VARCHAR" property="fixedDemand" />
    <result column="auto_publish" jdbcType="INTEGER" property="autoPublish" />
    <result column="order_mode" jdbcType="INTEGER" property="orderMode" />
    <result column="if_ext_demand_order" jdbcType="INTEGER" property="ifExtDemandOrder" />
    <result column="vehicle_length_id" jdbcType="BIGINT" property="vehicleLengthId" />
    <result column="vehicle_length" jdbcType="DECIMAL" property="vehicleLength" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, entrust_status, status_update_time, status, if_cancel, cancel_type, cancel_time, 
    cancel_reason, source, demand_order_code, business_type, customer_name, customer_user_name, 
    customer_user_mobile, customer_order_source, customer_order_code, publish_name, publish_mobile, 
    publish_time, publish_org_code, publish_org_name, ticket_time, settlement_tonnage, 
    carrier_settlement, goods_amount, arranged_amount, not_arranged_amount, back_amount, 
    difference_amount, abnormal_amount, expect_contract_price, expect_contract_price_type, 
    contract_price_type, contract_price, goods_unit, company_entrust_id, company_entrust_name, 
    upstream_customer, company_carrier_type, company_carrier_id, company_carrier_name, 
    carrier_contact_id, carrier_contact_name, carrier_contact_phone, company_carrier_level, 
    dispatch_vehicle_count, entrust_type, remark, carrier_price_type, carrier_price, 
    if_urgent, dispatch_validity, if_overdue, if_empty, if_rollback, rollback_cause_type, 
    rollback_cause_type_two, rollback_remark, available_on_weekends, loading_unloading_part, 
    loading_unloading_charge, recycle_task_type, ground_push_task_code, project_label, 
    order_type, sinopec_order_no, manufacturer_name, item_trans_group_name, item_pack_spec_name, 
    dispatcher_name, dispatcher_phone, if_objection, if_objection_sinopec, sinopec_customer_id, 
    sinopec_customer_name, sinopec_online_goods_flag, lbs_code, fixed_demand, auto_publish,
    order_mode, vehicle_length_id, vehicle_length,
    created_by, created_time, last_modified_by, last_modified_time, valid, if_ext_demand_order
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_demand_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_demand_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDemandOrder">
    insert into t_demand_order (id, entrust_status, status_update_time, 
      status, if_cancel, cancel_type, 
      cancel_time, cancel_reason, source, 
      demand_order_code, business_type, customer_name, 
      customer_user_name, customer_user_mobile, customer_order_source, 
      customer_order_code, publish_name, publish_mobile, 
      publish_time, publish_org_code, publish_org_name, 
      ticket_time, settlement_tonnage, carrier_settlement, 
      goods_amount, arranged_amount, not_arranged_amount, 
      back_amount, difference_amount, abnormal_amount, 
      expect_contract_price, expect_contract_price_type, 
      contract_price_type, contract_price, goods_unit, 
      company_entrust_id, company_entrust_name, upstream_customer, 
      company_carrier_type, company_carrier_id, company_carrier_name, 
      carrier_contact_id, carrier_contact_name, carrier_contact_phone, 
      company_carrier_level, dispatch_vehicle_count, 
      entrust_type, remark, carrier_price_type, 
      carrier_price, if_urgent, dispatch_validity, 
      if_overdue, if_empty, if_rollback, 
      rollback_cause_type, rollback_cause_type_two, 
      rollback_remark, available_on_weekends, loading_unloading_part, 
      loading_unloading_charge, recycle_task_type, 
      ground_push_task_code, project_label, order_type, 
      sinopec_order_no, manufacturer_name, item_trans_group_name, 
      item_pack_spec_name, dispatcher_name, dispatcher_phone, 
      if_objection, if_objection_sinopec, sinopec_customer_id, 
      sinopec_customer_name, sinopec_online_goods_flag, 
      lbs_code, fixed_demand, auto_publish,
      order_mode, vehicle_length_id, vehicle_length,
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{entrustStatus,jdbcType=INTEGER}, #{statusUpdateTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=INTEGER}, #{ifCancel,jdbcType=INTEGER}, #{cancelType,jdbcType=INTEGER}, 
      #{cancelTime,jdbcType=TIMESTAMP}, #{cancelReason,jdbcType=VARCHAR}, #{source,jdbcType=INTEGER}, 
      #{demandOrderCode,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER}, #{customerName,jdbcType=VARCHAR}, 
      #{customerUserName,jdbcType=VARCHAR}, #{customerUserMobile,jdbcType=VARCHAR}, #{customerOrderSource,jdbcType=INTEGER}, 
      #{customerOrderCode,jdbcType=VARCHAR}, #{publishName,jdbcType=VARCHAR}, #{publishMobile,jdbcType=VARCHAR}, 
      #{publishTime,jdbcType=TIMESTAMP}, #{publishOrgCode,jdbcType=VARCHAR}, #{publishOrgName,jdbcType=VARCHAR}, 
      #{ticketTime,jdbcType=TIMESTAMP}, #{settlementTonnage,jdbcType=INTEGER}, #{carrierSettlement,jdbcType=INTEGER}, 
      #{goodsAmount,jdbcType=DECIMAL}, #{arrangedAmount,jdbcType=DECIMAL}, #{notArrangedAmount,jdbcType=DECIMAL}, 
      #{backAmount,jdbcType=DECIMAL}, #{differenceAmount,jdbcType=DECIMAL}, #{abnormalAmount,jdbcType=DECIMAL}, 
      #{expectContractPrice,jdbcType=DECIMAL}, #{expectContractPriceType,jdbcType=INTEGER}, 
      #{contractPriceType,jdbcType=INTEGER}, #{contractPrice,jdbcType=DECIMAL}, #{goodsUnit,jdbcType=INTEGER}, 
      #{companyEntrustId,jdbcType=BIGINT}, #{companyEntrustName,jdbcType=VARCHAR}, #{upstreamCustomer,jdbcType=VARCHAR}, 
      #{companyCarrierType,jdbcType=INTEGER}, #{companyCarrierId,jdbcType=BIGINT}, #{companyCarrierName,jdbcType=VARCHAR}, 
      #{carrierContactId,jdbcType=BIGINT}, #{carrierContactName,jdbcType=VARCHAR}, #{carrierContactPhone,jdbcType=VARCHAR}, 
      #{companyCarrierLevel,jdbcType=INTEGER}, #{dispatchVehicleCount,jdbcType=INTEGER}, 
      #{entrustType,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{carrierPriceType,jdbcType=INTEGER}, 
      #{carrierPrice,jdbcType=DECIMAL}, #{ifUrgent,jdbcType=INTEGER}, #{dispatchValidity,jdbcType=INTEGER}, 
      #{ifOverdue,jdbcType=INTEGER}, #{ifEmpty,jdbcType=INTEGER}, #{ifRollback,jdbcType=INTEGER}, 
      #{rollbackCauseType,jdbcType=INTEGER}, #{rollbackCauseTypeTwo,jdbcType=INTEGER}, 
      #{rollbackRemark,jdbcType=VARCHAR}, #{availableOnWeekends,jdbcType=INTEGER}, #{loadingUnloadingPart,jdbcType=INTEGER}, 
      #{loadingUnloadingCharge,jdbcType=DECIMAL}, #{recycleTaskType,jdbcType=INTEGER}, 
      #{groundPushTaskCode,jdbcType=VARCHAR}, #{projectLabel,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, 
      #{sinopecOrderNo,jdbcType=VARCHAR}, #{manufacturerName,jdbcType=VARCHAR}, #{itemTransGroupName,jdbcType=VARCHAR}, 
      #{itemPackSpecName,jdbcType=VARCHAR}, #{dispatcherName,jdbcType=VARCHAR}, #{dispatcherPhone,jdbcType=VARCHAR}, 
      #{ifObjection,jdbcType=INTEGER}, #{ifObjectionSinopec,jdbcType=INTEGER}, #{sinopecCustomerId,jdbcType=BIGINT}, 
      #{sinopecCustomerName,jdbcType=VARCHAR}, #{sinopecOnlineGoodsFlag,jdbcType=INTEGER}, 
      #{lbsCode,jdbcType=VARCHAR}, #{fixedDemand,jdbcType=VARCHAR}, #{autoPublish,jdbcType=INTEGER},
      #{orderMode,jdbcType=INTEGER},#{vehicleLengthId,jdbcType=BIGINT},#{vehicleLength,jdbcType=DECIMAL},
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR},
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDemandOrder">
    insert into t_demand_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="entrustStatus != null">
        entrust_status,
      </if>
      <if test="statusUpdateTime != null">
        status_update_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ifCancel != null">
        if_cancel,
      </if>
      <if test="cancelType != null">
        cancel_type,
      </if>
      <if test="cancelTime != null">
        cancel_time,
      </if>
      <if test="cancelReason != null">
        cancel_reason,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="demandOrderCode != null">
        demand_order_code,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerUserName != null">
        customer_user_name,
      </if>
      <if test="customerUserMobile != null">
        customer_user_mobile,
      </if>
      <if test="customerOrderSource != null">
        customer_order_source,
      </if>
      <if test="customerOrderCode != null">
        customer_order_code,
      </if>
      <if test="publishName != null">
        publish_name,
      </if>
      <if test="publishMobile != null">
        publish_mobile,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="publishOrgCode != null">
        publish_org_code,
      </if>
      <if test="publishOrgName != null">
        publish_org_name,
      </if>
      <if test="ticketTime != null">
        ticket_time,
      </if>
      <if test="settlementTonnage != null">
        settlement_tonnage,
      </if>
      <if test="carrierSettlement != null">
        carrier_settlement,
      </if>
      <if test="goodsAmount != null">
        goods_amount,
      </if>
      <if test="arrangedAmount != null">
        arranged_amount,
      </if>
      <if test="notArrangedAmount != null">
        not_arranged_amount,
      </if>
      <if test="backAmount != null">
        back_amount,
      </if>
      <if test="differenceAmount != null">
        difference_amount,
      </if>
      <if test="abnormalAmount != null">
        abnormal_amount,
      </if>
      <if test="expectContractPrice != null">
        expect_contract_price,
      </if>
      <if test="expectContractPriceType != null">
        expect_contract_price_type,
      </if>
      <if test="contractPriceType != null">
        contract_price_type,
      </if>
      <if test="contractPrice != null">
        contract_price,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="companyEntrustId != null">
        company_entrust_id,
      </if>
      <if test="companyEntrustName != null">
        company_entrust_name,
      </if>
      <if test="upstreamCustomer != null">
        upstream_customer,
      </if>
      <if test="companyCarrierType != null">
        company_carrier_type,
      </if>
      <if test="companyCarrierId != null">
        company_carrier_id,
      </if>
      <if test="companyCarrierName != null">
        company_carrier_name,
      </if>
      <if test="carrierContactId != null">
        carrier_contact_id,
      </if>
      <if test="carrierContactName != null">
        carrier_contact_name,
      </if>
      <if test="carrierContactPhone != null">
        carrier_contact_phone,
      </if>
      <if test="companyCarrierLevel != null">
        company_carrier_level,
      </if>
      <if test="dispatchVehicleCount != null">
        dispatch_vehicle_count,
      </if>
      <if test="entrustType != null">
        entrust_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="carrierPriceType != null">
        carrier_price_type,
      </if>
      <if test="carrierPrice != null">
        carrier_price,
      </if>
      <if test="ifUrgent != null">
        if_urgent,
      </if>
      <if test="dispatchValidity != null">
        dispatch_validity,
      </if>
      <if test="ifOverdue != null">
        if_overdue,
      </if>
      <if test="ifEmpty != null">
        if_empty,
      </if>
      <if test="ifRollback != null">
        if_rollback,
      </if>
      <if test="rollbackCauseType != null">
        rollback_cause_type,
      </if>
      <if test="rollbackCauseTypeTwo != null">
        rollback_cause_type_two,
      </if>
      <if test="rollbackRemark != null">
        rollback_remark,
      </if>
      <if test="availableOnWeekends != null">
        available_on_weekends,
      </if>
      <if test="loadingUnloadingPart != null">
        loading_unloading_part,
      </if>
      <if test="loadingUnloadingCharge != null">
        loading_unloading_charge,
      </if>
      <if test="recycleTaskType != null">
        recycle_task_type,
      </if>
      <if test="groundPushTaskCode != null">
        ground_push_task_code,
      </if>
      <if test="projectLabel != null">
        project_label,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="sinopecOrderNo != null">
        sinopec_order_no,
      </if>
      <if test="manufacturerName != null">
        manufacturer_name,
      </if>
      <if test="itemTransGroupName != null">
        item_trans_group_name,
      </if>
      <if test="itemPackSpecName != null">
        item_pack_spec_name,
      </if>
      <if test="dispatcherName != null">
        dispatcher_name,
      </if>
      <if test="dispatcherPhone != null">
        dispatcher_phone,
      </if>
      <if test="ifObjection != null">
        if_objection,
      </if>
      <if test="ifObjectionSinopec != null">
        if_objection_sinopec,
      </if>
      <if test="sinopecCustomerId != null">
        sinopec_customer_id,
      </if>
      <if test="sinopecCustomerName != null">
        sinopec_customer_name,
      </if>
      <if test="sinopecOnlineGoodsFlag != null">
        sinopec_online_goods_flag,
      </if>
      <if test="lbsCode != null">
        lbs_code,
      </if>
      <if test="fixedDemand != null">
        fixed_demand,
      </if>
      <if test="autoPublish != null">
        auto_publish,
      </if>
      <if test="orderMode != null">
        order_mode,
      </if>
      <if test="vehicleLengthId != null">
        vehicle_length_id,
      </if>
      <if test="vehicleLength != null">
        vehicle_length,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="entrustStatus != null">
        #{entrustStatus,jdbcType=INTEGER},
      </if>
      <if test="statusUpdateTime != null">
        #{statusUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="ifCancel != null">
        #{ifCancel,jdbcType=INTEGER},
      </if>
      <if test="cancelType != null">
        #{cancelType,jdbcType=INTEGER},
      </if>
      <if test="cancelTime != null">
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelReason != null">
        #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="demandOrderCode != null">
        #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserName != null">
        #{customerUserName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserMobile != null">
        #{customerUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderSource != null">
        #{customerOrderSource,jdbcType=INTEGER},
      </if>
      <if test="customerOrderCode != null">
        #{customerOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="publishName != null">
        #{publishName,jdbcType=VARCHAR},
      </if>
      <if test="publishMobile != null">
        #{publishMobile,jdbcType=VARCHAR},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publishOrgCode != null">
        #{publishOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="publishOrgName != null">
        #{publishOrgName,jdbcType=VARCHAR},
      </if>
      <if test="ticketTime != null">
        #{ticketTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementTonnage != null">
        #{settlementTonnage,jdbcType=INTEGER},
      </if>
      <if test="carrierSettlement != null">
        #{carrierSettlement,jdbcType=INTEGER},
      </if>
      <if test="goodsAmount != null">
        #{goodsAmount,jdbcType=DECIMAL},
      </if>
      <if test="arrangedAmount != null">
        #{arrangedAmount,jdbcType=DECIMAL},
      </if>
      <if test="notArrangedAmount != null">
        #{notArrangedAmount,jdbcType=DECIMAL},
      </if>
      <if test="backAmount != null">
        #{backAmount,jdbcType=DECIMAL},
      </if>
      <if test="differenceAmount != null">
        #{differenceAmount,jdbcType=DECIMAL},
      </if>
      <if test="abnormalAmount != null">
        #{abnormalAmount,jdbcType=DECIMAL},
      </if>
      <if test="expectContractPrice != null">
        #{expectContractPrice,jdbcType=DECIMAL},
      </if>
      <if test="expectContractPriceType != null">
        #{expectContractPriceType,jdbcType=INTEGER},
      </if>
      <if test="contractPriceType != null">
        #{contractPriceType,jdbcType=INTEGER},
      </if>
      <if test="contractPrice != null">
        #{contractPrice,jdbcType=DECIMAL},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=INTEGER},
      </if>
      <if test="companyEntrustId != null">
        #{companyEntrustId,jdbcType=BIGINT},
      </if>
      <if test="companyEntrustName != null">
        #{companyEntrustName,jdbcType=VARCHAR},
      </if>
      <if test="upstreamCustomer != null">
        #{upstreamCustomer,jdbcType=VARCHAR},
      </if>
      <if test="companyCarrierType != null">
        #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null">
        #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null">
        #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null">
        #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null">
        #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null">
        #{carrierContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="companyCarrierLevel != null">
        #{companyCarrierLevel,jdbcType=INTEGER},
      </if>
      <if test="dispatchVehicleCount != null">
        #{dispatchVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="entrustType != null">
        #{entrustType,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="carrierPriceType != null">
        #{carrierPriceType,jdbcType=INTEGER},
      </if>
      <if test="carrierPrice != null">
        #{carrierPrice,jdbcType=DECIMAL},
      </if>
      <if test="ifUrgent != null">
        #{ifUrgent,jdbcType=INTEGER},
      </if>
      <if test="dispatchValidity != null">
        #{dispatchValidity,jdbcType=INTEGER},
      </if>
      <if test="ifOverdue != null">
        #{ifOverdue,jdbcType=INTEGER},
      </if>
      <if test="ifEmpty != null">
        #{ifEmpty,jdbcType=INTEGER},
      </if>
      <if test="ifRollback != null">
        #{ifRollback,jdbcType=INTEGER},
      </if>
      <if test="rollbackCauseType != null">
        #{rollbackCauseType,jdbcType=INTEGER},
      </if>
      <if test="rollbackCauseTypeTwo != null">
        #{rollbackCauseTypeTwo,jdbcType=INTEGER},
      </if>
      <if test="rollbackRemark != null">
        #{rollbackRemark,jdbcType=VARCHAR},
      </if>
      <if test="availableOnWeekends != null">
        #{availableOnWeekends,jdbcType=INTEGER},
      </if>
      <if test="loadingUnloadingPart != null">
        #{loadingUnloadingPart,jdbcType=INTEGER},
      </if>
      <if test="loadingUnloadingCharge != null">
        #{loadingUnloadingCharge,jdbcType=DECIMAL},
      </if>
      <if test="recycleTaskType != null">
        #{recycleTaskType,jdbcType=INTEGER},
      </if>
      <if test="groundPushTaskCode != null">
        #{groundPushTaskCode,jdbcType=VARCHAR},
      </if>
      <if test="projectLabel != null">
        #{projectLabel,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="sinopecOrderNo != null">
        #{sinopecOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null">
        #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="itemTransGroupName != null">
        #{itemTransGroupName,jdbcType=VARCHAR},
      </if>
      <if test="itemPackSpecName != null">
        #{itemPackSpecName,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherName != null">
        #{dispatcherName,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherPhone != null">
        #{dispatcherPhone,jdbcType=VARCHAR},
      </if>
      <if test="ifObjection != null">
        #{ifObjection,jdbcType=INTEGER},
      </if>
      <if test="ifObjectionSinopec != null">
        #{ifObjectionSinopec,jdbcType=INTEGER},
      </if>
      <if test="sinopecCustomerId != null">
        #{sinopecCustomerId,jdbcType=BIGINT},
      </if>
      <if test="sinopecCustomerName != null">
        #{sinopecCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="sinopecOnlineGoodsFlag != null">
        #{sinopecOnlineGoodsFlag,jdbcType=INTEGER},
      </if>
      <if test="lbsCode != null">
        #{lbsCode,jdbcType=VARCHAR},
      </if>
      <if test="fixedDemand != null">
        #{fixedDemand,jdbcType=VARCHAR},
      </if>
      <if test="autoPublish != null">
        #{autoPublish,jdbcType=INTEGER},
      </if>
      <if test="orderMode != null">
        #{orderMode,jdbcType=INTEGER},
      </if>
      <if test="vehicleLengthId != null">
        #{vehicleLengthId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null">
        #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDemandOrder">
    update t_demand_order
    <set>
      <if test="entrustStatus != null">
        entrust_status = #{entrustStatus,jdbcType=INTEGER},
      </if>
      <if test="statusUpdateTime != null">
        status_update_time = #{statusUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="ifCancel != null">
        if_cancel = #{ifCancel,jdbcType=INTEGER},
      </if>
      <if test="cancelType != null">
        cancel_type = #{cancelType,jdbcType=INTEGER},
      </if>
      <if test="cancelTime != null">
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelReason != null">
        cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="demandOrderCode != null">
        demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserName != null">
        customer_user_name = #{customerUserName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserMobile != null">
        customer_user_mobile = #{customerUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderSource != null">
        customer_order_source = #{customerOrderSource,jdbcType=INTEGER},
      </if>
      <if test="customerOrderCode != null">
        customer_order_code = #{customerOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="publishName != null">
        publish_name = #{publishName,jdbcType=VARCHAR},
      </if>
      <if test="publishMobile != null">
        publish_mobile = #{publishMobile,jdbcType=VARCHAR},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publishOrgCode != null">
        publish_org_code = #{publishOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="publishOrgName != null">
        publish_org_name = #{publishOrgName,jdbcType=VARCHAR},
      </if>
      <if test="ticketTime != null">
        ticket_time = #{ticketTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementTonnage != null">
        settlement_tonnage = #{settlementTonnage,jdbcType=INTEGER},
      </if>
      <if test="carrierSettlement != null">
        carrier_settlement = #{carrierSettlement,jdbcType=INTEGER},
      </if>
      <if test="goodsAmount != null">
        goods_amount = #{goodsAmount,jdbcType=DECIMAL},
      </if>
      <if test="arrangedAmount != null">
        arranged_amount = #{arrangedAmount,jdbcType=DECIMAL},
      </if>
      <if test="notArrangedAmount != null">
        not_arranged_amount = #{notArrangedAmount,jdbcType=DECIMAL},
      </if>
      <if test="backAmount != null">
        back_amount = #{backAmount,jdbcType=DECIMAL},
      </if>
      <if test="differenceAmount != null">
        difference_amount = #{differenceAmount,jdbcType=DECIMAL},
      </if>
      <if test="abnormalAmount != null">
        abnormal_amount = #{abnormalAmount,jdbcType=DECIMAL},
      </if>
      <if test="expectContractPrice != null">
        expect_contract_price = #{expectContractPrice,jdbcType=DECIMAL},
      </if>
      <if test="expectContractPriceType != null">
        expect_contract_price_type = #{expectContractPriceType,jdbcType=INTEGER},
      </if>
      <if test="contractPriceType != null">
        contract_price_type = #{contractPriceType,jdbcType=INTEGER},
      </if>
      <if test="contractPrice != null">
        contract_price = #{contractPrice,jdbcType=DECIMAL},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=INTEGER},
      </if>
      <if test="companyEntrustId != null">
        company_entrust_id = #{companyEntrustId,jdbcType=BIGINT},
      </if>
      <if test="companyEntrustName != null">
        company_entrust_name = #{companyEntrustName,jdbcType=VARCHAR},
      </if>
      <if test="upstreamCustomer != null">
        upstream_customer = #{upstreamCustomer,jdbcType=VARCHAR},
      </if>
      <if test="companyCarrierType != null">
        company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      </if>
      <if test="companyCarrierId != null">
        company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      </if>
      <if test="companyCarrierName != null">
        company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactId != null">
        carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      </if>
      <if test="carrierContactName != null">
        carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      </if>
      <if test="carrierContactPhone != null">
        carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="companyCarrierLevel != null">
        company_carrier_level = #{companyCarrierLevel,jdbcType=INTEGER},
      </if>
      <if test="dispatchVehicleCount != null">
        dispatch_vehicle_count = #{dispatchVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="entrustType != null">
        entrust_type = #{entrustType,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="carrierPriceType != null">
        carrier_price_type = #{carrierPriceType,jdbcType=INTEGER},
      </if>
      <if test="carrierPrice != null">
        carrier_price = #{carrierPrice,jdbcType=DECIMAL},
      </if>
      <if test="ifUrgent != null">
        if_urgent = #{ifUrgent,jdbcType=INTEGER},
      </if>
      <if test="dispatchValidity != null">
        dispatch_validity = #{dispatchValidity,jdbcType=INTEGER},
      </if>
      <if test="ifOverdue != null">
        if_overdue = #{ifOverdue,jdbcType=INTEGER},
      </if>
      <if test="ifEmpty != null">
        if_empty = #{ifEmpty,jdbcType=INTEGER},
      </if>
      <if test="ifRollback != null">
        if_rollback = #{ifRollback,jdbcType=INTEGER},
      </if>
      <if test="rollbackCauseType != null">
        rollback_cause_type = #{rollbackCauseType,jdbcType=INTEGER},
      </if>
      <if test="rollbackCauseTypeTwo != null">
        rollback_cause_type_two = #{rollbackCauseTypeTwo,jdbcType=INTEGER},
      </if>
      <if test="rollbackRemark != null">
        rollback_remark = #{rollbackRemark,jdbcType=VARCHAR},
      </if>
      <if test="availableOnWeekends != null">
        available_on_weekends = #{availableOnWeekends,jdbcType=INTEGER},
      </if>
      <if test="loadingUnloadingPart != null">
        loading_unloading_part = #{loadingUnloadingPart,jdbcType=INTEGER},
      </if>
      <if test="loadingUnloadingCharge != null">
        loading_unloading_charge = #{loadingUnloadingCharge,jdbcType=DECIMAL},
      </if>
      <if test="recycleTaskType != null">
        recycle_task_type = #{recycleTaskType,jdbcType=INTEGER},
      </if>
      <if test="groundPushTaskCode != null">
        ground_push_task_code = #{groundPushTaskCode,jdbcType=VARCHAR},
      </if>
      <if test="projectLabel != null">
        project_label = #{projectLabel,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="sinopecOrderNo != null">
        sinopec_order_no = #{sinopecOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null">
        manufacturer_name = #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="itemTransGroupName != null">
        item_trans_group_name = #{itemTransGroupName,jdbcType=VARCHAR},
      </if>
      <if test="itemPackSpecName != null">
        item_pack_spec_name = #{itemPackSpecName,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherName != null">
        dispatcher_name = #{dispatcherName,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherPhone != null">
        dispatcher_phone = #{dispatcherPhone,jdbcType=VARCHAR},
      </if>
      <if test="ifObjection != null">
        if_objection = #{ifObjection,jdbcType=INTEGER},
      </if>
      <if test="ifObjectionSinopec != null">
        if_objection_sinopec = #{ifObjectionSinopec,jdbcType=INTEGER},
      </if>
      <if test="sinopecCustomerId != null">
        sinopec_customer_id = #{sinopecCustomerId,jdbcType=BIGINT},
      </if>
      <if test="sinopecCustomerName != null">
        sinopec_customer_name = #{sinopecCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="sinopecOnlineGoodsFlag != null">
        sinopec_online_goods_flag = #{sinopecOnlineGoodsFlag,jdbcType=INTEGER},
      </if>
      <if test="lbsCode != null">
        lbs_code = #{lbsCode,jdbcType=VARCHAR},
      </if>
      <if test="fixedDemand != null">
        fixed_demand = #{fixedDemand,jdbcType=VARCHAR},
      </if>
      <if test="autoPublish != null">
        auto_publish = #{autoPublish,jdbcType=INTEGER},
      </if>
      <if test="orderMode != null">
        order_mode = #{orderMode,jdbcType=INTEGER},
      </if>
      <if test="vehicleLengthId != null">
        vehicle_length_id = #{vehicleLengthId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLength != null">
        vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDemandOrder">
    update t_demand_order
    set entrust_status = #{entrustStatus,jdbcType=INTEGER},
      status_update_time = #{statusUpdateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      if_cancel = #{ifCancel,jdbcType=INTEGER},
      cancel_type = #{cancelType,jdbcType=INTEGER},
      cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      source = #{source,jdbcType=INTEGER},
      demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_user_name = #{customerUserName,jdbcType=VARCHAR},
      customer_user_mobile = #{customerUserMobile,jdbcType=VARCHAR},
      customer_order_source = #{customerOrderSource,jdbcType=INTEGER},
      customer_order_code = #{customerOrderCode,jdbcType=VARCHAR},
      publish_name = #{publishName,jdbcType=VARCHAR},
      publish_mobile = #{publishMobile,jdbcType=VARCHAR},
      publish_time = #{publishTime,jdbcType=TIMESTAMP},
      publish_org_code = #{publishOrgCode,jdbcType=VARCHAR},
      publish_org_name = #{publishOrgName,jdbcType=VARCHAR},
      ticket_time = #{ticketTime,jdbcType=TIMESTAMP},
      settlement_tonnage = #{settlementTonnage,jdbcType=INTEGER},
      carrier_settlement = #{carrierSettlement,jdbcType=INTEGER},
      goods_amount = #{goodsAmount,jdbcType=DECIMAL},
      arranged_amount = #{arrangedAmount,jdbcType=DECIMAL},
      not_arranged_amount = #{notArrangedAmount,jdbcType=DECIMAL},
      back_amount = #{backAmount,jdbcType=DECIMAL},
      difference_amount = #{differenceAmount,jdbcType=DECIMAL},
      abnormal_amount = #{abnormalAmount,jdbcType=DECIMAL},
      expect_contract_price = #{expectContractPrice,jdbcType=DECIMAL},
      expect_contract_price_type = #{expectContractPriceType,jdbcType=INTEGER},
      contract_price_type = #{contractPriceType,jdbcType=INTEGER},
      contract_price = #{contractPrice,jdbcType=DECIMAL},
      goods_unit = #{goodsUnit,jdbcType=INTEGER},
      company_entrust_id = #{companyEntrustId,jdbcType=BIGINT},
      company_entrust_name = #{companyEntrustName,jdbcType=VARCHAR},
      upstream_customer = #{upstreamCustomer,jdbcType=VARCHAR},
      company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
      company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
      company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
      carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
      carrier_contact_name = #{carrierContactName,jdbcType=VARCHAR},
      carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
      company_carrier_level = #{companyCarrierLevel,jdbcType=INTEGER},
      dispatch_vehicle_count = #{dispatchVehicleCount,jdbcType=INTEGER},
      entrust_type = #{entrustType,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      carrier_price_type = #{carrierPriceType,jdbcType=INTEGER},
      carrier_price = #{carrierPrice,jdbcType=DECIMAL},
      if_urgent = #{ifUrgent,jdbcType=INTEGER},
      dispatch_validity = #{dispatchValidity,jdbcType=INTEGER},
      if_overdue = #{ifOverdue,jdbcType=INTEGER},
      if_empty = #{ifEmpty,jdbcType=INTEGER},
      if_rollback = #{ifRollback,jdbcType=INTEGER},
      rollback_cause_type = #{rollbackCauseType,jdbcType=INTEGER},
      rollback_cause_type_two = #{rollbackCauseTypeTwo,jdbcType=INTEGER},
      rollback_remark = #{rollbackRemark,jdbcType=VARCHAR},
      available_on_weekends = #{availableOnWeekends,jdbcType=INTEGER},
      loading_unloading_part = #{loadingUnloadingPart,jdbcType=INTEGER},
      loading_unloading_charge = #{loadingUnloadingCharge,jdbcType=DECIMAL},
      recycle_task_type = #{recycleTaskType,jdbcType=INTEGER},
      ground_push_task_code = #{groundPushTaskCode,jdbcType=VARCHAR},
      project_label = #{projectLabel,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=INTEGER},
      sinopec_order_no = #{sinopecOrderNo,jdbcType=VARCHAR},
      manufacturer_name = #{manufacturerName,jdbcType=VARCHAR},
      item_trans_group_name = #{itemTransGroupName,jdbcType=VARCHAR},
      item_pack_spec_name = #{itemPackSpecName,jdbcType=VARCHAR},
      dispatcher_name = #{dispatcherName,jdbcType=VARCHAR},
      dispatcher_phone = #{dispatcherPhone,jdbcType=VARCHAR},
      if_objection = #{ifObjection,jdbcType=INTEGER},
      if_objection_sinopec = #{ifObjectionSinopec,jdbcType=INTEGER},
      sinopec_customer_id = #{sinopecCustomerId,jdbcType=BIGINT},
      sinopec_customer_name = #{sinopecCustomerName,jdbcType=VARCHAR},
      sinopec_online_goods_flag = #{sinopecOnlineGoodsFlag,jdbcType=INTEGER},
      lbs_code = #{lbsCode,jdbcType=VARCHAR},
      fixed_demand = #{fixedDemand,jdbcType=VARCHAR},
      auto_publish = #{autoPublish,jdbcType=INTEGER},
      order_mode = #{orderMode,jdbcType=INTEGER},
      vehicle_length_id = #{vehicleLengthId,jdbcType=BIGINT},
      vehicle_length = #{vehicleLength,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>