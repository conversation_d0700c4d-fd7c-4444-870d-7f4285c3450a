package com.logistics.tms.api.feign.traycost;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.traycost.hystrix.TrayCostServiceApiHystrix;
import com.logistics.tms.api.feign.traycost.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/20 19:14
 */
@Api(value = "API-TrayCostServiceApi-托盘费用管理")
@FeignClient(name = "logistics-tms-services", fallback = TrayCostServiceApiHystrix.class)
public interface TrayCostServiceApi {

    @ApiOperation(value = "托盘费用列表")
    @PostMapping("/service/trayCost/searchTrayCostList")
    Result<List<SearchTrayCostListResponseModel>> searchTrayCostList();

    @ApiOperation(value = "托盘费用详情")
    @PostMapping("/service/trayCost/getDetail")
    Result<TrayCostDetailResponseModel> getDetail(@RequestBody TrayCostIdRequestModel requestModel);

    @ApiOperation(value = "修改费用")
    @PostMapping("/service/trayCost/modifyPrice")
    Result modifyPrice(@RequestBody ModifyPriceRequestModel requestModel);

    @ApiOperation(value = "费用记录列表")
    @PostMapping("/service/trayCost/searchCostRecordsList")
    Result<PageInfo<SearchCostRecordsListResponseModel>> searchCostRecordsList(@RequestBody SearchCostRecordsListRequestModel requestModel);
}
