package com.logistics.management.webapi.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @author: wjf
 * @date: 2024/1/29 9:31
 */
@Getter
@AllArgsConstructor
public enum MatchingFieldEnum {
    DEFAULT(0, ""),
    AREA(1, "区域"),
    WAREHOUSE(2, "仓库"),
    ;

    private final Integer key;
    private final String value;

    public static MatchingFieldEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
