package com.logistics.tms.biz.carrierorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/19
 */
@Data
public class SearchListStatisticsForYeloLifeModel {

	@ApiModelProperty("运单号")
	private Long carrierOrderId;

	@ApiModelProperty("运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收")
	private Integer status;

	@ApiModelProperty("是否取消 0 否 1 是")
	private Integer ifCancel;
}
