package com.logistics.management.webapi.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

@Data
public class CarrierEditSettleStatementNameRequestDto {

    @ApiModelProperty(value = "对账单id", required = true)
    @NotEmpty(message = "对账单id不能为空")
    private String settleStatementId;

    @ApiModelProperty(value = "对账单名称",required = true)
    @Size(max = 20, message = "请正确维护对账单名称，最多50字")
    private String settleStatementName;
}
