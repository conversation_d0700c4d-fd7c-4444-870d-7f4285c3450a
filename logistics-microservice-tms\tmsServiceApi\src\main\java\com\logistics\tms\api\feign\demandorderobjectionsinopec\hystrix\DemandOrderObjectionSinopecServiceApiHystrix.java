package com.logistics.tms.api.feign.demandorderobjectionsinopec.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.DemandOrderObjectionSinopecServiceApi;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.*;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2022/5/30 15:17
 */
@Component
public class DemandOrderObjectionSinopecServiceApiHystrix implements DemandOrderObjectionSinopecServiceApi {
    @Override
    public Result<PageInfo<SearchDemandOrderObjectionSinopecResponseModel>> searchSinopecObjection(SearchDemandOrderObjectionSinopecRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetSinopecObjectionDetailResponseModel> getSinopecObjectionDetail(GetSinopecObjectionDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> sinopecObjectionAudit(SinopecObjectionAuditRequestModel requestModel) {
        return Result.timeout();
    }
}
