package com.logistics.tms.controller.customeraccount.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/5/11 13:55
 */
@Data
public class CustomerLoginResponseModel {
    @ApiModelProperty("登录人账号id")
    private Long userId;
    @ApiModelProperty("登录人姓名")
    private String userName;
    @ApiModelProperty("登录人账号")
    private String userAccount;
    @ApiModelProperty("token")
    private String token;

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("实名认证(个人): 0 待实名 1 实名中 2 已实名")
    private Integer realNameAuthenticationStatus;

    @ApiModelProperty("是否可跳转云仓小程序: 0 不可跳转 1 可跳转")
    private Integer warehouseSwitch;
}
