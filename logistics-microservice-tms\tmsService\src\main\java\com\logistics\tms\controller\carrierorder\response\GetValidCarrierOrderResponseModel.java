package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2022/9/6 9:52
 */
@Data
public class GetValidCarrierOrderResponseModel {
    @ApiModelProperty("运单id")
    private Long carrierOrderId;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消")
    private Integer status ;

    //车主
    @ApiModelProperty("车主公司类型")
    private Integer companyCarrierType;
    @ApiModelProperty("车主公司id")
    private Long companyCarrierId;
    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;
    @ApiModelProperty("车主联系人")
    private String carrierContactName;
    @ApiModelProperty("车主联系人电话")
    private String carrierContactMobile;

    @ApiModelProperty("预计承运数量")
    private BigDecimal expectAmount ;
    @ApiModelProperty("实际提货数量")
    private BigDecimal loadAmount ;
    @ApiModelProperty("实际卸货数量")
    private BigDecimal unloadAmount ;
    @ApiModelProperty("实际签收数量")
    private BigDecimal signAmount ;

    @ApiModelProperty("货物单位：1 件，2 吨，3 方，4 块")
    private Integer goodsUnit;

    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("手机号")
    private String driverMobile;
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("提货市")
    private String loadCityName;
    @ApiModelProperty("卸货市")
    private String unloadCityName;

    @ApiModelProperty("装卸方: 0 空，1 我司装卸，2 客户装卸")
    private Integer loadingUnloadingPart;
    @ApiModelProperty("装卸费用")
    private BigDecimal loadingUnloadingCharge;
}
