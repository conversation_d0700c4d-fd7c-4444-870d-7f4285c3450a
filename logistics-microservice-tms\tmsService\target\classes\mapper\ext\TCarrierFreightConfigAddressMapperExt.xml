<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierFreightConfigAddressMapper">
    <update id="batchUpdateSelective" parameterType="com.logistics.tms.entity.TCarrierFreightConfigAddress">
        <foreach collection="recordList" item="item" separator=";">
            update t_carrier_freight_config_address
            <set>
                <if test="item.schemeId != null">
                    scheme_id = #{item.schemeId,jdbcType=BIGINT},
                </if>
                <if test="item.fromProvinceId != null">
                    from_province_id = #{item.fromProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.fromProvinceName != null">
                    from_province_name = #{item.fromProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.fromCityId != null">
                    from_city_id = #{item.fromCityId,jdbcType=BIGINT},
                </if>
                <if test="item.fromCityName != null">
                    from_city_name = #{item.fromCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.fromAreaId != null">
                    from_area_id = #{item.fromAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.fromAreaName != null">
                    from_area_name = #{item.fromAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.toProvinceId != null">
                    to_province_id = #{item.toProvinceId,jdbcType=BIGINT},
                </if>
                <if test="item.toProvinceName != null">
                    to_province_name = #{item.toProvinceName,jdbcType=VARCHAR},
                </if>
                <if test="item.toCityId != null">
                    to_city_id = #{item.toCityId,jdbcType=BIGINT},
                </if>
                <if test="item.toCityName != null">
                    to_city_name = #{item.toCityName,jdbcType=VARCHAR},
                </if>
                <if test="item.toAreaId != null">
                    to_area_id = #{item.toAreaId,jdbcType=BIGINT},
                </if>
                <if test="item.toAreaName != null">
                    to_area_name = #{item.toAreaName,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null">
                    enabled = #{item.enabled,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="searchAddressRuleList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_freight_config_address
        where valid = 1
          and scheme_id = #{schemeId,jdbcType=BIGINT}
        <if test="freightConfigAddressIds != null and freightConfigAddressIds != ''">
            and id in (${freightConfigAddressIds })
        </if>
        <if test="loadAddress != null and loadAddress != ''">
            and (instr(from_city_name, #{loadAddress,jdbcType=VARCHAR})
                or instr(from_area_name, #{loadAddress,jdbcType=VARCHAR}))
        </if>
        <if test="unloadAddress != null and unloadAddress != ''">
            and (instr(to_city_name, #{unloadAddress,jdbcType=VARCHAR})
                or instr(to_area_name, #{unloadAddress,jdbcType=VARCHAR}))
        </if>
        <if test="enabled != null">
            and enabled = #{enabled,jdbcType=INTEGER}
        </if>
        order by last_modified_time desc, id desc
    </select>

    <select id="searchFreightAddressListByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_freight_config_address
        where valid = 1
          and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="searchAreaRouteExist" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_freight_config_address
        where valid = 1
          and scheme_id = #{freightConfigSchemeId,jdbcType=BIGINT}
          and
        <foreach collection="areaList" item="item" open="(" close=")" separator="or">
            (from_area_id = #{item.fromAreaId} and to_area_id = #{item.toAreaId})
        </foreach>
    </select>


    <select id="searchAddressByConfigSchemeId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_freight_config_address
        where valid = 1
        and scheme_id = #{freightConfigSchemeId,jdbcType=BIGINT}
    </select>

    <update id="deleteBySchemeId">
        update t_carrier_freight_config_address set valid = 0, last_modified_time = now()
        <if test="lastModifiedBy != null" >
            , last_modified_by = #{lastModifiedBy}
        </if>
        where valid = 1
        <if test="schemeId != null">
            and scheme_id =#{schemeId}
        </if>
    </update>

    <insert id="batchOneSqlInsert" parameterType="com.logistics.tms.entity.TCarrierFreightConfigAddress"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_carrier_freight_config_address
        (id, scheme_id, from_province_id,
        from_province_name, from_city_id, from_city_name,
        from_area_id, from_area_name, to_province_id,
        to_province_name, to_city_id, to_city_name,
        to_area_id, to_area_name, enabled,
        created_by, created_time, last_modified_by,
        last_modified_time, valid)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.schemeId,jdbcType=BIGINT}, #{item.fromProvinceId,jdbcType=BIGINT},
             #{item.fromProvinceName,jdbcType=VARCHAR}, #{item.fromCityId,jdbcType=BIGINT}, #{item.fromCityName,jdbcType=VARCHAR},
             #{item.fromAreaId,jdbcType=BIGINT}, #{item.fromAreaName,jdbcType=VARCHAR}, #{item.toProvinceId,jdbcType=BIGINT},
             #{item.toProvinceName,jdbcType=VARCHAR}, #{item.toCityId,jdbcType=BIGINT}, #{item.toCityName,jdbcType=VARCHAR},
             #{item.toAreaId,jdbcType=BIGINT}, #{item.toAreaName,jdbcType=VARCHAR}, #{item.enabled,jdbcType=INTEGER},
             #{item.createdBy,jdbcType=VARCHAR}, #{item.createdTime,jdbcType=TIMESTAMP}, #{item.lastModifiedBy,jdbcType=VARCHAR},
             #{item.lastModifiedTime,jdbcType=TIMESTAMP}, #{item.valid,jdbcType=INTEGER})
        </foreach>
    </insert>

    <resultMap id="selectAddressConfigAndLadderBySchemeIdMap" type="com.logistics.tms.biz.carrierfreight.model.AddressConfigModel">
        <id column="id" property="addressConfigId" jdbcType="BIGINT"/>
        <result column="scheme_id" property="schemeId"/>
        <result column="to_area_id" property="toAreaId"/>
        <result column="from_area_id" property="fromAreaId"/>
        <result column="to_area_name" property="toAreaName"/>
        <result column="from_area_name" property="fromAreaName"/>
        <collection property="ladderConfigList" ofType="com.logistics.tms.biz.carrierfreight.model.AddressConfigLadderModel">
            <id column="ladderId" property="ladderId" jdbcType="BIGINT"/>
            <result column="mode_id" property="modeId"/>
            <result column="ladder_to" property="ladderTo"/>
            <result column="price_mode" property="priceMode"/>
            <result column="ladder_pid" property="ladderPid"/>
            <result column="unit_price" property="unitPrice"/>
            <result column="ladder_mode" property="ladderMode"/>
            <result column="ladder_type" property="ladderType"/>
            <result column="ladder_from" property="ladderFrom"/>
            <result column="ladder_unit" property="ladderUnit"/>
            <result column="ladder_level" property="ladderLevel"/>
        </collection>
    </resultMap>

    <select id="selectAddressConfigAndLadderBySchemeId" resultMap="selectAddressConfigAndLadderBySchemeIdMap">
        select
        tcfca.id,
        tcfca.scheme_id,
        tcfca.from_area_id,
        tcfca.from_area_name,
        tcfca.to_area_id,
        tcfca.to_area_name,

        tcfcl.id ladderId,
        tcfcl.mode_id,
        tcfcl.price_mode,
        tcfcl.ladder_mode,
        tcfcl.ladder_level,
        tcfcl.ladder_pid,
        tcfcl.ladder_type,
        tcfcl.ladder_from,
        tcfcl.ladder_to,
        tcfcl.ladder_unit,
        tcfcl.unit_price
        from t_carrier_freight_config_address tcfca
        left join t_carrier_freight_config_ladder tcfcl on tcfcl.valid = 1 and tcfca.id = tcfcl.mode_id
        where tcfca.valid = 1
          and tcfca.scheme_id = #{schemeId,jdbcType=BIGINT}
        <if test="enabled">
            and enabled = #{enabled,jdbcType=INTEGER}
        </if>
    </select>
</mapper>