package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.client.carrierorder.response.CarrierOrderBeforeSignUpResponseModel;
import com.logistics.management.webapi.client.carrierorder.response.CarrierOrderListBeforeSignUpGoodsModel;
import com.logistics.management.webapi.client.carrierorder.response.CarrierOrderListBeforeSignUpResponseModel;
import com.logistics.management.webapi.controller.carrierorder.response.CarrierOrderBeforeSignUpResponseDto;
import com.logistics.management.webapi.controller.carrierorder.response.CarrierOrderListBeforeSignUpResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.util.ArrayList;

public class CarrierOrderListBeforeSignUpMapping extends MapperMapping<CarrierOrderBeforeSignUpResponseModel, CarrierOrderBeforeSignUpResponseDto> {
    @Override
    public void configure() {
        CarrierOrderBeforeSignUpResponseModel source = getSource();
        CarrierOrderBeforeSignUpResponseDto destination = getDestination();
        if (source != null) {
            destination.setCarrierOrderList(new ArrayList<>());
            if (ListUtils.isNotEmpty(source.getCarrierOrderList())) {
                CarrierOrderListBeforeSignUpResponseDto dto;
                BigDecimal amountTotal = BigDecimal.ZERO;
                BigDecimal capacityTotal = BigDecimal.ZERO;
                BigDecimal dispatchFreightFeeTotal = BigDecimal.ZERO;
                for (CarrierOrderListBeforeSignUpResponseModel tmp : source.getCarrierOrderList()) {
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal capacity = BigDecimal.ZERO;
                    StringBuilder goodsName = new StringBuilder();

                    if (tmp.getStatus() < CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey()) {
                        tmp.setLoadAmount(tmp.getExpectAmount());
                        tmp.setUnloadAmount(tmp.getExpectAmount());
                        tmp.setSignAmount(tmp.getExpectAmount());
                    } else if (tmp.getStatus() < CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()) {
                        tmp.setUnloadAmount(tmp.getLoadAmount());
                        tmp.setSignAmount(tmp.getLoadAmount());
                    } else {
                        tmp.setSignAmount(tmp.getUnloadAmount());
                    }

                    int index = 0;
                    for (CarrierOrderListBeforeSignUpGoodsModel goods:tmp.getGoodsInfo()) {
                        if(index != 0){
                            goodsName.append("/");
                        }
                        goodsName.append(goods.getGoodsName());
                        index++;
                        BigDecimal goodsAmount;
                        if (tmp.getStatus() > CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()) {
                            goodsAmount = goods.getUnloadAmount();
                        } else if (tmp.getStatus() > CarrierOrderStatusEnum.WAIT_LOAD.getKey()) {
                            goodsAmount = goods.getLoadAmount();
                        } else {
                            goodsAmount = goods.getExpectAmount();
                        }

                        amount = amount.add(goodsAmount);
                        BigDecimal goodsCapacity = ConverterUtils.toBigDecimal(goods.getLength()).multiply(ConverterUtils.toBigDecimal(goods.getWidth())).multiply(ConverterUtils.toBigDecimal(goods.getHeight()));
                        capacity = capacity.add(goodsAmount.multiply(goodsCapacity).divide(new BigDecimal(1000).pow(3),3,BigDecimal.ROUND_HALF_UP));
                    }

                    //司机运费
                    BigDecimal dispatchFreightFee = tmp.getDispatchFreightFee();
                    BigDecimal dispatchAmount;
                    if (tmp.getStatus() > CarrierOrderStatusEnum.WAIT_UNLOAD.getKey()) {
                        dispatchAmount = tmp.getUnloadAmount();
                    }else{
                        dispatchAmount = tmp.getExpectAmount();
                    }
                    if (PriceTypeEnum.UNIT_PRICE.getKey().equals(tmp.getDispatchFreightFeeType())) {
                        dispatchFreightFee = dispatchFreightFee.multiply(dispatchAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    dispatchFreightFee = dispatchFreightFee.add(tmp.getAdjustFee()).add(tmp.getMarkupFee());

                    //货主费用
                    BigDecimal entrustFreightFee = tmp.getEntrustFreight();
                    if (EntrustTypeEnum.BOOKING.getKey().equals(tmp.getEntrustType())){//tms系统调度的云盘预约单
                        entrustFreightFee = dispatchFreightFee;
                    }else{
                        if (PriceTypeEnum.UNIT_PRICE.getKey().equals(tmp.getEntrustFreightType())) {
                            //货主实际结算数量
                            BigDecimal entrustSettlementAmount = BigDecimal.ZERO;
                            if (SettlementTonnageEnum.LOAD.getKey().equals(tmp.getSettlementTonnage())) {
                                entrustSettlementAmount = tmp.getLoadAmount();
                            } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(tmp.getSettlementTonnage())) {
                                entrustSettlementAmount = tmp.getUnloadAmount();
                            } else if (SettlementTonnageEnum.SIGN.getKey().equals(tmp.getSettlementTonnage())) {
                                entrustSettlementAmount = tmp.getSignAmount();
                            } else if (SettlementTonnageEnum.EXPECT.getKey().equals(tmp.getSettlementTonnage())) {
                                entrustSettlementAmount = tmp.getExpectAmount();
                            }
                            entrustFreightFee = entrustFreightFee.multiply(entrustSettlementAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                        }
                    }

                    dto = new CarrierOrderListBeforeSignUpResponseDto();
                    dto.setCarrierOrderId(tmp.getCarrierOrderId().toString());
                    dto.setCarrierOrderCode(tmp.getCarrierOrderCode());
                    dto.setGoodsName(goodsName.toString());
                    dto.setAmount(amount.stripTrailingZeros().toPlainString());
                    dto.setCapacity(capacity.stripTrailingZeros().toPlainString());
                    dto.setDispatchFreightFee(ConverterUtils.toString(dispatchFreightFee));
                    dto.setEntrustFreightFee(ConverterUtils.toString(entrustFreightFee));
                    if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(tmp.getSource())){
                        dto.setIfLeYi(CommonConstant.ONE);
                    }
                    destination.getCarrierOrderList().add(dto);

                    amountTotal = amountTotal.add(amount);
                    capacityTotal = capacityTotal.add(capacity);
                    dispatchFreightFeeTotal = dispatchFreightFeeTotal.add(dispatchFreightFee);
                }
                destination.setGoodsUnit(ConverterUtils.toString(source.getCarrierOrderList().get(0).getGoodsUnit()));
                destination.setAmountTotal(amountTotal.stripTrailingZeros().toPlainString());
                destination.setCapacityTotal(capacityTotal.stripTrailingZeros().toPlainString());
                destination.setDispatchFreightFeeTotal(ConverterUtils.toString(dispatchFreightFeeTotal));
            }
        }
    }
}
