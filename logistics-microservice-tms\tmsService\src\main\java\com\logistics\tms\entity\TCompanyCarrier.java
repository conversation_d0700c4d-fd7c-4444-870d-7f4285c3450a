package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2024/08/06
*/
@Data
public class TCompanyCarrier extends BaseEntity {
    /**
    * 公司主表id
    */
    @ApiModelProperty("公司主表id")
    private Long companyId;

    /**
    * 公司简称水印
    */
    @ApiModelProperty("公司简称水印")
    private String companyWaterMark;

    /**
    * 道路许可证号
    */
    @ApiModelProperty("道路许可证号")
    private String roadTransportCertificateNumber;

    /**
    * 道路许可证图片
    */
    @ApiModelProperty("道路许可证图片")
    private String roadTransportCertificateImage;

    /**
    * 道路许可证有效期
    */
    @ApiModelProperty("道路许可证有效期")
    private Date roadTransportCertificateValidityTime;

    /**
    * 道路许可证是否永久 0不用就1永久
    */
    @ApiModelProperty("道路许可证是否永久 0不用就1永久")
    private Integer roadTransportCertificateIsForever;

    /**
    * 道路许可证是否后补 0 否1 是
    */
    @ApiModelProperty("道路许可证是否后补 0 否1 是")
    private Integer roadTransportCertificateIsAmend;

    /**
    * 是否启用,1启用,0禁用
    */
    @ApiModelProperty("是否启用,1启用,0禁用")
    private Integer enabled;

    /**
    * 货主类型 1公司 2 个人
    */
    @ApiModelProperty("货主类型 1公司 2 个人")
    private Integer type;

    /**
    * 公司级别 1 奇亚 2 二级承运商 
    */
    @ApiModelProperty("公司级别 1 奇亚 2 二级承运商 ")
    private Integer level;

    /**
    *  来源 1 后台添加 2 web注册 3 app注册 4 车主升级
    */
    @ApiModelProperty(" 来源 1 后台添加 2 web注册 3 app注册 4 车主升级")
    private Integer source;

    /**
    * 临时费用提交：0 否，1 是
    */
    @ApiModelProperty("临时费用提交：0 否，1 是")
    private Integer commitOtherFee;

    /**
    * 临时费用费点
    */
    @ApiModelProperty("临时费用费点")
    private BigDecimal otherFeeTaxPoint;

    /**
    * 运费费点
    */
    @ApiModelProperty("运费费点")
    private BigDecimal freightTaxPoint;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 授权状态(企业): 0 待授权 1 待审核 2已驳回 3 已授权
    */
    @ApiModelProperty("授权状态(企业): 0 待授权 1 待审核 2已驳回 3 已授权")
    private Integer authorizationStatus;

    /**
    * 实名认证(个人): 0 待实名 1 实名中 2 已实名
    */
    @ApiModelProperty("实名认证(个人): 0 待实名 1 实名中 2 已实名")
    private Integer realNameAuthenticationStatus;

    /**
    * 是否加入黑名单：0 否，1 是
    */
    @ApiModelProperty("是否加入黑名单：0 否，1 是")
    private Integer ifAddBlacklist;

    /**
    * 是否零担模式：0 否，1 是
    */
    @ApiModelProperty("是否零担模式：0 否，1 是")
    private Integer ifLessThanTruckload;



    /**
     * 是否零担模式：0 否，1 是
     */
    @ApiModelProperty("零担运价id")
    private Long shippingFreightId;

    /**
     * 零担运价新增人
     */
    @ApiModelProperty("零担运价新增人")
    private String shippingFreightAddUser;

    /**
     * 零担运价新增人
     */
    @ApiModelProperty("零担运价新增时间")
    private Date shippingFreightAddTime;


    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}