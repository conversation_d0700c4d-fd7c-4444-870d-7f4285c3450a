package com.logistics.tms.controller.staff.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class IntegrityExaminationAddOrModifyRequestModel {

    @ApiModelProperty("诚信考核记录Id")
    private Long integrityExaminationRecordId;
    @ApiModelProperty("人员Id")
    private Long staffId;
    @ApiModelProperty("诚信考核有效期")
    private Date validDate;
    @ApiModelProperty("凭证地址")
    private List<String> imagePaths;
    @ApiModelProperty("备注")
    private String remark;

}
