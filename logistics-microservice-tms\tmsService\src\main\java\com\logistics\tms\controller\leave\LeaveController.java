package com.logistics.tms.controller.leave;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.attendanceaskleave.AttendanceAskLeaveBiz;
import com.logistics.tms.controller.leave.request.*;
import com.logistics.tms.controller.leave.response.DriverLeaveApplyDetailResponseModel;
import com.logistics.tms.controller.leave.response.DriverLeaveApplyListResponseModel;
import com.logistics.tms.controller.leave.response.LeaveApplyDetailResponseModel;
import com.logistics.tms.controller.leave.response.LeaveApplySearchListResponseModel;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/14 13:53
 */
@Api(value = "请假申请", tags = "请假申请")
@RestController
public class LeaveController {

    @Resource
    private AttendanceAskLeaveBiz attendanceAskLeaveBiz;

    /**
     * 小程序 - 请假申请
     * @param requestModel
     * @return boolean
     */
    @ApiOperation(value = "请假申请", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/leave/leaveApply")
    public Result<Boolean> driverLeaveApply(@RequestBody DriverLeaveApplyRequestModel requestModel) {
        return Result.success(attendanceAskLeaveBiz.driverLeaveApply(requestModel));
    }

    /**
     * 小程序 - 请假记录列表
     * @param requestModel
     * @return LeaveApplyListResponseModel
     */
    @ApiOperation(value = "请假记录查询", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/leave/leaveApplyList")
    public Result<DriverLeaveApplyListResponseModel> driverLeaveApplyList(@RequestBody DriverLeaveApplyListRequestModel requestModel) {
        return Result.success(attendanceAskLeaveBiz.driverLeaveApplyList(requestModel));
    }

    /**
     * 小程序 - 请假申请详情
     * @param requestModel
     * @return LeaveApplyDetailResponseModel
     */
    @ApiOperation(value = "请假记录详情查询", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/leave/leaveApplyDetail")
    public Result<DriverLeaveApplyDetailResponseModel> driverLeaveApplyDetail(@RequestBody DriverLeaveApplyDetailRequestModel requestModel) {
        return Result.success(attendanceAskLeaveBiz.driverLeaveApplyDetail(requestModel));
    }

    /**
     * 小程序 - 重新提交请假申请
     * @param requestModel
     * @return boolean
     */
    @ApiOperation(value = "重新提交请假申请", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/leave/resubmitLeaveApply")
    public Result<Boolean> driverResubmitLeaveApply(@RequestBody DriverLeaveApplyResubmitRequestModel requestModel) {
        return Result.success(attendanceAskLeaveBiz.driverResubmitLeaveApply(requestModel));
    }

    /**
     * 小程序 - 司机撤销请假申请
     * @param requestModel
     * @return boolean
     */
    @ApiOperation(value = "撤销请假申请", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/leave/cancelLeaveApply")
    public Result<Boolean> driverCancelLeaveApply(@RequestBody DriverLeaveApplyCancelRequestModel requestModel) {
        return Result.success(attendanceAskLeaveBiz.driverCancelLeaveApply(requestModel));
    }

    /**
     * 后台 - 查询请假申请列表
     * @param requestModel
     * @return PageInfo<LeaveApplySearchListResponseModel>
     */
    @ApiOperation(value = "查询请假记录列表", tags = "1.1.8")
    @PostMapping(value = "/service/leave/searchLeaveApplyList")
    public Result<PageInfo<LeaveApplySearchListResponseModel>> searchLeaveApplyList(@RequestBody LeaveApplySearchListRequestModel requestModel) {
        return Result.success(attendanceAskLeaveBiz.searchLeaveApplyList(requestModel));
    }

    /**
     * 后台 - 导出请假申请列表
     * @param requestModel
     * @return List<LeaveApplySearchListResponseModel>
     */
    @ApiOperation(value = "导出请假记录列表", tags = "1.1.8")
    @PostMapping(value = "/service/leave/exportLeaveApplyList")
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<List<LeaveApplySearchListResponseModel>> exportLeaveApplyList(@RequestBody LeaveApplySearchListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<LeaveApplySearchListResponseModel> pageInfo = attendanceAskLeaveBiz.searchLeaveApplyList(requestModel);
        return Result.success(pageInfo.getList());
    }

    /**
     * 后台 - 撤销请假申请
     * @param requestModel
     * @return Boolean
     */
    @ApiOperation(value = "撤销请假申请", tags = "1.1.8")
    @PostMapping(value = "/service/leave/cancelLeaveApply")
    public Result<Boolean> cancelLeaveApply(@RequestBody LeaveApplyCancelRequestModel requestModel) {
        return Result.success(attendanceAskLeaveBiz.cancelLeaveApply(requestModel));
    }

    /**
     * 后台 - 请假申请详情
     * @param requestModel
     * @return LeaveApplyDetailResponseModel
     */
    @ApiOperation(value = "查询请假记录详情", tags = "1.1.8")
    @PostMapping(value = "/service/leave/leaveApplyDetail")
    public Result<LeaveApplyDetailResponseModel> leaveApplyDetail(@RequestBody LeaveApplyDetailRequestModel requestModel) {
        return Result.success(attendanceAskLeaveBiz.leaveApplyDetail(requestModel));
    }

    /**
     * 后台 - 请假申请审核
     * @param requestModel
     * @return boolean
     */
    @ApiOperation(value = "审核请假申请", tags = "1.1.8")
    @PostMapping(value = "/service/leave/auditLeaveApply")
    public Result<Boolean> auditLeaveApply(@RequestBody LeaveApplyAuditRequestModel requestModel) {
        return Result.success(attendanceAskLeaveBiz.auditLeaveApply(requestModel));
    }
}
