package com.logistics.management.webapi.api.feign.driversafepromise.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 补发
 * @Author: sj
 * @Date: 2019/11/4 10:17
 */
@Data
public class ReissueSavePromiseRequestDto {
    @ApiModelProperty("司机承诺书ID")
    @NotBlank(message = "请选择承诺书")
    private String safePromiseId;
    @ApiModelProperty("司机列表")
    private List<String> driverInfoList;
}
