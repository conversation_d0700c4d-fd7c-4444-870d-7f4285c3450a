package com.logistics.tms.controller.workgroup.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @author: wjf
 * @date: 2023/12/22 15:44
 */
@Data
public class WorkGroupDistrictResponseModel {

    @ApiModelProperty("区域配置id")
    private Long workGroupDistrictId;

    @ApiModelProperty(value = "省份id")
    private Long provinceId;

    @ApiModelProperty(value = "省份名")
    private String provinceName;

    @ApiModelProperty(value = "市id")
    private Long cityId;

    @ApiModelProperty(value = "市名")
    private String cityName;
}
