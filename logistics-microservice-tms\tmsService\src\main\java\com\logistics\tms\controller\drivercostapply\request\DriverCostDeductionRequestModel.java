package com.logistics.tms.controller.drivercostapply.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DriverCostDeductionRequestModel {

    @ApiModelProperty("备用金单号")
    private String reserveCode;

    @ApiModelProperty("余额")
    private BigDecimal balance;

    @ApiModelProperty("冲销金额")
    private BigDecimal writeOffAmount;

    @ApiModelProperty("备用金类型：1 申请，2 垫付，3 红冲退款")
    private Integer reserveType;
}
