package com.logistics.management.webapi.api.feign.extvehiclesettlement;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.extvehiclesettlement.dto.*;
import com.logistics.management.webapi.api.feign.extvehiclesettlement.hystrix.ExtVehicleSettlementApiHystrix;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * @author: wjf
 * @date: 2019/11/21 13:19
 */
@Api(value = "API-ExtVehicleSettlementApi-外部车辆结算管理")
@FeignClient(name = "logistics-tms-managementWeb-api", fallback = ExtVehicleSettlementApiHystrix.class)
public interface ExtVehicleSettlementApi {

    @ApiOperation("列表 v1.1.1")
    @PostMapping(value = "/api/extVehicleSettlement/searchExtVehicleSettlementList")
    Result<PageInfo<SearchExtVehicleSettlementListResponseDto>> searchExtVehicleSettlementList(@RequestBody @Valid SearchExtVehicleSettlementListRequestDto requestDto);

    @ApiOperation("详情")
    @PostMapping(value = "/api/extVehicleSettlement/extVehicleSettlementDetail")
    Result<ExtVehicleSettlementDetailResponseDto> extVehicleSettlementDetail(@RequestBody @Valid ExtVehicleSettlementIdRequestDto requestDto);

    @ApiOperation("付款")
    @PostMapping(value = "/api/extVehicleSettlement/extVehicleSettlementPayment")
    Result extVehicleSettlementPayment(@RequestBody @Valid ExtVehicleSettlementPaymentRequestDto requestDto);

    @ApiOperation("回退")
    @PostMapping(value = "/api/extVehicleSettlement/extVehicleSettlementFallback")
    Result extVehicleSettlementFallback(@RequestBody @Valid ExtVehicleSettlementFallbackRequestDto requestDto);

    @ApiOperation("导出")
    @GetMapping(value = "/api/extVehicleSettlement/exportExtVehicleSettlement")
    void exportExtVehicleSettlement(SearchExtVehicleSettlementListRequestDto requestDto, HttpServletResponse response);
}
