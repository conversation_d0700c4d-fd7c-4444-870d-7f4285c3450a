package com.logistics.tms.biz.attendancerecord.handle;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.AttendancePunchTypeEnum;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.biz.attendancerecord.model.AttendanceClockModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.entity.TAttendanceRecord;
import com.logistics.tms.mapper.TAttendanceRecordMapper;
import com.yelo.tools.utils.ObjectUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

@Component
@RequiredArgsConstructor
public class UpdateDutyPunchHandle implements AttendancePunchHandle {

    private final CommonBiz commonBiz;
    private final TAttendanceRecordMapper attendanceRecordMapper;

    /**
     * 上班打卡 filter
     */
    @Override
    public boolean filter(Integer dutyPunchType) {
        return AttendancePunchTypeEnum.UPDATE_DUTY_CLOCK_IN.getKey().equals(dutyPunchType);
    }

    /**
     * 更新上班打卡 handel
     * @param boModel
     * @return true 成功 false 失败
     */
    @Override
    public boolean handle(AttendanceClockModel boModel) {

        if (ObjectUtils.isEmpty(boModel.getRecodeId())) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_RECORD_NOT_EXISTS);
        }
        // 查询考勤记录
        TAttendanceRecord record = attendanceRecordMapper.selectOneById(boModel.getRecodeId());
        if (ObjectUtils.isEmpty(record)) {
            throw new BizException(CarrierDataExceptionEnum.ATTENDANCE_RECORD_NOT_EXISTS);
        }

        // 构建model
        TAttendanceRecord updateRecord = new TAttendanceRecord();
        updateRecord.setOnDutyPunchTime(boModel.getOnDutyPunchTime());
        updateRecord.setOffDutyPunchTime(boModel.getOffDutyPunchTime());

        // 计算工时
        if (ObjectUtils.isNotEmpty(record.getOffDutyPunchTime())) {
            Date onDutyPunchTime = ObjectUtils.isEmpty(boModel.getOnDutyPunchTime()) ?
                    record.getOnDutyPunchTime():boModel.getOnDutyPunchTime();
            Date offDutyPunchTime = ObjectUtils.isEmpty(boModel.getOffDutyPunchTime()) ?
                    record.getOffDutyPunchTime():boModel.getOffDutyPunchTime();
            BigDecimal manHour = calculatingManHour(onDutyPunchTime, offDutyPunchTime);
            updateRecord.setManHour(manHour);
        }

        // 更新考勤记录
        return attendanceRecordMapper.updateById(updateRecord, boModel.getRecodeId()) > CommonConstant.INTEGER_ZERO;
    }

}
