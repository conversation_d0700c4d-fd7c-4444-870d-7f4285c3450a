package com.logistics.appapi.controller.renewableaudit.mapping;

import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.enums.YeloLifeGoodsUnitEnum;
import com.logistics.appapi.client.renewableaudit.response.SearchLifeSkuResponseModel;
import com.logistics.appapi.controller.renewableaudit.response.SearchLifeSkuResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/7
 */
public class SearchLifeSkuMapping extends MapperMapping<SearchLifeSkuResponseModel, SearchLifeSkuResponseDto> {
	@Override
	public void configure() {
		SearchLifeSkuResponseModel source = getSource();
		SearchLifeSkuResponseDto destination = getDestination();

		if (source != null) {
			//处理sku单位label
			if (CommonConstant.INTEGER_ONE.equals(source.getSuggestGoodsPriceUnit())) {
				destination.setSuggestGoodsPriceUnit(YeloLifeGoodsUnitEnum.BY_WEIGHT.getUnit());
			} else if (CommonConstant.INTEGER_TWO.equals(source.getSuggestGoodsPriceUnit())) {
				destination.setSuggestGoodsPriceUnit(YeloLifeGoodsUnitEnum.BY_PACKAGE.getUnit());
			}
		}
	}
}
