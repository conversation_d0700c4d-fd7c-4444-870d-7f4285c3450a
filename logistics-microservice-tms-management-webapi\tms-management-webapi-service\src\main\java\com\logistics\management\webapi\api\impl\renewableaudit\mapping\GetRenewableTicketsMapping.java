package com.logistics.management.webapi.api.impl.renewableaudit.mapping;

import com.logistics.management.webapi.api.feign.renewableaudit.dto.RenewableTicketsResponseDto;
import com.logistics.management.webapi.base.enums.CertificationPicturesFileTypeEnum;
import com.logistics.tms.api.feign.renewableaudit.model.RenewableAuditTicketsResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;
import java.util.Map;

public class GetRenewableTicketsMapping extends MapperMapping<RenewableAuditTicketsResponseModel, RenewableTicketsResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;

    public GetRenewableTicketsMapping(String imagePrefix, Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap = imageMap;
    }

    @Override
    public void configure() {
        RenewableAuditTicketsResponseModel source = getSource();
        RenewableTicketsResponseDto destination = getDestination();
        String filePath = source.getFilePath();
        if(StringUtils.isNotEmpty(filePath)){
            //现场图片
            if(CertificationPicturesFileTypeEnum.T_RENEWABLE_AUDIT_SCENE_PICTURE_FILE.getFileType().equals(source.getFileType())){
                destination.setScenePhotos(imagePrefix + imageMap.get(source.getFilePath()));
            //确认单据
            }else if(CertificationPicturesFileTypeEnum.T_RENEWABLE_AUDIT_CONFIRM_PICTURE_FILE.getFileType().equals(source.getFileType())){
                destination.setOrderPhotos(imagePrefix + imageMap.get(source.getFilePath()));
            }
        }
    }
}
