package com.logistics.tms.api.feign.terminalcustomeraddress.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.terminalcustomeraddress.TerminalCustomerAddressServiceApi;
import com.logistics.tms.api.feign.terminalcustomeraddress.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/1/4 16:13
 */
@Component
public class TerminalCustomerAddressServiceApiHystrix implements TerminalCustomerAddressServiceApi {
    @Override
    public Result<PageInfo<SearchTerminalCustomerAddressListResponseModel>> searchTerminalCustomerAddressList(SearchTerminalCustomerAddressListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchTerminalCustomerAddressListResponseModel>> exportTerminalCustomerAddressList(SearchTerminalCustomerAddressListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addOrModifyTerminalCustomerAddress(AddOrModifyTerminalCustomerAddressRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetTerminalCustomerAddressDetailResponseModel> getTerminalCustomerAddressDetail(GetTerminalCustomerAddressDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delTerminalCustomerAddress(DeleteTerminalCustomerAddressRequestModel requestModel) {
        return Result.timeout();
    }
}
