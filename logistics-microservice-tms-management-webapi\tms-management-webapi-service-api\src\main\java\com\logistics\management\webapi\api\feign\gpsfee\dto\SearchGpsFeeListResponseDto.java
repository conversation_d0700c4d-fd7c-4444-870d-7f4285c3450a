package com.logistics.management.webapi.api.feign.gpsfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/8 10:29
 */
@Data
public class SearchGpsFeeListResponseDto {
    private String gpsFeeId="";
    @ApiModelProperty("结算状态：0 待结算，1 部分结算，2 结算完成")
    private String status="";
    private String statusLabel="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("车辆机构：1 自主 2 外部 3 自营")
    private String vehicleProperty = "";
    @ApiModelProperty("车辆机构展示文本")
    private String vehiclePropertyLabel = "";
    @ApiModelProperty("司机")
    private String driverName="";
    private String driverPhone="";
    @ApiModelProperty("服务商")
    private String gpsServiceProvider="";
    @ApiModelProperty("终端型号")
    private String terminalType="";
    @ApiModelProperty("服务费")
    private String serviceFee="";
    @ApiModelProperty("SIM卡号")
    private String simNumber="";
    @ApiModelProperty("起始日期")
    private String startDate="";
    @ApiModelProperty("截止时间")
    private String endDate="";
    @ApiModelProperty("终止时间")
    private String finishDate="";
    @ApiModelProperty("合作状态：1 已预付，2 进行中，3 已终止")
    private String cooperationStatus="";
    private String cooperationStatusLabel="";
    @ApiModelProperty("备注")
    private String remark="";
    @ApiModelProperty("最后修改人")
    private String lastModifiedBy="";
    @ApiModelProperty("最后修改时间")
    private String lastModifiedTime="";
}
