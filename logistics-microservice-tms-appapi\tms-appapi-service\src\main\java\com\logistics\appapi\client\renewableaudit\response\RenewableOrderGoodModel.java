package com.logistics.appapi.client.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderGoodModel {

	@ApiModelProperty("货物id")
	private Long goodsId;

	@ApiModelProperty("sku编码")
	private String skuCode;

	@ApiModelProperty("货物名")
	private String goodsName;

	@ApiModelProperty("确认货物数量")
	private BigDecimal goodsAmount;

	@ApiModelProperty("货物单位")
	private Integer goodsUnit;

	@ApiModelProperty("建议单价")
	private BigDecimal suggestGoodsPrice;

	@ApiModelProperty("收货单价")
	private BigDecimal goodsPrice;
}
