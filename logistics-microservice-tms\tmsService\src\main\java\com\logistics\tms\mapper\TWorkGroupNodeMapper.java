package com.logistics.tms.mapper;

import com.logistics.tms.controller.workgroup.response.WorkGroupNodeListResponseModel;
import com.logistics.tms.entity.TWorkGroupNode;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* Created by Mybatis Generator on 2023/12/21
*/
@Mapper
public interface TWorkGroupNodeMapper extends BaseMapper<TWorkGroupNode> {

    void batchInsertAndReturnId(@Param("recordList") List<TWorkGroupNode> recordList);

    List<TWorkGroupNode> getByWorkGroupId(@Param("workGroupId") Long workGroupId);

    List<TWorkGroupNode> selectWorkGroupNodeByWorkGroupIdIn(@Param("workGroupIds") Collection<Long> workGroupIds,
                                                            @Param("orderType") Integer orderType,
                                                            @Param("orderNode") Integer orderNode);

    List<WorkGroupNodeListResponseModel> selectNodeListByWorkGroupId(@Param("workGroupId") Long workGroupId);
}