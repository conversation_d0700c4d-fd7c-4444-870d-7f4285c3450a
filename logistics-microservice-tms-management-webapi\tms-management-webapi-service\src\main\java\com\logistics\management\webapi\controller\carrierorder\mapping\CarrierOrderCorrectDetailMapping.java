package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CarrierSettleStatementStatusEnum;
import com.logistics.management.webapi.base.enums.CorrectStatusEnum;
import com.logistics.management.webapi.base.enums.VehicleSettleStatusEnum;
import com.logistics.management.webapi.client.carrierorder.response.CarrierOrderCorrectDetailResponseModel;
import com.logistics.management.webapi.controller.carrierorder.response.CarrierOrderCorrectDetailResponseDto;
import com.logistics.management.webapi.controller.carrierorder.response.CarrierOrderCorrectDetailTicketDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2021/9/18 16:59
 */
public class CarrierOrderCorrectDetailMapping extends MapperMapping<CarrierOrderCorrectDetailResponseModel, CarrierOrderCorrectDetailResponseDto> {

    private final String imagePrefix;
    private final Map<String, String> imageMap;
    //操作类型：1 纠错，2 查看
    private final String operateType;

    public CarrierOrderCorrectDetailMapping(String imagePrefix, Map<String, String> imageMap, String operateType) {
        this.imagePrefix = imagePrefix;
        this.imageMap = imageMap;
        this.operateType = operateType;
    }

    @Override
    public void configure() {
        CarrierOrderCorrectDetailResponseModel source = getSource();
        CarrierOrderCorrectDetailResponseDto destination = getDestination();

        //入库单
        if (ListUtils.isNotEmpty(source.getStockInTicketsList())) {
            List<String> stockInTicketsList = new ArrayList<>();
            for (String path : source.getStockInTicketsList()) {
                stockInTicketsList.add(imagePrefix + imageMap.get(path));
            }
            destination.setStockInTicketsList(stockInTicketsList);
        }
        //是否第二次纠错
        boolean secondCorrect = source.getAbnormalAmount() != null && source.getAbnormalAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO;
        //如果是第二次纠错没有确认纠错之前,  原实提数取第一次纠错后的
        if (secondCorrect && CommonConstant.ONE.equals(operateType)) {
            //第二次纠错
            destination.setLoadAmountExpect(source.getLoadAmount().stripTrailingZeros().toPlainString());
            destination.setUnloadAmountExpect(source.getUnloadAmount().stripTrailingZeros().toPlainString());
        } else {
            destination.setLoadAmountExpect(source.getLoadAmountExpect().stripTrailingZeros().toPlainString());
            destination.setUnloadAmountExpect(source.getUnloadAmountExpect().stripTrailingZeros().toPlainString());
        }
        /*
         如果有了云仓异常数判断是是第二次纠错,实卸数=云仓入库数-云仓异常数,
          但是已经同步了云仓异常数并且还没有确定第二次纠错时查看纠错详情的话不应该减去云仓异常数
         */
        if (secondCorrect) {
            //是第二次纠错但是还是待纠错的状态下查看详情
            if (CorrectStatusEnum.WAIT_CORRECT.getKey().equals(source.getCorrectStatus()) && CommonConstant.TWO.equals(operateType)) {
                destination.setRelUnloadAmount(source.getStockInCount().stripTrailingZeros().toPlainString());
            } else {
                destination.setRelUnloadAmount(source.getStockInCount().subtract(source.getAbnormalAmount()).stripTrailingZeros().toPlainString());
            }
        } else {
            destination.setRelUnloadAmount(source.getStockInCount().stripTrailingZeros().toPlainString());
        }

        //去除末尾的0
        destination.setStockInCount(source.getStockInCount().stripTrailingZeros().toPlainString());
        destination.setExpectAmount(source.getExpectAmount().stripTrailingZeros().toPlainString());
        destination.setLoadAmount(source.getLoadAmount().stripTrailingZeros().toPlainString());
        destination.setLoadErrorAmount(source.getLoadErrorAmount().stripTrailingZeros().toPlainString());
        destination.setLoseErrorAmount(source.getLoseErrorAmount().stripTrailingZeros().toPlainString());
        destination.setAbnormalAmount(source.getAbnormalAmount().stripTrailingZeros().toPlainString());

        //是否关联结算信息, 对账单,外部车辆结算,自主运费结算
        String relSettleStatement = CommonConstant.ZERO;
        if (source.getCarrierSettleStatementStatus() > CarrierSettleStatementStatusEnum.NOT_RELATED.getKey()) {
            //关联了对账单
            relSettleStatement = CommonConstant.ONE;
        } else if (VehicleSettleStatusEnum.PAYMENT.getKey().equals(source.getExtVehiclePayStatus())) {
            //关联了外部结算并且已经支付
            relSettleStatement = CommonConstant.ONE;
        } else if (source.isRelDriverSettlement()) {
            //关联了自主对账单
            relSettleStatement = CommonConstant.ONE;
        }
        destination.setRelSettleStatement(relSettleStatement);
        //回单
        List<CarrierOrderCorrectDetailTicketDto> tickets = new ArrayList<>();
        if (ListUtils.isNotEmpty(source.getSignTicketsList())) {
            CarrierOrderCorrectDetailTicketDto ticketDto;
            for (String path : source.getSignTicketsList()) {
                ticketDto = new CarrierOrderCorrectDetailTicketDto();
                ticketDto.setRelativePath(path);
                ticketDto.setSrc(imagePrefix + imageMap.get(path));
                tickets.add(ticketDto);
            }
        }
        destination.setTickets(tickets);
    }
}
