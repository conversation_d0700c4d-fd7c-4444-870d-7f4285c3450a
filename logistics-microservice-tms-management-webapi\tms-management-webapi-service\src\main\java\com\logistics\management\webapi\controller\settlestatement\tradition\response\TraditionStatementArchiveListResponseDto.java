package com.logistics.management.webapi.controller.settlestatement.tradition.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/9
 */
@Data
public class TraditionStatementArchiveListResponseDto {

	@ApiModelProperty("对账单详情item id")
	private String settleStatementItemId = "";

	@ApiModelProperty("运单ID")
	private String carrierOrderId = "";

	@ApiModelProperty("运单号")
	private String carrierOrderCode = "";

	@ApiModelProperty("归档原因")
	private String archiveRemark = "";

	@ApiModelProperty("操作人")
	private String lastModifiedBy = "";

	@ApiModelProperty("操作时间")
	private String lastModifiedTime = "";
}
