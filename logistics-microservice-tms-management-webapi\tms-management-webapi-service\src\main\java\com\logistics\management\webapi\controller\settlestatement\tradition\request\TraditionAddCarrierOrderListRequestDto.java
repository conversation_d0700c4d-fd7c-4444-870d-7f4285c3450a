package com.logistics.management.webapi.controller.settlestatement.tradition.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class TraditionAddCarrierOrderListRequestDto extends AbstractPageForm<TraditionAddCarrierOrderListRequestDto> implements Serializable {

    @ApiModelProperty(value = "对账单id",required = true)
    @NotBlank(message = "对账单id不能为空")
    private String settleStatementId;

    @ApiModelProperty(value = "车主id", required = true)
    @NotBlank(message = "车主不能为空")
    private String companyCarrierId;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("货主名称")
    private String companyEntrustName;

    @ApiModelProperty("车牌号")
    private String vehicleNumber;

    @ApiModelProperty("司机")
    private String driver;

    @ApiModelProperty("签收时间起")
    private String signTimeStart;

    @ApiModelProperty("签收时间止")
    private String signTimeEnd;
}
