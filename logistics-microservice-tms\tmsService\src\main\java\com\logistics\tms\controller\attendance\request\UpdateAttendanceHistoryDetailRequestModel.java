package com.logistics.tms.controller.attendance.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UpdateAttendanceHistoryDetailRequestModel {

    @ApiModelProperty(value = "考勤打卡ID", required = true)
    private Long attendanceRecordId;

    @ApiModelProperty(value = "要变更的打卡类型 1: 上班 2:下班", required = true)
    private Integer changeType;

    @ApiModelProperty(value = "申请变更类型 0: 修改申请 1:撤销申请", required = true)
    private Integer changeStatus;
}
