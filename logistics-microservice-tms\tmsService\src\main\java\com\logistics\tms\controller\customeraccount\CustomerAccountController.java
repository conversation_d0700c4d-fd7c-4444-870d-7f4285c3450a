package com.logistics.tms.controller.customeraccount;

import com.logistics.tms.biz.customeraccount.CustomerAccountBiz;
import com.logistics.tms.controller.customeraccount.request.*;
import com.logistics.tms.controller.customeraccount.response.CustomerLoginResponseModel;
import com.logistics.tms.controller.customeraccount.response.FindPasswordPrepareResponseModel;
import com.logistics.tms.controller.customeraccount.response.QYWeChatLoginResponseModel;
import com.logistics.tms.controller.customeraccount.response.UpdatePhoneByVerifyPhoneResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: wjf
 * @date: 2023/10/26 10:55
 */
@RestController
@Api(value = "客户账号模块")
@RequestMapping(value = "/service/customerAccount")
public class CustomerAccountController {

    @Resource
    private CustomerAccountBiz customerAccountBiz;

    //小程序
    /**
     * 司机小程序默认登录
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "司机小程序默认登录")
    @PostMapping(value = "/driverAppletDefaultLogin")
    public Result<CustomerLoginResponseModel> driverAppletDefaultLogin(@RequestBody DriverAppletDefaultLoginRequestModel requestModel) {
        return Result.success(customerAccountBiz.driverAppletDefaultLogin(requestModel));
    }

    /**
     * 司机小程序登录
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "司机小程序登录")
    @PostMapping(value = "/driverAppletLogin")
    public Result<CustomerLoginResponseModel> driverAppletLogin(@RequestBody CustomerLoginRequestModel requestModel) {
        return Result.success(customerAccountBiz.driverAppletLogin(requestModel));
    }

    /**
     * 小程序登录成功后绑定openId
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "小程序登录成功后绑定openId")
    @PostMapping(value = "/appletBindingOpenId")
    public Result<Boolean> appletBindingOpenId(@RequestBody AppletBindingOpenIdRequestModel requestModel) {
        customerAccountBiz.appletBindingOpenId(requestModel);
        return Result.success(true);
    }

    /**
     * 小程序退出登录（清空openid）
     * @return
     */
    @ApiOperation(value = "小程序退出登录（清空openid）")
    @PostMapping(value = "/appletLoginOut")
    public Result<Boolean> appletLoginOut() {
        customerAccountBiz.appletLoginOut();
        return Result.success(true);
    }

    /**
     * 直接修改密码（需要验证手机验证码）
     * @param requestModel
     * @return
     */
    @ApiOperation("直接修改密码（需要验证手机验证码）")
    @PostMapping(value = "/updateAccountPassword")
    public Result<Boolean> updateAccountPassword(@RequestBody UpdateAccountPasswordRequestModel requestModel) {
        customerAccountBiz.updateAccountPassword(requestModel);
        return Result.success(true);
    }

    /**
     * 根据用户账号id修改密码
     * @param requestModel
     * @return
     */
    @ApiOperation("根据用户账号id修改密码")
    @PostMapping(value = "/modifyPassword")
    public Result<Boolean> modifyPassword(@RequestBody ModifyPasswordRequestModel requestModel) {
        customerAccountBiz.modifyPassword(requestModel);
        return Result.success(true);
    }


    //web
    /**
     * 前台web客户登录
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "前台web客户登录")
    @PostMapping(value = "/customerLoginForWeb")
    public Result<CustomerLoginResponseModel> customerLoginForWeb(@RequestBody CustomerLoginRequestModel requestModel) {
        return Result.success(customerAccountBiz.customerLoginForWeb(requestModel));
    }

    /**
     * 找回密码-验证手机号
     * @param requestModel
     * @return
     */
    @ApiOperation("找回密码-验证手机号")
    @PostMapping(value = "/findPasswordPrepare")
    public Result<FindPasswordPrepareResponseModel> findPasswordPrepare(@RequestBody FindPasswordPrepareRequestModel requestModel) {
        return Result.success(customerAccountBiz.findPasswordPrepare(requestModel));
    }

    /**
     * 找回密码-更新密码
     * @param requestModel
     * @return
     */
    @ApiOperation("找回密码-更新密码")
    @PostMapping(value = "/updatePasswordForFind")
    public Result<Boolean> updatePasswordForFind(@RequestBody UpdatePasswordForFindRequestModel requestModel) {
        customerAccountBiz.updatePasswordForFind(requestModel);
        return Result.success(true);
    }

    /**
     * 更换手机号-验证手机号（前台车主、司机小程序）
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "更换手机号-验证手机号（前台车主、司机小程序）")
    @PostMapping(value = "/verifyPhone")
    public Result<UpdatePhoneByVerifyPhoneResponseModel> verifyPhone(@RequestBody UpdatePhoneByVerifyPhoneRequestModel requestModel) {
        return Result.success(customerAccountBiz.verifyPhone(requestModel));
    }

    /**
     * 更换手机号-更新手机号（前台车主、司机小程序）
     * @param requestModel
     * @return
     */
    @ApiOperation("更换手机号-更新手机号（前台车主、司机小程序）")
    @PostMapping(value = "/updatePhone")
    public Result<Boolean> updatePhone(@RequestBody UpdatePhoneRequestModel requestModel) {
        customerAccountBiz.updatePhone(requestModel);
        return Result.success(true);
    }

    @ApiOperation(value = "TMS移动办公小程序 - 根据企业微信用户手机号登录")
    @PostMapping(value = "/qy/login")
    public Result<QYWeChatLoginResponseModel> qyWeChatLogin(@RequestBody QYWeChatLoginRequestModel requestModel) {
        return Result.success(customerAccountBiz.qyWeChatLogin(requestModel));
    }
}
