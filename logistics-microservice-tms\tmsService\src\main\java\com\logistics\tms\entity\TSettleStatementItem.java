package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* Created by Mybatis Generator on 2022/11/15
*/
@Data
public class TSettleStatementItem extends BaseEntity {
    /**
    * 对账单表Id
    */
    @ApiModelProperty("对账单表Id")
    private Long settleStatementId;

    /**
    * 运单Id
    */
    @ApiModelProperty("运单Id")
    private Long carrierOrderId;

    /**
    * 运单号
    */
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    /**
    * 结算数量
    */
    @ApiModelProperty("结算数量")
    private BigDecimal settlementAmount;

    /**
    * 货物单位：1 件，2 吨
    */
    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;

    /**
    * 委托费用/车主运费
    */
    @ApiModelProperty("委托费用/车主运费")
    private BigDecimal entrustFreight;

    /**
    * 临时费用
    */
    @ApiModelProperty("临时费用")
    private BigDecimal otherFees;

    /**
    * 应付费用
    */
    @ApiModelProperty("应付费用")
    private BigDecimal payableFee;

    /**
    * 货主公司id
    */
    @ApiModelProperty("货主公司id")
    private Long companyEntrustId;

    /**
    * 货主名称
    */
    @ApiModelProperty("货主名称")
    private String companyEntrustName;

    /**
    * 车主公司id
    */
    @ApiModelProperty("车主公司id")
    private Long companyCarrierId;

    /**
    * 车主名称
    */
    @ApiModelProperty("车主名称")
    private String companyCarrierName;

    /**
    * 手机号（原长度50）
    */
    @ApiModelProperty("手机号（原长度50）")
    private String contactPhone;

    /**
    * 联系人姓名
    */
    @ApiModelProperty("联系人姓名")
    private String contactName;

    /**
    * 车主类型：1 公司，2 个人
    */
    @ApiModelProperty("车主类型：1 公司，2 个人")
    private Integer type;

    /**
    * 是否归档：0 否，1 是
    */
    @ApiModelProperty("是否归档：0 否，1 是")
    private Integer ifArchive;

    /**
    * 归档备注
    */
    @ApiModelProperty("归档备注")
    private String archiveRemark;

    /**
    * 归档人
    */
    @ApiModelProperty("归档人")
    private String archiveBy;

    /**
    * 归档时间
    */
    @ApiModelProperty("归档时间")
    private Date archiveTime;
}