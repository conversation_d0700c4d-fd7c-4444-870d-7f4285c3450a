package com.logistics.tms.base.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CarrierFreightConfigSchemeTypeEnum {
    DEFAULT(-1, "", ConfigTypeEnum.DEFAULT_VALUE),
    ROUTE_CONFIG(100, "路线配置", ConfigTypeEnum.FIXED_ROUTE),
    REGION_CONFIG(200, "同区-跨区价格", ConfigTypeEnum.AREA_CONFIG),
    REGION_SAME_CONFIG(201, "同区价格", ConfigTypeEnum.AREA_CONFIG),
    REGION_DIFFERENT_CONFIG(202, "跨区价格", ConfigTypeEnum.AREA_CONFIG),
    CALCULATIONS_DISTANCE_CONFIG(301, "系统计算预计距离", ConfigTypeEnum.DISTANCE_CONFIG),
    DISTANCE_CONFIG(302, "系统配置距离", ConfigTypeEnum.DISTANCE_CONFIG),
    ;
    private final Integer key;
    private final String value;
    private final ConfigTypeEnum configType;

    public static CarrierFreightConfigSchemeTypeEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

    public static List<Integer> getRegionType() {
        return Lists.newArrayList(REGION_CONFIG.key, REGION_SAME_CONFIG.key, REGION_DIFFERENT_CONFIG.key);
    }
}
