package com.logistics.tms.controller.demandorder.request.sinopec;

import com.yelo.tools.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/12
 */
@Data
public class SinopecManufacturerModel {

	@ApiModelProperty("生产企业类型 1内部企业，0外部企业")
	private Integer productionEnterpriseType;

	@ApiModelProperty("生产企业名称")
	private String productionEnterpriseName;

	@ApiModelProperty("生产企业编码")
	private String productionEnterpriseCode;

	/**
	 * 校验参数
	 *
	 * @return 校验结果
	 */
	public String check() {
		if (this.productionEnterpriseType == null) {
			return "生产企业类型不能为空";
		}
		if (StringUtils.isBlank(this.productionEnterpriseName)) {
			return "生产企业名称不能为空";
		}
		if (StringUtils.isBlank(this.productionEnterpriseCode)) {
			return "生产企业编码不能为空";
		}

		//长度校验
		if (this.productionEnterpriseType < 0 || this.productionEnterpriseType > 1) {
			return "生产企业类型只能是0或1";
		}
		if (this.productionEnterpriseName.length() > 100) {
			return "生产企业名称长度不能大于100个字符";
		}
		if (this.productionEnterpriseCode.length() > 20) {
			return "生产企业编码长度不能大于20个字符";
		}
		return null;
	}
}
