package com.logistics.tms.api.feign.safegroupmeeting.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class SafeGroupMeetingRelationModel {

    @ApiModelProperty(value = "会议人员关系Id")
    private Long safeGroupMeetingRelationId;

    @ApiModelProperty(value = "职务")
    private String position;

    @ApiModelProperty(value = "参与人姓名")
    private String participatePerson;

}
