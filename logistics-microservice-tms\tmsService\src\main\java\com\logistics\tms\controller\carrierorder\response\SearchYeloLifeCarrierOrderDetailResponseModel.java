package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/23
 */
@Data
public class SearchYeloLifeCarrierOrderDetailResponseModel {

	@ApiModelProperty("运单ID")
	private Long carrierOrderId;

	@ApiModelProperty("10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收 0 已取消")
	private Integer status;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("业务类型: 1:公司 2:个人")
	private Integer businessType;

	@ApiModelProperty("需求单号")
	private Long demandOrderId;

	@ApiModelProperty("需求单号")
	private String demandOrderCode;

	@ApiModelProperty("回收申请单号")
	private String recycleOrderCode;

	@ApiModelProperty("司机姓名")
	private String driverName;

	@ApiModelProperty("司机手机号")
	private String driverMobile;

	@ApiModelProperty("车牌号")
	private String vehicleNo;


	//发货地址信息
	@ApiModelProperty("发货仓库")
	private String loadWarehouse;
	@ApiModelProperty("发货省")
	private String loadProvinceName;
	@ApiModelProperty("发货市")
	private String loadCityName;
	@ApiModelProperty("发货区")
	private String loadAreaName;
	@ApiModelProperty("发货地址详情")
	private String loadDetailAddress;
	@ApiModelProperty("发件人")
	private String consignorName;
	@ApiModelProperty("发件人手机号")
	private String consignorMobile;

	//收货地址信息
	@ApiModelProperty("收货仓库")
	private String unloadWarehouse;
	@ApiModelProperty("收货省")
	private String unloadProvinceName;
	@ApiModelProperty("收货市")
	private String unloadCityName;
	@ApiModelProperty("收货区")
	private String unloadAreaName;
	@ApiModelProperty("收货地址详情")
	private String unloadDetailAddress;
	@ApiModelProperty("收件人")
	private String receiverName;
	@ApiModelProperty("收件人手机号")
	private String receiverMobile;

	@ApiModelProperty("提货时间")
	private Date loadTime;

	@ApiModelProperty("客户")
	private String customName;

	@ApiModelProperty("个人客户姓名")
	private String customerUserName;

	@ApiModelProperty("个人客户手机号")
	private String customerUserMobile;

	@ApiModelProperty("发布人姓名")
	private String publishName;

	@ApiModelProperty("发布人手机号")
	private String publishMobile;

	@ApiModelProperty("是否取消: 0:否 1:是")
	private Integer ifCancel;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("货物单位：1 件，2 吨")
	private Integer goodsUnit;

	@ApiModelProperty("货物")
	private List<SearchYeloLifeCarrierOrderGoodsModel> goodsList;

	@ApiModelProperty("票据")
	private List<TicketsModel> ticketList;

	private Integer ifRecycleByCode;

}
