package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TSinopecMessageLog extends BaseEntity {
    /**
    * 消息类型：1、委托单
    */
    @ApiModelProperty("消息类型：1、委托单")
    private Integer messageType;

    /**
    * 消息动作类型：1、新增，2、撤销（委托单）
    */
    @ApiModelProperty("消息动作类型：1、新增，2、撤销（委托单）")
    private Integer messageAction;

    /**
    * 消息接口调用IP
    */
    @ApiModelProperty("消息接口调用IP")
    private String messageIp;

    /**
    * 消息主体信息
    */
    @ApiModelProperty("消息主体信息")
    private String messageBody;
}