package com.logistics.tms.api.feign.violationregulation.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/6/3 11:39
 */
@Data
public class ViolationRegulationListResponseModel  {
    @ApiModelProperty("违章记录ID")
    private Long violationRegulationId;
    private Long vehicleId;
    @ApiModelProperty("司机ID")
    private Long driverId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车辆机构：1 自主 2 外部 3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("司机姓名")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driverPhone;
    @ApiModelProperty("违章时间")
    private Date occuranceTime;
    @ApiModelProperty("违章地点")
    private String occuranceAddress;
    @ApiModelProperty("扣分")
    private Integer deduction;
    @ApiModelProperty("罚款")
    private BigDecimal fine;
    @ApiModelProperty("备注信息")
    private String remark;
    @ApiModelProperty("最后操作人")
    private String lastModifiedBy;
    @ApiModelProperty("最后操作时间")
    private Date lastModifiedTime;
    @ApiModelProperty("证件信息列表")
    private List<CertificationPicturesResponseModel> fileList;
}
