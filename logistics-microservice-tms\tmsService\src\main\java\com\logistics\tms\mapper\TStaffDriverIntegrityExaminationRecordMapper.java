package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.controller.staff.response.IntegrityExaminationListResponseModel;
import com.logistics.tms.entity.TStaffDriverIntegrityExaminationRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface TStaffDriverIntegrityExaminationRecordMapper extends BaseMapper<TStaffDriverIntegrityExaminationRecord>{

    List<IntegrityExaminationListResponseModel>  integrityExaminationList(@Param("staffId") Long staffId);

    Map<String,String> getDueIntegrityExaminationCount(@Param("companyCarrierId") Long companyCarrierId,@Param("remindDays") Integer remindDays);

    int batchUpdate(@Param("list") List<TStaffDriverIntegrityExaminationRecord> list);

    int selectRecordByStaffIdAndValidDate(@Param("staffId") Long staffId, @Param("validDate") Date date);

    List<TStaffDriverIntegrityExaminationRecord> getTopDriverIntegrityExaminationByStaffId(@Param("staffIds") String staffIds);

}