package com.logistics.tms.biz.vehiclelength;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.CarrierDataExceptionEnum;
import com.logistics.tms.base.enums.IfValidEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.controller.vehiclelength.request.AddOrUpdateVehicleLengthRequestModel;
import com.logistics.tms.controller.vehiclelength.request.SearchVehicleLengthListRequestModel;
import com.logistics.tms.controller.vehiclelength.request.VehicleLengthDetailRequestModel;
import com.logistics.tms.controller.vehiclelength.response.SearchVehicleLengthListResponseModel;
import com.logistics.tms.controller.vehiclelength.response.SelectVehicleLengthListResponseModel;
import com.logistics.tms.controller.vehiclelength.response.VehicleLengthDetailResponseModel;
import com.logistics.tms.entity.TVehicleLength;
import com.logistics.tms.mapper.TVehicleLengthMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/4/29 13:15
 */
@Service
public class VehicleLengthBiz {

    @Resource
    private TVehicleLengthMapper tVehicleLengthMapper;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 分页搜索车长配置
     * @param requestModel 请求参数
     * @return 车长配置
     */
    public PageInfo<SearchVehicleLengthListResponseModel> searchVehicleLengthList(SearchVehicleLengthListRequestModel requestModel){
        requestModel.enablePaging();
        return new PageInfo<>(tVehicleLengthMapper.searchVehicleLengthList());
    }


    /**
     * 车长配置详情
     * @param requestModel 请求参数
     * @return 车长配置详情
     */
    public VehicleLengthDetailResponseModel vehicleLengthDetail(VehicleLengthDetailRequestModel requestModel){
        return tVehicleLengthMapper.vehicleLengthDetail(requestModel.getVehicleLengthId());
    }

    /**
     * 新增编辑车长
     * @param requestModel 请求参数
     */
    @Transactional
    public void addOrUpdateVehicleLength(AddOrUpdateVehicleLengthRequestModel requestModel){
        //编辑
        if (requestModel.getVehicleLengthId() != null && requestModel.getVehicleLengthId() > CommonConstant.LONG_ZERO){
            TVehicleLength dbVehicleLength = tVehicleLengthMapper.selectByPrimaryKey(requestModel.getVehicleLengthId());
            if (dbVehicleLength == null || IfValidEnum.INVALID.getKey().equals(dbVehicleLength.getValid())){
                throw new BizException(CarrierDataExceptionEnum.VEHICLE_LENGTH_INFO_NOT_EXIST);
            }

            TVehicleLength upVehicleLength = new TVehicleLength();
            upVehicleLength.setId(dbVehicleLength.getId());
            upVehicleLength.setVehicleLength(requestModel.getVehicleLength());
            upVehicleLength.setCarriageScopeMin(requestModel.getCarriageScopeMin());
            upVehicleLength.setCarriageScopeMax(requestModel.getCarriageScopeMax());
            commonBiz.setBaseEntityModify(upVehicleLength, BaseContextHandler.getUserName());
            tVehicleLengthMapper.updateByPrimaryKeySelective(upVehicleLength);
        }
        //新增
        else{
            TVehicleLength addVehicleLength = new TVehicleLength();
            addVehicleLength.setVehicleLength(requestModel.getVehicleLength());
            addVehicleLength.setCarriageScopeMin(requestModel.getCarriageScopeMin());
            addVehicleLength.setCarriageScopeMax(requestModel.getCarriageScopeMax());
            commonBiz.setBaseEntityAdd(addVehicleLength, BaseContextHandler.getUserName());
            tVehicleLengthMapper.insertSelective(addVehicleLength);
        }
    }

    /**
     * 删除车长
     * @param requestModel 请求参数
     */
    @Transactional
    public void delVehicleLength(VehicleLengthDetailRequestModel requestModel){
        TVehicleLength dbVehicleLength = tVehicleLengthMapper.selectByPrimaryKey(requestModel.getVehicleLengthId());
        if (dbVehicleLength == null || IfValidEnum.INVALID.getKey().equals(dbVehicleLength.getValid())){
            throw new BizException(CarrierDataExceptionEnum.VEHICLE_LENGTH_INFO_NOT_EXIST);
        }

        //删除车长
        TVehicleLength delVehicleLength = new TVehicleLength();
        delVehicleLength.setId(dbVehicleLength.getId());
        delVehicleLength.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(delVehicleLength, BaseContextHandler.getUserName());
        tVehicleLengthMapper.updateByPrimaryKeySelective(delVehicleLength);
    }

    /**
     * 下拉选择车长
     * @return 下拉选择车长
     */
    public List<SelectVehicleLengthListResponseModel> selectVehicleLengthList(){
        return tVehicleLengthMapper.selectVehicleLengthList();
    }
    
}
