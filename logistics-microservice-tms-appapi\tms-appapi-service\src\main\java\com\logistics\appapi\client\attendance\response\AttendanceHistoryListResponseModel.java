package com.logistics.appapi.client.attendance.response;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 考勤打卡历史响应
 */
@Data
public class AttendanceHistoryListResponseModel {

	@ApiModelProperty("打卡次数")
	private Integer clockInCount;

	@ApiModelProperty("工时")
	private BigDecimal manHourSum;

	@ApiModelProperty("考勤打卡历史")
	private PageInfo<AttendanceHistoryItemResponseModel> attendanceHistoryItem;
}
