package com.logistics.appapi.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class CarrierOrderTicketRequestDto {

	@ApiModelProperty(value = "单据类型：1 提货单，2 出库单，3 签收单，4 其他，5 到达提货地凭证，6 到达卸货地凭证，8 提货现场图片，9 卸货现场图片")
	private String ticketType;

	@ApiModelProperty(value = "图片路径")
	private String src;
}
