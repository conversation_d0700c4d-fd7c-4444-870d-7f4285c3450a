package com.logistics.tms.biz.basicinfo.web;


import com.alibaba.fastjson.JSONObject;
import com.logistics.tms.controller.baiscinfo.applet.request.GetPersonAuthVerifyCodeRequestModel;
import com.logistics.tms.controller.baiscinfo.web.request.CarrierBasicInfoSubmitRequestModel;
import com.logistics.tms.controller.baiscinfo.web.request.UploadAuthorizationRequestModel;
import com.logistics.tms.controller.baiscinfo.web.request.VerifyPersonThreeElementsRequestModel;
import com.logistics.tms.controller.baiscinfo.web.request.VerifyPersonTwoElementsRequestModel;
import com.logistics.tms.controller.baiscinfo.web.response.CarrierBasicInfoResponseModel;
import com.logistics.tms.controller.baiscinfo.web.response.DownloadAuthTemplateResponseModel;
import com.logistics.tms.controller.baiscinfo.web.response.VerifyPersonThreeElementsResponseModel;
import com.logistics.tms.controller.baiscinfo.web.response.VerifyPersonTwoElementsResponseModel;
import com.logistics.tms.controller.companycarrier.response.FuzzySearchCompanyCarrierResponseModel;
import com.logistics.tms.api.feign.mailinginfo.model.response.MailingInfoDetailResponseModel;
import com.logistics.tms.base.constant.BestSignCommon;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.authentication.AuthenticationBiz;
import com.logistics.tms.biz.basicinfo.applet.DriverBasicInfoBiz;
import com.logistics.tms.biz.basicinfo.common.BasicInfoCommonBiz;
import com.logistics.tms.biz.basicinfo.common.model.BestSignUserRegModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.customeraccount.CustomerAccountBiz;
import com.logistics.tms.biz.sysconfig.SysConfigBiz;
import com.logistics.tms.client.model.GetVerifyCodeRequestModel;
import com.logistics.tms.controller.customeraccount.request.UpdatePhoneRequestModel;
import com.logistics.tms.controller.customeraccount.response.AccountInfoResponseModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.yelo.life.basicdata.api.base.enums.bestsign.BestSignResultEnum;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/9
 */
@Slf4j
@Service
public class BasicInfoWebBiz {

	@Autowired
	private TCompanyAuthorizationMapper tCompanyAuthorizationMapper;
	@Autowired
	private TCertificationPicturesMapper tCertificationPicturesMapper;
	@Autowired
	private TCompanyCarrierMapper tCompanyCarrierMapper;
	@Autowired
	private TRealNameAuthenticationMapper tRealNameAuthenticationMapper;
	@Autowired
	private TCustomerSmsAuthInfoMapper tCustomerSmsAuthInfoMapper;
	@Autowired
	private TCarrierContactMapper tCarrierContactMapper;
	@Autowired
	private TStaffBasicMapper tStaffBasicMapper;
	@Autowired
	private CommonBiz commonBiz;
	@Autowired
	private BasicInfoCommonBiz basicInfoCommonBiz;
	@Autowired
	private AuthenticationBiz authenticationBiz;
	@Autowired
	private DriverBasicInfoBiz driverBasicInfoBiz;
	@Autowired
	private CustomerAccountBiz customerAccountBiz;
	@Autowired
	private TCustomerAccountMapper tCustomerAccountMapper;
	@Autowired
	private SysConfigBiz sysConfigBiz;

	/**
	 * 车主前台提交授权书
	 *
	 * @param requestModel
	 * @return
	 */
	@Transactional
	public void uploadAuthorization(UploadAuthorizationRequestModel requestModel) {
		//获取当前登录的车主ID
		Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
		if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
		}
		//查询公司信息
		TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(loginUserCompanyCarrierId);
		if (tCompanyCarrier == null || IfValidEnum.INVALID.getKey().equals(tCompanyCarrier.getValid())) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
		}
		//企业车主才需要授权
		if (!CompanyTypeEnum.COMPANY.getKey().equals(tCompanyCarrier.getType())) {
			throw new BizException(CarrierDataExceptionEnum.NO_COMPANYTYPE);
		}

		//查询当前车主授权信息
		TCompanyAuthorization tCompanyAuthorization = tCompanyAuthorizationMapper.selectByCompanyIdAndType(tCompanyCarrier.getCompanyId(), CommonConstant.INTEGER_ONE);
		if (tCompanyAuthorization == null) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_AUTH_INFO_IS_NULL);
		}

		//判断授权状态
		if (!(CompanyCarrierAuthAuditStatusEnum.WAIT_AUTH.getKey().equals(tCompanyAuthorization.getAuditStatus()) ||
				CompanyCarrierAuthAuditStatusEnum.AUDIT_REJECT.getKey().equals(tCompanyAuthorization.getAuditStatus()))) {
			//如果是已驳回状态那就是重新授权
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_AUTH_STATUS_ERROR);
		}

		String userName = BaseContextHandler.getUserName();
		//授权书文件
		List<TCertificationPictures> tCertificationPicturesList = tCertificationPicturesMapper.getByObjectIdType(tCompanyAuthorization.getId(), CertificationPicturesObjectTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION.getObjectType(), CertificationPicturesFileTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION_FILE.getFileType());
		if (ListUtils.isNotEmpty(tCertificationPicturesList)) {
			//修改授权书
			TCertificationPictures tCertificationPictures = tCertificationPicturesList.get(CommonConstant.INTEGER_ZERO);//授权书只有一份
			TCertificationPictures certificationPictures = new TCertificationPictures();
			certificationPictures.setId(tCertificationPictures.getId());
			certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_CARRIER_AUTHORIZATION.getKey(), "", requestModel.getAuthorizationPath(), null));
			commonBiz.setBaseEntityModify(certificationPictures, userName);
			tCertificationPicturesMapper.updateByPrimaryKeySelective(certificationPictures);
		} else {
			//保存授权书文件
			TCertificationPictures certificationPictures = new TCertificationPictures();
			certificationPictures.setObjectId(tCompanyAuthorization.getId());
			certificationPictures.setObjectType(CertificationPicturesObjectTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION.getObjectType());
			certificationPictures.setFileType(CertificationPicturesFileTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION_FILE.getFileType());
			certificationPictures.setFileName(CertificationPicturesFileTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION_FILE.getFileName());
			certificationPictures.setFileTypeName(CertificationPicturesFileTypeEnum.T_COMPANY_CARRIER_AUTHORIZATION_FILE.getFileName());
			certificationPictures.setFilePath(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.COMPANY_CARRIER_AUTHORIZATION.getKey(), "", requestModel.getAuthorizationPath(), null));
			certificationPictures.setUploadTime(new Date());
			certificationPictures.setUploadUserName(userName);
			certificationPictures.setSuffix(requestModel.getAuthorizationPath().substring(requestModel.getAuthorizationPath().lastIndexOf(CommonConstant.POINT)));
			commonBiz.setBaseEntityAdd(certificationPictures, userName);
			tCertificationPicturesMapper.insertSelective(certificationPictures);
		}

		//更新授权状态
		TCompanyAuthorization tCompanyAuthorizationUp = new TCompanyAuthorization();
		tCompanyAuthorizationUp.setId(tCompanyAuthorization.getId());
		tCompanyAuthorizationUp.setAuditStatus(CompanyCarrierAuthAuditStatusEnum.WAIT_AUDIT.getKey());
		tCompanyAuthorizationUp.setRemark("");
		commonBiz.setBaseEntityModify(tCompanyAuthorizationUp, userName);
		tCompanyAuthorizationMapper.updateByPrimaryKeySelective(tCompanyAuthorizationUp);

		//更新车主授权状态
		TCompanyCarrier tCompanyCarrierUp = new TCompanyCarrier();
		tCompanyCarrierUp.setId(tCompanyCarrier.getId());
		tCompanyCarrierUp.setAuthorizationStatus(CompanyCarrierAuthAuditStatusEnum.WAIT_AUDIT.getKey());
		commonBiz.setBaseEntityModify(tCompanyCarrierUp, userName);
		tCompanyCarrierMapper.updateByPrimaryKeySelective(tCompanyCarrierUp);
	}

	/**
	 * 个人二要素校验
	 *
	 * @param requestModel
	 * @return
	 */
	public VerifyPersonTwoElementsResponseModel verifyPersonTwoElements(VerifyPersonTwoElementsRequestModel requestModel) {
		VerifyPersonTwoElementsResponseModel responseModel = authenticationBiz.verifyPersonTwoElements(requestModel);
		//认证通过,注册上上签账户
		if (CommonConstant.ONE.equals(responseModel.getResult())) {
			//获取当前登录的车主ID
			Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
			if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
				throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
			}
			//查询车主信息
			FuzzySearchCompanyCarrierResponseModel companyCarrierInfoById = tCompanyCarrierMapper.getCompanyCarrierInfoById(loginUserCompanyCarrierId);
			if (companyCarrierInfoById == null) {
				throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
			}
			//个人车主类型
			if (CompanyTypeEnum.PERSON.getKey().equals(companyCarrierInfoById.getCompanyType())) {
				//注册上上签账户
				BestSignUserRegModel model = new BestSignUserRegModel();
				model.setAuthenticationType(CommonConstant.INTEGER_TWO);
				model.setName(requestModel.getName());
				model.setMobile(companyCarrierInfoById.getContactPhone());
				model.setIdentityNumber(requestModel.getIdentity());
				model.setRegType(CommonConstant.INTEGER_TWO);
				basicInfoCommonBiz.bestSignUserReg(model);
			}
		} else {
			throw new BizException(CarrierDataExceptionEnum.REAL_NAME_BASIC_INFO_ERROR);
		}
		return responseModel;
	}

	/**
	 * 个人三要素校验
	 *
	 * @param requestModel
	 * @return
	 */
	@Transactional
	public VerifyPersonThreeElementsResponseModel verifyPersonThreeElements(VerifyPersonThreeElementsRequestModel requestModel) {
		VerifyPersonThreeElementsResponseModel responseModel = authenticationBiz.verifyPersonalThreeElements(requestModel);
		if (!CommonConstant.ONE.equals(responseModel.getResult())) {
			throw new BizException(CarrierDataExceptionEnum.REAL_NAME_BASIC_INFO_ERROR);
		}
		return responseModel;
	}

	/**
	 * 个人手机号认证-获取验证码
	 *
	 * @return
	 */
	@Transactional
	public void getVerifyCode(GetPersonAuthVerifyCodeRequestModel requestModel) {
		//获取当前登录的车主ID
		Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
		if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
		}
		//查询车主信息
		FuzzySearchCompanyCarrierResponseModel companyCarrierInfoById = tCompanyCarrierMapper.getCompanyCarrierInfoById(loginUserCompanyCarrierId);
		if (companyCarrierInfoById == null) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
		}
		//个人车主类型
		if (CompanyTypeEnum.PERSON.getKey().equals(companyCarrierInfoById.getCompanyType())) {
			//获取验证码
			TRealNameAuthentication tRealNameAuthentication = tRealNameAuthenticationMapper.selectRealNameAuthenticationByMobile(companyCarrierInfoById.getContactPhone());
			if (tRealNameAuthentication == null) {
				throw new BizException(CarrierDataExceptionEnum.REAL_NAME_AUTH_INFO_NOT_EXIST);
			}
			//获取验证码
			GetVerifyCodeRequestModel getVerifyCodeRequestModel = new GetVerifyCodeRequestModel();
			getVerifyCodeRequestModel.setAccount(tRealNameAuthentication.getBestsignAccount());
			getVerifyCodeRequestModel.setName(tRealNameAuthentication.getName());
			getVerifyCodeRequestModel.setMobile(requestModel.getMobile());
			getVerifyCodeRequestModel.setIdentity(tRealNameAuthentication.getIdentityNumber());
			basicInfoCommonBiz.bestSignGetVerifyCode(getVerifyCodeRequestModel);
		}
	}

	/**
	 * 车主基础信息提交
	 *
	 * @param requestModel
	 * @return
	 */
	@Transactional
	public void carrierBasicInfoSubmit(CarrierBasicInfoSubmitRequestModel requestModel) {
		//获取当前登录的车主ID
		Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
		if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
		}
		//查询个人车主信息
		FuzzySearchCompanyCarrierResponseModel companyCarrierInfoById = tCompanyCarrierMapper.getCompanyCarrierInfoById(loginUserCompanyCarrierId);
		if (companyCarrierInfoById == null) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
		}
		//个人车主类型
		if (CompanyTypeEnum.PERSON.getKey().equals(companyCarrierInfoById.getCompanyType())) {
			//未实名才能操作
			if (RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(companyCarrierInfoById.getRealNameAuthenticationStatus())) {
				throw new BizException(CarrierDataExceptionEnum.REAL_NAME_AUTH_INFO_EXIST);
			}

			//查询实名认证信息
			TRealNameAuthentication tRealNameAuthentication = tRealNameAuthenticationMapper.selectRealNameAuthenticationByMobile(companyCarrierInfoById.getContactPhone());
			if (tRealNameAuthentication == null) {
				throw new BizException(CarrierDataExceptionEnum.REAL_NAME_AUTH_INFO_NOT_EXIST);
			}

			//查询上上签短信认证记录表
			TCustomerSmsAuthInfo tCustomerSmsAuthInfo = tCustomerSmsAuthInfoMapper.verificationCode(BaseContextHandler.getUserId());
			if (tCustomerSmsAuthInfo == null) {
				throw new BizException(CarrierDataExceptionEnum.VERIFICATION_CODE_ERROR);
			}
			JSONObject responseBody = authenticationBiz.checkVerifyCode(requestModel.getVerificationCode(), tCustomerSmsAuthInfo.getPersonalIdentityKey());
			if (responseBody != null) {
				if (!BestSignResultEnum.SUCCESS.getKey().equals(responseBody.getString(BestSignCommon.RESULT))) {
					throw new BizException(CarrierDataExceptionEnum.VERIFICATION_CODE_ERROR);
				}
				TCustomerSmsAuthInfo customerSmsAuthInfo = new TCustomerSmsAuthInfo();
				customerSmsAuthInfo.setId(tCustomerSmsAuthInfo.getId());
				customerSmsAuthInfo.setWhetherUse(CommonConstant.INTEGER_ONE);
				customerSmsAuthInfo.setRemark(CommonConstant.VERIFY_CODE_USER);
				commonBiz.setBaseEntityModify(customerSmsAuthInfo, BaseContextHandler.getUserName());
				tCustomerSmsAuthInfoMapper.updateByPrimaryKeySelective(customerSmsAuthInfo);

				//车主联系人名和身份证如果和库里面的不一样就更新车主姓名和身份证信息
				updatePersonCarrierInfo(companyCarrierInfoById, requestModel.getCarrierContactIdentityFaceFile(), requestModel.getCarrierContactIdentityNationalFile(), tRealNameAuthentication);

				//查询当前个人车主是否还是司机
				TStaffBasic tStaffBasic = tStaffBasicMapper.getByMobile(companyCarrierInfoById.getContactPhone());
				if (tStaffBasic != null) {
					//更新司机信息
					driverBasicInfoBiz.updateStaffBasicInfo(tStaffBasic, requestModel.getCarrierContactIdentityFaceFile(), requestModel.getCarrierContactIdentityNationalFile(), tRealNameAuthentication);
				}

				//更新车主账号名
				TCustomerAccount tCustomerAccount = tCustomerAccountMapper.selectByMobile(companyCarrierInfoById.getContactPhone());
				if (tCustomerAccount != null) {
					TCustomerAccount tCustomerAccountUp = new TCustomerAccount();
					tCustomerAccountUp.setId(tCustomerAccount.getId());
					tCustomerAccountUp.setUserName(tRealNameAuthentication.getName());
					commonBiz.setBaseEntityModify(tCustomerAccountUp, BaseContextHandler.getUserName());
					tCustomerAccountMapper.updateByPrimaryKeySelective(tCustomerAccountUp);
				}

				//更新实名认证状态
				TRealNameAuthentication tRealNameAuthenticationUp = new TRealNameAuthentication();
				tRealNameAuthenticationUp.setId(tRealNameAuthentication.getId());
				tRealNameAuthenticationUp.setAuthMode(CommonConstant.INTEGER_ONE);
				tRealNameAuthenticationUp.setCertificationStatus(CommonConstant.INTEGER_TWO);
				commonBiz.setBaseEntityModify(tRealNameAuthenticationUp, BaseContextHandler.getUserName());
				int size = tRealNameAuthenticationMapper.updateCertificationStatus(tRealNameAuthenticationUp, RealNameAuthenticationStatusEnum.NOT_REAL_NAME.getKey());
				if (size < CommonConstant.INTEGER_ONE) {
					throw new BizException(CarrierDataExceptionEnum.REAL_NAME_AUTH_INFO_EXIST);
				}


				//如果更换了手机号,则触发更换手机号逻辑
				if (!requestModel.getCarrierContactPhone().equals(tRealNameAuthentication.getMobile())) {
					//查询登陆人账号信息
					AccountInfoResponseModel dbCustomerAccount = tCustomerAccountMapper.getAccountInfoBy(null, BaseContextHandler.getUserId(), null);
					if (dbCustomerAccount == null) {
						throw new BizException(CarrierDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
					}
					UpdatePhoneRequestModel updatePhoneRequestModel = new UpdatePhoneRequestModel();
					updatePhoneRequestModel.setUserAccount(requestModel.getCarrierContactPhone());
					updatePhoneRequestModel.setVerificationCode(requestModel.getVerificationCode());
					updatePhoneRequestModel.setRequestSource(CommonConstant.INTEGER_TWO);
					updatePhoneRequestModel.setCheckRealName(CommonConstant.BOOLFALES);
					customerAccountBiz.updateAccountPhone(updatePhoneRequestModel, dbCustomerAccount);
				}
			}
		}
	}

	/**
	 * 实名-更新个人车主信息
	 *
	 * @param companyCarrierInfoById  个人车主信息
	 * @param identityFaceFile        身份证人面图片路径
	 * @param identityNationalFile    身份证国徽面图片路径
	 * @param tRealNameAuthentication 实名认证信息
	 */
	@Transactional
	public void updatePersonCarrierInfo(FuzzySearchCompanyCarrierResponseModel companyCarrierInfoById, String identityFaceFile, String identityNationalFile, TRealNameAuthentication tRealNameAuthentication) {
		//更新个人车主信息
		TCarrierContact tCarrierContactUp = new TCarrierContact();
		tCarrierContactUp.setId(companyCarrierInfoById.getContactId());
		tCarrierContactUp.setIdentityFaceFile("");
		tCarrierContactUp.setIdentityFaceFileIsAmend(CompanyCertificateIsAmendEnum.CERTIFICATE_AMEND.getKey());
		tCarrierContactUp.setIdentityNationalFile("");
		tCarrierContactUp.setIdentityNationalFileIsAmend(CompanyCertificateIsAmendEnum.CERTIFICATE_AMEND.getKey());
		if (!companyCarrierInfoById.getContactName().equals(tRealNameAuthentication.getName())) {
			tCarrierContactUp.setContactName(tRealNameAuthentication.getName());
		}
		if (!companyCarrierInfoById.getIdentityNumber().equals(tRealNameAuthentication.getIdentityNumber())) {
			tCarrierContactUp.setIdentityNumber(tRealNameAuthentication.getIdentityNumber());
		}
		if (StringUtils.isNotBlank(identityFaceFile)) {
			tCarrierContactUp.setIdentityFaceFile(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CUSTOMER_CARRIER_IDENTITY_FACE.getKey(), "", identityFaceFile, null));
			tCarrierContactUp.setIdentityFaceFileIsAmend(CompanyCertificateIsAmendEnum.CERTIFICATE_NO_AMEND.getKey());
		}
		if (StringUtils.isNotBlank(identityNationalFile)) {
			tCarrierContactUp.setIdentityNationalFile(commonBiz.copyFileToDirectoryOfType(CopyFileTypeEnum.CUSTOMER_CARRIER_IDENTITY_NATIONAL.getKey(), "", identityNationalFile, null));
			tCarrierContactUp.setIdentityNationalFileIsAmend(CompanyCertificateIsAmendEnum.CERTIFICATE_NO_AMEND.getKey());
		}
		commonBiz.setBaseEntityModify(tCarrierContactUp, BaseContextHandler.getUserName());
		tCarrierContactMapper.updateByPrimaryKeySelectiveEncrypt(tCarrierContactUp);

		//更新车主实名认证状态
		TCompanyCarrier tCompanyCarrierUp = new TCompanyCarrier();
		tCompanyCarrierUp.setId(companyCarrierInfoById.getCompanyId());
		tCompanyCarrierUp.setRealNameAuthenticationStatus(RealNameAuthenticationStatusEnum.REAL_NAME.getKey());
		commonBiz.setBaseEntityModify(tCompanyCarrierUp, BaseContextHandler.getUserName());
		tCompanyCarrierMapper.updateByPrimaryKeySelective(tCompanyCarrierUp);
	}

	/**
	 * 车主基础信息查询
	 *
	 * @return CarrierBasicInfoResponseModel
	 */
	public CarrierBasicInfoResponseModel carrierBasicInfo() {
		return carrierBasicInfo(getLoginUserCompanyCarrierId());
	}

	/**
	 * 根据车主ID查询基础信息查询
	 * @param companyCarrierId
	 * @return CarrierBasicInfoResponseModel
	 */
	public CarrierBasicInfoResponseModel carrierBasicInfo(Long companyCarrierId) {

		// 查询车主信息
		CarrierBasicInfoResponseModel carrierBasicInfo = tCompanyCarrierMapper.selectCarrierBasicInfo(companyCarrierId);

		// 公司类型 授权处理
		if (CompanyTypeEnum.COMPANY.getKey().equals(carrierBasicInfo.getCompanyCarrierType())) {
			// 驳回状态 获取驳回原因
			if (CompanyCarrierAuthAuditStatusEnum.AUDIT_REJECT.getKey().equals(carrierBasicInfo.getAuthorizationStatus())) {
				// 查询授权驳回原因
				TCompanyAuthorization companyAuthorization = tCompanyAuthorizationMapper.selectByCompanyIdAndType(carrierBasicInfo.getCompanyId(), CommonConstant.INTEGER_ONE);
				if (companyAuthorization == null) {
					throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_AUTH_INFO_IS_NULL);
				}
				carrierBasicInfo.setRemark(companyAuthorization.getRemark());
			}
			// 查询授权书邮寄地址
			ConfigKeyEnum addressConfigEnum = ConfigKeyEnum.AUTHORIZATION_RETURN_ADDRESS_CONFIG;
			var mailingInfoDetail = sysConfigBiz.getSysConfig(addressConfigEnum.getGroupCode(), addressConfigEnum.getValue(), MailingInfoDetailResponseModel.class);
			String address = Optional.ofNullable(mailingInfoDetail)
					.map(s -> {
						return String.join(" ",
								mailingInfoDetail.getAddress(),
								mailingInfoDetail.getAddressee(),
								mailingInfoDetail.getAddresseeMobile());
					})
					.orElse("");
			carrierBasicInfo.setMailingAddress(address);
		}
		// 个人类型 实名认证处理
		else {
			if (RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(carrierBasicInfo.getRealNameAuthenticationStatus())
					&& StringUtils.isNotBlank(carrierBasicInfo.getCarrierContactPhone())) {
				// 查询认证方式
				TRealNameAuthentication realNameAuthentication = tRealNameAuthenticationMapper.selectRealNameAuthenticationByMobile(carrierBasicInfo.getCarrierContactPhone());
				if (Objects.isNull(realNameAuthentication)) {
					throw new BizException(CarrierDataExceptionEnum.REAL_NAME_AUTH_INFO_NOT_EXIST);
				}
				carrierBasicInfo.setAuthMode(realNameAuthentication.getAuthMode());
			}
		}
		return carrierBasicInfo;
	}

	// 获取车主Id
	private Long getLoginUserCompanyCarrierId() {
		Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
		if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
		}
		return loginUserCompanyCarrierId;
	}

	/**
	 * 校验车主是否实名认证或授权
	 */
	public void checkCarrierRealNameOrAuthorization() {
		checkCarrierRealNameOrAuthorization(getLoginUserCompanyCarrierId());
	}

	/**
	 * 校验车主是否实名认证或授权
	 * @param companyCarrierId
	 */
	public void checkCarrierRealNameOrAuthorization(Long companyCarrierId) {
		CarrierBasicInfoResponseModel carrierBasicInfo = carrierBasicInfo(companyCarrierId);
		if (CompanyTypeEnum.COMPANY.getKey().equals(carrierBasicInfo.getCompanyCarrierType())) {
			if (!CompanyCarrierAuthAuditStatusEnum.BE_AUTH.getKey().equals(carrierBasicInfo.getAuthorizationStatus())) {
				throw new BizException(CarrierDataExceptionEnum.CHECK_AUTHORIZATION);
			}
		} else {
			if (!RealNameAuthenticationStatusEnum.REAL_NAME.getKey().equals(carrierBasicInfo.getRealNameAuthenticationStatus())) {
				throw new BizException(CarrierDataExceptionEnum.CHECK_REAL_NAME);
			}
		}
	}

	/**
	 * 下载车主授权书
	 */
	public DownloadAuthTemplateResponseModel downloadAuthTemplate() {
		DownloadAuthTemplateResponseModel responseModel = new DownloadAuthTemplateResponseModel();
		//获取当前登录的车主ID
		Long loginUserCompanyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
		if (loginUserCompanyCarrierId == null || CommonConstant.LONG_ZERO.equals(loginUserCompanyCarrierId)) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
		}
		CarrierBasicInfoResponseModel carrierBasicInfo = tCompanyCarrierMapper.selectCarrierBasicInfo(loginUserCompanyCarrierId);
		if (carrierBasicInfo == null) {
			throw new BizException(CarrierDataExceptionEnum.COMPANY_CARRIER_IS_EMPTY);
		}
		if (StringUtils.isBlank(carrierBasicInfo.getCompanyCarrierName())){//只有企业车主允许操作（公司名称为null，api下载授权书替换模板内容报错）
			throw new BizException(CarrierDataExceptionEnum.NO_PERMISSION);
		}

		responseModel.setCompanyCarrierName(carrierBasicInfo.getCompanyCarrierName());
		responseModel.setCompanyCarrierCreateTime(carrierBasicInfo.getCompanyCarrierCreateTime());
		return responseModel;
	}
}
