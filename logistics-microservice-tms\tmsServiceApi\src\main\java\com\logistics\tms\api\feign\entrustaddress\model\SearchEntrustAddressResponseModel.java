package com.logistics.tms.api.feign.entrustaddress.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2018/12/26 15:47
 */
@Data
public class SearchEntrustAddressResponseModel {
    @ApiModelProperty("货主类型 1企业 2 个人")
    private Integer type;
    @ApiModelProperty("地址id")
    private Long entrustAddressId;
    @ApiModelProperty("货主公司名称")
    private String companyEntrustName;
    @ApiModelProperty("购货公司名称")
    private String companyName;
    @ApiModelProperty("仓库")
    private String warehouse;
    @ApiModelProperty("地址")
    private String provinceName;
    private String cityName;
    private String areaName;
    private String detailAddress;
    @ApiModelProperty("联系人")
    private String contactName;
    @ApiModelProperty("联系方式")
    private String contactMobile;
    @ApiModelProperty("最后操作人")
    private String lastModifiedBy;
    @ApiModelProperty("最后操作时间")
    private Date lastModifiedTime;
    @ApiModelProperty("添加账号")
    private String addUserPhone;

    private Long companyId;//公司主表id

    @ApiModelProperty(value = "创建人")
    private String createdBy;
}
