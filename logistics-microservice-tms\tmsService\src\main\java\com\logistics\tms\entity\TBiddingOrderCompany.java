package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2024/06/04
*/
@Data
public class TBiddingOrderCompany extends BaseEntity {
    /**
    * 竞价单id
    */
    @ApiModelProperty("竞价单id")
    private Long biddingOrderId;

    /**
    * 车主ID
    */
    @ApiModelProperty("车主ID")
    private Long companyCarrierId;

    /**
    * 1.有效,0.无效
    */
    @ApiModelProperty("1.有效,0.无效")
    private Integer valid;
}