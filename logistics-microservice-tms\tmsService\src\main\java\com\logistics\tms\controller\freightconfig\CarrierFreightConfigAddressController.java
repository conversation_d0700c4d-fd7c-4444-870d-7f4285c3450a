package com.logistics.tms.controller.freightconfig;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.carrierfreight.CarrierFreightConfigAddressBiz;
import com.logistics.tms.controller.freightconfig.request.address.*;
import com.logistics.tms.controller.freightconfig.response.address.CarrierFreightConfigAddressDetailResponseModel;
import com.logistics.tms.controller.freightconfig.response.address.CarrierFreightConfigAddressListResponseModel;
import com.logistics.tms.controller.freightconfig.response.address.CarrierFreightConfigAddressLogsResponseModel;
import com.logistics.tms.controller.freightconfig.response.address.SearchCarrierFreightConfigResponseModel;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 路线计价配置管理
 */
@RestController
@Api(tags = "路线计价配置管理")
@RequestMapping(value = "/service/freight/config/address")
public class CarrierFreightConfigAddressController {

    @Autowired
    private CarrierFreightConfigAddressBiz carrierFreightConfigAddressBiz;

    @PostMapping(value = "/searchList")
    @ApiOperation(value = "路线计价配置列表")
    public Result<CarrierFreightConfigAddressListResponseModel> searchList(@RequestBody CarrierFreightConfigAddressListRequestModel requestModel) {
        return Result.success(carrierFreightConfigAddressBiz.searchConfigAddressList(requestModel));
    }

    @PostMapping(value = "/detail")
    @ApiOperation(value = "路线计价配置查看")
    public Result<CarrierFreightConfigAddressDetailResponseModel> detail(@RequestBody CarrierFreightAddressDetailRequestModel requestModel) {
        return Result.success(carrierFreightConfigAddressBiz.configAddressDetail(requestModel));
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "新增路线计价配置")
    public Result<Boolean> add(@RequestBody CarrierFreightConfigAddressAddRequestModel requestModel) {
        carrierFreightConfigAddressBiz.addAddressConfig(requestModel);
        return Result.success(true);
    }

    @PostMapping(value = "/edit")
    @ApiOperation(value = "编辑路线计价配置")
    public Result<Boolean> edit(@RequestBody CarrierFreightAddressEditEnableRequestModel requestModel) {
        carrierFreightConfigAddressBiz.editAddressConfig(requestModel);
        return Result.success(true);
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "删除路线计价配置")
    public Result<Boolean> delete(@RequestBody CarrierFreightAddressDeleteRequestModel requestModel) {
        carrierFreightConfigAddressBiz.deleteConfigAddress(requestModel);
        return Result.success(true);
    }

    @PostMapping(value = "/enable")
    @ApiOperation(value = "启用禁用路线计价配置")
    public Result<Boolean> enable(@RequestBody CarrierFreightAddressEnableRequestModel requestModel) {
        carrierFreightConfigAddressBiz.enableConfigAddress(requestModel);
        return Result.success(true);
    }

    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    @PostMapping(value = "/export")
    @ApiOperation(value = "路线计价配置列表导出")
    public Result<CarrierFreightConfigAddressListResponseModel> export(@RequestBody CarrierFreightConfigAddressListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return Result.success(carrierFreightConfigAddressBiz.searchConfigAddressList(requestModel));
    }

    @ApiOperation(value = "查询车主运价信息")
    @PostMapping(value = "/getCarrierFreight")
    public Result<SearchCarrierFreightConfigResponseModel> getCarrierFreight(@RequestBody SearchCarrierFreightConfigRequestModel requestModel) {
        SearchCarrierFreightConfigResponseModel responseModel = carrierFreightConfigAddressBiz.getCarrierFreight(requestModel);
        return Result.success(responseModel);
    }

    @ApiOperation(value = "车主运价细则日志")
    @PostMapping(value = "/getAddressRuleLogs")
    public Result<List<CarrierFreightConfigAddressLogsResponseModel>> getAddressRuleLogs(@RequestBody CarrierFreightAddressDetailRequestModel responseModel) {
        return Result.success(carrierFreightConfigAddressBiz.getAddressRuleLogs(responseModel));
    }
}
