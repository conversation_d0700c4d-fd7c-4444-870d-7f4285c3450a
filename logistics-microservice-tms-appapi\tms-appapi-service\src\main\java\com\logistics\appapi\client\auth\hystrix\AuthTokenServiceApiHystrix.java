package com.logistics.appapi.client.auth.hystrix;

import com.logistics.appapi.client.auth.AuthTokenServiceApi;
import com.logistics.appapi.client.auth.request.CreateToken;
import com.logistics.appapi.client.auth.response.TokenModule;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2023/12/25 13:38
 */
@Component
public class AuthTokenServiceApiHystrix implements AuthTokenServiceApi {
    @Override
    public Result<TokenModule> createToken(CreateToken requestModel) {
        return Result.timeout();
    }
}
