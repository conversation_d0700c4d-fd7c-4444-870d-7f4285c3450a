package com.logistics.appapi.controller.attendance.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 根据经纬度获取地址请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/23
 */
@Data
public class QueryPathByLonAndLatRequestDto {

	//经度
	@ApiModelProperty(value = "经度", required = true)
	@NotBlank(message = "定位信息是必须的")
	private String lon;

	//维度
	@ApiModelProperty(value = "维度", required = true)
	@NotBlank(message = "定位信息是必须的")
	private String lat;
}
