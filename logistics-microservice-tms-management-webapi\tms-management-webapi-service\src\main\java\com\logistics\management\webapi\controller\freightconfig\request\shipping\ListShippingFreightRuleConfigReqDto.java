package com.logistics.management.webapi.controller.freightconfig.request.shipping;

import com.yelo.tray.core.page.AbstractPageForm;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class ListShippingFreightRuleConfigReqDto extends AbstractPageForm<ListShippingFreightRuleConfigReqDto> {


    /**
     * 零担运价id
     */
    private String shippingFreightId = "";

    /**
     * 主键ids 用于勾选导出
     */
    private List<String> shippingFreightRuleIds ;



    /**
     * 发货地
     */
    private String loadAddress = "";


    /**
     * 收货地
     */
    private String unloadAddress = "";


}
