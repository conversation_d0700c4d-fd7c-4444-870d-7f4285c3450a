package com.logistics.appapi.client.workordercenter.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/4/20
 */
@Data
public class WorkOrderListAppletResponseModel {

    @ApiModelProperty("工单id")
    private Long workOrderId;

    @ApiModelProperty("状态：0 待处理，10 处理中，20 已处理，30 已关闭，40 已撤销")
    private Integer status;

    //运单id
    private Long carrierOrderId;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("提货省")
    private String loadProvinceName;

    @ApiModelProperty("提货市")
    private String loadCityName;

    @ApiModelProperty("提货区")
    private String loadAreaName;

    @ApiModelProperty("提货详细地址")
    private String loadDetailAddress;

    @ApiModelProperty("提货仓库")
    private String loadWarehouse;

    @ApiModelProperty("提货地址联系人")
    private String loadPerson;

    @ApiModelProperty("提货地址联系人联系方式")
    private String loadMobile;

    @ApiModelProperty("提报来源：1 后台，2 前台，3 小程序")
    private Integer reportSource;
}
