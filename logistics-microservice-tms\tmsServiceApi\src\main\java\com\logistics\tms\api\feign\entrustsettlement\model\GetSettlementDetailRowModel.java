package com.logistics.tms.api.feign.entrustsettlement.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2019/10/11 18:58
 */
@Data
public class GetSettlementDetailRowModel {
    @ApiModelProperty("委托方公司")
    private String companyEntrust;
    @ApiModelProperty("单位")
    private Integer goodsUnit;
    @ApiModelProperty("结算数据")
    private BigDecimal settlementAmount;
    @ApiModelProperty("结算金额")
    private BigDecimal settlementCostTotal;
}
