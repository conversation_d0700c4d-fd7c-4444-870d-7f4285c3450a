package com.logistics.tms.controller.demandorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * liang current user system login name
 * 2018/9/25 current system date
 */
@Data
public class DemandOrderSearchRequestModel extends AbstractPageForm<DemandOrderSearchRequestModel> {
    @ApiModelProperty("需求单状态委托单状态：空 全部 500待发布 1000待调度 2000部分调度 3000调度完成 4000待签收 5000已签收 1取消")
    private Integer demandStatus;
    @ApiModelProperty(value ="货主ID集合")
    private String companyEntrustIds;
    @ApiModelProperty(value = "车主ID集合")
    private String companyCarrierIds;
    @ApiModelProperty(value = "需求单号")
    private String demandOrderCode;
    @ApiModelProperty(value = "客户的单号")
    private String customerOrderCode;
    @ApiModelProperty(value = "期望提货时间开始")
    private String expectedLoadTimeStart;
    @ApiModelProperty(value = "期望提货时间结束")
    private String expectedLoadTimeEnd;
    @ApiModelProperty(value = "期望到货时间开始")
    private String expectedUnloadTimeStart;
    @ApiModelProperty(value = "期望到货时间结束")
    private String expectedUnloadTimeEnd;
    @ApiModelProperty(value = "下单时间开始")
    private String publishTimeStart;
    @ApiModelProperty(value = "下单时间结束")
    private String publishTimeEnd;
    @ApiModelProperty(value = "凭证日期开始")
    private String ticketDateFrom;
    @ApiModelProperty(value = "凭证日期结束")
    private String ticketDateTo;
    @ApiModelProperty(value = "发货地址")
    private String loadDetailAddress;
    @ApiModelProperty(value = "发货人")
    private String consignorMobile;
    @ApiModelProperty(value = "收货地址")
    private String unloadDetailAddress;
    @ApiModelProperty(value = "收货人")
    private String receiverName;
    @ApiModelProperty("委托人")
    private String customerEntrustName;
    @ApiModelProperty("车主名")
    private String companyCarrierName;
    private Integer entrustType;
    private String showIfCancelStatus;
    private String consignorMobileAndReceiver;
    @ApiModelProperty("拼单助手需求单号")
    private List<String> demandOrderCodeList;
    @ApiModelProperty("拼单助手客户单号")
    private List<String> customerOrderCodeList;
    @ApiModelProperty("是否异常：0 否，1 是")
    private Integer ifObjection;
    @ApiModelProperty("下单类型：10 发布，20 拉取，21 推送")
    private Integer orderType;

    private String sort;
    private String order;

    @ApiModelProperty(value = "选择性导出，传入选择的ids,多个逗号分隔")
    private String demandIds;

}
