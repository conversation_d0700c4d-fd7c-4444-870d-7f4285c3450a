package com.logistics.management.webapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author:lei.zhu
 * @date:2021/4/9 11:43
 */
@Data
public class CancelVehicleSettlementDetailResponseDto {
    @ApiModelProperty("车辆运费账单id")
    private String vehicleSettlementId="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("账单月")
    private String settlementMonth="";
    @ApiModelProperty("实付运费")
    private String actualExpensesPayable="";
    @ApiModelProperty("司机  姓名+手机号")
    private String driverName="";
    @ApiModelProperty("司机确认状态 司机确认状态：-1 未操作，0 无需确认，1 确认，2 驳回")
    private String driverStatus="";

    @ApiModelProperty("无需确认/撤回理由")
    private String reason="";
    @ApiModelProperty("司机确认绝对路径")
    private String commitImageUrlSrc="";
}
