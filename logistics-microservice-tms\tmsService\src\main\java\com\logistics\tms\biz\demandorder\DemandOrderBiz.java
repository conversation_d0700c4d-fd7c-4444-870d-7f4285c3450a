package com.logistics.tms.biz.demandorder;

import com.github.pagehelper.PageInfo;
import com.logistics.entrust.api.feign.demandorder.model.CancelDemandOrderFromTmsLeYiRequestModel;
import com.logistics.entrust.api.feign.demandorder.model.CancelDemandOrderVerifyFromTmsLeYiRequestModel;
import com.logistics.tms.api.feign.entrustaddress.model.AddOrModifyEntrustAddressRequestModel;
import com.logistics.tms.api.feign.freight.model.GetPriceByAddressAndAmountRequestModel;
import com.logistics.tms.api.feign.freight.model.GetPriceByAddressAndAmountResponseModel;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.base.utils.LocalStringUtil;
import com.logistics.tms.base.utils.RegExpValidatorUtil;
import com.logistics.tms.biz.carrierorder.CarrierOrderBiz;
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz;
import com.logistics.tms.biz.carrierorder.model.CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel;
import com.logistics.tms.biz.carrierorder.model.CancelCarrierOrderImpactDemandOrderModel;
import com.logistics.tms.biz.carrierorder.model.CarrierOrderSynchronizeModel;
import com.logistics.tms.biz.carrierorder.model.ExternalCancelCarrierOrderImpactDemandOrderModel;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.demandorder.model.*;
import com.logistics.tms.biz.dispatch.model.DriverByVehiclesModel;
import com.logistics.tms.biz.freight.FreightBiz;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupEventModel;
import com.logistics.tms.biz.workgroup.sendmsg.model.WorkGroupPushBoModel;
import com.logistics.tms.biz.workordercenter.WorkOrderBiz;
//import com.logistics.tms.client.EntrustClient;
import com.logistics.tms.client.model.RecyclePublishUpdateDemandRequestModel;
import com.logistics.tms.controller.carrierorder.request.DemandOrderIdsRequestModel;
import com.logistics.tms.controller.carrierorder.request.GetCarrierOrdersByDemandIdRequestModel;
import com.logistics.tms.controller.carrierorder.response.*;
import com.logistics.tms.controller.demandorder.request.*;
import com.logistics.tms.controller.demandorder.response.*;
import com.logistics.tms.controller.dispatch.response.VehicleSearchResponseModel;
import com.logistics.tms.entity.*;
import com.logistics.tms.mapper.*;
import com.logistics.tms.rabbitmq.consumer.model.SyncLogisticsWithdrawOrderModel;
import com.logistics.tms.rabbitmq.consumer.model.WithdrawDemandRelModel;
import com.logistics.tms.rabbitmq.publisher.RabbitMqPublishBiz;
import com.logistics.tms.rabbitmq.publisher.model.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @author: wjf
 * @date: 2019/9/11 19:47
 */
@Slf4j
@Service
public class DemandOrderBiz {

    @Autowired
    private TDemandOrderMapper demandOrderMapper;
    @Autowired
    private TDemandOrderAddressMapper demandOrderAddressMapper;
    @Autowired
    private TDemandOrderGoodsMapper demandOrderGoodsMapper;
    @Autowired
    private TDemandOrderEventsMapper demandOrderEventsMapper;
    @Autowired
    private TDemandOrderOperateLogsMapper demandOrderOperateLogsMapper;
    @Autowired
    private TDemandOrderOrderRelMapper tDemandOrderOrderRelMapper;
    @Autowired
    private TCompanyEntrustMapper companyEntrustMapper;
    @Autowired
    private TEntrustAddressMapper entrustAddressMapper;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private CarrierOrderBiz carrierOrderBiz;
    @Autowired
    private TDemandPaymentMapper tDemandPaymentMapper;
    @Autowired
    private TDemandReceivementMapper tDemandReceivementMapper;
    @Autowired
    private CarrierOrderCommonBiz carrierOrderCommonBiz;
    @Autowired
    private FreightBiz freightBiz;
    @Autowired
    private TDemandOrderGoodsRelMapper  tDemandOrderGoodsRelMapper;
    @Autowired
    private DemandOrderCommonBiz demandOrderCommonBiz;
    @Autowired
    private TDemandOrderObjectionMapper tDemandOrderObjectionMapper;
    @Autowired
    private TDemandOrderCarrierMapper demandOrderCarrierMapper;
    @Autowired
    private TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper;
    @Autowired
    private TDemandOrderCarrierMapper tDemandOrderCarrierMapper;
    @Autowired
    private TCarrierOrderMapper tCarrierOrderMapper;
    @Autowired
    private TDemandOrderMapper tDemandOrderMapper;
    @Autowired
    private TCompanyCarrierMapper tCompanyCarrierMapper;
    @Autowired
    private TDemandOrderEventsMapper tDemandOrderEventsMapper;
    @Autowired
    private TReceivementMapper tReceivementMapper;
    @Autowired
    private TPaymentMapper tPaymentMapper;
    @Autowired
    private TStaffBasicMapper tStaffBasicMapper;
    @Autowired
    private TVehicleBasicMapper tVehicleBasicMapper;
//    @Autowired
//    private EntrustClient entrustClient;
    @Autowired
    private WorkOrderBiz workOrderBiz;
    @Autowired
    private RabbitMqPublishBiz rabbitMqPublishBiz;
    @Resource
    private ApplicationContext applicationContext;

    /**
     * 委托发布
     *
     * @param requestModel
     */
    @Transactional
    public void saveDemandOrder(SaveDemandOrderRequestModel requestModel) {
        //判断发单货主是否存在
        TCompanyEntrust companyEntrust = companyEntrustMapper.selectByPrimaryKey(requestModel.getCompanyEntrustId());
        if (companyEntrust == null) {
            throw new BizException(CarrierDataExceptionEnum.COMPANY_ENTRUST_EMPTY);
        }
        //判断是否是乐医发布
        Long companyEntrustId = CommonConstant.LONG_ZERO;
        String leyiCompanyName = commonBiz.getLeyiCompanyName();
        if(StringUtils.isNotBlank(leyiCompanyName)) {
            TCompanyEntrust tCompanyEntrust = companyEntrustMapper.getByName(leyiCompanyName);
            if (tCompanyEntrust != null) {
                companyEntrustId = tCompanyEntrust.getId();
            }
        }

        //查询我司或者其他车主信息
        CompanyCarrierByIdModel companyCarrierByIdModel = demandOrderCommonBiz.companyCarrierInfoByIsOurCompany(requestModel.getIsOurCompany(), requestModel.getCompanyCarrierId());

        //是乐医集团发的委托单
        boolean ifLeyi = companyEntrustId.equals(requestModel.getCompanyEntrustId());
        //判断是否是乐医按体积发布
        Integer lastGoodsUnit = null;
        for (SaveDemandOrderGoodsRequestModel goodsRequestModel : requestModel.getDemandOrderGoodsList()) {
            Integer thisGoodsUnit;
            if (ifLeyi && GoodsUnitEnum.BY_PACKAGE.getKey().equals(goodsRequestModel.getGoodsUnit()) && RegExpValidatorUtil.isTraySpecification(goodsRequestModel.getGoodsSize())) {
                thisGoodsUnit = GoodsUnitEnum.BY_VOLUME.getKey();
            } else {
                thisGoodsUnit = goodsRequestModel.getGoodsUnit();
            }
            if (lastGoodsUnit == null) {
                lastGoodsUnit = thisGoodsUnit;
            } else if (!lastGoodsUnit.equals(thisGoodsUnit)) {
                throw new BizException(CarrierDataExceptionEnum.DEMAND_ORDER_GOODS_UNIT_DIFFERENT);
            }
        }
        boolean ifTray = ifLeyi && GoodsUnitEnum.BY_VOLUME.getKey().equals(lastGoodsUnit);

        BigDecimal goodsAmount = BigDecimal.ZERO;
        for (SaveDemandOrderGoodsRequestModel goods : requestModel.getDemandOrderGoodsList()) {
            goodsAmount = goodsAmount.add(goods.getGoodsAmount());
        }
        String remark = requestModel.getRemark();
        String userName = BaseContextHandler.getUserName();
        Date now = new Date();
        Integer demandOrderStatus = DemandOrderStatusEnum.WAIT_DISPATCH.getKey();
        Integer goodsUnit = requestModel.getDemandOrderGoodsList().get(0).getGoodsUnit();

        //生成需求单
        TDemandOrder demandOrder = new TDemandOrder();
        demandOrder.setEntrustStatus(demandOrderStatus);
        demandOrder.setStatusUpdateTime(now);
        demandOrder.setStatus(demandOrderStatus);
        demandOrder.setDemandOrderCode(commonBiz.getBusinessTypeCode(BusinessCodeTypeEnum.DEMAND_ORDER_CODE, "", userName));
        demandOrder.setCustomerOrderCode(requestModel.getCustomerOrderCode());
        demandOrder.setPublishName(userName);
        demandOrder.setPublishTime(now);
        demandOrder.setSettlementTonnage(companyEntrust.getSettlementTonnage());
        demandOrder.setGoodsAmount(goodsAmount);
        demandOrder.setNotArrangedAmount(goodsAmount);
        demandOrder.setExpectContractPriceType(requestModel.getContractPriceType());
        demandOrder.setExpectContractPrice(requestModel.getContractPrice());
        demandOrder.setGoodsUnit(goodsUnit);
        demandOrder.setCompanyEntrustId(requestModel.getCompanyEntrustId());
        demandOrder.setCompanyEntrustName(requestModel.getCompanyEntrustName());
        demandOrder.setRemark(remark);
        demandOrder.setSource(DemandOrderSourceEnum.PUBLISH.getKey());

        //设置车主信息
        demandOrder.setCompanyCarrierType(companyCarrierByIdModel.getCompanyCarrierType());
        demandOrder.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
        demandOrder.setCompanyCarrierName(companyCarrierByIdModel.getCompanyCarrierName());
        demandOrder.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
        demandOrder.setCarrierContactName(companyCarrierByIdModel.getCarrierContactName());
        demandOrder.setCarrierContactPhone(companyCarrierByIdModel.getCarrierContactPhone());
        demandOrder.setCompanyCarrierLevel(companyCarrierByIdModel.getCompanyCarrierLevel());
        demandOrder.setCarrierPriceType(requestModel.getCarrierPriceType());
        demandOrder.setCarrierPrice(requestModel.getCarrierPrice());
        //是否乐医
        if (ifTray) {
            demandOrder.setGoodsUnit(GoodsUnitEnum.BY_VOLUME.getKey());
            demandOrder.setEntrustType(EntrustTypeEnum.LEYI_PUBLISH.getKey());
        }

        //匹配货主运价规则
        GetPriceByAddressAndAmountResponseModel dbFreightModel = processActualFee(requestModel, goodsAmount);
        if (dbFreightModel != null) {
            demandOrder.setContractPrice(dbFreightModel.getFreightFee());
            demandOrder.setContractPriceType(dbFreightModel.getFreightType());
        } else {
            demandOrder.setContractPrice(BigDecimal.ZERO);
            demandOrder.setContractPriceType(CommonConstant.INTEGER_ZERO);
        }

        commonBiz.setBaseEntityAdd(demandOrder, userName);
        demandOrderMapper.insertSelectiveEncrypt(demandOrder);
        //生成需求单地址
        TDemandOrderAddress demandOrderAddress = new TDemandOrderAddress();
        demandOrderAddress.setDemandOrderId(demandOrder.getId());
        demandOrderAddress.setLoadProvinceId(requestModel.getLoadProvinceId());
        demandOrderAddress.setLoadProvinceName(requestModel.getLoadProvinceName());
        demandOrderAddress.setLoadCityId(requestModel.getLoadCityId());
        demandOrderAddress.setLoadCityName(requestModel.getLoadCityName());
        demandOrderAddress.setLoadAreaId(requestModel.getLoadAreaId());
        demandOrderAddress.setLoadAreaName(requestModel.getLoadAreaName());
        demandOrderAddress.setLoadDetailAddress(requestModel.getLoadDetailAddress());
        demandOrderAddress.setLoadWarehouse(requestModel.getLoadWarehouse());
        demandOrderAddress.setConsignorName(requestModel.getLoadContactName());
        demandOrderAddress.setConsignorMobile(requestModel.getLoadContactMobile());
        demandOrderAddress.setExpectedLoadTime(requestModel.getExpectedLoadTime());
        demandOrderAddress.setLoadCompany(requestModel.getCompanyEntrustName());

        demandOrderAddress.setUnloadProvinceId(requestModel.getUnloadProvinceId());
        demandOrderAddress.setUnloadProvinceName(requestModel.getUnloadProvinceName());
        demandOrderAddress.setUnloadCityId(requestModel.getUnloadCityId());
        demandOrderAddress.setUnloadCityName(requestModel.getUnloadCityName());
        demandOrderAddress.setUnloadAreaId(requestModel.getUnloadAreaId());
        demandOrderAddress.setUnloadAreaName(requestModel.getUnloadAreaName());
        demandOrderAddress.setUnloadDetailAddress(requestModel.getUnloadDetailAddress());
        demandOrderAddress.setUnloadWarehouse(requestModel.getUnloadWarehouse());
        demandOrderAddress.setReceiverName(requestModel.getUnloadContactName());
        demandOrderAddress.setReceiverMobile(requestModel.getUnloadContactMobile());
        demandOrderAddress.setExpectedUnloadTime(requestModel.getExpectedUnloadTime());
        demandOrderAddress.setUnloadCompany(requestModel.getUnloadCompanyName());
        commonBiz.setBaseEntityAdd(demandOrderAddress, userName);
        demandOrderAddressMapper.insertSelective(demandOrderAddress);

        //生成货物
        TDemandOrderGoods demandOrderGoods;
        List<TDemandOrderGoods> addGoodsList = new ArrayList<>();
        for (SaveDemandOrderGoodsRequestModel goodsRequestModel : requestModel.getDemandOrderGoodsList()) {
            demandOrderGoods = new TDemandOrderGoods();
            if (ifTray) {
                String[] sizes = goodsRequestModel.getGoodsSize().replace("mm", "").split("\\*");
                demandOrderGoods.setLength(ConverterUtils.toInteger(sizes[0]));
                demandOrderGoods.setWidth(ConverterUtils.toInteger(sizes[1]));
                demandOrderGoods.setHeight(ConverterUtils.toInteger(sizes[2]));
                demandOrderGoods.setGoodsSize("");
            } else {
                demandOrderGoods.setGoodsSize(goodsRequestModel.getGoodsSize());
            }
            demandOrderGoods.setDemandOrderId(demandOrder.getId());
            demandOrderGoods.setGoodsName(goodsRequestModel.getGoodsName());
            demandOrderGoods.setGoodsAmount(goodsRequestModel.getGoodsAmount());
            demandOrderGoods.setNotArrangedAmount(goodsRequestModel.getGoodsAmount());
            commonBiz.setBaseEntityAdd(demandOrderGoods, userName);
            addGoodsList.add(demandOrderGoods);
        }
        if (ListUtils.isNotEmpty(addGoodsList)) {
            demandOrderGoodsMapper.batchInsert(addGoodsList);
        }
        //生成需求单日志
        TDemandOrderOperateLogs demandOrderOperateLogs = new TDemandOrderOperateLogs();
        demandOrderOperateLogs.setDemandOrderId(demandOrder.getId());
        demandOrderOperateLogs.setOperationType(DemandOrderOperateLogsEnum.CREATE_DEMAND_ORDER.getKey());
        demandOrderOperateLogs.setOperationContent(DemandOrderOperateLogsEnum.CREATE_DEMAND_ORDER.getValue());
        demandOrderOperateLogs.setRemark(remark);
        demandOrderOperateLogs.setOperatorName(userName);
        demandOrderOperateLogs.setOperateTime(now);
        commonBiz.setBaseEntityAdd(demandOrderOperateLogs, userName);
        demandOrderOperateLogsMapper.insertSelective(demandOrderOperateLogs);
        //生成需求单事件
        TDemandOrderEvents demandOrderEvents = new TDemandOrderEvents();
        demandOrderEvents.setDemandOrderId(demandOrder.getId());
        demandOrderEvents.setCompanyCarrierId(demandOrder.getCompanyCarrierId());
        demandOrderEvents.setEvent(DemandOrderEventsTypeEnum.ACCEPT_CARRIERORDER.getKey());
        demandOrderEvents.setEventDesc(DemandOrderEventsTypeEnum.ACCEPT_CARRIERORDER.getValue());
        demandOrderEvents.setEventTime(now);
        demandOrderEvents.setOperatorName(userName);
        demandOrderEvents.setOperateTime(now);
        commonBiz.setBaseEntityAdd(demandOrderEvents, userName);
        demandOrderEventsMapper.insertSelective(demandOrderEvents);

        //新增记录到需求单车主变更表
        TDemandOrderCarrier tDemandOrderCarrier = new TDemandOrderCarrier();
        tDemandOrderCarrier.setDemandOrderId(demandOrder.getId());
        tDemandOrderCarrier.setCompanyCarrierId(demandOrder.getCompanyCarrierId());
        tDemandOrderCarrier.setCarrierContactId(demandOrder.getCarrierContactId());
        tDemandOrderCarrier.setCarrierPrice(demandOrder.getCarrierPrice());
        tDemandOrderCarrier.setCarrierPriceType(demandOrder.getCarrierPriceType());
        commonBiz.setBaseEntityAdd(tDemandOrderCarrier, BaseContextHandler.getUserName());
        tDemandOrderCarrierMapper.insertSelective(tDemandOrderCarrier);

        //收发货地址判断（新增/修改）
        processAddressWhenPublish(requestModel);

        //异步查询地址经纬度
        List<TDemandOrderAddress> addDemandOrderAddressList = new ArrayList<>();
        addDemandOrderAddressList.add(demandOrderAddress);
        AsyncProcessQueue.execute(() -> demandOrderCommonBiz.updateAddressLonAndLat(addDemandOrderAddressList));
    }

    /**
     * 匹配货主运价规则
     * @param requestModel
     */
    private GetPriceByAddressAndAmountResponseModel processActualFee(SaveDemandOrderRequestModel requestModel,BigDecimal goodsAmount){
        GetPriceByAddressAndAmountRequestModel model = new GetPriceByAddressAndAmountRequestModel();
        model.setCompanyEntrustId(requestModel.getCompanyEntrustId());
        model.setFromProvinceId(requestModel.getLoadProvinceId());
        model.setFromCityId(requestModel.getLoadCityId());
        model.setFromAreaId(requestModel.getLoadAreaId());
        model.setFromWarehouse(requestModel.getLoadWarehouse());
        model.setToProvinceId(requestModel.getUnloadProvinceId());
        model.setToCityId(requestModel.getUnloadCityId());
        model.setToAreaId(requestModel.getUnloadAreaId());
        model.setToWarehouseDetail(requestModel.getUnloadDetailAddress());
        model.setFreightType(requestModel.getContractPriceType());
        model.setGoodsUnit(requestModel.getDemandOrderGoodsList().get(0).getGoodsUnit());
        model.setGoodsAmount(goodsAmount);
        return freightBiz.getEntrustFreightInfo(model);
    }


    //委托发布-收发货地址判断（新增/修改）
    private void processAddressWhenPublish(SaveDemandOrderRequestModel requestModel) {
        //验证收发货地址是否改变
        List<TEntrustAddress> entrustAddressList = new ArrayList<>();
        List<TEntrustAddress> upEntrustAddressList = new ArrayList<>();
        AddOrModifyEntrustAddressRequestModel addressRequestModel = new AddOrModifyEntrustAddressRequestModel();
        addressRequestModel.setAddressType(EntrustAddressTypeEnum.DELIVERY.getKey());
        addressRequestModel.setCompanyEntrustId(requestModel.getCompanyEntrustId());
        addressRequestModel.setProvinceId(requestModel.getLoadProvinceId());
        addressRequestModel.setCityId(requestModel.getLoadCityId());
        addressRequestModel.setAreaId(requestModel.getLoadAreaId());
        addressRequestModel.setDetailAddress(requestModel.getLoadDetailAddress());
        addressRequestModel.setWarehouse(requestModel.getLoadWarehouse());
        TEntrustAddress tEntrustAddress = entrustAddressMapper.getByCompanyAndAddress(addressRequestModel);
        TEntrustAddress entrustAddress = new TEntrustAddress();
        entrustAddress.setAddressType(EntrustAddressTypeEnum.DELIVERY.getKey());
        entrustAddress.setCompanyEntrustId(requestModel.getCompanyEntrustId());
        entrustAddress.setContactName(requestModel.getLoadContactName());
        entrustAddress.setContactMobile(requestModel.getLoadContactMobile());
        entrustAddress.setWarehouse(requestModel.getLoadWarehouse());
        entrustAddress.setProvinceId(requestModel.getLoadProvinceId());
        entrustAddress.setProvinceName(requestModel.getLoadProvinceName());
        entrustAddress.setCityId(requestModel.getLoadCityId());
        entrustAddress.setCityName(requestModel.getLoadCityName());
        entrustAddress.setAreaId(requestModel.getLoadAreaId());
        entrustAddress.setAreaName(requestModel.getLoadAreaName());
        entrustAddress.setDetailAddress(requestModel.getLoadDetailAddress());
        if (tEntrustAddress == null) {
            commonBiz.setBaseEntityAdd(entrustAddress, BaseContextHandler.getUserName());
            entrustAddressList.add(entrustAddress);
        } else {
            entrustAddress.setId(tEntrustAddress.getId());
            commonBiz.setBaseEntityModify(entrustAddress, BaseContextHandler.getUserName());
            upEntrustAddressList.add(entrustAddress);
        }
        addressRequestModel = new AddOrModifyEntrustAddressRequestModel();
        addressRequestModel.setCompanyEntrustId(requestModel.getCompanyEntrustId());
        addressRequestModel.setAddressType(EntrustAddressTypeEnum.RECEIVING.getKey());
        addressRequestModel.setCompanyName(requestModel.getUnloadCompanyName());
        addressRequestModel.setProvinceId(requestModel.getUnloadProvinceId());
        addressRequestModel.setCityId(requestModel.getUnloadCityId());
        addressRequestModel.setAreaId(requestModel.getUnloadAreaId());
        addressRequestModel.setDetailAddress(requestModel.getUnloadDetailAddress());
        addressRequestModel.setWarehouse(requestModel.getUnloadWarehouse());
        tEntrustAddress = entrustAddressMapper.getByCompanyAndAddress(addressRequestModel);
        entrustAddress = new TEntrustAddress();
        entrustAddress.setAddressType(EntrustAddressTypeEnum.RECEIVING.getKey());
        entrustAddress.setCompanyEntrustId(requestModel.getCompanyEntrustId());
        entrustAddress.setCompanyName(requestModel.getUnloadCompanyName());
        entrustAddress.setContactName(requestModel.getUnloadContactName());
        entrustAddress.setContactMobile(requestModel.getUnloadContactMobile());
        entrustAddress.setWarehouse(requestModel.getUnloadWarehouse());
        entrustAddress.setProvinceId(requestModel.getUnloadProvinceId());
        entrustAddress.setProvinceName(requestModel.getUnloadProvinceName());
        entrustAddress.setCityId(requestModel.getUnloadCityId());
        entrustAddress.setCityName(requestModel.getUnloadCityName());
        entrustAddress.setAreaId(requestModel.getUnloadAreaId());
        entrustAddress.setAreaName(requestModel.getUnloadAreaName());
        entrustAddress.setDetailAddress(requestModel.getUnloadDetailAddress());
        if (tEntrustAddress == null) {
            commonBiz.setBaseEntityAdd(entrustAddress, BaseContextHandler.getUserName());
            entrustAddressList.add(entrustAddress);
        } else {
            entrustAddress.setId(tEntrustAddress.getId());
            commonBiz.setBaseEntityModify(entrustAddress, BaseContextHandler.getUserName());
            upEntrustAddressList.add(entrustAddress);
        }
        if (ListUtils.isNotEmpty(entrustAddressList)) {
            entrustAddressMapper.batchInsert(entrustAddressList);
        }
        if (ListUtils.isNotEmpty(upEntrustAddressList)) {
            entrustAddressMapper.batchUpdate(upEntrustAddressList);
        }
    }

    /**
     * 调度车辆生成运单同时修改需求单状态、已安排、未安排数量、调车数，修改需求单S单数量，生成需求单日志和事件
     * @param requestModel
     */
    public void operationDemandOrderByDispatchVehicle(OperationDemandOrderByDispatchVehicleModel requestModel) {
        UpdateDemandOrderStatusAndAmountModel demandModel = requestModel.getStatusAmountModel();
        if (demandModel != null) {
            List<UpdateDispatchVehicleCountRequestModel> dispatchVehicleCountList = requestModel.getUpdateCountList();
            Map<Long,String> demandOrderCarrierMap = requestModel.getDemandOrderCarrierMap();//需求单id-》运单号

            List<Long> completeDispatchList = new ArrayList<>();
            List<LogisticsDemandStateSynchronizeModel> demandStateSynchronizeModels = new ArrayList<>();
            List<DispatchCompleteModel> dispatchCompleteModelList=new ArrayList<>();

            DispatchCompleteModel dispatchCompleteModel;
            Map<Long, BigDecimal> demandOrderArrangedAmountMap = demandModel.getDemandOrderArrangedAmountMap();
            Map<Long, BigDecimal> demandGoodArrangedAmountMap = demandModel.getDemandGoodArrangedAmountMap();
            //更新需求单信息
            if (ListUtils.isNotEmpty(demandModel.getDemandOrderIds()) && ListUtils.isNotEmpty(dispatchVehicleCountList)) {
                List<TDemandOrder> demandOrderList = demandOrderMapper.getByIds(StringUtils.listToString(demandModel.getDemandOrderIds(), ','));
                if (ListUtils.isNotEmpty(demandOrderList)) {
                    Map<Long, BigDecimal> arrangedAmountMap = new HashMap<>();
                    Map<Long, BigDecimal> notArrangedAmountMap = new HashMap<>();
                    Map<Long, Integer> dispatchVehicleCountMap = new HashMap<>();
                    dispatchVehicleCountList.forEach(count ->
                            dispatchVehicleCountMap.put(count.getDemandId(), count.getDispatchVehicleCount())
                    );
                    demandOrderList.forEach(order -> {
                        arrangedAmountMap.put(order.getId(), order.getArrangedAmount());
                        notArrangedAmountMap.put(order.getId(), order.getNotArrangedAmount());
                    });

                    Integer dispatchValidity;
                    TDemandOrder demandOrder;
                    List<TDemandOrder> updateDemandOrderList = new ArrayList<>();
                    Date now = new Date();
                    LogisticsDemandStateSynchronizeModel synchronizeModel;
                    for (TDemandOrder demand : demandOrderList) {
                        //更新需求单
                        BigDecimal notArrangedAmount = notArrangedAmountMap.get(demand.getId()).subtract(demandOrderArrangedAmountMap.get(demand.getId()));
                        demandOrder = new TDemandOrder();
                        demandOrder.setId(demand.getId());
                        demandOrder.setStatusUpdateTime(now);
                        if (notArrangedAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                            demandOrder.setEntrustStatus(DemandOrderStatusEnum.PART_DISPATCH.getKey());
                            demandOrder.setStatus(DemandOrderStatusEnum.PART_DISPATCH.getKey());
                        } else {
                            demandOrder.setEntrustStatus(DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey());
                            demandOrder.setStatus(DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey());
                            completeDispatchList.add(demand.getId());

                            //托盘同步过来的需求单
                            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(demand.getSource())) {
                                //计算调度时效、是否逾期
                                dispatchValidity = commonBiz.differentDays(demand.getPublishTime(), now);
                                demandOrder.setDispatchValidity(dispatchValidity);
                                if (CommonConstant.INTEGER_ONE.equals(demand.getIfUrgent())){//加急的单子，超过1天算逾期
                                    if (dispatchValidity > CommonConstant.INTEGER_ONE){
                                        demandOrder.setIfOverdue(CommonConstant.INTEGER_ONE);
                                    }
                                }else{//非加急的单子，超过3天算逾期
                                    if (dispatchValidity > CommonConstant.INTEGER_THREE){
                                        demandOrder.setIfOverdue(CommonConstant.INTEGER_ONE);
                                    }
                                }

                                //同步托盘系统
                                synchronizeModel = new LogisticsDemandStateSynchronizeModel();
                                synchronizeModel.setType(demand.getEntrustType());
                                synchronizeModel.setDemandOrderId(demand.getId());
                                synchronizeModel.setDemandState(DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey());
                                synchronizeModel.setDemandCode(demand.getDemandOrderCode());
                                synchronizeModel.setCompleteBackAmount(BigDecimal.ZERO);
                                synchronizeModel.setLastModifiedBy(BaseContextHandler.getUserName());
                                demandStateSynchronizeModels.add(synchronizeModel);
                                if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(demand.getEntrustType())
                                        || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(demand.getEntrustType())
                                        || EntrustTypeEnum.BOOKING.getKey().equals(demand.getEntrustType())){
                                    dispatchCompleteModel=new DispatchCompleteModel();
                                    dispatchCompleteModel.setDemandOrderId(demand.getId());
                                    dispatchCompleteModel.setDemandCode(demand.getDemandOrderCode());
                                    dispatchCompleteModel.setDemandState(DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey());
                                    dispatchCompleteModel.setCompleteBackAmount(CommonConstant.INTEGER_ZERO);
                                    dispatchCompleteModel.setDispatchTime(now);
                                    dispatchCompleteModelList.add(dispatchCompleteModel);
                                }
                            }
                        }
                        demandOrder.setArrangedAmount(arrangedAmountMap.get(demand.getId()).add(demandOrderArrangedAmountMap.get(demand.getId())));
                        demandOrder.setNotArrangedAmount(notArrangedAmount);
                        demandOrder.setDispatchVehicleCount(dispatchVehicleCountMap.get(demand.getId()));
                        commonBiz.setBaseEntityModify(demandOrder, BaseContextHandler.getUserName());
                        updateDemandOrderList.add(demandOrder);
                    }
                    if (ListUtils.isNotEmpty(updateDemandOrderList)) {
                        demandOrderMapper.batchUpdateByPrimaryKeySelective(updateDemandOrderList);
                    }
                }
            }

            //更新需求单货物信息
            Map<Long,List<CompleteDemandOrderGoodsBackAmountModel>> goodsBackMap = new HashMap<>();
            if (ListUtils.isNotEmpty(demandModel.getDemandGoodIds())) {
                List<TDemandOrderGoods> goodsList = demandOrderGoodsMapper.getDemandOrderGoodsByGoodsIds(StringUtils.listToString(demandModel.getDemandGoodIds(), ','));
                if (ListUtils.isNotEmpty(goodsList)) {
                    List<TDemandOrderGoodsRel> demandOrderGoodsRelList = tDemandOrderGoodsRelMapper.getByDemandOrderIds(StringUtils.listToString(demandModel.getDemandOrderIds(), ','));
                    Map<Long,Long> goodsIdMap = new HashMap<>();
                    if (ListUtils.isNotEmpty(demandOrderGoodsRelList)){
                        demandOrderGoodsRelList.forEach(item -> goodsIdMap.put(item.getDemandOrderGoodsId(),item.getBookingOrderGoodsId()));
                    }

                    Map<Long, BigDecimal> arrangedAmountMap = new HashMap<>();
                    Map<Long, BigDecimal> notArrangedAmountMap = new HashMap<>();
                    goodsList.forEach(good -> {
                        arrangedAmountMap.put(good.getId(), good.getArrangedAmount());
                        notArrangedAmountMap.put(good.getId(), good.getNotArrangedAmount());
                    });
                    TDemandOrderGoods demandOrderGoods;
                    List<TDemandOrderGoods> demandOrderGoodsList = new ArrayList<>();
                    List<CompleteDemandOrderGoodsBackAmountModel> goodsBackList;
                    CompleteDemandOrderGoodsBackAmountModel goodsBackAmountModel;
                    for (TDemandOrderGoods goods : goodsList) {
                        demandOrderGoods = new TDemandOrderGoods();
                        demandOrderGoods.setId(goods.getId());
                        demandOrderGoods.setArrangedAmount(arrangedAmountMap.get(goods.getId()).add(demandGoodArrangedAmountMap.get(goods.getId())));
                        demandOrderGoods.setNotArrangedAmount(notArrangedAmountMap.get(goods.getId()).subtract(demandGoodArrangedAmountMap.get(goods.getId())));
                        commonBiz.setBaseEntityModify(demandOrderGoods, BaseContextHandler.getUserName());
                        demandOrderGoodsList.add(demandOrderGoods);

                        //预约单货物信息
                        if (goodsIdMap.get(demandOrderGoods.getId()) != null){
                            goodsBackAmountModel = new CompleteDemandOrderGoodsBackAmountModel();
                            goodsBackAmountModel.setGoodsId(goodsIdMap.get(demandOrderGoods.getId()));
                            goodsBackAmountModel.setArrangedAmount(demandOrderGoods.getArrangedAmount());
                            goodsBackAmountModel.setNotArrangedAmount(demandOrderGoods.getNotArrangedAmount());
                            goodsBackAmountModel.setFallbackCount(BigDecimal.ZERO);
                            goodsBackList = goodsBackMap.get(goods.getDemandOrderId());
                            if (ListUtils.isEmpty(goodsBackList)) {
                                goodsBackList = new ArrayList<>();
                            }
                            goodsBackList.add(goodsBackAmountModel);
                            goodsBackMap.put(goods.getDemandOrderId(),goodsBackList);
                        }
                    }
                    if (ListUtils.isNotEmpty(demandOrderGoodsList)) {
                        demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(demandOrderGoodsList);
                    }
                }
            }

            //更新需求单关联S单
            if (ListUtils.isNotEmpty(demandModel.getDemandOrderRelIds())) {
                List<TDemandOrderOrderRel> relList = tDemandOrderOrderRelMapper.getByIds(StringUtils.listToString(demandModel.getDemandOrderRelIds(), ','));
                if (ListUtils.isNotEmpty(relList)) {
                    Map<Long, BigDecimal> arrangedAmountRelMap = demandModel.getDemandOrderRelArrangedAmountMap();
                    Map<Long, BigDecimal> arrangedAmountMap = new HashMap<>();
                    relList.forEach(item -> arrangedAmountMap.put(item.getId(), item.getArrangedAmount()));
                    List<TDemandOrderOrderRel> upRelList = new ArrayList<>();
                    TDemandOrderOrderRel demandOrderOrderRel;
                    for (Long item : demandModel.getDemandOrderRelIds()) {
                        demandOrderOrderRel = new TDemandOrderOrderRel();
                        demandOrderOrderRel.setId(item);
                        demandOrderOrderRel.setArrangedAmount(arrangedAmountMap.get(item).add(arrangedAmountRelMap.get(item)));
                        commonBiz.setBaseEntityModify(demandOrderOrderRel, BaseContextHandler.getUserName());
                        upRelList.add(demandOrderOrderRel);
                    }
                    if (ListUtils.isNotEmpty(upRelList)) {
                        tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(upRelList);
                    }
                }
            }
            //调度车辆生成运单同时记录需求单日志、需求单事件
            createCarrierOrderToDemandOrderLog(demandOrderCarrierMap, completeDispatchList);

            //调度完成-同步云仓数据信息
            if (ListUtils.isNotEmpty(demandStateSynchronizeModels)) {
                for (LogisticsDemandStateSynchronizeModel model : demandStateSynchronizeModels) {
                    if (ListUtils.isNotEmpty(goodsBackMap.get(model.getDemandOrderId()))){
                        model.setTLogisticsDemandGoodsItemModels(goodsBackMap.get(model.getDemandOrderId()));
                    }
                }
                rabbitMqPublishBiz.completeDemandOrderSyn(demandStateSynchronizeModels, CommonConstant.ONE);
            }
            //调度完成-同步云盘数据信息
            if (ListUtils.isNotEmpty(dispatchCompleteModelList)) {
                DispatchCompleteModelMessage dispatchCompleteModelMessage=new DispatchCompleteModelMessage();
                MapperMapping<CompleteDemandOrderGoodsBackAmountModel, TLogisticsDemandGoodsItemModel> mapping= new MapperMapping<CompleteDemandOrderGoodsBackAmountModel,TLogisticsDemandGoodsItemModel>() {
                    @Override
                    public void configure() {
                        TLogisticsDemandGoodsItemModel destination = getDestination();
                        destination.setLastModifiedBy(BaseContextHandler.getUserName());
                        destination.setLastModifiedTime(new Date());
                    }
                };
                for (DispatchCompleteModel model : dispatchCompleteModelList) {
                    if (ListUtils.isNotEmpty(goodsBackMap.get(model.getDemandOrderId()))){
                        List<CompleteDemandOrderGoodsBackAmountModel> completeDemandOrderGoodsBackAmountModels = goodsBackMap.get(model.getDemandOrderId());
                        model.setTLogisticsDemandGoodsItemModels(MapperUtils.mapper(completeDemandOrderGoodsBackAmountModels, TLogisticsDemandGoodsItemModel.class,mapping));
                    }
                }
                dispatchCompleteModelMessage.setDispatchCompleteModelList(dispatchCompleteModelList);
                dispatchCompleteModelMessage.setUserName(BaseContextHandler.getUserName());
                rabbitMqPublishBiz.completeDemandOrderSynToLeYi(dispatchCompleteModelMessage, CommonConstant.ONE);
            }
            //生成运单-同步新生数据信息
            if (ListUtils.isNotEmpty(requestModel.getCarrierOrderForLifeList())) {
                List<CarrierOrderCreateListToYeloLifeModel> carrierOrderForLifeList = requestModel.getCarrierOrderForLifeList();
                if (ListUtils.isNotEmpty(completeDispatchList)){
                    for (CarrierOrderCreateListToYeloLifeModel lifeModel : carrierOrderForLifeList) {
                        if (completeDispatchList.contains(lifeModel.getDemandOrderId())){
                            lifeModel.setCompleteStatus(CommonConstant.ONE);
                        }
                    }
                }
                CarrierOrderCreateToYeloLifeModel carrierOrderCreateToYeloLifeModel = new CarrierOrderCreateToYeloLifeModel();
                carrierOrderCreateToYeloLifeModel.setDemandCarrierOrderList(carrierOrderForLifeList);
                carrierOrderCreateToYeloLifeModel.setUserName(BaseContextHandler.getUserName());
                SyncCarrierOrderToYeloLifeModel<Object> syncCarrierOrderToYeloLifeModel = new SyncCarrierOrderToYeloLifeModel<>();
                syncCarrierOrderToYeloLifeModel.setType(SyncCarrierOrderToYeloLifeModel.SyncTypeEnum.CREATE.getKey());
                syncCarrierOrderToYeloLifeModel.setMsgData(carrierOrderCreateToYeloLifeModel);
                rabbitMqPublishBiz.syncCarrierOrderToYeloLife(syncCarrierOrderToYeloLifeModel);
            }
        }
    }

    //调度车辆生成运单同时记录需求单日志、需求单事件
    public void createCarrierOrderToDemandOrderLog(Map<Long, String> demandOrderLogMap, List<Long> completeDispatchList) {
        if (null != demandOrderLogMap && demandOrderLogMap.size() > 0) {
            TDemandOrderOperateLogs demandOrderOperateLogs;
            List<TDemandOrderOperateLogs> list = new ArrayList<>();
            TDemandOrderEvents demandOrderEvents;
            List<TDemandOrderEvents> eventsList = new ArrayList<>();
            for (Map.Entry<Long, String> entry : demandOrderLogMap.entrySet()) {
                demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(entry.getKey(), DemandOrderOperateLogsEnum.DISPATCH_VEHICLE, BaseContextHandler.getUserName(), "");
                demandOrderOperateLogs.setOperationContent(String.format(DemandOrderOperateLogsEnum.DISPATCH_VEHICLE.getValue(), entry.getValue()));
                list.add(demandOrderOperateLogs);
                TDemandOrder demandOrder = demandOrderMapper.selectByPrimaryKeyDecrypt(entry.getKey());
                if (demandOrder == null || CommonConstant.INTEGER_ZERO.equals(demandOrder.getValid())) {
                    throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
                }

                demandOrderEvents = demandOrderCommonBiz.generateEvent(entry.getKey(), demandOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.DISPATCH_VEHICLE, BaseContextHandler.getUserName());
                eventsList.add(demandOrderEvents);

                if (completeDispatchList.contains(entry.getKey())) {
                    demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(entry.getKey(), DemandOrderOperateLogsEnum.COMPLETE_DISPATCH, BaseContextHandler.getUserName(), String.format(DemandOrderOperateLogsEnum.COMPLETE_DISPATCH.getValue(), "0"));
                    list.add(demandOrderOperateLogs);
                    demandOrderEvents = demandOrderCommonBiz.generateEvent(entry.getKey(), demandOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.COMPLETE_DISPATCH_EVENTS, BaseContextHandler.getUserName());
                    eventsList.add(demandOrderEvents);
                }
            }
            if (ListUtils.isNotEmpty(list)) {
                demandOrderOperateLogsMapper.batchInsertSelective(list);
            }
            if (ListUtils.isNotEmpty(eventsList)) {
                demandOrderEventsMapper.batchInsertSelective(eventsList);
            }
        }
    }

    /**
     * 物流系统取消运单-修改需求单的状态和数量
     * @param responseModels
     */
    public void cancelCarrierOrderUpdateDemandOrder(CancelCarrierOrderImpactDemandOrderModel responseModels) {
        if (responseModels == null) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        Map<Long, BigDecimal> carrierOrderCountMap = responseModels.getCarrierOrderCountMap();
        Iterator<Long> iterator = carrierOrderCountMap.keySet().iterator();

        Map<Long, BigDecimal> carrierGoodCountMap = responseModels.getCarrierGoodCountMap();
        Iterator<Long> iterator1 = carrierGoodCountMap.keySet().iterator();
        List<Long> demandGoods = new ArrayList<>();
        while (iterator1.hasNext()) {
            demandGoods.add(iterator1.next());
        }
        List<Long> demandIds = new ArrayList<>();
        while (iterator.hasNext()) {
            demandIds.add(iterator.next());
        }
        //同步云仓model
        CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel cancelCarrierOrderStataSynchronizeModel = new CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel();
        //同步云盘model
        CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel cancelCarrierOrderStataSynchronizeToLeYiModel = new CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel();

        //同步云仓,云盘 model
        CancelCarrierOrderChangeDemandStateSynchronizeModel synchronizeModel;
        List<CancelCarrierOrderChangeDemandStateSynchronizeModel> demandStateSynchronizeModels = new ArrayList<>();//云仓
        List<CancelCarrierOrderChangeDemandStateSynchronizeModel> demandStateSynchronizeToLeYiModels = new ArrayList<>();//云盘

        //同步新生需求单号
        List<String> codeListSyncLife = new ArrayList<>();

        List<TDemandOrder> list = demandOrderMapper.getByIds(StringUtils.listToString(demandIds, ','));
        if (ListUtils.isNotEmpty(list)) {
            TDemandOrder demandOrder;
            List<TDemandOrder> cancelList = new ArrayList<>();
            Date now = new Date();
            for (TDemandOrder model : list) {
                //改变需求单状态
                demandOrder = new TDemandOrder();
                demandOrder.setId(model.getId());
                demandOrder.setArrangedAmount(model.getArrangedAmount().subtract(carrierOrderCountMap.getOrDefault(model.getId(), CommonConstant.BIG_DECIMAL_ZERO)));
                demandOrder.setNotArrangedAmount(model.getNotArrangedAmount().add(carrierOrderCountMap.getOrDefault(model.getId(), CommonConstant.BIG_DECIMAL_ZERO)));
                demandOrder.setStatusUpdateTime(now);
                commonBiz.setBaseEntityModify(demandOrder, BaseContextHandler.getUserName());
                //需求单状态回退到未完成调度，则调度时效、是否逾期均置零（托盘的单子）
                demandOrder.setDispatchValidity(CommonConstant.INTEGER_ZERO);
                demandOrder.setIfOverdue(CommonConstant.INTEGER_ZERO);

                synchronizeModel = new CancelCarrierOrderChangeDemandStateSynchronizeModel();
                synchronizeModel.setDemandOrderId(model.getId());//临时字段
                synchronizeModel.setDemandCode(model.getDemandOrderCode());
                synchronizeModel.setUserName(BaseContextHandler.getUserName());
                if (demandOrder.getArrangedAmount().compareTo(CommonConstant.BIG_DECIMAL_ZERO) > CommonConstant.INTEGER_ZERO) {
                    demandOrder.setStatus(DemandOrderStatusEnum.PART_DISPATCH.getKey());
                    demandOrder.setEntrustStatus(DemandOrderStatusEnum.PART_DISPATCH.getKey());
                    synchronizeModel.setDemandState(DemandOrderStatusEnum.PART_DISPATCH.getKey());
                } else {
                    demandOrder.setStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
                    demandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
                    synchronizeModel.setDemandState(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
                }
                cancelList.add(demandOrder);
                //托盘同步过来的需求单
                if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(model.getSource())) {
                    demandStateSynchronizeModels.add(synchronizeModel);

                    //回收入库、回收出库、预约类型
                    if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(model.getEntrustType())
                            || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(model.getEntrustType())
                            || EntrustTypeEnum.BOOKING.getKey().equals(model.getEntrustType())) {
                        demandStateSynchronizeToLeYiModels.add(synchronizeModel);
                    }
                }
                //新生单子
                else if (DemandOrderSourceEnum.YELO_LIFE.getKey().equals(model.getSource())) {
                    //需求单回到待调度状态需同步新生
                    if (DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(demandOrder.getEntrustStatus())){
                        codeListSyncLife.add(model.getDemandOrderCode());
                    }
                }
            }
            List<TDemandOrderGoods> demandOrderGoodsList = new ArrayList<>();
            List<TDemandOrderGoods> goodsList = demandOrderGoodsMapper.getDemandOrderGoodsByGoodsIds(StringUtils.listToString(demandGoods, ','));
            TDemandOrderGoods demandOrderGoods;
            for (TDemandOrderGoods goods : goodsList) {
                demandOrderGoods = new TDemandOrderGoods();
                demandOrderGoods.setId(goods.getId());
                demandOrderGoods.setArrangedAmount(goods.getArrangedAmount().subtract(carrierGoodCountMap.get(goods.getId())));
                demandOrderGoods.setNotArrangedAmount(goods.getNotArrangedAmount().add(carrierGoodCountMap.get(goods.getId())));
                commonBiz.setBaseEntityModify(demandOrderGoods, BaseContextHandler.getUserName());
                demandOrderGoodsList.add(demandOrderGoods);
            }

            //关联减去s单数据
            List<CancelCarrierOrderOrderRelModel> carrierOrderOrderRelModels = responseModels.getCarrierOrderOrderRelModels();
            Map<Long, BigDecimal> carrierOrderOrderRelModelMap = new HashMap<>();
            if (ListUtils.isNotEmpty(carrierOrderOrderRelModels)) {
                BigDecimal expectAmount;
                for (CancelCarrierOrderOrderRelModel relModel : carrierOrderOrderRelModels) {
                    expectAmount = carrierOrderOrderRelModelMap.get(relModel.getDemandOrderOrderId());
                    if (expectAmount == null) {
                        carrierOrderOrderRelModelMap.put(relModel.getDemandOrderOrderId(), relModel.getExpectAmount());
                    } else {
                        expectAmount = expectAmount.add(relModel.getExpectAmount());
                        carrierOrderOrderRelModelMap.put(relModel.getDemandOrderOrderId(), expectAmount);
                    }
                }
                List<Long> collect = carrierOrderOrderRelModels.stream().map(CancelCarrierOrderOrderRelModel::getDemandOrderOrderId).collect(Collectors.toList());
                List<TDemandOrderOrderRel> byIds = tDemandOrderOrderRelMapper.getByIds(StringUtils.listToString(collect, ','));
                List<TDemandOrderOrderRel> upOrderRelsList = new ArrayList<>();
                if (ListUtils.isNotEmpty(byIds)) {
                    TDemandOrderOrderRel upOrderOrderRel;
                    for (TDemandOrderOrderRel orderOrderRel : byIds) {
                        expectAmount = carrierOrderOrderRelModelMap.get(orderOrderRel.getId());
                        if (expectAmount != null) {
                            upOrderOrderRel = new TDemandOrderOrderRel();
                            upOrderOrderRel.setId(orderOrderRel.getId());
                            upOrderOrderRel.setArrangedAmount(orderOrderRel.getArrangedAmount().subtract(expectAmount));
                            commonBiz.setBaseEntityModify(orderOrderRel, BaseContextHandler.getUserName());
                            upOrderRelsList.add(upOrderOrderRel);
                        }
                    }
                }
                if (ListUtils.isNotEmpty(upOrderRelsList)) {
                    tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(upOrderRelsList);
                }
            }

            if (ListUtils.isNotEmpty(demandOrderGoodsList)) {
                demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(demandOrderGoodsList);
            }
            if (ListUtils.isNotEmpty(cancelList)) {
                demandOrderMapper.batchUpdateByPrimaryKeySelective(cancelList);
            }


            List<CarrierOrderSynchronizeModel> synchronizeModels = responseModels.getSynchronizeModels();//同步给云仓的运单数据
            List<CarrierOrderSynchronizeModel> synchronizeToLeYiModels = new ArrayList<>();//同步给云盘的运单数据

            //回收类型的需求单id list
            List<Long> recycleDemandOrderIdList = demandStateSynchronizeToLeYiModels.stream()
                    .map(CancelCarrierOrderChangeDemandStateSynchronizeModel::getDemandOrderId).collect(Collectors.toList());
            //同步云仓数据
            if (ListUtils.isNotEmpty(demandStateSynchronizeModels)) {
                cancelCarrierOrderStataSynchronizeModel.setCancelCarrierOrderChangeDemandStateSynchronizeModels(demandStateSynchronizeModels);
            }
            if (ListUtils.isNotEmpty(synchronizeModels)) {
                //筛选出回收类型的
                for (CarrierOrderSynchronizeModel model : synchronizeModels) {

                    if (recycleDemandOrderIdList.contains(model.getDemandOrderId())) {
                        synchronizeToLeYiModels.add(model);
                    }
                }
                cancelCarrierOrderStataSynchronizeModel.setSynchronizeModels(synchronizeModels);
            }
            if (ListUtils.isNotEmpty(cancelCarrierOrderStataSynchronizeModel.getCancelCarrierOrderChangeDemandStateSynchronizeModels())
                    || ListUtils.isNotEmpty(cancelCarrierOrderStataSynchronizeModel.getSynchronizeModels())) {
                rabbitMqPublishBiz.cancelCarrierOrderSyn(cancelCarrierOrderStataSynchronizeModel, CommonConstant.ONE);
            }

            //同步云盘数据
            if (ListUtils.isNotEmpty(demandStateSynchronizeToLeYiModels)) {
                cancelCarrierOrderStataSynchronizeToLeYiModel.setCancelCarrierOrderChangeDemandStateSynchronizeModels(demandStateSynchronizeToLeYiModels);
            }
            if (ListUtils.isNotEmpty(synchronizeToLeYiModels)) {
                cancelCarrierOrderStataSynchronizeToLeYiModel.setSynchronizeModels(synchronizeToLeYiModels);
            }
            if (ListUtils.isNotEmpty(cancelCarrierOrderStataSynchronizeToLeYiModel.getCancelCarrierOrderChangeDemandStateSynchronizeModels())
                    || ListUtils.isNotEmpty(cancelCarrierOrderStataSynchronizeToLeYiModel.getSynchronizeModels())) {
                rabbitMqPublishBiz.cancelCarrierOrderSynToLeYi(cancelCarrierOrderStataSynchronizeToLeYiModel, CommonConstant.ONE);
            }

            //同步新生
            if (ListUtils.isNotEmpty(responseModels.getCarrierOrderForLifeList())){
                List<CarrierOrderCancelToYeloLifeModel> carrierOrderForLifeList = responseModels.getCarrierOrderForLifeList();
                if (ListUtils.isNotEmpty(codeListSyncLife)){
                    for (CarrierOrderCancelToYeloLifeModel lifeModel : carrierOrderForLifeList) {
                        if (codeListSyncLife.contains(lifeModel.getDemandOrderCode())){
                            lifeModel.setDemandOrderStatusBack(YesOrNoEnum.YES.getKey());
                        }
                    }
                }
                SyncCarrierOrderToYeloLifeModel<Object> syncCarrierOrderToYeloLifeModel = new SyncCarrierOrderToYeloLifeModel<>();
                syncCarrierOrderToYeloLifeModel.setType(SyncCarrierOrderToYeloLifeModel.SyncTypeEnum.CANCEL.getKey());
                syncCarrierOrderToYeloLifeModel.setMsgData(carrierOrderForLifeList);
                rabbitMqPublishBiz.syncCarrierOrderToYeloLife(syncCarrierOrderToYeloLifeModel);
            }
        }
    }

    /**
     * 云盘云仓系统取消运单（发货、回收出库）-修改需求单的状态和数量
     * @param requestModel
     */
    public void externalCancelCarrierOrderUpdateDemandOrder(ExternalCancelCarrierOrderImpactDemandOrderModel requestModel) {
        if (requestModel == null) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }

        //需求单id-》退回到需求单已安排的数量
        Map<Long, BigDecimal> arrangedAmountMap = requestModel.getArrangedAmountMap();

        //需求单id-》退回到需求单未安排的数量
        Map<Long, BigDecimal> notArrangedAmountMap = requestModel.getNotArrangedAmountMap();

        //获取需求单id
        Iterator<Long> iterator = arrangedAmountMap.keySet().iterator();
        List<Long> demandIds = new ArrayList<>();
        while (iterator.hasNext()) {
            demandIds.add(iterator.next());
        }

        //需求单货物id-》退回到需求单货物已安排的数量
        Map<Long, BigDecimal> goodsArrangedAmountMap = requestModel.getGoodsArrangedAmountMap();

        //需求单货物id-》退回到需求单货物未安排的数量
        Map<Long, BigDecimal> goodsNotArrangedAmountMap = requestModel.getGoodsNotArrangedAmountMap();

        //获取需求单获取id
        Iterator<Long> iterator1 = goodsArrangedAmountMap.keySet().iterator();
        List<Long> demandGoodsIds = new ArrayList<>();
        while (iterator1.hasNext()) {
            demandGoodsIds.add(iterator1.next());
        }

        //查询需求单
        List<TDemandOrder> dbDemandOrderList = demandOrderMapper.getByIds(StringUtils.listToString(demandIds, ','));
        if (ListUtils.isEmpty(dbDemandOrderList)) {
            return;
        }

        //同步云仓,云盘 model
        CancelCarrierOrderChangeDemandStateSynchronizeModel synchronizeModel;
        List<CancelCarrierOrderChangeDemandStateSynchronizeModel> demandStateSynchronizeModels = new ArrayList<>();//云仓
        List<CancelCarrierOrderChangeDemandStateSynchronizeModel> demandStateSynchronizeToLeYiModels = new ArrayList<>();//云盘


        //更新需求单信息
        TDemandOrder demandOrder;
        List<TDemandOrder> upDemandOrderList = new ArrayList<>();
        Date now = new Date();
        for (TDemandOrder tDemandOrder : dbDemandOrderList) {
            BigDecimal arrangedAmount = arrangedAmountMap.get(tDemandOrder.getId());
            BigDecimal notArrangedAmount = notArrangedAmountMap.get(tDemandOrder.getId());
            //改变需求单状态
            demandOrder = new TDemandOrder();
            demandOrder.setId(tDemandOrder.getId());
            demandOrder.setArrangedAmount(tDemandOrder.getArrangedAmount().subtract(arrangedAmount));
            demandOrder.setNotArrangedAmount(tDemandOrder.getNotArrangedAmount().add(notArrangedAmount));
            demandOrder.setStatusUpdateTime(now);
            commonBiz.setBaseEntityModify(demandOrder, BaseContextHandler.getUserName());
            if (demandOrder.getArrangedAmount().compareTo(CommonConstant.BIG_DECIMAL_ZERO) > CommonConstant.INTEGER_ZERO) {
                demandOrder.setStatus(DemandOrderStatusEnum.PART_DISPATCH.getKey());
                demandOrder.setEntrustStatus(DemandOrderStatusEnum.PART_DISPATCH.getKey());
            } else {
                demandOrder.setStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
                demandOrder.setEntrustStatus(DemandOrderStatusEnum.WAIT_DISPATCH.getKey());
            }

            //需求单状态回退到未完成调度，则调度时效、是否逾期均置零（托盘的单子）
            demandOrder.setDispatchValidity(CommonConstant.INTEGER_ZERO);
            demandOrder.setIfOverdue(CommonConstant.INTEGER_ZERO);

            upDemandOrderList.add(demandOrder);

            //同步云盘、云仓数据
            synchronizeModel = new CancelCarrierOrderChangeDemandStateSynchronizeModel();
            synchronizeModel.setDemandOrderId(tDemandOrder.getId());//临时字段
            synchronizeModel.setDemandCode(tDemandOrder.getDemandOrderCode());
            synchronizeModel.setUserName(BaseContextHandler.getUserName());
            synchronizeModel.setDemandState(demandOrder.getEntrustStatus());

            //回收出库类型，同步云盘
            if (EntrustTypeEnum.RECYCLE_OUT.getKey().equals(tDemandOrder.getEntrustType())){
                demandStateSynchronizeToLeYiModels.add(synchronizeModel);
            }

            //同步云仓
            demandStateSynchronizeModels.add(synchronizeModel);
        }


        //更新需求单货物信息
        List<TDemandOrderGoods> upDemandOrderGoodsList = new ArrayList<>();
        List<TDemandOrderGoods> dbDemandOrderGoodsList = demandOrderGoodsMapper.getDemandOrderGoodsByGoodsIds(StringUtils.listToString(demandGoodsIds, ','));
        TDemandOrderGoods demandOrderGoods;
        for (TDemandOrderGoods goods : dbDemandOrderGoodsList) {
            BigDecimal goodsArrangedAmount = goodsArrangedAmountMap.get(goods.getId());
            BigDecimal goodsNotArrangedAmount = goodsNotArrangedAmountMap.get(goods.getId());

            demandOrderGoods = new TDemandOrderGoods();
            demandOrderGoods.setId(goods.getId());
            demandOrderGoods.setArrangedAmount(goods.getArrangedAmount().subtract(goodsArrangedAmount));
            demandOrderGoods.setNotArrangedAmount(goods.getNotArrangedAmount().add(goodsNotArrangedAmount));
            commonBiz.setBaseEntityModify(demandOrderGoods, BaseContextHandler.getUserName());
            upDemandOrderGoodsList.add(demandOrderGoods);
        }


        //更新关联s单信息
        List<CancelCarrierOrderOrderRelModel> carrierOrderOrderRelModels = requestModel.getCarrierOrderOrderRelModels();
        Map<Long, BigDecimal> carrierOrderOrderRelModelMap = new HashMap<>();
        if (ListUtils.isNotEmpty(carrierOrderOrderRelModels)) {
            BigDecimal expectAmount;
            List<Long> collect = new ArrayList<>();
            for (CancelCarrierOrderOrderRelModel relModel : carrierOrderOrderRelModels) {
                expectAmount = carrierOrderOrderRelModelMap.get(relModel.getDemandOrderOrderId());
                if (expectAmount == null) {
                    carrierOrderOrderRelModelMap.put(relModel.getDemandOrderOrderId(), relModel.getExpectAmount());
                } else {
                    expectAmount = expectAmount.add(relModel.getExpectAmount());
                    carrierOrderOrderRelModelMap.put(relModel.getDemandOrderOrderId(), expectAmount);
                }
                collect.add(relModel.getDemandOrderOrderId());
            }
            List<TDemandOrderOrderRel> byIds = tDemandOrderOrderRelMapper.getByIds(StringUtils.listToString(collect, ','));
            List<TDemandOrderOrderRel> upOrderRelsList = new ArrayList<>();
            if (ListUtils.isNotEmpty(byIds)) {
                TDemandOrderOrderRel upOrderOrderRel;
                for (TDemandOrderOrderRel orderOrderRel : byIds) {
                    expectAmount = carrierOrderOrderRelModelMap.get(orderOrderRel.getId());
                    if (expectAmount != null) {
                        upOrderOrderRel = new TDemandOrderOrderRel();
                        upOrderOrderRel.setId(orderOrderRel.getId());
                        upOrderOrderRel.setArrangedAmount(orderOrderRel.getArrangedAmount().subtract(expectAmount));
                        commonBiz.setBaseEntityModify(orderOrderRel, BaseContextHandler.getUserName());
                        upOrderRelsList.add(upOrderOrderRel);
                    }
                }
            }
            if (ListUtils.isNotEmpty(upOrderRelsList)) {
                tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(upOrderRelsList);
            }
        }

        //更新数据库
        if (ListUtils.isNotEmpty(upDemandOrderGoodsList)) {
            demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(upDemandOrderGoodsList);
        }
        if (ListUtils.isNotEmpty(upDemandOrderList)) {
            demandOrderMapper.batchUpdateByPrimaryKeySelective(upDemandOrderList);
        }


        //同步云盘、云仓更新需求单信息
        List<CarrierOrderSynchronizeModel> synchronizeModels = requestModel.getSynchronizeModels();//同步给云仓的运单数据
        List<CarrierOrderSynchronizeModel> synchronizeToLeYiModels = new ArrayList<>();//同步给云盘的运单数据
        //同步云仓model
        CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel cancelCarrierOrderStataSynchronizeModel = new CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel();

        //回收类型的需求单id list
        List<Long> recycleDemandOrderIdList = demandStateSynchronizeToLeYiModels.stream()
                .map(CancelCarrierOrderChangeDemandStateSynchronizeModel::getDemandOrderId).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(synchronizeModels)) {
            //筛选出回收类型的
            for (CarrierOrderSynchronizeModel model : synchronizeModels) {

                if (recycleDemandOrderIdList.contains(model.getDemandOrderId())) {
                    synchronizeToLeYiModels.add(model);
                }
            }
            cancelCarrierOrderStataSynchronizeModel.setSynchronizeModels(synchronizeModels);
        }

        //同步云仓数据
        cancelCarrierOrderStataSynchronizeModel.setCancelCarrierOrderChangeDemandStateSynchronizeModels(demandStateSynchronizeModels);
        rabbitMqPublishBiz.cancelCarrierOrderSyn(cancelCarrierOrderStataSynchronizeModel, CommonConstant.TWO);

        //同步云盘model
        CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel cancelCarrierOrderStataSynchronizeToLeYiModel = new CancelCarrierOrderChangeDemandAndCarrierStataSynchronizeModel();
        //同步云盘数据
        if (ListUtils.isNotEmpty(demandStateSynchronizeToLeYiModels)) {
            cancelCarrierOrderStataSynchronizeToLeYiModel.setCancelCarrierOrderChangeDemandStateSynchronizeModels(demandStateSynchronizeToLeYiModels);
        }
        if (ListUtils.isNotEmpty(synchronizeToLeYiModels)) {
            cancelCarrierOrderStataSynchronizeToLeYiModel.setSynchronizeModels(synchronizeToLeYiModels);
        }
        if (ListUtils.isNotEmpty(cancelCarrierOrderStataSynchronizeToLeYiModel.getCancelCarrierOrderChangeDemandStateSynchronizeModels())
                || ListUtils.isNotEmpty(cancelCarrierOrderStataSynchronizeToLeYiModel.getSynchronizeModels())) {
            rabbitMqPublishBiz.cancelCarrierOrderSynToLeYi(cancelCarrierOrderStataSynchronizeToLeYiModel, CommonConstant.TWO);
        }
    }

    /**
     * 根据需求单ID修改需求单状态（运单签收时调用）
     *
     * @param requestModel
     */
    @Transactional
    public void updateDemandOrderStatusByIds(UpdateDemandOrderStatusByIdsRequestModel requestModel) {
        List<TDemandOrder> demandOrderList = demandOrderMapper.getByIds(StringUtils.listToString(requestModel.getDemandOrderIdList(), ','));
        if (ListUtils.isEmpty(demandOrderList)) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        Map<Long, BigDecimal> differenceAmountMap = new HashMap<>();
        //判断需求单状态
        for (TDemandOrder order : demandOrderList) {
            if (CommonConstant.INTEGER_ONE.equals(order.getIfCancel())){
                throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_CANCEL);
            }
            if (CommonConstant.INTEGER_ONE.equals(order.getIfEmpty())){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY);
            }
            differenceAmountMap.put(order.getId(), order.getDifferenceAmount());
        }
        List<TDemandOrder> upList = new ArrayList<>();
        List<TDemandOrderOperateLogs> logList = new ArrayList<>();
        List<TDemandOrderEvents> eventsList = new ArrayList<>();
        List<TDemandOrder> signDemandOrderListForRecycle=new ArrayList<>();
        List<Long> signDemandOrderIdList = new ArrayList<>();
        List<Long> signDemandOrderIdListForRecycle = new ArrayList<>();
        Date now = new Date();
        String userName = requestModel.getOperatorName();
        Map<String, TDemandOrder> dbDemandOrderMap = new HashMap<>();//需求单号-》需求单信息
        //同步新生需求单号
        DemandOrderListToYeloLifeModel demandOrderListToYeloLifeModel;
        List<DemandOrderListToYeloLifeModel> codeListSyncLife = new ArrayList<>();

        for (TDemandOrder order : demandOrderList) {
            //每次纠错触发时，修改
            TDemandOrder demandOrder = null;
            if (requestModel.getDifferenceAmount() != null && requestModel.getDifferenceAmount().compareTo(BigDecimal.ZERO) != CommonConstant.INTEGER_ZERO){
                demandOrder=new TDemandOrder();
                demandOrder.setId(order.getId());
                if (differenceAmountMap.get(order.getId()) == null) {
                    demandOrder.setDifferenceAmount(requestModel.getDifferenceAmount());
                }else{
                    demandOrder.setDifferenceAmount(requestModel.getDifferenceAmount().add(differenceAmountMap.get(order.getId())));
                }
            }
            if (order.getEntrustStatus() >= DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey()) {
                demandOrder = demandOrder==null?new TDemandOrder():demandOrder;
                demandOrder.setId(order.getId());
                demandOrder.setEntrustStatus(requestModel.getEntrustStatus());
                demandOrder.setStatusUpdateTime(now);

                if (DemandOrderStatusEnum.SIGN_DISPATCH.getKey().equals(requestModel.getEntrustStatus())) {
                    //签收的需求单id
                    signDemandOrderIdList.add(order.getId());

                    //记录日志、事件
                    TDemandOrderOperateLogs demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(order.getId(), DemandOrderOperateLogsEnum.SIGN_UP, userName, "");
                    logList.add(demandOrderOperateLogs);
                    TDemandOrderEvents upEvent = demandOrderCommonBiz.generateEvent(order.getId(), order.getCompanyCarrierId(), DemandOrderEventsTypeEnum.SIGN_UP, userName);
                    eventsList.add(upEvent);

                    //云盘单子
                    if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(order.getSource())) {
                        if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(order.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(order.getEntrustType())) {
                            if (demandOrder.getDifferenceAmount() != null && requestModel.getDifferenceAmount().compareTo(BigDecimal.ZERO) != CommonConstant.INTEGER_ZERO) {
                                order.setDifferenceAmount(demandOrder.getDifferenceAmount());
                            }
                            signDemandOrderListForRecycle.add(order);
                            signDemandOrderIdListForRecycle.add(order.getId());
                        }
                    }
                    //新生单子
                    else if (DemandOrderSourceEnum.YELO_LIFE.getKey().equals(order.getSource())) {
                        demandOrderListToYeloLifeModel = new DemandOrderListToYeloLifeModel();
                        demandOrderListToYeloLifeModel.setDemandOrderCode(order.getDemandOrderCode());
                        demandOrderListToYeloLifeModel.setCustomerOrderCode(order.getCustomerOrderCode());
                        codeListSyncLife.add(demandOrderListToYeloLifeModel);
                    }
                } else if (DemandOrderStatusEnum.WAIT_SIGN_DISPATCH.getKey().equals(requestModel.getEntrustStatus())) {
                    //记录日志、事件
                    TDemandOrderOperateLogs demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(order.getId(), DemandOrderOperateLogsEnum.WAIT_SIGN_UP, userName, "");
                    logList.add(demandOrderOperateLogs);
                    TDemandOrderEvents upEvent = demandOrderCommonBiz.generateEvent(order.getId(), order.getCompanyCarrierId(), DemandOrderEventsTypeEnum.WAIT_SIGN_UP, userName);
                    eventsList.add(upEvent);
                }
            }
            if(demandOrder!=null){
                upList.add(demandOrder);
            }

            dbDemandOrderMap.put(order.getDemandOrderCode(), order);
        }
        if (ListUtils.isNotEmpty(upList)) {
            demandOrderMapper.batchUpdateByPrimaryKeySelective(upList);
        }
        if (ListUtils.isNotEmpty(logList)) {
            demandOrderOperateLogsMapper.batchInsertSelective(logList);
        }
        if (ListUtils.isNotEmpty(eventsList)) {
            demandOrderEventsMapper.batchInsertSelective(eventsList);
        }
        if (ListUtils.isNotEmpty(signDemandOrderIdList)) {//需求单签收时生成运单结算信息
            //生成需求单结算信息
            CreateSettlementForEntrustConsumerModel createSettlementForEntrustConsumerModel = new CreateSettlementForEntrustConsumerModel();
            createSettlementForEntrustConsumerModel.setDemandOrderIds(StringUtils.listToString(signDemandOrderIdList, ','));
            createSettlementForEntrustConsumerModel.setUserName(userName);
            createSettlementCost(createSettlementForEntrustConsumerModel);

            //同步云盘
            if (ListUtils.isNotEmpty(signDemandOrderListForRecycle)) {
                //需求单签收同步云盘差异数数据mq消息内容
                List<DemandBackCountModel> demandBackCountModelList = new ArrayList<>();
                signDemandOrderListForRecycle.forEach(item -> {
                    if (item.getDifferenceAmount() != null && item.getDifferenceAmount().compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO) {
                        DemandBackCountModel demandBackCountModel = new DemandBackCountModel();
                        demandBackCountModel.setDemandCode(item.getDemandOrderCode());
                        demandBackCountModel.setCompleteBackAmount(-item.getDifferenceAmount().intValue());
                        demandBackCountModelList.add(demandBackCountModel);
                    }
                });
                //有回退数才调用mq
                if (ListUtils.isNotEmpty(demandBackCountModelList)) {
                    DemandBackCountMessage demandBackCountMessage = new DemandBackCountMessage();
                    demandBackCountMessage.setDemandBackCountModelList(demandBackCountModelList);
                    demandBackCountMessage.setUserName(BaseContextHandler.getUserName());
                    log.info("需求单签收同步云盘差异数");
                    rabbitMqPublishBiz.syncBackAmountToLeYi(demandBackCountMessage);

                    //打标的回收入库类型且是自动发布，有回退数量需同步给智慧运营系统
                    TDemandOrder demandOrder;
                    RecyclePublishUpdateDemandRequestModel recyclePublishUpdateDemandRequestModel;
                    List<RecyclePublishUpdateDemandRequestModel> recyclePublishUpdateDemandList = new ArrayList<>();
                    for (DemandBackCountModel backCountModel : demandBackCountModelList) {
                        demandOrder = dbDemandOrderMap.get(backCountModel.getDemandCode());
                        if (demandOrder != null
                                && EntrustTypeEnum.RECYCLE_IN.getKey().equals(demandOrder.getEntrustType())
                                && StringUtils.isNotBlank(demandOrder.getFixedDemand())
                                && CommonConstant.INTEGER_ONE.equals(demandOrder.getAutoPublish())) {

                            recyclePublishUpdateDemandRequestModel = new RecyclePublishUpdateDemandRequestModel();
                            recyclePublishUpdateDemandRequestModel.setConfigCode(demandOrder.getFixedDemand());
                            recyclePublishUpdateDemandRequestModel.setDemandOrderNo(demandOrder.getDemandOrderCode());
                            recyclePublishUpdateDemandRequestModel.setBackspaceNum(ConverterUtils.toBigDecimal(backCountModel.getCompleteBackAmount()));
                            recyclePublishUpdateDemandList.add(recyclePublishUpdateDemandRequestModel);
                        }
                    }
                    if (ListUtils.isNotEmpty(recyclePublishUpdateDemandList)) {
                        commonBiz.synRecyclePublishUpdateDemand(recyclePublishUpdateDemandList);
                    }
                }
            }

            //同步新生
            if (ListUtils.isNotEmpty(codeListSyncLife)) {
                DemandOrderToYeloLifeModel demandOrderToYeloLifeModel = new DemandOrderToYeloLifeModel();
                demandOrderToYeloLifeModel.setItems(codeListSyncLife);
                demandOrderToYeloLifeModel.setUserName(BaseContextHandler.getUserName());
                SyncDemandOrderToYeloLifeModel<Object> syncDemandOrderToYeloLifeModel = new SyncDemandOrderToYeloLifeModel<>();
                syncDemandOrderToYeloLifeModel.setType(SyncDemandOrderToYeloLifeModel.SyncTypeEnum.SIGN_DISPATCH.getKey());
                syncDemandOrderToYeloLifeModel.setMsgData(demandOrderToYeloLifeModel);
                rabbitMqPublishBiz.syncDemandOrderToYeloLife(syncDemandOrderToYeloLifeModel);
            }

            //需求单（HR单）签收同步给地推系统
            if (ListUtils.isNotEmpty(signDemandOrderIdListForRecycle)) {
                demandOrderCommonBiz.demandOrderSignSyncGroundPush(signDemandOrderIdListForRecycle, userName);
            }
        }
    }

    /**
     * 生成需求单结算信息
     *
     * @param model 需求单id
     */
    public void createSettlementCost(CreateSettlementForEntrustConsumerModel model) {
        if (StringUtils.isNotBlank(model.getDemandOrderIds())) {
            //查询需求单信息
            List<TDemandOrder> demandOrderList = demandOrderMapper.getByIds(model.getDemandOrderIds());
            if (ListUtils.isNotEmpty(demandOrderList)) {
                //根据需求单id查询运单信息
                DemandOrderIdsRequestModel demandOrderIdsRequestModel = new DemandOrderIdsRequestModel();
                demandOrderIdsRequestModel.setDemandOrderIds(model.getDemandOrderIds());
                List<GetCarrierOrderResponseModel> carrierOrderList = carrierOrderBiz.getCarrierOrderByDemandOrderIds(demandOrderIdsRequestModel);

                Integer settlementTonnage;
                Integer carrierSettlement;
                Map<Long, BigDecimal> amountMap = new HashMap<>();
                Map<Long, BigDecimal> costMap = new HashMap<>();
                Map<Long, BigDecimal> carrierAmountMap = new HashMap<>();
                Map<Long, BigDecimal> carrierCostMap = new HashMap<>();
                Map<Long, Integer> entrustPriceTypeMap = new HashMap<>();
                Map<Long, Integer> carrierPriceTypeMap = new HashMap<>();
                List<CreateSettlementForCarrierConsumerModel> carrierSettlementList = new ArrayList<>();
                CreateSettlementForCarrierConsumerModel createSettlementForCarrierConsumerModel;

                //遍历运单信息
                for (GetCarrierOrderResponseModel carrier : carrierOrderList) {
                    createSettlementForCarrierConsumerModel = new CreateSettlementForCarrierConsumerModel();
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal price = BigDecimal.ZERO;
                    BigDecimal carrierAmount = BigDecimal.ZERO;
                    BigDecimal carrierPrice = BigDecimal.ZERO;
                    settlementTonnage = carrier.getSettlementTonnage();
                    carrierSettlement = carrier.getCarrierSettlement();
                    //遍历运单货物信息
                    for (GetCarrierOrderGoodsResponseModel goods : carrier.getGoodsList()) {
                        //放空运单，结算数量取预提
                        if(CommonConstant.INTEGER_ONE.equals(carrier.getIfEmpty())){
                            amount = amount.add(goods.getExpectAmount());
                            //放空运单目前仅针对云盘，云盘车主结算数量等于货主结算数量
                            carrierAmount = amount;
                            continue;
                        }

                        //根据结算吨位获取货主结算数量
                        if (settlementTonnage.equals(EntrustSettlementTonnageEnum.EXPECT_AMOUNT.getKey())) {
                            amount = amount.add(goods.getExpectAmount());
                        } else if (settlementTonnage.equals(EntrustSettlementTonnageEnum.LOAD_AMOUNT.getKey())) {
                            amount = amount.add(goods.getLoadAmount());
                        } else if (settlementTonnage.equals(EntrustSettlementTonnageEnum.UNLOAD_AMOUNT.getKey())) {
                            amount = amount.add(goods.getUnloadAmount());
                        } else if (settlementTonnage.equals(EntrustSettlementTonnageEnum.SIGN_AMOUNT.getKey())) {
                            if (goods.getSignAmount() != null && goods.getSignAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                                amount = amount.add(goods.getSignAmount());
                            } else {
                                amount = amount.add(goods.getUnloadAmount());
                            }
                        }

                        //根据结算吨位获取车主结算数量
                        if (carrierSettlement.equals(EntrustSettlementTonnageEnum.EXPECT_AMOUNT.getKey())) {
                            carrierAmount = carrierAmount.add(goods.getExpectAmount());
                        } else if (carrierSettlement.equals(EntrustSettlementTonnageEnum.LOAD_AMOUNT.getKey())) {
                            carrierAmount = carrierAmount.add(goods.getLoadAmount());
                        } else if (carrierSettlement.equals(EntrustSettlementTonnageEnum.UNLOAD_AMOUNT.getKey())) {
                            carrierAmount = carrierAmount.add(goods.getUnloadAmount());
                        } else if (carrierSettlement.equals(EntrustSettlementTonnageEnum.SIGN_AMOUNT.getKey())) {
                            if (goods.getSignAmount() != null && goods.getSignAmount().compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                                carrierAmount = carrierAmount.add(goods.getSignAmount());
                            } else {
                                carrierAmount = carrierAmount.add(goods.getUnloadAmount());
                            }
                        }
                    }

                    //货主费用类型
                    if (entrustPriceTypeMap.get(carrier.getDemandOrderId()) == null){
                        entrustPriceTypeMap.put(carrier.getDemandOrderId(), carrier.getEntrustFreightType());
                    }else if (!entrustPriceTypeMap.get(carrier.getDemandOrderId()).equals(carrier.getEntrustFreightType())){
                        //当需求单下多个运单货主费用类型不一样，则需求单货主结算费用类型为一口价
                        entrustPriceTypeMap.put(carrier.getDemandOrderId(), ContractPriceTypeEnum.FIXED_PRICE.getKey());
                    }
                    //车主费用类型
                    if (carrierPriceTypeMap.get(carrier.getDemandOrderId()) == null){
                        carrierPriceTypeMap.put(carrier.getDemandOrderId(), carrier.getCarrierPriceType());
                    }else if (!carrierPriceTypeMap.get(carrier.getDemandOrderId()).equals(carrier.getCarrierPriceType())){
                        //当需求单下多个运单车主费用类型不一样，则需求单车主结算费用类型为一口价
                        carrierPriceTypeMap.put(carrier.getDemandOrderId(), ContractPriceTypeEnum.FIXED_PRICE.getKey());
                    }

                    //货主结算数量
                    if (amountMap.get(carrier.getDemandOrderId()) == null) {
                        amountMap.put(carrier.getDemandOrderId(), amount);
                    } else {
                        amountMap.put(carrier.getDemandOrderId(), amount.add(amountMap.get(carrier.getDemandOrderId())));
                    }
                    //车主结算数量
                    if (carrierAmountMap.get(carrier.getDemandOrderId()) == null) {
                        carrierAmountMap.put(carrier.getDemandOrderId(), carrierAmount);
                    } else {
                        carrierAmountMap.put(carrier.getDemandOrderId(), carrierAmount.add(carrierAmountMap.get(carrier.getDemandOrderId())));
                    }

                    //货主的费用计算
                    if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(carrier.getDemandOrderSource())) {//乐医托盘
                        price = carrier.getSignFreightFee();
                    } else {
                        if (carrier.getEntrustFreightType().equals(ContractPriceTypeEnum.UNIT_PRICE.getKey())) {
                            price = carrier.getEntrustFreight().multiply(amount).setScale(2, BigDecimal.ROUND_HALF_UP);
                        } else if (carrier.getEntrustFreightType().equals(ContractPriceTypeEnum.FIXED_PRICE.getKey())) {
                            price = carrier.getEntrustFreight();
                        }
                    }
                    //车主的费用计算
                    if (carrier.getCarrierPriceType().equals(ContractPriceTypeEnum.UNIT_PRICE.getKey())) {
                        carrierPrice = carrier.getCarrierPrice().multiply(carrierAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                    } else if (carrier.getCarrierPriceType().equals(ContractPriceTypeEnum.FIXED_PRICE.getKey())) {
                        carrierPrice = carrier.getCarrierPrice();
                    }

                    //需求单货主结算费用
                    if (costMap.get(carrier.getDemandOrderId()) == null) {
                        costMap.put(carrier.getDemandOrderId(), price);
                    } else {
                        costMap.put(carrier.getDemandOrderId(), price.add(costMap.get(carrier.getDemandOrderId())));
                    }
                    //需求单车主结算费用
                    if (carrierCostMap.get(carrier.getDemandOrderId()) == null) {
                        carrierCostMap.put(carrier.getDemandOrderId(), carrierPrice);
                    } else {
                        carrierCostMap.put(carrier.getDemandOrderId(), carrierPrice.add(carrierCostMap.get(carrier.getDemandOrderId())));
                    }

                    //构建运单维度结算信息
                    createSettlementForCarrierConsumerModel.setCarrierOrderId(carrier.getCarrierOrderId());
                    createSettlementForCarrierConsumerModel.setSettlementAmount(amount);
                    createSettlementForCarrierConsumerModel.setPaySettlementAmount(carrierAmount);
                    if(CommonConstant.INTEGER_ONE.equals(carrier.getIfEmpty())){
                        createSettlementForCarrierConsumerModel.setPriceType(FreightTypeEnum.FIXED_PRICE.getKey());
                        createSettlementForCarrierConsumerModel.setPayPriceType(FreightTypeEnum.FIXED_PRICE.getKey());
                    }else{
                        createSettlementForCarrierConsumerModel.setPriceType(carrier.getEntrustFreightType());
                        createSettlementForCarrierConsumerModel.setPayPriceType(carrier.getCarrierPriceType());
                    }
                    createSettlementForCarrierConsumerModel.setUserName(model.getUserName());
                    createSettlementForCarrierConsumerModel.setPaySettlementCost(carrierPrice);
                    createSettlementForCarrierConsumerModel.setSettlementCost(price);
                    carrierSettlementList.add(createSettlementForCarrierConsumerModel);
                }

                //生成需求单结算数据
                List<TDemandPayment> paymentList = new ArrayList<>();
                List<TDemandReceivement> receivementList = new ArrayList<>();
                CreateEntrustSettlementForLeYiConsumerModel leYiConsumerModel;
                List<CreateEntrustSettlementForLeYiConsumerModel> leYiConsumerModelList = new ArrayList<>();
                Date now = new Date();
                for (TDemandOrder item : demandOrderList) {
                    //需求单货主结算
                    TDemandReceivement receivement = new TDemandReceivement();
                    receivement.setDemandOrderId(item.getId());
                    receivement.setPriceType(entrustPriceTypeMap.get(item.getId()));
                    receivement.setSettlementAmount(amountMap.get(item.getId()));
                    receivement.setSettlementCostTotal(costMap.get(item.getId()));
                    commonBiz.setBaseEntityAdd(receivement, model.getUserName());
                    receivementList.add(receivement);

                    //需求单车主结算
                    TDemandPayment payment = new TDemandPayment();
                    payment.setDemandOrderId(item.getId());
                    payment.setPriceType(carrierPriceTypeMap.get(item.getId()));
                    payment.setSettlementAmount(carrierAmountMap.get(item.getId()));
                    payment.setSettlementCostTotal(carrierCostMap.get(item.getId()));
                    commonBiz.setBaseEntityAdd(payment, model.getUserName());
                    paymentList.add(payment);

                    //将需求单货主结算信息同步给托盘
                    if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(item.getSource()) && EntrustTypeEnum.BOOKING.getKey().equals(item.getEntrustType())) {//乐医托盘预约的单子
                        leYiConsumerModel = new CreateEntrustSettlementForLeYiConsumerModel();
                        leYiConsumerModel.setLogisticsDemandCode(item.getDemandOrderCode());
                        leYiConsumerModel.setOperation(model.getUserName());
                        leYiConsumerModel.setOperationTime(now);
                        leYiConsumerModel.setExpenseType(ContractPriceTypeEnum.FIXED_PRICE.getKey());
                        leYiConsumerModel.setCount(amountMap.get(item.getId()));
                        leYiConsumerModel.setAmount(costMap.get(item.getId()));
                        leYiConsumerModelList.add(leYiConsumerModel);
                    }
                }

                //新增
                if (ListUtils.isNotEmpty(paymentList)) {
                    tDemandPaymentMapper.batchInsert(paymentList);
                }
                if (ListUtils.isNotEmpty(receivementList)) {
                    tDemandReceivementMapper.batchInsert(receivementList);
                }

                //生成运单结算信息
                if (ListUtils.isNotEmpty(carrierSettlementList)) {
                    carrierOrderCommonBiz.createSettlement(carrierSettlementList, CommonConstant.ONE);
                }

                //将托盘的需求单货主结算信息同步给托盘
                if (ListUtils.isNotEmpty(leYiConsumerModelList)) {
                    rabbitMqPublishBiz.createDemandSettleSynToLeYi(leYiConsumerModelList);
                }

            }
        }
    }

    /**
     * 第二次纠错触发重新生成需求单结算信息（原结算信息删除）
     *
     * @param model 需求单id
     */
    @Transactional
    public void createSettlementCostForCorrect(CreateSettlementForEntrustCorrectConsumerModel model, Long carrierOrderId) {
        //查询需求单信息
        TDemandOrder dbDemandOrder = demandOrderMapper.selectByPrimaryKeyDecrypt(model.getDemandOrderId());
        if (dbDemandOrder == null) {
            return;
        }
        //根据需求单id查询运单信息
        DemandOrderIdsRequestModel demandOrderIdsRequestModel = new DemandOrderIdsRequestModel();
        demandOrderIdsRequestModel.setDemandOrderIds(dbDemandOrder.getId().toString());
        List<GetCarrierOrderResponseModel> carrierOrderList = carrierOrderBiz.getCarrierOrderByDemandOrderIds(demandOrderIdsRequestModel);

        Map<Long, BigDecimal> amountMap = new HashMap<>();
        Map<Long, BigDecimal> costMap = new HashMap<>();
        Map<Long, Integer> entrustPriceTypeMap = new HashMap<>();
        List<CreateSettlementForCarrierConsumerModel> carrierSettlementList = new ArrayList<>();
        CreateSettlementForCarrierConsumerModel createSettlementForCarrierConsumerModel;
        List<Long> carrierOrderIdList = new ArrayList<>();

        //遍历运单信息
        for (GetCarrierOrderResponseModel carrier : carrierOrderList) {
            BigDecimal amount = BigDecimal.ZERO;
            BigDecimal price = BigDecimal.ZERO;

            //放空运单，结算数量取预提
            if(CommonConstant.INTEGER_ONE.equals(carrier.getIfEmpty())){
                amount = amount.add(carrier.getExpectAmount());
            }else{
                //根据结算吨位获取货主结算数量
                if (carrier.getSettlementTonnage().equals(EntrustSettlementTonnageEnum.EXPECT_AMOUNT.getKey())) {
                    amount = amount.add(carrier.getExpectAmount());
                } else if (carrier.getSettlementTonnage().equals(EntrustSettlementTonnageEnum.LOAD_AMOUNT.getKey())) {
                    amount = amount.add(carrier.getLoadAmount());
                } else if (carrier.getSettlementTonnage().equals(EntrustSettlementTonnageEnum.UNLOAD_AMOUNT.getKey())) {
                    amount = amount.add(carrier.getUnloadAmount());
                } else if (carrier.getSettlementTonnage().equals(EntrustSettlementTonnageEnum.SIGN_AMOUNT.getKey())) {
                    amount = amount.add(carrier.getSignAmount());
                }
            }

            //货主费用类型
            if (entrustPriceTypeMap.get(carrier.getDemandOrderId()) == null){
                entrustPriceTypeMap.put(carrier.getDemandOrderId(), carrier.getEntrustFreightType());
            }else if (!entrustPriceTypeMap.get(carrier.getDemandOrderId()).equals(carrier.getEntrustFreightType())){
                //当需求单下多个运单货主费用类型不一样，则需求单货主结算费用类型为一口价
                entrustPriceTypeMap.put(carrier.getDemandOrderId(), ContractPriceTypeEnum.FIXED_PRICE.getKey());
            }

            //货主结算数量
            if (amountMap.get(carrier.getDemandOrderId()) == null) {
                amountMap.put(carrier.getDemandOrderId(), amount);
            } else {
                amountMap.put(carrier.getDemandOrderId(), amount.add(amountMap.get(carrier.getDemandOrderId())));
            }

            //货主的费用计算
            if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(carrier.getDemandOrderSource())) {//乐医托盘
                price = carrier.getSignFreightFee();
            } else {
                if (carrier.getEntrustFreightType().equals(ContractPriceTypeEnum.UNIT_PRICE.getKey())) {
                    price = carrier.getEntrustFreight().multiply(amount).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (carrier.getEntrustFreightType().equals(ContractPriceTypeEnum.FIXED_PRICE.getKey())) {
                    price = carrier.getEntrustFreight();
                }
            }

            //需求单货主结算费用
            if (costMap.get(carrier.getDemandOrderId()) == null) {
                costMap.put(carrier.getDemandOrderId(), price);
            } else {
                costMap.put(carrier.getDemandOrderId(), price.add(costMap.get(carrier.getDemandOrderId())));
            }

            //构建运单维度结算信息
            createSettlementForCarrierConsumerModel = new CreateSettlementForCarrierConsumerModel();
            createSettlementForCarrierConsumerModel.setCarrierOrderId(carrier.getCarrierOrderId());
            createSettlementForCarrierConsumerModel.setSettlementAmount(amount);
            if(CommonConstant.INTEGER_ONE.equals(carrier.getIfEmpty())){
                createSettlementForCarrierConsumerModel.setPriceType(FreightTypeEnum.FIXED_PRICE.getKey());
            }else{
                createSettlementForCarrierConsumerModel.setPriceType(carrier.getEntrustFreightType());
            }
            createSettlementForCarrierConsumerModel.setUserName(BaseContextHandler.getUserName());
            createSettlementForCarrierConsumerModel.setSettlementCost(price);
            carrierSettlementList.add(createSettlementForCarrierConsumerModel);

            carrierOrderIdList.add(carrier.getCarrierOrderId());
        }

        //删除货主结算数据
        delSettlementCost(dbDemandOrder.getId(), carrierOrderIdList);

        //需求单货主结算
        TDemandReceivement receivement = new TDemandReceivement();
        receivement.setDemandOrderId(dbDemandOrder.getId());
        receivement.setPriceType(entrustPriceTypeMap.get(dbDemandOrder.getId()));
        receivement.setSettlementAmount(amountMap.get(dbDemandOrder.getId()));
        receivement.setSettlementCostTotal(costMap.get(dbDemandOrder.getId()));
        commonBiz.setBaseEntityAdd(receivement, BaseContextHandler.getUserName());
        tDemandReceivementMapper.insertSelective(receivement);

        //生成运单结算信息(生成货主结算信息)
        carrierOrderCommonBiz.createSettlement(carrierSettlementList, CommonConstant.ZERO);

        //修改车主费用
        if (CommonConstant.ONE.equals(model.getIfUpdateCarrierSettlement())) {
            editCarrierOrderCarrierCost(model.getDemandOrderId(), carrierOrderId);
        }
    }
    //删除需求单结算数据
    private void delSettlementCost(Long demandOrderId, List<Long> carrierOrderIdList) {
        //需求单有效
        TDemandReceivement tDemandReceivementUp = new TDemandReceivement();
        tDemandReceivementUp.setDemandOrderId(demandOrderId);
        tDemandReceivementUp.setValid(IfValidEnum.INVALID.getKey());
        commonBiz.setBaseEntityModify(tDemandReceivementUp, BaseContextHandler.getUserName());
        tDemandReceivementMapper.updateByDemandOrderId(tDemandReceivementUp);

        //删除运单结算数据
        TReceivement tReceivementUp;
        List<TReceivement> tReceivementUpList = new ArrayList<>();
        for (Long carrierOrderId : carrierOrderIdList) {
            tReceivementUp = new TReceivement();
            tReceivementUp.setCarrierOrderId(carrierOrderId);
            tReceivementUp.setValid(IfValidEnum.INVALID.getKey());
            commonBiz.setBaseEntityModify(tReceivementUp, BaseContextHandler.getUserName());
            tReceivementUpList.add(tReceivementUp);
        }

        if (ListUtils.isNotEmpty(tReceivementUpList)) {
            tReceivementMapper.batchUpdateByCarrierOrderIds(tReceivementUpList);
        }
    }

    /**
     * 更新车主结算信息
     *
     * @param carrierOrderId 运单ID
     * @param demandOrderId  需求单id
     */
    @Transactional
    public void editCarrierOrderCarrierCost(Long demandOrderId, Long carrierOrderId) {
        //查询最新的运单信息
        TCarrierOrder tCarrierOrder = tCarrierOrderMapper.selectByPrimaryKeyDecrypt(carrierOrderId);
        if (tCarrierOrder == null || IfValidEnum.INVALID.getKey().equals(tCarrierOrder.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
        }
        //当前运单价格类型
        Integer priceType = tCarrierOrder.getCarrierPriceType();
        //当前运单价格
        BigDecimal price = tCarrierOrder.getCarrierPrice();
        //当前运单最新结算数量
        BigDecimal settlementAmount = getGoodsAmount(tCarrierOrder.getCarrierSettlement(), tCarrierOrder);

        //分布式锁,根据需求单维度加锁
        String redisKey = CommonConstant.EDIT_CARRIER_ORDER_PRICE_KEY + tCarrierOrder.getDemandOrderCode();
        String distributedLock = commonBiz.getDistributedLock(redisKey);
        try {
            //查询当前需求单下所有运单
            List<TCarrierOrder> notCancelCarrierOrder = tCarrierOrderMapper.getNotCancelEmptyByDemandOrderId(demandOrderId);

            //需求单下所有有效运单id
            List<Long> demandOrderCarrierOrderIdsList = new ArrayList<>();
            //需求单是否一口价(需求单下面如果有一个运单是一口价那么需求单也是一口价)
            boolean demandOrderCarrierIsFixedPrice = false;
            for (TCarrierOrder carrierOrder : notCancelCarrierOrder) {
                if (FreightTypeEnum.FIXED_PRICE.getKey().equals(carrierOrder.getCarrierPriceType())) {//一口价
                    demandOrderCarrierIsFixedPrice = true;
                }
                demandOrderCarrierOrderIdsList.add(carrierOrder.getId());
            }

            //当前运单结算数据
            TPayment dbPayment = null;


            //需求单下排除当前运单的所有结算费用
            BigDecimal carrierSettlementPriceTotal = BigDecimal.ZERO;
            //需求单下排除当前运单的所有结算数量
            BigDecimal carrierSettlementAmount = BigDecimal.ZERO;
            //查询需求单下所有结算数据
            List<TPayment> tPaymentList = tPaymentMapper.getByCarrierOrderIds(LocalStringUtil.listTostring(demandOrderCarrierOrderIdsList, ','));
            for (TPayment tPayment : tPaymentList) {
                if (tPayment.getCarrierOrderId().equals(carrierOrderId)) {
                    dbPayment = tPayment;
                }else{
                    //计算排除当前运单后的结算费用之和、结算数量之和
                    carrierSettlementPriceTotal = carrierSettlementPriceTotal.add(tPayment.getSettlementCostTotal());
                    carrierSettlementAmount = carrierSettlementAmount.add(tPayment.getSettlementAmount());
                }
            }
            if (dbPayment == null){
                throw new BizException(CarrierDataExceptionEnum.CARRIER_ORDER_NOT_EXIST);
            }

            //查询需求单结算数据
            TDemandPayment tDemandPayment = tDemandPaymentMapper.getByDemandOrderId(demandOrderId);

            //更新运单车主结算信息
            TPayment upPayment = new TPayment();
            upPayment.setId(dbPayment.getId());
            upPayment.setPriceType(priceType);
            if (FreightTypeEnum.FIXED_PRICE.getKey().equals(priceType)) {
                upPayment.setSettlementCostTotal(price);
            }else {
                upPayment.setSettlementCostTotal(settlementAmount.multiply(price));
            }
            upPayment.setSettlementAmount(settlementAmount);

            //更新需求单车主结算信息
            TDemandPayment upDemandPayment = new TDemandPayment();
            upDemandPayment.setId(tDemandPayment.getId());
            if (demandOrderCarrierIsFixedPrice) {
                upDemandPayment.setPriceType(FreightTypeEnum.FIXED_PRICE.getKey());
            } else {
                upDemandPayment.setPriceType(priceType);
            }
            upDemandPayment.setSettlementCostTotal(carrierSettlementPriceTotal.add(upPayment.getSettlementCostTotal()));
            upDemandPayment.setSettlementAmount(carrierSettlementAmount.add(settlementAmount));

            //更新车主结算表
            commonBiz.setBaseEntityModify(upPayment, BaseContextHandler.getUserName());
            tPaymentMapper.updateByPrimaryKeySelective(upPayment);

            commonBiz.setBaseEntityModify(upDemandPayment, BaseContextHandler.getUserName());
            tDemandPaymentMapper.updateByPrimaryKeySelective(upDemandPayment);

        } catch (Exception e) {
            throw e;
        } finally {
            commonBiz.removeDistributedLock(redisKey, distributedLock);
        }
    }

    /**
     * 根据不同结算方式获取货物数量
     *
     * @param settlementTonnage 结算方式
     * @param tCarrierOrder     运单信息
     * @return 货物数量
     */
    private BigDecimal getGoodsAmount(Integer settlementTonnage, TCarrierOrder tCarrierOrder) {
        BigDecimal resultAmount = BigDecimal.ZERO;
        if (CommonConstant.INTEGER_ONE.equals(tCarrierOrder.getIfEmpty())) {
            resultAmount = tCarrierOrder.getExpectAmount();
        } else {
            if (SettlementTonnageEnum.LOAD.getKey().equals(settlementTonnage)) {
                resultAmount = tCarrierOrder.getLoadAmount();
            } else if (SettlementTonnageEnum.UNLOAD.getKey().equals(settlementTonnage)) {
                resultAmount = tCarrierOrder.getUnloadAmount();
            } else if (SettlementTonnageEnum.SIGN.getKey().equals(settlementTonnage)) {
                resultAmount = tCarrierOrder.getSignAmount();
            } else if (SettlementTonnageEnum.EXPECT.getKey().equals(settlementTonnage)) {
                resultAmount = tCarrierOrder.getExpectAmount();
            }
        }
        return resultAmount;
    }

    /**
     * 获取需求单实际结算费用
     *
     * @param demandOrderInfos 需求单信息
     */
    public Map<Long, BigDecimal> getDemandActualFee(List<DemandActualFeeModel> demandOrderInfos) {
        Map<Long, BigDecimal> demandActualFeeMap = new HashMap<>();
        if (ListUtils.isNotEmpty(demandOrderInfos)) {
            List<Long> demandOrderIdList = new ArrayList<>();
            //需求单ID-结算吨位 需求单ID-委托类型(0乐医) 需求单ID-委托数量
            Map<Long, Integer> settlementTonnageMap = new HashMap<>();
            Map<Long, Integer> demandOrderSourceMap = new HashMap<>();
            for (DemandActualFeeModel item : demandOrderInfos) {
                settlementTonnageMap.put(item.getDemandOrderId(), item.getSettlementTonnage());
                demandOrderSourceMap.put(item.getDemandOrderId(), item.getSource());
                demandOrderIdList.add(item.getDemandOrderId());
            }

            //根据需求单ID-查询运单
            DemandOrderIdsRequestModel demandOrderIdsRequestModel = new DemandOrderIdsRequestModel();
            demandOrderIdsRequestModel.setDemandOrderIds(StringUtils.listToString(demandOrderIdList, ','));
            List<GetCarrierOrderResponseModel> result = carrierOrderBiz.getCarrierOrderByDemandOrderIds(demandOrderIdsRequestModel);

            //根据需求单ID分组
            Map<Long, List<GetCarrierOrderResponseModel>> demandOrderCarrierMap = result.stream()
                    .collect(Collectors.groupingBy(GetCarrierOrderResponseModel::getDemandOrderId, Collectors.toList()));

            BigDecimal amount;
            //计算实际费用
            for (Map.Entry<Long, List<GetCarrierOrderResponseModel>> entry : demandOrderCarrierMap.entrySet()) {
                Long tempDemandOrderId = entry.getKey();
                List<GetCarrierOrderResponseModel> carrierOrderList = entry.getValue();
                Integer settlementTonnage = settlementTonnageMap.get(tempDemandOrderId);
                if (ListUtils.isNotEmpty(carrierOrderList) && this.carrierStatusAvailable(carrierOrderList, settlementTonnage)) {
                    BigDecimal price = BigDecimal.ZERO;
                    for (GetCarrierOrderResponseModel carrier : carrierOrderList) {
                        amount = BigDecimal.ZERO;
                        if (CommonConstant.INTEGER_ONE.equals(carrier.getIfEmpty())) {
                            amount = carrier.getExpectAmount();
                        } else {
                            //数量
                            if (settlementTonnage.equals(EntrustSettlementTonnageEnum.EXPECT_AMOUNT.getKey())) {
                                amount = carrier.getExpectAmount();
                            } else if (settlementTonnage.equals(EntrustSettlementTonnageEnum.LOAD_AMOUNT.getKey())) {
                                amount = carrier.getLoadAmount();
                            } else if (settlementTonnage.equals(EntrustSettlementTonnageEnum.UNLOAD_AMOUNT.getKey())) {
                                amount = carrier.getUnloadAmount();
                            } else if (settlementTonnage.equals(EntrustSettlementTonnageEnum.SIGN_AMOUNT.getKey())) {
                                amount = carrier.getSignAmount();
                            }
                        }

                        //费用
                        if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(demandOrderSourceMap.get(carrier.getDemandOrderId()))
                                && (settlementTonnage.equals(EntrustSettlementTonnageEnum.SIGN_AMOUNT.getKey()) || CommonConstant.INTEGER_ONE.equals(carrier.getIfEmpty()))) {//乐医托盘
                            price = price.add(carrier.getSignFreightFee());
                        } else {
                            if (ContractPriceTypeEnum.UNIT_PRICE.getKey().equals(carrier.getEntrustFreightType())) {
                                price = price.add(carrier.getEntrustFreight().multiply(amount).setScale(2, RoundingMode.HALF_UP));
                            } else if (ContractPriceTypeEnum.FIXED_PRICE.getKey().equals(carrier.getEntrustFreightType())) {
                                price = price.add(carrier.getEntrustFreight());
                            }
                        }
                    }
                        demandActualFeeMap.put(entry.getKey(), price);
                    }
                }
            }
        return demandActualFeeMap;
    }

    /**
     * 根据货主结算吨位,判断需求单下所有运单状态，是否可用于计算货主实际结算费用
     * @param carrierOrderList 需求单下的运单列表
     * @param settlementTonnage 货主结算吨位
     * @return
     */
    private boolean carrierStatusAvailable(List<GetCarrierOrderResponseModel> carrierOrderList,Integer settlementTonnage){
        boolean flag = true;
        if(ListUtils.isEmpty(carrierOrderList)){
            return false;
        }
        for (GetCarrierOrderResponseModel tempModel: carrierOrderList) {
            if (settlementTonnage.equals(EntrustSettlementTonnageEnum.LOAD_AMOUNT.getKey())) {
                if(tempModel.getStatus() < CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey()){
                    flag = false;
                }
            } else if (settlementTonnage.equals(EntrustSettlementTonnageEnum.UNLOAD_AMOUNT.getKey())) {
                if(tempModel.getStatus() < CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()){
                    flag = false;
                }
            } else if (settlementTonnage.equals(EntrustSettlementTonnageEnum.SIGN_AMOUNT.getKey())) {
                if (tempModel.getStatus() < CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey()) {
                    flag = false;
                }
            }else{
                //
            }
        }
        return flag;
    }

    /**
     * 需求单列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<DemandOrderResponseModel> searchListManage(DemandOrderSearchRequestModel requestModel, boolean isExport) {
        //如果下单开始时间跟下单结束时间为空的话，默认为当前-90
        if (ListUtils.isEmpty(requestModel.getDemandOrderCodeList()) && ListUtils.isEmpty(requestModel.getCustomerOrderCodeList())) {
            /*非批量查询情况下*/
            if (StringUtils.isBlank(requestModel.getPublishTimeStart()) && StringUtils.isBlank(requestModel.getPublishTimeEnd())) {
                requestModel.setPublishTimeEnd(DateUtils.dateToString(new Date(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
                Calendar now = Calendar.getInstance();
                now.add(Calendar.DAY_OF_MONTH, -90);
                requestModel.setPublishTimeStart(DateUtils.dateToString(now.getTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN));
            }
        } else {
            //以批量查询条件为准,清掉默认的时间条件
            requestModel.setPublishTimeEnd("");
            requestModel.setPublishTimeStart("");
        }

        //入参排序转换
        processSortParams(requestModel);

        if (!isExport) {
            //分页查询需求单id
            requestModel.enablePaging();
        }
        List<Long> idList = demandOrderMapper.searchListManageIds(requestModel);
        PageInfo page = new PageInfo(idList);
        if (ListUtils.isNotEmpty(idList)) {
            String demandOrderIds = StringUtils.listToString(idList, ',');
            //查询需求单信息
            List<DemandOrderResponseModel> demandOrderResponseModels = demandOrderMapper.searchListManageAddressGoodsDemand(requestModel, demandOrderIds);

            List<DemandActualFeeModel> demandOrderActualFeeInfos = new ArrayList<>();//完成调度的需求单id
            for (DemandOrderResponseModel responseModel : demandOrderResponseModels) {
                if (DemandOrderStatusEnum.COMPLETE_DISPATCH.getKey().equals(responseModel.getUseStatus())) {
                    demandOrderActualFeeInfos.add(new DemandActualFeeModel()
                            .setSource(responseModel.getSource())
                            .setSettlementTonnage(responseModel.getSettlementTonnage())
                            .setDemandOrderId(responseModel.getDemandId()));
                }
            }

            //查询需求单结算信息
            List<TDemandReceivement> tDemandReceivementList = tDemandReceivementMapper.getByDemandOrderIds(demandOrderIds);

            //查询货主实际费用
            Map<Long, BigDecimal> actualFeeMap = this.getDemandActualFee(demandOrderActualFeeInfos);

            //组装需求单结算信息
            Map<Long, TDemandReceivement> demandReceivementMap = new HashMap<>();
            if (ListUtils.isNotEmpty(tDemandReceivementList)) {
                for (TDemandReceivement demandOrderResponseModel : tDemandReceivementList) {
                    demandReceivementMap.put(demandOrderResponseModel.getDemandOrderId(), demandOrderResponseModel);
                }
            }

            //拼接数据
            TDemandReceivement tDemandReceivement;
            for (DemandOrderResponseModel responseModel : demandOrderResponseModels) {
                //货主结算费用
                tDemandReceivement = demandReceivementMap.get(responseModel.getDemandId());
                if(tDemandReceivement!=null){
                    responseModel.setPriceType(tDemandReceivement.getPriceType());
                    responseModel.setActualEntrustFee(tDemandReceivement.getSettlementCostTotal());
                }

                //货主实际费用
                if (responseModel.getActualEntrustFee() == null && actualFeeMap.get(responseModel.getDemandId()) != null) {
                    responseModel.setActualEntrustFee(actualFeeMap.get(responseModel.getDemandId()));
                }
            }
            page.setList(demandOrderResponseModels);
        }
        return page;
    }

    //入参排序转换
    private void processSortParams(DemandOrderSearchRequestModel requestModel) {
        String sort = requestModel.getSort();
        String order = requestModel.getOrder();
        if ("desc".equals(order) || "asc".equals(order)) {
            requestModel.setOrder(order);
        } else {
            requestModel.setOrder("asc");
        }

        if ("notArrangedAmount".equals(sort)) {
            requestModel.setSort("(tdo.not_arranged_amount)");
        } else if ("expectedLoadTime".equals(sort)) {
            requestModel.setSort("(tdoa.expected_load_time)");
        } else if ("expectedUnloadTime".equals(sort)) {
            requestModel.setSort("(tdoa.expected_unload_time)");
        } else if ("publishTime".equals(sort)) {
            requestModel.setSort("(tdo.publish_time)");
        } else {
            requestModel.setSort(null);
            requestModel.setOrder(null);
        }
    }

    /**
     * 获取需求单列表统计
     * @param requestModel
     * @return
     */
    public SearchListStatisticsResponseModel searchListStatistics(DemandOrderSearchRequestModel requestModel) {
        return demandOrderMapper.searchListStatistics(requestModel);
    }

    /**
     * 取消需求单
     *
     * @param requestModel
     */
    @Transactional
    public void cancelDemandOrder(DemandOrderCancelRequestModel requestModel) {
        List<GetDemandOrderInfoByIdsModel> list = demandOrderMapper.getDemandOrderInfoByIds(requestModel.getDemandId());
        if (ListUtils.isNotEmpty(list)) {
            //判断需求单状态
            for (GetDemandOrderInfoByIdsModel model : list) {
                //中石化推送的需求单不走此取消方法
                if (DemandOrderSourceEnum.SINOPEC_SYSTEM.getKey().equals(model.getSource()) && DemandOrderOrderTypeEnum.PUSH.getKey().equals(model.getOrderType())) {
                    throw new BizException(EntrustDataExceptionEnum.REQUEST_PARAM_ERROR);
                }
                //已取消不能操作
                if (CommonConstant.INTEGER_ONE.equals(model.getIfCancel())) {
                    throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_CANCEL.getCode(), model.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_IS_CANCEL.getMsg());
                }
                //已放空不能操作
                if (CommonConstant.INTEGER_ONE.equals(model.getIfEmpty())) {
                    throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getCode(), model.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getMsg());
                }
                //竞价中的需求单不能取消
                if (DemandOrderStatusEnum.BIDDING.getKey().equals(model.getEntrustStatus())){
                    throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_BIDDING_NOT_OPERATION.getCode(), model.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_BIDDING_NOT_OPERATION.getMsg());
                }

                //物流系统操作取消
                if (!requestModel.isLeyiOperate()) {
                    //云盘回收类型不能取消（走回退接口）
                    if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(model.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(model.getEntrustType())) {
                        throw new BizException(EntrustDataExceptionEnum.RECYCLE_DEMAND_ORDER_CANCEL_ERROR.getCode(), model.getDemandOrderCode() + EntrustDataExceptionEnum.RECYCLE_DEMAND_ORDER_CANCEL_ERROR.getMsg());
                    }
                    //已回退，不能操作
                    if (CommonConstant.INTEGER_ONE.equals(model.getIfRollback())) {
                        throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getCode(), model.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getMsg());
                    }
                }
                //已调度车辆不能取消
                if (!(DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(model.getEntrustStatus()) || DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(model.getEntrustStatus()))) {
                    throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_NOT_CANCEL.getCode(), model.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_NOT_CANCEL.getMsg());
                }
            }
            //需求单有未处理的异常工单，则不能操作
            Map<Long, Long> workOrderMap = workOrderBiz.checkDemandExceptionExist(requestModel.getDemandId());
            if (!workOrderMap.isEmpty()){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EXCEPTION);
            }
            //查询需求单是否存在异常信息
            List<TDemandOrderObjection> tDemandOrderObjectionList = tDemandOrderObjectionMapper.getByDemandOrderIds(requestModel.getDemandId());
            Map<Long, Long> demandOrderObjectionMap = tDemandOrderObjectionList.stream().collect(Collectors.toMap(TDemandOrderObjection::getDemandOrderId, TDemandOrderObjection::getId));

            Date now = new Date();
            TDemandOrder demandOrder;
            List<TDemandOrder> cancelList = new ArrayList<>();
            TDemandOrderOperateLogs demandOrderOperateLogs;
            List<TDemandOrderOperateLogs> logList = new ArrayList<>();
            List<CancelDemandCodesAndTypeModel> demandOrderCodesLeYi = new ArrayList<>();
            CancelDemandCodesAndTypeModel cancelDemandCodesAndTypeModel;
            List<String> demandOrderCodesRecycle = new ArrayList<>();
            TDemandOrderEvents demandOrderEvents;
            List<TDemandOrderEvents> eventsList = new ArrayList<>();
            TDemandOrderGoods demandOrderGoods;
            List<TDemandOrderGoods> updateGoodList = new ArrayList<>();
            List<TDemandOrderObjection> addDemandOrderObjectionList = new ArrayList<>();
            TDemandOrderObjection demandOrderObjection;
            List<TDemandOrderObjection> upDemandOrderObjectionList = new ArrayList<>();
            CancelDemandOrderBackAmountModel amountModel;
            Map<Long, CancelDemandOrderBackAmountModel> backAmountModelMap = new HashMap<>();
            String remark;
            RecyclePublishUpdateDemandRequestModel recyclePublishUpdateDemandRequestModel;
            List<RecyclePublishUpdateDemandRequestModel> recyclePublishUpdateDemandList = new ArrayList<>();
            List<WorkGroupPushBoModel> workGroupPushBoModels = Lists.newArrayList();
            for (GetDemandOrderInfoByIdsModel model : list) {
                //取消需求单
                demandOrder = new TDemandOrder();
                demandOrder.setId(model.getDemandOrderId());
                demandOrder.setIfCancel(CommonConstant.INTEGER_ONE);
                demandOrder.setCancelReason(requestModel.getCancelReason());
                demandOrder.setCancelType(requestModel.getCancelType());
                demandOrder.setCancelTime(now);
                demandOrder.setNotArrangedAmount(CommonConstant.BIG_DECIMAL_ZERO);
                demandOrder.setBackAmount(model.getNotArrangedAmount().add(model.getBackAmount()));
                commonBiz.setBaseEntityModify(demandOrder, BaseContextHandler.getUserName());
                cancelList.add(demandOrder);

                List<TDemandOrderGoods> demandOrderGoodsList = demandOrderGoodsMapper.getDemandOrderGoodsByDemandOrderIds(ConverterUtils.toString(model.getDemandOrderId()));
                //将物品未安排数量返回到退回数量里
                for (TDemandOrderGoods good : demandOrderGoodsList) {
                    demandOrderGoods = new TDemandOrderGoods();
                    demandOrderGoods.setId(good.getId());
                    demandOrderGoods.setNotArrangedAmount(CommonConstant.BIG_DECIMAL_ZERO);
                    demandOrderGoods.setBackAmount(good.getNotArrangedAmount().add(good.getBackAmount()));
                    commonBiz.setBaseEntityModify(demandOrderGoods, BaseContextHandler.getUserName());
                    updateGoodList.add(demandOrderGoods);
                }
                //新增取消需求单操作日志
                if (requestModel.getCancelType() == null){
                    remark = requestModel.getCancelReason();
                }else{
                    remark = String.format(DemandOrderOperateLogsEnum.CANCEL_DEMAND_ORDER.getFormat(), DemandOrderCancelTypeEnum.getEnum(requestModel.getCancelType()).getValue(), requestModel.getCancelReason());
                }
                demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(model.getDemandOrderId(), DemandOrderOperateLogsEnum.CANCEL_DEMAND_ORDER, BaseContextHandler.getUserName(), remark);
                logList.add(demandOrderOperateLogs);

                //新增取消需求单事件
                demandOrderEvents = demandOrderCommonBiz.generateEvent(model.getDemandOrderId(), model.getCompanyCarrierId(), DemandOrderEventsTypeEnum.CANCEL_DEMANDORDER, BaseContextHandler.getUserName());
                eventsList.add(demandOrderEvents);

                //取消需求单同时取消乐医的需求单、出入库计划
                if (DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(model.getSource())){
                    demandOrderObjection = new TDemandOrderObjection();
                    demandOrderObjection.setDemandOrderId(model.getDemandOrderId());
                    demandOrderObjection.setReportContactName(BaseContextHandler.getUserName());
                    demandOrderObjection.setReportTime(now);
                    demandOrderObjection.setObjectionType(requestModel.getCancelType());
                    demandOrderObjection.setObjectionReason(requestModel.getCancelReason());
                    demandOrderObjection.setCustomerName(model.getUpstreamCustomer());
                    //取消需求单记录异常
                    if (demandOrderObjectionMap.get(model.getDemandOrderId()) != null){

                        demandOrderObjection.setId(demandOrderObjectionMap.get(model.getDemandOrderId()));
                        commonBiz.setBaseEntityModify(demandOrderObjection, BaseContextHandler.getUserName());
                        upDemandOrderObjectionList.add(demandOrderObjection);
                    }else {
                        commonBiz.setBaseEntityAdd(demandOrderObjection, BaseContextHandler.getUserName());
                        addDemandOrderObjectionList.add(demandOrderObjection);
                    }
                    //需求单回退的数量
                    amountModel = new CancelDemandOrderBackAmountModel();
                    amountModel.setCancelBackAmount(model.getNotArrangedAmount());
                    amountModel.setDemandOrderCode(model.getDemandOrderCode());
                    amountModel.setLastModifiedBy(BaseContextHandler.getUserName());
                    backAmountModelMap.put(model.getDemandOrderId(), amountModel);

                    //回收类型：云盘可操作取消，非回收、预约类型：云仓可操作取消
                    if (!requestModel.isLeyiOperate()){
                        cancelDemandCodesAndTypeModel=new CancelDemandCodesAndTypeModel();
                        cancelDemandCodesAndTypeModel.setDemandCode(model.getDemandOrderCode());
                        cancelDemandCodesAndTypeModel.setType(model.getEntrustType());
                        demandOrderCodesLeYi.add(cancelDemandCodesAndTypeModel);
                    }
                    //预约类型同步额外同步云盘系统
                    if (EntrustTypeEnum.BOOKING.getKey().equals(model.getEntrustType())){
                        demandOrderCodesRecycle.add(model.getDemandOrderCode());
                    }

                    //打标的回收入库类型且是自动发布，有回退数量需同步给智慧运营系统
                    if (EntrustTypeEnum.RECYCLE_IN.getKey().equals(model.getEntrustType())
                            && StringUtils.isNotBlank(model.getFixedDemand())
                            && CommonConstant.INTEGER_ONE.equals(model.getAutoPublish())){

                        recyclePublishUpdateDemandRequestModel = new RecyclePublishUpdateDemandRequestModel();
                        recyclePublishUpdateDemandRequestModel.setConfigCode(model.getFixedDemand());
                        recyclePublishUpdateDemandRequestModel.setDemandOrderNo(model.getDemandOrderCode());
                        recyclePublishUpdateDemandRequestModel.setBackspaceNum(model.getNotArrangedAmount());
                        recyclePublishUpdateDemandList.add(recyclePublishUpdateDemandRequestModel);
                    }
                }

                // 构建需求单智能推送取消节点事件Model
                workGroupPushBoModels.add(new WorkGroupPushBoModel()
                        .setOrderId(model.getDemandOrderId())
                        .setOrderSource(model.getSource())
                        .setEntrustTypeGroup(model.getEntrustType())
                        .setProjectLabel(model.getProjectLabel())
                        .setOrderType(WorkGroupOrderTypeEnum.DEMAND_ORDER)
                        .setOrderNode(WorkGroupOrderNodeEnum.DEMAND_ORDER_CANCEL));
            }

            List<TDemandOrderOrderRel> orderRelByDemandIds = tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(requestModel.getDemandId());
            //s单数据 退回数据
            List<TDemandOrderOrderRel> relList = new ArrayList<>();
            List<CancelDemandOrderBackAmountModel> returnBackAmount = new ArrayList<>();

            if (ListUtils.isNotEmpty(orderRelByDemandIds)) {
                Iterator<Map.Entry<Long, CancelDemandOrderBackAmountModel>> iterator = backAmountModelMap.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<Long, CancelDemandOrderBackAmountModel> next = iterator.next();
                    Long key = next.getKey();
                    CancelDemandOrderBackAmountModel value = next.getValue();
                    List<CancelDemandOrderSaleOrderBackAmountModel> amountModels = new ArrayList<>();
                    orderRelByDemandIds.stream().filter(s -> s.getDemandOrderId().equals(key)).forEach(s -> {
                        if ((s.getTotalAmount().subtract(s.getArrangedAmount()).subtract(s.getBackAmount())).compareTo(CommonConstant.BIG_DECIMAL_ZERO) > CommonConstant.INTEGER_ZERO) {
                            TDemandOrderOrderRel orderOrderRel = new TDemandOrderOrderRel();
                            orderOrderRel.setId(s.getId());
                            orderOrderRel.setBackAmount(s.getBackAmount().add(s.getTotalAmount().subtract(s.getArrangedAmount()).subtract(s.getBackAmount())));
                            commonBiz.setBaseEntityModify(orderOrderRel, BaseContextHandler.getUserName());
                            relList.add(orderOrderRel);

                            CancelDemandOrderSaleOrderBackAmountModel backAmountModel = new CancelDemandOrderSaleOrderBackAmountModel();
                            backAmountModel.setOrderBackAmount(s.getTotalAmount().subtract(s.getArrangedAmount()).subtract(s.getBackAmount()));
                            backAmountModel.setOrderId(s.getOrderId());
                            amountModels.add(backAmountModel);
                        }
                    });
                    value.setModels(amountModels);
                    returnBackAmount.add(value);

                }
                if (ListUtils.isNotEmpty(relList)) {
                    tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(relList);
                }
            }

            if (ListUtils.isNotEmpty(cancelList)) {
                demandOrderMapper.batchUpdateByPrimaryKeySelective(cancelList);
            }
            if (ListUtils.isNotEmpty(logList)) {
                demandOrderOperateLogsMapper.batchInsertSelective(logList);
            }
            if (ListUtils.isNotEmpty(eventsList)) {
                demandOrderEventsMapper.batchInsertSelective(eventsList);
            }
            if (ListUtils.isNotEmpty(updateGoodList)) {
                demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(updateGoodList);
            }
            if (ListUtils.isNotEmpty(addDemandOrderObjectionList)){
                tDemandOrderObjectionMapper.batchInsert(addDemandOrderObjectionList);
            }
            if (ListUtils.isNotEmpty(upDemandOrderObjectionList)){
                tDemandOrderObjectionMapper.batchUpdate(upDemandOrderObjectionList);
            }
            if (ListUtils.isNotEmpty(demandOrderCodesLeYi)) {
                CancelDemandByDemandOrderMessage message = new CancelDemandByDemandOrderMessage();
                message.setBackAmountModels(returnBackAmount);
                message.setItems(demandOrderCodesLeYi);
                message.setCancelReason(requestModel.getCancelReason());
                message.setCancelType(requestModel.getCancelType());
                message.setUserName(BaseContextHandler.getUserName());
                rabbitMqPublishBiz.cancelDemandOrderSyn(message);
            }
            if (ListUtils.isNotEmpty(demandOrderCodesRecycle)) {
                CancelDemandByDemandRecoverOrderMessage message = new CancelDemandByDemandRecoverOrderMessage();

                message.setDemandCodeList(demandOrderCodesRecycle);
                message.setCancelReason(requestModel.getCancelReason());
                message.setCancelType(requestModel.getCancelType());
                message.setUserName(BaseContextHandler.getUserName());
                rabbitMqPublishBiz.cancelDemandOrderSynToLeYi(message);
            }

            //打标的回收入库类型，有回退数量需同步给智慧运营系统
            if (ListUtils.isNotEmpty(recyclePublishUpdateDemandList)){
                commonBiz.synRecyclePublishUpdateDemand(recyclePublishUpdateDemandList);
            }
            // 发布智能推送取消节点事件
            applicationContext.publishEvent(new WorkGroupEventModel().setWorkGroupPushBoModels(workGroupPushBoModels));
        }
    }

    /**
     * 需求单查看详情
     *
     * @param requestModel
     * @return
     */
    public DemandOrderDetailResponseModel getDemandOrderDetail(DemandOrderDetailRequestModel requestModel) {
        DemandOrderDetailResponseModel demandOrderDetail = demandOrderMapper.getDemandOrderDetail(requestModel);
        if (demandOrderDetail == null) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }

        GetCarrierOrdersByDemandIdRequestModel getCarrierOrdersByDemandIdRequestModel = new GetCarrierOrdersByDemandIdRequestModel();
        getCarrierOrdersByDemandIdRequestModel.setDemandId(requestModel.getDemandId());
        List<DemandOrderCarrierDetailResponseModel> carrierOrdersModels = carrierOrderCommonBiz.getCarrierOrderInfoByDemandId(getCarrierOrdersByDemandIdRequestModel);

        List<DemandOrderGoodsResponseModel> tDemandOrderGoodsByDemandId = demandOrderGoodsMapper.getTDemandOrderGoodsByDemandId(demandOrderDetail.getDemandId());
        demandOrderDetail.setGoodsResponseModel(tDemandOrderGoodsByDemandId);

        //前台车主查询
        if (CommonConstant.INTEGER_TWO.equals(requestModel.getSource())) {
            Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            demandOrderDetail.setMyCompanyCarrierId(companyCarrierId);
            //车主修改原因
            String demandOrderIdStr = ConverterUtils.toString(demandOrderDetail.getDemandId());
            List<TDemandOrderCarrier> demandCarrierByDemandOrderIds = demandOrderCarrierMapper.getDemandCarrierByDemandOrderIds(demandOrderIdStr, companyCarrierId);
            Map<Long, TDemandOrderCarrier> demandOrderCarrierMap = new HashMap<>();
            demandCarrierByDemandOrderIds.forEach(tmp -> {
                if (demandOrderCarrierMap.get(tmp.getDemandOrderId()) == null) {
                    demandOrderCarrierMap.put(tmp.getDemandOrderId(), tmp);
                } else {
                    if (demandOrderCarrierMap.get(tmp.getDemandOrderId()).getCreatedTime().compareTo(tmp.getCreatedTime()) < 0) {
                        demandOrderCarrierMap.put(tmp.getDemandOrderId(), tmp);
                    }
                }
            });
            TDemandOrderCarrier tDemandOrderCarrier = demandOrderCarrierMap.get(demandOrderDetail.getDemandId());
            if (tDemandOrderCarrier != null) {
                demandOrderDetail.setModifyCarrierReason(tDemandOrderCarrier.getCancelReason());
                demandOrderDetail.setModifyCarrierPriceType(tDemandOrderCarrier.getCarrierPriceType());
                demandOrderDetail.setModifyCarrierPrice(tDemandOrderCarrier.getCarrierPrice());
            }
            demandOrderDetail.setIsOurCompany(CommonConstant.INTEGER_TWO);
            //不是这个车主的运单不能显示
            List<DemandOrderCarrierDetailResponseModel> carrierOrdersModelList = new ArrayList<>();
            for (DemandOrderCarrierDetailResponseModel item : carrierOrdersModels) {
                if (companyCarrierId.equals(item.getCompanyCarrierId())) {
                    carrierOrdersModelList.add(item);
                }
            }
            demandOrderDetail.setCarrierResponseModel(MapperUtils.mapper(carrierOrdersModelList, DemandOrderCarrierResponseModel.class));

            //需求单事件
            List<DemandOrderEventResponseModel> demandOrderEvents = tDemandOrderEventsMapper.getDemandOrderEventsByDemandId(demandOrderDetail.getDemandId(), companyCarrierId);
            demandOrderDetail.setEventResponseModel(demandOrderEvents);
        }else{
            demandOrderDetail.setCarrierResponseModel(MapperUtils.mapper(carrierOrdersModels, DemandOrderCarrierResponseModel.class));
        }
        return demandOrderDetail;
    }

    /**
     * 需求单日志
     *
     * @param demandOrderId
     * @return
     */
    public List<GetDemandOrderLogsResponseModel> getDemandOrderLogs(Long demandOrderId) {
        return demandOrderOperateLogsMapper.getDemandOrderLogs(demandOrderId);
    }

    /**
     * 复制发布详情接口
     *
     * @param demandOrderIdRequestModel
     * @return
     */
    public GetPublishDemandOrderDetailResponseModel getPublishDemandOrderDetail(DemandOrderIdRequestModel demandOrderIdRequestModel) {
        GetPublishDemandOrderDetailResponseModel publishDemandOrderDetail = demandOrderMapper.getPublishDemandOrderDetail(demandOrderIdRequestModel.getDemandOrderId());

        //查询需求单的接单车主是我司还是其他车主
        TCompanyCarrier tCompanyCarrier = tCompanyCarrierMapper.selectByPrimaryKey(publishDemandOrderDetail.getCompanyCarrierId());
        if (tCompanyCarrier != null && IfValidEnum.VALID.getKey().equals(tCompanyCarrier.getValid())) {
            publishDemandOrderDetail.setIsOurCompany(tCompanyCarrier.getLevel());
        }
        return publishDemandOrderDetail;
    }

    /**
     * 运单提货同步退回数量到需求单
     * @param syncModels
     */
    @Transactional
    public List<LogisticsDemandStateSynchronizeModel> syncCarrierLoadBackAmount(List<CarrierOrderLoadAmountSyncModel> syncModels){
        if(ListUtils.isEmpty(syncModels)){
            return new ArrayList<>();
        }
        List<Long> demandOrderIdList = new ArrayList<>();
        Map<Long,BigDecimal> demandBackAmountMap = new HashMap<>();
        Map<Long,BigDecimal> demandGoodsBackAmountMap = new HashMap<>();
        Map<String,BigDecimal> orderBackAmountMap = new HashMap<>();

        for (CarrierOrderLoadAmountSyncModel tempModel : syncModels) {
            demandOrderIdList.add(tempModel.getDemandOrderId());
            //需求单退回数据累加
            BigDecimal tempBackAmount = Optional.ofNullable(tempModel.getBackAmount()).orElse(BigDecimal.ZERO);
            BigDecimal tempMapBackAmount = Optional.ofNullable(demandBackAmountMap.get(tempModel.getDemandOrderId())).orElse(BigDecimal.ZERO);
            demandBackAmountMap.put(tempModel.getDemandOrderId(),tempBackAmount.add(tempMapBackAmount));

            if(ListUtils.isNotEmpty(tempModel.getCarrierOrderRelList())){
                for (CarrierOrderOrderRelSyncModel tempOrderBackModel : tempModel.getCarrierOrderRelList()) {
                    //运单关联S单数据累加
                    BigDecimal tempOrderBackAmount = Optional.ofNullable(tempOrderBackModel.getCount()).orElse(BigDecimal.ZERO);
                    BigDecimal tempMapOrderBackAmount = Optional.ofNullable(orderBackAmountMap.get(tempModel.getDemandOrderId()+"-"+tempOrderBackModel.getOrderId())).orElse(BigDecimal.ZERO);
                    orderBackAmountMap.put(tempModel.getDemandOrderId()+"-"+tempOrderBackModel.getOrderId(),tempOrderBackAmount.add(tempMapOrderBackAmount));
                }
            }

            if(ListUtils.isNotEmpty(tempModel.getGoodsList())){
                for (CarrierOrderLoadGoodsAmountSyncModel tempGoodsModel : tempModel.getGoodsList()) {
                    //运单货物数量累加
                    BigDecimal tempGoodsBackAmount = Optional.ofNullable(tempGoodsModel.getBackAmount()).orElse(BigDecimal.ZERO);
                    BigDecimal tempMapGoodsBackAmount = Optional.ofNullable(demandGoodsBackAmountMap.get(tempGoodsModel.getGoodsId())).orElse(BigDecimal.ZERO);
                    demandGoodsBackAmountMap.put(tempGoodsModel.getGoodsId(),tempGoodsBackAmount.add(tempMapGoodsBackAmount));
                }
            }
        }

        List<TDemandOrder> dbDemandOrderList = demandOrderMapper.getByIds(StringUtils.listToString(demandOrderIdList,','));
        if(ListUtils.isEmpty(dbDemandOrderList)){
            return new ArrayList<>();
        }

        //更新需求单退回数量信息
        BigDecimal backAmount;
        BigDecimal dbBackAmount;
        TDemandOrder upTDemandOrder;
        List<TDemandOrder> upTDemandOrderList = new ArrayList<>();
        Map<Long, Integer> entrustTypeMap = new HashMap<>();
        for (TDemandOrder dbTDemandOrder : dbDemandOrderList) {
            entrustTypeMap.put(dbTDemandOrder.getId(), dbTDemandOrder.getEntrustType());
            //回收类型待需求单签收时以差异数当作回退数进行回退
            if (!EntrustTypeEnum.RECYCLE_IN.getKey().equals(dbTDemandOrder.getEntrustType()) && !EntrustTypeEnum.RECYCLE_OUT.getKey().equals(dbTDemandOrder.getEntrustType())) {
                backAmount = Optional.ofNullable(demandBackAmountMap.get(dbTDemandOrder.getId())).orElse(BigDecimal.ZERO);
                dbBackAmount = Optional.ofNullable(dbTDemandOrder.getBackAmount()).orElse(BigDecimal.ZERO);

                upTDemandOrder = new TDemandOrder();
                upTDemandOrder.setId(dbTDemandOrder.getId());
                upTDemandOrder.setBackAmount(dbBackAmount.add(backAmount));
                commonBiz.setBaseEntityModify(upTDemandOrder, BaseContextHandler.getUserName());
                upTDemandOrderList.add(upTDemandOrder);
            }
        }
        if(ListUtils.isNotEmpty(upTDemandOrderList)){
            demandOrderMapper.batchUpdateByPrimaryKeySelective(upTDemandOrderList);
        }

        //更新货物退回数量信息
        List<TDemandOrderGoods> dbDemandOrderGoodsList = demandOrderGoodsMapper.getDemandOrderGoodsByDemandOrderIds(StringUtils.listToString(demandOrderIdList,','));
        TDemandOrderGoods upTDemandOrderGoods;
        List<TDemandOrderGoods>  upTDemandOrderGoodsList = new ArrayList<>();
        for (TDemandOrderGoods temp : dbDemandOrderGoodsList) {
            //回收类型待需求单签收时以差异数当作回退数进行回退
            if(demandGoodsBackAmountMap.get(temp.getId()) != null && !EntrustTypeEnum.RECYCLE_IN.getKey().equals(entrustTypeMap.get(temp.getDemandOrderId())) && !EntrustTypeEnum.RECYCLE_OUT.getKey().equals(entrustTypeMap.get(temp.getDemandOrderId()))){
                upTDemandOrderGoods = new TDemandOrderGoods();
                upTDemandOrderGoods.setId(temp.getId());
                upTDemandOrderGoods.setBackAmount(temp.getBackAmount().add(demandGoodsBackAmountMap.get(temp.getId())));
                commonBiz.setBaseEntityModify(upTDemandOrderGoods,BaseContextHandler.getUserName());
                upTDemandOrderGoodsList.add(upTDemandOrderGoods);
            }
        }
        if(ListUtils.isNotEmpty(upTDemandOrderGoodsList)){
            demandOrderGoodsMapper.batchUpdateByPrimaryKeySelective(upTDemandOrderGoodsList);
        }

        //根据需求单ID分组货物信息、S单信息
        Map<Long,List<TDemandOrderGoods>> dbDemandOrderGoodsMap  = dbDemandOrderGoodsList.stream().collect(Collectors.groupingBy(TDemandOrderGoods :: getDemandOrderId,Collectors.toList()));
        List<TDemandOrderOrderRel> dbDemandOrderRelList = tDemandOrderOrderRelMapper.getDemandOrderOrderRelByDemandIds(StringUtils.listToString(demandOrderIdList,','));
        Map<Long,List<TDemandOrderOrderRel>> dbDemandOrderOderRelMap = dbDemandOrderRelList.stream().collect(Collectors.groupingBy(TDemandOrderOrderRel :: getDemandOrderId,Collectors.toList()));

        //更新S单数量信息
        TDemandOrderOrderRel upOrderRel;
        List<TDemandOrderOrderRel> upOrderRelList = new ArrayList<>();
        Map<Long,List<CompleteDemandOrderCarrierOrderBackAmountModel>> amountModelsMap = new HashMap<>();
        for (TDemandOrder dbTDemandOrder : dbDemandOrderList) {
            CompleteDemandOrderCarrierOrderBackAmountModel backAmountModel;
            List<CompleteDemandOrderCarrierOrderBackAmountModel> amountModels = new ArrayList<>();
            amountModelsMap.put(dbTDemandOrder.getId(),amountModels);

            List<TDemandOrderOrderRel> demandOrderRelList = dbDemandOrderOderRelMap.get(dbTDemandOrder.getId());
            if (ListUtils.isNotEmpty(demandOrderRelList) && !EntrustTypeEnum.RECYCLE_IN.getKey().equals(dbTDemandOrder.getEntrustType()) && !EntrustTypeEnum.RECYCLE_OUT.getKey().equals(dbTDemandOrder.getEntrustType())) {//回收类型待需求单签收时以差异数当作回退数进行回退
                for (TDemandOrderOrderRel tempRel : demandOrderRelList) {
                    log.info("更新S单数量信息 tempRel:" + tempRel.toString());
                    BigDecimal orderRelBackAmount = orderBackAmountMap.get(dbTDemandOrder.getId() + "-" + tempRel.getOrderId());
                    if (orderRelBackAmount != null && orderRelBackAmount.compareTo(BigDecimal.ZERO) > CommonConstant.INTEGER_ZERO) {
                        log.info("更新S单数量信息 tempRel:" + tempRel.toString());
                        upOrderRel = new TDemandOrderOrderRel();
                        upOrderRel.setId(tempRel.getId());
                        upOrderRel.setBackAmount(tempRel.getBackAmount().add(orderRelBackAmount));
                        upOrderRel.setArrangedAmount(tempRel.getArrangedAmount().subtract(orderRelBackAmount));
                        commonBiz.setBaseEntityModify(upOrderRel, BaseContextHandler.getUserName());
                        log.info("更新后S单数量信息 upOrderRel:" + upOrderRel.toString());
                        upOrderRelList.add(upOrderRel);
                    }
                    //返回数量
                    backAmountModel = new CompleteDemandOrderCarrierOrderBackAmountModel();
                    backAmountModel.setOrderBackAmount(Optional.ofNullable(orderRelBackAmount).orElse(BigDecimal.ZERO));
                    backAmountModel.setOrderId(tempRel.getOrderId());
                    amountModels.add(backAmountModel);
                }
            }
        }
        if(ListUtils.isNotEmpty(upOrderRelList)){
            tDemandOrderOrderRelMapper.batchUpdateByPrimaryKeySelective(upOrderRelList);
        }

        //需求单已完成,预约类型非预约类型区分发送消息
        List<LogisticsDemandStateSynchronizeModel> demandStateSynchronizeModels = new ArrayList<>();
        for (TDemandOrder dbTDemandOrder : dbDemandOrderList) {
            //回收类型待需求单签收时以差异数当作回退数进行回退
            if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(dbTDemandOrder.getSource()) || EntrustTypeEnum.RECYCLE_IN.getKey().equals(dbTDemandOrder.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(dbTDemandOrder.getEntrustType())){
                continue;
            }
            LogisticsDemandStateSynchronizeModel  synchronizeModel = new LogisticsDemandStateSynchronizeModel();
            synchronizeModel.setDemandOrderId(dbTDemandOrder.getId());
            synchronizeModel.setDemandState(dbTDemandOrder.getStatus());
            synchronizeModel.setDemandCode(dbTDemandOrder.getDemandOrderCode());
            synchronizeModel.setCompleteBackAmount(demandBackAmountMap.get(dbTDemandOrder.getId()));
            synchronizeModel.setLastModifiedBy(BaseContextHandler.getUserName());

            //处理云仓sku 数据
            List<ProductTypeAndBackAmountModel> productTypeAndBackAmountModels=new ArrayList<>();
            List<TDemandOrderGoods> demandOrderGoods = dbDemandOrderGoodsMap.get(dbTDemandOrder.getId());
            ProductTypeAndBackAmountModel productTypeAndBackAmountModel;
            for (TDemandOrderGoods demandOrderGood : demandOrderGoods) {
                productTypeAndBackAmountModel=new ProductTypeAndBackAmountModel();
                productTypeAndBackAmountModel.setProductTypeCode(demandOrderGood.getSkuCode());
                productTypeAndBackAmountModel.setCategoryName(demandOrderGood.getCategoryName());
                productTypeAndBackAmountModel.setSortName(demandOrderGood.getGoodsName());
                productTypeAndBackAmountModel.setHeight(ConverterUtils.toString(demandOrderGood.getHeight()));
                productTypeAndBackAmountModel.setLength(ConverterUtils.toString(demandOrderGood.getLength()));
                productTypeAndBackAmountModel.setWidth(ConverterUtils.toString(demandOrderGood.getWidth()));
                productTypeAndBackAmountModel.setCompleteBackAmount(Optional.ofNullable(demandGoodsBackAmountMap.get(demandOrderGood.getId())).orElse(BigDecimal.ZERO).intValue());

                productTypeAndBackAmountModels.add(productTypeAndBackAmountModel);
            }
            synchronizeModel.setProductTypeModel(productTypeAndBackAmountModels);
            demandStateSynchronizeModels.add(synchronizeModel);
            //非预约单回退S单信息
            if(!EntrustTypeEnum.BOOKING.getKey().equals(dbTDemandOrder.getEntrustType())){
                synchronizeModel.setModels(Optional.ofNullable(amountModelsMap.get(dbTDemandOrder.getId())).orElse(new ArrayList<>()));
            }else{
                //预约回退货物信息
                CompleteDemandOrderGoodsBackAmountModel goodsBackAmountModel;
                List<CompleteDemandOrderGoodsBackAmountModel> goodsBackList = new ArrayList<>();
                List<TDemandOrderGoods> demandOrderGoodsList = dbDemandOrderGoodsMap.get(dbTDemandOrder.getId());
                for (TDemandOrderGoods temp : demandOrderGoodsList) {
                    goodsBackAmountModel = new CompleteDemandOrderGoodsBackAmountModel();
                    goodsBackAmountModel.setGoodsId(temp.getId());
                    goodsBackAmountModel.setArrangedAmount(temp.getArrangedAmount());
                    goodsBackAmountModel.setNotArrangedAmount(temp.getNotArrangedAmount());
                    goodsBackAmountModel.setFallbackCount(demandGoodsBackAmountMap.get(temp.getId()));
                    goodsBackList.add(goodsBackAmountModel);
                }
                if(ListUtils.isNotEmpty(goodsBackList)){
                    synchronizeModel.setTLogisticsDemandGoodsItemModels(goodsBackList);
                }
            }
        }

        return demandStateSynchronizeModels;
    }

    /**
     *
     * 同步委托单到网络货运
     * @param requestModel
     *
     * */
    @Transactional
    public void confirmSynNetworkFreight(DemandOrderIdListRequestModel requestModel) {
        if(ListUtils.isEmpty(requestModel.getDemandOrderIdList())){
            return;
        }
        if (requestModel.getDemandOrderIdList().size() > CommonConstant.INTEGER_TWENTY) {
            throw new BizException(EntrustDataExceptionEnum.SYNC_DEMANDORDER_MAX);
        }
        String demandOrderIds = StringUtils.listToString(requestModel.getDemandOrderIdList(), ',');
        List<TDemandOrder> demandOrderList = demandOrderMapper.getByIds(demandOrderIds);
        if (ListUtils.isEmpty(demandOrderList)) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        //判断需求单状态
        for (TDemandOrder demandOrder : demandOrderList) {
            if (!DemandOrderSourceEnum.LEYI_TRAY.getKey().equals(demandOrder.getSource()) || EntrustTypeEnum.DEFAULT.getKey().equals(demandOrder.getEntrustType())) {
                //非乐医委托单，或非乐医需求单
                throw new BizException(EntrustDataExceptionEnum.NOT_LEYI_DEMAND_ORDER.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.NOT_LEYI_DEMAND_ORDER.getMsg());
            } else if (!DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(demandOrder.getEntrustStatus())) {
                //未发布
                throw new BizException(EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR.getMsg());
            } else if (CommonConstant.INTEGER_ONE.equals(demandOrder.getIfCancel())) {
                //已取消
                throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_CANCEL.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMANDORDER_CANCEL.getMsg());
            } else if (CommonConstant.INTEGER_ONE.equals(demandOrder.getIfEmpty())) {
                //已放空
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_EMPTY.getMsg());
            } else if (CommonConstant.INTEGER_ONE.equals(demandOrder.getIfRollback())) {
                //已回退
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.DEMAND_ORDER_IS_ROLLBACK.getMsg());
            } else if (StringUtils.isNotBlank(demandOrder.getFixedDemand())) {
                //固定需求
                throw new BizException(EntrustDataExceptionEnum.FIXED_DEMAND_ORDER_NOT_SYNC.getCode(), demandOrder.getDemandOrderCode() + EntrustDataExceptionEnum.FIXED_DEMAND_ORDER_NOT_SYNC.getMsg());
            }
        }
        //需求单有未处理的异常工单，则不能操作
        Map<Long, Long> workOrderMap = workOrderBiz.checkDemandExceptionExist(demandOrderIds);
        if (!workOrderMap.isEmpty()){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_EXCEPTION);
        }

        List<TDemandOrderOperateLogs> logList=new ArrayList<>();
        TDemandOrderOperateLogs logs;
        Date date=new Date();
        for(TDemandOrder demandOrder:demandOrderList){
            //添加操作日志 便于恢复后显示该操作日志
            logs = new TDemandOrderOperateLogs();
            logs.setDemandOrderId(demandOrder.getId());
            logs.setOperationType(DemandOrderOperateLogsEnum.SYNC_DEMAND_ORDER.getKey());
            logs.setOperationContent(DemandOrderOperateLogsEnum.SYNC_DEMAND_ORDER.getValue());
            logs.setRemark("");
            logs.setOperatorName(BaseContextHandler.getUserName());
            logs.setOperateTime(date);
            commonBiz.setBaseEntityAdd(logs, BaseContextHandler.getUserName());
            logList.add(logs);
        }
        demandOrderOperateLogsMapper.batchInsertSelective(logList);
        List<SyncTMSDemandOrderModel> orderModel = demandOrderMapper.getSyncTMSDemandOrderModelById(demandOrderIds);
        rabbitMqPublishBiz.confirmSynNetworkFreight(orderModel);
        //同步后删除tms需求单等信息
        demandOrderMapper.delDemandOrderAddressGoodsLogsByDemandOrderIds(demandOrderIds,BaseContextHandler.getUserName(),date);
    }

    /**
     *
     * 撤回需求单信息（不直接将删除的单子valid置为1进行恢复的原因是，如果涉及到刷数据后再恢复有可能出问题）
     * @param model
     * */
    @Transactional
    public void withdrawOrder(SyncLogisticsWithdrawOrderModel model) {
        TDemandOrder oldDemandOrder = demandOrderMapper.getInvalidTopByDemandOrderCode(model.getDemandOrderCode());
        if(oldDemandOrder == null){
            return;
        }

        TDemandOrderAddress tDemandOrderAddress = demandOrderAddressMapper.getInvalidByDemandOrderId(oldDemandOrder.getId());
        List<TDemandOrderGoods> tDemandOrderGoodsList = demandOrderGoodsMapper.getInvalidByDemandOrderId(oldDemandOrder.getId());
        List<TDemandOrderGoodsRel> tDemandOrderGoodsRelList = tDemandOrderGoodsRelMapper.getInvalidByDemandOrderId(oldDemandOrder.getId());
        List<TDemandOrderOrderRel> tDemandOrderOrderRelList = tDemandOrderOrderRelMapper.getInvalidByDemandOrderId(oldDemandOrder.getId());
        List<TDemandOrderOperateLogs> tDemandOrderOperateLogsList = demandOrderOperateLogsMapper.getInvalidByDemandOrderId(oldDemandOrder.getId());

        //新增需求单
        TDemandOrder newDemandOrder = MapperUtils.mapper(oldDemandOrder,TDemandOrder.class);
        newDemandOrder.setId(null);
        newDemandOrder.setValid(IfValidEnum.VALID.getKey());
        //回收类型需求单，更新货物数量
        if(EntrustTypeEnum.RECYCLE_IN.getKey().equals(oldDemandOrder.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(oldDemandOrder.getEntrustType())) {
            newDemandOrder.setGoodsAmount(model.getGoodsAmount());
            newDemandOrder.setNotArrangedAmount(model.getGoodsAmount());
            //回收类型单货物  更新需求单货物表 货物数量
            TDemandOrderGoods tDemandOrderGoods = tDemandOrderGoodsList.get(CommonConstant.INTEGER_ZERO);
            if(tDemandOrderGoods!=null){
                tDemandOrderGoods.setGoodsAmount(model.getGoodsAmount());
                tDemandOrderGoods.setNotArrangedAmount(model.getGoodsAmount());
            }
        }
        commonBiz.setBaseEntityModify(newDemandOrder,model.getOperateName());
        demandOrderMapper.insertSelectiveEncrypt(newDemandOrder);

        //新增需求单地址
        TDemandOrderAddress demandOrderAddress=MapperUtils.mapper(tDemandOrderAddress,TDemandOrderAddress.class);
        demandOrderAddress.setId(null);
        demandOrderAddress.setValid(IfValidEnum.VALID.getKey());
        demandOrderAddress.setDemandOrderId(newDemandOrder.getId());
        demandOrderAddress.setCreatedTime(oldDemandOrder.getCreatedTime());
        demandOrderAddress.setCreatedBy(oldDemandOrder.getCreatedBy());
        commonBiz.setBaseEntityModify(demandOrderAddress,model.getOperateName());
        demandOrderAddressMapper.insertSelective(demandOrderAddress);

        //新增需求单货物
        Map<Long,Long> goodsIdMap=new HashMap<>();
        for (TDemandOrderGoods orderGoods : tDemandOrderGoodsList) {
            Long oldOrderGoodsId=orderGoods.getId();
            orderGoods.setId(null);
            orderGoods.setDemandOrderId(newDemandOrder.getId());
            orderGoods.setCreatedBy(oldDemandOrder.getCreatedBy());
            orderGoods.setCreatedTime(oldDemandOrder.getCreatedTime());
            commonBiz.setBaseEntityModify(orderGoods,model.getOperateName());
            orderGoods.setValid(IfValidEnum.VALID.getKey());
            demandOrderGoodsMapper.insertSelective(orderGoods);

            goodsIdMap.put(oldOrderGoodsId,orderGoods.getId());
        }

        //新增s单货物关系
        if(EntrustTypeEnum.RECYCLE_IN.getKey().equals(oldDemandOrder.getEntrustType()) || EntrustTypeEnum.RECYCLE_OUT.getKey().equals(oldDemandOrder.getEntrustType())) {
            List<WithdrawDemandRelModel> withdrawDemandRelModelList = model.getWithdrawDemandRelModelList();
            if(ListUtils.isNotEmpty(withdrawDemandRelModelList)){
                List<TDemandOrderOrderRel> insertDemandOrderOrderRelList=new ArrayList<>();
                TDemandOrderOrderRel rel;
                for (WithdrawDemandRelModel withdrawDemandRelModel : withdrawDemandRelModelList) {
                    rel = new TDemandOrderOrderRel();
                    rel.setDemandOrderId(newDemandOrder.getId());
                    rel.setOrderId(withdrawDemandRelModel.getOrderId());
                    rel.setOrderCode(withdrawDemandRelModel.getOrderCode());
                    rel.setTotalAmount(withdrawDemandRelModel.getTotalAmount());
                    rel.setRelType(withdrawDemandRelModel.getRelType());
                    rel.setRemark(withdrawDemandRelModel.getRemark());
                    rel.setCreatedBy(withdrawDemandRelModel.getCreatedBy());
                    rel.setCreatedTime(withdrawDemandRelModel.getCreatedTime());
                    commonBiz.setBaseEntityModify(rel,model.getOperateName());
                    insertDemandOrderOrderRelList.add(rel);
                }
                tDemandOrderOrderRelMapper.batchInsertDemandOrderOrderRelSelective(insertDemandOrderOrderRelList);
            }
        }else{
            if (ListUtils.isNotEmpty(tDemandOrderOrderRelList)) {
                tDemandOrderOrderRelList.forEach(orderRel -> {
                    orderRel.setId(null);
                    orderRel.setDemandOrderId(newDemandOrder.getId());
                    orderRel.setCreatedBy(oldDemandOrder.getCreatedBy());
                    orderRel.setCreatedTime(oldDemandOrder.getCreatedTime());
                    orderRel.setValid(IfValidEnum.VALID.getKey());
                    commonBiz.setBaseEntityModify(orderRel, model.getOperateName());
                });
                tDemandOrderOrderRelMapper.batchInsertDemandOrderOrderRelSelective(tDemandOrderOrderRelList);
            }
        }

        //新增预约单货物关系
        if(ListUtils.isNotEmpty(tDemandOrderGoodsRelList)){
            tDemandOrderGoodsRelList.forEach(item->{
                item.setId(null);
                item.setDemandOrderGoodsId(goodsIdMap.get(item.getDemandOrderGoodsId()));
                item.setValid(IfValidEnum.VALID.getKey());
                commonBiz.setBaseEntityModify(item,model.getOperateName());
            });
            tDemandOrderGoodsRelMapper.batchInsert(tDemandOrderGoodsRelList);
        }
        //新增日志
        tDemandOrderOperateLogsList.forEach(logs->{
            logs.setId(null);
            logs.setDemandOrderId(newDemandOrder.getId());
            logs.setValid(IfValidEnum.VALID.getKey());
            commonBiz.setBaseEntityModify(logs,model.getOperateName());
        });
        //添加记录撤回日志
        TDemandOrderOperateLogs logs = demandOrderCommonBiz.getDemandOrderOperateLogs(newDemandOrder.getId(), DemandOrderOperateLogsEnum.WITHDRAW_DEMAND_ORDER, model.getOperateName(), model.getWithdrawReason());
        tDemandOrderOperateLogsList.add(logs);
        demandOrderOperateLogsMapper.batchInsertSelective(tDemandOrderOperateLogsList);
    }

    /**
     * 托盘取消出入库计划和取消预约需求单查询可不可以取消（托盘调用）
     * @param requestModel
     */
    public void cancelDemandOrderVerifyFromLeYi(CancelDemandOrderVerifyFromLeYiRequestModel requestModel) {
        TDemandOrder tDemandOrderByDemandCode = demandOrderMapper.getByCode(requestModel.getDemandCode());
        if (tDemandOrderByDemandCode == null) {
//            CancelDemandOrderVerifyFromTmsLeYiRequestModel mapper = MapperUtils.mapper(requestModel, CancelDemandOrderVerifyFromTmsLeYiRequestModel.class);
//            entrustClient.cancelDemandOrderVerifyFromTmsLeYi(mapper);
            throw new BizException("需求单不存在");
        }else {
            //竞价中的需求单不能取消
            if (DemandOrderStatusEnum.BIDDING.getKey().equals(tDemandOrderByDemandCode.getEntrustStatus())){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_BIDDING_NOT_OPERATION);
            }
            //修改取消
            if (CommonConstant.ONE.equals(requestModel.getOperateType())){
                //非【已回退】【待发布】状态不能操作
                if (!(CommonConstant.INTEGER_ONE.equals(tDemandOrderByDemandCode.getIfRollback())
                        || DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(tDemandOrderByDemandCode.getEntrustStatus()))) {
                    throw new BizException(EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR);
                }
            }
            //正常取消
            else {
                //已调度车辆不能取消
                if (!(DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(tDemandOrderByDemandCode.getEntrustStatus())
                        || DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(tDemandOrderByDemandCode.getEntrustStatus()))){
                    throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_NOT_CANCEL);
                }
            }
        }
    }

    /**
     * 托盘取消出入库计划和取消预约需求单同步取消物流系统（托盘调用）
     * @param requestModel
     */
    @Transactional
    public void cancelDemandOrderFromLeYi(CancelDemandOrderFromLeYiRequestModel requestModel) {
        TDemandOrder tDemandOrderByDemandCode = demandOrderMapper.getByCode(requestModel.getDemandCode());
        if (tDemandOrderByDemandCode == null) {
//            entrustClient.cancelDemandOrderFromTmsLeYi(MapperUtils.mapper(requestModel,CancelDemandOrderFromTmsLeYiRequestModel.class));
            throw new BizException("需求单不存在");
        } else {
            //已放空的需求单云盘操作取消，物流不作处理
            if (CommonConstant.INTEGER_ONE.equals(tDemandOrderByDemandCode.getIfEmpty())){
                log.info("云盘取消计划取消tms需求单：需求单已放空，不作处理");
                return;
            }
            //竞价中的需求单不能取消
            if (DemandOrderStatusEnum.BIDDING.getKey().equals(tDemandOrderByDemandCode.getEntrustStatus())){
                throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_BIDDING_NOT_OPERATION);
            }
            //修改取消
            if (CommonConstant.ONE.equals(requestModel.getOperateType())){
                //非【已回退】【待发布】状态不能操作
                if (!(CommonConstant.INTEGER_ONE.equals(tDemandOrderByDemandCode.getIfRollback())
                        || DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(tDemandOrderByDemandCode.getEntrustStatus()))) {
                    throw new BizException(EntrustDataExceptionEnum.COMPANY_CARRIER_PUBLISH_STATUS_ERROR);
                }
            }
            //正常取消
            else {
                //已调度车辆不能取消
                if (!(DemandOrderStatusEnum.WAIT_PUBLISH.getKey().equals(tDemandOrderByDemandCode.getEntrustStatus())
                        || DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(tDemandOrderByDemandCode.getEntrustStatus()))){
                    throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_NOT_CANCEL);
                }
            }

            //调取消需求单方法
            DemandOrderCancelRequestModel model = new DemandOrderCancelRequestModel();
            model.setDemandId(String.valueOf(tDemandOrderByDemandCode.getId()));
            if (CommonConstant.INTEGER_ONE.equals(tDemandOrderByDemandCode.getIfRollback()) && requestModel.getCancelType() == null){
                //获取回退原因
                DemandOrderCancelTypeEnum.DemandOrderCancelTypeTwoEnum typeTwoEnum = DemandOrderCancelTypeEnum.DemandOrderCancelTypeTwoEnum.getEnum(tDemandOrderByDemandCode.getRollbackCauseTypeTwo());
                DemandOrderCancelTypeEnum typeEnum = typeTwoEnum.getTypeEnum();
                //拼接操作日志备注
                String remark = typeEnum.getValue() +
                        "-" +
                        typeTwoEnum.getValue() +
                        "，" +
                        tDemandOrderByDemandCode.getRollbackRemark();
                //回退状态的需求单取消
                model.setCancelType(tDemandOrderByDemandCode.getRollbackCauseType());
                model.setCancelReason(remark);
            }else {
                model.setCancelType(requestModel.getCancelType());
                model.setCancelReason(requestModel.getCancelReason());
            }
            model.setLeyiOperate(true);
            BaseContextHandler.setUserName(requestModel.getOperator());
            this.cancelDemandOrder(model);
        }
    }

    /**
     * 前台-承运商-调度列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<WebDemandOrderResponseModel> searchListWeb(WebDemandOrderSearchRequestModel requestModel) {
        //查询车主公司id
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();

        List<Long> needExcludedOrderIds = workOrderBiz.getNeedExcludedOrderIds(WorkOrderTypeEnum.DEMAND_ORDER_TYPE, CommonConstant.INTEGER_TWO, companyCarrierId, null);
        //需要排除的需求单号
        requestModel.setExcludeDemandIdList(needExcludedOrderIds);

        processSortParamsWeb(requestModel);
        requestModel.enablePaging();
        List<Long> demandOrderIds = demandOrderMapper.searchDemandOrderListIdsForWebCarrier(requestModel, companyCarrierId);
        PageInfo pageInfo = new PageInfo(demandOrderIds);
        if (ListUtils.isEmpty(demandOrderIds)) {
            return pageInfo;
        }

        List<WebDemandOrderResponseModel> demandOrderResponseModels = demandOrderMapper.searchDemandOrderListForWebCarrier(requestModel, StringUtils.listToString(demandOrderIds, ','));
        pageInfo.setList(demandOrderResponseModels);
        if (ListUtils.isNotEmpty(demandOrderResponseModels)) {
            List<Long> demandOrderIdList = new ArrayList<>();
            for (WebDemandOrderResponseModel tempModel : demandOrderResponseModels) {
                demandOrderIdList.add(tempModel.getDemandId());
            }

            //已调车数
            Map<Long, WebDemandOrderDispatchVehicleRequestModel> dispatchVehicleMap =  carrierOrderBiz.countDispatchVehicleMap(demandOrderIdList);
            //车主修改原因
            List<TDemandOrderCarrier> demandCarrierByDemandOrderIds = demandOrderCarrierMapper.getDemandCarrierByDemandOrderIds(StringUtils.listToString(demandOrderIdList, ','), companyCarrierId);
            Map<Long, TDemandOrderCarrier> demandOrderCarrierMap = new HashMap<>();
            demandCarrierByDemandOrderIds.stream().forEach(tmp -> {
                if (demandOrderCarrierMap.get(tmp.getDemandOrderId()) == null) {
                    demandOrderCarrierMap.put(tmp.getDemandOrderId(), tmp);
                } else {
                    if (demandOrderCarrierMap.get(tmp.getDemandOrderId()).getCreatedTime().compareTo(tmp.getCreatedTime()) < 0) {
                        demandOrderCarrierMap.put(tmp.getDemandOrderId(), tmp);
                    }
                }
            });

            WebDemandOrderDispatchVehicleRequestModel demandOrderDispatchVehicleModel;
            TDemandOrderCarrier tDemandOrderCarrier;
            for (WebDemandOrderResponseModel item : demandOrderResponseModels) {
                item.setMyCompanyCarrierId(companyCarrierId);
                //已调车数
                demandOrderDispatchVehicleModel = dispatchVehicleMap.get(item.getDemandId());
                if (demandOrderDispatchVehicleModel != null) {
                    item.setDispatchVehicleCount(demandOrderDispatchVehicleModel.getCountVehicle());
                } else {
                    item.setDispatchVehicleCount(CommonConstant.INTEGER_ZERO);
                }
                //车辆修改原因
                tDemandOrderCarrier = demandOrderCarrierMap.get(item.getDemandId());
                if (tDemandOrderCarrier != null) {
                    item.setModifyCarrierReason(tDemandOrderCarrier.getCancelReason());
                }

                item.setWhetherWorkOrder(needExcludedOrderIds.contains(item.getDemandId()) ? CommonConstant.INTEGER_ONE : CommonConstant.INTEGER_ZERO);
            }
        }
        return pageInfo;
    }

    private void processSortParamsWeb(WebDemandOrderSearchRequestModel requestModel) {
        String sort = requestModel.getSort();
        String order = requestModel.getOrder();
        if ("desc".equals(order) || "asc".equals(order)) {
            requestModel.setOrder(order);
        } else {
            requestModel.setOrder("asc");
        }
        if ("notArrangedAmount".equals(sort)) {
            requestModel.setSort("(tdo.not_arranged_amount)");
        } else if ("expectedLoadTime".equals(sort)) {
            requestModel.setSort("tdoa.expected_load_time,tdoa.expected_unload_time");
        } else if ("customerOrderCode".equals(sort)) {
            if ("asc".equals(order)) {
                requestModel.setSort("if(if(entrust_type=0,customer_order_code,'') is not null and if(entrust_type=0,customer_order_code,'')!='',0,1) asc ,if(entrust_type=0,customer_order_code,'') asc ,tdo.demand_order_code asc");
            } else {
                requestModel.setSort("if(if(entrust_type=0,customer_order_code,'') is not null and if(entrust_type=0,customer_order_code,'')!='',0,1) asc ,if(entrust_type=0,customer_order_code,'') desc ,tdo.demand_order_code desc");
            }
            requestModel.setOrder(null);
        } else {
            requestModel.setSort(null);
            requestModel.setOrder(null);
        }
    }

    /**
     * 前台承运商-获取需求单列表每个状态的需求单数量（调度车辆）
     *
     * @return
     */
    public WebDemandOrderSearchAccountResponseModel searchListAccount(WebDemandOrderSearchRequestModel requestModel) {
        //查询车主公司id
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (companyCarrierId == null || CommonConstant.LONG_ZERO.equals(companyCarrierId)) {
            return new WebDemandOrderSearchAccountResponseModel();
        }

        //需要排除的需求单号
        requestModel.setExcludeDemandIdList(workOrderBiz.getNeedExcludedOrderIds(WorkOrderTypeEnum.DEMAND_ORDER_TYPE, CommonConstant.INTEGER_TWO, companyCarrierId, null));

        requestModel.setDemandStatus(null);
        requestModel.setSort(null);
        requestModel.setOrder(null);
        List<Long> demandOrderIdList = demandOrderMapper.searchDemandOrderListIdsForWebCarrier(requestModel, companyCarrierId);
        if (ListUtils.isEmpty(demandOrderIdList)) {
            return new WebDemandOrderSearchAccountResponseModel();
        }
        String demandOrderIds = StringUtils.listToString(demandOrderIdList, ',');

        //查询该车主各状态的需求量数量
        WebDemandOrderSearchAccountResponseModel responseModel = demandOrderMapper.searchDemandOrderListForWebCarrierStatistics(demandOrderIds, companyCarrierId);
        responseModel.setWaitAccount(responseModel.getWaitAccount() + responseModel.getPartAccount());

        //查询需求单被更换了车主，这种显示取消状态的需求单数量
        SearchDemandListForStatisticsChangeCarrierModel carrierModel = demandOrderMapper.searchDemandListForStatisticsChangeCarrier(demandOrderIds, companyCarrierId);

        //总数量和取消数量均加上这种显示取消的数量
        responseModel.setAllAccount(responseModel.getAllAccount() + carrierModel.getCancelAccount());
        responseModel.setCancelAccount(responseModel.getCancelAccount() + carrierModel.getCancelAccount());
        return responseModel;
    }


    /**
     * 查询车辆
     *
     * @param requestModel
     * @return
     */
    public List<VehicleSearchResponseModel> searchDriverAndVehicle(DemandOrderDriverRequestModel requestModel) {
        List<VehicleSearchResponseModel> result = new ArrayList<>();
        Long companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
        if (companyCarrierId == null || companyCarrierId <= CommonConstant.LONG_ZERO) {
            return new ArrayList<>();
        }
        List<TCarrierVehicleRelation> carrierVehicleRelationList = tCarrierVehicleRelationMapper.getByCompanyCarrierId(companyCarrierId);
        if (ListUtils.isNotEmpty(carrierVehicleRelationList)) {
            //查询车辆数据
            String vehicleIdListStr = StringUtils.join(carrierVehicleRelationList.stream().map(TCarrierVehicleRelation::getVehicleId).collect(Collectors.toList()), ',');
            result = tVehicleBasicMapper.searchVehicle(vehicleIdListStr, requestModel.getVehicleNo(), companyCarrierId);

            if (ListUtils.isNotEmpty(result)) {
                //查询车辆关联的司机
                List<Long> driverIds = result.stream().map(VehicleSearchResponseModel::getDriverId).collect(Collectors.toList());
                List<DriverByVehiclesModel> driverVehiclesModelList = tStaffBasicMapper.selectDriverByVehicles(LocalStringUtil.listTostring(driverIds, ','), companyCarrierId, EnabledEnum.ENABLED.getKey());
                if (ListUtils.isNotEmpty(driverVehiclesModelList)) {
                    Map<Long, DriverByVehiclesModel> driverInfoMap = driverVehiclesModelList.stream().collect(Collectors.toMap(DriverByVehiclesModel::getStaffId, item -> item, (o1, o2) -> o2));
                    //拼接数据
                    for (VehicleSearchResponseModel vehicleSearchResponseModel : result) {
                        DriverByVehiclesModel driverByVehiclesModel = driverInfoMap.get(vehicleSearchResponseModel.getDriverId());
                        if (driverByVehiclesModel != null) {
                            vehicleSearchResponseModel.setDriverId(driverByVehiclesModel.getStaffId());
                            vehicleSearchResponseModel.setDriverName(driverByVehiclesModel.getDriverName());
                            vehicleSearchResponseModel.setDriverPhone(driverByVehiclesModel.getDriverPhone());
                            vehicleSearchResponseModel.setDriverIdentityNumber(driverByVehiclesModel.getDriverIdentityNumber());
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 修改车主详情
     * @param requestModel
     * @return
     */
    public ModifyCarrierDetailResponseModel modifyCarrierDetail(ModifyCarrierDetailRequestModel requestModel) {
        //入参入空
        if (requestModel == null || requestModel.getDemandOrderId() == null) {
            return new ModifyCarrierDetailResponseModel();
        }
        DemandOrderDetailRequestModel model = new DemandOrderDetailRequestModel();
        model.setDemandId(requestModel.getDemandOrderId());
        DemandOrderDetailResponseModel demandOrderDetail = demandOrderMapper.getDemandOrderDetail(model);
        //订单失效或不存在状态
        if (demandOrderDetail == null ) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        //非待调度状态无法操作
        if (!DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(demandOrderDetail.getStatus())) {
            throw new BizException(EntrustDataExceptionEnum.ONLY_WAIT_DISPATCH_DEMAND_ORDER_CAN_OPERATE);
        }

        //需求单下有放空的运单，则不允许更换车主
        List<DemandOrderCarrierDetailResponseModel> orderInfoByDemandId = tCarrierOrderMapper.getEmptyCarrierOrderInfoByDemandIds(ConverterUtils.toString(requestModel.getDemandOrderId()));
        if (ListUtils.isNotEmpty(orderInfoByDemandId)){
            throw new BizException(EntrustDataExceptionEnum.DEMAND_ORDER_HAVE_EMPTY_ORDER);
        }

        //字段赋值
        ModifyCarrierDetailResponseModel result = new ModifyCarrierDetailResponseModel();
        result.setCompanyCarrierType(demandOrderDetail.getCompanyCarrierType());
        result.setCarrierContactName(demandOrderDetail.getCarrierContactName());
        result.setCarrierContactPhone(demandOrderDetail.getCarrierContactMobile());
        result.setDemandOrderId(demandOrderDetail.getDemandId());
        result.setIsOurCompany(demandOrderDetail.getIsOurCompany());
        result.setCompanyCarrierId(demandOrderDetail.getCompanyCarrierId());
        result.setCompanyCarrierName(demandOrderDetail.getCompanyCarrierName());
        result.setCarrierPrice(demandOrderDetail.getCarrierPrice());
        result.setCarrierPriceType(demandOrderDetail.getCarrierPriceType());
        result.setGoodsAmount(demandOrderDetail.getGoodsAmount());
        return result;
    }


    /**
     * 修改车主
     */
    @Transactional
    public void modifyCarrier(ModifyCarrierRequestModel requestModel) {
        if (requestModel == null) {
            return;
        }
        //查询需求单
        TDemandOrder demandOrder = tDemandOrderMapper.selectByPrimaryKeyDecrypt(requestModel.getDemandOrderId());
        if (demandOrder == null || CommonConstant.INTEGER_ZERO.equals(demandOrder.getValid())) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_IS_EMPTY);
        }
        //需求单已取消不能操作
        if (CommonConstant.INTEGER_ONE.equals(demandOrder.getIfCancel())) {
            throw new BizException(EntrustDataExceptionEnum.DEMANDORDER_CANCEL);
        }
        //不是待调度状态不能操作
        if (!DemandOrderStatusEnum.WAIT_DISPATCH.getKey().equals(demandOrder.getStatus())) {
            throw new BizException(EntrustDataExceptionEnum.ONLY_WAIT_DISPATCH_DEMAND_ORDER_CAN_OPERATE);
        }

        //查询车主信息
        CompanyCarrierByIdModel companyCarrierByIdModel = demandOrderCommonBiz.companyCarrierInfoByIsOurCompany(requestModel.getIsOurCompany(), requestModel.getCompanyCarrierId());
        //校验车主是否修改
        if (demandOrder.getCompanyCarrierId().equals(companyCarrierByIdModel.getCompanyCarrierId())) {
            throw new BizException(EntrustDataExceptionEnum.CARRIER_NOT_CHANGE);
        }

        //需求单下有放空的运单，则不允许更换车主
        List<DemandCarrierOrderRecursiveModel> carrierOrderRecursiveList = tCarrierOrderMapper.getCarrierOrderStatusByDemandOrderIds(requestModel.getDemandOrderId().toString());
        if (ListUtils.isNotEmpty(carrierOrderRecursiveList)) {
            List<DemandCarrierOrderModel> carrierOrderList = carrierOrderRecursiveList.get(CommonConstant.INTEGER_ZERO).getCarrierOrderList();
            if (ListUtils.isNotEmpty(carrierOrderList)) {
                DemandCarrierOrderModel demandCarrierOrderModel = carrierOrderList.stream().filter(item -> CommonConstant.INTEGER_ONE.equals(item.getIfEmpty())).findFirst().orElse(null);
                if (demandCarrierOrderModel != null) {
                    throw new BizException(EntrustDataExceptionEnum.MODIFY_CARRIER_ERROR);
                }
            }
        }

        //更新需求单
        TDemandOrder upDemandOrder = new TDemandOrder();
        upDemandOrder.setId(demandOrder.getId());
        upDemandOrder.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
        upDemandOrder.setCompanyCarrierType(companyCarrierByIdModel.getCompanyCarrierType());
        upDemandOrder.setCompanyCarrierLevel(companyCarrierByIdModel.getCompanyCarrierLevel());
        //个人车主
        if (CommonConstant.INTEGER_TWO.equals(companyCarrierByIdModel.getCompanyCarrierType())) {
            upDemandOrder.setCompanyCarrierName("");
            upDemandOrder.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
            upDemandOrder.setCarrierContactName(companyCarrierByIdModel.getCarrierContactName());
            upDemandOrder.setCarrierContactPhone(companyCarrierByIdModel.getCarrierContactPhone());
        }
        //企业车主
        else {
            upDemandOrder.setCompanyCarrierName(companyCarrierByIdModel.getCompanyCarrierName());
            upDemandOrder.setCarrierContactId(CommonConstant.LONG_ZERO);
            upDemandOrder.setCarrierContactName("");
            upDemandOrder.setCarrierContactPhone("");
        }
        //我司
        if (CommonConstant.INTEGER_ONE.equals(requestModel.getIsOurCompany())) {//更换为我司，则将车主费用置空
            upDemandOrder.setCarrierPrice(BigDecimal.ZERO);
            upDemandOrder.setCarrierPriceType(CommonConstant.INTEGER_ZERO);
        }
        //二级承运商
        else {
            upDemandOrder.setCarrierPrice(requestModel.getCarrierPrice());
            upDemandOrder.setCarrierPriceType(requestModel.getCarrierPriceType());
        }

        //添加取消事件，接单事件
        List<TDemandOrderEvents> tDemandOrderEvents = new ArrayList<>();
        tDemandOrderEvents.add(demandOrderCommonBiz.generateEvent(demandOrder.getId(), demandOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.CANCEL_DEMANDORDER, BaseContextHandler.getUserName()));
        tDemandOrderEvents.add(demandOrderCommonBiz.generateEvent(demandOrder.getId(), upDemandOrder.getCompanyCarrierId(), DemandOrderEventsTypeEnum.ACCEPT_CARRIERORDER, BaseContextHandler.getUserName()));
        if (ListUtils.isNotEmpty(tDemandOrderEvents)) {
            tDemandOrderEventsMapper.batchInsertSelective(tDemandOrderEvents);
        }

        commonBiz.setBaseEntityModify(upDemandOrder, BaseContextHandler.getUserName());
        tDemandOrderMapper.updateByPrimaryKeySelectiveEncrypt(upDemandOrder);

        //修改前车主修改原因
        List<TDemandOrderCarrier> tDemandOrderCarrierList = tDemandOrderCarrierMapper.getNewestDemandCarrierByDemandOrderIds(demandOrder.getId().toString(), ConverterUtils.toString(demandOrder.getCompanyCarrierId()));
        if (ListUtils.isNotEmpty(tDemandOrderCarrierList)) {
            TDemandOrderCarrier tDemandOrderCarrier = tDemandOrderCarrierList.get(CommonConstant.INTEGER_ZERO);
            TDemandOrderCarrier upTDemandOrderCarrier1 = new TDemandOrderCarrier();
            upTDemandOrderCarrier1.setId(tDemandOrderCarrier.getId());
            upTDemandOrderCarrier1.setCancelReason(requestModel.getModifyReason());
            commonBiz.setBaseEntityModify(upTDemandOrderCarrier1, BaseContextHandler.getUserName());
            tDemandOrderCarrierMapper.updateByPrimaryKeySelective(upTDemandOrderCarrier1);
        }
        //添加新的车主记录
        TDemandOrderCarrier newDemandOrderCarrier1 = new TDemandOrderCarrier();
        newDemandOrderCarrier1.setDemandOrderId(demandOrder.getId());
        newDemandOrderCarrier1.setCompanyCarrierId(companyCarrierByIdModel.getCompanyCarrierId());
        newDemandOrderCarrier1.setCarrierContactId(companyCarrierByIdModel.getCarrierContactId());
        newDemandOrderCarrier1.setCarrierPriceType(requestModel.getCarrierPriceType());
        newDemandOrderCarrier1.setCarrierPrice(requestModel.getCarrierPrice());
        commonBiz.setBaseEntityAdd(newDemandOrderCarrier1, BaseContextHandler.getUserName());
        tDemandOrderCarrierMapper.insertSelective(newDemandOrderCarrier1);
        //生成修改车主日志

        String beforeCompanyName = CompanyTypeEnum.PERSON.getKey().equals(demandOrder.getCompanyCarrierType()) ? demandOrder.getCarrierContactName() + " " +
                demandOrder.getCarrierContactPhone() : demandOrder.getCompanyCarrierName();
        String afterCompanyName = CompanyTypeEnum.PERSON.getKey().equals(companyCarrierByIdModel.getCompanyCarrierType()) ? companyCarrierByIdModel.getCarrierContactName() + " " +
                companyCarrierByIdModel.getCarrierContactPhone() : companyCarrierByIdModel.getCompanyCarrierName();
        String operateLogRemark = String.format(DemandOrderOperateLogsEnum.MODIFY_CARRIER.getFormat(), beforeCompanyName, afterCompanyName, requestModel.getModifyReason());
        TDemandOrderOperateLogs demandOrderOperateLogs = demandOrderCommonBiz.getDemandOrderOperateLogs(requestModel.getDemandOrderId(), DemandOrderOperateLogsEnum.MODIFY_CARRIER, BaseContextHandler.getUserName(), operateLogRemark);
        demandOrderOperateLogsMapper.insertSelective(demandOrderOperateLogs);
    }

}
