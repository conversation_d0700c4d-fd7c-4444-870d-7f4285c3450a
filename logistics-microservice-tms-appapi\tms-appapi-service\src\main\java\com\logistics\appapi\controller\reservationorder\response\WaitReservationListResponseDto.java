package com.logistics.appapi.controller.reservationorder.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/14 14:16
 */
@Data
public class WaitReservationListResponseDto {

    /**
     * 发货地/收货地
     */
    private String address="";

    /**
     * 运单列表
     */
    private List<ReservationCarrierOrderListResponseDto> orderList=new ArrayList<>();
}
