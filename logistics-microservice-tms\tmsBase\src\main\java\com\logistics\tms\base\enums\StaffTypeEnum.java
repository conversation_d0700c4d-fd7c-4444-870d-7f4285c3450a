package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2019/6/5 20:26
 */
public enum StaffTypeEnum {
    DEFAULT(0,""),
    DRIVER(1,"驾驶员"),
    SUPERCARGO(2,"押运员"),
    DRIVER_SUPERCARGO(3,"驾驶员及押运员"),
    ;

    private Integer key;
    private String value;

    StaffTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
