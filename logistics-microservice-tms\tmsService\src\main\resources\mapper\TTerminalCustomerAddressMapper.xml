<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TTerminalCustomerAddressMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TTerminalCustomerAddress" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="collect_warehouse" property="collectWarehouse" jdbcType="VARCHAR" />
    <result column="collect_province_id" property="collectProvinceId" jdbcType="BIGINT" />
    <result column="collect_province_name" property="collectProvinceName" jdbcType="VARCHAR" />
    <result column="collect_city_id" property="collectCityId" jdbcType="BIGINT" />
    <result column="collect_city_name" property="collectCityName" jdbcType="VARCHAR" />
    <result column="collect_area_id" property="collectAreaId" jdbcType="BIGINT" />
    <result column="collect_area_name" property="collectAreaName" jdbcType="VARCHAR" />
    <result column="collect_detail_address" property="collectDetailAddress" jdbcType="VARCHAR" />
    <result column="collect_contact_name" property="collectContactName" jdbcType="VARCHAR" />
    <result column="collect_contact_mobile" property="collectContactMobile" jdbcType="VARCHAR" />
    <result column="map_link_path" property="mapLinkPath" jdbcType="VARCHAR" />
    <result column="customer_situation" property="customerSituation" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, collect_warehouse, collect_province_id, collect_province_name, collect_city_id, 
    collect_city_name, collect_area_id, collect_area_name, collect_detail_address, collect_contact_name, 
    collect_contact_mobile, map_link_path, customer_situation, remark, created_by, created_time, 
    last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_terminal_customer_address
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_terminal_customer_address
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TTerminalCustomerAddress" >
    insert into t_terminal_customer_address (id, collect_warehouse, collect_province_id, 
      collect_province_name, collect_city_id, collect_city_name, 
      collect_area_id, collect_area_name, collect_detail_address, 
      collect_contact_name, collect_contact_mobile, 
      map_link_path, customer_situation, remark, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{collectWarehouse,jdbcType=VARCHAR}, #{collectProvinceId,jdbcType=BIGINT}, 
      #{collectProvinceName,jdbcType=VARCHAR}, #{collectCityId,jdbcType=BIGINT}, #{collectCityName,jdbcType=VARCHAR}, 
      #{collectAreaId,jdbcType=BIGINT}, #{collectAreaName,jdbcType=VARCHAR}, #{collectDetailAddress,jdbcType=VARCHAR}, 
      #{collectContactName,jdbcType=VARCHAR}, #{collectContactMobile,jdbcType=VARCHAR}, 
      #{mapLinkPath,jdbcType=VARCHAR}, #{customerSituation,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TTerminalCustomerAddress" >
    insert into t_terminal_customer_address
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="collectWarehouse != null" >
        collect_warehouse,
      </if>
      <if test="collectProvinceId != null" >
        collect_province_id,
      </if>
      <if test="collectProvinceName != null" >
        collect_province_name,
      </if>
      <if test="collectCityId != null" >
        collect_city_id,
      </if>
      <if test="collectCityName != null" >
        collect_city_name,
      </if>
      <if test="collectAreaId != null" >
        collect_area_id,
      </if>
      <if test="collectAreaName != null" >
        collect_area_name,
      </if>
      <if test="collectDetailAddress != null" >
        collect_detail_address,
      </if>
      <if test="collectContactName != null" >
        collect_contact_name,
      </if>
      <if test="collectContactMobile != null" >
        collect_contact_mobile,
      </if>
      <if test="mapLinkPath != null" >
        map_link_path,
      </if>
      <if test="customerSituation != null" >
        customer_situation,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="collectWarehouse != null" >
        #{collectWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="collectProvinceId != null" >
        #{collectProvinceId,jdbcType=BIGINT},
      </if>
      <if test="collectProvinceName != null" >
        #{collectProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="collectCityId != null" >
        #{collectCityId,jdbcType=BIGINT},
      </if>
      <if test="collectCityName != null" >
        #{collectCityName,jdbcType=VARCHAR},
      </if>
      <if test="collectAreaId != null" >
        #{collectAreaId,jdbcType=BIGINT},
      </if>
      <if test="collectAreaName != null" >
        #{collectAreaName,jdbcType=VARCHAR},
      </if>
      <if test="collectDetailAddress != null" >
        #{collectDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="collectContactName != null" >
        #{collectContactName,jdbcType=VARCHAR},
      </if>
      <if test="collectContactMobile != null" >
        #{collectContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="mapLinkPath != null" >
        #{mapLinkPath,jdbcType=VARCHAR},
      </if>
      <if test="customerSituation != null" >
        #{customerSituation,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TTerminalCustomerAddress" >
    update t_terminal_customer_address
    <set >
      <if test="collectWarehouse != null" >
        collect_warehouse = #{collectWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="collectProvinceId != null" >
        collect_province_id = #{collectProvinceId,jdbcType=BIGINT},
      </if>
      <if test="collectProvinceName != null" >
        collect_province_name = #{collectProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="collectCityId != null" >
        collect_city_id = #{collectCityId,jdbcType=BIGINT},
      </if>
      <if test="collectCityName != null" >
        collect_city_name = #{collectCityName,jdbcType=VARCHAR},
      </if>
      <if test="collectAreaId != null" >
        collect_area_id = #{collectAreaId,jdbcType=BIGINT},
      </if>
      <if test="collectAreaName != null" >
        collect_area_name = #{collectAreaName,jdbcType=VARCHAR},
      </if>
      <if test="collectDetailAddress != null" >
        collect_detail_address = #{collectDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="collectContactName != null" >
        collect_contact_name = #{collectContactName,jdbcType=VARCHAR},
      </if>
      <if test="collectContactMobile != null" >
        collect_contact_mobile = #{collectContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="mapLinkPath != null" >
        map_link_path = #{mapLinkPath,jdbcType=VARCHAR},
      </if>
      <if test="customerSituation != null" >
        customer_situation = #{customerSituation,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TTerminalCustomerAddress" >
    update t_terminal_customer_address
    set collect_warehouse = #{collectWarehouse,jdbcType=VARCHAR},
      collect_province_id = #{collectProvinceId,jdbcType=BIGINT},
      collect_province_name = #{collectProvinceName,jdbcType=VARCHAR},
      collect_city_id = #{collectCityId,jdbcType=BIGINT},
      collect_city_name = #{collectCityName,jdbcType=VARCHAR},
      collect_area_id = #{collectAreaId,jdbcType=BIGINT},
      collect_area_name = #{collectAreaName,jdbcType=VARCHAR},
      collect_detail_address = #{collectDetailAddress,jdbcType=VARCHAR},
      collect_contact_name = #{collectContactName,jdbcType=VARCHAR},
      collect_contact_mobile = #{collectContactMobile,jdbcType=VARCHAR},
      map_link_path = #{mapLinkPath,jdbcType=VARCHAR},
      customer_situation = #{customerSituation,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>