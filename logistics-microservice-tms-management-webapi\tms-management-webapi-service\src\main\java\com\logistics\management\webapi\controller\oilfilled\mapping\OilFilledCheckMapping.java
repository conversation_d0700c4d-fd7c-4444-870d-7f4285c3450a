package com.logistics.management.webapi.controller.oilfilled.mapping;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.controller.oilfilled.request.ImportOilFilledCarInfoRequestDto;
import com.logistics.management.webapi.controller.oilfilled.request.ImportOilFilledCarListRequestDto;
import com.logistics.management.webapi.controller.oilfilled.request.ImportOilFilledCardInfoRequestDto;
import com.logistics.management.webapi.controller.oilfilled.request.ImportOilFilledCardListRequestDto;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: sj
 * @Date: 2019/6/21 9:27
 */
@Slf4j
@Component
public class OilFilledCheckMapping {
    private static final String REG_ONE = "^[A-Z0-9]{1,30}$";
    private static final String REG_TWO = "^[\\u4E00-\\u9FA5a-zA-Z]{2,50}$";
    private static final String REG_FOUR = "^[A-Z0-9]{2,50}$";
    private static final String REG_FIVE = "^[1-8]{1}$";
    private static final String REG_SIX = "^[2-9]$|^[1-9]{1}[0-9]{1,4}$|^100000$";
    private static final String REG_SEVEN = "^[1-9]$|^10$";
    private static final String REG_EIGHT = "^[\\u4E00-\\u9FA5a-zA-Z]{1,10}$";
    private static final String REG_NINE = "^[\\u4E00-\\u9FA5-0-9]{2,50}$";
    private static final String REG_TEN = "^[1-9]$|^[1][0-9]$|^20$";
    private static final String REG_TRANSPORT_TONNAGE = "^\\d+(\\.\\d+)?$";
    private static final String REG_ELEVEN = "^\\d{18,25}$";
    private static final String REG_TWELVE = "^(100000(\\.0{1,2})?|\\d{1,5}(\\.\\d{1,2})?)$";
    private static final String REG_THIRTEEN = "^(?!0$)\\d{1,4}$";
    private static final String REG_FOURTEEN = "^(10000(\\.0{1,2})?|\\d{1,5}(\\.\\d{1,2})?)$";

    private static final String REG_FIFTEEN = "^.{5,50}$";


    /**
     * 初始化Excel车辆信息导入
     *
     * @param excelList
     * @return
     */

    public ImportOilFilledCardInfoRequestDto initImportRepeatDataOfCard(List<List<Object>> excelList) {
        ImportOilFilledCardInfoRequestDto requestDto = new ImportOilFilledCardInfoRequestDto();
        if (ListUtils.isEmpty(excelList)) {
            return requestDto;
        }
        if (excelList.size() > CommonConstant.INTEGER_ONE_HUNDRED) {
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_FILE_LARGER_THAN_100);
        }
        List<ImportOilFilledCardListRequestDto> importList = new ArrayList<>();
        Set<ImportOilFilledCardListRequestDto> setContains = new HashSet<>();
        ImportOilFilledCardListRequestDto importOilFilledCardListRequestDto;

        SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
        Date currentDate = new Date();
        for (int i = 0; i < excelList.size(); i++) {
            List<Object> objects = excelList.get(i);
            if (objects != null) {
                importOilFilledCardListRequestDto = new ImportOilFilledCardListRequestDto();
                //副卡卡号
                importOilFilledCardListRequestDto.setSubCardNumber(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ZERO)));
                if (StringUtils.isBlank(importOilFilledCardListRequestDto.getSubCardNumber().trim()) || !FrequentMethodUtils.checkReg(importOilFilledCardListRequestDto.getSubCardNumber().trim(), REG_ELEVEN)) {
                    continue;
                }
                //车牌号
                importOilFilledCardListRequestDto.setVehicleNo(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ONE)));
                if (!FrequentMethodUtils.validateVehicleFormat(importOilFilledCardListRequestDto.getVehicleNo().trim())) {
                    continue;
                }
                //司机姓名
                importOilFilledCardListRequestDto.setDriverName(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_TWO)));
                if (StringUtils.isBlank(importOilFilledCardListRequestDto.getDriverName().trim())) {
                    continue;
                }
                //司机手机号
                importOilFilledCardListRequestDto.setDriverPhone(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_THREE)));
                if (StringUtils.isBlank(importOilFilledCardListRequestDto.getDriverPhone().trim()) || !FrequentMethodUtils.validateTelFormat(importOilFilledCardListRequestDto.getDriverPhone().trim())) {
                    continue;
                }
                //充值金额
                importOilFilledCardListRequestDto.setApplyAmount(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_FOUR)));
                if (StringUtils.isBlank(importOilFilledCardListRequestDto.getApplyAmount().trim()) || !FrequentMethodUtils.checkReg(importOilFilledCardListRequestDto.getApplyAmount().trim(), REG_TWELVE)) {
                    continue;
                }
                try {
                    //充值时间
                    importOilFilledCardListRequestDto.setOilFilledDate(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_FIVE)));
                    Date inputDate = sdf.parse(importOilFilledCardListRequestDto.getOilFilledDate());
                    if (StringUtils.isBlank(importOilFilledCardListRequestDto.getOilFilledDate().trim()) || !inputDate.before(currentDate) && !inputDate.equals(currentDate)) {
                        continue;
                    }
                } catch (Exception e) {
                    continue;
                }
                //备注
                importOilFilledCardListRequestDto.setRemark(ConverterUtils.toString(objects.get(objects.size() - 1)));
                if (StringUtils.isNotBlank(importOilFilledCardListRequestDto.getRemark()) && importOilFilledCardListRequestDto.getRemark().length() > CommonConstant.INTEGER_THREE_HUNDRED) {
                    continue;
                }
                setContains.add(importOilFilledCardListRequestDto);
            }
        }

        if (!setContains.isEmpty()) {
            importList.addAll(setContains);
        }
        requestDto.setImportList(importList);
        requestDto.setNumberFailures(ConverterUtils.toString(excelList.size() - setContains.size()));
        return requestDto;
    }

    /**
     * 初始化Excel车辆信息导入
     *
     * @param excelList
     * @return
     */

    public ImportOilFilledCarInfoRequestDto initImportRepeatDataOfCar(List<List<Object>> excelList) {
        ImportOilFilledCarInfoRequestDto requestDto = new ImportOilFilledCarInfoRequestDto();
        if (ListUtils.isEmpty(excelList)) {
            return requestDto;
        }
        if (excelList.size() > CommonConstant.INTEGER_ONE_HUNDRED) {
            throw new BizException(ManagementWebApiExceptionEnum.IMPORT_FILE_LARGER_THAN_100);
        }
        List<ImportOilFilledCarListRequestDto> importList = new ArrayList<>();
        Set<ImportOilFilledCarListRequestDto> setContains = new HashSet<>();
        ImportOilFilledCarListRequestDto importOilFilledCarListRequestDto;

        SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
        Date currentDate = new Date();
        for (int i = 0; i < excelList.size(); i++) {
            List<Object> objects = excelList.get(i);
            if (objects != null) {
                importOilFilledCarListRequestDto = new ImportOilFilledCarListRequestDto();
                //合作公司
                importOilFilledCarListRequestDto.setCooperationCompany(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ZERO)));
                if (StringUtils.isBlank(importOilFilledCarListRequestDto.getCooperationCompany().trim()) || !FrequentMethodUtils.checkReg(importOilFilledCarListRequestDto.getCooperationCompany().trim(), REG_FIFTEEN)) {
                    continue;
                }
                //升数
                importOilFilledCarListRequestDto.setLiter(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_ONE)));
                if (StringUtils.isBlank(importOilFilledCarListRequestDto.getLiter().trim()) || !FrequentMethodUtils.checkReg(importOilFilledCarListRequestDto.getLiter().trim(), REG_THIRTEEN)) {
                    continue;
                }
                //车牌号
                importOilFilledCarListRequestDto.setVehicleNo(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_TWO)));
                if (!FrequentMethodUtils.validateVehicleFormat(importOilFilledCarListRequestDto.getVehicleNo().trim())) {
                    continue;
                }
                //司机姓名
                importOilFilledCarListRequestDto.setDriverName(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_THREE)));
                if (StringUtils.isBlank(importOilFilledCarListRequestDto.getDriverName().trim())) {
                    continue;
                }
                //司机手机号
                importOilFilledCarListRequestDto.setDriverPhone(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_FOUR)));
                if (StringUtils.isBlank(importOilFilledCarListRequestDto.getDriverPhone().trim()) || !FrequentMethodUtils.validateTelFormat(importOilFilledCarListRequestDto.getDriverPhone().trim())) {
                    continue;
                }
                //充值金额
                importOilFilledCarListRequestDto.setTotalAmount(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_FIVE)));
                if (StringUtils.isBlank(importOilFilledCarListRequestDto.getTotalAmount().trim()) || !FrequentMethodUtils.checkReg(importOilFilledCarListRequestDto.getTotalAmount().trim(), REG_FOURTEEN)) {
                    continue;
                }
                try {
                    //加油时间
                    importOilFilledCarListRequestDto.setOilFilledDate(ConverterUtils.toString(objects.get(CommonConstant.INTEGER_SIX)));
                    Date inputDate = sdf.parse(importOilFilledCarListRequestDto.getOilFilledDate());
                    if (StringUtils.isBlank(importOilFilledCarListRequestDto.getOilFilledDate().trim()) || !inputDate.before(currentDate) && !inputDate.equals(currentDate)) {
                        continue;
                    }
                } catch (Exception e) {
                    continue;
                }
                //备注
                importOilFilledCarListRequestDto.setRemark(ConverterUtils.toString(objects.get(objects.size() - 1)));
                if (StringUtils.isNotBlank(importOilFilledCarListRequestDto.getRemark()) && importOilFilledCarListRequestDto.getRemark().length() > CommonConstant.INTEGER_THREE_HUNDRED) {
                    continue;
                }
                setContains.add(importOilFilledCarListRequestDto);
            }
        }

        if (!setContains.isEmpty()) {
            importList.addAll(setContains);
        }
        requestDto.setImportList(importList);
        requestDto.setNumberFailures(ConverterUtils.toString(excelList.size() - setContains.size()));
        return requestDto;
    }

}
