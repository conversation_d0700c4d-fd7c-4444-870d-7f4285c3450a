package com.logistics.management.webapi.controller.settlestatement.packaging.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class CarrierAddCarrierOrderConfirmRequestDto {

    @ApiModelProperty(value = "对账单id",required = true)
    @NotBlank(message = "对账单id不能为空")
    private String settleStatementId;

    @ApiModelProperty("货主")
    private String companyEntrustName;

    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("需求单号")
    private String demandOrderCode;

    @ApiModelProperty("车牌号")
    private String vehicleNumber;

    @ApiModelProperty("司机名,司机姓名+手机号")
    private String driverName;

    @ApiModelProperty("签收时间起")
    private String signTimeStart;

    @ApiModelProperty("签收时间止")
    private String signTimeEnd;

    @ApiModelProperty("拼单助手运单号")
    private List<String> carrierOrderCodeList;

    @ApiModelProperty("拼单助手客户单号")
    private List<String> demandOrderCodeList;

    @ApiModelProperty("仅操作为确定时 运单ids  字符串拼接,','拼接")
    private String carrierOrderIds;
}
