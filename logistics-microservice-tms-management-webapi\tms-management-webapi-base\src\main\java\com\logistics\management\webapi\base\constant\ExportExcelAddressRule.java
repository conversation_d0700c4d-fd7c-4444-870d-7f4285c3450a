package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 车主运价导出
 *
 * <AUTHOR>
 * @date 2022/9/5 15:14
 */
public class ExportExcelAddressRule {

    private ExportExcelAddressRule() {
    }

    private static final Map<String, String> ADDRESS_RULE_MAP;

    static {
        ADDRESS_RULE_MAP = new LinkedHashMap<>();
        ADDRESS_RULE_MAP.put("状态", "enabledLabel");
        ADDRESS_RULE_MAP.put("发货市", "fromCityName");
        ADDRESS_RULE_MAP.put("发货区", "fromAreaName");
        ADDRESS_RULE_MAP.put("收货市", "toCityName");
        ADDRESS_RULE_MAP.put("收货区", "toAreaName");
        ADDRESS_RULE_MAP.put("操作人", "lastModifiedBy");
        ADDRESS_RULE_MAP.put("操作时间", "lastModifiedTime");
    }


    public static Map<String, String> getAddressRuleMap() {
        return ADDRESS_RULE_MAP;
    }

}
