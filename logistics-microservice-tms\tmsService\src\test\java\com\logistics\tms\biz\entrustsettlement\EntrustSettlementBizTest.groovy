package com.logistics.tms.biz.entrustsettlement

import com.logistics.tms.api.feign.entrustsettlement.model.EntrustSettlementListRequestModel
import com.logistics.tms.api.feign.entrustsettlement.model.EntrustSettlementListResponseModel
import com.logistics.tms.api.feign.entrustsettlement.model.EntrustSettlementRowModel
import com.logistics.tms.api.feign.entrustsettlement.model.GetDetailRequestModel
import com.logistics.tms.api.feign.entrustsettlement.model.GetDetailResponseModel
import com.logistics.tms.api.feign.entrustsettlement.model.GetSettlementDetailRequestModel
import com.logistics.tms.api.feign.entrustsettlement.model.GetSettlementDetailResponseModel
import com.logistics.tms.api.feign.entrustsettlement.model.GetSettlementDetailRowModel
import com.logistics.tms.api.feign.entrustsettlement.model.ModifyCostRequestModel
import com.logistics.tms.api.feign.entrustsettlement.model.RefundRequestModel
import com.logistics.tms.biz.carrierorder.common.CarrierOrderCommonBiz
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TDemandPayment
import com.logistics.tms.entity.TDemandReceivement
import com.logistics.tms.mapper.TDemandOrderMapper
import com.logistics.tms.mapper.TDemandOrderOperateLogsMapper
import com.logistics.tms.mapper.TDemandPaymentMapper
import com.logistics.tms.mapper.TDemandReceivementMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class EntrustSettlementBizTest extends Specification {
    @Mock
    TDemandReceivementMapper tReceivementMapper
    @Mock
    TDemandPaymentMapper paymentMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TDemandOrderOperateLogsMapper demandOrderOperateLogsMapper
    @Mock
    TDemandOrderMapper demandOrderMapper
    @Mock
    CarrierOrderCommonBiz carrierOrderCommonBiz
    @InjectMocks
    EntrustSettlementBiz entrustSettlementBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "entrust Settlement List where requestModel=#requestModel and paging=#paging then expect: #expectedResult"() {
        given:
        when(tReceivementMapper.searchIdForEntrustSettlementList(any())).thenReturn([1l])
        when(tReceivementMapper.entrustSettlementList(anyString())).thenReturn([new EntrustSettlementRowModel()])
        when(tReceivementMapper.entrustSettlementListCount(any())).thenReturn(new EntrustSettlementListResponseModel())

        expect:
        entrustSettlementBiz.entrustSettlementList(requestModel, paging) == expectedResult

        where:
        requestModel                            | paging || expectedResult
        new EntrustSettlementListRequestModel() | true   || new EntrustSettlementListResponseModel()
    }

    @Unroll
    def "modify Cost where requestModel=#requestModel"() {
        given:
        when(paymentMapper.getByDemandOrderId(anyLong())).thenReturn(new TDemandPayment(priceType: 0, settlementCostTotal: 0 as BigDecimal, status: 0))

        expect:
        entrustSettlementBiz.modifyCost(requestModel)
        assert expectedResult == false

        where:
        requestModel                 || expectedResult
        new ModifyCostRequestModel() || true
    }

    @Unroll
    def "get Settlement Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tReceivementMapper.entrustSettlementListCount(any())).thenReturn(new EntrustSettlementListResponseModel())
        when(tReceivementMapper.getSettlementDetail(anyString())).thenReturn([new GetSettlementDetailRowModel()])
        when(tReceivementMapper.getByIds(anyString())).thenReturn([new TDemandReceivement(priceType: 0, status: 0)])

        expect:
        entrustSettlementBiz.getSettlementDetail(requestModel) == expectedResult

        where:
        requestModel                          || expectedResult
        new GetSettlementDetailRequestModel() || new GetSettlementDetailResponseModel()
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tReceivementMapper.getDetailForUpdateCost(anyLong())).thenReturn(new GetDetailResponseModel())
        when(paymentMapper.getByDemandOrderId(anyLong())).thenReturn(new TDemandPayment(status: 0))

        expect:
        entrustSettlementBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                || expectedResult
        new GetDetailRequestModel() || new GetDetailResponseModel()
    }

    @Unroll
    def "receive Money where requestModel=#requestModel"() {
        given:
        when(tReceivementMapper.batchUpdate(any())).thenReturn(0)
        when(tReceivementMapper.getByIds(anyString())).thenReturn([new TDemandReceivement(demandOrderId: 1l, priceType: 0, settlementCostTotal: 0 as BigDecimal, settlementTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 14).getTime(), status: 0)])
        when(demandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)

        expect:
        entrustSettlementBiz.receiveMoney(requestModel)
        assert expectedResult == false

        where:
        requestModel                          || expectedResult
        new GetSettlementDetailRequestModel() || true
    }

    @Unroll
    def "refund where requestModel=#requestModel"() {
        given:
        when(tReceivementMapper.batchUpdateForTime(any())).thenReturn(0)
        when(tReceivementMapper.getByIds(anyString())).thenReturn([new TDemandReceivement(demandOrderId: 1l, settlementCostTotal: 0 as BigDecimal, settlementTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 15, 14).getTime(), status: 0)])
        when(demandOrderOperateLogsMapper.batchInsertSelective(any())).thenReturn(0)

        expect:
        entrustSettlementBiz.refund(requestModel)
        assert expectedResult == false

        where:
        requestModel             || expectedResult
        new RefundRequestModel() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme