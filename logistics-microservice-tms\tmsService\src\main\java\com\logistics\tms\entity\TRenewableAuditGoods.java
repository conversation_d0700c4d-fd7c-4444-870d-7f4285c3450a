package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/08/17
*/
@Data
public class TRenewableAuditGoods extends BaseEntity {
    /**
    * 新生订单审核表id
    */
    @ApiModelProperty("新生订单审核表id")
    private Long renewableOrderId;

    /**
    * 货物类型：1 新生同步，2 司机确认
    */
    @ApiModelProperty("货物类型：1 新生同步，2 司机确认")
    private Integer goodsSourceType;

    /**
    * 新生sku编号
    */
    @ApiModelProperty("新生sku编号")
    private String renewableSkuCode;

    /**
    * 货物品名
    */
    @ApiModelProperty("货物品名")
    private String goodsName;

    /**
    * 货物数量
    */
    @ApiModelProperty("货物数量")
    private BigDecimal goodsAmount;

    /**
    * 货物单位：1 件，2 KG
    */
    @ApiModelProperty("货物单位：1 件，2 KG")
    private Integer goodsUnit;

    /**
    * 货物单价
    */
    @ApiModelProperty("货物单价")
    private BigDecimal goodsPrice;
}