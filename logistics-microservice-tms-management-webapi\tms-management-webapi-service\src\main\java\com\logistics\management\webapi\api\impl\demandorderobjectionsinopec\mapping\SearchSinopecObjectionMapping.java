package com.logistics.management.webapi.api.impl.demandorderobjectionsinopec.mapping;

import com.logistics.management.webapi.api.feign.demandorderobjectionsinopec.dto.SearchDemandOrderObjectionSinopecResponseDto;
import com.logistics.management.webapi.base.enums.AuditStatusEnum;
import com.logistics.management.webapi.base.enums.DemandOrderSinopecObjectionTypeEnum;
import com.logistics.tms.api.feign.demandorderobjectionsinopec.model.SearchDemandOrderObjectionSinopecResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @author: wjf
 * @date: 2022/5/30 17:08
 */
public class SearchSinopecObjectionMapping extends MapperMapping<SearchDemandOrderObjectionSinopecResponseModel, SearchDemandOrderObjectionSinopecResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    public SearchSinopecObjectionMapping(String imagePrefix , Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        SearchDemandOrderObjectionSinopecResponseModel source = getSource();
        SearchDemandOrderObjectionSinopecResponseDto destination = getDestination();

        //客户单号订单号（委托单号）
        destination.setCustomerOrderCode(source.getSinopecOrderNo() + "（" + source.getCustomerOrderCode() + "）");
        //审核状态转换
        destination.setAuditStatusLabel(AuditStatusEnum.getEnum(source.getAuditStatus()).getValue());
        //调度人
        if (StringUtils.isNotBlank(source.getDispatcherName())) {
            destination.setDispatcher(Optional.ofNullable(source.getDispatcherName()).orElse("") + " " + Optional.ofNullable(source.getDispatcherPhone()).orElse(""));
        }

        //审核依据
        if (ListUtils.isNotEmpty(source.getAuditTicketList())){
            List<String> auditTicketList = new ArrayList<>();
            for (String path : source.getAuditTicketList()) {
                auditTicketList.add(imagePrefix+imageMap.get(path));
            }
            destination.setAuditTicketCount(ConverterUtils.toString(auditTicketList.size()));
            destination.setAuditTicketList(auditTicketList);
        }
        //异常类型
        destination.setAuditObjectionTypeLabel(DemandOrderSinopecObjectionTypeEnum.getEnum(source.getAuditObjectionType()).getValue());
    }
}
