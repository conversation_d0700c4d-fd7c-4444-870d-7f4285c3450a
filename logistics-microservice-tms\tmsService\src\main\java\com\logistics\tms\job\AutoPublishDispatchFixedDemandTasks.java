package com.logistics.tms.job;

import com.logistics.tms.base.utils.AsyncProcessQueue;
import com.logistics.tms.biz.demandorder.DemandOrderTaskBiz;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 自动调度固定需求需求单定时任务
 */
@Slf4j
@Component
public class AutoPublishDispatchFixedDemandTasks {

    @Autowired
    private DemandOrderTaskBiz demandOrderTaskBiz;

    /**
     * 自动调度固定需求需求单定时任务（十分钟一次）
     */
    @XxlJob("logisticsTmsAutoPublishDispatch")
    public void autoPublishAndDispatch() {
        AsyncProcessQueue.execute(this::publishDispatchFixedDemand);
        AsyncProcessQueue.execute(this::publishDispatchRecycleDemand);
    }

    public void publishDispatchFixedDemand() {
        try {
            log.info("tms定时任务：自动发布调度固定需求需求单-开始");
            demandOrderTaskBiz.publishDispatchFixedDemand();
            log.info("tms定时任务：自动发布调度固定需求需求单-结束");
        } catch (Exception e) {
            log.error("定时任务，自动发布调度固定需求需求单错误: ", e);
        }
    }

    public void publishDispatchRecycleDemand() {
        try {
            log.info("tms定时任务：自动发布回收入库需求单-开始");
            demandOrderTaskBiz.publishDispatchRecycleDemand();
            log.info("tms定时任务：自动发布回收入库需求单-结束");
        } catch (Exception e) {
            log.error("定时任务，自动发布回收入库需求单错误: ", e);
        }
    }
}
