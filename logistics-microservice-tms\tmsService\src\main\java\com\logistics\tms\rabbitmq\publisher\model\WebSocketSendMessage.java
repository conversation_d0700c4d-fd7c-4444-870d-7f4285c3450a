package com.logistics.tms.rabbitmq.publisher.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/5 11:19
 */
@Data
public class WebSocketSendMessage {
    @ApiModelProperty("操作人")
    private String userName;
    @ApiModelProperty("消息内容")
    private List<WebSocketAddMessage> messageList;
    @ApiModelProperty("弹窗类型：1 托盘后台，2 云仓后台，3 云途后台，4 云途前台 5.云仓错误消息后台 6.TMS后台 7.TMS前台")
    private Integer moduleType;
}
