package com.logistics.tms.controller.demandorder;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.demandorder.DemandOrderForLeYiBiz;
import com.logistics.tms.controller.demandorder.request.CommitExtDemandOrderReqModel;
import com.logistics.tms.controller.demandorder.request.*;
import com.logistics.tms.controller.demandorder.response.*;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping(value = "/service/demandOrder")
public class DemandOrderForLeYiController {

    @Resource
    private DemandOrderForLeYiBiz demandOrderForLeYiBiz;

    /**
     * 需求单批量发布详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "需求单批量发布详情")
    @PostMapping(value = "/publishDetail")
    public Result<List<BatchPublishDetailResponseModel>> publishDetail(@RequestBody BatchPublishDetailRequestModel requestModel) {
        return Result.success(demandOrderForLeYiBiz.publishDetail(requestModel));
    }

    /**
     * 需求单批量发布
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "需求单批量发布")
    @PostMapping(value = "/confirmPublish")
    public Result<Boolean> confirmPublish(@RequestBody BatchPublishRequestModel requestModel) {
        demandOrderForLeYiBiz.confirmPublish(requestModel);
        return Result.success(true);
    }

    /**
     * 获取云盘需求单列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取云盘需求单列表")
    @PostMapping(value = "/searchListForLeYi")
    public Result<PageInfo<DemandOrderForLeYiResponseModel>> searchListForLeYi(@RequestBody DemandOrderSearchForLeYiRequestModel requestModel) {
        return Result.success(demandOrderForLeYiBiz.searchListForLeYi(requestModel));
    }

    /**
     * 导出需求单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出需求单")
    @PostMapping(value = "/exportDemandOrderForLeYi")
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<PageInfo<DemandOrderForLeYiResponseModel>> exportDemandOrderForLeYi(@RequestBody DemandOrderSearchForLeYiRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        return Result.success(demandOrderForLeYiBiz.searchListForLeYi(requestModel));
    }

    /**
     * 获取需求单详情
     *
     * @param requestModel
     */
    @ApiOperation(value = "获取需求单详情")
    @PostMapping(value = "/getDetailForLeYi")
    public Result<DemandOrderDetailForLeYiResponseModel> getDetailForLeYi(@RequestBody DemandOrderDetailRequestModel requestModel) {
        return Result.success(demandOrderForLeYiBiz.getDetailForLeYi(requestModel));
    }
    /**
     * 客户单号详情
     *
     * @param requestModel 需求单客户单号详情
     */
    @ApiOperation(value = "获取客户单号详情")
    @PostMapping(value = "/getDemandOrderOrders")
    public Result<List<DemandOrderOrderRelResponseModel>> getDemandOrderOrders(@RequestBody DemandOrderDetailRequestModel requestModel) {
        return Result.success(demandOrderForLeYiBiz.getDemandOrderOrders(requestModel));
    }

    /**
     * 模糊搜索仓库地址(发布页面调用云盘仓库接口)
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "模糊搜索仓库地址(发布页面调用云盘仓库接口)")
    @PostMapping(value = "/searchYPWarehouse")
    public Result<List<SearchDemandUnLoadAddressResponseModel>> searchYPWarehouse(@RequestBody SearchDemandUnLoadAddressRequestModel requestModel) {
        return Result.success(demandOrderForLeYiBiz.searchYPWarehouse(requestModel));
    }

    /**
     * 确认放空（需求单仅存在【已放空】/【已取消】且无其他有效状态运单&【待调度】需求单）
     * @param requestModel
     */
    @ApiOperation(value = "确认放空")
    @PostMapping(value = "/confirmEmpty")
    public Result<Boolean> confirmEmpty(@RequestBody DemandOrderEmptyRequestModel requestModel) {
        demandOrderForLeYiBiz.confirmEmpty(requestModel);
        return Result.success(true);
    }

    /**
     * 云盘物流看板--调度报警
     * @return
     */
    @ApiOperation(value = "云盘物流看板-调度报警")
    @PostMapping(value = "/dispatchAlarmStatistics")
    public Result<List<DispatchAlarmStatisticsResponseModel>> dispatchAlarmStatistics() {
        return Result.success(demandOrderForLeYiBiz.dispatchAlarmStatistics());
    }

    /**
     * 云盘物流看板-待调度、待发布
     *
     * @return 待发布, 待调度数据
     */
    @ApiOperation(value = "云盘物流看板-待调度、待发布")
    @PostMapping(value = "/waitDispatchStatistics")
    public Result<WaitDispatchStatisticsResponseModel> waitDispatchStatistics() {
        return Result.success(demandOrderForLeYiBiz.waitDispatchStatistics());
    }

    /**
     * 云盘物流看板--合计数据
     *
     * @return 合计数据
     */
    @ApiOperation(value = "云盘物流看板-合计数据")
    @PostMapping(value = "/aggregateDataStatistics")
    public Result<AggregateDataStatisticsResponseModel> aggregateDataStatistics() {
        return Result.success(demandOrderForLeYiBiz.aggregateDataStatistics());
    }

    /**
     * 云盘物流看板-地图数据
     *
     * @return 市维度数据
     */
    @ApiOperation(value = "云盘物流看板-地图数据")
    @PostMapping(value = "/mapDataStatistics")
    public Result<List<MapDataStatisticsResponseModel>> mapDataStatistics() {
        return Result.success(demandOrderForLeYiBiz.mapDataStatistics());
    }

    /**
     * 云盘需求单-智能拼单
     *
     * @return
     */
    @ApiOperation(value = "云盘需求单-智能拼单")
    @PostMapping(value = "/smartSpellList")
    public Result<SmartSpellListResponseModel> smartSpellList() {
        return Result.success(demandOrderForLeYiBiz.smartSpellList());
    }

    /**
     * 云盘需求单修改车主
     *
     * @param requestModel 修改车主信息
     * @return 操作结果
     */
    @ApiOperation(value = "云盘需求单修改车主v1.2.3")
    @PostMapping(value = "/modifyCarrierForLeyi")
    public Result<Boolean> modifyCarrierForLeyi(@RequestBody ModifyCarrierForLeyiRequestModel requestModel) {
        demandOrderForLeYiBiz.modifyCarrierForLeyi(requestModel);
        return Result.success(true);
    }

    /**
     * 云盘需求单修改车主详情查询
     *
     * @param requestModel 需求单id集合
     * @return 详情信息
     */
    @ApiOperation(value = "云盘需求单修改车主详情查询v1.2.3")
    @PostMapping(value = "/modifyCarrierDetailForLeyi")
    public Result<List<ModifyCarrierDetailForLeyiResponseModel>> modifyCarrierDetailForLeyi(@RequestBody ModifyCarrierDetailForLeyiRequestModel requestModel) {
        return Result.success(demandOrderForLeYiBiz.modifyCarrierDetailForLeyi(requestModel));
    }

    /**
     * 云盘需求单回退
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "云盘需求单回退")
    @PostMapping(value = "/rollbackDemandOrder")
    public Result<Boolean> rollbackDemandOrder(@RequestBody RollbackDemandOrderRequestModel requestModel) {
        demandOrderForLeYiBiz.rollbackDemandOrder(requestModel);
        return Result.success(true);
    }

    /**
     * 根据需求单id查询信息
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "根据需求单id查询信息(地推调用)")
    @PostMapping(value = "/searchDemandOrderInfo")
    public Result<List<SearchDemandOrderInfoResponseModel>> searchDemandOrderInfo(@RequestBody SearchDemandOrderInfoRequestModel requestModel) {
        return Result.success(demandOrderForLeYiBiz.searchDemandOrderInfo(requestModel));
    }

    @ApiOperation(value = "驳回回退的需求单（云盘重新推送需求单调用）")
    @PostMapping(value = "/rejectRollbackDemandOrder")
    public Result<List<RejectRollbackDemandOrderResponseModel>> rejectRollbackDemandOrder(@RequestBody RejectRollbackDemandOrderRequestModel requestModel) {
        return Result.success(demandOrderForLeYiBiz.rejectRollbackDemandOrder(requestModel));
    }


    @ApiOperation(value = "补单1-确认关联需求单信息 v2.6.8", tags = "2.6.8")
    @PostMapping(value = "/commitExtDemandOrder")
    public Result<CommitExtDemandOrderRespModel> commitExtDemandOrder(@RequestBody CommitExtDemandOrderReqModel reqModel) {
        return Result.success(demandOrderForLeYiBiz.commitExtDemandOrder(reqModel));
    }


}
