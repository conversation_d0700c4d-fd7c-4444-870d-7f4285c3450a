package com.logistics.management.webapi.api.impl.vehiclesafecheck;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.vehiclesafecheck.VehicleSafeCheckApi;
import com.logistics.management.webapi.api.feign.vehiclesafecheck.dto.*;
import com.logistics.management.webapi.api.impl.common.CommonBiz;
import com.logistics.management.webapi.api.impl.vehiclesafecheck.mapping.SafeCheckBoardMapping;
import com.logistics.management.webapi.api.impl.vehiclesafecheck.mapping.SafeCheckDetailMapping;
import com.logistics.management.webapi.api.impl.vehiclesafecheck.mapping.SearchListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ConfigKeyConstant;
import com.logistics.management.webapi.base.constant.ExportSafeCheckInfo;
import com.logistics.tms.api.feign.vehiclesafecheck.SafeCheckServiceApi;
import com.logistics.tms.api.feign.vehiclesafecheck.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/11/13 9:41
 */
@RestController
public class VehicleSafeCheckApiImpl implements VehicleSafeCheckApi {
    @Autowired
    private SafeCheckServiceApi safeCheckServiceApi;

    @Autowired
    private ConfigKeyConstant configKeyConstant;

    @Autowired
    private CommonBiz commonBiz;

    /**
     * 列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchSafeCheckListResponseDto>> searchList(@RequestBody SearchSafeCheckListRequestDto requestDto) {
        Result<PageInfo<SearchSafeCheckListResponseModel>> result = safeCheckServiceApi.searchList(MapperUtils.mapper(requestDto, SearchSafeCheckListRequestModel.class));
        result.throwException();

        PageInfo pageInfo = result.getData();
        List<SearchSafeCheckListResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(),SearchSafeCheckListResponseDto.class,new SearchListMapping());
        pageInfo.setList(dtoList == null ? new ArrayList() : dtoList);
        return Result.success(pageInfo);
    }

    /**
     * 列表汇总
     * @param requestDto
     * @return
     */
    @Override
    public Result<SummarySafeCheckResponseDto> getListSummary(@RequestBody SearchSafeCheckListRequestDto requestDto) {
        Result<SummarySafeCheckResponseModel> result = safeCheckServiceApi.getListSummary(MapperUtils.mapper(requestDto,SearchSafeCheckListRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SummarySafeCheckResponseDto.class));
    }

    /**
     * 导出
     * @param requestDto
     * @param response
     */
    @Override
    public void export(SearchSafeCheckListRequestDto requestDto, HttpServletResponse response) {
        SearchSafeCheckListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchSafeCheckListRequestModel.class);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);

        Result<PageInfo<SearchSafeCheckListResponseModel>> result = safeCheckServiceApi.searchList(requestModel);
        result.throwException();
        List<SearchSafeCheckListResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(),SearchSafeCheckListResponseDto.class,new SearchListMapping());
        String fileName = "车辆安全检查"+ DateUtils.dateToString(new Date(),CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportSafeCheckInfo.getExportSafeCheckInfo();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }

    /**
     * 车辆检查-新增
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addSafeCheck(@RequestBody @Valid AddSafeCheckRequestDto requestDto) {
        Result<Boolean> result = safeCheckServiceApi.addSafeCheck(MapperUtils.mapper(requestDto, AddSafeCheckRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 删除
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delSafeCheck(@RequestBody @Valid DelSafeCheckRequestDto requestDto) {
        Result<Boolean> result = safeCheckServiceApi.delSafeCheck(MapperUtils.mapper(requestDto, DelSafeCheckRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<SafeCheckDetailResponseDto> getDetail(@RequestBody @Valid SafeCheckDetailRequestDto requestDto) {
        Result<SafeCheckDetailResponseModel> result = safeCheckServiceApi.getDetail(MapperUtils.mapper(requestDto,SafeCheckDetailRequestModel.class));
        result.throwException();
        List<String> sourceSrcList=new ArrayList<>();
        for (SafeCheckFileResponseModel responseModel : result.getData().getFileList()) {
            sourceSrcList.add(responseModel.getRelativeFilepath());
        }
        SafeCheckReformResponseModel checkReformInfo = result.getData().getCheckReformInfo();
        if(checkReformInfo!=null){
            checkReformInfo.getItemFileList().stream().forEach(item->sourceSrcList.add(item.getRelativeFilepath()));
            checkReformInfo.getResultFileList().stream().forEach(item->sourceSrcList.add(item.getRelativeFilepath()));
        }
        Map<String, String> imageMap = commonBiz.batchGetOSSFileUrl(sourceSrcList);
        return Result.success(MapperUtils.mapper(result.getData(),SafeCheckDetailResponseDto.class,new SafeCheckDetailMapping(configKeyConstant,imageMap)));
    }

    /**
     * 车辆检查-待检查/待整改-提交
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addSafeCheckDetail(@RequestBody @Valid AddSafeCheckDetailRequestDto requestDto) {
        Result<Boolean> result = safeCheckServiceApi.addSafeCheckDetail(MapperUtils.mapper(requestDto,AddSafeCheckDetailRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 车辆检查-提交整改结果
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addReformResult(@RequestBody @Valid AddReformResultRequestDto requestDto) {
        Result<Boolean> result = safeCheckServiceApi.addReformResult(MapperUtils.mapper(requestDto,AddReformResultRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 车辆检查-重新检查
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> reformCheck(@RequestBody @Valid ReformCheckRequestDto requestDto) {
        Result<Boolean> result = safeCheckServiceApi.reformCheck(MapperUtils.mapper(requestDto,ReformCheckRequestModel.class));
        result.throwException();
        return Result.success(true);
    }

    /**
     * 车辆检车看板（列表）
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<SafeCheckBoardResponseDto>> getSafeCheckBoardInfo(@RequestBody @Valid SafeCheckBoardRequestDto requestDto) {
        Result<List<SafeCheckBoardResponseModel>> result = safeCheckServiceApi.getSafeCheckBoardInfo(MapperUtils.mapper(requestDto,SafeCheckBoardRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(),SafeCheckBoardResponseDto.class,new SafeCheckBoardMapping()));
    }
}
