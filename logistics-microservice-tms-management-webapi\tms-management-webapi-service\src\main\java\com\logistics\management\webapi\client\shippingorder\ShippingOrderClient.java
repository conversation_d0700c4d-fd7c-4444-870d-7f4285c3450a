package com.logistics.management.webapi.client.shippingorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.shippingorder.hystrix.ShippingOrderClientHystrix;
import com.logistics.management.webapi.client.shippingorder.request.*;
import com.logistics.management.webapi.client.shippingorder.response.GetShippingOrderRoutePlanResponseModel;
import com.logistics.management.webapi.client.shippingorder.response.GetShippingOrderDetailResponseModel;
import com.logistics.management.webapi.client.shippingorder.response.SearchShippingOrderListResponseModel;
import com.logistics.management.webapi.controller.shippingorder.request.ShippingOrderIdRequestDto;
import com.yelo.tray.core.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/8/6 17:39
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        path = "/service/shippingOrder",
        fallback = ShippingOrderClientHystrix.class)
public interface ShippingOrderClient {

    /**
     * 列表
     *
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/searchList")
    Result<PageInfo<SearchShippingOrderListResponseModel>> searchList(@RequestBody SearchShippingOrderListRequestModel requestModel);

    /**
     * 列表导出
     *
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/export")
    Result<List<SearchShippingOrderListResponseModel>> export(@RequestBody SearchShippingOrderListRequestModel requestModel);

    /**
     * 审核
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/audit")
    Result<Boolean> audit(@RequestBody ShippingOrderAuditRequestModel requestModel);

    /**
     * 重新报价
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/reQuote")
    Result<Boolean> reQuote(@RequestBody ShippingOrderReQuoteRequestModel requestModel);

    /**
     * 查看路径规划
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getRoutePlan")
    Result<List<GetShippingOrderRoutePlanResponseModel>> getRoutePlan(@RequestBody GetShippingOrderDetailRequestModel requestModel);

    /**
     * 查询详情
     *
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/getDetail")
    public Result<GetShippingOrderDetailResponseModel> getDetail(@RequestBody GetShippingOrderDetailRequestModel requestModel);


}
