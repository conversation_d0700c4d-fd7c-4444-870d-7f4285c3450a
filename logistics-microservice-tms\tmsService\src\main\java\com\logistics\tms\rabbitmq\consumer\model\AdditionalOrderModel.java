package com.logistics.tms.rabbitmq.consumer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author:lei.zhu
 * @date:2021/12/28 18:10:20
 */
@Data
public class AdditionalOrderModel {
    @ApiModelProperty("销售单ID")
    private Long orderId;

    @ApiModelProperty("销售单号")
    private String orderCode;

    @ApiModelProperty("数量")
    private BigDecimal totalAmount;
}
