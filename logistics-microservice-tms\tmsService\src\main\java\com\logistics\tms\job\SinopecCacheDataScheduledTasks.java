package com.logistics.tms.job;

import com.logistics.tms.biz.warehouseaddress.WarehouseAddressBiz;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author：wjf
 * @date：2021/5/8 15:56
 */
@Slf4j
@Component
public class SinopecCacheDataScheduledTasks {

    @Autowired
    private WarehouseAddressBiz warehouseAddressBiz;

    /**
     * 删除缓存的仓库（一天一次（凌晨一点））
     */
    @XxlJob("logisticsTmsDelCacheWarehouse")
    public void delCacheData(){
        try {
            log.info("tms定时任务：删除缓存的仓库-开始");
            warehouseAddressBiz.delCacheWarehouse();
            log.info("tms定时任务：删除缓存的仓库-结束");
        }catch (Exception e){
            log.error("定时任务，删除缓存的仓库错误: ", e);
        }
    }
}
