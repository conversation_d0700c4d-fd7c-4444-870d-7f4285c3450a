package com.logistics.appapi.controller.attendance.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;

/**
 * 修改考勤打卡详情请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/14
 */
@Data
public class UpdateAttendanceHistoryDetailRequestDto {

	@ApiModelProperty(value = "考勤打卡ID", required = true)
	@NotBlank(message = "考勤信息不能为空")
	private String attendanceRecordId;

	@ApiModelProperty(value = "要变更的打卡类型 1: 上班 2:下班", required = true)
	@NotBlank(message = "请选择申请变更类型")
	@Range(min = 1, max = 2, message = "请选择申请变更类型")
	private String changeType;

	@ApiModelProperty(value = "申请变更类型 0: 修改申请 1:撤销申请", required = true)
	@NotBlank(message = "请选择申请变更状态")
	@Range(min = 0, max = 1, message = "请选择申请变更状态")
	private String changeStatus;
}
