package com.logistics.tms.api.feign.entrustsettlement.hystrix;

import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.entrustsettlement.EntrustSettlementServiceApi;
import com.logistics.tms.api.feign.entrustsettlement.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/11 18:59
 */
@Component("tmsEntrustSettlementServiceApiHystrix")
public class EntrustSettlementServiceApiHystrix implements EntrustSettlementServiceApi {
    @Override
    public Result<EntrustSettlementListResponseModel> entrustSettlementList(EntrustSettlementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<EntrustSettlementRowModel>> exportEntrustSettlementList(EntrustSettlementListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result modifyCost(ModifyCostRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetSettlementDetailResponseModel> getSettlementDetail(GetSettlementDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<GetDetailResponseModel> getDetail(GetDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result receiveMoney(GetSettlementDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result refund(RefundRequestModel requestModel) {
        return Result.timeout();
    }
}
