package com.logistics.management.webapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author:lei.zhu
 * @date:2021/4/9 11:43
 */
@Data
public class CancelVehicleSettlementDetailResponseModel {
    @ApiModelProperty("车辆运费账单id")
    private Long vehicleSettlementId;
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("账单月")
    private String settlementMonth;
    @ApiModelProperty("实付运费")
    private BigDecimal actualExpensesPayable;
    @ApiModelProperty("司机 姓名")
    private String driverName;
    @ApiModelProperty("司机 手机号")
    private String driverPhone;

    @ApiModelProperty("司机确认状态 司机确认状态：-1 未操作，0 无需确认，1 确认，2 驳回")
    private Integer driverStatus;
    @ApiModelProperty("无需确认/撤回理由")
    private String reason;
    private String cancelReason;
    @ApiModelProperty("司机确认绝对路径")
    private String commitImageUrlSrc;
    @ApiModelProperty("司机确认相对路径")
    private String commitImageUrl;
    private Integer status;
}
