package com.logistics.management.webapi.api.feign.carrierfreight.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 车主运价禁用启用
 *
 * <AUTHOR>
 * @date 2022/9/1 14:46
 */
@Data
public class CarrierFreightEnableRequestDto {

    @ApiModelProperty(value = "车主运价id", required = true)
    @NotNull(message = "请选择车主运价")
    private Long carrierFreightId;

    @ApiModelProperty("禁用/启用。1：启用，0：禁用")
    @NotNull(message = "操作类型不能为空")
    private Integer enabled;
}
