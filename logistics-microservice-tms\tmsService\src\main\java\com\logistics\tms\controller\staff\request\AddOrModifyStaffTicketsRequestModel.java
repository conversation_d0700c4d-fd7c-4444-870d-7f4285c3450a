package com.logistics.tms.controller.staff.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/6/5 20:51
 */
@Data
public class AddOrModifyStaffTicketsRequestModel {

    @ApiModelProperty("车主司机关联id（编辑时必填）")
    private Long carrierDriverId;

    @ApiModelProperty("人员类别 1 驾驶员 2 押运员 3 驾驶员&押运员")
    private Integer type;

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private Integer staffProperty;

    @ApiModelProperty("性别：1 男，2 女")
    private Integer gender;

    @ApiModelProperty("人员姓名")
    private String name;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("身份证号码")
    private String identityNumber;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("身份证有效期")
    private Date identityValidity;

    @ApiModelProperty("身份证是否永久: 0 否 1 是")
    private Integer identityIsForever;

    @ApiModelProperty("劳动合同编号")
    private String laborContractNo;

    @ApiModelProperty("劳动合同有效期")
    private Date laborContractValidDate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("司机证件表ID")
    private Long staffDriverCredentialId;

    @ApiModelProperty("从业资格证件号")
    private String occupationalRequirementsCredentialNo;

    @ApiModelProperty("初次发证日期")
    private Date initialIssuanceDate;

    @ApiModelProperty("机动车驾驶证号")
    private String driversLicenseNo;

    @ApiModelProperty("准驾车型")
    private String permittedType;

    @ApiModelProperty("驾照期限开始")
    private Date driversLicenseDateFrom;

    @ApiModelProperty("驾照期限结束")
    private Date driversLicenseDateTo;

    @ApiModelProperty("司机账号开通状态 0 待开通 1 已开通 2 已关闭")
    private Integer openStatus = 1;

    @ApiModelProperty("启用 1 禁用 0")
    private Integer enabled = 1;

    private List<AddOrModifyStaffRequestModel> ticketsList;

    @ApiModelProperty("从业资格证件")
    private List<OccupationalAddOrModifyRequestModel> occupationalList;

    @ApiModelProperty("诚信考核记录")
    private List<IntegrityExaminationAddOrModifyRequestModel> integrityExaminationList;

    @ApiModelProperty("继续教育记录")
    private List<ContinueLearningAddOrModifyRequestModel> continueLearningList;
}
