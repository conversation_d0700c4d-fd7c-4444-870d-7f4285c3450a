<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TWorkOrderProcessMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TWorkOrderProcess">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="work_order_id" jdbcType="BIGINT" property="workOrderId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="solve_desc" jdbcType="VARCHAR" property="solveDesc" />
    <result column="solve_user_name" jdbcType="VARCHAR" property="solveUserName" />
    <result column="solve_time" jdbcType="TIMESTAMP" property="solveTime" />
    <result column="solve_source" jdbcType="INTEGER" property="solveSource" />
    <result column="solve_remark" jdbcType="VARCHAR" property="solveRemark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, work_order_id, status, solve_desc, solve_user_name, solve_time, solve_source,
    solve_remark, created_by, created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_work_order_process
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_work_order_process
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TWorkOrderProcess">
    insert into t_work_order_process (id, work_order_id, status,
      solve_desc, solve_user_name, solve_time,
      solve_source, solve_remark, created_by,
      created_time, last_modified_by, last_modified_time,
      valid)
    values (#{id,jdbcType=BIGINT}, #{workOrderId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER},
      #{solveDesc,jdbcType=VARCHAR}, #{solveUserName,jdbcType=VARCHAR}, #{solveTime,jdbcType=TIMESTAMP},
      #{solveSource,jdbcType=INTEGER}, #{solveRemark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP},
      #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TWorkOrderProcess">
    insert into t_work_order_process
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="workOrderId != null">
        work_order_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="solveDesc != null">
        solve_desc,
      </if>
      <if test="solveUserName != null">
        solve_user_name,
      </if>
      <if test="solveTime != null">
        solve_time,
      </if>
      <if test="solveSource != null">
        solve_source,
      </if>
      <if test="solveRemark != null">
        solve_remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="workOrderId != null">
        #{workOrderId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="solveDesc != null">
        #{solveDesc,jdbcType=VARCHAR},
      </if>
      <if test="solveUserName != null">
        #{solveUserName,jdbcType=VARCHAR},
      </if>
      <if test="solveTime != null">
        #{solveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="solveSource != null">
        #{solveSource,jdbcType=INTEGER},
      </if>
      <if test="solveRemark != null">
        #{solveRemark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TWorkOrderProcess">
    update t_work_order_process
    <set>
      <if test="workOrderId != null">
        work_order_id = #{workOrderId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="solveDesc != null">
        solve_desc = #{solveDesc,jdbcType=VARCHAR},
      </if>
      <if test="solveUserName != null">
        solve_user_name = #{solveUserName,jdbcType=VARCHAR},
      </if>
      <if test="solveTime != null">
        solve_time = #{solveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="solveSource != null">
        solve_source = #{solveSource,jdbcType=INTEGER},
      </if>
      <if test="solveRemark != null">
        solve_remark = #{solveRemark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TWorkOrderProcess">
    update t_work_order_process
    set work_order_id = #{workOrderId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      solve_desc = #{solveDesc,jdbcType=VARCHAR},
      solve_user_name = #{solveUserName,jdbcType=VARCHAR},
      solve_time = #{solveTime,jdbcType=TIMESTAMP},
      solve_source = #{solveSource,jdbcType=INTEGER},
      solve_remark = #{solveRemark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>