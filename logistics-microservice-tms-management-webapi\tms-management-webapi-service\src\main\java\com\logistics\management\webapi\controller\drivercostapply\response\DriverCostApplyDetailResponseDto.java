package com.logistics.management.webapi.controller.drivercostapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2022/8/3 13:51
 */
@Data
public class DriverCostApplyDetailResponseDto {
    @ApiModelProperty("司机费用申请表id")
    private String driverCostApplyId="";

    //申请费用信息
    @ApiModelProperty("申请人")
    private String staffName="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("发生时间")
    private String occurrenceTime="";
    @ApiModelProperty("(3.17.0)费用类型：100 住宿费，101 装卸费，102 其他费用，103 加班费，104 劳保费，105 电话费，106 交通费， 107 打印费，108 叉车费，109 盖雨布， 110破包赔偿，111 交通罚款；200 维修费，201 车辆保养费，202 过路过桥费，203 停车费，204 尿素费，205 加油费；300 核酸检测费，301 医疗防护用品；400 扣款")
    private String costType="";
    @ApiModelProperty("费用类型")
    private String costTypeLabel="";
    @ApiModelProperty("申请费用")
    private String applyCost="";
    @ApiModelProperty("申请时间")
    private String applyTime="";
    @ApiModelProperty("关联油卡")
    private String associatedOilCard="";
    @ApiModelProperty("申请说明")
    private String applyRemark="";
    @ApiModelProperty("申请依据; 1.3.6 修改")
    private List<DriverCostApplyTickDetail> ticketList;

    //审核信息
    @ApiModelProperty("(3.17.0)审核状态：-1 待业务审核，0 待财务审核，1 已审核，2 已驳回，3 已撤销，4 已红冲")
    private String auditStatus = "";
    @ApiModelProperty("审核状态")
    private String auditStatusLabel = "";
    @ApiModelProperty("审核人")
    private String auditorName = "";
    @ApiModelProperty("备注")
    private String remark = "";
    @ApiModelProperty("审核时间")
    private String auditTime = "";

    @ApiModelProperty("冲销费用列表; 1.3.6 修改")
    private List<ReserveItemDto> reserveList;

    @ApiModelProperty("垫付费用（元）; 1.3.6 新增")
    private String advanceCosts = "";

    @ApiModelProperty("发票信息; 1.3.8 新增")
    private List<DriverCostApplyInvoiceResponseDto> invoiceInfoList;

    @ApiModelProperty("(3.17.0)操作日志")
    private List<DriverCostApplyRecordResponseDto> recordList;

    @Data
    public static class DriverCostApplyTickDetail {

        @ApiModelProperty(value = "图片类型: 1 现场图片, 2 支付图片; 1.3.8删除(3 发票图片)")
        private String type;

        @ApiModelProperty(value = "图片依据路径")
        private List<String> imagePathList;
    }
}
