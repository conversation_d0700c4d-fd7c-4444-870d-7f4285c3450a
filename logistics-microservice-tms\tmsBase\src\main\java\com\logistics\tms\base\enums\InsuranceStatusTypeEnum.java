package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2019/12/25 18:53
 */
public enum InsuranceStatusTypeEnum {
    NORMAL(0,"正常"),
    CANCEL(1,"取消"),
    REFUND(2,"退保"),
    ;

    private Integer key;
    private String value;

    InsuranceStatusTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
