package com.logistics.management.webapi.controller.dispatch.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class SearchSpecialDispatchIfMatchFreightReqDto {

    /**
     * 需求单集合
     */
    @NotEmpty(message = "请选择需求单")
    @Size(min = 1, max = 15, message = "最多支持15个需求单一起调度")
    private List<SearchSpecialDispatchIfMatchFreightDemandReqDto> demandIdList;


    /**
     * 车长（米） -1就是零担
     */
    @NotBlank(message = "请选择车长")
    private String vehicleLength;


}
