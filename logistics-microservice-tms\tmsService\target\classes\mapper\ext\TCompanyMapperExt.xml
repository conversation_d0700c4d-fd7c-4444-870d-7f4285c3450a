<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCompanyMapper" >
  <select id="getByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_company
    where valid = 1
    and company_name = #{companyName,jdbcType=VARCHAR}
  </select>

  <update id="updateByPrimaryKeySelectiveForTime" parameterType="com.logistics.tms.entity.TCompany" >
    update t_company
    <set >
      <if test="companyName != null" >
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="tradingCertificateImage != null" >
        trading_certificate_image = #{tradingCertificateImage,jdbcType=VARCHAR},
      </if>
        trading_certificate_validity_time = #{tradingCertificateValidityTime,jdbcType=TIMESTAMP},
      <if test="tradingCertificateIsForever != null" >
        trading_certificate_is_forever = #{tradingCertificateIsForever,jdbcType=INTEGER},
      </if>
      <if test="tradingCertificateIsAmend != null" >
        trading_certificate_is_amend = #{tradingCertificateIsAmend,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getByIdsName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_company
    where valid = 1
    <if test="companyName != null and companyName != ''">
      and instr(company_name, #{companyName,jdbcType=VARCHAR})
    </if>
    <if test="ids != null and ids != ''">
      and id in (${ids})
    </if>
  </select>
</mapper>