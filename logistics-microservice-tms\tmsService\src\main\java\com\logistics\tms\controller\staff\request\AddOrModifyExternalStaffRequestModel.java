package com.logistics.tms.controller.staff.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/9 17:03
 */
@Data
public class AddOrModifyExternalStaffRequestModel {

    @ApiModelProperty("车主司机关联id，编辑时填写")
    private Long carrierDriverId;

    @ApiModelProperty("车主id")
    private Long companyCarrierId;

    @ApiModelProperty(value = "人员姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "身份证号码")
    private String identityNumber;
}
