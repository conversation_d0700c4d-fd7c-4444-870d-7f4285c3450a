package com.logistics.management.webapi.api.impl.personalaccidentinsurance.mapping;

import com.logistics.management.webapi.api.feign.personalaccidentinsurance.dto.PersonalAccidentInsuranceListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.InsuranceTypeEnum;
import com.logistics.tms.api.feign.personalaccidentinsurance.model.PersonalAccidentInsuranceListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/5/30 19:26
 */
public class SearchPersonalAccidentInsuranceListMapping extends MapperMapping<PersonalAccidentInsuranceListResponseModel,PersonalAccidentInsuranceListResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    public SearchPersonalAccidentInsuranceListMapping(String imagePrefix ,Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        PersonalAccidentInsuranceListResponseModel source = getSource();
        PersonalAccidentInsuranceListResponseDto destination = getDestination();
        if (source != null){
            destination.setInsuranceType(InsuranceTypeEnum.getEnum(source.getInsuranceType()).getValue());
            destination.setSinglePremium(ConverterUtils.toString(source.getGrossPremium().divide(ConverterUtils.toBigDecimal(source.getPolicyPersonCount()),2, BigDecimal.ROUND_HALF_UP)));
            if (source.getStartTime() != null){
                destination.setStartTime(DateUtils.dateToString(source.getStartTime(), CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
            }
            if (source.getEndTime() != null){
                destination.setEndTime(DateUtils.dateToString(source.getEndTime(),CommonConstant.DATE_FORMAT_ACCURATE_HOURS_AND_MINUTES));
            }
            destination.setTicketsCount("0");
            if (ListUtils.isNotEmpty(source.getTicketsList())){
                destination.setTicketsCount(ConverterUtils.toString(source.getTicketsList().size()));
                destination.getTicketsList().stream().forEach(dto ->
                    source.getTicketsList().stream().forEach(model -> {
                        if (model.getTicketId().toString().equals(dto.getTicketId())){
                            dto.setFilePathSrc(imagePrefix + imageMap.get(model.getFilePath()));
                        }
                    })
                );
            }
            destination.setAssociatedCount("0");
            if(ListUtils.isNotEmpty(source.getInsuranceList())){
                destination.setAssociatedCount(ConverterUtils.toString(source.getInsuranceList().size()));

            }

        }
    }
}
