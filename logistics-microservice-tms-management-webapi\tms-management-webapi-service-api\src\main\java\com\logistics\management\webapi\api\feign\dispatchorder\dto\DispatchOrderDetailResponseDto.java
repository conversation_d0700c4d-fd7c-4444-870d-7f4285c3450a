package com.logistics.management.webapi.api.feign.dispatchorder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by yuhong.lin on 2019/1/17
 */
@Data
public class DispatchOrderDetailResponseDto {
    @ApiModelProperty("调度单id")
    private String dispatchOrderId="";
    @ApiModelProperty("多装多卸（装货数）")
    private String loadPointAmount="";
    @ApiModelProperty("多装多卸（卸货数）")
    private String unloadPointAmount="";
    @ApiModelProperty("预计到货时间")
    private String expectArrivalTime="";
    @ApiModelProperty("车辆id")
    private String vehicleId="";
    @ApiModelProperty("车牌号")
    private String vehicleNo="";
    @ApiModelProperty("车辆机构 1 自主，2 外部，3 自营")
    private String  vehicleProperty = "";
    @ApiModelProperty("司机")
    private String driverName="";
    @ApiModelProperty("司机id")
    private String driverId="";
    @ApiModelProperty("司机电话")
    private String driverMobile="";
    @ApiModelProperty("司机身份证号")
    private String driverIdentity="";
    @ApiModelProperty("司机结算运费价格类型 1 单价 2 一口价")
    private String dispatchFreightFeeType="";
    @ApiModelProperty("司机运费报价")
    private String dispatchFreightFee="";
    @ApiModelProperty("司机运费合计")
    private String dispatchFreightFeeTotal="";
    @ApiModelProperty("多装多卸加价")
    private String markupFee="";
    @ApiModelProperty("调整费用类型：1 加价，2 减价")
    private String adjustFeeType="";
    @ApiModelProperty("调整价")
    private String adjustFee="";
    @ApiModelProperty("调整说明")
    private String adjustRemark="";
    @ApiModelProperty("是否调整价格：0 否 1 是")
    private String ifAdjust="";
    @ApiModelProperty("承运商公司id")
    private String companyCarrierId="";
    @ApiModelProperty("需求单汇总")
    private List<CarrierOrderGoodsResponseDto> carrierOrderGoodsList;
    @ApiModelProperty("预提合计数量")
    private String notArrangedAmountTotal="";
    @ApiModelProperty("预提合计体积(乐医单子才有此字段)")
    private String notArrangedVolumeTotal="";
    @ApiModelProperty("审批日志")
    private List<OperateLogsResponseDto> operateLogsList;
    @ApiModelProperty("货物单位：1 件，2 吨，3 方")
    private String goodsUnit="";
    @ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
    private String entrustType="";
    @ApiModelProperty("调度单备注")
    private String remark="";

    @ApiModelProperty("车主名")
    private String companyCarrierName = "";
}