package com.logistics.tms.controller.dispatch.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DriverSearchRequestModel  extends AbstractPageForm<DriverSearchRequestModel> {
    @ApiModelProperty(value = "司机机构 1 自主，2 外部，3 自营")
    private String staffProperty;
    @ApiModelProperty("车主ID")
    private Long companyCarrierId;
    @ApiModelProperty("是否我司 1:我司,2:其他车主")
    private Integer isOurCompany;
    @ApiModelProperty("司机姓名手机号")
    private String driverNameAndPhone;
}
