package com.logistics.management.webapi.api.impl.contractorder.mapping;

import com.logistics.management.webapi.api.feign.contractorder.dto.ContractOrderSearchResponseDto;
import com.logistics.management.webapi.base.enums.ContractNatureEnum;
import com.logistics.management.webapi.base.enums.ContractStatusEnum;
import com.logistics.management.webapi.base.enums.ContractTypeEnum;
import com.logistics.tms.api.feign.contractorder.model.ContractOrderSearchResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;

public class ListContractOrderMapping extends MapperMapping<ContractOrderSearchResponseModel,ContractOrderSearchResponseDto> {
    @Override
    public void configure() {
        ContractOrderSearchResponseModel source = getSource();
        ContractOrderSearchResponseDto destination = getDestination();
        if (source != null) {
            if (source.getContractStatus() != null) {
                destination.setContractStatusDsc(ContractStatusEnum.getEnum(source.getContractStatus()).getValue());
            }
            if (source.getContractType() != null) {
                destination.setContractType(ContractTypeEnum.getEnum(source.getContractType()).getValue());
            }
            if (source.getContractNature() != null) {
                destination.setContractNature(ContractNatureEnum.getEnum(source.getContractNature()).getValue());
            }
            if (source.getContractStartTime() != null && source.getContractEndTime() != null) {
                String startTime = DateUtils.dateToString(source.getContractStartTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN);
                String endTime = DateUtils.dateToString(source.getContractEndTime(), DateUtils.DATE_TO_STRING_SHORT_PATTERN);
                destination.setContractValidTime(startTime + "至" + endTime);
            }
        }
    }
}
