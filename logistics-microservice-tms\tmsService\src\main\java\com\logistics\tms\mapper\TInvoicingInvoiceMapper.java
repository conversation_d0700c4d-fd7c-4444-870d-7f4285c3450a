package com.logistics.tms.mapper;

import com.logistics.tms.biz.invoicingmanagement.model.GetInvoiceAmountModel;
import com.logistics.tms.entity.TInvoicingInvoice;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
* Created by Mybatis Generator on 2024/03/20
*/
@Mapper
public interface TInvoicingInvoiceMapper extends BaseMapper<TInvoicingInvoice> {

    @MapKey("invoicingId")
    Map<Long, GetInvoiceAmountModel> getInvoiceAmountByInvoicingIds(@Param("invoicingIds") Collection<Long> invoicingIds);

    List<TInvoicingInvoice> getByInvoicingIds(@Param("invoicingIds") Collection<Long> invoicingIds);

    /**
     * 通过发票管理ID 和发票号、发票代码查询发票信息
     * @param invoiceCode
     * @param invoiceNum
     * @param invoicingId
     * @return {@link TInvoicingInvoice}
     */
    TInvoicingInvoice getOneByInvoiceCodeAndInvoiceNumAndInvoicingId(@Param("invoiceCode") String invoiceCode
            , @Param("invoiceNum") String invoiceNum, @Param("invoicingId") Long invoicingId);

}