package com.logistics;

import com.leyi.auth.service.client.annotation.EnableTransUserForApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * @Author: sj
 * @Date: 2019/10/22 14:10
 */
@EnableWebMvc
@EnableTransUserForApi
@EnableFeignClients({"com.leyi","com.logistics","com.yelo"})
@EnableDiscoveryClient
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"com.leyi","com.logistics","com.yelo"})
public class AppApiApplication {
    public static void main(String[] args){
        SpringApplication.run(AppApiApplication.class, args);
    }
}
