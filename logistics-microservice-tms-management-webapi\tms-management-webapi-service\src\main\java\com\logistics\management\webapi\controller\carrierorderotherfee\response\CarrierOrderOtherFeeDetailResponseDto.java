package com.logistics.management.webapi.controller.carrierorderotherfee.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CarrierOrderOtherFeeDetailResponseDto {

    @ApiModelProperty("临时费用id")
    private String carrierOrderOtherFeeId="";

    @ApiModelProperty("运单id")
    private String carrierOrderId="";

    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty(value = "车主")
    private String companyCarrierName = "";

    @ApiModelProperty("司机")
    private String driver="";

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo = "";

    @ApiModelProperty("提货地")
    private String loadAddress;
    @ApiModelProperty("卸货地")
    private String unloadAddress;

    @ApiModelProperty("数量(带单位)")
    private String carrierOrderAmount="";

    @ApiModelProperty(value = "合计临时费用")
    private String totalAmount = "";

    @ApiModelProperty("临时费用明细")
    private List<CarrierOrderOtherFeeItemDetailResponseDto> otherFeeList;

    @ApiModelProperty(value = "（3.16.0修改）审核状态：1 待提交，2 待审核，3 已驳回，4 已撤销，5 已审核")
    private String auditStatus = "";

    @ApiModelProperty("审核状态")
    private String auditStatusLabel="";

    @ApiModelProperty(value = "备注")
    private String remark = "";

    @ApiModelProperty("装卸方式")
    private String loadingUnloadingPartLabel= "";
    @ApiModelProperty("装卸费用")
    private String loadingUnloadingCharge= "";
}
