package com.logistics.tms.mapper;

import com.logistics.tms.biz.demandorder.model.GetRegionInfoByCityIdModel;
import com.logistics.tms.controller.region.request.SearchRegionRequestModel;
import com.logistics.tms.controller.region.response.RegionDetailResponseModel;
import com.logistics.tms.controller.region.response.SearchRegionResponseModel;
import com.logistics.tms.entity.TRegion;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TRegionMapper extends BaseMapper<TRegion> {

    TRegion findByName(@Param("regionName") String regionName);

    RegionDetailResponseModel getDetail(@Param("regionId") Long regionId);

    List<Long> searchListIds(@Param("params") SearchRegionRequestModel requestModel);

    List<SearchRegionResponseModel> searchList(@Param("ids") String ids);

    List<GetRegionInfoByCityIdModel> getEnableRegionInfoByDemandIds(@Param("demandIds") String listToString);

    List<TRegion> getByIds(@Param("ids")String ids);

    void batchUpdate(@Param("list")List<TRegion> list);

    void delRegionById(@Param("id") Long id, @Param("userName") String userName, @Param("updateTime") Date updateTime);

}