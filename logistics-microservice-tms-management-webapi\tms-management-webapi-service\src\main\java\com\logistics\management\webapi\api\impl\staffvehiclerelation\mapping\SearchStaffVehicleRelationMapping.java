package com.logistics.management.webapi.api.impl.staffvehiclerelation.mapping;

import com.logistics.management.webapi.api.feign.staffvehiclerelation.dto.SearchStaffVehicleListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.*;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.staffvehiclerelation.model.SearchStaffVehicleListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.StringUtils;

/**
 * @Author: sj
 * @Date: 2019/7/26 16:57
 */
public class SearchStaffVehicleRelationMapping  extends MapperMapping<SearchStaffVehicleListResponseModel,SearchStaffVehicleListResponseDto> {
    @Override
    public void configure() {
        SearchStaffVehicleListResponseModel source = this.getSource();
        SearchStaffVehicleListResponseDto target = this.getDestination();
        if(source!=null){
            target.setTypeLabel(VehiclePropertyEnum.getEnum(source.getType()).getValue());
            target.setVehicleNo(source.getTractorVehicleNo());
            target.setVehicleCategoryLabel(VehicleCategoryEnum.getEnum(source.getVehicleCategory()).getValue());
            if (StringUtils.isNotBlank(source.getBrand()) && StringUtils.isNotBlank(source.getModel())) {
                target.setBrandModelLabel(source.getBrand() + "-" + source.getModel());
            }
            if (source.getApprovedLoadWeight() == null || CommonConstant.BIG_DECIMAL_ZERO.compareTo(source.getApprovedLoadWeight()) == 0) {
                target.setApprovedLoadWeight(CommonConstant.BLANK_TEXT);
            } else {
                target.setApprovedLoadWeight(source.getApprovedLoadWeight().stripTrailingZeros().toPlainString() + "KG");
            }
            if (source.getLastModifiedTime() != null) {
                target.setLastModifiedTime(DateUtils.dateToString(source.getLastModifiedTime(), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN));
            }
            target.setEmissionStandard(EmissionStandardTypeEnum.getEnum(source.getEmissionStandardType()).getValue());

            //处理车主信息
            if (CompanyTypeEnum.PERSONAL.getKey().equals(source.getCompanyCarrierType())) {
                target.setExportCompanyCarrierName(source.getCarrierContactName() + " " + source.getCarrierContactMobile());
                target.setCompanyCarrierName(source.getCarrierContactName() + " " + FrequentMethodUtils.encryptionData(source.getCarrierContactMobile(), EncodeTypeEnum.MOBILE_PHONE));
            } else {
                target.setExportCompanyCarrierName(source.getCompanyCarrierName());
            }
        }
    }
}
