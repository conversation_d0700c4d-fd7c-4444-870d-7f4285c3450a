package com.logistics.management.webapi.client.demandorder.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.demandorder.DemandOrderForYeloLifeClient;
import com.logistics.management.webapi.client.demandorder.request.*;
import com.logistics.management.webapi.client.demandorder.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/27 16:31
 */
@Component
public class DemandOrderForYeloLifeClientHystrix implements DemandOrderForYeloLifeClient {
    @Override
    public Result<PageInfo<RenewableDemandOrderResponseModel>> renewableDemandOrderList(RenewableDemandOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<RenewableDemandOrderResponseModel>> renewableDemandOrderListExport(RenewableDemandOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<RenewableDemandOrderDetailResponseModel> renewableDemandOrderDetail(DemandOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<RenewableDemandListStatisticsResponseModel> getRenewableDemandOrderListStatistics(RenewableDemandOrderRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<LifeBatchPublishDetailResponseModel>> publishDetail(LifeBatchPublishDetailRequestModel mapper) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> confirmPublish(LifeBatchPublishRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancelDemandOrder(LifeDemandOrderCancelRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifyCarrier(ModifyCarrierForLifeRequestModel requestModel) {
        return Result.timeout();
    }
}
