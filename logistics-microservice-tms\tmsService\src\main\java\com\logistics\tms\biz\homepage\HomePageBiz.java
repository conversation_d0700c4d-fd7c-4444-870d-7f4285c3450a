package com.logistics.tms.biz.homepage;

import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.*;
import com.logistics.tms.biz.carrierorder.CarrierOrderBiz;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.renewableaudit.RenewableAuditBiz;
import com.logistics.tms.biz.staff.model.CarrierDriverRelationModel;
import com.logistics.tms.biz.workordercenter.WorkOrderBiz;
import com.logistics.tms.client.WarehouseStockClient;
import com.logistics.tms.controller.homepage.response.HomeOrderCollectResponseModel;
import com.logistics.tms.controller.homepage.response.VerifyWarehousePermissionResponseModel;
import com.logistics.tms.entity.TStaffBasic;
import com.logistics.tms.mapper.TStaffBasicMapper;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 新生app首页
 *
 * <AUTHOR>
 * @date 2022/8/18 13:39
 */
@Service
public class HomePageBiz {

    @Resource
    private RenewableAuditBiz renewableAuditBiz;
    @Resource
    private CarrierOrderBiz carrierOrderBiz;
    @Resource
    private CommonBiz commonBiz;
    @Resource
    private TStaffBasicMapper tStaffBasicMapper;
    @Resource
    private WarehouseStockClient warehouseStockClient;
    @Resource
    private WorkOrderBiz workOrderBiz;

    /**
     * 首页单据汇总
     *
     * @return
     */
    public HomeOrderCollectResponseModel homeOrderCollect() {
        HomeOrderCollectResponseModel responseModel = new HomeOrderCollectResponseModel();
        //获取当前登录司机关联关系
        List<CarrierDriverRelationModel> relationList = commonBiz.getLoginUserDriver();
        CarrierDriverRelationModel driverRelationModel = null;
        String companyCarrierIdListStr = null;
        if (ListUtils.isNotEmpty(relationList)) {
            driverRelationModel = relationList.get(CommonConstant.INTEGER_ZERO);
            companyCarrierIdListStr = StringUtils.join(relationList.stream().map(CarrierDriverRelationModel::getCompanyCarrierId).collect(Collectors.toList()), ',');
        }
        if (driverRelationModel != null) {
            responseModel.setWaitConfirmedCount(renewableAuditBiz.getCountByStaffId(driverRelationModel.getDriverId(), RenewableAuditStatusEnum.WAIT_CONFIRM.getKey()));
            responseModel.setWaitLoadCount(carrierOrderBiz.getCountByDriverId(driverRelationModel.getDriverId(), companyCarrierIdListStr, Arrays.asList(CarrierOrderStatusEnum.WAIT_REACH_LOAD_ADDRESS.getKey(), CarrierOrderStatusEnum.WAIT_LOAD.getKey())));
            responseModel.setWorkOrderCount(workOrderBiz.getPendingWorkOrderCount(driverRelationModel.getDriverId()));
        }
        return responseModel;
    }

    /**
     * 验证是否可以跳转云仓小程序
     *
     * @return
     */
    public VerifyWarehousePermissionResponseModel verifyWarehousePermission() {
        Long loginDriverAppletUserId = commonBiz.getLoginDriverAppletUserId();
        if (CommonConstant.LONG_ZERO.equals(loginDriverAppletUserId)) {
            throw new BizException(CarrierDataExceptionEnum.DATA_ERROR_AND_RE_LOGIN);
        }
        //查询司机信息
        TStaffBasic tStaffBasic = tStaffBasicMapper.selectByPrimaryKey(loginDriverAppletUserId);
        if (tStaffBasic == null || IfValidEnum.INVALID.getKey().equals(tStaffBasic.getValid())) {
            throw new BizException(CarrierDataExceptionEnum.STAFF_NOT_EXIST);
        }
        //判断云仓开关是否打开
        if (!SwitchEnum.OPEN.getKey().equals(tStaffBasic.getWarehouseSwitch())) {
            throw new BizException(CarrierDataExceptionEnum.WAREHOUSE_SWITCH_CLOSE);
        }
        //调用云仓验证登录
        String warehouseAppletToken = warehouseStockClient.getSkipToken(tStaffBasic.getMobile());
        VerifyWarehousePermissionResponseModel responseModel = new VerifyWarehousePermissionResponseModel();
        responseModel.setUserAccount(tStaffBasic.getMobile());
        responseModel.setWarehouseAppletToken(warehouseAppletToken);
        return responseModel;
    }
}
