package com.logistics.appapi.controller.driverappoint.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/17
 */
@Data
public class SearchDrierAppointDetailRequestDto {

	@ApiModelProperty(value = "司机预约记录id", required = true)
	@NotBlank(message = "请选择要查看的记录")
	private String driverAppointId;
}
