package com.logistics.tms.controller.attendance.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AuditAttendanceChangeApplyRequestModel {

    //请假申请ID
    @ApiModelProperty(value = "考勤变更申请ID", required = true)
    private Long attendanceChangeApplyId;

    //审核类型
    @ApiModelProperty(value = "审核类型: 1:通过 ,2:驳回", required = true)
    private Integer auditType;

    //审核备注,驳回时必填
    @ApiModelProperty(value = "审核备注,驳回时必填,1-100个字符")
    private String remark;
}
