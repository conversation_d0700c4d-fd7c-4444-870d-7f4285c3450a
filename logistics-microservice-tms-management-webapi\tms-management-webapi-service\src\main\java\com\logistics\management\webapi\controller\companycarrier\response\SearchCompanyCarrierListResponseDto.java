package com.logistics.management.webapi.controller.companycarrier.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/9/27 17:39
 */
@Data
public class SearchCompanyCarrierListResponseDto {

	@ApiModelProperty("公司id")
	private String companyCarrierId = "";

	@ApiModelProperty("公司编码")
	private String companyCode = "";

	@ApiModelProperty("公司名字")
	private String companyCarrierName = "";

	@ApiModelProperty("证件补充状态: 1 待补充 2 齐全")
	private String certificateSupplement = "";

	@ApiModelProperty("创建时间")
	private String createdTime = "";

	@ApiModelProperty("最后修改人")
	private String lastModifiedBy = "";

	@ApiModelProperty("最后操作时间")
	private String lastModifiedTime = "";

	@ApiModelProperty("车主类型: 1 企业 2 个人")
	private String type = "";

	@ApiModelProperty("货主类型 文本")
	private String typeLabel = "";

	@ApiModelProperty("来源 1 后台添加 2 web注册 3 app注册 4 车主升级 5 刷数据 ")
	private String source = "";

	@ApiModelProperty("授权状态, 0:待授权 1:待审核 2:已驳回 3:已授权")
	private String authorizationStatus = "";

	@ApiModelProperty("授权状态展示文本")
	private String authorizationStatusLabel = "";

	@ApiModelProperty("实名状态, 0:待实名 1:实名中 2:已实名")
	private String realNameAuthenticationStatus = "";

	@ApiModelProperty("实名状态展示文本")
	private String realNameAuthenticationStatusLabel = "";

	/**
	 * (3.22.0)是否加入黑名单：0 否，1 是
	 */
	@ApiModelProperty("是否加入黑名单：0 否，1 是")
	private String ifAddBlacklist = "";
	/**
	 * (3.22.0)黑名单
	 */
	@ApiModelProperty("黑名单")
	private String ifAddBlacklistLabel = "";

	/**
	 * (3.27.0)是否零担模式：0 否，1 是
	 */
	private String ifLessThanTruckload = "";
	/**
	 * (3.27.0)零担模式
	 */
	private String ifLessThanTruckloadLabel = "";
}
