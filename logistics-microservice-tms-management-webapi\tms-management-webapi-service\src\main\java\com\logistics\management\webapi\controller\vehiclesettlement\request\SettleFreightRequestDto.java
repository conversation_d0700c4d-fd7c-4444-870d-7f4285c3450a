package com.logistics.management.webapi.controller.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author:lei.zhu
 * @date:2021/4/9 13:18
 */
@Data
public class SettleFreightRequestDto {
    @ApiModelProperty(value = "车辆结算账单id",required = true)
    @NotBlank(message = "id不能为空")
    private String vehicleSettlementId;
    @ApiModelProperty(value = "打款公司")
    private String payCompany;
    @ApiModelProperty(value = "收款人")
    private String receivedName;
    @ApiModelProperty(value = "打款时间")
    private String payTime;
    @ApiModelProperty(value = "打款金额")
    private String payFee;
}
