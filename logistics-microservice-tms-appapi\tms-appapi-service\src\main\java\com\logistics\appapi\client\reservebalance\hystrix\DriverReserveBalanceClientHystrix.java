package com.logistics.appapi.client.reservebalance.hystrix;

import com.logistics.appapi.client.reservebalance.DriverReserveBalanceClient;
import com.logistics.appapi.client.reservebalance.response.ReserveBalanceInfoResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

@Component
public class DriverReserveBalanceClientHystrix implements DriverReserveBalanceClient {

    @Override
    public Result<ReserveBalanceInfoResponseModel> reserveBalanceInfo() {
        return Result.timeout();
    }
}
