package com.logistics.tms.controller.renewableaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/16
 */
@Data
public class RenewableOrderGoodRequestModel {

	@ApiModelProperty("货物id")
	private Long goodsId;

	@ApiModelProperty(value = "sku编码", required = true)
	private String skuCode;

	@ApiModelProperty(value = "货物名(sku名)", required = true)
	private String goodsName;

	@ApiModelProperty(value = "确认货物数量(Kg) 1-100Kg", required = true)
	private BigDecimal goodsAmount;

	@ApiModelProperty(value = "收货单价 0<单价<=10000", required = true)
	private BigDecimal goodsPrice;
}
