<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCustomerSmsAuthInfoMapper" >
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TCustomerSmsAuthInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="account_id" property="accountId" jdbcType="BIGINT" />
    <result column="personal_identity_key" property="personalIdentityKey" jdbcType="VARCHAR" />
    <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP" />
    <result column="whether_use" property="whetherUse" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_modified_by" property="lastModifiedBy" jdbcType="VARCHAR" />
    <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, account_id, personal_identity_key, expire_time, whether_use, remark, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from t_customer_sms_auth_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from t_customer_sms_auth_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TCustomerSmsAuthInfo" >
    insert into t_customer_sms_auth_info (id, account_id, personal_identity_key, 
      expire_time, whether_use, remark, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{personalIdentityKey,jdbcType=VARCHAR}, 
      #{expireTime,jdbcType=TIMESTAMP}, #{whetherUse,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TCustomerSmsAuthInfo" >
    insert into t_customer_sms_auth_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="accountId != null" >
        account_id,
      </if>
      <if test="personalIdentityKey != null" >
        personal_identity_key,
      </if>
      <if test="expireTime != null" >
        expire_time,
      </if>
      <if test="whetherUse != null" >
        whether_use,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdTime != null" >
        created_time,
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time,
      </if>
      <if test="valid != null" >
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="accountId != null" >
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="personalIdentityKey != null" >
        #{personalIdentityKey,jdbcType=VARCHAR},
      </if>
      <if test="expireTime != null" >
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="whetherUse != null" >
        #{whetherUse,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TCustomerSmsAuthInfo" >
    update t_customer_sms_auth_info
    <set >
      <if test="accountId != null" >
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="personalIdentityKey != null" >
        personal_identity_key = #{personalIdentityKey,jdbcType=VARCHAR},
      </if>
      <if test="expireTime != null" >
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="whetherUse != null" >
        whether_use = #{whetherUse,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null" >
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null" >
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TCustomerSmsAuthInfo" >
    update t_customer_sms_auth_info
    set account_id = #{accountId,jdbcType=BIGINT},
      personal_identity_key = #{personalIdentityKey,jdbcType=VARCHAR},
      expire_time = #{expireTime,jdbcType=TIMESTAMP},
      whether_use = #{whetherUse,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>