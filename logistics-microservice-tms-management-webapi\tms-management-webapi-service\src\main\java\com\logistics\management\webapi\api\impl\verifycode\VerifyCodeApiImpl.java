package com.logistics.management.webapi.api.impl.verifycode;

import com.logistics.management.webapi.api.feign.verifycode.VerifyCodeApi;
import com.logistics.management.webapi.api.feign.verifycode.dto.VerifyCodeRequestDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.tms.api.feign.verificationcode.VerificationCodeServiceApi;
import com.logistics.tms.api.feign.verificationcode.model.VerificationCodeRequestModel;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @createDate 2018-08-02 9:26
 */

@RestController
@Slf4j
public class VerifyCodeApiImpl implements VerifyCodeApi {

    @Autowired
    private VerificationCodeServiceApi verificationCodeServiceApi;

    /**
     * 获取短信验证码
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result getVerifyCode(@RequestBody @Valid VerifyCodeRequestDto requestDto) {
        return verificationCodeServiceApi.getVerifyCode(MapperUtils.mapper(requestDto, VerificationCodeRequestModel.class));
    }

}
