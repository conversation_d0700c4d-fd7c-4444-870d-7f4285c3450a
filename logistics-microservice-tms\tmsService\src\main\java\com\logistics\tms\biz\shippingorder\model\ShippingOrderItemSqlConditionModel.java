package com.logistics.tms.biz.shippingorder.model;

import lombok.Data;

import java.util.List;

@Data
public class ShippingOrderItemSqlConditionModel {

    /**
     * 运单ids
     */
    private List<Long> carrierOrderIds;

    /**
     * 零担运输单ids
     */
    private List<Long> shippingOrderIds;


    /**
     * 提货地址
     */
    private String loadAddress;

    /**
     * 卸货地址
     */
    private String unloadAddress;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 需求单号
     */
    private String demandOrderCode;

    /**
     * 运单号
     */
    private String carrierOrderCode;

}
