package com.logistics.management.webapi.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2021/9/17 17:01
 */
@Data
public class CarrierOrderCorrectDetailResponseDto {

    @ApiModelProperty("入库数量")
    private String stockInCount = "";

    @ApiModelProperty("入库备注信息")
    private String stockInRemark = "";

    @ApiModelProperty("入库单")
    private List<String> stockInTicketsList;

    @ApiModelProperty("运单ID")
    private String carrierOrderId = "";

    @ApiModelProperty("运单号")
    private String carrierOrderCode = "";

    @ApiModelProperty("原数据-预提")
    private String expectAmount = "";

    @ApiModelProperty("原数据-实提")
    private String loadAmountExpect = "";

    @ApiModelProperty("原数据-实卸")
    private String unloadAmountExpect = "";

    @ApiModelProperty("实际实提数量")
    private String loadAmount = "";

    @ApiModelProperty("实际实卸数量 1.3.3")
    private String relUnloadAmount = "";

    @ApiModelProperty("纠错原因类型: 1 系统数量错误，2  实物损耗，3 提错托盘  4 仓库入错")
    private String correctType = "";

    @ApiModelProperty("纠错操作人")
    private String correctUser = "";

    @ApiModelProperty("纠错操作时间")
    private String correctTime = "";

    @ApiModelProperty("车主运费类型：1 单价，2 一口价")
    private String carrierFreightType = "";

    @ApiModelProperty("车主运费(元)")
    private String carrierFreight = "";

    @ApiModelProperty("车主结算数量 1.3.3")
    private String carrierSettlementAmount = "";

    @ApiModelProperty("是否我司 1:我司,2:其他车主")
    private String isOurCompany = "";

    @ApiModelProperty("临时费用(元)")
    private String otherFee = "";

    @ApiModelProperty("货物单位：1 件，2 吨")
    private String goodsUnit = "";

    @ApiModelProperty("提错托盘数量")
    private String loadErrorAmount = "";

    @ApiModelProperty("遗失托盘数量")
    private String loseErrorAmount = "";

    @ApiModelProperty("运单回单路径")
    private List<CarrierOrderCorrectDetailTicketDto> tickets;

    @ApiModelProperty("云仓异常数 1.3.3")
    private String abnormalAmount = "";

    @ApiModelProperty("需求类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨 1.3.3")
    private String entrustType;

    @ApiModelProperty("是否关联结算,1:已关联结算 0:未关联结算")
    private String relSettleStatement = "";
}
