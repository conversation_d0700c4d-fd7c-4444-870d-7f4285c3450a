package com.logistics.management.webapi.client.carrierorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.carrierorder.hystrix.CarrierOrderClientHystrix;
import com.logistics.management.webapi.client.carrierorder.request.*;
import com.logistics.management.webapi.client.carrierorder.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/*
  运单管理
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = CarrierOrderClientHystrix.class)
public interface CarrierOrderClient {

    /**
     * 运单列表
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/service/management/carrierOrderManagement/searchList")
    Result<PageInfo<SearchCarrierOrderListResponseModel>> searchList(@RequestBody SearchCarrierOrderListRequestModel requestModel);

    /**
     * 待审核车辆数
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/service/management/carrierOrderManagement/getWaitAuditVehicleCountInfo")
    Result<WaitAuditVehicleInfoResponseModel> waitAuditVehicleCountInfo(@RequestBody WaitAuditVehicleInfoRequestModel requestModel);

    /**
     * 运单详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "运单详情")
    @PostMapping(value = "/service/management/carrierOrderManagement/detail")
    Result<CarrierOrderDetailResponseModel> carrierOrderDetail(@RequestBody CarrierOrderDetailRequestModel requestModel);

    /**
     * 票据信息列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "票据信息列表")
    @PostMapping(value = "/service/management/carrierOrderManagement/getTickets")
    Result<List<GetTicketsResponseModel>> getTickets(@RequestBody GetTicketsRequestModel requestModel);

    /**
     * 上传票据
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "上传票据")
    @PostMapping(value = "/service/management/carrierOrderManagement/uploadTickets")
    Result<Boolean> uploadTickets(@RequestBody UploadTicketsRequestModel requestModel);

    /**
     * 运单取消
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "运单取消")
    @PostMapping(value = "/service/management/carrierOrderManagement/cancelCarrierOrder")
    Result<Boolean> cancelCarrierOrder(@RequestBody CancelCarrierOrderRequestModel requestModel);

    /**
     * 签收详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "签收详情")
    @PostMapping(value = "/service/management/carrierOrderManagement/getSignDetail")
    Result<CarrierOrderBeforeSignUpResponseModel> carrierOrderListBeforeSignUp(@RequestBody CarrierOrderListBeforeSignUpRequestModel requestModel);

    /**
     * 签收
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "签收")
    @PostMapping(value = "/service/management/carrierOrderManagement/signUp")
    Result<Boolean> carrierOrderSignUp(@RequestBody CarrierOrderConfirmSignUpRequestModel requestModel);

    /**
     * 审核/驳回车辆
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "审核/驳回车辆")
    @PostMapping(value = "/service/management/carrierOrderManagement/auditOrRejectVehicle")
    Result<Boolean> auditOrRejectVehicle(@RequestBody AuditOrRejectVehicleRequestModel requestModel);

    /**
     * 下载提货单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "下载提货单")
    @PostMapping(value = "/service/management/carrierOrderManagement/downloadLadingBill")
    Result<DownloadLadingBillResponseModel> downloadLadingBill(@RequestBody CarrierOrderDetailRequestModel requestModel);

    /**
     * 运单日志
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "运单日志")
    @PostMapping(value = "/service/management/carrierOrderManagement/getCarrierOrderLogs")
    Result<List<GetCarrierOrderLogsResponseModel>> getCarrierOrderLogs(@RequestBody CarrierOrderDetailRequestModel requestModel);

    /**
     * 导出运单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出运单")
    @PostMapping(value = "/service/management/carrierOrderManagement/exportCarrierOrder")
    Result<List<SearchCarrierOrderListResponseModel>> exportCarrierOrder(@RequestBody SearchCarrierOrderListRequestModel requestModel);

    /**
     * 删除票据
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "删除票据")
    @PostMapping(value = "/service/management/carrierOrderManagement/delTickets")
    Result<Boolean> delTickets(@RequestBody DeleteTicketsRequestModel requestModel);

    /**
     * 获取微信推送人
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取微信推送人")
    @PostMapping(value = "/service/carrierOrderManagement/getWeixinPushInfo")
    Result<List<GetCarrierOrderWeixinPushResponseModel>> getWeixinPushInfo(@RequestBody GetCarrierOrderWeixinPushRequestModel requestModel);

    /**
     * 确定推送微信消息
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "确定推送微信消息")
    @PostMapping(value = "/service/carrierOrderManagement/confirmPushWeixin")
    Result<Boolean> confirmPushWeixin(@RequestBody ConfirmPushWeixinRequestModel requestModel);

    /**
     * 修改司机运费（运单详情页面）
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "修改司机运费（运单详情页面）")
    @PostMapping(value = "/service/carrierOrderManagement/updateDriverFreightFee")
    Result<Boolean> updateDriverFreightFee(@RequestBody UpdateDriverFreightFeeRequestModel requestModel);

    /**
     * 提货
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "提货")
    @PostMapping(value = "/service/carrierOrderManagement/load")
    Result<Boolean> load(@RequestBody CarrierOrderLoadRequestModel requestModel);

    /**
     * 卸货
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "卸货")
    @PostMapping(value = "/service/carrierOrderManagement/unload")
    Result<Boolean> unload(@RequestBody CarrierOrderUnLoadRequestModel requestModel);

    /**
     * 到达提货地
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "到达卸货地")
    @PostMapping(value = "/service/carrierOrderManagement/reachLoadAddress")
    Result<Boolean> reachLoadAddress(@RequestBody ReachLoadAddressRequestModel requestModel);

    /**
     * 到达卸货地
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "卸货")
    @PostMapping(value = "/service/carrierOrderManagement/reachUnloadAddress")
    Result<Boolean> reachUnloadAddress(@RequestBody ReachUnloadAddressRequestModel requestModel);

    /**
     * 查询提卸货详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询提卸货详情")
    @PostMapping(value = "/service/carrierOrderManagement/getLoadDetail")
    Result<List<LoadDetailResponseModel>> getLoadDetail(@RequestBody LoadDetailRequestModel requestModel);

    /**
     * 检测导出运单回单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "检测导出运单回单")
    @PostMapping(value = "/service/carrierOrderManagement/checkExportCarrierOrderTickets")
    Result<Boolean> checkExportCarrierOrderTickets(@RequestBody ExportCarrierOrderTicketsRequestModel requestModel);

    /**
     * 导出运单回单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "导出运单回单")
    @PostMapping(value = "/service/carrierOrderManagement/exportCarrierOrderTickets")
    Result<ExportCarrierOrderTicketsResponseModel> exportCarrierOrderTickets(@RequestBody ExportCarrierOrderTicketsRequestModel requestModel);

    @ApiOperation(value = "修改车辆")
    @PostMapping(value = "/service/carrierOrderManagement/modifyVehicle")
    Result<Boolean> modifyVehicle(@RequestBody ModifyVehicleRequestModel requestModel);

    @ApiOperation(value = "根据运单id查询司机车辆信息（后台修改车辆详情）")
    @PostMapping(value = "/service/carrierOrderManagement/getDriverVehicleInfoByCarrierOrderId")
    Result<DriverAndVehicleResponseModel> getDriverVehicleInfoByCarrierOrderId(@RequestBody CarrierOrderDetailRequestModel requestModel);


    @ApiOperation(value = "校验接口是否能满足多提接口")
    @PostMapping(value = "/service/carrierOrderManagement/verifyEnablePickUpMore")
    Result<Boolean> verifyEnablePickUpMore(@RequestBody VerifyEnablePickUpMoreReqModel requestModel);


    @ApiOperation(value = "补单2-关联调度单信息 v2.6.8", tags = "2.6.8")
    @PostMapping(value = "/driverApplet/carrierOrder/associateExtDemandOrder")
    Result<AssociateExtDemandOrderRespModel> associateExtDemandOrder(@RequestBody @Valid AssociateExtDemandOrderReqModel requestModel);


}
