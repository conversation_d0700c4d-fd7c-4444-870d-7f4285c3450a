package com.logistics.management.webapi.base.enums;

public enum ContractLogsTypeEnum {
    DEFAULT_VALUE(0,""),
    ADD_CONTRACT(1, "新增合同"),
    MODIFY_CONTRACT(2, "修改合同"),
    TERMINATE_CONTRACT(3, "终止合同"),
    CANCEL_CONTRACT(4, "作废合同"),
    ;
    private Integer key;
    private String value;

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    ContractLogsTypeEnum(Integer key, String value) {

        this.key = key;
        this.value = value;
    }

    public static ContractLogsTypeEnum getEnum(Integer key) {
        for (ContractLogsTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }
}
