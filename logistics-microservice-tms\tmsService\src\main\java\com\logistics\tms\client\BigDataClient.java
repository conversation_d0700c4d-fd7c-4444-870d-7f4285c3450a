package com.logistics.tms.client;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.constant.ConfigKeyConstant;
import com.logistics.tms.base.enums.BigDataInterfaceEnum;
import com.logistics.tms.base.enums.EntrustDataExceptionEnum;
import com.logistics.tms.client.model.*;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.utils.HttpClientUtils;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * 对接大数据接口
 * @author: wjf
 * @date: 2022/9/1 13:56
 */
@Service
@Slf4j
public class BigDataClient {

    @Autowired
    private ConfigKeyConstant configKeyConstant;
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 智能拼单获取智慧物流页面
     *
     * @return
     */
    public GetUserAuthForBigDataResponseModel smartSpellList() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userName", BaseContextHandler.getUserName());
        jsonObject.put("userId", BaseContextHandler.getUserId());
        jsonObject.put("source", CommonConstant.ONE);

        //请求大数据接口
        return requestBigDataInterface(configKeyConstant.bigDataScheduleUrl + BigDataInterfaceEnum.USER_AUTH.getKey(), jsonObject, null, "智能物流鉴权", new TypeReference<>() {
        }, true);
    }

    /**
     * 物流⼩程序下单-查询发货人地址（根据当前位置查询新⽣客⼾地址，按最近距离 排序）
     * @param requestModel
     * @return
     */
    public List<SearchConsignorListForBigDataResponseModel> searchConsignorList(SearchConsignorListForBigDataRequestModel requestModel){
        //请求大数据接口
        return requestBigDataInterface(configKeyConstant.bigDataUrl + BigDataInterfaceEnum.SEARCH_CONSIGNOR_LIST.getKey(), requestModel, null, "查询发货人地址", new TypeReference<>() {
        }, true);
    }

    /**
     * 物流⼩程序下单-查询收货仓库（根据发货⼈地址最近距离排序，查询云仓仓库（和新⽣⼯⼚）地址信息
     *
     * @param requestModel
     * @return
     */
    public List<SearchWarehouseForBigDataResponseModel> searchWarehouseList(SearchWarehouseForBigDataRequestModel requestModel) {
        //请求大数据接口
        return requestBigDataInterface(configKeyConstant.bigDataUrl + BigDataInterfaceEnum.SEARCH_WAREHOUSE_LIST.getKey(), requestModel, null, "查询收货仓库", new TypeReference<>() {
        }, true);
    }

    /**
     * 根据仓库code批量查询查派车装载量，车牌号，承运商
     *
     * @param requestModel
     * @return
     */
    public List<FixedCompanyVehicleInfoResponseModel> searchFixedInfo(FixedCompanyVehicleInfoRequestModel requestModel) {
        //请求大数据接口
        HashMap<String, String> headers = new HashMap<>();
        headers.put(CommonConstant.TOKEN, configKeyConstant.bigDataScheduleToken);
        return requestBigDataInterface(configKeyConstant.bigDataScheduleUrl + BigDataInterfaceEnum.SEARCH_FIXED_INFO.getKey(), requestModel, headers, "查询固定客户", new TypeReference<>() {
        }, false);
    }

    /**
     * 更新智慧运营回收配置关联的需求单信息
     *
     * @param requestModel
     * @return
     */
    public void recyclePublishUpdateDemand(List<RecyclePublishUpdateDemandRequestModel> requestModel) {
        //请求大数据接口
        HashMap<String, String> headers = new HashMap<>();
        headers.put(CommonConstant.TOKEN, configKeyConstant.bigDataScheduleToken);
        this.requestBigDataInterface(configKeyConstant.bigDataScheduleUrl + BigDataInterfaceEnum.RECYCLE_PUBLISH_UPDATE_DEMAND.getKey(), requestModel, headers, "更新智慧运营回收配置关联的需求单信息", new TypeReference<>() {
        }, true);
    }

    /**
     * 根据自动发布配置code获取配置信息
     *
     * @param requestModel
     * @return
     */
    public List<AutoPublishInfoResponseModel> searchAutoPublishInfo(AutoPublishInfoRequestModel requestModel) {
        //请求大数据接口
        HashMap<String, String> headers = new HashMap<>();
        headers.put(CommonConstant.TOKEN, configKeyConstant.bigDataScheduleToken);
        return requestBigDataInterface(configKeyConstant.bigDataScheduleUrl + BigDataInterfaceEnum.SEARCH_AUTO_PUBLISH_INFO.getKey(), requestModel, headers, "查询自动发布配置", new TypeReference<>() {
        }, true);
    }

    /**
     * 路径规划
     *
     * @param requestModel
     * @return
     */
    public PathPlanResponseModel pathPlan(List<PathPlanRequestModel> requestModel) {
        //请求大数据接口
        return requestBigDataInterface(configKeyConstant.bigDataScheduleUrl + BigDataInterfaceEnum.PATH_PLAN.getKey(), requestModel, null, "路径规划", new TypeReference<>() {
        }, true);
    }

    /**
     * 请求大数据接口
     *
     * @param url           请求地址
     * @param requestObject 请求参数
     * @param logStr        日志注释
     * @param valueTypeRef  返回值
     * @param ifVerifyCode  是否验证返回值code
     * @param <T>
     * @return
     */
    public <T> T requestBigDataInterface(String url, Object requestObject, HashMap<String, String> headers, String logStr, TypeReference<T> valueTypeRef, boolean ifVerifyCode) {
        RequestBigDataResponseModel<Object> userAuthResponseModel = null;
        try {
            String requestParams = objectMapper.writeValueAsString(requestObject);
            //请求大数据接口
            log.info("调用大数据【" + logStr + url + "】接口 入参：" + requestParams);
            String result = HttpClientUtils.requestWithJsonWithHeadersUsingPost(url, headers, requestParams);
            log.info("调用大数据【" + logStr + url + "】接口 返参：" + result);
            if (result == null) {
                throw new BizException(EntrustDataExceptionEnum.NETWORK_CONNECTION_FAILURE);
            }
            //转换返回结果
            userAuthResponseModel = objectMapper.readValue(result, new TypeReference<RequestBigDataResponseModel<Object>>() {
            });
        } catch (Exception e) {
            log.error("调用大数据【" + logStr + url + "】接口失败：" + e.getMessage());
        }
        if (userAuthResponseModel == null) {
            throw new BizException(EntrustDataExceptionEnum.NETWORK_CONNECTION_FAILURE);
        }
        if (ifVerifyCode && !CommonConstant.ZERO.equals(userAuthResponseModel.getCode())){
            throw new BizException(EntrustDataExceptionEnum.NETWORK_CONNECTION_FAILURE);
        }
        //转换data数据
        T result = null;
        try {
            result = objectMapper.readValue(objectMapper.writeValueAsString(userAuthResponseModel.getData()), valueTypeRef);
        } catch (Exception e) {
            log.error("调用大数据【" + logStr + url + "】接口失败：" + e.getMessage());
        }
        if (result == null) {
            throw new BizException(EntrustDataExceptionEnum.NETWORK_CONNECTION_FAILURE);
        }
        return result;
    }
}
