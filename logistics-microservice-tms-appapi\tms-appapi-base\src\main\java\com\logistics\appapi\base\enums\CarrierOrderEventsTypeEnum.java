/**
 * Created by yun<PERSON><PERSON><PERSON> on 2017/10/23.
 */
package com.logistics.appapi.base.enums;

public enum CarrierOrderEventsTypeEnum {
    BY_ORDER(10, "承接订单"),
    DISPATCH_VEHICLE(20, "调度车辆"),
    HAS_CANCELED(30, "已取消"),
    UPDATE_VEHICLE_WAIT_AUDIT(40, "（待审核）修改车辆"),
    AUDIT_VEHICLES(45, "审核车辆"),
    UPDATE_VEHICLE_AUDIT(50, "（已审核）修改车辆"),
    UPDATE_VEHICLE_REJECT(55, "（已驳回）修改车辆"),
    ARRIVED_PICK_UP(60, "到达提货地"),
    PICK_UP(70, "提货"),
    ARRIVED_UNLOAD(80, "到达卸货地"),
    UNLOADING(90, "卸货"),
    SIGN_IN(100, "签收"),
    ;
    private Integer key;
    private String value;

    CarrierOrderEventsTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;

    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CarrierOrderEventsTypeEnum getEnum(Integer key) {
        for (CarrierOrderEventsTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }

}
