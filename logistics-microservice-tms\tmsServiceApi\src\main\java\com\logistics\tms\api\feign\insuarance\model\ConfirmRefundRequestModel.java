package com.logistics.tms.api.feign.insuarance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2019/12/25 17:53
 */
@Data
public class ConfirmRefundRequestModel {
    @ApiModelProperty(value = "车辆ID")
    private Long vehicleId;
    @ApiModelProperty("退保保险")
    private List<InsuranceRefundModel> insuranceRefundList;
    @ApiModelProperty("备注")
    private String remark;
}
