package com.logistics.management.webapi.test;

import com.logistics.management.webapi.base.utils.WordBarCodeUtils;
import com.yelo.tools.utils.WordToPdfUtils;
import org.junit.Test;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * word模板测试类
 */
public class FileTemplateTest {

    @Test
    public void testExportVehicleSettlementPdf() throws IOException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\vehicle_settlement_detail.docx";
        String outPath = "C:\\Users\\<USER>\\Desktop\\test1.docx";
        String outPath1 = "C:\\Users\\<USER>\\Desktop\\test1";

        Map<String, String> templateValues = new HashMap<>();
        templateValues.put("settlementYear","2024");
        templateValues.put("settlementMonth","2");
        templateValues.put("vehicleNo","鄂A12345");
        //运费
        templateValues.put("freightFeeTotal","100");
        templateValues.put("adjustFee","5");
        templateValues.put("adjustRemark","天冷了");
        //应扣除费用
        templateValues.put("deductingFeeTotal","1");
        templateValues.put("oilFilledFeeTotal","2");
        templateValues.put("insuranceFee","3");
        templateValues.put("tireCostTotal","4");
        templateValues.put("gpsDeductingFee","5");
        templateValues.put("parkingDeductingFee","6");
        templateValues.put("loanFee","7");
        templateValues.put("accidentInsuranceFee","8");
        //当月合计应付运费
        templateValues.put("freightFeeTotal1","88");
        templateValues.put("deductingFeeTotal1","31");
        templateValues.put("vehicleClaimFee","4");
        templateValues.put("accidentInsuranceClaimFee","4");
        templateValues.put("actualExpensesPayable","67");

        Map<String, List<String[]>> tableValueMapLeYi = new HashMap<>();
        List<String[]> tableItemsLeYi = new ArrayList<>();
        tableItemsLeYi.add(new String[]{"", "Y2211010007-01", "湖北省武汉市江夏区高新六路88号", "上海市闵行区浦江镇万康路268号上海乐橘科技有限公司", "2", "22.22"});
        tableItemsLeYi.add(new String[]{"", "Y2211010007-02", "湖北省武汉市江夏区高新六路99号", "上海市闵行区浦江镇万康路268号上海乐橘科技有限公司1", "3", "33.33"});
        tableValueMapLeYi.put("包装业务", tableItemsLeYi);

        Map<String, List<String[]>> tableValueMap = new HashMap<>();
        List<String[]> tableItems = new ArrayList<>();
        tableItems.add(new String[]{"", "QY01912260107-01", "上海市闵行区浦江镇万康路268号上海乐橘科技有限公司", "湖北省武汉市江夏区高新六路88号", "1", "11.11"});
        tableItems.add(new String[]{"", "QY01912260107-02", "上海市闵行区浦江镇万康路268号上海乐橘科技有限公司1", "湖北省武汉市江夏区高新六路99号", "4", "44.44"});
        tableValueMap.put("石化业务", tableItems);

        List<Map<String, List<String[]>>> insertTableList = new ArrayList<>();
        insertTableList.add(tableValueMapLeYi);
        insertTableList.add(tableValueMap);

        WordBarCodeUtils.fillWordTemplateValues(Files.newInputStream(Path.of(filePath)), outPath,
                templateValues, null, null, insertTableList);
        //将生成的word文档转换为pdf，保存在同一个路径下
        WordToPdfUtils.wordConverterToPdf(outPath1);
    }

    @Test
    public void testExportRouteContractPdf() throws IOException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\route_enquiry_contract_template.docx";
        String outPath = "C:\\Users\\<USER>\\Desktop\\test2.docx";
        String outPath1 = "C:\\Users\\<USER>\\Desktop\\test2";

        Map<String, String> templateValues = new HashMap<>();
        templateValues.put("contractCode","HT202407040001");
        templateValues.put("carrierCompany","上海伊利有限公司");
        templateValues.put("quoteOperator","张三");
        templateValues.put("quoteOperatorPhone","18037584956");
        templateValues.put("quoteTime","2024-07-05");
        templateValues.put("createdBy","李四");
        templateValues.put("createdTime","2024-07-03");
        templateValues.put("auditorNameOne","王五");
        templateValues.put("auditTimeOne","2024-07-04");

        Map<String, List<String[]>> tableValueMap = new HashMap<>();
        List<String[]> tableItems = new ArrayList<>();
        tableItems.add(new String[]{"香蕉", "上海市上海城区闵行区","湖北省武汉市武昌区", "999.99", "23.43元/吨", "单价", "2024-07-01", "2025-06-30"});
        tableItems.add(new String[]{"苹果", "【上海仓库2】上海市上海城区浦东新区", "湖北省武汉市江夏区", "1098.98", "44444元", "一口价", "2024-07-05", "2025-07-04"});
        tableValueMap.put("货物名称", tableItems);

        List<Map<String, List<String[]>>> insertTableList = new ArrayList<>();
        insertTableList.add(tableValueMap);

        WordBarCodeUtils.fillWordTemplateValues(Files.newInputStream(Path.of(filePath)), outPath,
                templateValues, null, null, insertTableList);
        //将生成的word文档转换为pdf，保存在同一个路径下
        WordToPdfUtils.wordConverterToPdf(outPath1);
    }

    @Test
    public void testExportWordToPdf() {
        String sourcePath = "C:\\Users\\<USER>\\Desktop\\test2.docx";
        String outPath = "C:\\Users\\<USER>\\Desktop\\test5.pdf";
        WordToPdfUtils.wordConverterToPdf(sourcePath, outPath);
    }

    @Test
    public void testR_Order() throws IOException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\pick_up_goods_bill_for_leyi.docx";
        String picPath = "C:\\Users\\<USER>\\Downloads\\Y1812070004-02.png";

        Map<String, String> templateValues = new HashMap<>();
        templateValues.put("carrierOrderCode", "Y1812070004-02");
        templateValues.put("companyEntrustName", "上海乐聚撒地方撒旦有限公司");
        templateValues.put("dispatchTime", "2023-01-01");
        templateValues.put("vehicleNumber", "沪A00000");
        templateValues.put("demandOrderCode", "HR1812070004");
        templateValues.put("driverName", "王五");
        templateValues.put("goodsName", "共享托盘");
        templateValues.put("driverPhone", "13123456789");
        templateValues.put("expectAmount", "12");
        templateValues.put("driverIdCardNum", "24214234242342324");
        templateValues.put("pickUpWarehouse", "发货仓库001");
        templateValues.put("loadAmount", "12");
        templateValues.put("consignor", "李四 13123456789");
        templateValues.put("pickUpWarehouseAddress", "上海市浦东新区浦东国际机场");
        templateValues.put("receiptsWarehouse", "收货仓库001");
        templateValues.put("receiver", "张三 13123456789");
        templateValues.put("receiptsWarehouseAddress", "上海市浦东新区迪士尼乐园");
        templateValues.put("remark", "备注");
        templateValues.put("publisher", "张三 13123456789");


        Map<String, InputStream> picMap = new HashMap<>();
        picMap.put("rId5", Files.newInputStream(Path.of(picPath)));

        WordBarCodeUtils.fillWordTemplateValues(Files.newInputStream(Path.of(filePath)), "C:\\Users\\<USER>\\Desktop\\test.docx",
                templateValues, picMap, null, null);
    }

    @Test
    public void testNOR_Order() throws IOException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\stock_out_for_leyi_template.docx";
        String picPath = "C:\\Users\\<USER>\\Downloads\\Y1812070004-02.png";

        Map<String, String> templateValues = new HashMap<>();
        templateValues.put("carrierOrderCode", "Y1812070004-02");
        templateValues.put("companyEntrustName", "上海乐聚撒地方撒旦有限公司");
        templateValues.put("dispatchTime", "2023-01-01");
        templateValues.put("vehicleNumber", "沪A00000");
        templateValues.put("demandOrderCode", "HR1812070004");
        templateValues.put("driverName", "王五");
        templateValues.put("goodsNameSize", "共享托盘 二级大类");
        templateValues.put("driverPhone", "13123456789");
        templateValues.put("expectAmount", "12");
        templateValues.put("driverIdCardNum", "24214234242342324");
        templateValues.put("pickUpWarehouse", "发货仓库001");
        templateValues.put("loadAmount", "12");
        templateValues.put("consignor", "李四 13123456789");
        templateValues.put("pickUpWarehouseAddress", "上海市浦东新区浦东国际机场");
        templateValues.put("receiptsWarehouse", "收货仓库001");
        templateValues.put("receiver", "张三 13123456789");
        templateValues.put("receiptsWarehouseAddress", "上海市浦东新区迪士尼乐园");

        Map<String, InputStream> picMap = new HashMap<>();
        picMap.put("rId5", Files.newInputStream(Path.of(picPath)));

        List<String[]> tableItems = new ArrayList<>();
        tableItems.add(new String[]{"1", "共享托盘", "1*1", "1", "1", "beiz"});

        WordBarCodeUtils.fillWordTemplateValues(Files.newInputStream(Path.of(filePath)), "C:\\Users\\<USER>\\Desktop\\test1.docx",
                templateValues, picMap, tableItems, null);
    }

    @Test
    public void toPDF() throws IOException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\load_sign_bills_template.docx";
        String picPath = "C:\\Users\\<USER>\\Downloads\\Y1812070004-02.png";
        String outPath = "C:\\Users\\<USER>\\Desktop\\test1.docx";
        String outPath1 = "C:\\Users\\<USER>\\Desktop\\test1";

        Map<String, String> templateValues = new HashMap<>();
        templateValues.put("carrierOrder", "test");
        templateValues.put("unloadWarehouse", "test");
        templateValues.put("loadWarehouse", "test");

        Map<String, InputStream> picMap = new HashMap<>();
        picMap.put("rId7", Files.newInputStream(Path.of(picPath)));

        WordBarCodeUtils.fillWordTemplateValues(Files.newInputStream(Path.of(filePath)), outPath,
                templateValues, picMap, null, null);
        //将生成的word文档转换为pdf，保存在同一个路径下
        WordToPdfUtils.wordConverterToPdf(outPath1);
    }

    @Test
    public void toPDF1() throws IOException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\load_sign_bills_template.docx";
        String picPath = "C:\\Users\\<USER>\\Downloads\\9f60d7a4487bc24eee9e8b1b0e19177f.jpeg";
        String outPath = "C:\\Users\\<USER>\\Desktop\\test1.docx";
        String outPath1 = "C:\\Users\\<USER>\\Desktop\\test1";

        Map<String, String> templateValues = new HashMap<>();
        templateValues.put("carrierOrder", "test");
        templateValues.put("unloadWarehouse", "test");
        templateValues.put("loadWarehouse", "test");

        Map<String, InputStream> picMap = new HashMap<>();
        picMap.put("rId7", Files.newInputStream(Path.of(picPath)));

        Map<String, List<String[]>> goodsMap = new HashMap<>();
        List<String[]> goodsList = new ArrayList<>();
        goodsList.add(new String[]{"", "货物信息1test1", "1", "2", ""});
        goodsMap.put("* 货物信息1", goodsList);

        Map<String, List<String[]>> goodsMap1 = new HashMap<>();
        List<String[]> goodsList1 = new ArrayList<>();
        goodsList1.add(new String[]{"", "货物信息2test1", "1", "2", ""});
        goodsMap1.put("* 货物信息2", goodsList1);

        List<Map<String, List<String[]>>> insertTableList = new ArrayList<>();
        insertTableList.add(goodsMap);
        insertTableList.add(goodsMap1);

        WordBarCodeUtils.fillWordTemplateValues(Files.newInputStream(Path.of(filePath)), outPath,
                templateValues, picMap, null, insertTableList);
        //将生成的word文档转换为pdf，保存在同一个路径下
        WordToPdfUtils.wordConverterToPdf(outPath1);
    }

    @Test
    public void toPDF2() throws IOException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\load_sign_bills_template.docx";
        String picPath = "C:\\Users\\<USER>\\Downloads\\9f60d7a4487bc24eee9e8b1b0e19177f.jpeg";
        String outPath = "C:\\Users\\<USER>\\Desktop\\test1.docx";
        String outPath1 = "C:\\Users\\<USER>\\Desktop\\test1";

        Map<String, String> templateValues = new HashMap<>();
        templateValues.put("carrierOrder", "test");
        templateValues.put("unloadWarehouse", "test");
        templateValues.put("loadWarehouse", "test");

        Map<String, InputStream> picMap = new HashMap<>();
        picMap.put("rId8", Files.newInputStream(Path.of(picPath)));

        Map<String, List<String[]>> goodsMap = new HashMap<>();
        List<String[]> goodsList = new ArrayList<>();
        goodsList.add(new String[]{"", "货物信息1test1", "1", "2", ""});
        goodsMap.put("*货物信息1", goodsList);

        Map<String, List<String[]>> goodsMap1 = new HashMap<>();
        List<String[]> goodsList1 = new ArrayList<>();
        goodsList1.add(new String[]{"", "货物信息2test1", "1", "2", ""});
        goodsMap1.put("*货物信息2", goodsList1);

        List<Map<String, List<String[]>>> insertTableList = new ArrayList<>();
        insertTableList.add(goodsMap);
        insertTableList.add(goodsMap1);

        WordBarCodeUtils.fillWordTemplateValues(Files.newInputStream(Path.of(filePath)), outPath,
                templateValues, picMap, null, insertTableList);
        //将生成的word文档转换为pdf，保存在同一个路径下
        WordToPdfUtils.wordConverterToPdf(outPath1);
    }
}
