package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2022/1/4 17:11
 */
public class ExportExcelTerminalCustomerAddress {
    private ExportExcelTerminalCustomerAddress() {
    }

    private static final Map<String, String> CUSTOMER_ADDRESS;

    static {
        CUSTOMER_ADDRESS = new LinkedHashMap<>();
        CUSTOMER_ADDRESS.put("地址", "collectAddress");
        CUSTOMER_ADDRESS.put("详细地址", "collectDetailAddress");
        CUSTOMER_ADDRESS.put("联系人姓名", "collectContactName");
        CUSTOMER_ADDRESS.put("地图链接", "mapLinkPath");
        CUSTOMER_ADDRESS.put("客户情况", "customerSituation");
        CUSTOMER_ADDRESS.put("备注说明", "remark");
        CUSTOMER_ADDRESS.put("最新操作人", "lastModifiedBy");
        CUSTOMER_ADDRESS.put("最新操作时间", "lastModifiedTime");
    }

    public static Map<String, String> getCustomerAddress() {
        return CUSTOMER_ADDRESS;
    }
}
