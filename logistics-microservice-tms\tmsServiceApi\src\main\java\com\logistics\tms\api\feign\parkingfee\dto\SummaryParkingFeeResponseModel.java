package com.logistics.tms.api.feign.parkingfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: sj
 * @Date: 2019/10/9 15:34
 */
@Data
public class SummaryParkingFeeResponseModel {
    @ApiModelProperty("待结算数量")
    private Integer waitCount;
    @ApiModelProperty("部分结算数量")
    private Integer partCount;
    @ApiModelProperty("结算完成数量")
    private Integer hasCount;
    @ApiModelProperty("已终止数量")
    private Integer terminateCount;
    @ApiModelProperty("总数量")
    private Integer allCount;
}
