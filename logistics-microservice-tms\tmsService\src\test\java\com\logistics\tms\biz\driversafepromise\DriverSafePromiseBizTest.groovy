package com.logistics.tms.biz.driversafepromise

import com.logistics.tms.controller.driversafepromise.request.AddSafePromiseRequestModel
import com.logistics.tms.controller.driversafepromise.request.DeleteSafePromiseRequestModel
import com.logistics.tms.controller.driversafepromise.request.ReissueSavePromiseRequestModel
import com.logistics.tms.controller.driversafepromise.request.SafePromiseDetailRequestModel
import com.logistics.tms.controller.driversafepromise.response.SafePromiseDetailResponseModel
import com.logistics.tms.controller.driversafepromise.request.SearchSafePromiseAppletListRequestModel
import com.logistics.tms.controller.driversafepromise.response.SearchSafePromiseAppletListResponseModel
import com.logistics.tms.controller.driversafepromise.request.SearchSafePromiseListRequestModel
import com.logistics.tms.controller.driversafepromise.response.SearchSafePromiseListResponseModel
import com.logistics.tms.controller.driversafepromise.request.SearchSignSafePromiseListRequestModel
import com.logistics.tms.controller.driversafepromise.response.SearchSignSafePromiseListResponseModel
import com.logistics.tms.controller.driversafepromise.request.SignSafePromiseDetailRequestModel
import com.logistics.tms.controller.driversafepromise.response.SignSafePromiseDetailResponseModel
import com.logistics.tms.controller.driversafepromise.response.SummarySavePromiseInfoModel
import com.logistics.tms.controller.driversafepromise.response.SummarySignSafePromiseAppletResponseModel
import com.logistics.tms.controller.driversafepromise.response.SummarySignSafePromiseResponseModel
import com.logistics.tms.controller.driversafepromise.request.UploadSafePromiseRequestModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.controller.driversafepromise.request.SafePromiseAppletDetailRequestModel
import com.logistics.tms.controller.driversafepromise.response.SafePromiseAppletDetailResponseModel
import com.logistics.tms.entity.TDriverSafePromiseRelation
import com.logistics.tms.entity.TStaffBasic
import com.logistics.tms.entity.TStaffVehicleRelation
import com.logistics.tms.entity.TVehicleDrivingLicense
import com.logistics.tms.mapper.TDriverSafePromiseMapper
import com.logistics.tms.mapper.TDriverSafePromiseRelationMapper
import com.logistics.tms.mapper.TStaffBasicMapper
import com.logistics.tms.mapper.TStaffVehicleRelationMapper
import com.logistics.tms.mapper.TVehicleDrivingLicenseMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class DriverSafePromiseBizTest extends Specification {
    @Mock
    CommonBiz commonBiz
    @Mock
    TDriverSafePromiseMapper tDriverSafePromiseMapper
    @Mock
    TDriverSafePromiseRelationMapper tDriverSafePromiseRelationMapper
    @Mock
    TStaffBasicMapper tStaffBasicMapper
    @Mock
    TVehicleDrivingLicenseMapper tVehicleDrivingLicenseMapper
    @Mock
    TStaffVehicleRelationMapper tqStaffVehicleRelationMapper
    @InjectMocks
    DriverSafePromiseBiz driverSafePromiseBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tDriverSafePromiseMapper.searchList(any())).thenReturn([new SearchSafePromiseListResponseModel()])
        when(tDriverSafePromiseRelationMapper.getSummaryBySafePromiseIds(anyString())).thenReturn([new SummarySavePromiseInfoModel()])

        expect:
        driverSafePromiseBiz.searchList(requestModel) == expectedResult

        where:
        requestModel                            || expectedResult
        new SearchSafePromiseListRequestModel() || null
    }

    @Unroll
    def "add Safe Promise where requestModel=#requestModel"() {
        given:
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(commonBiz.processReplaceTempPicture(anyString(), any(), anyString())).thenReturn("processReplaceTempPictureResponse")
        when(tDriverSafePromiseMapper.getCountByPeriod(anyString())).thenReturn(0)
        when(tDriverSafePromiseRelationMapper.batchInsert(any())).thenReturn(0)
        when(tStaffBasicMapper.getInternalDriverByIds(anyString())).thenReturn([new TStaffBasic(staffProperty: 0, name: "name", mobile: "mobile")])

        expect:
        driverSafePromiseBiz.addSafePromise(requestModel)
        assert expectedResult == false

        where:
        requestModel                     || expectedResult
        new AddSafePromiseRequestModel() || true
    }

    @Unroll
    def "get Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.addRealPath(anyString())).thenReturn("addRealPathResponse")
        when(tDriverSafePromiseRelationMapper.getBySafePromiseIdAndStatus(anyLong(), anyInt())).thenReturn([new TDriverSafePromiseRelation(staffId: 1l, status: 0)])

        expect:
        driverSafePromiseBiz.getDetail(requestModel) == expectedResult

        where:
        requestModel                        || expectedResult
        new SafePromiseDetailRequestModel() || new SafePromiseDetailResponseModel()
    }

    @Unroll
    def "del Safe Promise where requestModel=#requestModel"() {
        given:
        when(tDriverSafePromiseMapper.deleteSafePromiseById(anyLong(), anyString(), any())).thenReturn(0)

        expect:
        driverSafePromiseBiz.delSafePromise(requestModel)
        assert expectedResult == false

        where:
        requestModel                        || expectedResult
        new DeleteSafePromiseRequestModel() || true
    }

    @Unroll
    def "reissue Safe Promise where requestModel=#requestModel"() {
        given:
        when(tDriverSafePromiseMapper.getCountById(anyLong())).thenReturn(0)
        when(tDriverSafePromiseRelationMapper.batchInsert(any())).thenReturn(0)
        when(tDriverSafePromiseRelationMapper.getBySafePromiseIdAndStatus(anyLong(), anyInt())).thenReturn([new TDriverSafePromiseRelation(safePromiseId: 1l, staffId: 1l, status: 0)])
        when(tStaffBasicMapper.getInternalDriverByIds(anyString())).thenReturn([new TStaffBasic()])

        expect:
        driverSafePromiseBiz.reissueSafePromise(requestModel)
        assert expectedResult == false

        where:
        requestModel                         || expectedResult
        new ReissueSavePromiseRequestModel() || true
    }

    @Unroll
    def "search Sign List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tDriverSafePromiseRelationMapper.searchSignList(any())).thenReturn([new SearchSignSafePromiseListResponseModel()])

        expect:
        driverSafePromiseBiz.searchSignList(requestModel) == expectedResult

        where:
        requestModel                                || expectedResult
        new SearchSignSafePromiseListRequestModel() || null
    }

    @Unroll
    def "get Sign Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tVehicleDrivingLicenseMapper.getListByVehicleIds(anyString())).thenReturn([new TVehicleDrivingLicense(vehicleNo: "vehicleNo")])
        when(tqStaffVehicleRelationMapper.findRelationByStaffId(anyLong())).thenReturn([new TStaffVehicleRelation(vehicleId: 1l)])

        expect:
        driverSafePromiseBiz.getSignDetail(requestModel) == expectedResult

        where:
        requestModel                            || expectedResult
        new SignSafePromiseDetailRequestModel() || new SignSafePromiseDetailResponseModel()
    }

    @Unroll
    def "get Sign Summary where responseModel=#responseModel then expect: #expectedResult"() {
        given:
        when(tDriverSafePromiseRelationMapper.getSummarySignCount(any())).thenReturn(new SummarySignSafePromiseResponseModel())

        expect:
        driverSafePromiseBiz.getSignSummary(responseModel) == expectedResult

        where:
        responseModel                               || expectedResult
        new SearchSignSafePromiseListRequestModel() || new SummarySignSafePromiseResponseModel()
    }

    @Unroll
    def "search Applet List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(tDriverSafePromiseMapper.searchAppletList(any())).thenReturn([new SearchSafePromiseAppletListResponseModel()])

        expect:
        driverSafePromiseBiz.searchAppletList(requestModel) == expectedResult

        where:
        requestModel                                  || expectedResult
        new SearchSafePromiseAppletListRequestModel() || null
    }

    @Unroll
    def "get Applet Sign Summary where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.getLoginDriverAppletUserId()).thenReturn(1l)
        when(tDriverSafePromiseRelationMapper.getAppletSignSummary(any())).thenReturn(new SummarySignSafePromiseAppletResponseModel())

        expect:
        driverSafePromiseBiz.getAppletSignSummary(requestModel) == expectedResult

        where:
        requestModel                                  || expectedResult
        new SearchSafePromiseAppletListRequestModel() || new SummarySignSafePromiseAppletResponseModel()
    }

    @Unroll
    def "upload Safe Promise where requestModel=#requestModel"() {
        given:
        when(commonBiz.copyFileToDirectoryOfType(anyInt(), anyString(), anyString(), any())).thenReturn("copyFileToDirectoryOfTypeResponse")
        when(tVehicleDrivingLicenseMapper.getListByVehicleIds(anyString())).thenReturn([new TVehicleDrivingLicense(vehicleNo: "vehicleNo")])
        when(tqStaffVehicleRelationMapper.findRelationByStaffId(anyLong())).thenReturn([new TStaffVehicleRelation(vehicleId: 1l)])

        expect:
        driverSafePromiseBiz.uploadSafePromise(requestModel)
        assert expectedResult == false

        where:
        requestModel                        || expectedResult
        new UploadSafePromiseRequestModel() || true
    }

    @Unroll
    def "get Applet Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(commonBiz.addRealPath(anyString())).thenReturn("addRealPathResponse")
        when(tDriverSafePromiseRelationMapper.getAppletDetail(anyLong())).thenReturn(new SafePromiseAppletDetailResponseModel())

        expect:
        driverSafePromiseBiz.getAppletDetail(requestModel) == expectedResult

        where:
        requestModel                                                                                  || expectedResult
        new SafePromiseAppletDetailRequestModel() || new SafePromiseAppletDetailResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme