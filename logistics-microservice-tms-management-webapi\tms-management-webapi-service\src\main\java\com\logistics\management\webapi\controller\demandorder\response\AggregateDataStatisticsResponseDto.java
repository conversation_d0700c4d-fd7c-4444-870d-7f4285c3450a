package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2021/10/18 16:55
 */
@Data
public class AggregateDataStatisticsResponseDto {
    @ApiModelProperty("全部（单数）")
    private String allCount = "0";
    @ApiModelProperty("全部（数量）")
    private String allAmount = "0";
    @ApiModelProperty("待调度（单数）")
    private String waitDispatchCount = "0";
    @ApiModelProperty("待调度（数量）")
    private String waitDispatchAmount = "0";
    @ApiModelProperty("已调度（单数）")
    private String dispatchCount = "0";
    @ApiModelProperty("已调度（数量）")
    private String dispatchAmount = "0";
    @ApiModelProperty("待纠错（单数）")
    private String waitCorrectCount = "0";
    @ApiModelProperty("待纠错（数量）")
    private String waitCorrectAmount = "0";
}
