package com.logistics.management.webapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wjf
 * @date: 2019/10/14 16:57
 */
@Data
public class GetParkingFeeByVehicleIdResponseModel {
    private Long parkingFeeId;
    private Integer status;
    private BigDecimal parkingFee;
    private Integer cooperationPeriod;
    @ApiModelProperty("停车费用结算月份")
    private String deductingMonth;
    @ApiModelProperty("停车费用当月扣减费用")
    private BigDecimal deductingFee;
    @ApiModelProperty("停车费用剩余未扣减费用")
    private BigDecimal remainingDeductingFee;
    @ApiModelProperty("终止时间（用于判断是否是合作的最后一个月）")
    private Date finishDate;
    @ApiModelProperty("停车费用查询月份")
    private String currentMonth;
}
