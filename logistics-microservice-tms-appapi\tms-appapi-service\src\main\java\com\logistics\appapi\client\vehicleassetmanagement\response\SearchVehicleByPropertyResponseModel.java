package com.logistics.appapi.client.vehicleassetmanagement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/8
 */
@Data
public class SearchVehicleByPropertyResponseModel {

	@ApiModelProperty("车辆id")
	private Long vehicleId;

	@ApiModelProperty("车牌号")
	private String vehicleNo;

	@ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
	private Integer vehicleProperty;

	@ApiModelProperty("车辆类别：1 牵引车 2 挂车 3 一体车")
	private Integer vehicleCategory;
}
