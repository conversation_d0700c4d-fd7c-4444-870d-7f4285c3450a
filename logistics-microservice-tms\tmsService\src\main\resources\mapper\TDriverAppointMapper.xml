<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TDriverAppointMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TDriverAppoint">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="demand_order_id" jdbcType="BIGINT" property="demandOrderId" />
    <result column="demand_order_code" jdbcType="VARCHAR" property="demandOrderCode" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="staff_property" jdbcType="INTEGER" property="staffProperty" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="staff_mobile" jdbcType="VARCHAR" property="staffMobile" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="goods_amount_total" jdbcType="DECIMAL" property="goodsAmountTotal" />
    <result column="goods_price_total" jdbcType="DECIMAL" property="goodsPriceTotal" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_user_name" jdbcType="VARCHAR" property="customerUserName" />
    <result column="customer_user_mobile" jdbcType="VARCHAR" property="customerUserMobile" />
    <result column="publish_user_name" jdbcType="VARCHAR" property="publishUserName" />
    <result column="publish_user_mobile" jdbcType="VARCHAR" property="publishUserMobile" />
    <result column="if_associated_vehicle" jdbcType="INTEGER" property="ifAssociatedVehicle" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, demand_order_id, demand_order_code, business_type, staff_id, staff_property, 
    staff_name, staff_mobile, vehicle_id, vehicle_no, goods_amount_total, goods_price_total, 
    publish_time, customer_name, customer_user_name, customer_user_mobile, publish_user_name, 
    publish_user_mobile, if_associated_vehicle, created_by, created_time, last_modified_by, 
    last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_driver_appoint
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_driver_appoint
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TDriverAppoint">
    insert into t_driver_appoint (id, demand_order_id, demand_order_code, 
      business_type, staff_id, staff_property, 
      staff_name, staff_mobile, vehicle_id, 
      vehicle_no, goods_amount_total, goods_price_total, 
      publish_time, customer_name, customer_user_name, 
      customer_user_mobile, publish_user_name, publish_user_mobile, 
      if_associated_vehicle, created_by, created_time, 
      last_modified_by, last_modified_time, valid
      )
    values (#{id,jdbcType=BIGINT}, #{demandOrderId,jdbcType=BIGINT}, #{demandOrderCode,jdbcType=VARCHAR}, 
      #{businessType,jdbcType=INTEGER}, #{staffId,jdbcType=BIGINT}, #{staffProperty,jdbcType=INTEGER}, 
      #{staffName,jdbcType=VARCHAR}, #{staffMobile,jdbcType=VARCHAR}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehicleNo,jdbcType=VARCHAR}, #{goodsAmountTotal,jdbcType=DECIMAL}, #{goodsPriceTotal,jdbcType=DECIMAL}, 
      #{publishTime,jdbcType=TIMESTAMP}, #{customerName,jdbcType=VARCHAR}, #{customerUserName,jdbcType=VARCHAR}, 
      #{customerUserMobile,jdbcType=VARCHAR}, #{publishUserName,jdbcType=VARCHAR}, #{publishUserMobile,jdbcType=VARCHAR}, 
      #{ifAssociatedVehicle,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedBy,jdbcType=VARCHAR}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TDriverAppoint">
    insert into t_driver_appoint
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="demandOrderId != null">
        demand_order_id,
      </if>
      <if test="demandOrderCode != null">
        demand_order_code,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="staffProperty != null">
        staff_property,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="staffMobile != null">
        staff_mobile,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleNo != null">
        vehicle_no,
      </if>
      <if test="goodsAmountTotal != null">
        goods_amount_total,
      </if>
      <if test="goodsPriceTotal != null">
        goods_price_total,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerUserName != null">
        customer_user_name,
      </if>
      <if test="customerUserMobile != null">
        customer_user_mobile,
      </if>
      <if test="publishUserName != null">
        publish_user_name,
      </if>
      <if test="publishUserMobile != null">
        publish_user_mobile,
      </if>
      <if test="ifAssociatedVehicle != null">
        if_associated_vehicle,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="demandOrderId != null">
        #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null">
        #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffProperty != null">
        #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsAmountTotal != null">
        #{goodsAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="goodsPriceTotal != null">
        #{goodsPriceTotal,jdbcType=DECIMAL},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserName != null">
        #{customerUserName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserMobile != null">
        #{customerUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="publishUserName != null">
        #{publishUserName,jdbcType=VARCHAR},
      </if>
      <if test="publishUserMobile != null">
        #{publishUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="ifAssociatedVehicle != null">
        #{ifAssociatedVehicle,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TDriverAppoint">
    update t_driver_appoint
    <set>
      <if test="demandOrderId != null">
        demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      </if>
      <if test="demandOrderCode != null">
        demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="staffProperty != null">
        staff_property = #{staffProperty,jdbcType=INTEGER},
      </if>
      <if test="staffName != null">
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="staffMobile != null">
        staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsAmountTotal != null">
        goods_amount_total = #{goodsAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="goodsPriceTotal != null">
        goods_price_total = #{goodsPriceTotal,jdbcType=DECIMAL},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserName != null">
        customer_user_name = #{customerUserName,jdbcType=VARCHAR},
      </if>
      <if test="customerUserMobile != null">
        customer_user_mobile = #{customerUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="publishUserName != null">
        publish_user_name = #{publishUserName,jdbcType=VARCHAR},
      </if>
      <if test="publishUserMobile != null">
        publish_user_mobile = #{publishUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="ifAssociatedVehicle != null">
        if_associated_vehicle = #{ifAssociatedVehicle,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TDriverAppoint">
    update t_driver_appoint
    set demand_order_id = #{demandOrderId,jdbcType=BIGINT},
      demand_order_code = #{demandOrderCode,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=BIGINT},
      staff_property = #{staffProperty,jdbcType=INTEGER},
      staff_name = #{staffName,jdbcType=VARCHAR},
      staff_mobile = #{staffMobile,jdbcType=VARCHAR},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
      goods_amount_total = #{goodsAmountTotal,jdbcType=DECIMAL},
      goods_price_total = #{goodsPriceTotal,jdbcType=DECIMAL},
      publish_time = #{publishTime,jdbcType=TIMESTAMP},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_user_name = #{customerUserName,jdbcType=VARCHAR},
      customer_user_mobile = #{customerUserMobile,jdbcType=VARCHAR},
      publish_user_name = #{publishUserName,jdbcType=VARCHAR},
      publish_user_mobile = #{publishUserMobile,jdbcType=VARCHAR},
      if_associated_vehicle = #{ifAssociatedVehicle,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>