package com.logistics.management.webapi.client.biddingorder.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.biddingorder.BiddingOrderClient;
import com.logistics.management.webapi.client.biddingorder.request.*;
import com.logistics.management.webapi.client.biddingorder.response.*;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/05/06
 */
@Component
public class BiddingOrderClientHystrix implements BiddingOrderClient {

    @Override
    public Result<PageInfo<SearchBiddingOrderListResponseModel>> searchBiddingOrderListByManager(@Valid SearchBiddingOrderListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<BiddingOrderDetailByManagerResponseModel> biddingOrderDetailByManager(@Valid BiddingOrderDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> stopBiddingByManager(@Valid StopBiddingRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> cancelBiddingByManager(@Valid CancelBiddingRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<BottomPriceResponseModel> bottomPrice(BottomPriceRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> confirmQuote(ConfirmQuoteRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> modifyQuote(ModifyQuoteRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> rebiddingQuote(RebiddingRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> delDemand(DelDemandRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SearchBiddingDemandResponseModel>> searchBiddingDemand(SearchBiddingDemandRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<BiddingOrderQuoteDetailResponseModel> biddingOrderQuoteDetail(BiddingOrderQuoteDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> addDemand(AddDemandRequestModel requestModel) {
        return Result.timeout();
    }
}
