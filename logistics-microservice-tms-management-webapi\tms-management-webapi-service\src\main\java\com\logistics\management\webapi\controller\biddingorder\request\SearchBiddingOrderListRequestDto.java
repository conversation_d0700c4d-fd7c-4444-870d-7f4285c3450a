package com.logistics.management.webapi.controller.biddingorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SearchBiddingOrderListRequestDto extends AbstractPageForm<SearchBiddingOrderListRequestDto> {

    /**
     * 竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消
     */
    @ApiModelProperty("竞价状态：1 报价中，2 暂停报价，3 待选择，4 完成报价 5报价取消")
    private String biddingStatus;

    /**
     * 装卸方式 全部-空、1一装一卸、2多装一卸
     */
    @ApiModelProperty("装卸方式 全部-空、1一装一卸、2多装一卸")
    private String handlingMode;

    /**
     * 发货地址
     */
    @ApiModelProperty("发货地址")
    private String loadAddress;

    /**
     * 收货地址
     */
    @ApiModelProperty("收货地址")
    private String unloadAddress;

    /**
     * 车长
     */
    @ApiModelProperty("车长")
    private String vehicleLength;
}
