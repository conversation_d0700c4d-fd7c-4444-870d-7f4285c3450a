package com.logistics.tms.controller.settlestatement.packaging;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.settlestatement.CarrierSettleStatementManageBiz;
import com.logistics.tms.controller.settlestatement.packaging.request.*;
import com.logistics.tms.controller.settlestatement.packaging.response.*;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 包装业务车主对账管理
 * @author: wjf
 * @date: 2023/10/26 11:31
 */
@RestController
@Api(value = "包装业务车主对账管理")
@RequestMapping(value = "/service/carrierStatementManage")
public class CarrierSettleStatementController {

    @Resource
    private CarrierSettleStatementManageBiz carrierSettleStatementManageBiz;

    /*
    待对账单
    * */
    /**
     * 查询待对账运单列表
     *
     * @param requestModel 筛选条件
     * @return 待对账运单列表
     */
    @ApiOperation(value = "待对账运单列表")
    @PostMapping(value = "/waitSettleStatementList")
    public Result<PageInfo<CarrierWaitSettleStatementListResponseModel>> waitSettleStatementList(@RequestBody CarrierWaitSettleStatementListRequestModel requestModel) {
        PageInfo<CarrierWaitSettleStatementListResponseModel> result = carrierSettleStatementManageBiz.waitSettleStatementList(requestModel);
        return Result.success(result);
    }

    /**
     * 导出查询待对账运单列表
     *
     * @param requestModel 筛选条件
     * @return 待对账运单列表
     */
    @ApiOperation(value = "导出待对账运单列表")
    @PostMapping(value = "/exportWaitSettleStatementList")
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<PageInfo<CarrierWaitSettleStatementListResponseModel>> exportWaitSettleStatementList(@RequestBody CarrierWaitSettleStatementListRequestModel requestModel) {
        PageInfo<CarrierWaitSettleStatementListResponseModel> result = carrierSettleStatementManageBiz.waitSettleStatementList(requestModel);
        return Result.success(result);
    }

    /**
     * 生成对账单
     *
     * @param requestModel 对账单信息
     * @return 操作结果
     */
    @ApiOperation(value = "生成对账单")
    @PostMapping(value = "/createSettleStatement")
    public Result<Boolean> createSettleStatement(@RequestBody CarrierCreateSettleStatementRequestModel requestModel) {
        carrierSettleStatementManageBiz.createSettleStatement(requestModel);
        return Result.success(true);
    }

    /**
     * 查询车主费点
     *
     * @param requestModel 车主id
     * @return 费点
     */
    @ApiOperation(value = "查询车主税点")
    @PostMapping(value = "/queryTaxPoint")
    public Result<CarrierTaxPointResponseModel> queryTaxPoint(@RequestBody CarrierTaxPointRequestModel requestModel) {
        return Result.success(carrierSettleStatementManageBiz.queryTaxPoint(requestModel));
    }


    /*
    对账单功能
    * */
    /**
     * 对账单列表
     *
     * @param requestModel 筛选条件
     * @return 对账单
     */
    @ApiOperation(value = "对账单列表")
    @PostMapping(value = "/settleStatementList")
    public Result<PageInfo<CarrierSettleStatementListResponseModel>> settleStatementList(@RequestBody CarrierSettleStatementListRequestModel requestModel) {
        return Result.success(carrierSettleStatementManageBiz.settleStatementList(requestModel));
    }

    /**
     * 关联运单号
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "关联运单号")
    @PostMapping(value = "/associationCarrierOrder")
    public Result<CarrierAssociationCarrierOrderResponseModel> associationCarrierOrder(@RequestBody CarrierAssociationCarrierOrderRequestModel requestModel) {
        return Result.success(carrierSettleStatementManageBiz.associationCarrierOrder(requestModel));
    }

    /**
     * 编辑对账月份
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "编辑对账月份")
    @PostMapping(value = "/modifySettleStatementMonth")
    public Result<Boolean> modifySettleStatementMonth(@RequestBody ModifySettleStatementMonthRequestModel requestModel) {
        carrierSettleStatementManageBiz.modifySettleStatementMonth(requestModel);
        return Result.success(true);
    }

    /**
     * 编辑结算主体
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "编辑结算主体")
    @PostMapping(value = "/modifyPlatformCompany")
    public Result<Boolean> modifyPlatformCompany(@RequestBody ModifyPlatformCompanyRequestModel requestModel) {
        carrierSettleStatementManageBiz.modifyPlatformCompany(requestModel);
        return Result.success(true);
    }

    /**
     * 修改对账单费点
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "修改对账单费点")
    @PostMapping(value = "/modifyTaxPoint")
    public Result<Boolean> modifyTaxPoint(@RequestBody CarrierModifyTaxPointRequestModel requestModel) {
        carrierSettleStatementManageBiz.modifyTaxPoint(requestModel);
        return Result.success(true);
    }

    /**
     * 修改对账单名称
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "修改对账单名称")
    @PostMapping(value = "/renameStatement")
    public Result<Boolean> renameStatement(@RequestBody CarrierEditSettleStatementNameRequestModel requestModel) {
        carrierSettleStatementManageBiz.renameStatement(requestModel);
        return Result.success(true);
    }

    /**
     * 差异调整-回显
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "差异调整-回显")
    @PostMapping(value = "/queryAdjustCost")
    public Result<CarrierAdjustCostResponseModel> queryAdjustCost(@RequestBody CarrierQueryAdjustCostRequestModel requestModel) {
        return Result.success(carrierSettleStatementManageBiz.queryAdjustCost(requestModel));
    }

    /**
     * 差异调整-发起
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "差异调整-发起")
    @PostMapping(value = "/AdjustCost")
    public Result<Boolean> adjustCost(@RequestBody CarrierAdjustRequestModel requestModel) {
        carrierSettleStatementManageBiz.adjustCost(requestModel);
        return Result.success(true);
    }

    /**
     * 申请开票
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "申请开票")
    @PostMapping(value = "/applyInvoicing")
    public Result<Boolean> applyInvoicing(@RequestBody SettleStatementApplyInvoicingRequestModel requestModel) {
        carrierSettleStatementManageBiz.applyInvoicing(requestModel);
        return Result.success(true);
    }

    /**
     * 撤销对账单
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "撤销对账单")
    @PostMapping(value = "/cancel")
    public Result<Boolean> cancel(@RequestBody CarrierCancelRequestModel requestModel) {
        carrierSettleStatementManageBiz.cancel(requestModel);
        return Result.success(true);
    }

    /*
     对账单下运单归档
     **/
    /**
     * 对账单归档列表
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "对账单归档列表")
    @PostMapping(value = "/statementArchiveList")
    public Result<PageInfo<StatementArchiveListResponseModel>> statementArchiveList(@RequestBody StatementArchiveListRequestModel requestModel) {
        return Result.success(carrierSettleStatementManageBiz.statementArchiveList(requestModel));
    }

    /**
     * 对账单归档/编辑
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "对账单归档/编辑")
    @PostMapping(value = "/statementArchive")
    public Result<Boolean> statementArchive(@RequestBody StatementArchiveRequestModel requestModel) {
        carrierSettleStatementManageBiz.statementArchive(requestModel);
        return Result.success(true);
    }

    /**
     * 查看归档图片
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查看归档图片")
    @PostMapping(value = "/archiveTicketList")
    public Result<List<String>> archiveTicketList(@RequestBody StatementArchiveTicketListRequestModel requestModel) {
        return Result.success(carrierSettleStatementManageBiz.archiveTicketList(requestModel));
    }

    /**
     * 查看归档详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查看归档详情")
    @PostMapping(value = "/statementArchiveDetail")
    public Result<StatementArchiveDetailResponseModel> statementArchiveDetail(@RequestBody StatementArchiveDetailRequestModel requestModel) {
        return Result.success(carrierSettleStatementManageBiz.statementArchiveDetail(requestModel));
    }

    /**
     * 查询对账单下待归档运单
     *
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查询对账单下待归档运单")
    @PostMapping(value = "/statementWaitArchiveList")
    public Result<List<StatementWaitArchiveListResponseModel>> statementWaitArchiveList(@RequestBody StatementWaitArchiveListRequestModel requestModel) {
        return Result.success(carrierSettleStatementManageBiz.statementWaitArchiveList(requestModel));
    }


    /*
    对账单详情
    **/
    /**
     * 对账单详情-提交对账单
     *
     * @param requestModel 对账单id
     * @return 操作结果
     */
    @ApiOperation(value = "对账单详情-提交")
    @PostMapping(value = "/applicationCheck")
    public Result<Boolean> submitSettleStatement(@RequestBody CarrierSettleStatementIdRequestModel requestModel) {
        carrierSettleStatementManageBiz.submitSettleStatement(requestModel);
        return Result.success(true);
    }

    /**
     * 对账单详情-审核/驳回
     *
     * @param requestModel 对账单id
     * @return 操作结果
     */
    @ApiOperation(value = "对账单详情-审核/驳回")
    @PostMapping(value = "/auditOrReject")
    public Result<Boolean> auditOrReject(@RequestBody CarrierChangeSettleStatementStatsRequestModel requestModel) {
        carrierSettleStatementManageBiz.auditOrReject(requestModel);
        return Result.success(true);
    }

    /**
     * 对账单详情-合计数据
     *
     * @param requestModel 对账单id
     * @return 合计
     */
    @ApiOperation(value = "对账单详情-合计")
    @PostMapping(value = "/settleStatementDetailTotal")
    public Result<CarrierSettleStatementDetailTotalResponseModel> settleStatementDetailTotal(@RequestBody CarrierSettleStatementIdRequestModel requestModel) {
        return Result.success(carrierSettleStatementManageBiz.settleStatementDetailTotal(requestModel));
    }

    /**
     * 对账单详情-对账单运单列表
     *
     * @param requestModel 对账单id,筛选条件
     * @return 运单条目信息
     */
    @ApiOperation(value = "对账单详情-对账单运单列表")
    @PostMapping(value = "/settleStatementDetailList")
    public Result<PageInfo<CarrierSettleStatementDetailListResponseModel>> settleStatementDetailList(@RequestBody CarrierSettleStatementDetailListRequestModel requestModel) {
        return Result.success(carrierSettleStatementManageBiz.settleStatementDetailList(requestModel));
    }

    /**
     * 对账单详情-对账单运单条目信息
     *
     * @param requestModel 对账单id,筛选条件
     * @return 运单条目信息
     */
    @ApiOperation(value = "导出对账单详情-对账单运单列表")
    @PostMapping(value = "/exportSettleStatementDetailList")
    @IgnoreParamsLog(type = IgnoreParamsLog.IgnoreLogType.RESPONSE)
    public Result<PageInfo<CarrierSettleStatementDetailListResponseModel>> exportSettleStatementDetailList(@RequestBody CarrierSettleStatementDetailListRequestModel requestModel) {
        return Result.success(carrierSettleStatementManageBiz.settleStatementDetailList(requestModel));
    }

    /**
     * 对账单详情-查询添加运单列表
     *
     * @param requestModel 筛选条件
     * @return 运单信息
     */
    @ApiOperation(value = "对账单详情-查询添加运单列表")
    @PostMapping(value = "/addCarrierOrderList")
    public Result<PageInfo<CarrierWaitSettleStatementListResponseModel>> addCarrierOrderList(@RequestBody CarrierAddCarrierOrderListRequestModel requestModel) {
        return Result.success(carrierSettleStatementManageBiz.addCarrierOrderList(requestModel));
    }

    /**
     * 对账单详情-添加运单
     *
     * @param requestModel 对账单id,运单筛选条件
     * @return 操作结果
     */
    @ApiOperation(value = "对账单详情-添加运单")
    @PostMapping(value = "/addCarrierOrder")
    public Result<Boolean> addCarrierOrderConfirm(@RequestBody CarrierAddCarrierOrderConfirmRequestModel requestModel) {
        carrierSettleStatementManageBiz.addCarrierOrderConfirm(requestModel);
        return Result.success(true);
    }

    /**
     * 对账单详情-撤销运单
     *
     * @param requestModel 对账单运单id
     * @return 操作结果
     */
    @ApiOperation(value = "对账单详情-撤销运单")
    @PostMapping(value = "/cancelCarrierOrder")
    public Result<Boolean> cancelCarrierOrder(@RequestBody CarrierUndoCarrierOrderRequestModel requestModel) {
        carrierSettleStatementManageBiz.cancelCarrierOrder(requestModel);
        return Result.success(true);
    }

    /**
     * 对账单详情-已对账驳回
     *
     * @param requestModel 对账单id
     * @return 操作结果
     */
    @ApiOperation(value = "对账单详情-已对账驳回")
    @PostMapping(value = "/rejectCompleteSettle")
    public Result<Boolean> rejectCompleteSettle(@RequestBody CarrierSettleStatementIdRequestModel requestModel) {
        carrierSettleStatementManageBiz.rejectCompleteSettle(requestModel);
        return Result.success(true);
    }

}
