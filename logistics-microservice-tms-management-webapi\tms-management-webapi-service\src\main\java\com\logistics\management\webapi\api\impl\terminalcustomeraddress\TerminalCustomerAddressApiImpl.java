package com.logistics.management.webapi.api.impl.terminalcustomeraddress;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.logistics.management.webapi.api.feign.terminalcustomeraddress.TerminalCustomerAddressApi;
import com.logistics.management.webapi.api.feign.terminalcustomeraddress.dto.*;
import com.logistics.management.webapi.api.impl.terminalcustomeraddress.mapping.SearchTerminalCustomerAddressListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportExcelTerminalCustomerAddress;
import com.logistics.tms.api.feign.terminalcustomeraddress.TerminalCustomerAddressServiceApi;
import com.logistics.tms.api.feign.terminalcustomeraddress.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2022/1/4 13:35
 */
@RestController
public class TerminalCustomerAddressApiImpl implements TerminalCustomerAddressApi {

    @Autowired
    private TerminalCustomerAddressServiceApi terminalCustomerAddressServiceApi;

    /**
     * 终端客户地址列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchTerminalCustomerAddressListResponseDto>> searchTerminalCustomerAddressList(@RequestBody SearchTerminalCustomerAddressListRequestDto requestDto) {
        Result<PageInfo<SearchTerminalCustomerAddressListResponseModel>> result = terminalCustomerAddressServiceApi.searchTerminalCustomerAddressList(MapperUtils.mapper(requestDto, SearchTerminalCustomerAddressListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchTerminalCustomerAddressListResponseDto> list = MapperUtils.mapper(pageInfo.getList(), SearchTerminalCustomerAddressListResponseDto.class, new SearchTerminalCustomerAddressListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 导出终端客户地址列表
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportTerminalCustomerAddressList(SearchTerminalCustomerAddressListRequestDto requestDto, HttpServletResponse response) {
        Result<List<SearchTerminalCustomerAddressListResponseModel>> result = terminalCustomerAddressServiceApi.exportTerminalCustomerAddressList(MapperUtils.mapper(requestDto, SearchTerminalCustomerAddressListRequestModel.class));
        result.throwException();
        List<SearchTerminalCustomerAddressListResponseDto> list = MapperUtils.mapper(result.getData(), SearchTerminalCustomerAddressListResponseDto.class, new SearchTerminalCustomerAddressListMapping());
        String fileName = "终端客户地址管理" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportExcelTerminalCustomerAddress.getCustomerAddress();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return list;
            }
        });
    }

    /**
     * 新增/修改终端客户地址
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addOrModifyTerminalCustomerAddress(@RequestBody @Valid AddOrModifyTerminalCustomerAddressRequestDto requestDto) {
        return terminalCustomerAddressServiceApi.addOrModifyTerminalCustomerAddress(MapperUtils.mapper(requestDto, AddOrModifyTerminalCustomerAddressRequestModel.class));
    }

    /**
     * 终端客户地址详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<GetTerminalCustomerAddressDetailResponseDto> getTerminalCustomerAddressDetail(@RequestBody @Valid GetTerminalCustomerAddressDetailRequestDto requestDto) {
        Result<GetTerminalCustomerAddressDetailResponseModel> result = terminalCustomerAddressServiceApi.getTerminalCustomerAddressDetail(MapperUtils.mapper(requestDto, GetTerminalCustomerAddressDetailRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), GetTerminalCustomerAddressDetailResponseDto.class));
    }

    /**
     * 批量删除终端客户地址
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> delTerminalCustomerAddress(@RequestBody @Valid DeleteTerminalCustomerAddressRequestDto requestDto) {
        return terminalCustomerAddressServiceApi.delTerminalCustomerAddress(MapperUtils.mapper(requestDto, DeleteTerminalCustomerAddressRequestModel.class));
    }
}
