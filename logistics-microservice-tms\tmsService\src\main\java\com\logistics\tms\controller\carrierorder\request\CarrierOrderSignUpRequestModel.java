package com.logistics.tms.controller.carrierorder.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/12/13 13:27
 */
@Data
public class CarrierOrderSignUpRequestModel {
    @ApiModelProperty("运单ID")
    private Long carrierOrderId;
    @ApiModelProperty("签收运费")
    private BigDecimal signFreightFee;
    @ApiModelProperty("签收货物列表 - 网络货运同步操作必传需要")
    private List<CarrierOrderSignUpGoodsRequestModel> signUpGoodsList;
}
