package com.logistics.tms.controller.vehicleassetmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/6/10 9:23
 */
@Data
public class ImportVehicleBasicInfoListRequestModel {
    //基础信息[t_vehicle_basic]
    @ApiModelProperty("车辆使用性质 1普货 2危货")
    private Integer usageProperty;
    @ApiModelProperty("是否安装GPS: 1 是 0 否")
    private Integer ifInstallGps;
    @ApiModelProperty("是否入网石化 1 是 0 否")
    private Integer ifAccessSinopec;
    @ApiModelProperty("类型 1 自主，2 外部，3 自营")
    private Integer vehicleProperty;
    @ApiModelProperty("真实所属车主")
    private String vehicleOwner;
    @ApiModelProperty("排放标准类型：1 国一，2 国二，3 国三，4 国四，5 国五，6 国六")
    private Integer emissionStandardType;

    //机动车行驶证[t_vehicle_driving_license]
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("车牌类型")
    private Long vehicleType;
    @ApiModelProperty("车牌类型")
    private String vehicleTypeLabel;
    @ApiModelProperty("所有人")
    private String owner;
    @ApiModelProperty("住址")
    private String address;
    @ApiModelProperty("品牌")
    private String brand;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("车辆识别号")
    private String vehicleIdentificationNumber;
    @ApiModelProperty("发动机号码")
    private String engineNumber;
    @ApiModelProperty("车辆行驶证发证部门")
    private String drivingCertificationDepartmentOne;
    @ApiModelProperty("注册日期")
    private Date registrationDate;
    @ApiModelProperty("车辆行驶证表发证日期")
    private Date drivingIssueDate;
    @ApiModelProperty("归档编号")
    private String filingNumber;
    @ApiModelProperty("核定载人数")
    private Integer authorizedCarryingCapacity;
    @ApiModelProperty("总质量(KG)")
    private BigDecimal totalWeight;
    @ApiModelProperty("整备质量(KG)")
    private BigDecimal curbWeight;
    @ApiModelProperty("准牵引总质量(KG)")
    private BigDecimal tractionMassWeight;
    @ApiModelProperty("核定载质量(KG)")
    private BigDecimal approvedLoadWeight;
    @ApiModelProperty("长(mm)")
    private Integer length;
    @ApiModelProperty("宽(mm)")
    private Integer width;
    @ApiModelProperty("高(mm)")
    private Integer height;
    @ApiModelProperty("车辆强制报废日期")
    private Date obsolescenceDate;
    @ApiModelProperty("车辆轴数")
    private Integer axleNumber;
    @ApiModelProperty("驱动轴数")
    private Integer driveShaftNumber;
    @ApiModelProperty("轮胎数")
    private Integer tiresNumber;
    @ApiModelProperty("车牌颜色 1 黄色 2 蓝色")
    private Integer plateColor;
    @ApiModelProperty("车身颜色")
    private String bodyColor;

    //车辆行驶证检查记录表[t_vehicle_driving_license_annual_review]
    @ApiModelProperty("行驶证检查有效期")
    private Date checkVehicleValidDate;

    //道路运输证[t_vehicle_road_transport_certificate]
    @ApiModelProperty("发证签")
    private String certificationSign;
    @ApiModelProperty("经营许可证号")
    private String businessLicenseNumber;
    @ApiModelProperty("经济类型")
    private String economicType;
    @ApiModelProperty("吨位")
    private BigDecimal transportTonnage;
    @ApiModelProperty("经营范围")
    private String businessScope;
    @ApiModelProperty("道路运输证发证部门")
    private String roadTransportCertificationDepartment;
    @ApiModelProperty("道路运输证发证日期")
    private Date issueDate;
    @ApiModelProperty("初领日期")
    private Date obtainDate;

    //道路运输证年审记录表[t_vehicle_road_transport_certificate_annual_review]
    @ApiModelProperty("车辆运营证年审有效期")
    private Date checkRoadValidDate;

    @ApiModelProperty("认证期限开始时间")
    private Date authenticationStartTime;
    @ApiModelProperty("认证期限到期时间")
    private Date authenticationExpireTime;

    //车辆GPS安装记录表[t_vehicle_gps_record]
    @ApiModelProperty("备注")
    private Date installTime;
    @ApiModelProperty("终端型号")
    private String terminalType;
    @ApiModelProperty("终端SIM卡号")
    private String simNumber;
    @ApiModelProperty("服务提供商")
    private String gpsServiceProvider;

    //等级评定表[t_vehicle_grade_estimation_record]
    @ApiModelProperty("等级评定检查日期")
    private Date estimationDate;

    @ApiModelProperty("登记证书编号")
    private String registrationCertificationNumber;
}
