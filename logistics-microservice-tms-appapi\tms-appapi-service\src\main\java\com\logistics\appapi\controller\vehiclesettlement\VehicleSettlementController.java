package com.logistics.appapi.controller.vehiclesettlement;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.controller.common.CommonBiz;
import com.logistics.appapi.base.constant.CommonConstant;
import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.base.enums.AppApiExceptionEnum;
import com.logistics.appapi.client.vehiclesettlement.VehicleSettlementClient;
import com.logistics.appapi.client.vehiclesettlement.request.DriverReconciliationConfirmRequestModel;
import com.logistics.appapi.client.vehiclesettlement.request.SearchDriverReconciliationListRequestModel;
import com.logistics.appapi.client.vehiclesettlement.request.VehicleSettlementIdRequestModel;
import com.logistics.appapi.client.vehiclesettlement.response.*;
import com.logistics.appapi.controller.vehiclesettlement.mapping.*;
import com.logistics.appapi.controller.vehiclesettlement.request.DriverReconciliationConfirmRequestDto;
import com.logistics.appapi.controller.vehiclesettlement.request.SearchDriverReconciliationListRequestDto;
import com.logistics.appapi.controller.vehiclesettlement.request.VehicleSettlementIdRequestDto;
import com.logistics.appapi.controller.vehiclesettlement.response.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/2/22 10:27
 */
@Api(value = "对账管理")
@RestController
@RequestMapping(value = "/api/vehicleSettlement")
public class VehicleSettlementController {

    @Resource
    private VehicleSettlementClient vehicleSettlementClient;
    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 司机账单列表
     * @param requestDto
     * @return
     */
    @ApiOperation(("司机账单列表v1.0.0"))
    @PostMapping(value = "/searchDriverReconciliationList")
    public Result<PageInfo<SearchDriverReconciliationListResponseDto>> searchDriverReconciliationList(@RequestBody SearchDriverReconciliationListRequestDto requestDto) {
        Result<PageInfo<SearchDriverReconciliationListResponseModel>> result = vehicleSettlementClient.searchDriverReconciliationList(MapperUtils.mapper(requestDto, SearchDriverReconciliationListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        List<SearchDriverReconciliationListResponseDto> list = MapperUtils.mapper(pageInfo.getList(), SearchDriverReconciliationListResponseDto.class, new SearchDriverReconciliationListMapping());
        pageInfo.setList(list);
        return Result.success(pageInfo);
    }

    /**
     * 司机账单列表数量统计
     * @param requestDto
     * @return
     */
    @ApiOperation(("司机账单列表数量统计v1.0.0"))
    @PostMapping(value = "/driverReconciliationListCount")
    public Result<DriverReconciliationListCountResponseDto> driverReconciliationListCount(@RequestBody SearchDriverReconciliationListRequestDto requestDto) {
        Result<DriverReconciliationListCountResponseModel> result = vehicleSettlementClient.driverReconciliationListCount(MapperUtils.mapper(requestDto, SearchDriverReconciliationListRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), DriverReconciliationListCountResponseDto.class));
    }

    /**
     * 司机账单详情
     * @param requestDto
     * @return
     */
    @ApiOperation(("司机账单详情v1.0.0"))
    @PostMapping(value = "/driverReconciliationDetail")
    public Result<DriverReconciliationDetailResponseDto> driverReconciliationDetail(@RequestBody @Valid VehicleSettlementIdRequestDto requestDto) {
        Result<DriverReconciliationDetailResponseModel> result = vehicleSettlementClient.driverReconciliationDetail(MapperUtils.mapper(requestDto, VehicleSettlementIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), DriverReconciliationDetailResponseDto.class,new DriverReconciliationDetailMapping()));
    }

    /**
     * 司机确认账单
     * @param requestDto
     * @return
     */
    @ApiOperation(("司机确认账单v1.0.0"))
    @PostMapping(value = "/driverReconciliationConfirm")
    @IdempotentLimited(timeout = 3, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result driverReconciliationConfirm(@RequestBody @Valid DriverReconciliationConfirmRequestDto requestDto) {
        if (CommonConstant.ONE.equals(requestDto.getConfirmReconciliationType())){
            if (StringUtils.isBlank(requestDto.getCommitImageUrl())){
                throw new BizException(AppApiExceptionEnum.DRIVER_RECONCILIATION_CONFIRM_IMAGE_URL_EMPTY);
            }
        }else if (CommonConstant.ZERO.equals(requestDto.getConfirmReconciliationType())){
            if (StringUtils.isBlank(requestDto.getSettlementReasonRemark()) || requestDto.getSettlementReasonRemark().length() > CommonConstant.INT_FIFTY){
                throw new BizException(AppApiExceptionEnum.DRIVER_RECONCILIATION_CONFIRM_REMARK_EMPTY);
            }
        }else{
            throw new BizException(AppApiExceptionEnum.DRIVER_RECONCILIATION_IF_CONFIRM_EMPTY);
        }
        return vehicleSettlementClient.driverReconciliationConfirm(MapperUtils.mapper(requestDto, DriverReconciliationConfirmRequestModel.class));
    }

    /**
     * 司机确认详情
     * @param requestDto
     * @return
     */
    @ApiOperation(("司机确认详情v1.0.0"))
    @PostMapping(value = "/driverReconciliationConfirmDetail")
    public Result<DriverReconciliationConfirmDetailResponseDto> driverReconciliationConfirmDetail(@RequestBody @Valid VehicleSettlementIdRequestDto requestDto) {
        Result<DriverReconciliationConfirmDetailResponseModel> result = vehicleSettlementClient.driverReconciliationConfirmDetail(MapperUtils.mapper(requestDto, VehicleSettlementIdRequestModel.class));
        result.throwException();
        result.getData().setCommitImageUrl(commonBiz.getImageURL(result.getData().getCommitImageUrl()));
        return Result.success(MapperUtils.mapper(result.getData(), DriverReconciliationConfirmDetailResponseDto.class, new DriverReconciliationConfirmDetailMapping(configKeyConstant.fileAccessAddress)));
    }

    /**
     * 司机账单运单列表
     * @param requestDto
     * @return
     */
    @ApiOperation(("司机账单运单列表v1.0.0"))
    @PostMapping(value = "/driverReconciliationCarrierOrder")
    public Result<ReconciliationCarrierOrderDetailResponseDto> driverReconciliationCarrierOrder(@RequestBody @Valid VehicleSettlementIdRequestDto requestDto) {
        Result<ReconciliationCarrierOrderDetailResponseModel> result = vehicleSettlementClient.driverReconciliationCarrierOrder(MapperUtils.mapper(requestDto, VehicleSettlementIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ReconciliationCarrierOrderDetailResponseDto.class, new DriverReconciliationCarrierOrderMapping()));
    }

    /**
     * 司机账单收款记录
     * @param requestDto
     * @return
     */
    @ApiOperation(("司机账单收款记录v1.0.0"))
    @PostMapping(value = "/driverReconciliationBillingRecords")
    public Result<List<ReconciliationBillingRecordsResponseDto>> driverReconciliationBillingRecords(@RequestBody @Valid VehicleSettlementIdRequestDto requestDto) {
        Result<List<ReconciliationBillingRecordsResponseModel>> result = vehicleSettlementClient.driverReconciliationBillingRecords(MapperUtils.mapper(requestDto, VehicleSettlementIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ReconciliationBillingRecordsResponseDto.class, new DriverReconciliationBillingRecordsMapping()));
    }

    /**
     * 司机账单轮胎费用列表
     * @param requestDto
     * @return
     */
    @ApiOperation(("司机账单轮胎费用列表v1.0.0"))
    @PostMapping(value = "/driverReconciliationTire")
    public Result<ReconciliationTireDetailResponseDto> driverReconciliationTire(@RequestBody @Valid VehicleSettlementIdRequestDto requestDto) {
        Result<ReconciliationTireDetailResponseModel> result = vehicleSettlementClient.driverReconciliationTire(MapperUtils.mapper(requestDto, VehicleSettlementIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ReconciliationTireDetailResponseDto.class, new DriverReconciliationTireMapping()));
    }

    /**
     * 司机账单充油费用列表
     * @param requestDto
     * @return
     */
    @ApiOperation(("司机账单充油费用列表v1.0.0"))
    @PostMapping(value = "/driverReconciliationOilFilled")
    public Result<ReconciliationOilFilledDetailResponseDto> driverReconciliationOilFilled(@RequestBody @Valid VehicleSettlementIdRequestDto requestDto) {
        Result<ReconciliationOilFilledDetailResponseModel> result = vehicleSettlementClient.driverReconciliationOilFilled(MapperUtils.mapper(requestDto, VehicleSettlementIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), ReconciliationOilFilledDetailResponseDto.class, new DriverReconciliationOilFilledMapping()));
    }
}
