package com.logistics.management.webapi.controller.drivercostapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/8/3 9:28
 */
@Data
public class DriverCostApplyInvoiceResponseDto {

    @ApiModelProperty(value = "发票名称")
    private String invoiceName = "";

    @ApiModelProperty(value = "票据类型")
    private String invoiceType = "";

    @ApiModelProperty(value = "票据类型")
    private String invoiceTypeLabel = "";

    @ApiModelProperty(value = "发票代码")
    private String invoiceCode = "";

    @ApiModelProperty(value = "发票号码")
    private String invoiceNum = "";

    @ApiModelProperty(value = "合计金额")
    private String totalPrice = "";

    @ApiModelProperty(value = "合计税额")
    private String totalTax = "";

    @ApiModelProperty(value = "价税合计")
    private String totalTaxAndPrice = "";

    @ApiModelProperty(value = "发票图片路径")
    private String invoicePicPath = "";
}
