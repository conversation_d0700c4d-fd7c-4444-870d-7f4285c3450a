package com.logistics.tms.base.enums;

public enum CarrierPriceTypeEnum {

    DEFAULT(0, ""),
    UNIT_PRICE(1, "单价"),
    FIXED_PRICE(2, "一口价"),;

    private Integer key;
    private String value;

    CarrierPriceTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CarrierPriceTypeEnum getEnum(Integer key) {
        for (CarrierPriceTypeEnum t : values()) {
            if (t.getKey() .equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
