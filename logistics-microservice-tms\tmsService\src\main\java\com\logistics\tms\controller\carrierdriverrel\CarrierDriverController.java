package com.logistics.tms.controller.carrierdriverrel;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.biz.carrierdriverrel.CarrierDriverBiz;
import com.logistics.tms.controller.carrierdriverrel.request.AddOrModifyDriverRequestModel;
import com.logistics.tms.controller.carrierdriverrel.request.DriveDetailRequestModel;
import com.logistics.tms.controller.carrierdriverrel.request.SearchCarrierDriverListRequestModel;
import com.logistics.tms.controller.carrierdriverrel.response.DriveDetailResponseModel;
import com.logistics.tms.controller.carrierdriverrel.response.SearchCarrierDriverListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(value = "API-CarrierDriverApi-司机与车主关联关系")
@RestController
@RequestMapping("/service/carrierDriver")
public class CarrierDriverController {

    @Autowired
    private CarrierDriverBiz carrierDriverBiz;


    /**
     * 司机列表(前台)
     * @param requestModel 筛选条件
     * @return 司机列表
     */
    @ApiOperation(value = "司机列表(前台)")
    @PostMapping(value = "/searchCarrierDriverList")
    public Result<PageInfo<SearchCarrierDriverListResponseModel>> searchCarrierDriverList(@RequestBody SearchCarrierDriverListRequestModel requestModel) {
        List<SearchCarrierDriverListResponseModel> responseModels = carrierDriverBiz.searchCarrierDriverList(requestModel);
        return Result.success(new PageInfo(responseModels));
    }

    /**
     * 前台车主获取司机详情
     * @param requestModel 司机ID
     * @return 司机详情
     */
    @ApiOperation(value = "查询司机详情(前台)")
    @PostMapping(value = "/getDetail")
    public Result<DriveDetailResponseModel> getDetail(@RequestBody DriveDetailRequestModel requestModel) {
        return Result.success(carrierDriverBiz.getDetail(requestModel));
    }

    /**
     * 前台车主新增/编辑司机
     * @param requestModel 司机信息
     * @return 操作结果
     */
    @ApiOperation(value = "新增/编辑司机(前台)")
    @PostMapping(value = "/addOrModifyStaff")
    public Result<Boolean> addOrModifyStaff(@RequestBody AddOrModifyDriverRequestModel requestModel) {
        carrierDriverBiz.addOrModifyStaff(requestModel);
        return Result.success(true);
    }
}
