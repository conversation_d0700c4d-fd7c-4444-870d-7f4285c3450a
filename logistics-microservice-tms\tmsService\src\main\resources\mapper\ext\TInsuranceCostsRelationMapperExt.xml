<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TInsuranceCostsRelationMapper" >
    <select id="getByInsuranceCostsId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_insurance_costs_relation
        where valid=1
        and insurance_costs_id = #{insuranceCostsId,jdbcType=BIGINT}
    </select>
    <insert id="batchInsert">
        <foreach collection="list" separator=";" item="item">
            insert into t_insurance_costs_relation
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.insuranceCostsId != null">
                    insurance_costs_id,
                </if>
                <if test="item.insuranceId != null">
                    insurance_id,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.insuranceCostsId != null">
                    #{item.insuranceCostsId,jdbcType=BIGINT},
                </if>
                <if test="item.insuranceId != null">
                    #{item.insuranceId,jdbcType=BIGINT},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>
    <select id="getByInsuranceId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_insurance_costs_relation
        where valid=1
        and insurance_id = #{insuranceId,jdbcType=BIGINT}
    </select>
    <update id="batchUpdateValidByInsuranceCostsIds">
        update t_insurance_costs_relation
        set valid=0
        where insurance_costs_id in (${insuranceCostsIds})
    </update>

    <select id="getWaitSettlementByInsuranceIds" resultMap="BaseResultMap">
        select
        ticr.*
        from t_insurance_costs_relation ticr
        left join t_insurance_costs tic on tic.id = ticr.insurance_costs_id and tic.valid = 1
        where ticr.valid=1
        and ticr.insurance_id in (${insuranceIds})
        and tic.status = 0
    </select>
    <select id="getByInsuranceCostsIds" resultType="java.lang.Long">
        select insurance_id
        from t_insurance_costs_relation
        where valid=1
        and insurance_costs_id in (${insuranceCostIds})
    </select>
</mapper>