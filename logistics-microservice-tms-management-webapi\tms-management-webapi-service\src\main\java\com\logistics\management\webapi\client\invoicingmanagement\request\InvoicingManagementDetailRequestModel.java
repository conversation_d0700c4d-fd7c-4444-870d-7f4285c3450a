package com.logistics.management.webapi.client.invoicingmanagement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/3/19 16:47
 */
@Data
public class InvoicingManagementDetailRequestModel {

    @ApiModelProperty(value = "发票管理id")
    private Long invoicingId;

    @ApiModelProperty("业务类型：1 包装业务，2 自营业务")
    private Integer businessType;

}
