package com.logistics.tms.api.feign.loanrecord.hystrix;

import com.github.pagehelper.PageInfo;
import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.loanrecord.LoanRecordServiceApi;
import com.logistics.tms.api.feign.loanrecord.model.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: sj
 * @Date: 2019/9/30 10:05
 */
@Component
public class LoanRecordServiceApiHystrix implements LoanRecordServiceApi{
    @Override
    public Result<PageInfo<LoanRecordListResponseModel>> searchList(LoanRecordListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SummaryLoanRecordResponseModel> getSummary(LoanRecordListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> saveOrUpdate(SaveOrUpdateLoanRecordRequestModel recordRequestModel) {
        return Result.timeout();
    }

    @Override
    public Result<LoanRecordDetailResponseModel> getDetail(LoanRecordDetailRequestModel requestModel) {
        return Result.timeout();
    }


    @Override
    public Result<List<LoanOperationRecordResponseModel>> getOperationRecords(LoanOperationRecordRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<LoanSettlementRecordResponseModel>> getSettlementRecords(LoanSettlementRecordRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<LoanRecordListResponseModel>> exportLoanRecords(LoanRecordListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ExportSettlementRecordResponseModel> exportSettlementRecords(LoanSettlementRecordRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<ExportOperationRecordResponseModel> exportOperationRecords(LoanOperationRecordRequestModel requestModel) {
        return Result.timeout();
    }
}
