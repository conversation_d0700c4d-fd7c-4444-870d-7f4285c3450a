package com.logistics.management.webapi.client.settlestatement.tradition.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/11/9
 */
@Data
public class TraditionStatementWaitArchiveListRequestModel {

	@ApiModelProperty(value = "对账单id")
	private Long settleStatementId;

	@ApiModelProperty(value = "运单号(支持模糊搜索)")
	private String carrierOrderCode;
}
