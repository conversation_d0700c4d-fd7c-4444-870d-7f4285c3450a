package com.logistics.tms.base.enums;

import com.yelo.tray.core.base.enums.BaseExceptionEnum;

/**
 * <AUTHOR>
 * @createDate 2018-08-13 20:36
 */
public enum EntrustDataExceptionEnum implements BaseExceptionEnum {

    DEMANDORDER_IS_EMPTY(20001,"需求单不存在"),
    DEMANDORDER_IS_CANCEL(20002,"需求单已取消，不能再次取消"),
    DEMANDORDER_NOT_CANCEL(20003,"需求单已安排车辆去提货，如要取消需求单请联系调度取消运单"),
    DEMAND_ORDER_IS_EXIST(20004,"需求单已存在"),

    DEMANDORDER_COMPLETE_DISPATCH_FAILURE(20005,"您选中的需求单%1$s %2$s件货物未安排车辆，不允许提交完结"),
    DEMANDORDER_COMPLETE_DISPATCH_FAILURE_ERROR(20006,"您选中的需求单已经提交完结"),
    DEMAND_ORDER_IS_COMPLETE(20007,"需求单已完成，不允许提交完结"),
    CONFIRM_PUBLISH_REMARK(20008,"备注不能为空"),
    COMPANY_CARRIER_DIFFERENT(20009,"车主不同不允许批量调度车辆或完成调度"),
    COMPANY_CARRIER_PUBLISH_STATUS_ERROR(20010,"只有待发布状态才能进行此操作"),
    DEMAND_ORDER_CANCEL(20011,"需求单已取消，不能进行调度操作"),
    DEMAND_ORDER_NOT_DISPATCH(20012,"需求单状态错误，不能进行调度操作"),
    DEMAND_ORDER_IS_CANCEL(20013,"需求单已取消，不允许提交完结"),
    DEMAND_ORDER_IS_WAIT_DISPATCH(20014,"需求单状态错误，不允许提交完结"),
    DEMAND_ORDER_IS_WAIT_PUBLISH(20015,"需求单待发布，不允许提交完结"),
    DEMAND_ORDER_BIDDING_NOT_OPERATION(20016,"需求单竞价中，不能进行该操作"),
    ENTRUST_CONTACT_EXIST(20022,"货主账号已存在"),
    COMPANY_NAME_EXIST(20023,"公司名字已存在"),
    MOBILE_EXIST(20024,"手机号码已存在"),
    COMPANY_ENTRUST_EMPTY(20025,"货主公司不存在"),
    ENTRUST_CONTACT_EMPTY(20026,"联系人信息不存在"),
    ENTRUST_ACCOUNT_PHONE_OTHER_COMPANY(20027,"该货主账号已经挂靠在其他公司，不允许重复挂靠"),
    ENTRUST_ACCOUNT_PHONE_EXIST_THIS_COMPANY(20028,"该货主账号已经挂靠在本公司，不允许重复挂靠"),
    COMPANY_ENTRUST_PERMISSIONS_EMPTY(20029,"权限不能为空"),
    ENTRUST_CONTACT_NOT_EXIST(20030,"货主账号不存在"),
    CONTACT_ROLE(20031,"该账号为公司联系人，不能变更公司"),

    FREIGHT_RULE_PERIODS_NULL(20032,"规则阶梯不能为空"),
    FREIGHT_RULE_PERIODS_FIRST_START_WITH_ZERO(20033,"规则阶梯首重必须已'0<吨数'开始"),
    FREIGHT_RULE_CALC_RULE_ERROR(20034,"计价规则只能按吨"),
    REQUEST_PARAM_ERROR(20035,"请求参数错误"),
    FREIGHT_RULE_ONLY_ONE_BASE_PRICE(20036,"只允许设置一个基价"),
    FREIGHT_RULE_MUST_ONE_BASE_PRICE(20037,"请务必设置一个基价"),
    FREIGHT_RULE_EXIST(20038,"该公司下运价规则已存在"),
    FREIGHT_RULE_NOT_EXIST(20039,"该运价规则不存在"),
    FREIGHT_RULE_ALREADY_ENABLED(20040,"该运费规则已经启用，无法再次启用"),
    FREIGHT_RULE_BASE_PRICE_NEGATIVE(20041,"修改基价出现负值，请检查"),
    FREIGHT_RULE_BASE_PRICE_EXIST(20042,"您要添加或修改的基价已存在"),
    ENTRUST_ADDRESS_EMPTY(20043,"地址信息不存在"),
    ENTRUST_ADDRESS_COMPANY_NAME_EMPTY(20044,"公司不能为空"),
    CONTACT_PHONE_EMPTY(20045,"请填写正确的联系方式"),
    ENTRUST_ADDRESS_EXIST(20046,"地址信息已存在"),
    DEMAND_ORDER_STATUS_ERROR(20047,"需求单不是待签收状态，不能进行签收操作"),
    FREIGHT_NEGATIVE(20048,"该线路下的运价为负数或0，请检查规则设置"),
    FREIGHT_RULE_ALREADY_DISABLED(20049,"该运费规则已经禁用，无法再次禁用"),
    DEMANDORDER_CANCEL(20050,"需求单已取消，不能进行该操作"),
    GOODS_UNIT_DIFFERENT(20051,"货物单位不同不允许批量调度车辆或完成调度"),
    COMPANY_ENTRUST_DISABLED(20052,"货主公司被禁用，不能进行该操作"),
    MAINTAIN_RIGHT_PERIODS(20053,"请维护正确的阶梯数据"),
    MAINTAIN_ADJUST_RULE_OR_UNIT(20054,"请维护调整规则和调整比例单位"),
    MAINTAIN_BASE_PRICE(20055,"请选择基价设置"),
    REMARK_MAX(20056,"备注不能超过300字"),
    NETWORK_CONNECTION_FAILURE(20057,"网络连接失败"),
    RECEIVER_ADDRESS_EMPTY(20058,"未找到正确的收货地址"),
    COMPANY_CARRIER_EMPTY(20059,"车主公司不存在"),
    CONFIGURATION_ERROR(20060,"请配置相关参数"),
    CUSTOMER_ORDER_CODE_EXIST(20061,"客户单号已存在"),
    GET_LOCK_ERROR(20062, "获取锁失败！"),
    SETTLEMENT_INFO_EMPTY(20064,"结算信息不存在"),
    PAYMENT(20065,"已收款的请勿重复操作收款动作"),
    NOT_PAYMENT(20066,"未收款的请勿重复操作收款动作"),
    ENTRUST_CONTACT_DISABLED(20067,"该货主账号被禁用"),
    LEYI_DEMAND_ORDER(20068,"乐医委托单，不能进行该操作"),
    SETTLEMENT_IDS_NULL(20069,"结算单ID不能为空"),
    CARRIER_SETTLEMENT_HAS_BEEN_PAYED(20070,"已付款的请勿重复操作付款动作"),
    CARRIER_SETTLEMENT_HAS_NOT_BEEN_PAYED(20071,"未付款的请勿操作回退动作"),
    ENTRUST_PAYMENT_NOT_MODIFY_COST(20072,"已收款的数据不允许修改运费"),
    CARRIER_PAYMENT_NOT_MODIFY_COST(20073,"该单据车主已付款，不允许修改费用"),
    ENTRUST_PRICE_TYPE_EMPTY(20074,"请勿选择报价类型为空的数据进行收款操作"),
    CARRIER_SETTLEMENT_PRICE_TYPE_NULL(20075,"请勿选择报价类型为空的数据进行付款操作"),
    DEMAND_ORDER_GOODS_EMPTY(20076,"请填写委托货物"),
    DEMAND_ORDER_GOODS_UNIT_DIFFERENT(20077,"委托货物请选择正确且相同的单位"),
    COMPANY_ENTRUST_NAME_TOO_LONG(20078,"公司简称长度不能超过50"),
    CUSTOMER_COMPANY_EMPTY(20079,"客户不存在"),
    ENTRUST_CARRIER_RELATION_EMPTY(20080,"货商关系不存在"),
    CHOOSE_COMPANY_CARRIER(20081,"请选择车主"),
    ENTRUST_CARRIER_RELATION_REPEAT(20082,"该货主已存在关联关系，请勿重复设置"),
    NOT_EXECUTING_NOT_ENDING(20083, "只有执行中的合同才能终止"),
    NOT_WAIT_EXECUTE_NOT_CANCEL(20084, "只有待执行的合同才能作废"),
    CONTRACT_NOT_EXIST(20085, "合同信息不存在"),
    CONTRACT_NOT_MODIFY(20086, "合同信息不允许修改"),
    CONTRACT_HAS_EXIST(20087, "合同信息已经存在"),
    CONTRACT_FILE_EMPTY(20088, "请上传合同附件"),
    CONTRACT_FILE_COUNT_MAX(20089,"最多上传50个附件"),
    ENTRUST_CARRIER_COMPANY_EMPTY(20090,"请及时联系客服为您关联指定承运商"),
    REGISTER_ENTRUST_ROLE_EXIST(20091,"您已注册货主，请勿重复注册"),
    SHIPPER_TYPE_NOT_EXIST(20092,"货主类型为空"),
    COMPANY_ENTRUST_IDENTITYCARD_INVALID_NUMBER(20093,"请填写有效身份证号"),
    COMPANY_ENTRUST_PERSONAL_EXIST(20094,"货主资质为个人的已存在"),
    CONTACT_PHONE_HAS_CHANGE(20095,"联系方式不一致"),
    COMPANY_ENTRUST_ASSOCIATED(20096,"该公司您已关联，请勿重复操作"),
    COMPANY_ENTRUST_HAS_REGISTER(20097,"该公司已被其他人注册，请直接联系客服为您建立关联关系"),
    PERSONAL_QUALIFICATIONS_NOT_EXIST(20098,"货主个人资质未认证"),
    COMPANY_ENTRUST_IS_WAIT_AUDIT(20099,"货主信息待审核，不允许操作"),
    COMPANY_ENTRUST_HAS_AUDITED(20100,"货主信息已审核，不允许操作"),
    COMPANY_ENTRUST_HAS_REJECT(20101,"货主信息已驳回，不允许操作"),
    COMPANY_ENTRUST_TO_BE_SUBMITTED(20102,"货主信息待提交，不允许操作"),
    COMPANY_ENTRUST_REJECT_MESSAGE_EMPTY(20103,"驳回信息不能为空"),
    COMPANY_ENTRUST_IF_AUDIT_MESSAGE_EMPTY(20104,"车辆是否审核信息为空"),
    COMPANY_ENTRUST_ORDER_SET_MESSAGE_EMPTY(20105,"接单设置信息为空"),
    OPERATION_TYPE_EMPTY(20106,"操作类型不能为空"),
    ORDER_MODE_NOT_EXIST(20107,"接单模式不为空"),
    REPUBLISH_FAILED(20108,"重新发布失败"),
    DEMAND_ORDER_STATUS_CHANGE(20110,"该需求单状态发生变更，请刷新重试"),
    ORDER_MODE_EMPTY(20111,"请选择接单模式"),
    ONLY_WAIT_DISPATCH_DEMAND_ORDER_CAN_OPERATE(20013,"“待调度”的数据才允许操作修改车主"),
    CARRIER_PRICE_LIMIT(20014,"价格必须大于0 ，小于50000元"),
    CARRIER_NOT_CHANGE(20015,"车主未作修改"),
    COMPANY_ENTRUST_NOT_PASSED_AUDIT(20016,"该企业还未审核通过，无法建立关联关系"),
    COMPANY_ENTRUST_EXISTING_PERSONAL_CONTACT_ACCOUNT(20017,"该联系方式已存在个人类型的账号，请勿重复添加"),
    COMPANY_CARRIER_NOT_AUDIT(20118,"该车主资质未审核通过，无法指定车主发单，请审核之后再操作"),
    COMPANY_ENTRUST_PERSONAL_NOT_EXIST(20119,"货主个人资质不存在,无法建立关联关系"),
    COMPANY_ENTRUST_FOR_ENTERPRISE_NOT_PASSED_AUDIT(20120,"货主公司还未审核通过，不允许发单，请及时联系客服审核"),
    COMPANY_ENTRUST_FOR_PERSONAL_NOT_PASSED_AUDIT(20121,"您的资质还未审核通过，不允许发单，请及时联系客服审核"),
    DEMAND_ORDER_ID_EMPTY(20122,"需求单ID不能为空"),
    DEMAND_ORDER_APPROVAL_STATUS_ERROR(20124,"该数据审核状态已变更，不能进行该操作"),
    CARRIER_PRICE_EMPTY(20125,"请维护车主费用"),
    COMPANY_ENTRUST_NOT_AUDIT(20126,"该货主资质未审核通过，无法发布需求单，请审核之后再操作"),
    USER_ACCOUNT_EMPTY(20127,"用户不存在"),
    DEMAND_ORDER_IS_WAIT_AUDIT(20128,"该需求单修改后还未审核，请审核之后再操作"),
    CARRIER_ORDER_UNLOAD_GOODS(20129,"下的运单已卸货，不能进行该操作"),
    DEMAND_ORDER_PUBLISHED(20130,"已发布，不能进行该操作"),
    LEYI_NOT_AUTO_SIGN(20131,"乐医不允许选择默认签收"),
    DEMAND_ORDER_NOT_BELONG_YOU(20132,"无法操作不属于自己的需求单"),
    DEMAND_ORDER_NOT_BELONG_TO_SAME_CARRIER(20133,"不同的车主无法同时调度车辆"),
    WAREHOUSE_EXIST(20134,"该配置已存在，请勿重复添加"),
    WAREHOUSE_NAME_EMPTY(20135,"仓库名不能为空"),
    WAREHOUSE_ADDRESS_EMPTY(20136,"请选择省市区"),

    FREIGHT_RULE_PERIODS_PRICE_ERROR(20138,"请维护正确的阶梯价格"),
    FREIGHT_RULE_PERIODS_SAME_AS_PREVIOUS_PRICE(20139,"请注意您的阶梯之间数字必须连贯哟!"),
    FREIGHT_RULE_PERIODS_MAX(20140," 一个公司最多10条基价阶梯"),
    FREIGHT_RULE_PERIODS_MARKUP_MAX(20141," 一个公司最多30条多装多卸价格"),
    FREIGHT_RULE_PERIODS_MARKUP_REPEAT(20142,"多装多卸价重复"),
    PRICE_ADJUSTMENT_ERROR(20143,"费用调整价格出现负值,请重新调整！"),
    TRAY_COST_EMPTY(20144,"托盘费用不存在"),
    TRAY_COST_MODIFY_COUNT_MAX(20145,"您当前月修改费用次数已经用完，请下次修改！"),
    DATA_SYNCHRONIZATION_NETWORK_FREIGHT_NOT_ALLOWED_OPERATION(20146,"数据已同步网络货运，不允许操作"),
    NOT_LEYI_DEMAND_ORDER(20147,"非乐医委托单，无法进行同步操作"),
    SYNC_DEMANDORDER_MAX(20148,"最多同步20条数据"),
    ADDRESS_EMPTY(20149,"地址信息不存在"),
    PUBLISH_DEMAND_ORDER_MAX(20152,"最多发布10条数据"),
    ENTRUST_UNLOAD_ADDRESS_BLANK(20153,"需求单卸货地址不能为空"),
    PUBLISH_DEMAND_ORDER_GOODS_UNIT_ERROR(20154,"仅限相同单位的需求单一起发布"),
    DEMAND_ORDER_COMPLETE_DISPATCH_FOR_LEYI_REQUEST_PARAM_ERROR(20155,"请维护原因和描述，描述1-100字"),
    DEMAND_ORDER_EMPTY_STATUS_ERROR(20156, "需求单不符合放空状态"),
    DEMAND_ORDER_IS_EMPTY(20157, "需求单已放空，不能进行该操作"),
    DEMAND_ORDER_IS_OBJECTION (20158, "需求单已标记异常，不能进行该操作"),
    DEMAND_ORDER_ONLY_ONE(20159, "只允许操作一条记录"),
    DEMAND_ORDER_NOT_PUBLISH(20160, "所选需求单不符合发布条件"),
    PUBLISH_DEMAND_ORDER_ENTRUST_ERROR(20161,"仅限相同货主的需求单一起发布"),
    OPERATION_DEMAND_ORDER_ONLY_SINOPEC(20162,"只允许操作中石化推送的需求单"),
    DEMAND_ORDER_OBJECTION_INFO_EMPTY (20163, "需求单异常信息不存在"),
    DEMAND_ORDER_IS_OBJECTION_SINOPEC(20164, "需求单存在异常，不能进行该操作"),
    THE_CURRENT_STATE_CANNOT_REPORT_EXCEPTIONS(20165, "当前状态无法上报异常"),
    QUOTATION_CANNOT_BE_SELECTED_FOR_THE_DEMAND_LIST_TO_BE_SCHEDULED(20166,"待调度需求单不能选择已报价"),
    SINOPEC_CANCEL_DEMAND_ORDER_IS_OBJECTION(20167, "当前需求单异常，不能取消"),
    SINOPEC_DEMANDORDER_EXIST(20168, "委托单已存在"),
    SINOPEC_DEMANDORDER_NOT_EXIST(20169, "委托单不存在"),
    ONLY_THE_SAME_OWNER_CAN_MERGE_AND_DISPATCH_VEHICLES(20170, "同一车主才能合并调度车辆"),
    DISPATCH_FEE_TYPE_ERROR(20171, "请选择司机运费类型"),
    DEMAND_ORDER_HAVE_EMPTY_ORDER(20172, "需求单下有放空运单"),
    QIYA_COMPANY_EMPTY(20173, "我司公司信息不存在"),
    PLEASE_ENTER_A_SUITABLE_PRICE_RANGE(20174, "价格必须在0.01~1000000之间"),
    PLEASE_ENTER_SUITABLE_UNIT_PRICE_RANGE(20175, "价格必须在0.01~50000之间"),
    THE_DEMAND_IN_AN_EXCEPTION(20176, "您要操作的需求单正处于异常中，请先去处理异常，再操作"),
    PLEASE_RESELECT_YOUR_CARRIER_OWNER(20177, "请重新选择车主"),
    MODIFY_CARRIER_ERROR(20178, "H单存在被放空数据，不可修改车主"),
    FREIGHT_ALREADY_DISABLED(20179, "该运价已被禁用，无法再次禁用"),
    FREIGHT_ALREADY_ENABLED(20180, "该运价已被启用，无法再次启用"),
    LIFE_DEMAND_ORDER_CANT_COMPLETE_DISPATCH(20181, "误差太大，不能完成调度"),
    SINOPEC_DEMANDORDER_LIST_NOT_EXIST(20182, "行项目信息不存在"),
    SINOPEC_DEMANDORDER_MULTIPLE_CANCEL_ERROR(20183, "网货对接运单，不允许批量撤销"),
    SINOPEC_DEMANDORDER_ASSOCIATED_NO_IS_NULL(20184, "委托单号不许为空"),
    SINOPEC_DEMANDORDER_ACTION_CODE_ERROR(20185, "撤销委托操作码错误"),
    OPERATION_DEMAND_ORDER_ONLY_SINOPEC_ONLINE(20186, "只允许操作网货需求单"),
    SINOPEC_DEMANDORDER_CHECK_ERROR(20187, "参数校验未通过: "),
    SINOPEC_DEMANDORDER_CHECK_ALL_ERROR(20188, "参数校验未通过,请检查请求参数是否正确"),
    SINOPEC_SIGN_ERROR(20189, "验签失败"),
    RECYCLE_DEMAND_ORDER_CANCEL_ERROR(20190, "回收需求单不允许当前操作，仅允许回退"),
    DEMAND_ORDER_IS_ROLLBACK(20191, "需求单已回退，不能进行该操作"),
    DEMAND_ORDER_ROLLBACK_ERROR(20192, "当前需求单不允许回退"),
    FIXED_DEMAND_ORDER_NOT_SYNC(20193, "固定需求的需求单不允许当前操作"),
    DEMAND_ORDER_EXCEPTION(20194, "需求单存在未处理的异常工单，不能进行当前操作"),
    RECYCLE_DEMAND_ORDER_NOT_OPERATION(20195, "回收需求单不允许当前操作"),
    VEHICLE_LENGTH_NOT_EXIST(20196, "车长信息不存在"),
    DEMAND_ORDER_NOT_OPERATION(20197, "需求单不支持当前操作"),
    SYSTEM_NOT_SUPPORT(20198, "系统不支持此操作"),
    ;

    @Override
    public int getCode() {
        return key;
    }

    @Override
    public String getMsg() {
        return value;
    }

    private Integer key;
    private String value;

    EntrustDataExceptionEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

}
