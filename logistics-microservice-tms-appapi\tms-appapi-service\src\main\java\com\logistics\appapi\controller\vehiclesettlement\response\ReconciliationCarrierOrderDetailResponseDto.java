package com.logistics.appapi.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author：wjf
 * @date：2021/4/9 15:52
 */
@Data
public class ReconciliationCarrierOrderDetailResponseDto {
    @ApiModelProperty("账单月份")
    private String settlementMonth="";
    @ApiModelProperty("运费合计")
    private String carrierFreight="";
    @ApiModelProperty("调整费用")
    private String adjustFee="";
    @ApiModelProperty("运单列表")
    private List<ReconciliationCarrierOrderListResponseDto> carrierOrderList;
}
