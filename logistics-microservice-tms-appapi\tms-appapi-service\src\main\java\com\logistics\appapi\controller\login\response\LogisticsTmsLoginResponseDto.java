package com.logistics.appapi.controller.login.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2019/10/22 15:27
 */
@Data
public class LogisticsTmsLoginResponseDto {
    private String token = "";

    @ApiModelProperty("用户名")
    private String userName = "";

    @ApiModelProperty("用户账号")
    private String userAccount = "";

    @ApiModelProperty("人员机构：1 自主，2 外部，3 自营")
    private String staffProperty = "";

    @ApiModelProperty("实名认证(个人): 0 待实名 1 实名中 2 已实名")
    private String realNameAuthenticationStatus = "";

    @ApiModelProperty("是否可跳转云仓小程序: 0 不可跳转 1 可跳转")
    private String warehouseSwitch = "";
}
