package com.logistics.management.webapi.api.impl.extvehiclesettlement.mapping;

import com.logistics.management.webapi.api.feign.extvehiclesettlement.dto.ExtVehicleSettlementDetailResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.tms.api.feign.carrierorder.model.SearchCarrierOrderListGoodsInfoModel;
import com.logistics.tms.api.feign.extvehiclesettlement.model.ExtVehicleSettlementDetailResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2019/11/21 14:33
 */
public class ExtVehicleSettlementDetailMapping extends MapperMapping<ExtVehicleSettlementDetailResponseModel,ExtVehicleSettlementDetailResponseDto> {

    private String imagePrefix;
    private Map<String, String> imageMap;
    public ExtVehicleSettlementDetailMapping(String imagePrefix , Map<String, String> imageMap) {
        this.imagePrefix = imagePrefix;
        this.imageMap=imageMap;
    }

    @Override
    public void configure() {
        ExtVehicleSettlementDetailResponseModel source = getSource();
        ExtVehicleSettlementDetailResponseDto destination = getDestination();
        if (source != null){
            //司机费用
            BigDecimal driverTotalFee = source.getDriverTotalFee();
            if (source.getDriverOtherFee() != null){
                driverTotalFee = driverTotalFee.add(source.getDriverOtherFee());
            }
            destination.setDriverTotalFee(ConverterUtils.toString(driverTotalFee.setScale(2, RoundingMode.HALF_UP)));

            //体积计算
            BigDecimal unloadCapacity = CommonConstant.BIG_DECIMAL_ZERO;
            if (GoodsUnitEnum.BY_VOLUME.getKey().equals(source.getSettlementUnit())){
                BigDecimal unloadCapacityTotal = CommonConstant.BIG_DECIMAL_ZERO;
                for(SearchCarrierOrderListGoodsInfoModel tmp: source.getGoodsInfoList()){
                    BigDecimal capacity = ConverterUtils.toBigDecimal(tmp.getLength()).multiply(ConverterUtils.toBigDecimal(tmp.getWidth())).multiply(ConverterUtils.toBigDecimal(tmp.getHeight()));
                    if (CommonConstant.INTEGER_ONE.equals(source.getIfEmpty())){
                        unloadCapacityTotal = unloadCapacityTotal.add(tmp.getExpectAmount().multiply(capacity).divide(new BigDecimal(1000).pow(3), 3, BigDecimal.ROUND_HALF_UP));
                    }else {
                        unloadCapacityTotal = unloadCapacityTotal.add(tmp.getUnloadAmount().multiply(capacity).divide(new BigDecimal(1000).pow(3), 3, BigDecimal.ROUND_HALF_UP));
                    }
                }
                unloadCapacity = unloadCapacity.add(unloadCapacityTotal);
            }
            //结算数量
            destination.setSettlementAmount(source.getSettlementAmount().stripTrailingZeros().toPlainString()+ GoodsUnitEnum.getEnum(source.getSettlementUnit()).getUnit());
            if(GoodsUnitEnum.BY_VOLUME.getKey().equals(source.getSettlementUnit())){
                destination.setSettlementAmount(destination.getSettlementAmount()+"("+unloadCapacity.stripTrailingZeros().toPlainString()+ CommonConstant.SQUARE+")");
            }

            //合计总费用
            if (source.getTotalFee() != null) {
                BigDecimal totalFee = source.getTotalFee();
                if (source.getDriverOtherFee() != null) {
                    totalFee = totalFee.add(source.getDriverOtherFee());
                }
                destination.setTotalFee(ConverterUtils.toString(totalFee.setScale(2, RoundingMode.HALF_UP)));
            }

            //票据拼接全路径
            if (ListUtils.isNotEmpty(source.getAttachmentList())){
                List<String> attachmentList = new ArrayList<>();
                for (String path:source.getAttachmentList()) {
                    attachmentList.add(imagePrefix + imageMap.get(path));
                }
                destination.setAttachmentList(attachmentList);
            }
        }
    }
}
