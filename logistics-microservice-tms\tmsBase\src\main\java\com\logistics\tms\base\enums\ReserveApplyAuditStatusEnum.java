package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ReserveApplyAuditStatusEnum {

    AUDIT_UNDO(-1, "已撤销"),
    AUDIT_BUSINESS(0,"待业务审核"),
    AUDIT_FINANCIAL(1, "待财务审核"),
    AUDIT_REJECT(2, "已驳回"),
    AUDIT_WAIT_PAY(3, "待打款"),
    AUDIT_PAY_DONE(4, "已打款"),
    DEFAULT(-2, ""),
    ;

    private Integer key;
    private String value;

    public static ReserveApplyAuditStatusEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }
}
