package com.logistics.management.webapi.client.drivercostapply.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: wjf
 * @date: 2024/2/21 13:24
 */
@Data
public class DriverCostApplyRecordResponseModel {
    @ApiModelProperty("操作人")
    private String operateUserName;
    @ApiModelProperty("操作时间")
    private Date operateTime;
    @ApiModelProperty("操作内容")
    private String operateContents;
    @ApiModelProperty("备注")
    private String remark;
}
