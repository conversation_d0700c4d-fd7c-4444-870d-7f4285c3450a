package com.logistics.tms.controller.carrierorder.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/9/6 9:52
 */
@Data
public class GetValidCarrierOrderRequestModel extends AbstractPageForm<GetValidCarrierOrderRequestModel> {
    @ApiModelProperty("车主id")
    private Long companyCarrierId;
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("来源：1 后台，2 前台")
    private String requestSource;
}
