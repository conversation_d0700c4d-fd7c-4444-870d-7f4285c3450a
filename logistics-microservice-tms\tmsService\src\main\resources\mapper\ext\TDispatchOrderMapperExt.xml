<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TDispatchOrderMapper" >
    <sql id="Base_Column_List_Decrypt">
    id, dispatch_order_code, carrier_order_count, vehicle_id, vehicle_no, trailer_vehicle_id, trailer_vehicle_no, driver_id,
    driver_name, driver_mobile, driver_identity, expect_arrival_time, entrust_freight_type,
    entrust_freight, dispatch_freight_fee_type, dispatch_freight_fee, load_point_amount,
    unload_point_amount, markup_fee, if_adjust, adjust_fee_type, adjust_fee, adjust_remark,
    goods_unit, source, dispatch_user_id, dispatch_user_name, dispatch_time, remark,
    company_carrier_type, company_carrier_id, company_carrier_name, carrier_contact_id,
    carrier_contact_name, AES_DECRYPT(UNHEX(carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrier_contact_phone,
    created_by, created_time, last_modified_by,
    last_modified_time, valid
    </sql>

    <select id="selectByPrimaryKeyDecrypt" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt" />
        from t_dispatch_order
        where id = #{id,jdbcType=BIGINT}
        and valid = 1
    </select>

    <insert id="insertSelectiveEncrypt" parameterType="com.logistics.tms.entity.TDispatchOrder" keyProperty="id" useGeneratedKeys="true">
        insert into t_dispatch_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="dispatchOrderCode != null">
                dispatch_order_code,
            </if>
            <if test="carrierOrderCount != null">
                carrier_order_count,
            </if>
            <if test="vehicleId != null">
                vehicle_id,
            </if>
            <if test="vehicleNo != null">
                vehicle_no,
            </if>
            <if test="trailerVehicleId != null">
                trailer_vehicle_id,
            </if>
            <if test="trailerVehicleNo != null">
                trailer_vehicle_no,
            </if>
            <if test="driverId != null">
                driver_id,
            </if>
            <if test="driverName != null">
                driver_name,
            </if>
            <if test="driverMobile != null">
                driver_mobile,
            </if>
            <if test="driverIdentity != null">
                driver_identity,
            </if>
            <if test="expectArrivalTime != null">
                expect_arrival_time,
            </if>
            <if test="entrustFreightType != null">
                entrust_freight_type,
            </if>
            <if test="entrustFreight != null">
                entrust_freight,
            </if>
            <if test="dispatchFreightFeeType != null">
                dispatch_freight_fee_type,
            </if>
            <if test="dispatchFreightFee != null">
                dispatch_freight_fee,
            </if>
            <if test="loadPointAmount != null">
                load_point_amount,
            </if>
            <if test="unloadPointAmount != null">
                unload_point_amount,
            </if>
            <if test="markupFee != null">
                markup_fee,
            </if>
            <if test="ifAdjust != null">
                if_adjust,
            </if>
            <if test="adjustFeeType != null">
                adjust_fee_type,
            </if>
            <if test="adjustFee != null">
                adjust_fee,
            </if>
            <if test="adjustRemark != null">
                adjust_remark,
            </if>
            <if test="goodsUnit != null">
                goods_unit,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="dispatchUserId != null">
                dispatch_user_id,
            </if>
            <if test="dispatchUserName != null">
                dispatch_user_name,
            </if>
            <if test="dispatchTime != null">
                dispatch_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="companyCarrierType != null">
                company_carrier_type,
            </if>
            <if test="companyCarrierId != null">
                company_carrier_id,
            </if>
            <if test="companyCarrierName != null">
                company_carrier_name,
            </if>
            <if test="carrierContactId != null">
                carrier_contact_id,
            </if>
            <if test="carrierContactName != null">
                carrier_contact_name,
            </if>
            <if test="carrierContactPhone != null">
                carrier_contact_phone,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by,
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time,
            </if>
            <if test="valid != null">
                valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="dispatchOrderCode != null">
                #{dispatchOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="carrierOrderCount != null">
                #{carrierOrderCount,jdbcType=INTEGER},
            </if>
            <if test="vehicleId != null">
                #{vehicleId,jdbcType=BIGINT},
            </if>
            <if test="vehicleNo != null">
                #{vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="trailerVehicleId != null">
                #{trailerVehicleId,jdbcType=BIGINT},
            </if>
            <if test="trailerVehicleNo != null">
                #{trailerVehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="driverId != null">
                #{driverId,jdbcType=BIGINT},
            </if>
            <if test="driverName != null">
                #{driverName,jdbcType=VARCHAR},
            </if>
            <if test="driverMobile != null">
                #{driverMobile,jdbcType=VARCHAR},
            </if>
            <if test="driverIdentity != null">
                #{driverIdentity,jdbcType=VARCHAR},
            </if>
            <if test="expectArrivalTime != null">
                #{expectArrivalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="entrustFreightType != null">
                #{entrustFreightType,jdbcType=INTEGER},
            </if>
            <if test="entrustFreight != null">
                #{entrustFreight,jdbcType=DECIMAL},
            </if>
            <if test="dispatchFreightFeeType != null">
                #{dispatchFreightFeeType,jdbcType=INTEGER},
            </if>
            <if test="dispatchFreightFee != null">
                #{dispatchFreightFee,jdbcType=DECIMAL},
            </if>
            <if test="loadPointAmount != null">
                #{loadPointAmount,jdbcType=INTEGER},
            </if>
            <if test="unloadPointAmount != null">
                #{unloadPointAmount,jdbcType=INTEGER},
            </if>
            <if test="markupFee != null">
                #{markupFee,jdbcType=DECIMAL},
            </if>
            <if test="ifAdjust != null">
                #{ifAdjust,jdbcType=INTEGER},
            </if>
            <if test="adjustFeeType != null">
                #{adjustFeeType,jdbcType=INTEGER},
            </if>
            <if test="adjustFee != null">
                #{adjustFee,jdbcType=DECIMAL},
            </if>
            <if test="adjustRemark != null">
                #{adjustRemark,jdbcType=VARCHAR},
            </if>
            <if test="goodsUnit != null">
                #{goodsUnit,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="dispatchUserId != null">
                #{dispatchUserId,jdbcType=BIGINT},
            </if>
            <if test="dispatchUserName != null">
                #{dispatchUserName,jdbcType=VARCHAR},
            </if>
            <if test="dispatchTime != null">
                #{dispatchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="companyCarrierType != null">
                #{companyCarrierType,jdbcType=INTEGER},
            </if>
            <if test="companyCarrierId != null">
                #{companyCarrierId,jdbcType=BIGINT},
            </if>
            <if test="companyCarrierName != null">
                #{companyCarrierName,jdbcType=VARCHAR},
            </if>
            <if test="carrierContactId != null">
                #{carrierContactId,jdbcType=BIGINT},
            </if>
            <if test="carrierContactName != null">
                #{carrierContactName,jdbcType=VARCHAR},
            </if>
            <if test="carrierContactPhone != null">
                HEX(AES_ENCRYPT(#{carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                #{valid,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelectiveEncrypt" parameterType="com.logistics.tms.entity.TDispatchOrder">
        update t_dispatch_order
        <set>
            <if test="dispatchOrderCode != null">
                dispatch_order_code = #{dispatchOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="carrierOrderCount != null">
                carrier_order_count = #{carrierOrderCount,jdbcType=INTEGER},
            </if>
            <if test="vehicleId != null">
                vehicle_id = #{vehicleId,jdbcType=BIGINT},
            </if>
            <if test="vehicleNo != null">
                vehicle_no = #{vehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="trailerVehicleId != null">
                trailer_vehicle_id = #{trailerVehicleId,jdbcType=BIGINT},
            </if>
            <if test="trailerVehicleNo != null">
                trailer_vehicle_no = #{trailerVehicleNo,jdbcType=VARCHAR},
            </if>
            <if test="driverId != null">
                driver_id = #{driverId,jdbcType=BIGINT},
            </if>
            <if test="driverName != null">
                driver_name = #{driverName,jdbcType=VARCHAR},
            </if>
            <if test="driverMobile != null">
                driver_mobile = #{driverMobile,jdbcType=VARCHAR},
            </if>
            <if test="driverIdentity != null">
                driver_identity = #{driverIdentity,jdbcType=VARCHAR},
            </if>
            <if test="expectArrivalTime != null">
                expect_arrival_time = #{expectArrivalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="entrustFreightType != null">
                entrust_freight_type = #{entrustFreightType,jdbcType=INTEGER},
            </if>
            <if test="entrustFreight != null">
                entrust_freight = #{entrustFreight,jdbcType=DECIMAL},
            </if>
            <if test="dispatchFreightFeeType != null">
                dispatch_freight_fee_type = #{dispatchFreightFeeType,jdbcType=INTEGER},
            </if>
            <if test="dispatchFreightFee != null">
                dispatch_freight_fee = #{dispatchFreightFee,jdbcType=DECIMAL},
            </if>
            <if test="loadPointAmount != null">
                load_point_amount = #{loadPointAmount,jdbcType=INTEGER},
            </if>
            <if test="unloadPointAmount != null">
                unload_point_amount = #{unloadPointAmount,jdbcType=INTEGER},
            </if>
            <if test="markupFee != null">
                markup_fee = #{markupFee,jdbcType=DECIMAL},
            </if>
            <if test="ifAdjust != null">
                if_adjust = #{ifAdjust,jdbcType=INTEGER},
            </if>
            <if test="adjustFeeType != null">
                adjust_fee_type = #{adjustFeeType,jdbcType=INTEGER},
            </if>
            <if test="adjustFee != null">
                adjust_fee = #{adjustFee,jdbcType=DECIMAL},
            </if>
            <if test="adjustRemark != null">
                adjust_remark = #{adjustRemark,jdbcType=VARCHAR},
            </if>
            <if test="goodsUnit != null">
                goods_unit = #{goodsUnit,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=INTEGER},
            </if>
            <if test="dispatchUserId != null">
                dispatch_user_id = #{dispatchUserId,jdbcType=BIGINT},
            </if>
            <if test="dispatchUserName != null">
                dispatch_user_name = #{dispatchUserName,jdbcType=VARCHAR},
            </if>
            <if test="dispatchTime != null">
                dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="companyCarrierType != null">
                company_carrier_type = #{companyCarrierType,jdbcType=INTEGER},
            </if>
            <if test="companyCarrierId != null">
                company_carrier_id = #{companyCarrierId,jdbcType=BIGINT},
            </if>
            <if test="companyCarrierName != null">
                company_carrier_name = #{companyCarrierName,jdbcType=VARCHAR},
            </if>
            <if test="carrierContactId != null">
                carrier_contact_id = #{carrierContactId,jdbcType=BIGINT},
            </if>
            <if test="carrierContactName != null">
                carrier_contact_phone = HEX(AES_ENCRYPT(#{carrierContactPhone,jdbcType=VARCHAR},'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
            </if>
            <if test="carrierContactPhone != null">
                carrier_contact_phone = #{carrierContactPhone,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastModifiedBy != null">
                last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
            </if>
            <if test="lastModifiedTime != null">
                last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valid != null">
                valid = #{valid,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="searchIdList" resultType="java.lang.Long">
        select DISTINCT
        tdo.id
        from t_dispatch_order tdo
        left join t_carrier_order tco on tco.dispatch_order_id = tdo.id and tco.valid = 1
        where tdo.valid = 1
        <if test="companyCarrierId != null">
            and tdo.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        </if>
        <if test="params.dispatchOrderCode != null and params.dispatchOrderCode != ''">
            and instr(tdo.dispatch_order_code,#{params.dispatchOrderCode,jdbcType=VARCHAR}) > 0
        </if>
        <if test="params.vehicleNo != null and params.vehicleNo != ''">
            and instr(tdo.vehicle_no,#{params.vehicleNo,jdbcType=VARCHAR}) > 0
        </if>
        <if test="params.driverName != null and params.driverName != ''">
            and (instr(tdo.driver_name,#{params.driverName,jdbcType=VARCHAR}) > 0
            or instr(tdo.driver_mobile,#{params.driverName,jdbcType=VARCHAR}) > 0)
        </if>
        <if test="params.dispatchUserName != null and params.dispatchUserName != ''">
            and instr(tdo.dispatch_user_name,#{params.dispatchUserName,jdbcType=VARCHAR}) > 0
        </if>
        <if test="params.dispatchTimeStart != null and params.dispatchTimeStart != ''">
            and tdo.dispatch_time &gt;= DATE_FORMAT(#{params.dispatchTimeStart,jdbcType=TIMESTAMP},'%Y-%m-%d %k:%i:%S')
        </if>
        <if test="params.dispatchTimeEnd != null and params.dispatchTimeEnd != ''">
            and tdo.dispatch_time &lt;= DATE_FORMAT(#{params.dispatchTimeEnd,jdbcType=TIMESTAMP},'%Y-%m-%d 23:59:59')
        </if>

        <if test="params.carrierOrderCode!=null and params.carrierOrderCode!=''">
            and instr(tco.carrier_order_code,#{params.carrierOrderCode,jdbcType = VARCHAR}) > 0
        </if>
        <if test="params.customerOrderCode!=null and params.customerOrderCode!=''">
            and instr(tco.customer_order_code,#{params.customerOrderCode,jdbcType =VARCHAR})>0
        </if>
        <if test="params.demandOrderTimeFrom != null and params.demandOrderTimeFrom != ''">
            and tco.publish_time &gt;= DATE_FORMAT(#{params.demandOrderTimeFrom,jdbcType=TIMESTAMP},'%Y-%m-%d %k:%i:%S')
        </if>
        <if test="params.demandOrderTimeTo != null and params.demandOrderTimeTo != ''">
            and tco.publish_time &lt;= DATE_FORMAT(#{params.demandOrderTimeTo,jdbcType=TIMESTAMP},'%Y-%m-%d 23:59:59')
        </if>
        order by tdo.dispatch_time desc
    </select>
    <resultMap id="searchList_Map" type="com.logistics.tms.controller.dispatchorder.response.DispatchOrderSearchResponseModel">
        <id column="dispatchOrderId" property="dispatchOrderId" jdbcType="BIGINT"/>
        <result column="dispatchOrderCode" property="dispatchOrderCode" jdbcType="VARCHAR"/>
        <result column="carrierOrderCount" property="carrierOrderCount" jdbcType="INTEGER"/>
        <result column="vehicleNo" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="driverName" property="driverName" jdbcType="VARCHAR"/>
        <result column="driverMobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="dispatchTime" property="dispatchTime" jdbcType="TIMESTAMP"/>
        <result column="dispatchUserName" property="dispatchUserName" jdbcType="VARCHAR"/>
        <result column="loadPointAmount" property="loadPointAmount" jdbcType="INTEGER"/>
        <result column="unloadPointAmount" property="unloadPointAmount" jdbcType="INTEGER"/>
        <result column="dispatchFreightFeeType" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="adjustFeeType" property="adjustFeeType" jdbcType="INTEGER"/>
        <result column="adjustFee" property="adjustFee" jdbcType="DECIMAL"/>
        <result column="markupFee" property="markupFee" jdbcType="DECIMAL"/>
        <collection property="carrierOrderList" resultMap="searchList_goods_Map"/>
    </resultMap>
    <resultMap id="searchList_goods_Map" type="com.logistics.tms.controller.dispatchorder.response.CarrierOrderModel">
        <id column="carrierOrderId" property="carrierOrderId" jdbcType="BIGINT"/>
        <result column="demandOrderId" property="demandOrderId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="ifCancel" property="ifCancel" jdbcType="INTEGER"/>
        <result column="ifEmpty" property="ifEmpty" jdbcType="INTEGER"/>
        <result column="dispatchFreightFee" property="dispatchFreightFee" jdbcType="DECIMAL"/>
        <result column="goodsUnit" property="goodsUnit" jdbcType="INTEGER"/>
        <result column="companyCarrierName" property="companyCarrierName" jdbcType="VARCHAR"/>
        <result column="companyCarrierType" property="companyCarrierType" jdbcType="INTEGER"/>
        <result column="carrierContactName" property="carrierContactName" jdbcType="VARCHAR"/>
        <result column="carrierContactPhone" property="carrierContactPhone" jdbcType="VARCHAR"/>
        <result column="carrierExpectAmount" property="carrierExpectAmount" jdbcType="DECIMAL"/>
        <result column="carrierUnloadAmount" property="carrierUnloadAmount" jdbcType="DECIMAL"/>
        <collection property="carrierOrderGoodsList" ofType="com.logistics.tms.controller.dispatchorder.response.CarrierOrderGoodsModel">
            <result column="goodsName" property="goodsName" jdbcType="VARCHAR"/>
            <result column="length" property="length" jdbcType="INTEGER"/>
            <result column="width" property="width" jdbcType="INTEGER"/>
            <result column="height" property="height" jdbcType="INTEGER"/>
            <result column="goodsSize" property="goodsSize" jdbcType="VARCHAR"/>
            <result column="expectAmount" property="expectAmount" jdbcType="DECIMAL"/>
            <result column="loadAmount" property="loadAmount" jdbcType="DECIMAL"/>
            <result column="unloadAmount" property="unloadAmount" jdbcType="DECIMAL"/>
            <result column="signAmount" property="signAmount" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <select id="searchList" resultMap="searchList_Map">
        select
        tdo.id                                                                                                                         as dispatchOrderId,
        tdo.dispatch_order_code                                                                                                        as dispatchOrderCode,
        tdo.carrier_order_count                                                                                                        as carrierOrderCount,
        tdo.vehicle_no                                                                                                                 as vehicleNo,
        tdo.driver_name                                                                                                                as driverName,
        tdo.driver_mobile                                                                                                              as driverMobile,
        tdo.dispatch_time                                                                                                              as dispatchTime,
        tdo.dispatch_user_name                                                                                                         as dispatchUserName,
        tdo.load_point_amount                                                                                                          as loadPointAmount,
        tdo.unload_point_amount                                                                                                        as unloadPointAmount,
        tdo.adjust_fee_type                                                                                                            as adjustFeeType,
        tdo.adjust_fee                                                                                                                 as adjustFee,
        tdo.markup_fee                                                                                                                 as markupFee,
        tdo.dispatch_freight_fee_type                                                                                                  as dispatchFreightFeeType,

        tco.id                                                                                                                         as carrierOrderId,
        tco.demand_order_id                                                                                                            as demandOrderId,
        tco.status,
        tco.if_cancel                                                                                                                  as ifCancel,
        tco.if_empty                                                                                                                   as ifEmpty,
        tco.dispatch_freight_fee                                                                                                       as dispatchFreightFee,
        tco.goods_unit                                                                                                                 as goodsUnit,
        tco.expect_amount                                                                                                              as carrierExpectAmount,
        tco.unload_amount                                                                                                              as carrierUnloadAmount,
        tco.company_carrier_name                                                                                                       as companyCarrierName,
        tco.company_carrier_type                                                                                                       as companyCarrierType,
        tco.carrier_contact_name                                                                                                       as carrierContactName,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactPhone,

        tcog.carrier_order_id                                                                                                          as carrierOrderId,
        tcog.goods_name                                                                                                                as goodsName,
        tcog.length                                                                                                                    as length,
        tcog.width                                                                                                                     as width,
        tcog.height                                                                                                                    as height,
        tcog.goods_size                                                                                                                as goodsSize,
        tcog.expect_amount                                                                                                             as expectAmount,
        tcog.load_amount                                                                                                               as loadAmount,
        tcog.unload_amount                                                                                                             as unloadAmount,
        tcog.sign_amount                                                                                                               as signAmount
        from t_dispatch_order tdo
        left join t_carrier_order tco on tco.dispatch_order_id = tdo.id and tco.valid = 1
        left join t_carrier_order_goods tcog on tcog.carrier_order_id = tco.id and tcog.valid = 1
        where tdo.id in (${ids})
        order by tdo.dispatch_time desc
    </select>

    <resultMap type="com.logistics.tms.controller.dispatchorder.response.DispatchOrderDetailResponseModel"
               id="getDetail_Map">
        <id column="dispatchOrderId" property="dispatchOrderId" jdbcType="BIGINT"/>
        <result column="vehicleId" property="vehicleId" jdbcType="BIGINT"/>
        <result column="vehicleNo" property="vehicleNo" jdbcType="VARCHAR"/>
        <result column="driverId" property="driverId" jdbcType="BIGINT"/>
        <result column="driverName" property="driverName" jdbcType="VARCHAR"/>
        <result column="driverMobile" property="driverMobile" jdbcType="VARCHAR"/>
        <result column="driverIdentity" property="driverIdentity" jdbcType="VARCHAR"/>
        <result column="loadPointAmount" property="loadPointAmount" jdbcType="INTEGER"/>
        <result column="unloadPointAmount" property="unloadPointAmount" jdbcType="INTEGER"/>
        <result column="markupFee" property="markupFee" jdbcType="DECIMAL"/>
        <result column="dispatchFreightFeeType" property="dispatchFreightFeeType" jdbcType="INTEGER"/>
        <result column="dispatchFreightFee" property="dispatchFreightFee" jdbcType="DECIMAL"/>
        <result column="adjustFeeType" property="adjustFeeType" jdbcType="INTEGER"/>
        <result column="adjustFee" property="adjustFee" jdbcType="DECIMAL"/>
        <result column="adjustRemark" property="adjustRemark" jdbcType="VARCHAR"/>
        <result column="ifAdjust" property="ifAdjust" jdbcType="INTEGER"/>
        <result column="expectArrivalTime" property="expectArrivalTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="vehicleProperty" property="vehicleProperty" jdbcType="INTEGER"/>
        <collection property="operateLogsList" ofType="com.logistics.tms.controller.dispatchorder.response.TOperateLogsResponseModel">
            <result column="operateLogsId" property="operateLogsId" jdbcType="BIGINT"/>
            <result column="operateUserName" property="operateUserName" jdbcType="VARCHAR"/>
            <result column="operateTime" property="operateTime" jdbcType="TIMESTAMP"/>
            <result column="operateType" property="operateType" jdbcType="INTEGER"/>
            <result column="operateContents" property="operateContents" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <select id="getDetail" resultMap="getDetail_Map">
        select
        tdo.id                        as dispatchOrderId,
        tdo.load_point_amount         as loadPointAmount,
        tdo.unload_point_amount       as unloadPointAmount,
        tdo.expect_arrival_time       as expectArrivalTime,
        tdo.vehicle_id                as vehicleId,
        tdo.vehicle_no                as vehicleNo,
        tdo.driver_name               as driverName,
        tdo.driver_id                 as driverId,
        tdo.driver_mobile             as driverMobile,
        tdo.driver_identity           as driverIdentity,
        tdo.dispatch_freight_fee_type as dispatchFreightFeeType,
        tdo.dispatch_freight_fee      as dispatchFreightFee,
        tdo.markup_fee                as markupFee,
        tdo.adjust_fee_type           as adjustFeeType,
        tdo.adjust_fee                as adjustFee,
        tdo.adjust_remark             as adjustRemark,
        tdo.if_adjust                 as ifAdjust,
        tdo.remark,
        tvb.vehicle_property          as vehicleProperty,
        tol.id                        as operateLogsId,
        tol.operate_user_name         as operateUserName,
        tol.operate_time              as operateTime,
        tol.operate_type              as operateType,
        tol.operate_contents          as operateContents
        from t_dispatch_order tdo
        left join t_vehicle_basic tvb on tvb.id = tdo.vehicle_id and tvb.valid = 1
        left join t_operate_logs tol on tol.object_type = 1 and tol.object_id = tdo.id and tol.valid = 1
        where tdo.valid = 1
        and tdo.id = #{dispatchOrderId,jdbcType=BIGINT}
        order by tol.id desc
    </select>



    <select id="selectByCondition" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List_Decrypt"/>
        from t_dispatch_order
        where valid = 1
        <if test="param1.dispatchOrderCode != null and param1.dispatchOrderCode != ''">
            and instr(dispatch_order_code,#{param1.dispatchOrderCode,jdbcType=VARCHAR})
        </if>
        <if test="param1.dispatchOrderIds != null and param1.dispatchOrderIds.size() != 0">
           and id in
            <foreach collection="param1.dispatchOrderIds" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="param1.companyCarrierName != null and param1.companyCarrierName != ''">
            and
            (
                case
                    when company_carrier_type = 2 then  instr(carrier_contact_name,#{param1.companyCarrierName,jdbcType=VARCHAR})
<!--                    or instr(carrier_contact_phone,#{param1.companyCarrierName,jdbcType=VARCHAR})-->
                    or instr(AES_DECRYPT(UNHEX(carrier_contact_phone),'${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}'),#{param1.companyCarrierName,jdbcType=VARCHAR})

                    when company_carrier_type = 1 then  instr(company_carrier_name,#{param1.companyCarrierName,jdbcType=VARCHAR})
                end
            )
        </if>
        <if test="param1.companyCarrierId != null">
            and company_carrier_id = #{param1.companyCarrierId,jdbcType=BIGINT}
        </if>

</select>



</mapper>