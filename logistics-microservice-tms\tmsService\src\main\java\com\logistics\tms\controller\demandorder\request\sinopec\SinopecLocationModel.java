package com.logistics.tms.controller.demandorder.request.sinopec;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/12
 */
@Data
public class SinopecLocationModel {

	@ApiModelProperty("位置类型")
	private Integer locationType;

	@ApiModelProperty("位置编码")
	private String locationCode;

	@ApiModelProperty("位置名称【仓库名称】")
	private String locationName;

	@ApiModelProperty("四级地址【省、市、区、街道】")
	private SinopecFourLevelAddressModel fourLevelAddress;

	@ApiModelProperty("详细地址")
	private String detailAddress;

	@ApiModelProperty("经纬度坐标")
	private SinopecCoordinatesModel coordinates;
}
