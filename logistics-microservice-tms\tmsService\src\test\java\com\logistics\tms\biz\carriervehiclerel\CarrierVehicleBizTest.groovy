package com.logistics.tms.biz.carriervehiclerel

import com.logistics.tms.controller.carriervehiclerel.request.SearchCarrierVehicleListRequestModel
import com.logistics.tms.controller.carriervehiclerel.response.SearchCarrierVehicleListResponseModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.mapper.TCarrierVehicleRelationMapper
import com.logistics.tms.mapper.TOperateLogsMapper
import com.logistics.tms.mapper.TVehicleBasicMapper
import com.logistics.tms.mapper.TVehicleDrivingLicenseMapper
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class CarrierVehicleBizTest extends Specification {
    @Mock
    CommonBiz commonBiz
    @Mock
    TCarrierVehicleRelationMapper tCarrierVehicleRelationMapper
    @Mock
    TOperateLogsMapper tOperateLogsMapper
    @Mock
    TVehicleBasicMapper tVehicleBasicMapper
    @Mock
    TVehicleDrivingLicenseMapper tVehicleDrivingLicenseMapper
    @InjectMocks
    CarrierVehicleBiz carrierVehicleBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tCarrierVehicleRelationMapper.getList(any())).thenReturn([new SearchCarrierVehicleListResponseModel()])

        expect:
        carrierVehicleBiz.searchList(requestModel) == expectedResult

        where:
        requestModel                               || expectedResult
        new SearchCarrierVehicleListRequestModel() || null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme