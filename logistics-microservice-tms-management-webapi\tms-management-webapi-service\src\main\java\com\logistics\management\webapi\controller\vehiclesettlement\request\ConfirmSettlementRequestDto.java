package com.logistics.management.webapi.controller.vehiclesettlement.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @author: wjf
 * @date: 2019/10/12 19:40
 */
@Data
public class ConfirmSettlementRequestDto {
    @NotBlank(message = "id不能为空")
    private String vehicleSettlementId;

    @ApiModelProperty("是否调整费用：0 否，1 是")
    @NotBlank(message = "请选择是否调整费用")
    private String ifAdjustFee;

    @ApiModelProperty("月运单数量")
    @NotBlank(message = "运单数量不能为空")
    private String carrierOrderCount;

    @ApiModelProperty("车辆运单运费合计")
    @NotBlank(message = "累计运费不能为空")
    private String dispatchFreightFeeTotal;

    @ApiModelProperty("调整费用符号：1 +，2 -")
    private String adjustFeeSymbol;

    @ApiModelProperty("调整费用")
    private String adjustFee;

    @ApiModelProperty("调整原因")
    private String adjustRemark;

    @ApiModelProperty("月应扣轮胎费")
    @NotBlank(message = "应扣轮胎费用不能为空")
    private String tireCostTotal;

    @ApiModelProperty("月应扣充油费")
    @NotBlank(message = "应扣充油费用不能为空")
    private String oilFilledFeeTotal;

    @ApiModelProperty("月应扣gps费")
    @NotBlank(message = "请维护应扣GPS费用")
    private String gpsDeductingFee;

    @ApiModelProperty("月应扣停车费")
    @NotBlank(message = "请维护应扣停车费用")
    private String parkingDeductingFee;

    @ApiModelProperty("保险未扣除费用")
    private List<VehicleInsuranceCostRequestDto> insuranceCostList;

    @ApiModelProperty("应扣保险费")
    @NotBlank(message = "应扣保险费用不能为空")
    private String insuranceFee;

    @ApiModelProperty("车辆理赔费用")
    @NotBlank(message = "保险理赔费用不能为空")
    private String vehicleClaimFee;

    @ApiModelProperty("个人意外险费用合计")
    @NotBlank(message = "请维护个人意外险费用合计，0≤个人意外险合计≤10000000")
    @Min(value = 0,message = "请维护个人意外险费用合计，0≤个人意外险合计≤10000000")
    @Max(value = 10000000,message = "请维护个人意外险费用合计，0≤个人意外险合计≤10000000")
    private String accidentInsuranceExpenseTotal;

    @ApiModelProperty("月应扣个人意外险费")
    @NotBlank(message = "请维护应扣个人意外险费用，0≤个人意外险应扣减费用≤10000000")
    @Min(value = 0,message = "请维护应扣个人意外险费用，0≤个人意外险应扣减费用≤10000000")
    @Max(value = 10000000,message = "请维护应扣个人意外险费用，0≤个人意外险应扣减费用≤10000000")
    private String accidentInsuranceFee;

    @ApiModelProperty("个人意外险费月理赔费用")
    @NotBlank(message = "请维护个人意外险当月理赔费用，0≤个人意外险当月理赔费用≤10000000")
    @Min(value = 0,message = "请维护个人意外险当月理赔费用，0≤个人意外险当月理赔费用≤10000000")
    @Max(value = 10000000,message = "请维护个人意外险当月理赔费用，0≤个人意外险当月理赔费用≤10000000")
    private String accidentInsuranceClaimFee;

    @ApiModelProperty("月应扣贷款费")
    @NotBlank(message = "请维护应扣贷款费用")
    private String loanFee;


    @ApiModelProperty("月实际应付运费")
    @NotBlank(message = "月实际应付运费不能为空")
    private String actualExpensesPayable;

    @ApiModelProperty("扣减费用合计")
    @NotBlank(message = "扣减费用合计不能为空")
    private String deductingFeeTotal;

    @ApiModelProperty("剩余未扣费用合计")
    @NotBlank(message = "剩余未扣费用合计不能为空")
    private String remainingDeductingFeeTotal;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private String attachment;

}
