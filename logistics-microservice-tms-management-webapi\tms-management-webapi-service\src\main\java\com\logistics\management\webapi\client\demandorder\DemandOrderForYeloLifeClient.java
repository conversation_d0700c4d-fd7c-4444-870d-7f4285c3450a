package com.logistics.management.webapi.client.demandorder;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.FeignClientName;
import com.logistics.management.webapi.client.demandorder.hystrix.DemandOrderForYeloLifeClientHystrix;
import com.logistics.management.webapi.client.demandorder.request.*;
import com.logistics.management.webapi.client.demandorder.response.*;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/27 16:31
 */
@FeignClient(name = FeignClientName.TMS_SERVICES, fallback = DemandOrderForYeloLifeClientHystrix.class)
public interface DemandOrderForYeloLifeClient {

    /**
     * 新生需求单列表
     *
     * @param requestModel 筛选条件
     * @return 需求单列表
     */
    @ApiOperation("新生需求单列表")
    @PostMapping(value = "/service/renewableDemandOrder/renewableDemandOrderList")
    Result<PageInfo<RenewableDemandOrderResponseModel>> renewableDemandOrderList(@RequestBody RenewableDemandOrderRequestModel requestModel);

    /**
     * 新生需求单列表导出
     *
     * @param requestModel 筛选条件
     * @return 需求单列表
     */
    @ApiOperation("新生需求单列表导出")
    @PostMapping(value = "/service/renewableDemandOrder/renewableDemandOrderListExport")
    Result<List<RenewableDemandOrderResponseModel>> renewableDemandOrderListExport(@RequestBody RenewableDemandOrderRequestModel requestModel);

    /**
     * 新生需求单详情
     *
     * @param requestModel 需求单id
     * @return 需求单详情
     */
    @ApiOperation("新生需求单详情")
    @PostMapping(value = "/service/renewableDemandOrder/renewableDemandOrderDetail")
    Result<RenewableDemandOrderDetailResponseModel> renewableDemandOrderDetail(@RequestBody DemandOrderDetailRequestModel requestModel);

    /**
     * 新生需求单列表统计
     *
     * @param requestModel 筛选条件
     * @return 统计TAB
     */
    @ApiOperation(value = "新生需求单列表统计")
    @PostMapping(value = "/service/renewableDemandOrder/getRenewableDemandOrderListStatistics")
    Result<RenewableDemandListStatisticsResponseModel> getRenewableDemandOrderListStatistics(@RequestBody RenewableDemandOrderRequestModel requestModel);

    /**
     * 云盘需求单批量发布详情
     */
    @ApiOperation(value = "新生需求单批量发布详情")
    @PostMapping(value = "/service/renewableDemandOrder/publishDetail")
    Result<List<LifeBatchPublishDetailResponseModel>> publishDetail(LifeBatchPublishDetailRequestModel mapper);

    /**
     * 新生需求单批量发布
     */
    @PostMapping(value = "/service/renewableDemandOrder/confirmPublish")
    Result<Boolean> confirmPublish(LifeBatchPublishRequestModel requestModel);

    /**
     * 新生取消需求单
     */
    @PostMapping(value = "/service/renewableDemandOrder/cancelDemandOrder")
    Result<Boolean> cancelDemandOrder(LifeDemandOrderCancelRequestModel requestModel);

    /**
     * 新生需求单修改车主
     */
    @PostMapping(value = "/service/renewableDemandOrder/modifyCarrier")
    Result<Boolean> modifyCarrier(ModifyCarrierForLifeRequestModel requestModel);

}
