package com.logistics.management.webapi.base.enums;

/**
 * @author: wjf
 * @date: 2019/12/23 15:02
 */
public enum OilFilledSourceEnum {
    DEFAULT(-999,""),
    OIL_FILLED(10,"充油"),
    REFUND(20,"退款"),
    ;

    private Integer key;
    private String value;

    OilFilledSourceEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static OilFilledSourceEnum getEnum(int key) {
        for (OilFilledSourceEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
