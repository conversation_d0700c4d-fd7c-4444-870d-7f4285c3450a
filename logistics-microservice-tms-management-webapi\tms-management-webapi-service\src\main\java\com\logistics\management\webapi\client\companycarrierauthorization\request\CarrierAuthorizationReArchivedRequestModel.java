package com.logistics.management.webapi.client.companycarrierauthorization.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarrierAuthorizationReArchivedRequestModel {

    @ApiModelProperty(value = "车主授权信息id", required = true)
    private Long carrierAuthorizationId;

    @ApiModelProperty(value = "归档文件; 为空时清空文件", required = true)
    private String archivedFilePath;

    @ApiModelProperty(value = "备注")
    private String remark;
}
