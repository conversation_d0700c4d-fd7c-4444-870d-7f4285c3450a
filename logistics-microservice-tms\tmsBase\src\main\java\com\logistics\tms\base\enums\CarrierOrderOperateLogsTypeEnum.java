package com.logistics.tms.base.enums;

/**
 * @author: wjf
 * @date: 2018/9/27 11:55
 */
public enum CarrierOrderOperateLogsTypeEnum {

    CREATE_DEMAND_ORDER(1000,"生成需求单",""),
    CREATE_CARRIER_ORDER(1010,"生成运单","车牌号：%1$s 司机：%2$s"),
    CANCEL_CARRIER_ORDER(1020,"取消运单",""),
    UPDATE_VEHICLE(1030,"修改车辆信息","，临时修改车辆为%1$s，司机：%2$s"),
    AUDIT_VEHICLE(1040,"审核车辆信息",""),
    REJECT_VEHICLE(1050,"驳回车辆信息",""),
    ARRIVED_PICK_UP(1060,"到达提货地",""),
    PICK_UP(1070,"提货","提货%1$s"),
    SCANNER_PICK_UP(1071,"扫码提货","提货%1$s"),
    ARRIVED_UNLOAD(1080,"到达卸货地",""),
    UNLOADING(1090,"卸货","卸货%1$s"),
    SIGN_IN(1100,"签收","签收%1$s，%2$s元"),
    CARRIER_MODIFY_COST(1110,"修改委托方运价","运单原报价类型%1$s，修改为%2$s，原运价%3$s元修改为%4$s元"),
    UPDATE_DRIVER_FREIGHT_FEE(1120,"修改司机运费","修改前%1$s，修改后%2$s"),
    EXT_VEHICLE_SETTLEMENT_PAYMENT(1130,"外部车辆结算-付款","已付款%1$s元"),
    EXT_VEHICLE_SETTLEMENT_FALL_BACK(1140,"外部车辆结算-退款","已退款%1$s元"),
    UPDATE_UNLOAD_ADDRESS(1150, "修改卸货地址", ""),
    CORRECT(1160, "运单纠错", "%1$s，原实提数为【%2$s】，回退【%3$s】"),
    CARRIER_ORDER_EMPTY(1170, "放空", ""),
    EXCEPTION_FALLBACK(1171, "异常回退", "%1$s-回退%2$s件"),
    ;

    private Integer key;
    private String value;
    private String format;

    public String format(String... values){
        return String.format(this.getFormat(),values);
    }

    CarrierOrderOperateLogsTypeEnum(Integer key, String value,String format) {
        this.key = key;
        this.value = value;
        this.format = format;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getFormat() {
        return format;
    }

}
