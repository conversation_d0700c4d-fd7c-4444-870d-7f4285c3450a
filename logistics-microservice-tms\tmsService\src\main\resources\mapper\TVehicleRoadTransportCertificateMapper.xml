<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logistics.tms.mapper.TVehicleRoadTransportCertificateMapper">
  <resultMap id="BaseResultMap" type="com.logistics.tms.entity.TVehicleRoadTransportCertificate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="certification_sign" jdbcType="VARCHAR" property="certificationSign" />
    <result column="business_license_number" jdbcType="VARCHAR" property="businessLicenseNumber" />
    <result column="economic_type" jdbcType="VARCHAR" property="economicType" />
    <result column="transport_tonnage" jdbcType="DECIMAL" property="transportTonnage" />
    <result column="business_scope" jdbcType="VARCHAR" property="businessScope" />
    <result column="certification_department" jdbcType="VARCHAR" property="certificationDepartment" />
    <result column="issue_date" jdbcType="TIMESTAMP" property="issueDate" />
    <result column="obtain_date" jdbcType="TIMESTAMP" property="obtainDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="last_modified_time" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, vehicle_id, certification_sign, business_license_number, economic_type, transport_tonnage, 
    business_scope, certification_department, issue_date, obtain_date, remark, created_by, 
    created_time, last_modified_by, last_modified_time, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_vehicle_road_transport_certificate
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_vehicle_road_transport_certificate
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.logistics.tms.entity.TVehicleRoadTransportCertificate">
    insert into t_vehicle_road_transport_certificate (id, vehicle_id, certification_sign,
      business_license_number, economic_type, transport_tonnage, 
      business_scope, certification_department, 
      issue_date, obtain_date, remark, 
      created_by, created_time, last_modified_by, 
      last_modified_time, valid)
    values (#{id,jdbcType=BIGINT}, #{vehicleId,jdbcType=BIGINT}, #{certificationSign,jdbcType=VARCHAR}, 
      #{businessLicenseNumber,jdbcType=VARCHAR}, #{economicType,jdbcType=VARCHAR}, #{transportTonnage,jdbcType=DECIMAL}, 
      #{businessScope,jdbcType=VARCHAR}, #{certificationDepartment,jdbcType=VARCHAR}, 
      #{issueDate,jdbcType=TIMESTAMP}, #{obtainDate,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.logistics.tms.entity.TVehicleRoadTransportCertificate" useGeneratedKeys="true" keyProperty="id">
    insert into t_vehicle_road_transport_certificate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="certificationSign != null">
        certification_sign,
      </if>
      <if test="businessLicenseNumber != null">
        business_license_number,
      </if>
      <if test="economicType != null">
        economic_type,
      </if>
      <if test="transportTonnage != null">
        transport_tonnage,
      </if>
      <if test="businessScope != null">
        business_scope,
      </if>
      <if test="certificationDepartment != null">
        certification_department,
      </if>
      <if test="issueDate != null">
        issue_date,
      </if>
      <if test="obtainDate != null">
        obtain_date,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="certificationSign != null">
        #{certificationSign,jdbcType=VARCHAR},
      </if>
      <if test="businessLicenseNumber != null">
        #{businessLicenseNumber,jdbcType=VARCHAR},
      </if>
      <if test="economicType != null">
        #{economicType,jdbcType=VARCHAR},
      </if>
      <if test="transportTonnage != null">
        #{transportTonnage,jdbcType=DECIMAL},
      </if>
      <if test="businessScope != null">
        #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="certificationDepartment != null">
        #{certificationDepartment,jdbcType=VARCHAR},
      </if>
      <if test="issueDate != null">
        #{issueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="obtainDate != null">
        #{obtainDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logistics.tms.entity.TVehicleRoadTransportCertificate">
    update t_vehicle_road_transport_certificate
    <set>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="certificationSign != null">
        certification_sign = #{certificationSign,jdbcType=VARCHAR},
      </if>
      <if test="businessLicenseNumber != null">
        business_license_number = #{businessLicenseNumber,jdbcType=VARCHAR},
      </if>
      <if test="economicType != null">
        economic_type = #{economicType,jdbcType=VARCHAR},
      </if>
      <if test="transportTonnage != null">
        transport_tonnage = #{transportTonnage,jdbcType=DECIMAL},
      </if>
      <if test="businessScope != null">
        business_scope = #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="certificationDepartment != null">
        certification_department = #{certificationDepartment,jdbcType=VARCHAR},
      </if>
      <if test="issueDate != null">
        issue_date = #{issueDate,jdbcType=TIMESTAMP},
      </if>
      <if test="obtainDate != null">
        obtain_date = #{obtainDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedTime != null">
        last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.logistics.tms.entity.TVehicleRoadTransportCertificate">
    update t_vehicle_road_transport_certificate
    set vehicle_id = #{vehicleId,jdbcType=BIGINT},
      certification_sign = #{certificationSign,jdbcType=VARCHAR},
      business_license_number = #{businessLicenseNumber,jdbcType=VARCHAR},
      economic_type = #{economicType,jdbcType=VARCHAR},
      transport_tonnage = #{transportTonnage,jdbcType=DECIMAL},
      business_scope = #{businessScope,jdbcType=VARCHAR},
      certification_department = #{certificationDepartment,jdbcType=VARCHAR},
      issue_date = #{issueDate,jdbcType=TIMESTAMP},
      obtain_date = #{obtainDate,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      last_modified_time = #{lastModifiedTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>