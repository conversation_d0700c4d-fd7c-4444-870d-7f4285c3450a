package com.logistics.management.webapi.api.impl.terminalreachmanagement.mapping;

import com.logistics.management.webapi.api.feign.terminalreachmanagement.dto.SearchReachManagementListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.CheckReachContactEnum;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.terminalreachmanagement.model.SearchReachManagementListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public class SearchReachManagementListMapping extends MapperMapping<SearchReachManagementListResponseModel, SearchReachManagementListResponseDto> {

    @Override
    public void configure() {
        SearchReachManagementListResponseModel source = getSource();
        SearchReachManagementListResponseDto destination = getDestination();
        if (source != null){
            destination.setCheckReachContactLabel(CheckReachContactEnum.getEnum(source.getCheckReachContact()).getValue());

            destination.setReachDriver(source.getReachDriverName() + " " + FrequentMethodUtils.encryptionData(source.getReachDriverPhone(), EncodeTypeEnum.MOBILE_PHONE));
            destination.setContact(source.getReachContactor() + " " + FrequentMethodUtils.encryptionData(source.getReachTelephone(), EncodeTypeEnum.MOBILE_PHONE));

            destination.setReachAddress(source.getReachProvinceName() + source.getReachCityName() + source.getReachAreaName() + source.getReachAddressDetail());

            destination.setDistanceDeviation(Objects.isNull(source.getDistanceDeviation())? CommonConstant.ZERO:new BigDecimal(source.getDistanceDeviation()).setScale(CommonConstant.INTEGER_TWO, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());

        }

    }

}
