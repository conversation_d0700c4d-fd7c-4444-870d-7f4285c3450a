package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DemandOrderDetailForLeYiResponseDto {
    @ApiModelProperty("需求单id")
    private String demandId="";
    @ApiModelProperty("需求单号")
    private String demandOrderCode="";
    @ApiModelProperty("内部单号")
    private String customerOrderCode="";
    @ApiModelProperty("需求单状态")
    private String status="";
    private String statusLabel="";
    @ApiModelProperty("客户")
    private String customerCompanyName="";
    @ApiModelProperty("需求生成人")
    private String publishName="";
    @ApiModelProperty("需求生成时间")
    private String publishTime="";
    @ApiModelProperty("发货地址")
    private String loadDetailAddress="";
    @ApiModelProperty("发货人")
    private String consignorName="";
    @ApiModelProperty("期望提货时间")
    private String expectedLoadTime="";
    @ApiModelProperty("期望卸货时间")
    private String expectedUnloadTime="";
    @ApiModelProperty("备注")
    private String remark="";
    @ApiModelProperty("取消原因")
    private String cancelReason;

    @ApiModelProperty("货物信息")
    private List<DemandOrderGoodsResponseDto> goodsResponseModel;
    @ApiModelProperty("运单信息")
    private List<DemandOrderCarrierResponseDto> carrierResponseModel;


    @ApiModelProperty("货主费用类型 1单价 2一口价")
    private String contractPriceType = "";
    @ApiModelProperty("货主费用")
    private String contractPrice = "";
    @ApiModelProperty("预计货主费用合计")
    private String contractPriceTotal = "";

    @ApiModelProperty("实际货主费用类型 1单价 2一口价")
    private String actualContractPriceType = "";
    @ApiModelProperty("实际货主费用")
    private String actualContractPrice = "";
    @ApiModelProperty("实际货主费用合计")
    private String actualContractPriceTotal = "";

    @ApiModelProperty("车主费用类型 1单价 2一口价")
    private String carrierPriceType = "";
    @ApiModelProperty("车主费用")
    private String carrierPrice = "";
    @ApiModelProperty("车主费用合计")
    private String carrierPriceTotal = "";

    @ApiModelProperty("预计车主费用类型 1单价 2一口价")
    private String expectedCarrierPriceType = "";
    @ApiModelProperty("预计车主费用")
    private String expectedCarrierPrice = "";
    @ApiModelProperty("预计车主费用合计")
    private String expectedCarrierPriceTotal = "";

    @ApiModelProperty("单位")
    private String goodsUnit = "";
    @ApiModelProperty("上游客户")
    private String upstreamCustomer = "";

    @ApiModelProperty("凭证日期")
    private String ticketDate = "";
    @ApiModelProperty("结算吨位")
    private String settlementTonnage = "";
    @ApiModelProperty("结算吨位文本信息")
    private String settlementTonnageLabel = "";
    @ApiModelProperty("需求类型")
    private String entrustType;

    @ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
    private String isOurCompany;

    @ApiModelProperty("装卸方式")
    private String loadingUnloadingPartLabel= "";
    @ApiModelProperty("装卸费用")
    private String loadingUnloadingCharge= "";
    @ApiModelProperty("其他要求")
    private String otherRequirements= "";
    @ApiModelProperty("1.3.7新增；项目标签")
    private String projectLabel="";
}
