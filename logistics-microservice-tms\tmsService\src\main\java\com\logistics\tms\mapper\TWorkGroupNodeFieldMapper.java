package com.logistics.tms.mapper;

import com.logistics.tms.entity.TWorkGroupNodeField;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* Created by Mybatis Generator on 2023/12/21
*/
@Mapper
public interface TWorkGroupNodeFieldMapper extends BaseMapper<TWorkGroupNodeField> {

    List<TWorkGroupNodeField> selectAllByWorkGroupNodeIdIn(@Param("workGroupNodeIds") Collection<Long> workGroupNodeIds);

    void delAllFieldByNodeId(@Param("nodeIdList") List<Long> nodeIdList, @Param("userName") String userName);
}