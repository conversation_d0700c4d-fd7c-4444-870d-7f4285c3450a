package com.logistics.tms.biz.demandorderobjection

import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionRequestModel
import com.logistics.tms.api.feign.demandorderobjection.model.SearchDemandOrderObjectionResponseModel
import com.logistics.tms.mapper.TDemandOrderObjectionMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class DemandOrderObjectionBizTest extends Specification {
    @Mock
    TDemandOrderObjectionMapper tDemandOrderObjectionMapper
    @InjectMocks
    DemandOrderObjectionBiz demandOrderObjectionBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Demand Order Objection where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(tDemandOrderObjectionMapper.searchDemandOrderObjectionIds(any())).thenReturn([1l])
        when(tDemandOrderObjectionMapper.searchDemandOrderObjection(anyString())).thenReturn([new SearchDemandOrderObjectionResponseModel()])

        expect:
        demandOrderObjectionBiz.searchDemandOrderObjection(requestModel) == expectedResult

        where:
        requestModel                                 || expectedResult
        new SearchDemandOrderObjectionRequestModel() || null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme