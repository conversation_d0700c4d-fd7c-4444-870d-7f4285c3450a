package com.logistics.management.webapi.api.impl.vehiclesafecheck.mapping;

import com.logistics.management.webapi.api.feign.vehiclesafecheck.dto.SearchSafeCheckListResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.SafeCheckStatusEnum;
import com.logistics.management.webapi.base.enums.VehiclePropertyEnum;
import com.logistics.tms.api.feign.vehiclesafecheck.model.SearchSafeCheckListResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.StringUtils;
import java.util.Optional;

/**
 * @Author: sj
 * @Date: 2019/11/13 10:34
 */
public class SearchListMapping extends MapperMapping<SearchSafeCheckListResponseModel,SearchSafeCheckListResponseDto> {
    @Override
    public void configure() {
        SearchSafeCheckListResponseModel source = this.getSource();
        SearchSafeCheckListResponseDto dto = this.getDestination();

        if (source != null) {
            //状态展示文本
            dto.setStatusLabel(SafeCheckStatusEnum.getEnum(source.getStatus()).getValue());
            if (StringUtils.isBlank(dto.getReformCount())) {
                dto.setReformCount(CommonConstant.ZERO);
            }
            //司机展示文本,司机姓名 手机号
            if (source.getStaffName() != null) {
                dto.setStaffName(Optional.ofNullable(source.getStaffName()).orElse("") + " " + Optional.ofNullable(source.getStaffMobile()).orElse(""));
            }
            //车辆机构展示文本
            dto.setVehiclePropertyLabel(VehiclePropertyEnum.getEnum(source.getVehicleProperty()).getValue());
        }
    }
}
