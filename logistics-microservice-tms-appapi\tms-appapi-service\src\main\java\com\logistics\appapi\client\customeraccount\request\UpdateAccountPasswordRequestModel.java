package com.logistics.appapi.client.customeraccount.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：wjf
 * @date：2021/5/12 14:54
 */
@Data
public class UpdateAccountPasswordRequestModel {
    @ApiModelProperty(value = "用户账号")
    private String userAccount;
    @ApiModelProperty(value = "手机验证码")
    private String verificationCode;
    @ApiModelProperty(value = "密码")
    private String password;
    @ApiModelProperty(value = "验证码来源：1 运营平台，2 客户前台，3 客户APP，4 承运商网站，5 司机APP，6 云途微信，7 司机小程序")
    private Integer codeSource;
    @ApiModelProperty(value = "验证码类型 1 承运商网站登陆，2 承运商网站找回密码，3 司机登陆，4 司机找回密码，5 微信绑定手机号，6 承运商网站注册，7 司机APP注册")
    private Integer codeType;
}
