package com.logistics.management.webapi.client.sysconfig.hystrix;


import com.logistics.management.webapi.client.sysconfig.SysConfigClient;
import com.logistics.management.webapi.client.sysconfig.request.SysConfigEditRequestModel;
import com.logistics.management.webapi.client.sysconfig.request.SysConfigRequestModel;
import com.logistics.management.webapi.client.sysconfig.response.SysConfigResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/27
 */
@Component
public class SysConfigClientHystrix implements SysConfigClient {
    @Override
    public Result<String> getSysConfig(SysConfigRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<List<SysConfigResponseModel>> batchGetSysConfig(Collection<SysConfigRequestModel> requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> editSysConfig(SysConfigEditRequestModel requestModel) {
        return Result.timeout();
    }
}
