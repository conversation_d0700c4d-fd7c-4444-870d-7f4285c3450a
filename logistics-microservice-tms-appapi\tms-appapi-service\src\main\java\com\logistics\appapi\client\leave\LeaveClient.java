package com.logistics.appapi.client.leave;

import com.logistics.appapi.client.FeignClientName;
import com.logistics.appapi.client.leave.hystrix.LeaveClientHystrix;
import com.logistics.appapi.client.leave.request.*;
import com.logistics.appapi.client.leave.response.DriverLeaveApplyDetailResponseModel;
import com.logistics.appapi.client.leave.response.DriverLeaveApplyListResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: wjf
 * @date: 2024/3/14 13:57
 */
@FeignClient(name = FeignClientName.TMS_SERVICES,
        fallback = LeaveClientHystrix.class)
public interface LeaveClient {

    @ApiOperation(value = "请假申请", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/leave/leaveApply")
    Result<Boolean> driverLeaveApply(@RequestBody DriverLeaveApplyRequestModel requestModel);

    @ApiOperation(value = "请假记录查询", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/leave/leaveApplyList")
    Result<DriverLeaveApplyListResponseModel> driverLeaveApplyList(@RequestBody DriverLeaveApplyListRequestModel requestModel);

    @ApiOperation(value = "请假记录详情查询", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/leave/leaveApplyDetail")
    Result<DriverLeaveApplyDetailResponseModel> driverLeaveApplyDetail(@RequestBody DriverLeaveApplyDetailRequestModel requestModel);

    @ApiOperation(value = "重新提交请假申请", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/leave/resubmitLeaveApply")
    Result<Boolean> driverResubmitLeaveApply(@RequestBody DriverLeaveApplyResubmitRequestModel requestModel);

    @ApiOperation(value = "撤销请假申请", tags = "1.0.6")
    @PostMapping(value = "/service/driverApplet/leave/cancelLeaveApply")
    Result<Boolean> driverCancelLeaveApply(@RequestBody DriverLeaveApplyCancelRequestModel requestModel);

}
