package com.logistics.tms.api.feign.carrierfreight;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.api.feign.carrierfreight.hystrix.CarrierFreightServiceApiHystrix;
import com.logistics.tms.api.feign.carrierfreight.model.CarrierFreightAddRequestModel;
import com.logistics.tms.api.feign.carrierfreight.model.CarrierFreightEnableRequestModel;
import com.logistics.tms.api.feign.carrierfreight.model.SearchCarrierFreightRequestModel;
import com.logistics.tms.api.feign.carrierfreight.model.SearchCarrierFreightResponseModel;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 车主运价
 *
 * <AUTHOR>
 * @date 2022/9/1 14:32
 */
@Api(value = "CarrierFreightServiceApi-车主运价管理")
@FeignClient(name = "logistics-tms-services", fallback = CarrierFreightServiceApiHystrix.class)
public interface CarrierFreightServiceApi {

    /**
     * 添加车主运价
     *
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/service/carrierFreight/addCarrierFreight")
    Result<Boolean> addCarrierFreight(@RequestBody CarrierFreightAddRequestModel requestModel);

    /**
     * 车主运价禁用启用
     *
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/service/carrierFreight/enable")
    Result<Boolean> enable(@RequestBody CarrierFreightEnableRequestModel requestModel);

    /**
     * 查询车主运价列表
     *
     * @param requestModel
     * @return
     */
    @PostMapping(value = "/service/carrierFreight/searchList")
    Result<PageInfo<SearchCarrierFreightResponseModel>> searchList(@RequestBody SearchCarrierFreightRequestModel requestModel);
}
