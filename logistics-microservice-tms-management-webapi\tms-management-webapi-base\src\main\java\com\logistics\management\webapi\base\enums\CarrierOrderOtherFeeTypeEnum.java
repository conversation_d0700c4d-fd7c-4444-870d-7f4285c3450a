package com.logistics.management.webapi.base.enums;

public enum CarrierOrderOtherFeeTypeEnum {

    DEFAULT(-1,""),
    SHORT_BARGE_FEE(1, "短驳费"),
    STORAGE_FEE(2, "保管费"),
    HANDLING_CHARGES_FEE(3, "装卸费"),
    CRUSHING_FEE(4, "压车费"),
    QUALITY_PENALTY_FEE(5, "质量处罚费"),
    OTHER_FEE(6, "其他杂费");

    CarrierOrderOtherFeeTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    private Integer key;
    private String value;

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CarrierOrderOtherFeeTypeEnum getEnum(Integer key) {
        for (CarrierOrderOtherFeeTypeEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
