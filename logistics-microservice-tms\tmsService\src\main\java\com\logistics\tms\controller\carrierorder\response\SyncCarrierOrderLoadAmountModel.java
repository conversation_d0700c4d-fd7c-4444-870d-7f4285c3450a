package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2022/3/22 9:49
 */
@Data
public class SyncCarrierOrderLoadAmountModel {
    @ApiModelProperty(value = "提货数量")
    private Integer loadAmount;

    @ApiModelProperty("sku code")
    private String productTypeCode;

    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    @ApiModelProperty(value = "托盘品名")
    private String goodsName;

    @ApiModelProperty(value = "长")
    private Integer length;

    @ApiModelProperty(value = "宽")
    private Integer width;

    @ApiModelProperty(value = "高")
    private Integer height;
}
