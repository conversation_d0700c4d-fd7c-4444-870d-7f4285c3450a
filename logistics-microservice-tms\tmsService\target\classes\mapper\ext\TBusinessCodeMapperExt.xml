<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TBusinessCodeMapper" >
    <select id="selectBusinessCodeByCondition" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM  t_business_code
        WHERE valid=1
        AND business_type=#{businessType,jdbcType=VARCHAR}
        AND business_code_prefix = #{businessCodePrefix,jdbcType=VARCHAR}
        AND business_code_date_format = #{businessCodeDateFormat,jdbcType=VARCHAR}
        FOR UPDATE
    </select>
</mapper>