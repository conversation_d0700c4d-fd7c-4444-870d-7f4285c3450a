package com.logistics.management.webapi.controller.dispatch;

import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.base.enums.PriceTypeEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.management.webapi.client.dispatch.DispatchClient;
import com.logistics.management.webapi.client.dispatch.request.*;
import com.logistics.management.webapi.client.dispatch.response.DemandOrderSpecialDispatchDetailResponseModel;
import com.logistics.management.webapi.client.dispatch.response.SearchCanJoinShippingOrderRespModel;
import com.logistics.management.webapi.client.dispatch.response.SearchSpecialDispatchIfMatchFreightRespModel;
import com.logistics.management.webapi.controller.dispatch.mapping.SpecialDispatchDetailMapping;
import com.logistics.management.webapi.controller.dispatch.request.*;
import com.logistics.management.webapi.controller.dispatch.response.DemandOrderSpecialDispatchDetailResponseDto;
import com.logistics.management.webapi.controller.dispatch.response.SearchCanJoinShippingOrderRespDto;
import com.logistics.management.webapi.controller.dispatch.response.SearchSpecialDispatchIfMatchFreightRespDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 调度管理
 */
@Api(value = "API-DispatchServiceApi-调度管理",tags = "调度管理")
@RestController
@RequestMapping(value = "/api/dispatch")
public class DispatchController {

    @Resource
    private DispatchClient dispatchClient;

    /**
     * 调度车辆-确认调度（物流管理） 3.21.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/dispatchVehicle")
    @ApiOperation(value = "调度车辆-确认调度（物流管理）", tags = "1.4.0")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> dispatchVehicle(@RequestBody @Valid DispatchRequestDto requestDto) {
        DispatchRequestModel model = MapperUtils.mapperNoDefault(requestDto, DispatchRequestModel.class);
        model.setSource(CommonConstant.INTEGER_ONE);
        return dispatchClient.dispatchVehicle(model);
    }

    /**
     * 调度车辆-确认调度（云盘物流管理） 3.21.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/dispatchVehicleForLeYi")
    @ApiOperation(value = "调度车辆-确认调度（云盘物流管理）", tags = "1.4.0")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> dispatchVehicleForLeYi(@RequestBody @Valid DispatchForLeYiRequestDto requestDto) {
        //勾选了临时定价
        if (CommonConstant.ONE.equals(requestDto.getIfProvisionalPricing())){
            //车主运费类型判断
            PriceTypeEnum priceTypeEnum = PriceTypeEnum.getEnumStringKey(requestDto.getCarrierPriceType());
            if (priceTypeEnum == null){
                throw new BizException(ManagementWebApiExceptionEnum.CARRIER_PRICE_ERROR);
            }
            //车主运费校验
            if (!FrequentMethodUtils.isNumberOrFloatNumberTwo(requestDto.getCarrierPrice())
                    || ConverterUtils.toBigDecimal(requestDto.getCarrierPrice()).compareTo(BigDecimal.ZERO) < CommonConstant.INTEGER_ZERO
                    || ConverterUtils.toBigDecimal(requestDto.getCarrierPrice()).compareTo(CommonConstant.BIG_DECIMAL_ONE_HUNDRED_THOUSAND) > CommonConstant.INTEGER_ZERO){
                throw new BizException(ManagementWebApiExceptionEnum.CARRIER_PRICE_ERROR);
            }
        }else{
            requestDto.setCarrierPriceType(null);
            requestDto.setCarrierPrice(null);
        }

        DispatchRequestModel model = MapperUtils.mapperNoDefault(requestDto, DispatchRequestModel.class);
        model.setSource(CommonConstant.INTEGER_ONE);
        return dispatchClient.dispatchVehicle(model);
    }

    /**
     * 零担调度详情 v2.42
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/specialDispatchDetail")
    public Result<DemandOrderSpecialDispatchDetailResponseDto> specialDispatchDetail(@RequestBody @Valid DemandOrderSpecialDispatchDetailRequestDto requestDto) {
        DemandOrderSpecialDispatchDetailRequestModel requestModel = MapperUtils.mapper(requestDto, DemandOrderSpecialDispatchDetailRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        Result<DemandOrderSpecialDispatchDetailResponseModel> result = dispatchClient.specialDispatchDetail(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), DemandOrderSpecialDispatchDetailResponseDto.class, new SpecialDispatchDetailMapping()));
    }


    /**
     * 零担调度查询是否有匹配的串点和车长费用 2.42
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/searchSpecialDispatchIfMatchFreight")
    public Result<SearchSpecialDispatchIfMatchFreightRespDto> searchSpecialDispatchIfMatchFreight(@RequestBody @Valid SearchSpecialDispatchIfMatchFreightReqDto requestDto) {
        SearchSpecialDispatchIfMatchFreightReqModel requestModel = MapperUtils.mapper(requestDto, SearchSpecialDispatchIfMatchFreightReqModel.class);
        Result<SearchSpecialDispatchIfMatchFreightRespModel> result = dispatchClient.searchSpecialDispatchIfMatchFreight(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SearchSpecialDispatchIfMatchFreightRespDto.class));
    }

    /**
     * 零担调度车辆 2.42
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/specialDispatchVehicle")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> specialDispatchVehicle(@RequestBody @Valid SpecialDispatchVehicleRequestDto requestDto) {

        if (StringUtils.isBlank(requestDto.getShippingOrderCode())) {
            if (StringUtils.isBlank(requestDto.getDriverId())) {
                throw new BizException("请选择司机");
            }
            if (StringUtils.isBlank(requestDto.getVehicleLength())) {
                throw new BizException("请选择车长");
            }
            if (StringUtils.isBlank(requestDto.getExpectArrivalTime())) {
                throw new BizException("预计到货时间不能为空");
            }
        }

        SpecialDispatchVehicleRequestModel requestModel = MapperUtils.mapperNoDefault(requestDto, SpecialDispatchVehicleRequestModel.class);
        requestModel.setSource(CommonConstant.INTEGER_ONE);
        return dispatchClient.specialDispatchVehicle(requestModel);
    }


    /**
     * 零担调度 根据车牌号查询可加入的零担 2.42
     */
    @PostMapping(value = "/searchCanJoinShippingOrder")
    public Result<List<SearchCanJoinShippingOrderRespDto>> searchCanJoinShippingOrder(@RequestBody @Valid SearchCanJoinShippingOrderRequestDto requestDto) {
        SearchCanJoinShippingOrderRequestModel requestModel = MapperUtils.mapper(requestDto, SearchCanJoinShippingOrderRequestModel.class);
        Result<List<SearchCanJoinShippingOrderRespModel>> result = dispatchClient.searchCanJoinShippingOrder(requestModel);
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), SearchCanJoinShippingOrderRespDto.class));
    }


    /**
     * 调度车辆-确认调度（新生管理） 3.21.0
     * @param requestDto
     * @return
     */
    @PostMapping(value = "/dispatchVehicleForYeloLife")
    @ApiOperation(value = "调度车辆-确认调度（新生管理）", tags = "1.4.0")
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> dispatchVehicleForYeloLife(@RequestBody @Valid DispatchForYeloLifeRequestDto requestDto) {
        DispatchRequestModel model = MapperUtils.mapperNoDefault(requestDto, DispatchRequestModel.class);
        model.setSource(CommonConstant.INTEGER_ONE);
        return dispatchClient.dispatchVehicle(model);
    }
}