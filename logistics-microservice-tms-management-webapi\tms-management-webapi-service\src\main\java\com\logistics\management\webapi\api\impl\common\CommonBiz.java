package com.logistics.management.webapi.api.impl.common;

import com.logistics.management.webapi.client.thirdparty.basicdata.file.request.GetFileByFilePathRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.request.GetOSSUrlRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.response.GetFileByteOSSResponseModel;
import com.logistics.management.webapi.controller.carrierorder.response.CreateQrCodeModel;
import com.logistics.management.webapi.controller.uploadfile.response.SrcUrlDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.ManagementWebApiExceptionEnum;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.FileClient;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.request.BatchGetOSSFileUrlRequestModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.response.GetOSSUrlResponseModel;
import com.logistics.management.webapi.client.thirdparty.basicdata.file.response.UploadFileOSSResponseModel;
import com.yelo.tools.utils.ConverterUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.QRCodeUtils;
import com.yelo.tools.utils.StringUtils;
import com.yelo.tray.core.base.Result;
import com.yelo.tray.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Arrays;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component("commonBiz")
public class CommonBiz {

    @Value("${images.tempImageUploadCatalog}")
    private String tempImageUploadCatalog;
    @Value("${images.fileAccessAddressTemp}")
    private String fileAccessAddressTemp;
    @Resource
    private FileClient fileClient;

    //下载文件
    public void downLoadFile(String name, String type, InputStream inputStream, HttpServletResponse response) throws IOException {
        if (inputStream != null) {
            response.reset();
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=\"" + new String((name + "." + type).getBytes(), StandardCharsets.ISO_8859_1) + "\"");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setDateHeader("Expires", 0);
            ServletOutputStream servletOS = null;
            try {
                servletOS = response.getOutputStream();
                byte[] buf = new byte[4096];
                int readLength;
                while ((readLength = inputStream.read(buf)) != -1) {
                    servletOS.write(buf, 0, readLength);
                }
                servletOS.flush();
            } catch (IOException var12) {
                log.info("", var12);
            } finally {
                inputStream.close();
            }
        }
    }

    //下载文件
    public  void downLoadFile(String name, String type, byte[] fileByte, HttpServletResponse response) {
        if(fileByte != null){
            try(ServletOutputStream servletOS = response.getOutputStream()) {
                response.reset();
                response.setContentType("application/vnd.ms-excel");
                response.setHeader("Content-Disposition", "attachment;filename=\"" + URLEncoder.encode(name + "." + type, CommonConstant.ENCODE_UTF) +"\"");
                response.setHeader("Pragma", "no-cache");
                response.setHeader("Cache-Control", "no-cache");
                response.setDateHeader("Expires", 0);
                servletOS.write(fileByte);
                servletOS.flush();
            } catch (Exception var12) {
                log.info("",var12);
            }
        }
    }

    //上传文件
    public SrcUrlDto uploadToTmpCatalog(MultipartFile file) {
        String fileOriginFileName = file.getOriginalFilename();
        String fileType = fileOriginFileName.substring(fileOriginFileName.lastIndexOf('.'));
        String fileName = fileOriginFileName.substring(CommonConstant.INTEGER_ZERO, fileOriginFileName.lastIndexOf('.'));
        if (!checkFileSize(file.getSize(), 10, "M")) {
            throw new BizException(ManagementWebApiExceptionEnum.UPLOAD_SIZE_TO_LONG);
        }
        Result<UploadFileOSSResponseModel> result = fileClient.uploadMultiPartFileOSS(file);
        result.throwException();
        String tmpRelativeUrl = result.getData().getTempRelativePath().substring(result.getData().getTempRelativePath().lastIndexOf("/"));
        String relativeUrl = result.getData().getRelativePath().substring(result.getData().getRelativePath().lastIndexOf("/"));
        SrcUrlDto srcUrlDto = new SrcUrlDto();
        srcUrlDto.setSrc(fileAccessAddressTemp + tmpRelativeUrl);
        srcUrlDto.setRelativePath(relativeUrl);
        srcUrlDto.setFileName(fileName);
        srcUrlDto.setFileType(fileType);
        return srcUrlDto;
    }
    /**
     * 获取图片访问路径
     *
     * @param  fileSrc
     */
    public String getImageURL(String fileSrc){
        if(StringUtils.isEmpty(fileSrc)){
            return "";
        }
        GetOSSUrlRequestModel requestModel = new GetOSSUrlRequestModel();
        requestModel.setFileSrc(fileSrc);
        Result<GetOSSUrlResponseModel> result = fileClient.getOSSFileUrl(requestModel);
        result.throwException();
        return result.getData().getFileSrc();
    }

    /**
     * 批量获取图片访问路径
     *
     * @param  fileSrc
     */
    public Map<String,String> batchGetOSSFileUrl(List<String> fileSrc){
        if(ListUtils.isEmpty(fileSrc)){
            return new HashMap<>();
        }
        BatchGetOSSFileUrlRequestModel requestModel = new BatchGetOSSFileUrlRequestModel();
        requestModel.setFileSrcList(fileSrc);
        Result<List<GetOSSUrlResponseModel>> result = fileClient.batchGetOSSFileUrl(requestModel);
        result.throwException();
        if(ListUtils.isNotEmpty(result.getData())){
            return result.getData().stream().collect(Collectors.toMap(GetOSSUrlResponseModel::getSourceFileSrc, GetOSSUrlResponseModel::getFileSrc,(key1,key2)->key2));
        }else{
            return new HashMap<>();
        }
    }

    /**
     * 将excel内容转为放入dto里
     *
     * @param clazz
     * @param excelList
     * @param <E>
     * @return
     */
    public <E> List<E> transferExcelToObject(Class<E> clazz, List<List<Object>> excelList) {
        List<E> objList = new ArrayList<>();
        E obj = null;
        for (List<Object> tmpObj : excelList) {
            try {
                obj = clazz.newInstance();
            } catch (InstantiationException | IllegalAccessException e) {
                log.warn(e.getMessage(), e);
            }
            Field[] fields = clazz.getDeclaredFields();
            int idx = 0;
            for (Field field : fields) {
                String methodSig = "set" + field.getName().substring(0, 1).toUpperCase() + field.getName().substring(1);
                try {
                    Method method = clazz.getDeclaredMethod(methodSig, String.class);
                    method.invoke(obj, ConverterUtils.toString(tmpObj.get(idx)));
                } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                    log.warn(e.getMessage(), e);
                }
                idx++;
            }
            objList.add(obj);
        }
        return objList;

    }

    public void convertObjectListToNullIfIsEmpty(List list) {
        if (ListUtils.isNotEmpty(list)) {
            for (Object obg : list) {
                convertObjectFieldToNullIfIsEmpty(obg);
            }
        }
    }

    public void convertObjectFieldToNullIfIsEmpty(Object object) {
        if (object == null) {
            return;
        }
        Class clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();
        if (!Arrays.isNullOrEmpty(fields)) {
            for (Field field : fields) {
                field.setAccessible(true);
                if (field.getType() == String.class) {
                    try {
                        if (StringUtils.isBlank((String) field.get(object))) {
                            field.set(object, null);
                        }
                    } catch (IllegalAccessException e) {
                        log.warn(e.getMessage(), e);
                    }
                }
            }
        }
    }

    //校验文件大小
    public static boolean checkFileSize(Long len, int size, String unit) {
        double fileSize = 0;
        unit = unit.toUpperCase();
        if ("B".equals(unit)) {
            fileSize = (double) len;
        } else if ("K".equals(unit)) {
            fileSize = (double) len / 1024;
        } else if ("M".equals(unit)) {
            fileSize = (double) len / 1048576;
        } else if ("G".equals(unit)) {
            fileSize = (double) len / 1073741824;
        }
        return !(fileSize > size);
    }

    //判断文件夹是否存在
    public boolean dirIfExist(String saveFile) {
        boolean existFlag = true;
        File file = new File(saveFile);
        if (!file.exists()) {//创建目录
            file.mkdirs();
        } else {
            existFlag = false;
        }
        return existFlag;
    }

    //将文件写到指定目录
    public void writeToLocal(InputStream is, String saveFile) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int read = 0;
        while (read != -1) {
            read = is.read(buffer);
            if (read > 0) {
                baos.write(buffer, 0, read);
            }
        }
        byte[] bytes = baos.toByteArray();
        try (OutputStream outputStream = new FileOutputStream(saveFile)) {
            outputStream.write(bytes);
        }
    }

    /**
     * 获取oss文件字节数组
     *
     * @param ossPath
     */
    public byte[] getOssFileByte(String ossPath) {
        if (StringUtils.isBlank(ossPath)) {
            return null;
        }

        GetFileByFilePathRequestModel requestModel = new GetFileByFilePathRequestModel();
        requestModel.setFilePath(ossPath);
        Result<GetFileByteOSSResponseModel> result = fileClient.getFileByteOSS(requestModel);
        result.throwException();
        return result.getData().getFileByte();
    }




    /**
     * 创建二维码
     *
     * @param linkPrefix 链接前缀
     * @param qrCodeParamsMap  业务参数
     * @return 二维码图片地址
     */
    public CreateQrCodeModel createQrCode(String linkPrefix,
                                          Map<String, Object> qrCodeParamsMap) {

        CreateQrCodeModel createQrCodeModel = new CreateQrCodeModel();
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            String qrCodeContent = getQrCodeContent(linkPrefix, qrCodeParamsMap);
            if (StringUtils.isNotBlank(qrCodeContent)) {
                //生成二维码
                BufferedImage bufferedImage = QRCodeUtils.createImage(qrCodeContent, null, false);
                ImageIO.write(bufferedImage, "jpg", bos);
                createQrCodeModel.setFileByte(bos.toByteArray());
            }
        } catch (Exception e) {
            log.error("生成二维码失败", e);
        }
        return createQrCodeModel;
    }

    /**
     * 拼接二维码内容
     * @param linkPrefix 链接前缀
     * @param qrCodeParamsMap 业务参数
     * @return 跳转路径拼接业务参数的字符串
     */
    public String getQrCodeContent(String linkPrefix,
                                   Map<String, Object> qrCodeParamsMap){
        return CommonConstant.linkParamsJoinFunction.apply(linkPrefix, qrCodeParamsMap);
    }
}
