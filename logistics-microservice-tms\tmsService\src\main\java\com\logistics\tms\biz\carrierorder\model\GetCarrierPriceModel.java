package com.logistics.tms.biz.carrierorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/9/6
 */
@Data
@Accessors(chain = true)
public class GetCarrierPriceModel {

	@ApiModelProperty("提货区id")
	private Long loadAreaId;

	@ApiModelProperty("卸货区id")
	private Long unloadAreaId;

	@ApiModelProperty("货物单位：1 件，2 吨，3 件（方），4 块")
	private Integer goodsUnit;

	@ApiModelProperty("货物数量")
	private BigDecimal goodsAmount;

	@ApiModelProperty("委托类型：1 发货，2 回收入库，3 采购，4 调拨，5 委托发布，6 预约，7 退货，9 供应商直配，10 回收出库，11 退货仓库配送，12 退货调拨")
	private Integer entrustType;

	@ApiModelProperty("预计距离")
	private BigDecimal expectMileage;


	@ApiModelProperty("匹配到的价格类型")
	private Integer carrierPriceType;

	@ApiModelProperty("匹配到的价格")
	private BigDecimal carrierPrice;
}
