package com.logistics.management.webapi.client.messagenotice.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.client.messagenotice.MessageNoticeClient;
import com.logistics.management.webapi.client.messagenotice.request.ReadMessageNoticeRequestModel;
import com.logistics.management.webapi.client.messagenotice.request.SearchMessageNoticeListRequestModel;
import com.logistics.management.webapi.client.messagenotice.response.SearchMessageNoticeListResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/6/5 16:15
 */
@Component
public class MessageNoticeClientHystrix implements MessageNoticeClient {
    @Override
    public Result<PageInfo<SearchMessageNoticeListResponseModel>> searchList(SearchMessageNoticeListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> readMessageNotice(ReadMessageNoticeRequestModel requestModel) {
        return Result.timeout();
    }
}
