package com.logistics.tms.config.rdelayqueue.spring;


import com.logistics.tms.config.rdelayqueue.core.RDelayQueue;
import com.logistics.tms.config.rdelayqueue.core.RDelayQueueHandler;
import com.logistics.tms.config.rdelayqueue.core.RDelayQueueRouting;
import com.yelo.tray.core.utils.YeloThreadPoolExecutor;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

import java.util.List;
import java.util.concurrent.*;

@EnableConfigurationProperties(RDelayProperties.class)
@ConditionalOnProperty(name = "delayqueue.queueName", matchIfMissing = false)
public class RDelayQueueAutoConfig {

    @Bean
    public RDelayQueue rBlockingDeque(RedissonClient redissonClient, RDelayProperties rDelayProperties) {
        return new RDelayQueue(redissonClient, rDelayProperties.getQueueName());
    }

    @Bean(name = "relayQueueExecutor")
    public Executor getExecutor() {
        //当线程池已满时，这个拒绝策略会让任务在主线程中执行
        return new YeloThreadPoolExecutor(20
                , 50
                , 60
                , TimeUnit.SECONDS
                , new ArrayBlockingQueue<>(20000)
                , new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(initMethod = "init")
    public RDelayQueueRouting rDelayQueueRouting(RDelayQueue rDelayQueue
            , List<RDelayQueueHandler> rDelayQueueHandlers
            , @Qualifier("relayQueueExecutor") Executor executor) {
        return new RDelayQueueRouting(rDelayQueue, rDelayQueueHandlers, executor);
    }

}
