package com.logistics.management.webapi.api.feign.attendance.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 打卡变更申请详情请求dto
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/15
 */
@Data
public class AttendanceChangeDetailRequestDto {

	@ApiModelProperty(value = "打卡变更申请ID", required = true)
	@NotBlank(message = "请选择要查看的记录")
	private String attendanceChangeApplyId;
}
