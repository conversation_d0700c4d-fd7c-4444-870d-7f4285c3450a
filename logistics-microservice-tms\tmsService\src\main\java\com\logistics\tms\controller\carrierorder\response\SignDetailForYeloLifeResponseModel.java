package com.logistics.tms.controller.carrierorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/24
 */
@Data
public class SignDetailForYeloLifeResponseModel {

	@ApiModelProperty("运单ID")
	private Long carrierOrderId;

	@ApiModelProperty("运单状态 10000 待到达提货地 20000待提货 30000待到达卸货地 40000 待卸货 50000 待签收 60000 已签收")
	private Integer status;

	@ApiModelProperty("运单号")
	private String carrierOrderCode;

	@ApiModelProperty("车辆")
	private String vehicleNo;

	@ApiModelProperty("司机姓名")
	private String driverName;

	@ApiModelProperty("司机手机号")
	private String driverMobile;

	@ApiModelProperty("提货地址信息")
	private String loadProvinceName;
	private String loadCityName;
	private String loadAreaName;
	private String loadDetailAddress;
	private String loadWarehouse;

	@ApiModelProperty("发货人")
	private String consignorName;

	@ApiModelProperty("发货人手机号")
	private String consignorMobile;

	@ApiModelProperty("卸货地址信息")
	private String unloadProvinceName;
	private String unloadCityName;
	private String unloadAreaName;
	private String unloadDetailAddress;
	private String unloadWarehouse;

	@ApiModelProperty("收货人")
	private String receiverName;

	@ApiModelProperty("收货人手机号")
	private String receiverMobile;

	@ApiModelProperty("业务类型：1 公司，2 个人")
	private Integer businessType;

	@ApiModelProperty("司机运费价格类型 1 单价 2 一口价")
	private Integer dispatchFreightFeeType;

	@ApiModelProperty("司机运费")
	private BigDecimal dispatchFreightFee;

	@ApiModelProperty("卸货数量")
	private BigDecimal unloadAmount;

	@ApiModelProperty("是否取消 0 否 1 是")
	private Integer ifCancel;

	@ApiModelProperty("货物单位：1 件，2 吨")
	private Integer goodsUnit;

	@ApiModelProperty("货物列表")
	private List<CarrierOrderGoodsResponseModel> goodsList;

	@ApiModelProperty("预提数量")
	private BigDecimal expectAmount;
	@ApiModelProperty("装货数量")
	private BigDecimal loadAmount;

	@ApiModelProperty("车主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
	private Integer carrierSettlement;
	@ApiModelProperty("货主结算吨位：1 实际提货吨位，2 实际卸货吨位，3 实际签收吨位，4 委托吨位")
	private Integer settlementTonnage;

	@ApiModelProperty("车主价格：1 单价，2 一口价")
	private Integer carrierPriceType;
	@ApiModelProperty("车主价格")
	private BigDecimal carrierPrice;
	@ApiModelProperty("货主价格：1 单价，2 一口价")
	private Integer entrustFreightType;
	@ApiModelProperty("货主价格")
	private BigDecimal entrustFreight;

	@ApiModelProperty("委托类型：100 新生回收，101 新生销售")
	private Integer entrustType;

	@ApiModelProperty(value = "是否我司 1:我司,2:其他车主")
	private Integer isOurCompany;

	@ApiModelProperty(value = "车主id")
	private Long companyCarrierId;

}
