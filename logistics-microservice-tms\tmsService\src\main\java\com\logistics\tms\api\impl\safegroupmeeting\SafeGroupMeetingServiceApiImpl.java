package com.logistics.tms.api.impl.safegroupmeeting;

import com.yelo.tray.core.base.Result;
import com.logistics.tms.api.feign.safegroupmeeting.SafeGroupMeetingServiceApi;
import com.logistics.tms.api.feign.safegroupmeeting.model.*;
import com.logistics.tms.biz.safegroupmeeting.SafeGroupMeetingBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * @author: MI<PERSON>a
 * @date: 2021/04/09
 */
@RestController
@Slf4j
public class SafeGroupMeetingServiceApiImpl implements SafeGroupMeetingServiceApi {

    @Autowired
    private SafeGroupMeetingBiz safeGroupMeetingBiz;

    /**
     * 新建安全小组领导会议
     * @param requestModel
     * @return
     */
    @Override
    public Result<Boolean> addSafeGroupMeeting(@RequestBody AddSafeGroupMeetingRequestModel requestModel) {
        safeGroupMeetingBiz.addSafeGroupMeeting(requestModel);
        return Result.success(true);
    }

    /**
     * 安全小组领导会议记录列表
     * @param requestModel
     * @return
     */
    @Override
    public Result<List<SafeGroupMeetingResponseModel>> safeGroupMeetingData(@RequestBody SafeGroupMeetingRequestModel requestModel) {
        return Result.success(safeGroupMeetingBiz.safeGroupMeetingData(requestModel));
    }

    /**
     * 安全小组领导会议详情
     * @param requestModel
     * @return
     */
    @Override
    public Result<SafeGroupMeetingDetailResponseModel> safeGroupMeetingDetail(@RequestBody SafeGroupMeetingDetailRequestModel requestModel) {
        return Result.success(safeGroupMeetingBiz.safeGroupMeetingDetail(requestModel));
    }
}
