package com.logistics.tms.controller.renewableaudit.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RenewableAuditListStatisticsResponseModel {

    @ApiModelProperty("全部")
    private String allCount;
    @ApiModelProperty("待指派")
    private String waitAssignedCount;
    @ApiModelProperty("待确认")
    private String waitConfirmCount;
    @ApiModelProperty("待审核")
    private String waitAuditCount;
    @ApiModelProperty("已审核")
    private String alreadyAuditCount;
    @ApiModelProperty("已取消")
    private String alreadyCancelCount;
}
