package com.logistics.management.webapi.controller.routeenquiry.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @author: wjf
 * @date: 2024/7/9 10:34
 */
@Data
@ExcelIgnoreUnannotated
public class SearchRouteEnquirySummaryListResponseDto {

    /**
     * 路线询价单地址表id
     */
    private String routeEnquiryAddressId="";

    /**
     * 竞价单号
     */
    @ExcelProperty("竞价单号")
    private String orderCode="";

    /**
     * 发货地
     */
    @ExcelProperty("发货地")
    private String loadAddress="";

    /**
     * 收货地
     */
    @ExcelProperty("卸货地")
    private String unloadAddress="";

    /**
     * 运距
     */
    @ExcelProperty("运距（公里）")
    private String distance="";

    /**
     * 货物名称
     */
    @ExcelProperty("货物名称")
    private String goodsName="";

    /**
     * 结算价
     */
    @ExcelProperty("结算价（含税）")
    private String quotePrice="";

    /**
     * 结算模式
     */
    @ExcelProperty("结算模式")
    private String quotePriceTypeLabel="";

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark="";

    /**
     * 中标承运商
     */
    @ExcelProperty("中标承运商")
    private String companyCarrierName="";

    /**
     * 报价生效开始时间
     */
    @ExcelProperty("报价生效开始")
    private String quoteStartTime="";

    /**
     * 报价生效结束时间
     */
    @ExcelProperty("报价生效截止")
    private String quoteEndTime="";

    /**
     * 关联合同号
     */
    @ExcelProperty("关联合同号")
    private String contractCode="";

    /**
     * 创建人
     */
    @ExcelProperty("创建人")
    private String createdBy="";

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private String createdTime="";

}
