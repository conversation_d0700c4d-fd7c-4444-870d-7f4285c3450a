package com.logistics.tms.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 *
 */
@Getter
@AllArgsConstructor
public enum WorkOrderProcessDescEnum {

    DEFAULT(-1, ""),
    CREATE(0, "司机%s到达现场，并提报工单，请及时处理"),
    BEING_PROCESSED_CENTER(10, "工作人员已经开始处理，请稍后"),
    PROCESSED_CENTER(20, "工单已经关闭，请继续运输"),
    CLOSED_CENTER(30, "工单已经关闭，请继续运输"),
    RESCINDED(40, "工单已经关闭，请继续运输"),
    RESUBMIT(50, "工单重新提交，请及时处理"),
    ;

    private final Integer key;
    private final String value;

    public String getValue(boolean isArriveScene) {
        if (this.equals(CREATE)) {
            return String.format(value, isArriveScene ? "已" : "未");
        }
        return value;
    }

    public static WorkOrderProcessDescEnum getEnumByKey(Integer key) {
        return Stream.of(values())
                .filter(f -> f.getKey().equals(key))
                .findFirst()
                .orElse(DEFAULT);
    }

    public static String getDesc(Integer status, boolean isArriveScene) {
        return getEnumByKey(status).getValue(isArriveScene);
    }

    public static String getDesc(Integer status) {
        return getDesc(status, false);
    }
}
