package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.common.model.CertificatePictureModel;
import com.logistics.tms.biz.vehicleassetmanagement.model.SearchCertificationPicturesModel;
import com.logistics.tms.api.feign.violationregulation.model.UpdateCertificationPicturesModel;
import com.logistics.tms.entity.TCertificationPictures;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TCertificationPicturesMapper extends BaseMapper<TCertificationPictures> {

    List<TCertificationPictures> getByObjectIdType(@Param("objectId") Long objectId, @Param("objectType") Integer objectType, @Param("fileType") Integer fileType);

    /**
     * 根据objectId和类型查询图片
     *
     * @param objectId 图片外键id
     * @param objectType 图片外键类型
     * @return
     */
    List<TCertificationPictures> getByObjectIdAndType(@Param("objectId") Long objectId, @Param("objectType") Integer objectType);

    int batchInsert(@Param("list") List<TCertificationPictures> list);

    int batchUpdate(@Param("list") List<TCertificationPictures> list);

    int updateFilePath(@Param("params") UpdateCertificationPicturesModel updateModel);

    List<CertificatePictureModel> getPicsByIdsAndType(@Param("objectIds") String objectIds, @Param("objectType") Integer objectType, @Param("fileType") Integer fileType);

    List<TCertificationPictures> getImageByIdsAndType(@Param("objectIds") String objectIds, @Param("objectType") Integer objectType);

    List<TCertificationPictures> getTPicsByIds(@Param("objectIds") String objectIds, @Param("objectTypes") String objectTypes);

    List<TCertificationPictures> getUnionFiles(@Param("list") List<SearchCertificationPicturesModel> list);

    int delByObjectTypeId(@Param("objectType") Integer objectType, @Param("objectId") Long objectId,@Param("operatorName") String operatorName);

    int delByObjectTypeFileTypeId(@Param("objectType") Integer objectType,@Param("fileType") Integer fileType, @Param("objectId") Long objectId,@Param("operatorName") String operatorName);

    int delByObjectTypeObjectIds(@Param("objectType") Integer objectType, @Param("objectIds") String objectIds,@Param("operatorName") String operatorName);
}