package com.logistics.management.webapi.base.enums;

/**
 * <AUTHOR>
 * @createDate 2018-07-29 19:15
 */
public enum EnabledEnum {
    DEFAULT(-1, ""),
    DISABLED(0, "禁用"),
    ENABLED(1, "启用"),;

    private Integer key;
    private String value;

    EnabledEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static EnabledEnum getEnum(Integer key) {
        for (EnabledEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
