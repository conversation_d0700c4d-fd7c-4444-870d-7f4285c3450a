package com.logistics.management.webapi.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RenewableDemandOrderGoodsResponseDto {

    @ApiModelProperty("货物信息id")
    private String demandOrderGoodsId="";

    @ApiModelProperty("品名")
    private String goodsName="";

    @ApiModelProperty("规格")
    private String goodsSize="";

    @ApiModelProperty("委托")
    private String goodsAmountNumber="";

    @ApiModelProperty("已安排")
    private String arrangedAmountNumber="";

    @ApiModelProperty("未安排")
    private String notArrangedAmountNumber="";

    @ApiModelProperty("退回")
    private String backAmountNumber="";
}
