package com.logistics.management.webapi.controller.routeenquiry.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/9 10:28
 */
@Data
public class RouteEnquiryArchiveRequestDto {

    /**
     * 路线询价单表id
     */
    @NotBlank(message = "id不能为空")
    private String routeEnquiryId;

    /**
     * 归档文件
     */
    @Size(max = 6, message = "归档文件支持上传最多6张")
    private List<String> fileList;

}
