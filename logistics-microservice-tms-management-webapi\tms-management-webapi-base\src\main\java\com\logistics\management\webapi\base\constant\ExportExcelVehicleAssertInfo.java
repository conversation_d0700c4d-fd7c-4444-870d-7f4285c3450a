package com.logistics.management.webapi.base.constant;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: sj
 * @Date: 2019/7/11 19:51
 */
public class ExportExcelVehicleAssertInfo {
    private ExportExcelVehicleAssertInfo() {

    }

    private static final Map<String, String> VEHICLE_ASSET_INFO;

    static {
        VEHICLE_ASSET_INFO = new LinkedHashMap<>();
        VEHICLE_ASSET_INFO.put("车辆使用性质","usagePropertyLabel");
        VEHICLE_ASSET_INFO.put("是否安装GPS","ifInstallGpsLabel");
        VEHICLE_ASSET_INFO.put("是否入网石化","ifAccessSinopecLabel");
        VEHICLE_ASSET_INFO.put("车辆机构","vehiclePropertyLabel");
        VEHICLE_ASSET_INFO.put("真实所属车主","vehicleOwner");
        VEHICLE_ASSET_INFO.put("车牌号","vehicleNo");
        VEHICLE_ASSET_INFO.put("关联车主","companyCarrierName");
        VEHICLE_ASSET_INFO.put("车辆类型","vehicleTypeLabel");
        VEHICLE_ASSET_INFO.put("所有人","owner");
        VEHICLE_ASSET_INFO.put("住址","address");
        VEHICLE_ASSET_INFO.put("品牌","brand");
        VEHICLE_ASSET_INFO.put("型号","model");
        VEHICLE_ASSET_INFO.put("车辆识别号","vehicleIdentificationNumber");
        VEHICLE_ASSET_INFO.put("发动机号码","engineNumber");
        VEHICLE_ASSET_INFO.put("发证部门","drivingCertificationDepartmentOne");
        VEHICLE_ASSET_INFO.put("注册日期","registrationDate");
        VEHICLE_ASSET_INFO.put("发证日期","drivingIssueDate");
        VEHICLE_ASSET_INFO.put("归档编号","filingNumber");
        VEHICLE_ASSET_INFO.put("核定载人数（人）","authorizedCarryingCapacity");
        VEHICLE_ASSET_INFO.put("总质量(KG)","totalWeight");
        VEHICLE_ASSET_INFO.put("整备质量(KG)","curbWeight");
        VEHICLE_ASSET_INFO.put("核定载质量(KG)","approvedLoadWeight");
        VEHICLE_ASSET_INFO.put("准牵引总质量(KG)","tractionMassWeight");
        VEHICLE_ASSET_INFO.put("车外廓长（mm）","length");
        VEHICLE_ASSET_INFO.put("车外廓宽（mm）","width");
        VEHICLE_ASSET_INFO.put("车外廓高（mm）","height");
        VEHICLE_ASSET_INFO.put("车辆强制报废期","obsolescenceDate");
        VEHICLE_ASSET_INFO.put("车辆轴数","axleNumber");
        VEHICLE_ASSET_INFO.put("驱动轴数","driveShaftNumber");
        VEHICLE_ASSET_INFO.put("轮胎数","tiresNumber");
        VEHICLE_ASSET_INFO.put("车牌颜色","plateColor");
        VEHICLE_ASSET_INFO.put("车身颜色","bodyColor");
        VEHICLE_ASSET_INFO.put("行驶证检查有效期","checkVehicleValidDate");
        VEHICLE_ASSET_INFO.put("道路运输证发证签","certificationSign");
        VEHICLE_ASSET_INFO.put("道路运输证经营许可证号","businessLicenseNumber");
        VEHICLE_ASSET_INFO.put("道路运输证经济类型","economicType");
        VEHICLE_ASSET_INFO.put("道路运输证吨（坐）位","transportTonnage");
        VEHICLE_ASSET_INFO.put("道路运输证经营范围","businessScope");
        VEHICLE_ASSET_INFO.put("道路运输证发证部门","roadTransportCertificationDepartment");
        VEHICLE_ASSET_INFO.put("道路运输证发证日期","issueDate");
        VEHICLE_ASSET_INFO.put("道路运输证初领日期","obtainDate");
        VEHICLE_ASSET_INFO.put("车辆年审有效期","checkRoadValidDate");
        VEHICLE_ASSET_INFO.put("认证开始时间","authenticationStartTime");
        VEHICLE_ASSET_INFO.put("认证截止时间","authenticationExpireTime");
        VEHICLE_ASSET_INFO.put("GPS安装日期","installTime");
        VEHICLE_ASSET_INFO.put("终端型号","terminalType");
        VEHICLE_ASSET_INFO.put("GPS终端SIM卡号","simNumber");
        VEHICLE_ASSET_INFO.put("GPS服务商名称","gpsServiceProvider");
        VEHICLE_ASSET_INFO.put("登记证书编号","registrationCertificationNumber");
        VEHICLE_ASSET_INFO.put("等级评定检查日期","estimationDate");
        VEHICLE_ASSET_INFO.put("排放标准","emissionStandard");
    }

    public static Map<String, String> getVehicleAssetInfo() {
        return VEHICLE_ASSET_INFO;
    }
}
