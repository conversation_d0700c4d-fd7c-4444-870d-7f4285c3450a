<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderGoodsMapper" >
    <insert id="batchInsertSelective" parameterType="com.logistics.tms.entity.TCarrierOrderGoods" >
        <foreach collection="list" separator=";" item="item">
            insert into t_carrier_order_goods
            <trim prefix="(" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    id,
                </if>
                <if test="item.carrierOrderId != null" >
                    carrier_order_id,
                </if>
                <if test="item.demandOrderGoodsId != null" >
                    demand_order_goods_id,
                </if>
                <if test="item.skuCode != null">
                    sku_code,
                </if>
                <if test="item.goodsName != null" >
                    goods_name,
                </if>
                <if test="item.categoryName != null" >
                    category_name,
                </if>
                <if test="item.length != null" >
                    length,
                </if>
                <if test="item.width != null" >
                    width,
                </if>
                <if test="item.height != null" >
                    height,
                </if>
                <if test="item.goodsSize != null">
                    goods_size,
                </if>
                <if test="item.expectAmount != null" >
                    expect_amount,
                </if>
                <if test="item.loadAmount != null" >
                    load_amount,
                </if>
                <if test="item.unloadAmount != null" >
                    unload_amount,
                </if>
                <if test="item.signAmount != null" >
                    sign_amount,
                </if>
                <if test="item.createdBy != null" >
                    created_by,
                </if>
                <if test="item.createdTime != null" >
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time,
                </if>
                <if test="item.valid != null" >
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides="," >
                <if test="item.id != null" >
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderId != null" >
                    #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderGoodsId != null" >
                    #{item.demandOrderGoodsId,jdbcType=BIGINT},
                </if>
                <if test="item.skuCode != null">
                    #{item.skuCode,jdbcType=VARCHAR},
                </if>
                <if test="item.goodsName != null" >
                    #{item.goodsName,jdbcType=VARCHAR},
                </if>
                <if test="item.categoryName != null" >
                    #{item.categoryName,jdbcType=VARCHAR},
                </if>
                <if test="item.length != null" >
                    #{item.length,jdbcType=INTEGER},
                </if>
                <if test="item.width != null" >
                    #{item.width,jdbcType=INTEGER},
                </if>
                <if test="item.height != null" >
                    #{item.height,jdbcType=INTEGER},
                </if>
                <if test="item.goodsSize != null">
                    #{item.goodsSize,jdbcType=VARCHAR},
                </if>
                <if test="item.expectAmount != null" >
                    #{item.expectAmount,jdbcType=INTEGER},
                </if>
                <if test="item.loadAmount != null" >
                    #{item.loadAmount,jdbcType=INTEGER},
                </if>
                <if test="item.unloadAmount != null" >
                    #{item.unloadAmount,jdbcType=INTEGER},
                </if>
                <if test="item.signAmount != null" >
                    #{item.signAmount,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null" >
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="selectGoodsByCarrierOrderIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_carrier_order_goods
        where valid = 1 and carrier_order_id in (${carrierOrderIds})
    </select>

    <update id="batchUpdateCarrierOrderGoods">
        <foreach collection="carrierOrderGoodsList" separator=";" item="item">
            update t_carrier_order_goods
            <set >
                <if test="item.carrierOrderId != null" >
                    carrier_order_id = #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.demandOrderGoodsId != null" >
                    demand_order_goods_id = #{item.demandOrderGoodsId,jdbcType=BIGINT},
                </if>
                <if test="item.skuCode != null">
                    sku_code = #{item.skuCode,jdbcType=VARCHAR},
                </if>
                <if test="item.goodsName != null" >
                    goods_name = #{item.goodsName,jdbcType=VARCHAR},
                </if>
                <if test="item.categoryName != null" >
                    category_name = #{item.categoryName,jdbcType=VARCHAR},
                </if>
                <if test="item.length != null" >
                    length = #{item.length,jdbcType=INTEGER},
                </if>
                <if test="item.width != null" >
                    width = #{item.width,jdbcType=INTEGER},
                </if>
                <if test="item.height != null" >
                    height = #{item.height,jdbcType=INTEGER},
                </if>
                <if test="item.goodsSize != null">
                    goods_size = #{item.goodsSize,jdbcType=VARCHAR},
                </if>
                <if test="item.expectAmount != null" >
                    expect_amount = #{item.expectAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.loadAmount != null" >
                    load_amount = #{item.loadAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.unloadAmount != null" >
                    unload_amount = #{item.unloadAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.signAmount != null" >
                    sign_amount = #{item.signAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.createdBy != null" >
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null" >
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null" >
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null" >
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null" >
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    
    <select id="getByDispatchOrderId" resultType="com.logistics.tms.controller.dispatchorder.response.CarrierOrderGoodsResponseModel">
        select
        tco.id                                                                                                                         as carrierOrderId,
        tco.demand_order_id                                                                                                            as demandOrderId,
        tco.demand_order_code                                                                                                          as demandOrderCode,
        tco.customer_order_code                                                                                                        as customerOrderCode,
        tco.goods_unit                                                                                                                 as goodsUnit,
        tco.company_carrier_name                                                                                                       as companyCarrierName,
        tco.company_carrier_type                                                                                                       as companyCarrierType,
        tco.carrier_contact_name                                                                                                       as carrierContactName,
        AES_DECRYPT(UNHEX(tco.carrier_contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactPhone,
        tco.demand_order_entrust_type                                                                                                  as entrustType,
        tco.demand_order_source                                                                                                        as source,

        tcoa.load_province_name                                                                                                        as loadProvinceName,
        tcoa.load_city_name                                                                                                            as loadCityName,
        tcoa.load_area_name                                                                                                            as loadAreaName,
        tcoa.load_detail_address                                                                                                       as loadDetailAddress,
        tcoa.load_warehouse                                                                                                            as loadWarehouse,
        tcoa.unload_province_name                                                                                                      as unloadProvinceName,
        tcoa.unload_city_name                                                                                                          as unloadCityName,
        tcoa.unload_area_name                                                                                                          as unloadAreaName,
        tcoa.unload_detail_address                                                                                                     as unloadDetailAddress,
        tcoa.unload_warehouse                                                                                                          as unloadWarehouse,

        tcog.id                                                                                                                        as carrierOrderGoodsId,
        tcog.goods_name                                                                                                                as goodsName,
        tcog.length                                                                                                                    as length,
        tcog.width                                                                                                                     as width,
        tcog.height                                                                                                                    as height,
        tcog.goods_size                                                                                                                as goodsSize,
        tcog.expect_amount                                                                                                             as expectAmount

        from t_carrier_order_goods tcog
        left join t_carrier_order tco on tco.id = tcog.carrier_order_id and tco.valid = 1
        left join t_carrier_order_address tcoa on tcoa.carrier_order_id = tco.id and tcoa.valid = 1
        where tcog.valid = 1
        and tco.dispatch_order_id = #{dispatchOrderId,jdbcType=BIGINT}
    </select>

    <select id="selectGoodsByCarrierOrderId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_carrier_order_goods
        where valid = 1 and  carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
    </select>
</mapper>