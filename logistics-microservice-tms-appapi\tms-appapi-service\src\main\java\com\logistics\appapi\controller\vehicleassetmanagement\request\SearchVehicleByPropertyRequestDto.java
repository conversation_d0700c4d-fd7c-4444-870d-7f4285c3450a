package com.logistics.appapi.controller.vehicleassetmanagement.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: wjf
 * @date: 2022/8/3 9:53
 */
@Data
public class SearchVehicleByPropertyRequestDto extends AbstractPageForm<SearchVehicleByPropertyRequestDto> {

    @ApiModelProperty(value = "车辆机构：车辆机构：1 自主，2 外部，3 自营（多个用逗号分隔）", required = true)
    @NotBlank(message = "车辆机构不能为空")
    private String vehicleProperty;

    @ApiModelProperty("车辆类别 1 牵引车 2 挂车 3 一体车,多个类型拼接传递 如: '1,2'")
    private String vehicleCategory;

    @ApiModelProperty(value = "车牌号", required = true)
    @NotBlank(message = "请输入车牌号")
    private String vehicleNo;
}
