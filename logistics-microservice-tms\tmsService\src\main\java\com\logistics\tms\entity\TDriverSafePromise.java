package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TDriverSafePromise extends BaseEntity {
    /**
    * 标题
    */
    @ApiModelProperty("标题")
    private String title;

    /**
    * 周期（年份）
    */
    @ApiModelProperty("周期（年份）")
    private String period;

    /**
    * 经办人
    */
    @ApiModelProperty("经办人")
    private String agent;

    /**
    * 附件
    */
    @ApiModelProperty("附件")
    private String attachmentUrl;

    /**
    * 内容
    */
    @ApiModelProperty("内容")
    private String content;
}