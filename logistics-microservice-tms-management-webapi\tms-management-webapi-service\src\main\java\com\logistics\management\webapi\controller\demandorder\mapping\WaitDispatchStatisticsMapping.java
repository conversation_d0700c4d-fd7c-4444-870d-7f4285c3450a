package com.logistics.management.webapi.controller.demandorder.mapping;


import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.client.demandorder.response.WaitDispatchStatisticsModel;
import com.logistics.management.webapi.client.demandorder.response.WaitDispatchStatisticsResponseModel;
import com.logistics.management.webapi.controller.demandorder.response.WaitDispatchStatisticsDto;
import com.logistics.management.webapi.controller.demandorder.response.WaitDispatchStatisticsResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tools.utils.MapUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 计算 待调度 待发布 每组所占百分比
 *
 * @author: wei.wang
 * @date: 2022/1/5
 */
public class WaitDispatchStatisticsMapping extends MapperMapping<WaitDispatchStatisticsResponseModel, WaitDispatchStatisticsResponseDto> {
    @Override
    public void configure() {
        WaitDispatchStatisticsResponseModel source = this.getSource();
        WaitDispatchStatisticsResponseDto destination = this.getDestination();

        //系统时间
        LocalDate systemDate = LocalDate.now();
        //待调度需求单list
        List<WaitDispatchStatisticsModel> waitDispatchList = source.getWaitDispatchList();
        if (ListUtils.isNotEmpty(waitDispatchList)) {
            Integer totalCount = waitDispatchList.size();
            //待调度,按指定天分组
            Map<Integer, List<WaitDispatchStatisticsModel>> dispatchDaysGroup = waitDispatchStatisticsGroupedByDay(waitDispatchList, systemDate);
            //待调度响应数据拼接
            List<WaitDispatchStatisticsDto> waitDispatchStatisticsList = assembleWaitDispatchStatistics(dispatchDaysGroup, BigDecimal.valueOf(totalCount));
            destination.setWaitDispatchTotal(String.valueOf(totalCount));
            destination.setWaitDispatchList(waitDispatchStatisticsList);
        }

        //待发布需求单list
        List<WaitDispatchStatisticsModel> waitPublishList = source.getWaitPublishList();
        if (ListUtils.isNotEmpty(waitPublishList)) {
            Integer totalCount = waitPublishList.size();
            //待发布,按指定天分组
            Map<Integer, List<WaitDispatchStatisticsModel>> publishDaysGroup = waitDispatchStatisticsGroupedByDay(waitPublishList, systemDate);
            //待发布响应数据拼接
            List<WaitDispatchStatisticsDto> waitPublishStatisticsList = assembleWaitDispatchStatistics(publishDaysGroup, BigDecimal.valueOf(waitPublishList.size()));
            destination.setWaitPublishTotal(String.valueOf(totalCount));
            destination.setWaitPublishList(waitPublishStatisticsList);
        }
    }

    /**
     * 组装待发布待调度响应数据
     *
     * @param waitDispatchGroup 按天分组后的map
     * @param totalOrderCount   总单数
     * @return 组装后的数据
     */
    private List<WaitDispatchStatisticsDto> assembleWaitDispatchStatistics(Map<Integer, List<WaitDispatchStatisticsModel>> waitDispatchGroup, BigDecimal totalOrderCount) {
        List<WaitDispatchStatisticsDto> waitDispatchStatisticsModelList = new ArrayList<>();

        if (!MapUtils.isEmpty(waitDispatchGroup)) {
            //100
            BigDecimal oneHundred = new BigDecimal(CommonConstant.INTEGER_ONE_HUNDRED);
            WaitDispatchStatisticsDto waitDispatchStatisticsDto;
            //1是0-3天，2是4-10天，3是11-20天，4是21-31天，5是大于31天
            for (Integer i = 1; i <= CommonConstant.INTEGER_FIVE; i++) {
                //每组的单数
                BigDecimal orderCountInner = BigDecimal.ZERO;
                //每组货物数
                BigDecimal orderAmount = BigDecimal.ZERO;
                List<WaitDispatchStatisticsModel> statisticsModelList = waitDispatchGroup.get(i);
                if (ListUtils.isNotEmpty(statisticsModelList)) {
                    for (WaitDispatchStatisticsModel waitPublishDispatchModel : statisticsModelList) {
                        //累加货物数量
                        orderAmount = orderAmount.add(waitPublishDispatchModel.getGoodsAmount());
                        //累加单量
                        orderCountInner = orderCountInner.add(BigDecimal.ONE);
                    }
                }
                waitDispatchStatisticsDto = new WaitDispatchStatisticsDto();
                waitDispatchStatisticsDto.setDayType(String.valueOf(i));
                //每组总单数
                waitDispatchStatisticsDto.setOrderCount(orderCountInner.toString());
                //每组货物总数
                waitDispatchStatisticsDto.setOrderAmount(orderAmount.stripTrailingZeros().toPlainString());
                //每组单数占比 保留两位小数
                waitDispatchStatisticsDto.setProportion(((orderCountInner.divide(totalOrderCount, 4, RoundingMode.HALF_UP)).multiply(oneHundred)).setScale(2, RoundingMode.HALF_UP).toString());
                waitDispatchStatisticsModelList.add(waitDispatchStatisticsDto);
            }
        }
        return waitDispatchStatisticsModelList;
    }

    /**
     * 把待发布待调度的数据按天分组 统计类型：1是0-3天，2是4-10天，3是11-20天，4是21-31天，5是大于31天
     *
     * @param waitPublishDispatchModels 按照需求单状态分组后的集合
     * @param systemDate                系统时间
     * @return 分组后的数据
     */
    private Map<Integer, List<WaitDispatchStatisticsModel>> waitDispatchStatisticsGroupedByDay(List<WaitDispatchStatisticsModel> waitPublishDispatchModels, LocalDate systemDate) {
        return waitPublishDispatchModels.stream().collect(Collectors.groupingBy(waitPublishDispatchModel -> {
            //需求单发布时间
            LocalDate publishTime = waitPublishDispatchModel.getPublishTime();
            //获取当前时间和发布时间相差天数
            long diffDays = systemDate.toEpochDay() - publishTime.toEpochDay();

            //0-3天的组
            if (diffDays >= CommonConstant.INTEGER_ZERO && diffDays <= CommonConstant.INTEGER_THREE) {
                return CommonConstant.INTEGER_ONE;
            } else if (diffDays >= CommonConstant.INTEGER_FOUR && diffDays <= CommonConstant.INT_TEN) {//4-10天的组
                return CommonConstant.INTEGER_TWO;
            } else if (diffDays >= CommonConstant.INT_ELEVEN && diffDays <= CommonConstant.INT_TWENTY) {//11-20天的组
                return CommonConstant.INTEGER_THREE;
            } else if (diffDays >= CommonConstant.INT_TWENTY_ONE && diffDays <= CommonConstant.INT_THIRTY_ONE) {//21-31天的组
                return CommonConstant.INTEGER_FOUR;
            } else if (diffDays > CommonConstant.INT_THIRTY_ONE) {//大于31天的组
                return CommonConstant.INTEGER_FIVE;
            }
            return CommonConstant.NEGATIVE_INTEGER_ONE;//不属于任何组的分到 -1组
        }));
    }
}
