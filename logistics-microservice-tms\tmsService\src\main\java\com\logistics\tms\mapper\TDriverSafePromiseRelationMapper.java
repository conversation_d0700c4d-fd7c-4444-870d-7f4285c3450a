package com.logistics.tms.mapper;

import com.logistics.tms.controller.driversafepromise.request.SearchSafePromiseAppletListRequestModel;
import com.logistics.tms.controller.driversafepromise.request.SearchSignSafePromiseListRequestModel;
import com.logistics.tms.controller.driversafepromise.response.*;
import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.entity.TDriverSafePromiseRelation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TDriverSafePromiseRelationMapper extends BaseMapper<TDriverSafePromiseRelation> {
    int batchInsert(@Param("list") List<TDriverSafePromiseRelation> list);

    List<SummarySavePromiseInfoModel> getSummaryBySafePromiseIds(@Param("safePromiseIds") String safePromiseIds);

    List<TDriverSafePromiseRelation> getBySafePromiseIdAndStatus(@Param("safePromiseId") Long safePromiseId,@Param("status") Integer status);

    List<SearchSignSafePromiseListResponseModel> searchSignList(@Param("params") SearchSignSafePromiseListRequestModel requestModel);

    SummarySignSafePromiseResponseModel getSummarySignCount(@Param("params") SearchSignSafePromiseListRequestModel responseModel);

    SummarySignSafePromiseAppletResponseModel getAppletSignSummary(@Param("params") SearchSafePromiseAppletListRequestModel responseModel);

    SafePromiseAppletDetailResponseModel getAppletDetail(@Param("id")Long id);

}