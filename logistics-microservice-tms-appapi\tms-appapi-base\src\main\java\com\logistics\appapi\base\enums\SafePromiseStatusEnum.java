package com.logistics.appapi.base.enums;

/**
 * @Author: sj
 * @Date: 2019/11/5 9:11
 */
public enum SafePromiseStatusEnum {
    DEFAULT(-99,""),
    WAIT(0, "待签订"),
    HAVE(1, "已签订"),
    ;

    private Integer key;
    private String value;

    SafePromiseStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static SafePromiseStatusEnum getEnum(Integer key) {
        for (SafePromiseStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
