package com.logistics.tms.controller.staffvehiclerelation.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: sj
 * @Date: 2019/7/26 13:18
 */
@Data
public class SearchStaffVehicleListResponseModel {

    @ApiModelProperty("车辆司机ID")
    private Long staffVehicleRelationId;

    @ApiModelProperty("类型 1 自主，2 外部，3 自营")
    private Integer type;

    @ApiModelProperty("车辆类别: 1 牵引车 2 挂车 3 一体车")
    private Integer vehicleCategory;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("型号")
    private String model;

    @ApiModelProperty("车辆类型")
    private String vehicleType;

    @ApiModelProperty("牵引车或一体车Id")
    private Long vehicleId;

    @ApiModelProperty("车牌号(牵引车)")
    private String tractorVehicleNo;

    @ApiModelProperty("挂车Id")
    private Long trailerVehicleId;

    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;

    @ApiModelProperty("最大限载量")
    private BigDecimal approvedLoadWeight;

    @ApiModelProperty("所有人")
    private String owner;

    @ApiModelProperty("司机名称")
    private String staffName;

    @ApiModelProperty("司机电话号码")
    private String staffPhoneNumber;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("操作时间")
    private Date lastModifiedTime;

    @ApiModelProperty("操作人")
    private String lastModifiedBy;

    @ApiModelProperty("总质量")
    private String totalWeight;

    @ApiModelProperty("装备质量")
    private String curbWeight;

    @ApiModelProperty("准牵引总质量")
    private String tractionMassWeight;

    @ApiModelProperty("排放标准类型：1 国一，2 国二，3 国三，4 国四，5 国五，6 国六")
    private Integer emissionStandardType;

    @ApiModelProperty("从业资格证号")
    private String occupationalRequirementsCredentialNo;

    @ApiModelProperty("身份证号")
    private String identityNumber;

    @ApiModelProperty("驾驶证号")
    private String driversLicenseNo;

    @ApiModelProperty("车主ID")
    private Long companyCarrierId;

    @ApiModelProperty("车主公司类型")
    private Integer companyCarrierType;

    @ApiModelProperty("车主公司名称")
    private String companyCarrierName;

    @ApiModelProperty("车主联系人")
    private String carrierContactName;

    @ApiModelProperty("车主联系人电话")
    private String carrierContactMobile;
}
