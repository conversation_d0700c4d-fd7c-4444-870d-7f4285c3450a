package com.logistics.tms.controller.vehicletype;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.biz.vehicletype.VehicleTypeBiz;
import com.logistics.tms.controller.vehicletype.request.*;
import com.logistics.tms.controller.vehicletype.response.GetVehicleTypeSearchByNameResponseModel;
import com.logistics.tms.controller.vehicletype.response.ImportVehicleTypeResponseModel;
import com.logistics.tms.controller.vehicletype.response.VehicleTypeDetailResponseModel;
import com.logistics.tms.controller.vehicletype.response.VehicleTypeListResponseModel;
import com.yelo.tray.core.annocation.IgnoreParamsLog;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: wjf
 * @date: 2023/10/26 11:16
 */
@RestController
@Api(value = "车辆类型管理")
@RequestMapping(value = "/service/vehicleType")
public class VehicleTypeController {

    @Resource
    private VehicleTypeBiz vehicleTypeBiz;

    /**
     * 模糊匹配已有的车辆类型
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "模糊匹配已有的车辆类型")
    @PostMapping(value = "/fuzzyVehicleType")
    public Result<List<GetVehicleTypeSearchByNameResponseModel>> fuzzyVehicleType(@RequestBody GetVehicleTypeSearchByNameRequestModel requestModel) {
        return Result.success(vehicleTypeBiz.fuzzyVehicleType(requestModel));
    }

    /**
     * 获取车辆类型列表
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "获取车辆类型列表")
    @PostMapping(value = "/searchVehicleTypeList")
    public Result<PageInfo<VehicleTypeListResponseModel>> searchVehicleTypeList(@RequestBody VehicleTypeListRequestModel requestModel) {
        return Result.success(vehicleTypeBiz.searchVehicleTypeList(requestModel.enablePaging()));
    }

    /**
     * 新增/修改车辆类型
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "新增/修改车辆类型")
    @PostMapping(value = "/addOrModifyVehicleType")
    public Result<Boolean> addOrModifyVehicleType(@RequestBody AddOrModifyVehicleTypeRequestModel requestModel) {
        vehicleTypeBiz.addOrModifyVehicleType(requestModel);
        return Result.success(true);
    }

    /**
     * 启用/禁用车辆类型
     * @param requestModel
     * @return
     */
    @ApiOperation(value ="启用/禁用车辆类型")
    @PostMapping(value="/enableOrDisable")
    public Result<Boolean> enableOrDisable(@RequestBody EnableVehicleTypeModel requestModel) {
        vehicleTypeBiz.enableOrDisable(requestModel);
        return Result.success(true);
    }

    /**
     * 查看详情
     * @param requestModel
     * @return
     */
    @ApiOperation(value = "查看详情")
    @PostMapping(value = "/getDetail")
    public Result<VehicleTypeDetailResponseModel> getDetail(@RequestBody VehicleTypeDetailRequestModel requestModel) {
        VehicleTypeDetailResponseModel detail=vehicleTypeBiz.getDetail(requestModel);
        return Result.success(detail);
    }

    /**
     * 导出车辆类型列表
     * @param requestModel
     * @return
     */
    @IgnoreParamsLog(type=IgnoreParamsLog.IgnoreLogType.RESPONSE)
    @ApiOperation(value = "导出车辆类型列表")
    @PostMapping(value = "/exportVehicleType")
    public Result<List<VehicleTypeListResponseModel>> exportVehicleType(@RequestBody VehicleTypeListRequestModel requestModel) {
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        PageInfo<VehicleTypeListResponseModel> pageInfo = vehicleTypeBiz.searchVehicleTypeList(requestModel);
        return Result.success(pageInfo.getList());
    }

    /**
     * 导入
     * @param requestModel
     * @return
     */
    @ApiOperation(value="导入")
    @PostMapping(value="/importVehicleType")
    public Result<ImportVehicleTypeResponseModel> importVehicleType(@RequestBody ImportVehicleTypeRequestModel requestModel) {
        return Result.success(vehicleTypeBiz.importVehicleType(requestModel));
    }
}
