package com.logistics.management.webapi.api.feign.dispatch.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DriverSearchResponseDto {
    @ApiModelProperty("司机id")
    private String driverId = "";
    @ApiModelProperty("司机名字")
    private String driverName = "";
    @ApiModelProperty("司机手机号")
    private String driverPhone = "";
    @ApiModelProperty("司机身份证")
    private String driverIdentityNumber = "";
   
}
