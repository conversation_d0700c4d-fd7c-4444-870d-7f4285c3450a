<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TCarrierOrderLoadCodeMapper" >

    <select id="selectIfProductCodeExist" parameterType="String" resultType="Long">
        select
        1
        from
        t_carrier_order_load_code tcolc
        left join t_carrier_order tco on tcolc.carrier_order_id = tco.id and tco.valid = 1
        where
        tcolc.valid = 1
        and tcolc.state = 1
        and tcolc.product_code = #{productCode}
        and tco.status not in(60000)
        and tco.if_cancel = 0
        and tco.if_empty = 0
        limit 1
    </select>


    <select id="listByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_carrier_order_load_code
        where valid = 1
        <if test="param.carrierOrderId != null">
            and carrier_order_id = #{param.carrierOrderId,jdbcType=BIGINT}
        </if>
        <if test="param.carrierOrderIds != null and param.carrierOrderIds.size() != 0">
            and carrier_order_id in
            <foreach collection="param.carrierOrderIds" open="(" close=")" item="id" separator=",">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="param.state != null">
            and state = #{param.state,jdbcType=INTEGER}
        </if>
        <if test="param.codes != null and param.codes.size() != 0">
            and product_code in
            <foreach collection="param.codes" open="(" close=")" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by id desc
    </select>

    <update id="batchUpdate" parameterType="com.logistics.tms.entity.TCarrierOrderLoadCode">
        <foreach collection="list" item="item" separator=";">
            update t_carrier_order_load_code
            <set>
                <if test="item.carrierOrderId != null">
                    carrier_order_id = #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.productCode != null">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.trayCustomerCompanyName != null">
                    tray_customer_company_name = #{item.trayCustomerCompanyName,jdbcType=VARCHAR},
                </if>
                <if test="item.state != null">
                    state = #{item.state,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="listCarrierOrderProductCode" resultType="com.logistics.tms.biz.carrierorder.model.CarrierOrderLoadProductCodeModel">
        select
        tco.carrier_order_code as carrierOrderCode,
        tcolc.product_code productCode,
        tcolc.state,
        tco.load_amount loadAmount
        from t_carrier_order_load_code tcolc
        left join  t_carrier_order tco on tcolc.carrier_order_id = tco.id and tco.valid = 1
        where tcolc.valid = 1
        <if test="param.carrierOrderIds != null and param.carrierOrderIds.size() != 0">
            and tcolc.carrier_order_id in
            <foreach collection="param.carrierOrderIds" open="(" close=")" item="id" separator=",">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="param.state != null">
            and tcolc.state = #{param.state,jdbcType=INTEGER}
        </if>
    </select>


</mapper>