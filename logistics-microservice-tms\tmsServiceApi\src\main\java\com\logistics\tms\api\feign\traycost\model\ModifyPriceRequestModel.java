package com.logistics.tms.api.feign.traycost.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/4/20 17:32
 */
@Data
public class ModifyPriceRequestModel {
    @ApiModelProperty(value = "托盘费用id")
    private Long trayCostId;
    @ApiModelProperty(value = "费用")
    private BigDecimal unitPrice;
    @ApiModelProperty(value = "生效时间")
    private Date startTime;
}
