<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TSettleStatementItemMapper">
    <sql id="Base_Column_List_Decrypt">
        id, settle_statement_id, carrier_order_id, carrier_order_code, settlement_amount,
        goods_unit, entrust_freight, other_fees, payable_fee,
        company_entrust_id, company_entrust_name,company_carrier_id, company_carrier_name,
        AES_DECRYPT(UNHEX(contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as contact_phone,
        contact_name, type, if_archive,archive_remark, archive_by, archive_time, created_by, created_time, last_modified_by,
        last_modified_time, valid
    </sql>

    <insert id="batchValuesInsert">
        insert into t_settle_statement_item (
        id,
        settle_statement_id,
        carrier_order_id,
        carrier_order_code,
        settlement_amount,
        goods_unit,
        entrust_freight,
        other_fees,
        payable_fee,
        company_entrust_id,
        company_entrust_name,
        company_carrier_id,
        company_carrier_name,
        contact_phone,
        contact_name,
        type,
        if_archive,
        archive_remark,
        created_by,
        created_time,
        last_modified_by,
        last_modified_time,
        valid)
        values
        <foreach collection="tSettleStatementItems" item="item" index="index" separator=",">
            (
            <choose>
                <when test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.settleStatementId != null">
                    #{item.settleStatementId,jdbcType=BIGINT},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.carrierOrderId != null">
                    #{item.carrierOrderId,jdbcType=BIGINT},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.carrierOrderCode != null">
                    #{item.carrierOrderCode,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.settlementAmount != null">
                    #{item.settlementAmount,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.goodsUnit != null">
                    #{item.goodsUnit,jdbcType=INTEGER},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.entrustFreight != null">
                    #{item.entrustFreight,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.otherFees != null">
                    #{item.otherFees,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.payableFee != null">
                    #{item.payableFee,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.companyEntrustId != null">
                    #{item.companyEntrustId,jdbcType=BIGINT},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.companyEntrustName != null">
                    #{item.companyEntrustName,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.companyCarrierId != null">
                    #{item.companyCarrierId,jdbcType=BIGINT},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.companyCarrierName != null">
                    #{item.companyCarrierName,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.contactPhone != null and item.contactPhone != ''">
                    HEX(AES_ENCRYPT(#{item.contactPhone,jdbcType=VARCHAR}, '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}')),
                </when>
                <when test="item.contactPhone == ''">
                    #{item.contactPhone,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.contactName != null">
                    #{item.contactName,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.type != null">
                    #{item.type,jdbcType=INTEGER},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.ifArchive != null">
                    #{item.ifArchive,jdbcType=INTEGER},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.archiveRemark != null">
                    #{item.archiveRemark,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </when>
                <otherwise>
                    default,
                </otherwise>
            </choose>
            <choose>
                <when test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER}
                </when>
                <otherwise>
                 default
                </otherwise>
            </choose>
            )
        </foreach>
    </insert>

    <update id="batchUpdate">
        <foreach collection="tSettleStatementItems" item="item" separator=";">
            update t_settle_statement_item
            <set>
                <if test="item.settleStatementId != null">
                    settle_statement_id = #{item.settleStatementId,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderId != null">
                    carrier_order_id = #{item.carrierOrderId,jdbcType=BIGINT},
                </if>
                <if test="item.carrierOrderCode != null">
                    carrier_order_code = #{item.carrierOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.settlementAmount != null">
                    settlement_amount = #{item.settlementAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.goodsUnit != null">
                    goods_unit = #{item.goodsUnit,jdbcType=INTEGER},
                </if>
                <if test="item.entrustFreight != null">
                    entrust_freight = #{item.entrustFreight,jdbcType=DECIMAL},
                </if>
                <if test="item.otherFees != null">
                    other_fees = #{item.otherFees,jdbcType=DECIMAL},
                </if>
                <if test="item.payableFee != null">
                    payable_fee = #{item.payableFee,jdbcType=DECIMAL},
                </if>
                <if test="item.companyEntrustId != null">
                    company_entrust_id = #{item.companyEntrustId,jdbcType=BIGINT},
                </if>
                <if test="item.companyEntrustName != null">
                    company_entrust_name = #{item.companyEntrustName,jdbcType=VARCHAR},
                </if>
                <if test="item.companyCarrierId != null">
                    company_carrier_id = #{item.companyCarrierId,jdbcType=BIGINT},
                </if>
                <if test="item.companyCarrierName != null">
                    company_carrier_name = #{item.companyCarrierName,jdbcType=VARCHAR},
                </if>
                <if test="item.contactPhone != null">
                    contact_phone = #{item.contactPhone,jdbcType=VARCHAR},
                </if>
                <if test="item.contactName != null">
                    contact_name = #{item.contactName,jdbcType=VARCHAR},
                </if>
                <if test="item.type != null">
                    type = #{item.type,jdbcType=INTEGER},
                </if>
                <if test="item.ifArchive != null">
                    if_archive = #{item.ifArchive,jdbcType=INTEGER},
                </if>
                <if test="item.archiveRemark != null">
                    archive_remark = #{item.archiveRemark,jdbcType=VARCHAR},
                </if>
                <if test="item.archiveBy != null">
                    archive_by = #{item.archiveBy,jdbcType=VARCHAR},
                </if>
                <if test="item.archiveTime != null">
                    archive_time = #{item.archiveTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by = #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time = #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    valid = #{item.valid,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="statementArchiveList" resultType="com.logistics.tms.controller.settlestatement.packaging.response.StatementArchiveListResponseModel">
        select
        id                 as settleStatementItemId,
        carrier_order_id   as carrierOrderId,
        carrier_order_code as carrierOrderCode,
        archive_remark     as archiveRemark,
        archive_by         as lastModifiedBy,
        archive_time       as lastModifiedTime
        from t_settle_statement_item
        where valid = 1
        and settle_statement_id = #{settleStatementId,jdbcType=BIGINT}
        and if_archive = 1
        order by archive_time desc, id desc
    </select>

    <resultMap id="statementArchiveDetail_Map" type="com.logistics.tms.controller.settlestatement.packaging.response.StatementArchiveDetailResponseModel">
        <id column="settleStatementItemId" property="settleStatementItemId" jdbcType="BIGINT"/>
        <result column="carrier_order_code" property="carrierOrderCode" jdbcType="VARCHAR"/>
        <result column="archive_remark" property="archiveRemark" jdbcType="VARCHAR"/>
        <collection property="archiveTicketList" column="settleStatementItemId" select="com.logistics.tms.mapper.TSettleStatementArchiveAttachmentMapper.archiveTicketPathByItemId"/>
    </resultMap>
    <select id="statementArchiveDetail" resultMap="statementArchiveDetail_Map">
        select
        tssi.id as settleStatementItemId,
        tssi.carrier_order_id,
        tssi.carrier_order_code,
        tssi.archive_remark
        from t_settle_statement_item tssi
        where tssi.valid = 1
        and tssi.id = #{id,jdbcType=BIGINT}
        and tssi.if_archive = 1
    </select>

    <select id="statementWaitArchiveList" resultType="com.logistics.tms.controller.settlestatement.packaging.response.StatementWaitArchiveListResponseModel">
        select
        id                 as settleStatementItemId,
        carrier_order_code as carrierOrderCode
        from t_settle_statement_item
        where valid = 1
        and settle_statement_id = #{params.settleStatementId,jdbcType=BIGINT}
        and instr(carrier_order_code, #{params.carrierOrderCode,jdbcType=VARCHAR})
        and if_archive = 0
        order by id desc
    </select>

    <select id="getBySettleStatementIds" resultType="com.logistics.tms.controller.settlestatement.packaging.response.CarrierSettleStatementItemModel">
        select
        tssi.id                                                                                                                 as settleStatementItemId,
        tssi.settle_statement_id                                                                                                as settleStatementId,
        tssi.settlement_amount                                                                                                  as settlementAmount,
        tssi.goods_unit                                                                                                         as goodsUnit,
        tssi.entrust_freight                                                                                                    as entrustFreight,
        tssi.other_fees                                                                                                         as otherFees,
        tssi.payable_fee                                                                                                        as payableFee,
        tssi.company_carrier_id                                                                                                 as companyCarrierId,
        tssi.company_carrier_name                                                                                               as companyCarrierName,
        tssi.type                                                                                                               as companyCarrierType,
        tssi.contact_name                                                                                                       as carrierContactName,
        AES_DECRYPT(UNHEX(tssi.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as carrierContactMobile
        from t_settle_statement_item tssi
        where tssi.valid = 1
        and tssi.settle_statement_id in (${settleStatementIds})
    </select>

    <select id="selectByStatementId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_settle_statement_item tssi
        where tssi.valid = 1
        and tssi.settle_statement_id = #{pid,jdbcType=BIGINT}
        <if test="companyCarrierId != null">
        and tssi.company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        </if>
        <if test="ids != null and ids != ''">
        and id in (${ids})
        </if>
    </select>

    <select id="getTopBySettleStatementId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_settle_statement_item
        where valid = 1
        and settle_statement_id = #{settleStatementId,jdbcType=BIGINT}
        order by id desc
        limit 1
    </select>

    <select id="carrierAssociationCarrierOrderCount" resultType="com.logistics.tms.controller.settlestatement.packaging.response.CarrierAssociationCarrierOrderResponseModel">
        select
        ifnull(count(id), 0)                                                       as carrierOrderAmount,
        ifnull(sum(entrust_freight), 0)                                            as entrustFreight,
        ifnull(sum(other_fees), 0)                                                 as otherFees,
        ifnull(sum(if(goods_unit = 1 or goods_unit = 3, settlement_amount, 0)), 0) as totalPackageSettlementAmount,
        ifnull(sum(if(goods_unit = 2, settlement_amount, 0)), 0)                   as totalWeightSettlementAmount,
        ifnull(sum(if(goods_unit = 4, settlement_amount, 0)), 0)                   as totalPieceSettlementAmount
        from t_settle_statement_item
        where valid = 1
        and settle_statement_id = #{settleStatementId,jdbcType=BIGINT}
    </select>

    <select id="carrierAssociationCarrierOrder" resultType="com.logistics.tms.controller.settlestatement.packaging.response.CarrierAssociationCarrierOrderItemModel">
        select
        tssi.id                                                                                                                 as settleStatementItemId,
        tssi.carrier_order_id                                                                                                   as carrierOrderId,
        tssi.carrier_order_code                                                                                                 as carrierOrderCode,
        tssi.company_entrust_name                                                                                               as companyEntrustName,
        tssi.type                                                                                                               as companyCarrierType,
        tssi.company_carrier_name                                                                                               as companyCarrierName,
        tssi.contact_name                                                                                                       as contactName,
        AES_DECRYPT(UNHEX(tssi.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as contactPhone,
        tssi.settlement_amount                                                                                                  as settlementAmount,
        tssi.goods_unit                                                                                                         as goodsUnit,
        tssi.entrust_freight                                                                                                    as entrustFreight,
        tssi.other_fees                                                                                                         as otherFees
        from t_settle_statement_item tssi
        where tssi.valid = 1
        and tssi.settle_statement_id = #{settleStatementId,jdbcType=BIGINT}
        order by tssi.last_modified_time desc, tssi.carrier_order_code desc
    </select>

    <select id="getCarrierOrderIdBySettleStatementId" parameterType="Long" resultType="Long">
        select carrier_order_id
        from t_settle_statement_item
        where settle_statement_id = #{settleStatementId,jdbcType = BIGINT}
        and valid = 1
    </select>

    <select id="selectByStatementItemIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Decrypt"/>
        from t_settle_statement_item
        where id in (${ids})
        and valid = 1
        <if test="companyCarrierId != null">
            and company_carrier_id = #{companyCarrierId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectValidByCarrierOrderId" resultMap="BaseResultMap">
        select
        tssi.id,
        tssi.settle_statement_id,
        tssi.carrier_order_id,
        tssi.carrier_order_code,
        tssi.settlement_amount,
        tssi.goods_unit,
        tssi.entrust_freight,
        tssi.other_fees,
        tssi.payable_fee,
        tssi.company_entrust_id,
        tssi.company_entrust_name,
        tssi.company_carrier_id,
        tssi.company_carrier_name,
        AES_DECRYPT(UNHEX(tssi.contact_phone), '${@com.leyi.auth.service.client.common.AuthCommonConstants@MYSQL_ENCRYPT_KEY}') as contact_phone,
        tssi.contact_name,
        tssi.type,
        tssi.if_archive,
        tssi.archive_remark,
        tssi.archive_by,
        tssi.archive_time,
        tssi.created_by,
        tssi.created_time,
        tssi.last_modified_by,
        tssi.last_modified_time,
        tssi.valid
        from t_settle_statement_item tssi
        left join t_settle_statement tss on tss.id = tssi.settle_statement_id and tss.valid = 1
        where tssi.valid = 1
          and tss.settle_statement_status > -2
          and tssi.carrier_order_id = #{carrierOrderId,jdbcType=BIGINT}
    </select>
</mapper>