package com.logistics.tms.base.enums;


public enum SinopecMessageLogTypeEnum {
    DEFAULT(-1,""),
    DEMANDORDER(1,"委托单"),
    ;

    private Integer key;
    private String value;

    SinopecMessageLogTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
    public static SinopecMessageLogTypeEnum getEnum(Integer key) {
        for (SinopecMessageLogTypeEnum t : values()) {
            if (t.getKey() .equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }


}
