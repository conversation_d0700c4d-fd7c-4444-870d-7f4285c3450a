/**
 * @author: wjf
 * @date: 2019/3/27 9:13
 */
package com.logistics.management.webapi.base.enums;

public enum SettlementReceivementStatusEnum {

    DEFAULT(-1, ""),
    NOT_RECEIVE(0, "未收款"),
    REVEICED(1, "已收款"),
    ;

    private Integer key;
    private String value;

    SettlementReceivementStatusEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static SettlementReceivementStatusEnum getEnum(Integer key) {
        for (SettlementReceivementStatusEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return DEFAULT;
    }
}
