package com.logistics.management.webapi.client.carrierorderticketsaudit.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CarrierOrderTicketsAuditStatusEnum {

    DEFAULT(-1, ""),
    WAIT_AUDIT(0, "待审核"),
    THROUGH_AUDIT(1, "已审核"),
    REJECT_AUDIT(2, "已驳回"),
    ;

    private final Integer key;
    private final String value;

    public static CarrierOrderTicketsAuditStatusEnum getEnumByKey(Integer key) {
        return Optional.ofNullable(key)
                .map(p -> {
                    return Stream.of(values())
                            .filter(f -> f.getKey().equals(key))
                            .findFirst()
                            .orElse(DEFAULT);
                })
                .orElse(DEFAULT);
    }
}
