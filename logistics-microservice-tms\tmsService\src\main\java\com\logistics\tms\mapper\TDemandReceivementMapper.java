package com.logistics.tms.mapper;

import com.yelo.tools.mybatis.mapper.BaseMapper;
import com.logistics.tms.api.feign.entrustsettlement.model.*;
import com.logistics.tms.entity.TDemandReceivement;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
@Mapper
public interface TDemandReceivementMapper extends BaseMapper<TDemandReceivement> {

    int batchInsert(@Param("list") List<TDemandReceivement> list);

    int batchUpdate(@Param("list") List<TDemandReceivement> list);

    int batchUpdateForTime(@Param("list") List<TDemandReceivement> list);

    List<Long> searchIdForEntrustSettlementList(@Param("params")EntrustSettlementListRequestModel requestModel);

    List<EntrustSettlementRowModel> entrustSettlementList(@Param("ids")String ids);

    EntrustSettlementListResponseModel entrustSettlementListCount(@Param("params") EntrustSettlementListRequestModel requestModel);

    List<GetSettlementDetailRowModel> getSettlementDetail(@Param("ids") String ids);

    List<TDemandReceivement> getByIds(@Param("ids") String ids);

    GetDetailResponseModel getDetailForUpdateCost(@Param("settlementId") Long settlementId);

    List<TDemandReceivement> getByDemandOrderIds(@Param("demandOrderIds") String demandOrderIds);

    TDemandReceivement getByDemandOrderId(@Param("demandOrderId") Long demandOrderId);

    void updateByDemandOrderId(TDemandReceivement tDemandReceivement);
}