package com.logistics.appapi.client.vehiclesafecheck.hystrix;

import com.github.pagehelper.PageInfo;
import com.logistics.appapi.client.vehiclesafecheck.VehicleSafeCheckClient;
import com.logistics.appapi.client.vehiclesafecheck.request.AppletAddWaitCheckRequestModel;
import com.logistics.appapi.client.vehiclesafecheck.request.AppletAddWaitReformRequestModel;
import com.logistics.appapi.client.vehiclesafecheck.request.AppletSafeCheckListRequestModel;
import com.logistics.appapi.client.vehiclesafecheck.request.SafeCheckDetailRequestModel;
import com.logistics.appapi.client.vehiclesafecheck.response.AppletSafeCheckListResponseModel;
import com.logistics.appapi.client.vehiclesafecheck.response.AppletSafeCheckSummaryResponseModel;
import com.logistics.appapi.client.vehiclesafecheck.response.SafeCheckDetailResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

/**
 * @author: wjf
 * @date: 2024/3/15 9:29
 */
@Component
public class VehicleSafeCheckClientHystrix implements VehicleSafeCheckClient {
    @Override
    public Result<PageInfo<AppletSafeCheckListResponseModel>> searchAppletList(AppletSafeCheckListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<AppletSafeCheckSummaryResponseModel> getAppletSummary(AppletSafeCheckListRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<SafeCheckDetailResponseModel> getDetail(SafeCheckDetailRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> submitWaitCheck(AppletAddWaitCheckRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> submitWaitReform(AppletAddWaitReformRequestModel requestModel) {
        return Result.timeout();
    }
}
