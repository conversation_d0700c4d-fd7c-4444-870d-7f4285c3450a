package com.logistics.management.webapi.controller.companycarrierauthorization.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/1/3
 */
@Data
public class CarrierAuthorizationDetailResponseDto {

	@ApiModelProperty("车主授权信息id")
	private String carrierAuthorizationId = "";

	@ApiModelProperty("车主名")
	private String companyCarrierName = "";

	@ApiModelProperty("车主公司类型, 1:企业 2:个人")
	private String companyCarrierType = "";

	@ApiModelProperty("车主公司类型展示文本")
	private String companyCarrierTypeLabel = "";

	@ApiModelProperty("是否归档, 0:否 1:是")
	private String isArchived = "";

	@ApiModelProperty("是否归档展示文本")
	private String isArchivedLabel = "";

	@ApiModelProperty("授权状态, 0:待授权 1:待审核 2:已驳回 3:已授权")
	private String authorizationStatus = "";

	@ApiModelProperty("备注")
	private String remark = "";

	@ApiModelProperty("创建人")
	private String createdBy = "";

	@ApiModelProperty("创建时间")
	private String createdTime = "";

	@ApiModelProperty("审核人")
	private String auditorName = "";

	@ApiModelProperty("审核时间")
	private String auditTime = "";

	@ApiModelProperty("归档人")
	private String archivedUsername = "";

	@ApiModelProperty("归档时间")
	private String archivedTime = "";

	@ApiModelProperty("授权书图片列表")
	private List<AuthorizationImage> authorizationImageList;

	@ApiModelProperty("归档文件; 1.3.6 新增")
	private AuthorizationImage archivedFile;

	@Data
	public static class AuthorizationImage {

		@ApiModelProperty("图片绝对路径")
		private String src = "";

		@ApiModelProperty("图片相对路径")
		private String relativePath = "";
	}
}
