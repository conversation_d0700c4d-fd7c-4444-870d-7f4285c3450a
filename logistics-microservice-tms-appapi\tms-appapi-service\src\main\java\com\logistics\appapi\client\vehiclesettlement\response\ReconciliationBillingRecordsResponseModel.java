package com.logistics.appapi.client.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author：wjf
 * @date：2021/4/12 13:12
 */
@Data
public class ReconciliationBillingRecordsResponseModel {
    @ApiModelProperty("打款公司")
    private String payCompany;
    @ApiModelProperty("收款人")
    private String receiverName;
    @ApiModelProperty("打款时间")
    private Date payTime;
    @ApiModelProperty("打款金额")
    private BigDecimal payFee;
}
