package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* Created by Mybatis Generator on 2022/07/04
*/
@Data
public class TVehicleSafeCheckVehicle extends BaseEntity {
    /**
    * 安全检查ID
    */
    @ApiModelProperty("安全检查ID")
    private Long safeCheckId;

    /**
    * 车辆ID
    */
    @ApiModelProperty("车辆ID")
    private Long vehicleId;

    /**
    * 车辆机构：1 自主，2 外部，3 自营
    */
    @ApiModelProperty("车辆机构：1 自主，2 外部，3 自营")
    private Integer vehicleProperty;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号")
    private String vehicleNo;

    /**
    * 挂车车牌号
    */
    @ApiModelProperty("挂车车牌号")
    private String trailerVehicleNo;

    /**
    * 司机ID
    */
    @ApiModelProperty("司机ID")
    private Long staffId;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名")
    private String staffName;

    /**
    * 电话
    */
    @ApiModelProperty("电话")
    private String staffMobile;

    /**
    * 状态0未检查，10待确认，20待整改，30已整改，40检查完成
    */
    @ApiModelProperty("状态0未检查，10待确认，20待整改，30已整改，40检查完成")
    private Integer status;

    /**
    * 检查时间
    */
    @ApiModelProperty("检查时间")
    private Date checkTime;

    /**
    * 检查人ID
    */
    @ApiModelProperty("检查人ID")
    private Long checkUserId;

    /**
    * 检查人
    */
    @ApiModelProperty("检查人")
    private String checkUserName;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;
}