package com.logistics.tms.biz.common

import com.logistics.tms.api.feign.common.model.WaterMarkModel
import com.logistics.tms.controller.customeraccount.response.AccountInfoResponseModel
import com.logistics.tms.base.constant.ConfigKeyConstant
import com.logistics.tms.base.enums.BusinessCodeTypeEnum
import com.logistics.tms.base.enums.CopyFileTypeEnum
import com.logistics.tms.base.enums.OperateLogsOperateTypeEnum
import com.logistics.tms.client.BasicDataClient
import com.logistics.tms.entity.TBusinessCode
import com.logistics.tms.entity.TCompanyEntrust
import com.logistics.tms.entity.TOperateLogs
import com.logistics.tms.mapper.TBusinessCodeMapper
import com.logistics.tms.mapper.TCompanyCarrierMapper
import com.logistics.tms.mapper.TCompanyEntrustMapper
import com.logistics.tms.mapper.TCustomerAccountMapper
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class CommonBizTest extends Specification {
    @Mock
    TBusinessCodeMapper tBusinessCodeMapper
    @Mock
    ConfigKeyConstant configKeyConstant
    @Mock
    TCompanyEntrustMapper tCompanyEntrustMapper
    @Mock
    TCompanyCarrierMapper tCompanyCarrierMapper
    @Mock
    TCustomerAccountMapper tCustomerAccountMapper
    @Mock
    BasicDataClient basicDataClient
    @Mock
    Logger log
    @InjectMocks
    CommonBiz commonBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "set Base Entity Add where operationName=#operationName and baseEntity=#baseEntity"() {
        expect:
        commonBiz.setBaseEntityAdd(baseEntity, operationName)
        assert expectedResult == false

        where:
        operationName   | baseEntity || expectedResult
        "operationName" | null       || true
    }

    @Unroll
    def "set Base Entity Modify where operationName=#operationName and baseEntity=#baseEntity"() {
        expect:
        commonBiz.setBaseEntityModify(baseEntity, operationName)
        assert expectedResult == false

        where:
        operationName   | baseEntity || expectedResult
        "operationName" | null       || true
    }

    @Unroll
    def "get Business Type Code where type=#type and userName=#userName and key=#key then expect: #expectedResult"() {
        given:
        when(tBusinessCodeMapper.selectBusinessCodeByCondition(anyInt(), anyString(), anyString())).thenReturn(new TBusinessCode(businessType: 0, businessCodeLength: 0, businessCodePrefix: "businessCodePrefix", businessCodeSequence: 0, businessCodeDateFormat: "businessCodeDateFormat"))

        expect:
        commonBiz.getBusinessTypeCode(type, key, userName) == expectedResult

        where:
        type                                 | userName   | key   || expectedResult
        BusinessCodeTypeEnum.COMPANY_CARRIER | "userName" | "key" || "expectedResult"
    }

    @Unroll
    def "batch Get OSS File Url where fileSrc=#fileSrc then expect: #expectedResult"() {
        given:
        when(basicDataClient.batchGetOSSFileUrl(any())).thenReturn([null])

        expect:
        commonBiz.batchGetOSSFileUrl(fileSrc) == expectedResult

        where:
        fileSrc    || expectedResult
        ["String"] || ["String": "String"]
    }

    @Unroll
    def "copy File To Directory Of Type where code=#code and waterMarkModel=#waterMarkModel and imgPath=#imgPath and type=#type then expect: #expectedResult"() {
        expect:
        commonBiz.copyFileToDirectoryOfType(type, code, imgPath, waterMarkModel) == expectedResult

        where:
        code   | waterMarkModel       | imgPath   | type || expectedResult
        "code" | new WaterMarkModel() | "imgPath" | 0    || "expectedResult"
    }

    @Unroll
    def "insert Logs where type=#type and objectId=#objectId and operateContents=#operateContents then expect: #expectedResult"() {
        expect:
        commonBiz.insertLogs(type, objectId, operateContents) == expectedResult

        where:
        type                                     | objectId | operateContents   || expectedResult
        OperateLogsOperateTypeEnum.AUDIT_THROUGH | 1l       | "operateContents" || new TOperateLogs(objectType: 0, objectId: 1l, operateType: 0, operateContents: "operateContents", operateUserName: "operateUserName", operateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime())
    }

    @Unroll
    def "get Login Driver Applet User Id"() {
        given:
        when(tCustomerAccountMapper.getAccountInfoBy(anyString(), anyLong(), anyInt())).thenReturn(new AccountInfoResponseModel())

        expect:
        commonBiz.getLoginDriverAppletUserId() == expectedResult

        where:
        expectedResult << 1l
    }

    @Unroll
    def "get Last Day Of Month where date=#date then expect: #expectedResult"() {
        expect:
        commonBiz.getLastDayOfMonth(date) == expectedResult

        where:
        date                                                             || expectedResult
        new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime() || new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime()
    }

    @Unroll
    def "get Leyi Company Entrust Id"() {
        given:
        when(tCompanyEntrustMapper.getByName(anyString())).thenReturn(new TCompanyEntrust())

        expect:
        commonBiz.getLeyiCompanyEntrustId() == expectedResult

        where:
        expectedResult << 1l
    }

    @Unroll
    def "add Operate Logs where remark=#remark and logsEnum=#logsEnum and userName=#userName and objectId=#objectId then expect: #expectedResult"() {
        expect:
        commonBiz.addOperateLogs(objectId, logsEnum, remark, userName) == expectedResult

        where:
        remark   | logsEnum                                 | userName   | objectId || expectedResult
        "remark" | OperateLogsOperateTypeEnum.AUDIT_THROUGH | "userName" | 1l       || new TOperateLogs(objectType: 0, objectId: 1l, operateType: 0, operateContents: "operateContents", operateUserName: "operateUserName", operateTime: new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime(), remark: "remark")
    }

    @Unroll
    def "get Province City Area where str=#str then expect: #expectedResult"() {
        given:
        when(basicDataClient.getProvinceCityAreaByKeywords(anyString())).thenReturn(null)

        expect:
        commonBiz.getProvinceCityArea(str) == expectedResult

        where:
        str   || expectedResult
        "str" || ["String": "String"]
    }

    @Unroll
    def "process Replace Temp Picture where typeEnum=#typeEnum and code=#code and context=#context then expect: #expectedResult"() {
        expect:
        commonBiz.processReplaceTempPicture(context, typeEnum, code) == expectedResult

        where:
        typeEnum                                 | code   | context   || expectedResult
        CopyFileTypeEnum.COMPANY_CARRIER_TRADING | "code" | "context" || "expectedResult"
    }

    @Unroll
    def "close HTML where str=#str then expect: #expectedResult"() {
        expect:
        CommonBiz.closeHTML(str) == expectedResult

        where:
        str   || expectedResult
        "str" || "expectedResult"
    }

    @Unroll
    def "add Real Path where context=#context then expect: #expectedResult"() {
        given:
        when(basicDataClient.getImageURL(anyString())).thenReturn("getImageURLResponse")

        expect:
        commonBiz.addRealPath(context) == expectedResult

        where:
        context   || expectedResult
        "context" || "expectedResult"
    }

    @Unroll
    def "file Sub Sequence where filePathFromRequest=#filePathFromRequest then expect: #expectedResult"() {
        expect:
        commonBiz.fileSubSequence(filePathFromRequest) == expectedResult

        where:
        filePathFromRequest   || expectedResult
        "filePathFromRequest" || "expectedResult"
    }

    @Unroll
    def "get Address By Lon And Lat where location=#location then expect: #expectedResult"() {
        given:
        when(basicDataClient.geoCodeForReGeo(any())).thenReturn([["String": "Map"]])

        expect:
        commonBiz.getAddressByLonAndLat(location) == expectedResult

        where:
        location   || expectedResult
        "location" || "expectedResult"
    }

    @Unroll
    def "get Mileage By Lon And Lat where unloadLocation=#unloadLocation and loadLocation=#loadLocation then expect: #expectedResult"() {
        given:
        when(basicDataClient.directionDriving(any())).thenReturn([["String": "Map"]])

        expect:
        commonBiz.getMileageByLonAndLat(loadLocation, unloadLocation) == expectedResult

        where:
        unloadLocation   | loadLocation   || expectedResult
        "unloadLocation" | "loadLocation" || 0 as BigDecimal
    }

    @Unroll
    def "different Days where date2=#date2 and date1=#date1 then expect: #expectedResult"() {
        expect:
        commonBiz.differentDays(date1, date2) == expectedResult

        where:
        date2                                                            | date1                                                                                || expectedResult
        new GregorianCalendar(2022, Calendar.JUNE, 16, 11, 11).getTime() | new java.util.GregorianCalendar(2022, java.util.Calendar.JUNE, 16, 11, 11).getTime() || 0
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme