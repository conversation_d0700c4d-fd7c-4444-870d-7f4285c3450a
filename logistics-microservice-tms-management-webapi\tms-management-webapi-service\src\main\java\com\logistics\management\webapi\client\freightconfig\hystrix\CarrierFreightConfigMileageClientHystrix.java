package com.logistics.management.webapi.client.freightconfig.hystrix;

import com.logistics.management.webapi.client.freightconfig.CarrierFreightConfigMileageClient;
import com.logistics.management.webapi.client.freightconfig.request.mileage.CarrierFreightConfigMileageAddRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.mileage.CarrierFreightConfigMileageEditRequestModel;
import com.logistics.management.webapi.client.freightconfig.request.mileage.CarrierFreightConfigMileageRequestModel;
import com.logistics.management.webapi.client.freightconfig.response.mileage.CarrierFreightConfigMileageResponseModel;
import com.yelo.tray.core.base.Result;
import org.springframework.stereotype.Component;

@Component
public class CarrierFreightConfigMileageClientHystrix implements CarrierFreightConfigMileageClient {

    @Override
    public Result<CarrierFreightConfigMileageResponseModel> detail(CarrierFreightConfigMileageRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> add(CarrierFreightConfigMileageAddRequestModel requestModel) {
        return Result.timeout();
    }

    @Override
    public Result<Boolean> edit(CarrierFreightConfigMileageEditRequestModel requestModel) {
        return Result.timeout();
    }
}
