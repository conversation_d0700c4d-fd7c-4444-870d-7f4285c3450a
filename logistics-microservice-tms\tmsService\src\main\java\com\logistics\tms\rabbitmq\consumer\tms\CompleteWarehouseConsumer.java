package com.logistics.tms.rabbitmq.consumer.tms;

import com.yelo.tools.rabbitmq.annocation.EnableMqErrorMessageCollect;
import com.logistics.tms.biz.carrierorder.CarrierOrderForLeYiBiz;
import com.logistics.tms.rabbitmq.consumer.model.CompleteWarehouseModel;
import com.rabbitmq.client.Channel;
import com.yelo.tools.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @author: wjf
 * @date: 2022/5/12 17:54
 */
@Component
@Slf4j
public class CompleteWarehouseConsumer {

    private ObjectMapper objectMapper = JacksonUtils.getInstance();

    @Autowired
    private CarrierOrderForLeYiBiz carrierOrderForLeYiBiz;

    @EnableMqErrorMessageCollect
    @RabbitListener(bindings = {@QueueBinding(
            exchange = @Exchange(name = "logistics.topic", type = "topic", durable = "true"),
            value = @Queue(value = "logistics.tmsSynCompleteWarehouseStatus", durable = "true"),
            key = "tmsSynCompleteWarehouseStatus")}
            , concurrency = "3")
    public void process(@Payload String message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws IOException {
        CompleteWarehouseModel completeWarehouseModel = objectMapper.readValue(message, CompleteWarehouseModel.class);
        log.info("接收云仓手动完成入库mq：" + completeWarehouseModel.toString());
        carrierOrderForLeYiBiz.synCompleteWarehouseStatus(completeWarehouseModel);
        channel.basicAck(deliveryTag, false);
    }
}
