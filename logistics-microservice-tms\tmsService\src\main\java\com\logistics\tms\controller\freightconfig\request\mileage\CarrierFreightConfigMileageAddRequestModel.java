package com.logistics.tms.controller.freightconfig.request.mileage;

import com.logistics.tms.controller.freightconfig.request.ladder.CarrierFreightConfigLadderRequestModel;
import com.logistics.tms.controller.freightconfig.request.scheme.CarrierFreightConfigSchemeAddRequestModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierFreightConfigMileageAddRequestModel extends CarrierFreightConfigSchemeAddRequestModel {

    @ApiModelProperty(value = "阶梯", required = true)
    private List<CarrierFreightConfigLadderRequestModel> ladderConfigList;
}
