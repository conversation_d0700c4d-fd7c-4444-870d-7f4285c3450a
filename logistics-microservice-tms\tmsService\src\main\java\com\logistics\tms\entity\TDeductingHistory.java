package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TDeductingHistory extends BaseEntity {
    /**
    * 对象类型（见枚举）
    */
    @ApiModelProperty("对象类型（见枚举）")
    private Integer objectType;

    /**
    * 对象id
    */
    @ApiModelProperty("对象id")
    private Long objectId;

    /**
    * 扣减月份
    */
    @ApiModelProperty("扣减月份")
    private String deductingMonth;

    /**
    * 总金额
    */
    @ApiModelProperty("总金额")
    private BigDecimal totalFee;

    /**
    * 扣减费用
    */
    @ApiModelProperty("扣减费用")
    private BigDecimal deductingFee;

    /**
    * 剩余未扣减费用
    */
    @ApiModelProperty("剩余未扣减费用")
    private BigDecimal remainingDeductingFee;
}