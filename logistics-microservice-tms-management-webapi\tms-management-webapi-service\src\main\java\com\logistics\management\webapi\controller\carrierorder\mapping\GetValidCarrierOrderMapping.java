package com.logistics.management.webapi.controller.carrierorder.mapping;

import com.logistics.management.webapi.base.enums.CarrierOrderStatusEnum;
import com.logistics.management.webapi.base.enums.GoodsUnitEnum;
import com.logistics.management.webapi.base.enums.LoadingUnloadingPartEnum;
import com.logistics.management.webapi.client.carrierorder.response.GetValidCarrierOrderResponseModel;
import com.logistics.management.webapi.controller.carrierorder.response.GetValidCarrierOrderResponseDto;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2022/9/6 10:37
 */
public class GetValidCarrierOrderMapping extends MapperMapping<GetValidCarrierOrderResponseModel, GetValidCarrierOrderResponseDto> {
    @Override
    public void configure() {
        GetValidCarrierOrderResponseModel source = getSource();
        GetValidCarrierOrderResponseDto destination = getDestination();

        //数据转换
        destination.setStaff(source.getDriverName());
        destination.setLoadAddress(source.getLoadCityName());
        destination.setUnloadAddress(source.getUnloadCityName());

        //数量转换
        BigDecimal carrierOrderAmount = BigDecimal.ZERO;
        if (source.getStatus() < CarrierOrderStatusEnum.WAIT_REACH_UNLOAD_ADDRESS.getKey()){//未提货
            carrierOrderAmount = source.getExpectAmount();
        }else if (source.getStatus() < CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey()){//未卸货
            carrierOrderAmount = source.getLoadAmount();
        }else if (source.getStatus().equals(CarrierOrderStatusEnum.WAIT_SIGN_UP.getKey())){//待签收
            carrierOrderAmount = source.getUnloadAmount();
        }else if (source.getStatus().equals(CarrierOrderStatusEnum.ALREADY_SIGN_UP.getKey())){//已签收
            carrierOrderAmount = source.getSignAmount();
        }
        destination.setAmount(carrierOrderAmount.stripTrailingZeros().toPlainString() + GoodsUnitEnum.getEnum(source.getGoodsUnit()).getUnit());

        //装卸方式
        destination.setLoadingUnloadingPartLabel(LoadingUnloadingPartEnum.getEnum(source.getLoadingUnloadingPart()).getValue());
    }
}
