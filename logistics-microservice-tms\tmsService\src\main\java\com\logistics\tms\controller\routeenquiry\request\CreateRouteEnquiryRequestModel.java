package com.logistics.tms.controller.routeenquiry.request;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/7/9 17:16
 */
@Data
public class CreateRouteEnquiryRequestModel {

    /**
     * 基础信息
     */
    private List<CreateRouteEnquiryAddressListRequestModel> addressList;

    /**
     * 承运商
     */
    private List<Long> companyCarrierIdList;

    /**
     * 货物名称
     */
    private String goodsName;

    /**
     * 报价生效开始时间
     */
    private Date quoteStartTime;

    /**
     * 报价生效结束时间
     */
    private Date quoteEndTime;

    /**
     * 备注
     */
    private String remark;

}
