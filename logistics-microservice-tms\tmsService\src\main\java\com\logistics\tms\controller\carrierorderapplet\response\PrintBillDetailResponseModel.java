package com.logistics.tms.controller.carrierorderapplet.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wjf
 * @date: 2023/5/5 14:01
 */
@Data
public class PrintBillDetailResponseModel {
    //运单信息
    @ApiModelProperty("运单号")
    private String carrierOrderCode;

    @ApiModelProperty("预提数")
    private BigDecimal expectAmount;

    @ApiModelProperty("实体数")
    private BigDecimal loadAmount;

    @ApiModelProperty("货物单位：1 件，2 吨")
    private Integer goodsUnit;

    @ApiModelProperty("状态")
    private Integer status;


    //运单地址信息
    //提货信息（提货地址前端拿当前定位）
    @ApiModelProperty("发货联系人")
    private String loadPerson;
    @ApiModelProperty("发货联系方式")
    private String loadMobile;

    @ApiModelProperty("发货仓库")
    private String loadWarehouse;

    //卸货信息
    @ApiModelProperty("收货联系人")
    private String unloadPerson;
    @ApiModelProperty("收货联系方式")
    private String unloadMobile;

    @ApiModelProperty("收货地址")
    private String unloadProvinceName;
    private String unloadCityName;
    private String unloadAreaName;
    private String unloadDetailAddress;
    private String unloadWarehouse;

    //司机信息
    @ApiModelProperty("车牌号")
    private String vehicleNo;
    @ApiModelProperty("司机名称")
    private String driverName;
    @ApiModelProperty("司机电话")
    private String driverMobile;


    @ApiModelProperty("二维码内容")
    private String qrCodeContent;
}
