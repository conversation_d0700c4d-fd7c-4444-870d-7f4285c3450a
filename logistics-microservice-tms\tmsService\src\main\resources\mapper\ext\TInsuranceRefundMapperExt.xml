<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.logistics.tms.mapper.TInsuranceRefundMapper" >
    <insert id="batchInsert" parameterType="com.logistics.tms.entity.TInsuranceRefund">
        <foreach collection="list" item="item" separator=";">
            insert into t_insurance_refund
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.status != null">
                    status,
                </if>
                <if test="item.vehicleId != null">
                    vehicle_id,
                </if>
                <if test="item.vehicleNo != null">
                    vehicle_no,
                </if>
                <if test="item.settlementMonth != null">
                    settlement_month,
                </if>
                <if test="item.insuranceId != null">
                    insurance_id,
                </if>
                <if test="item.insuranceType != null">
                    insurance_type,
                </if>
                <if test="item.refundCost != null">
                    refund_cost,
                </if>
                <if test="item.refundPath != null">
                    refund_path,
                </if>
                <if test="item.remark != null">
                    remark,
                </if>
                <if test="item.createdBy != null">
                    created_by,
                </if>
                <if test="item.createdTime != null">
                    created_time,
                </if>
                <if test="item.lastModifiedBy != null">
                    last_modified_by,
                </if>
                <if test="item.lastModifiedTime != null">
                    last_modified_time,
                </if>
                <if test="item.valid != null">
                    valid,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.status != null">
                    #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.vehicleId != null">
                    #{item.vehicleId,jdbcType=BIGINT},
                </if>
                <if test="item.vehicleNo != null">
                    #{item.vehicleNo,jdbcType=VARCHAR},
                </if>
                <if test="item.settlementMonth != null">
                    #{item.settlementMonth,jdbcType=VARCHAR},
                </if>
                <if test="item.insuranceId != null">
                    #{item.insuranceId,jdbcType=BIGINT},
                </if>
                <if test="item.insuranceType != null">
                    #{item.insuranceType,jdbcType=INTEGER},
                </if>
                <if test="item.refundCost != null">
                    #{item.refundCost,jdbcType=DECIMAL},
                </if>
                <if test="item.refundPath != null">
                    #{item.refundPath,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null">
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createdBy != null">
                    #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdTime != null">
                    #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastModifiedBy != null">
                    #{item.lastModifiedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.lastModifiedTime != null">
                    #{item.lastModifiedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.valid != null">
                    #{item.valid,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <update id="settlementOilFilledByIds">
        update t_insurance_refund set
        status = 1,
        last_modified_by = #{userName,jdbcType=VARCHAR},
        last_modified_time = now()
        where valid = 1
        and id in (${ids})
    </update>

    <select id="getByInsuranceIdType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_insurance_refund
        where valid = 1
        and insurance_id = #{insuranceId,jdbcType=BIGINT}
        and insurance_type = #{insuranceType,jdbcType=VARCHAR}
    </select>
</mapper>