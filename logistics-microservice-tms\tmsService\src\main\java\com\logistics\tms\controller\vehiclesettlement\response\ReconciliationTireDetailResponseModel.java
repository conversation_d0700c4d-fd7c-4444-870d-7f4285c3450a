package com.logistics.tms.controller.vehiclesettlement.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author：wjf
 * @date：2021/4/12 13:12
 */
@Data
public class ReconciliationTireDetailResponseModel {
    @ApiModelProperty("账单月份")
    private String settlementMonth;
    @ApiModelProperty("轮胎费用")
    private BigDecimal tireFee;
    @ApiModelProperty("轮胎费用列表")
    private List<ReconciliationTireListResponseModel> vehicleTireList;

    private List<Long> tireIdList;//结算表关联的轮胎费用ids
}
