package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* Created by Mybatis Generator on 2022/12/14
*/
@Data
public class TSinopecOriginalFreightUnitData extends BaseEntity {
    /**
    * 中石化委托单原始数据表ID
    */
    @ApiModelProperty("中石化委托单原始数据表ID")
    private Long sinopecOriginalDataId;

    /**
    * 货运单元名称(非必填)
    */
    @ApiModelProperty("货运单元名称(非必填)")
    private String name;

    /**
    * 货运单元编码(非必填)
    */
    @ApiModelProperty("货运单元编码(非必填)")
    private String type;

    /**
    * 货运单元数量(非必填)
    */
    @ApiModelProperty("货运单元数量(非必填)")
    private Integer count;
}