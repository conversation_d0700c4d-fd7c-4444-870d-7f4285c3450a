package com.logistics.management.webapi.controller.companyaccount.request;

import com.yelo.tray.core.page.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchCompanyAccountRequestDto extends AbstractPageForm<SearchCompanyAccountRequestDto> {

    @ApiModelProperty(value = "状态：1 启用 0 禁用")
    private String enabled;

    @ApiModelProperty(value = "银行账号")
    private String bankAccount;

    @ApiModelProperty(value = "新增开始时间: yyyy-MM-dd")
    private String createdStartTime;

    @ApiModelProperty(value = "新增结束时间: yyyy-MM-dd")
    private String createdEndTime;

    @ApiModelProperty(value = "操作开始时间: yyyy-MM-dd")
    private String operateStartTime;

    @ApiModelProperty(value = "操作结束时间: yyyy-MM-dd")
    private String operateEndTime;

}
