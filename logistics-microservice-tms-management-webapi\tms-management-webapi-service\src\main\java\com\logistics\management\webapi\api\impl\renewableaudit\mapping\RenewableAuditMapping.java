package com.logistics.management.webapi.api.impl.renewableaudit.mapping;

import com.logistics.management.webapi.api.feign.renewableaudit.dto.RenewableAuditResponseDto;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.enums.EncodeTypeEnum;
import com.logistics.management.webapi.base.enums.RenewableAuditStatusEnum;
import com.logistics.management.webapi.base.enums.RenewableBusinessType;
import com.logistics.management.webapi.base.enums.RenewableGoodsUnitEnum;
import com.logistics.management.webapi.base.utils.FrequentMethodUtils;
import com.logistics.tms.api.feign.renewableaudit.model.RenewableAuditResponseModel;
import com.yelo.tools.mapper.mapping.MapperMapping;

import java.util.Optional;


public class RenewableAuditMapping extends MapperMapping<RenewableAuditResponseModel, RenewableAuditResponseDto> {
    @Override
    public void configure() {
        RenewableAuditResponseModel source = getSource();
        RenewableAuditResponseDto destination = getDestination();

        //审核状态转换
        destination.setStatusDesc(RenewableAuditStatusEnum.getEnum(source.getStatus()).getValue());
        //数量去零
        destination.setGoodsAmountTotal(source.getGoodsAmountTotal().stripTrailingZeros().toPlainString());

        //业务类型转换
        if (RenewableBusinessType.COMPANY.getCode().equals(source.getBusinessType())) {
            destination.setBusinessTypeDesc(RenewableBusinessType.COMPANY.getName());
        } else if (RenewableBusinessType.PERSON.getCode().equals(source.getBusinessType())) {
            destination.setBusinessTypeDesc(RenewableBusinessType.PERSON.getName());
            destination.setCustomerName(Optional.ofNullable(source.getCustomerUserName()).orElse("") + " " + Optional.ofNullable(FrequentMethodUtils.encryptionData(source.getCustomerUserMobile(), EncodeTypeEnum.MOBILE_PHONE)).orElse(""));
        }

        if (RenewableAuditStatusEnum.WAIT_AUDIT.getKey().equals(source.getStatus()) || RenewableAuditStatusEnum.AUDIT_THROUGH.getKey().equals(source.getStatus())) {
            //确认数量
            destination.setVerifiedGoodsAmountTotal(source.getVerifiedGoodsAmountTotal().stripTrailingZeros().toPlainString() + RenewableGoodsUnitEnum.KILOGRAM.getValue());
            //收货仓库
            destination.setUnloadWarehouse(source.getUnloadWarehouse());
            //收货地址转换
            destination.setUnloadDetailAddress(Optional.ofNullable(source.getUnloadProvinceName()).orElse("") + Optional.ofNullable(source.getUnloadCityName()).orElse("") + Optional.ofNullable(source.getUnloadAreaName()).orElse(""));
            //收货人转换
            destination.setReceiver(Optional.ofNullable(source.getReceiverName()).orElse("") + " " + Optional.ofNullable(FrequentMethodUtils.encryptionData(source.getReceiverMobile(), EncodeTypeEnum.MOBILE_PHONE)).orElse(""));
        }else {
            //确认数量
            destination.setVerifiedGoodsAmountTotal(CommonConstant.BLANK_TEXT);
            //收货仓库
            destination.setUnloadWarehouse(CommonConstant.BLANK_TEXT);
            //收货地址转换
            destination.setUnloadDetailAddress(CommonConstant.BLANK_TEXT);
            //收货人转换
            destination.setReceiver(CommonConstant.BLANK_TEXT);
        }
        //发货地址转换
        destination.setLoadDetailAddress(Optional.ofNullable(source.getLoadProvinceName()).orElse("") + Optional.ofNullable(source.getLoadCityName()).orElse("") + Optional.ofNullable(source.getLoadAreaName()).orElse(""));
        //发货人转换
        destination.setConsignor(Optional.ofNullable(source.getConsignorName()).orElse("") + " " + Optional.ofNullable(FrequentMethodUtils.encryptionData(source.getConsignorMobile(), EncodeTypeEnum.MOBILE_PHONE)).orElse(""));
        //司机转换
        destination.setDriver(Optional.ofNullable(source.getStaffName()).orElse("") + " " + Optional.ofNullable(FrequentMethodUtils.encryptionData(source.getStaffMobile(), EncodeTypeEnum.MOBILE_PHONE)).orElse(""));
    }
}

