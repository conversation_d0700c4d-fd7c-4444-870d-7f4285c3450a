package com.logistics.appapi.controller.uploadfile;

import com.logistics.appapi.base.constant.ConfigKeyConstant;
import com.logistics.appapi.client.thirdparty.basicdata.BasicServiceClient;
import com.logistics.appapi.client.thirdparty.basicdata.file.response.FileUploadForAIResponseModel;
import com.logistics.appapi.client.thirdparty.basicdata.file.response.UploadFileOSSResponseModel;
import com.logistics.appapi.controller.uploadfile.response.FileUploadForAIResponseDto;
import com.logistics.appapi.controller.uploadfile.response.SrcUrlDto;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tray.core.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/3/7 16:22
 */
@Api(value = "上传文件", tags = "上传文件")
@RestController
@RequestMapping(value = "/api/uploadFile")
public class UploadFileController {

    @Resource
    private ConfigKeyConstant configKeyConstant;
    @Resource
    private BasicServiceClient basicServiceClient;

    /**
     * 上传图片
     * @param file
     * @return
     */
    @PostMapping(value = "/uploadFileImage")
    @ApiOperation(value = "上传图片")
    public Result<SrcUrlDto> uploadFileImage(@RequestParam("file") MultipartFile file) {
        UploadFileOSSResponseModel uploadFileOSSResponseModel = basicServiceClient.uploadMultiPartFileOSS(file);
        String rFileName = uploadFileOSSResponseModel.getTempRelativePath().substring(uploadFileOSSResponseModel.getTempRelativePath().lastIndexOf("/"));
        String relativeUrl = uploadFileOSSResponseModel.getRelativePath().substring(uploadFileOSSResponseModel.getRelativePath().lastIndexOf("/"));
        SrcUrlDto srcUrlDto = new SrcUrlDto();
        srcUrlDto.setSrc(configKeyConstant.fileAccessAddressTemp + rFileName);
        srcUrlDto.setRelativePath(relativeUrl);
        return Result.success(srcUrlDto);
    }

    /**
     * 上传图片（批量上传）
     * @param files
     * @return
     */
    @PostMapping(value = "/uploadFileImages")
    @ApiOperation(value = "上传图片（批量上传）")
    public Result<List<SrcUrlDto>> uploadFileImages(@RequestParam("files") MultipartFile[] files) {
        List<SrcUrlDto> resultList = new ArrayList<>();
        if (files != null && files.length > 0) {
            for (MultipartFile file : files) {
                UploadFileOSSResponseModel uploadFileOSSResponseModel = basicServiceClient.uploadMultiPartFileOSS(file);
                String rFileName = uploadFileOSSResponseModel.getTempRelativePath().substring(uploadFileOSSResponseModel.getTempRelativePath().lastIndexOf("/"));
                String relativeUrl = uploadFileOSSResponseModel.getRelativePath().substring(uploadFileOSSResponseModel.getRelativePath().lastIndexOf("/"));
                SrcUrlDto srcUrlDto = new SrcUrlDto();
                srcUrlDto.setSrc(configKeyConstant.fileAccessAddressTemp + rFileName);
                srcUrlDto.setRelativePath(relativeUrl);
                resultList.add(srcUrlDto);
            }
        }
        return Result.success(resultList);
    }

    /**
     * 上传图片并识别文字
     * @param file
     * @param picType
     * @param idCardSide
     * @return
     */
    @PostMapping(value = "/uploadFileImageForAI")
    @ApiOperation(value = "上传图片并识别文字")
    public Result<FileUploadForAIResponseDto> uploadFileImageForAI(@RequestParam("file")MultipartFile file, @RequestParam("picType")String picType, @RequestParam("idCardSide")String idCardSide) {
        FileUploadForAIResponseModel fileUploadForAIResponseModel = basicServiceClient.uploadOSSFileForAI(file,picType,idCardSide);
        return Result.success(MapperUtils.mapper(fileUploadForAIResponseModel,FileUploadForAIResponseDto.class));
    }
}
