package com.logistics.management.webapi.api.impl.vehicleoilcard;

import com.github.pagehelper.PageInfo;
import com.logistics.management.webapi.api.feign.vehicleoilcard.VehicleOilCardApi;
import com.logistics.management.webapi.api.feign.vehicleoilcard.dto.*;
import com.logistics.management.webapi.api.impl.vehicleoilcard.mapping.GetVehicleOilCardRecordMapping;
import com.logistics.management.webapi.api.impl.vehicleoilcard.mapping.SearchVehicleOilCardListMapping;
import com.logistics.management.webapi.base.constant.CommonConstant;
import com.logistics.management.webapi.base.constant.ExportVehicleOilCard;
import com.logistics.tms.api.feign.vehicleoilcard.VehicleOilCardServiceApi;
import com.logistics.tms.api.feign.vehicleoilcard.model.*;
import com.yelo.tools.mapper.utils.MapperUtils;
import com.yelo.tools.utils.AbstractExcelLoadBean;
import com.yelo.tools.utils.DateUtils;
import com.yelo.tools.utils.ExcelUtils;
import com.yelo.tools.utils.ListUtils;
import com.yelo.tray.core.annocation.IdempotentLimited;
import com.yelo.tray.core.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: wjf
 * @date: 2022/8/3 13:02
 */
@RestController
public class VehicleOilCardApiImpl implements VehicleOilCardApi {

    @Autowired
    private VehicleOilCardServiceApi vehicleOilCardServiceApi;

    /**
     * 车辆油卡列表
     * @param requestDto
     * @return
     */
    @Override
    public Result<PageInfo<SearchVehicleOilCardListResponseDto>> searchVehicleOilCardList(@RequestBody SearchVehicleOilCardListRequestDto requestDto) {
        Result<PageInfo<SearchVehicleOilCardListResponseModel>> result = vehicleOilCardServiceApi.searchVehicleOilCardList(MapperUtils.mapper(requestDto, SearchVehicleOilCardListRequestModel.class));
        result.throwException();
        PageInfo pageInfo = result.getData();
        if (ListUtils.isNotEmpty(pageInfo.getList())) {
            List<SearchVehicleOilCardListResponseDto> list = MapperUtils.mapper(pageInfo.getList(), SearchVehicleOilCardListResponseDto.class, new SearchVehicleOilCardListMapping());
            pageInfo.setList(list);
        }
        return Result.success(pageInfo);
    }

    /**
     * 导出车辆油卡列表
     * @param requestDto
     * @param response
     */
    @Override
    @IdempotentLimited(timeout = 5, message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public void exportVehicleOilCardList(SearchVehicleOilCardListRequestDto requestDto, HttpServletResponse response) {
        SearchVehicleOilCardListRequestModel requestModel = MapperUtils.mapper(requestDto, SearchVehicleOilCardListRequestModel.class);
        requestModel.setPageNum(CommonConstant.INTEGER_ZERO);
        requestModel.setPageSize(CommonConstant.INTEGER_ZERO);
        Result<PageInfo<SearchVehicleOilCardListResponseModel>> result = vehicleOilCardServiceApi.searchVehicleOilCardList(requestModel);
        result.throwException();
        List<SearchVehicleOilCardListResponseDto> dtoList = MapperUtils.mapper(result.getData().getList(), SearchVehicleOilCardListResponseDto.class, new SearchVehicleOilCardListMapping());

        String fileName = "车辆油卡表" + DateUtils.dateToString(new Date(), CommonConstant.YYYY_MM_DD_HH_MM_SS);
        Map<String,String> exportMap = ExportVehicleOilCard.getExportVehicleOilCardMap();
        ExcelUtils.exportExcelForServlet(fileName, fileName, exportMap, response, new AbstractExcelLoadBean() {
            @Override
            public List load() {
                return dtoList;
            }
        });
    }

    /**
     * 新增车辆油卡
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> addVehicleOilCard(@RequestBody @Valid AddVehicleOilCardRequestDto requestDto) {
        return vehicleOilCardServiceApi.addVehicleOilCard(MapperUtils.mapper(requestDto, AddVehicleOilCardRequestModel.class));
    }

    /**
     * 车辆油卡详情
     * @param requestDto
     * @return
     */
    @Override
    public Result<VehicleOilCardDetailResponseDto> vehicleOilCardDetail(@RequestBody @Valid VehicleOilCardIdRequestDto requestDto) {
        Result<VehicleOilCardDetailResponseModel> result = vehicleOilCardServiceApi.vehicleOilCardDetail(MapperUtils.mapper(requestDto, VehicleOilCardIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), VehicleOilCardDetailResponseDto.class));
    }

    /**
     * 解绑车辆油卡
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> unBindVehicleOilCard(@RequestBody @Valid VehicleOilCardIdRequestDto requestDto) {
        return vehicleOilCardServiceApi.unBindVehicleOilCard(MapperUtils.mapper(requestDto, VehicleOilCardIdRequestModel.class));
    }

    /**
     * 绑定车辆油卡
     * @param requestDto
     * @return
     */
    @Override
    @IdempotentLimited(message = CommonConstant.LIMITED_IDEMPOTENT_MESSAGE)
    public Result<Boolean> bindVehicleOilCard(@RequestBody @Valid BindVehicleOilCardRequestDto requestDto) {
        return vehicleOilCardServiceApi.bindVehicleOilCard(MapperUtils.mapper(requestDto, BindVehicleOilCardRequestModel.class));
    }

    /**
     * 操作记录
     * @param requestDto
     * @return
     */
    @Override
    public Result<List<GetVehicleOilCardRecordResponseDto>> getVehicleOilCardRecord(@RequestBody @Valid VehicleOilCardIdRequestDto requestDto) {
        Result<List<GetVehicleOilCardRecordResponseModel>> result = vehicleOilCardServiceApi.getVehicleOilCardRecord(MapperUtils.mapper(requestDto, VehicleOilCardIdRequestModel.class));
        result.throwException();
        return Result.success(MapperUtils.mapper(result.getData(), GetVehicleOilCardRecordResponseDto.class, new GetVehicleOilCardRecordMapping()));
    }
}
