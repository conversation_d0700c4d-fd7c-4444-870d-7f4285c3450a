package com.logistics.tms.entity;

import com.yelo.tray.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* Created by Mybatis Generator on 2022/06/30
*/
@Data
public class TFreightAddressRule extends BaseEntity {
    /**
    * 运价地址ID
    */
    @ApiModelProperty("运价地址ID")
    private Long freightAddressId;

    /**
    * 阶梯序列
    */
    @ApiModelProperty("阶梯序列")
    private Integer ruleIndex;

    /**
    * 从
    */
    @ApiModelProperty("从")
    private BigDecimal amountFrom;

    /**
    * 1 小于 2 小于等于
    */
    @ApiModelProperty("1 小于 2 小于等于")
    private Integer fromSymbol;

    /**
    * 到
    */
    @ApiModelProperty("到")
    private BigDecimal amountTo;

    /**
    * 1 小于 2 小于等于
    */
    @ApiModelProperty("1 小于 2 小于等于")
    private Integer toSymbol;

    /**
    * 费用类型 1 单价 2 一口价
    */
    @ApiModelProperty("费用类型 1 单价 2 一口价")
    private Integer freightType;

    /**
    * 费用
    */
    @ApiModelProperty("费用")
    private BigDecimal freightFee;
}