package com.logistics.management.webapi.api.feign.gpsfee.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * @author: wjf
 * @date: 2019/10/8 10:43
 */
@Data
public class AddOrModifyGpsFeeRequestDto {
    private String gpsFeeId;
    @ApiModelProperty("车牌号")
    @NotBlank(message = "请选择车牌号")
    private String vehicleId;
    @ApiModelProperty("司机")
    @NotBlank(message = "请选择司机")
    private String staffId;
    @ApiModelProperty("服务商")
    @Size(min = 1,max = 50,message = "请维护GPS服务商")
    private String gpsServiceProvider;
    @ApiModelProperty("终端型号")
    private String terminalType;
    @ApiModelProperty("服务费")
    @Pattern(regexp = "(^[0-9]*$)|(^[0-9]+([.]\\d{1,2})$)",message = "请维护服务费，大于0小于100000，且保留两位小数")
    @DecimalMin(value = "0.01",message = "请维护服务费，大于0小于100000，且保留两位小数")
    @DecimalMax(value = "99999.99",message = "请维护服务费，大于0小于100000，且保留两位小数")
    private String serviceFee;
    @ApiModelProperty("合作周期（月）")
    @Pattern(regexp = "^[0-9]*$",message = "请维护合作周期：0<月份≤240")
    @Min(value = 1,message = "请维护合作周期：0<月份≤240")
    @Max(value = 240,message = "请维护合作周期：0<月份≤240")
    private String cooperationPeriod;
    @ApiModelProperty("SIM卡号")
    private String simNumber;
    @ApiModelProperty("安装时间")
    private String installTime;
    @ApiModelProperty("起始时间")
    @Pattern(regexp = "[\\d]{4}-[\\d]{1,2}-[\\d]{1,2}",message = "请维护正确的合作起始时间")
    private String startDate;
    @ApiModelProperty("截止时间")
    @Pattern(regexp = "[\\d]{4}-[\\d]{1,2}-[\\d]{1,2}",message = "请维护正确的合作截止时间")
    private String endDate;
    @ApiModelProperty("备注")
    private String remark;
}
