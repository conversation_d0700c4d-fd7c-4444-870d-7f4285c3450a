package com.logistics.management.webapi.api.feign.driversafepromise.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 上传承诺书
 * @Author: sj
 * @Date: 2019/11/4 14:38
 */
@Data
public class UploadSafePromiseRequestDto {
    @ApiModelProperty("签订承诺书ID")
    @NotBlank(message = "签订承诺书ID不能为空")
    private String signSafePromiseId;
    @ApiModelProperty("手持承诺书图片地址")
    @NotBlank(message = "手持承诺书图片不能为空")
    private String handPromiseUrl;
    @ApiModelProperty("签字责任书图片地址")
    @NotBlank(message = "签字责任书图片不能为空")
    private String signResponsibilityUrl;
}
