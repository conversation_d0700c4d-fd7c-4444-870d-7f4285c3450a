package com.logistics.tms.biz.messagenotice;

import com.github.pagehelper.PageInfo;
import com.logistics.tms.base.constant.CommonConstant;
import com.logistics.tms.base.enums.MessageNoticeModuleEnum;
import com.logistics.tms.biz.common.CommonBiz;
import com.logistics.tms.biz.messagenotice.model.ReadMessageNoticeModel;
import com.logistics.tms.biz.messagenotice.model.SearchMessageNoticeListModel;
import com.logistics.tms.controller.messagenotice.request.ReadMessageNoticeRequestModel;
import com.logistics.tms.controller.messagenotice.request.SearchMessageNoticeListRequestModel;
import com.logistics.tms.controller.messagenotice.response.SearchMessageNoticeListResponseModel;
import com.logistics.tms.mapper.TMessageNoticeMapper;
import com.yelo.tools.context.BaseContextHandler;
import com.yelo.tools.mapper.utils.MapperUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wjf
 * @date: 2024/6/5 11:10
 */
@Service
public class MessageNoticeBiz {

    @Resource
    private TMessageNoticeMapper tMessageNoticeMapper;
    @Resource
    private CommonBiz commonBiz;

    /**
     * 消息列表
     *
     * @param requestModel
     * @return
     */
    public PageInfo<SearchMessageNoticeListResponseModel> searchList(SearchMessageNoticeListRequestModel requestModel) {
        Long companyCarrierId = null;
        Integer messageModule = MessageNoticeModuleEnum.MANAGEMENT.getKey();
        if (CommonConstant.TWO.equals(requestModel.getSource())) {
            companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || companyCarrierId.equals(CommonConstant.LONG_ZERO)) {
                return new PageInfo<>(new ArrayList<>());
            }
            messageModule = MessageNoticeModuleEnum.WEB.getKey();
        }

        SearchMessageNoticeListModel searchMessageNoticeListModel = MapperUtils.mapper(requestModel, SearchMessageNoticeListModel.class);
        searchMessageNoticeListModel.setMessageReceiver(companyCarrierId);
        searchMessageNoticeListModel.setMessageModule(messageModule);
        requestModel.enablePaging();
        List<SearchMessageNoticeListResponseModel> list = tMessageNoticeMapper.searchList(searchMessageNoticeListModel);
        return new PageInfo<>(list);
    }

    /**
     * 置为已读
     *
     * @param requestModel
     * @return
     */
    @Transactional
    public void readMessageNotice(ReadMessageNoticeRequestModel requestModel) {
        Long companyCarrierId = null;
        Integer messageModule = MessageNoticeModuleEnum.MANAGEMENT.getKey();
        if (CommonConstant.TWO.equals(requestModel.getSource())) {
            companyCarrierId = commonBiz.getLoginUserCompanyCarrierId();
            if (companyCarrierId == null || companyCarrierId.equals(CommonConstant.LONG_ZERO)) {
                return;
            }
            messageModule = MessageNoticeModuleEnum.WEB.getKey();
        }

        //将消息通知置为已读
        ReadMessageNoticeModel readMessageNoticeModel = new ReadMessageNoticeModel();
        readMessageNoticeModel.setMessageId(requestModel.getMessageId());
        readMessageNoticeModel.setMessageReceiver(companyCarrierId);
        readMessageNoticeModel.setUserName(BaseContextHandler.getUserName());
        readMessageNoticeModel.setUpdateTime(new Date());
        readMessageNoticeModel.setMessageModule(messageModule);
        tMessageNoticeMapper.readMessageNotice(readMessageNoticeModel);
    }
}
