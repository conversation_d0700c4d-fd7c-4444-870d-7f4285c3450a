package com.logistics.tms.controller.demandorder.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wjf
 * @date: 2018/10/10 9:36
 */
@Data
public class CancelDemandByDemandOrderMessage {
    @ApiModelProperty("需求单code的列表")
    private List<CancelDemandCodesAndTypeModel> items;
    @ApiModelProperty("取消原因")
    private String cancelReason;
    @ApiModelProperty("取消类型：1 我司原因， 2 客户原因")
    private Integer cancelType;
    @ApiModelProperty("操作人")
    private String userName;
    @ApiModelProperty("操作来源：1 网络货运系统，2 TMS系统")
    private String operateSource="2";
    @ApiModelProperty("取消退回数量列表")
    private List<CancelDemandOrderBackAmountModel> backAmountModels;
}
