package com.logistics.tms.biz.carrierfreight.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AddressConfigModel {

    @ApiModelProperty("地址配置id")
    private Long addressConfigId;

    /**
     * 方案Id
     */
    @ApiModelProperty("方案Id")
    private Long schemeId;


    /**
     * 发货县区id
     */
    @ApiModelProperty("发货县区id")
    private Long fromAreaId;

    /**
     * 发货县区名字
     */
    @ApiModelProperty("发货县区名字")
    private String fromAreaName;

    /**
     * 卸货县区id
     */
    @ApiModelProperty("卸货县区id")
    private Long toAreaId;

    /**
     * 卸货县区名字
     */
    @ApiModelProperty("卸货县区名字")
    private String toAreaName;

    @ApiModelProperty("阶梯价格信息")
    private List<AddressConfigLadderModel> ladderConfigList;
}
