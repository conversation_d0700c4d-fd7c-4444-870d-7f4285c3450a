package com.logistics.tms.biz.vehiclepayeerel

import com.logistics.tms.biz.vehicleassetmanagement.model.VehicleBasicPropertyModel
import com.logistics.tms.api.feign.vehiclepayeerel.model.AddOrModifyVehiclePayeeRelRequestModel
import com.logistics.tms.api.feign.vehiclepayeerel.model.ImportVehiclePayeeRelRequestModel
import com.logistics.tms.api.feign.vehiclepayeerel.model.SearchVehiclePayeeRelListRequestModel
import com.logistics.tms.api.feign.vehiclepayeerel.model.SearchVehiclePayeeRelListResponseModel
import com.logistics.tms.api.feign.vehiclepayeerel.model.VehiclePayeeRelDetailResponseModel
import com.logistics.tms.api.feign.vehiclepayeerel.model.VehiclePayeeRelIdRequestModel
import com.logistics.tms.api.feign.vehiclepayeerel.model.VehiclePayeeRelIdsRequestModel
import com.logistics.tms.biz.common.CommonBiz
import com.logistics.tms.entity.TDriverPayee
import com.logistics.tms.entity.TVehicleDrivingLicense
import com.logistics.tms.entity.TVehiclePayeeRel
import com.logistics.tms.mapper.TDriverPayeeMapper
import com.logistics.tms.mapper.TVehicleBasicMapper
import com.logistics.tms.mapper.TVehicleDrivingLicenseMapper
import com.logistics.tms.mapper.TVehiclePayeeRelMapper
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/16
 */
class VehiclePayeeRelBizTest extends Specification {
    @Mock
    TVehiclePayeeRelMapper vehiclePayeeRelMapper
    @Mock
    CommonBiz commonBiz
    @Mock
    TVehicleBasicMapper vehicleBasicMapper
    @Mock
    TDriverPayeeMapper driverPayeeMapper
    @Mock
    TVehicleDrivingLicenseMapper vehicleDrivingLicenseMapper
    @InjectMocks
    VehiclePayeeRelBiz vehiclePayeeRelBiz

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "search Vehicle Payee Rel List where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehiclePayeeRelMapper.searchVehiclePayeeRelList(any())).thenReturn([new SearchVehiclePayeeRelListResponseModel()])

        expect:
        vehiclePayeeRelBiz.searchVehiclePayeeRelList(requestModel) == expectedResult

        where:
        requestModel                                || expectedResult
        new SearchVehiclePayeeRelListRequestModel() || [new SearchVehiclePayeeRelListResponseModel()]
    }

    @Unroll
    def "get Vehicle Payee Rel Detail where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehiclePayeeRelMapper.getVehiclePayeeRelDetail(anyLong())).thenReturn(new VehiclePayeeRelDetailResponseModel())

        expect:
        vehiclePayeeRelBiz.getVehiclePayeeRelDetail(requestModel) == expectedResult

        where:
        requestModel                        || expectedResult
        new VehiclePayeeRelIdRequestModel() || new VehiclePayeeRelDetailResponseModel()
    }

    @Unroll
    def "add Or Modify Vehicle Payee Rel where requestModel=#requestModel"() {
        given:
        when(vehiclePayeeRelMapper.getByVehicleId(anyLong())).thenReturn(new TVehiclePayeeRel(vehicleId: 1l, driverPayeeId: 1l, remark: "remark"))
        when(vehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())

        expect:
        vehiclePayeeRelBiz.addOrModifyVehiclePayeeRel(requestModel)
        assert expectedResult == false

        where:
        requestModel                                 || expectedResult
        new AddOrModifyVehiclePayeeRelRequestModel() || true
    }

    @Unroll
    def "del Vehicle Payee Rel where requestModel=#requestModel"() {
        given:
        when(vehiclePayeeRelMapper.getByIds(anyString())).thenReturn([new TVehiclePayeeRel()])
        when(vehiclePayeeRelMapper.batchUpdate(any())).thenReturn(0)

        expect:
        vehiclePayeeRelBiz.delVehiclePayeeRel(requestModel)
        assert expectedResult == false

        where:
        requestModel                         || expectedResult
        new VehiclePayeeRelIdsRequestModel() || true
    }

    @Unroll
    def "import Vehicle Payee Rel where requestModel=#requestModel then expect: #expectedResult"() {
        given:
        when(vehiclePayeeRelMapper.getByVehicleId(anyLong())).thenReturn(new TVehiclePayeeRel(vehicleId: 1l, driverPayeeId: 1l, remark: "remark"))
        when(vehiclePayeeRelMapper.batchInsert(any())).thenReturn(0)
        when(vehiclePayeeRelMapper.batchUpdate(any())).thenReturn(0)
        when(vehicleBasicMapper.getVehicleBasicPropertyById(anyLong())).thenReturn(new VehicleBasicPropertyModel())
        when(driverPayeeMapper.getByNameMobileIdentity(anyString(), anyString(), anyString(), anyString())).thenReturn(new TDriverPayee(auditStatus: 0))
        when(vehicleDrivingLicenseMapper.getByVehicleNo(anyString())).thenReturn(new TVehicleDrivingLicense(vehicleId: 1l))

        expect:
        vehiclePayeeRelBiz.importVehiclePayeeRel(requestModel) == expectedResult

        where:
        requestModel                            || expectedResult
        new ImportVehiclePayeeRelRequestModel() || new com.logistics.tms.api.feign.vehiclepayeerel.model.ImportVehiclePayeeRelResponseModel()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme