package com.logistics.tms.biz.demandorder.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/3/11 17:21
 */
@Data
public class CreateEntrustSettlementForLeYiConsumerModel {
    @ApiModelProperty("物流需求单号")
    private String logisticsDemandCode;
    @ApiModelProperty("操作人")
    private String operation;
    @ApiModelProperty("操作时间")
    private Date operationTime;

    @ApiModelProperty("报价类型 1 单价 2 整车价")
    private Integer expenseType;
    @ApiModelProperty("结算费用")
    private BigDecimal amount;
    @ApiModelProperty("结算数量")
    private BigDecimal count;
}
